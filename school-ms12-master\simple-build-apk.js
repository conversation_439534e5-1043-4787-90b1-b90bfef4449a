const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Starting simple APK build process...');

// Update local.properties
console.log('🔧 Setting up Android SDK path...');
const localPropertiesPath = path.join(__dirname, 'android', 'local.properties');
const localPropertiesContent = `sdk.dir=D:\\Android\\Sdk
android.useAndroidX=true
android.enableJetifier=true`;

fs.writeFileSync(localPropertiesPath, localPropertiesContent, 'utf8');

// Skip NDK check
console.log('🔧 Configuring build to skip NDK...');
const gradlePropertiesPath = path.join(__dirname, 'android', 'gradle.properties');
let gradleContent = fs.readFileSync(gradlePropertiesPath, 'utf8');
gradleContent += '\n# Disable NDK\nandroid.enableNativeCompile=false\nandroid.defaults.buildfeatures.buildconfig=true';
fs.writeFileSync(gradlePropertiesPath, gradleContent, 'utf8');

// Run the build
console.log('🏗️ Building the APK...');
try {
  execSync('npx react-native build-android --mode=release', { stdio: 'inherit' });
  console.log('✅ Build command completed');
} catch (error) {
  console.log('⚠️ Build command had issues, but we\'ll check for APK anyway');
}

// Try direct Gradle command
try {
  console.log('🏗️ Trying direct Gradle build command...');
  execSync('cd android && gradlew.bat assembleRelease', { stdio: 'inherit' });
} catch (error) {
  console.log('⚠️ Direct Gradle build had issues');
}

// Check if APK exists
const apkPath = path.join(__dirname, 'android', 'app', 'build', 'outputs', 'apk', 'release', 'app-release.apk');
if (fs.existsSync(apkPath)) {
  // Copy to root directory for convenience
  fs.copyFileSync(apkPath, path.join(__dirname, 'app-release.apk'));
  console.log('✅ Build successful! APK is available at:');
  console.log(`   - ${apkPath}`);
  console.log(`   - ${path.join(__dirname, 'app-release.apk')}`);
} else {
  console.log('❌ Build process completed but no APK was found at the expected location.');
}

console.log('🏁 Build process completed!'); 