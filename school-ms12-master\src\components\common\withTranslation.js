import React from 'react';
import { useLanguage } from '../../context/LanguageContext';

/**
 * Higher-order component that injects translation capabilities into any component
 * @param {React.ComponentType} Component - Component to wrap
 * @returns {React.ComponentType} - Component with translation props added
 */
const withTranslation = (Component) => {
  return (props) => {
    const { translate, language, getTextStyle, isRTL, supportedLanguages, setLanguage } = useLanguage();
    
    // Pass the translation functions and properties as props to the component
    return (
      <Component
        {...props}
        translate={translate}
        language={language}
        getTextStyle={getTextStyle}
        isRTL={isRTL}
        supportedLanguages={supportedLanguages}
        setLanguage={setLanguage}
      />
    );
  };
};

export default withTranslation;
