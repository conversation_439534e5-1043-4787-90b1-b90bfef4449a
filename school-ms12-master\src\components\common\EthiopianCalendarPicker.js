import React, { useState, useEffect } from 'react';
import { View, StyleSheet, TouchableOpacity, Text, Animated } from 'react-native';
import { Card, Title, IconButton, Surface, Badge } from 'react-native-paper';
import EthiopianCalendar from '../../utils/EthiopianCalendar';
import { MaterialCommunityIcons } from '@expo/vector-icons';

/**
 * Enhanced Ethiopian Calendar Picker Component
 * A feature-rich calendar component that displays dates in the Ethiopian calendar
 */
const EthiopianCalendarPicker = ({
  selectedDate = new Date(),
  onDateSelect,
  minDate,
  maxDate,
  language = 'en', // Default to English if no language provided
  markedDates = [], // Array of dates to mark with dots
  markedDateColor = '#FF5722', // Color for marked dates
  showWeekNumbers = false, // Show week numbers
  allowSwipe = true, // Allow swiping to change months
  theme = {
    primaryColor: '#2196F3',
    selectedColor: '#2196F3',
    todayColor: '#03A9F4',
    textColor: '#333',
    disabledColor: '#ccc',
    backgroundColor: '#fff',
    weekendColor: '#f44336',
    headerTextColor: '#333'
  }
}) => {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [displayedMonth, setDisplayedMonth] = useState(null);
  const [displayedYear, setDisplayedYear] = useState(null);
  const [calendarDays, setCalendarDays] = useState([]);
  const [slideAnim] = useState(new Animated.Value(0));
  const [slideDirection, setSlideDirection] = useState(null); // 'left' or 'right'

  // Initialize the calendar
  useEffect(() => {
    const ethiopianDate = EthiopianCalendar.fromGregorian(currentDate);
    setDisplayedMonth(ethiopianDate.month);
    setDisplayedYear(ethiopianDate.year);
  }, [currentDate]);

  // Generate calendar days whenever month/year changes
  useEffect(() => {
    if (displayedMonth !== null && displayedYear !== null) {
      generateCalendarDays();
    }
  }, [displayedMonth, displayedYear]);

  // Generate days for the current month view
  const generateCalendarDays = () => {
    const days = [];
    const daysInMonth = displayedMonth === 12 ?
      (EthiopianCalendar.isLeapYear(displayedYear) ? 6 : 5) :
      EthiopianCalendar.DAYS_IN_MONTH;

    // Get the first day of the month
    const firstDayOfMonth = EthiopianCalendar.toGregorian(displayedYear, displayedMonth, 1);
    const firstDayWeekday = firstDayOfMonth.getDay(); // 0 = Sunday, 1 = Monday, etc.

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < firstDayWeekday; i++) {
      days.push({ day: null, gregorianDate: null });
    }

    // Add days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      const gregorianDate = EthiopianCalendar.toGregorian(displayedYear, displayedMonth, day);
      days.push({ day, gregorianDate });
    }

    setCalendarDays(days);
  };

  // Navigate to previous month with animation
  const goToPreviousMonth = () => {
    setSlideDirection('right');
    slideAnim.setValue(-300);

    Animated.timing(slideAnim, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true
    }).start();

    if (displayedMonth === 0) {
      setDisplayedMonth(12);
      setDisplayedYear(displayedYear - 1);
    } else {
      setDisplayedMonth(displayedMonth - 1);
    }
  };

  // Navigate to next month with animation
  const goToNextMonth = () => {
    setSlideDirection('left');
    slideAnim.setValue(300);

    Animated.timing(slideAnim, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true
    }).start();

    if (displayedMonth === 12) {
      setDisplayedMonth(0);
      setDisplayedYear(displayedYear + 1);
    } else {
      setDisplayedMonth(displayedMonth + 1);
    }
  };

  // Jump to a specific month and year
  const jumpToDate = (month, year) => {
    setDisplayedMonth(month);
    setDisplayedYear(year);
  };

  // Jump to today's date
  const jumpToToday = () => {
    const today = new Date();
    const ethiopianDate = EthiopianCalendar.fromGregorian(today);
    setDisplayedMonth(ethiopianDate.month);
    setDisplayedYear(ethiopianDate.year);
    if (onDateSelect) {
      onDateSelect(today);
    }
  };

  // Check if a date is the currently selected date
  const isSelectedDate = (date) => {
    if (!date || !selectedDate) return false;

    const selected = new Date(selectedDate);
    return date.getDate() === selected.getDate() &&
           date.getMonth() === selected.getMonth() &&
           date.getFullYear() === selected.getFullYear();
  };

  // Check if a date is today
  const isToday = (date) => {
    if (!date) return false;

    const today = new Date();
    return date.getDate() === today.getDate() &&
           date.getMonth() === today.getMonth() &&
           date.getFullYear() === today.getFullYear();
  };

  // Check if a date is disabled (outside min/max range)
  const isDisabled = (date) => {
    if (!date) return true;

    if (minDate && date < new Date(minDate)) return true;
    if (maxDate && date > new Date(maxDate)) return true;

    return false;
  };

  // Check if a date is a weekend
  const isWeekend = (date) => {
    if (!date) return false;
    const day = date.getDay();
    return day === 0 || day === 6; // Sunday or Saturday
  };

  // Check if a date is marked
  const isMarkedDate = (date) => {
    if (!date || !markedDates || markedDates.length === 0) return false;

    return markedDates.some(markedDate => {
      const marked = new Date(markedDate);
      return date.getDate() === marked.getDate() &&
             date.getMonth() === marked.getMonth() &&
             date.getFullYear() === marked.getFullYear();
    });
  };

  // Handle date selection
  const handleDateSelect = (date) => {
    if (!date || isDisabled(date)) return;

    if (onDateSelect) {
      onDateSelect(date);
    }
  };

  // Render weekday headers
  const renderWeekdays = () => {
    const weekdays = [];

    // Add week number header if enabled
    if (showWeekNumbers) {
      weekdays.push(
        <Text key="week-header" style={[styles.weekdayText, styles.weekNumberText]}>
          #
        </Text>
      );
    }

    for (let i = 0; i < 7; i++) {
      const isWeekendDay = i === 0 || i === 6;
      weekdays.push(
        <Text
          key={i}
          style={[
            styles.weekdayText,
            isWeekendDay && { color: theme.weekendColor }
          ]}
        >
          {EthiopianCalendar.getWeekdayName(i, language).charAt(0)}
        </Text>
      );
    }
    return <View style={styles.weekdaysRow}>{weekdays}</View>;
  };

  // Get week number for a date
  const getWeekNumber = (date) => {
    if (!date) return '';
    return EthiopianCalendar.getWeekNumber(date);
  };

  // Render calendar grid
  const renderCalendarGrid = () => {
    const rows = [];
    let cells = [];
    let currentWeekNumber = null;

    calendarDays.forEach((item, index) => {
      // Add week number cell at the beginning of each row if enabled
      if (showWeekNumbers && index % 7 === 0) {
        const weekNumber = item.gregorianDate ? getWeekNumber(item.gregorianDate) : '';
        currentWeekNumber = weekNumber;
        cells.push(
          <View key={`week-${index}`} style={styles.weekNumberCell}>
            <Text style={styles.weekNumberText}>{weekNumber}</Text>
          </View>
        );
      }

      cells.push(
        <TouchableOpacity
          key={index}
          style={[
            styles.dayCell,
            item.day && isSelectedDate(item.gregorianDate) && { backgroundColor: theme.selectedColor },
            item.day && isToday(item.gregorianDate) && { borderColor: theme.todayColor, borderWidth: 1 },
            item.day && isWeekend(item.gregorianDate) && styles.weekendCell
          ]}
          onPress={() => item.day && handleDateSelect(item.gregorianDate)}
          disabled={!item.day || isDisabled(item.gregorianDate)}
        >
          <Text
            style={[
              styles.dayText,
              isDisabled(item.gregorianDate) && { color: theme.disabledColor },
              isSelectedDate(item.gregorianDate) && { color: '#fff' },
              !item.day && { color: 'transparent' },
              item.day && isWeekend(item.gregorianDate) && !isSelectedDate(item.gregorianDate) && { color: theme.weekendColor }
            ]}
          >
            {item.day || ' '}
          </Text>

          {item.day && isMarkedDate(item.gregorianDate) && (
            <Badge
              style={[styles.markedDateBadge, { backgroundColor: markedDateColor }]}
              size={6}
            />
          )}
        </TouchableOpacity>
      );

      // Create a new row after every 7 cells
      if ((index + 1) % 7 === 0 || index === calendarDays.length - 1) {
        rows.push(
          <View key={`row-${index}`} style={styles.calendarRow}>
            {cells}
          </View>
        );
        cells = [];
      }
    });

    return (
      <Animated.View
        style={[styles.calendarGrid, { transform: [{ translateX: slideAnim }] }]}
      >
        {rows}
      </Animated.View>
    );
  };

  return (
    <Card style={[styles.container, { backgroundColor: theme.backgroundColor }]}>
      <Card.Content>
        {/* Calendar Header */}
        <View style={styles.header}>
          <IconButton
            icon="chevron-left"
            size={24}
            onPress={goToPreviousMonth}
            color={theme.primaryColor}
          />
          <Title style={[styles.headerTitle, { color: theme.headerTextColor || theme.textColor }]}>
            {EthiopianCalendar.getMonthName(displayedMonth, language)} {displayedYear}
          </Title>
          <IconButton
            icon="chevron-right"
            size={24}
            onPress={goToNextMonth}
            color={theme.primaryColor}
          />
        </View>

        {/* Today Button */}
        <TouchableOpacity
          style={styles.todayButton}
          onPress={jumpToToday}
        >
          <MaterialCommunityIcons name="calendar-today" size={14} color={theme.primaryColor} />
          <Text style={[styles.todayButtonText, { color: theme.primaryColor }]}>Today</Text>
        </TouchableOpacity>

        {/* Weekday Headers */}
        {renderWeekdays()}

        {/* Calendar Grid */}
        {renderCalendarGrid()}
      </Card.Content>
    </Card>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 10,
    borderRadius: 8,
    overflow: 'hidden',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 5,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  todayButton: {
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'flex-end',
    padding: 5,
    marginBottom: 5,
  },
  todayButtonText: {
    fontSize: 12,
    marginLeft: 4,
  },
  weekdaysRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 10,
    paddingHorizontal: 5,
  },
  weekdayText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#666',
    width: 30,
    textAlign: 'center',
  },
  weekNumberText: {
    fontSize: 12,
    color: '#999',
    width: 20,
  },
  weekNumberCell: {
    width: 20,
    height: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  calendarGrid: {
    marginBottom: 10,
  },
  calendarRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 5,
  },
  dayCell: {
    width: 30,
    height: 30,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 15,
    position: 'relative',
  },
  weekendCell: {
    backgroundColor: 'rgba(244, 67, 54, 0.1)',
  },
  dayText: {
    fontSize: 14,
    textAlign: 'center',
  },
  markedDateBadge: {
    position: 'absolute',
    bottom: 2,
  },
});

export default EthiopianCalendarPicker;
