import React, { createContext, useState, useContext, useRef } from 'react';
import { Animated } from 'react-native';

// Create the context
const AdminSidebarContext = createContext();

/**
 * Provider component for the AdminSidebar context
 * This allows the sidebar state to be shared across all admin screens
 */
export const AdminSidebarProvider = ({ children }) => {
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [activeSidebarItem, setActiveSidebarItem] = useState('dashboard');
  const drawerAnim = useRef(new Animated.Value(-280)).current;
  const backdropFadeAnim = useRef(new Animated.Value(0)).current;

  // Toggle drawer function
  const toggleDrawer = () => {
    if (drawerOpen) {
      // Close drawer
      Animated.parallel([
        Animated.timing(drawerAnim, {
          toValue: -280,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(backdropFadeAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        })
      ]).start(() => setDrawerOpen(false));
    } else {
      // Open drawer
      setDrawerOpen(true);
      Animated.parallel([
        Animated.timing(drawerAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(backdropFadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        })
      ]).start();
    }
  };

  // The value that will be provided to consumers of this context
  const contextValue = {
    drawerOpen,
    setDrawerOpen,
    activeSidebarItem,
    setActiveSidebarItem,
    drawerAnim,
    backdropFadeAnim,
    toggleDrawer
  };

  return (
    <AdminSidebarContext.Provider value={contextValue}>
      {children}
    </AdminSidebarContext.Provider>
  );
};

// Custom hook to use the AdminSidebar context
export const useAdminSidebar = () => {
  const context = useContext(AdminSidebarContext);
  if (!context) {
    throw new Error('useAdminSidebar must be used within an AdminSidebarProvider');
  }
  return context;
};
