import { jsPDF } from 'jspdf';
import html2canvas from 'html2canvas';
import * as XLSX from 'xlsx';
import { saveAs } from 'file-saver';
import { renderToString } from 'react-dom/server';

class ReportExportService {
    static instance = null;

    constructor() {
        if (ReportExportService.instance) {
            return ReportExportService.instance;
        }
        ReportExportService.instance = this;
    }

    // Export to PDF
    async exportToPDF(component, options = {}) {
        try {
            const {
                filename = 'report.pdf',
                orientation = 'portrait',
                format = 'a4',
                quality = 2,
                scale = 2
            } = options;

            // Create a temporary container
            const container = document.createElement('div');
            container.innerHTML = renderToString(component);
            document.body.appendChild(container);

            // Capture the component as canvas
            const canvas = await html2canvas(container, {
                scale: scale,
                quality: quality,
                logging: false,
                useCORS: true
            });

            // Remove temporary container
            document.body.removeChild(container);

            // Create PDF
            const pdf = new jsPDF({
                orientation: orientation,
                unit: 'mm',
                format: format
            });

            // Add canvas to PDF
            const imgData = canvas.toDataURL('image/png');
            const pdfWidth = pdf.internal.pageSize.getWidth();
            const pdfHeight = pdf.internal.pageSize.getHeight();
            const imgWidth = canvas.width;
            const imgHeight = canvas.height;
            const ratio = Math.min(pdfWidth / imgWidth, pdfHeight / imgHeight);
            const imgX = (pdfWidth - imgWidth * ratio) / 2;
            const imgY = 0;

            pdf.addImage(
                imgData, 'PNG',
                imgX, imgY,
                imgWidth * ratio,
                imgHeight * ratio
            );

            // Save PDF
            pdf.save(filename);
            return true;
        } catch (error) {
            console.error('Failed to export PDF:', error);
            throw error;
        }
    }

    // Export to Excel
    exportToExcel(data, options = {}) {
        try {
            const {
                filename = 'report.xlsx',
                sheetName = 'Sheet1',
                header = true
            } = options;

            // Create workbook
            const wb = XLSX.utils.book_new();
            const ws = XLSX.utils.json_to_sheet(data, { header });

            // Add worksheet to workbook
            XLSX.utils.book_append_sheet(wb, ws, sheetName);

            // Generate Excel file
            const excelBuffer = XLSX.write(wb, {
                bookType: 'xlsx',
                type: 'array'
            });

            // Save file
            const blob = new Blob([excelBuffer], {
                type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            });
            saveAs(blob, filename);
            return true;
        } catch (error) {
            console.error('Failed to export Excel:', error);
            throw error;
        }
    }

    // Export to CSV
    exportToCSV(data, options = {}) {
        try {
            const {
                filename = 'report.csv',
                delimiter = ',',
                header = true
            } = options;

            // Convert data to CSV string
            const ws = XLSX.utils.json_to_sheet(data, { header });
            const csv = XLSX.utils.sheet_to_csv(ws, { delimiter });

            // Save file
            const blob = new Blob([csv], { type: 'text/csv;charset=utf-8' });
            saveAs(blob, filename);
            return true;
        } catch (error) {
            console.error('Failed to export CSV:', error);
            throw error;
        }
    }

    // Export multiple reports as ZIP
    async exportAsZip(reports, options = {}) {
        try {
            const {
                filename = 'reports.zip',
                format = 'pdf'
            } = options;

            const JSZip = (await import('jszip')).default;
            const zip = new JSZip();

            // Process each report
            for (const [index, report] of reports.entries()) {
                const reportName = report.name || `report_${index + 1}`;
                
                if (format === 'pdf') {
                    const canvas = await html2canvas(report.component, {
                        scale: 2,
                        quality: 2,
                        logging: false,
                        useCORS: true
                    });

                    const pdf = new jsPDF({
                        orientation: 'portrait',
                        unit: 'mm',
                        format: 'a4'
                    });

                    const imgData = canvas.toDataURL('image/png');
                    pdf.addImage(imgData, 'PNG', 0, 0);

                    zip.file(`${reportName}.pdf`, pdf.output('blob'));
                } else if (format === 'excel') {
                    const wb = XLSX.utils.book_new();
                    const ws = XLSX.utils.json_to_sheet(report.data);
                    XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');

                    const excelBuffer = XLSX.write(wb, {
                        bookType: 'xlsx',
                        type: 'array'
                    });

                    zip.file(`${reportName}.xlsx`, excelBuffer);
                }
            }

            // Generate and save ZIP file
            const content = await zip.generateAsync({ type: 'blob' });
            saveAs(content, filename);
            return true;
        } catch (error) {
            console.error('Failed to export ZIP:', error);
            throw error;
        }
    }

    // Generate report filename
    generateFilename(options = {}) {
        const {
            prefix = 'report',
            studentName = '',
            reportType = '',
            date = new Date(),
            extension = 'pdf'
        } = options;

        const formattedDate = date.toISOString().split('T')[0];
        const sanitizedStudentName = studentName.replace(/[^a-zA-Z0-9]/g, '_');
        
        return `${prefix}_${sanitizedStudentName}_${reportType}_${formattedDate}.${extension}`;
    }

    // Convert component to image
    async componentToImage(component, options = {}) {
        try {
            const {
                scale = 2,
                quality = 2,
                format = 'png'
            } = options;

            const canvas = await html2canvas(component, {
                scale: scale,
                quality: quality,
                logging: false,
                useCORS: true
            });

            return canvas.toDataURL(`image/${format}`);
        } catch (error) {
            console.error('Failed to convert component to image:', error);
            throw error;
        }
    }

    // Add watermark to PDF
    addWatermark(pdf, text, options = {}) {
        const {
            color = [0, 0, 0, 0.1],
            angle = -45,
            fontSize = 60
        } = options;

        const pages = pdf.internal.getNumberOfPages();

        for (let i = 1; i <= pages; i++) {
            pdf.setPage(i);
            const pageWidth = pdf.internal.pageSize.getWidth();
            const pageHeight = pdf.internal.pageSize.getHeight();

            pdf.setTextColor(...color);
            pdf.setFontSize(fontSize);
            pdf.setGState(new pdf.GState({ opacity: 0.2 }));

            pdf.save();
            pdf.translate(pageWidth / 2, pageHeight / 2);
            pdf.rotate(angle);
            pdf.text(text, 0, 0, { align: 'center' });
            pdf.restore();
        }

        return pdf;
    }

    // Add QR code to PDF
    async addQRCode(pdf, data, options = {}) {
        const {
            x = 10,
            y = 10,
            width = 30,
            height = 30
        } = options;

        const QRCode = (await import('qrcode')).default;
        const qrDataUrl = await QRCode.toDataURL(data);

        pdf.addImage(qrDataUrl, 'PNG', x, y, width, height);
        return pdf;
    }
}

export default new ReportExportService();
