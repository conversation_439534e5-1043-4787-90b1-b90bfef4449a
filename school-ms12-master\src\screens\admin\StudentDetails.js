import React, { useState, useEffect } from 'react';
import { View, ScrollView, StyleSheet, TouchableOpacity } from 'react-native';
import { Card, Title, Text, Divider, List, ActivityIndicator, Avatar, DataTable, Chip, useTheme, Portal, Modal, Button, IconButton } from 'react-native-paper';
import { db } from '../../config/firebase';
import { collection, query, where, getDocs, doc, getDoc, orderBy } from 'firebase/firestore';
import { useLanguage } from '../../context/LanguageContext';
import * as Animatable from 'react-native-animatable';
import { LinearGradient } from 'expo-linear-gradient';
import CustomButton from '../../components/common/CustomButton';

const StudentDetails = ({ route, navigation }) => {
  const { studentId } = route.params;
  // No theme needed
  const { translate } = useLanguage();

  const [student, setStudent] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [attendanceData, setAttendanceData] = useState({
    present: 0,
    absent: 0,
    total: 0,
    percentage: 0
  });
  const [attendanceRecords, setAttendanceRecords] = useState([]);
  const [showAttendanceModal, setShowAttendanceModal] = useState(false);
  const [results, setResults] = useState([]);
  const [classInfo, setClassInfo] = useState(null);
  const [sectionInfo, setSectionInfo] = useState(null);

  useEffect(() => {
    fetchStudentDetails();
  }, [studentId]);

  const fetchStudentDetails = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch student details
      const studentRef = doc(db, 'users', studentId);
      const studentDoc = await getDoc(studentRef);

      if (!studentDoc.exists()) {
        throw new Error('Student not found');
      }

      const studentData = studentDoc.data();
      setStudent(studentData);

      // Fetch class and section info if available
      if (studentData.classId) {
        await fetchClassInfo(studentData.classId, studentData.sectionName);
      }

      // Fetch attendance data
      await fetchAttendanceData(studentId);

      // Fetch results/grades
      await fetchResults(studentId);

    } catch (error) {
      console.error('Error fetching student details:', error);
      setError(error.message || 'Failed to fetch student details');
    } finally {
      setLoading(false);
    }
  };

  const fetchClassInfo = async (classId, sectionName) => {
    try {
      const classRef = doc(db, 'classes', classId);
      const classDoc = await getDoc(classRef);

      if (classDoc.exists()) {
        const classData = classDoc.data();
        setClassInfo(classData);

        // Find section info
        if (sectionName && classData.sections) {
          const section = classData.sections.find(s => s.name === sectionName);
          if (section) {
            setSectionInfo(section);
          }
        }
      }
    } catch (error) {
      console.error('Error fetching class info:', error);
    }
  };

  const fetchAttendanceData = async (studentId) => {
    try {
      const attendanceRef = collection(db, 'attendance');
      let querySnapshot;

      try {
        // Try with ordering (requires composite index)
        const q = query(
          attendanceRef,
          where('studentId', '==', studentId),
          orderBy('date', 'desc')
        );
        querySnapshot = await getDocs(q);
      } catch (indexError) {
        console.log('Index error, falling back to basic query:', indexError);
        // Fallback to basic query without ordering
        const q = query(
          attendanceRef,
          where('studentId', '==', studentId)
        );
        querySnapshot = await getDocs(q);
      }

      let present = 0;
      let absent = 0;
      let total = querySnapshot.size;
      const records = [];

      querySnapshot.forEach((doc) => {
        const data = doc.data();
        records.push({ id: doc.id, ...data });

        if (data.status === 'present') {
          present++;
        } else if (data.status === 'absent') {
          absent++;
        }
      });

      // Sort records by date manually if the ordered query failed
      records.sort((a, b) => {
        if (!a.date || !b.date) return 0;
        return new Date(b.date) - new Date(a.date); // descending order
      });

      const percentage = total > 0 ? (present / total) * 100 : 0;

      setAttendanceData({
        present,
        absent,
        total,
        percentage: percentage.toFixed(2)
      });

      setAttendanceRecords(records);
    } catch (error) {
      console.error('Error fetching attendance data:', error);
      // Set empty data to avoid breaking the UI
      setAttendanceData({
        present: 0,
        absent: 0,
        total: 0,
        percentage: '0.00'
      });
      setAttendanceRecords([]);
    }
  };

  const fetchResults = async (studentId) => {
    try {
      const resultsRef = collection(db, 'results');
      const q = query(resultsRef, where('studentId', '==', studentId));
      const querySnapshot = await getDocs(q);

      const resultsData = [];
      querySnapshot.forEach((doc) => {
        resultsData.push({ id: doc.id, ...doc.data() });
      });

      setResults(resultsData);
    } catch (error) {
      console.error('Error fetching results:', error);
    }
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={'#1976d2'} />
        <Text style={styles.loadingText}>Loading student details...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>{error}</Text>
        <CustomButton
          mode="contained"
          onPress={fetchStudentDetails}
          style={styles.retryButton}
        >
          Retry
        </CustomButton>
      </View>
    );
  }

  if (!student) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>Student not found</Text>
        <CustomButton
          mode="contained"
          onPress={() => navigation.goBack()}
          style={styles.retryButton}
        >
          Go Back
        </CustomButton>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <Animatable.View animation="fadeIn" duration={500}>
        <Card style={styles.profileCard}>
          <LinearGradient
            colors={['#1976d2' || '#6200ee', '#9c27b0' || '#03dac4']}
            style={styles.headerGradient}
          >
            <View style={styles.profileHeader}>
              <Avatar.Text
                size={80}
                label={`${student.firstName?.[0] || ''}${student.lastName?.[0] || ''}`}
                style={styles.avatar}
              />
              <View style={styles.nameContainer}>
                <Title style={styles.nameText}>{`${student.firstName || ''} ${student.lastName || ''}`}</Title>
                <Text style={styles.idText}>{student.admissionNumber || 'No ID'}</Text>
                <Chip icon="school" style={styles.classChip}>
                  {classInfo ? `${classInfo.name} - ${student.sectionName || ''}` : 'No Class Assigned'}
                </Chip>
              </View>
            </View>
          </LinearGradient>

          <Card.Content style={styles.cardContent}>
            <Title style={styles.sectionTitle}>Personal Information</Title>
            <List.Item
              title="Email"
              description={student.email || 'Not provided'}
              left={props => <List.Icon {...props} icon="email" />}
            />
            <List.Item
              title="Phone"
              description={student.phone || 'Not provided'}
              left={props => <List.Icon {...props} icon="phone" />}
            />
            <List.Item
              title="Address"
              description={
                typeof student.address === 'object' && student.address !== null
                  ? `${student.address.street || ''}, ${student.address.city || ''}, ${student.address.state || ''}, ${student.address.country || ''}`.replace(/^[, ]+|[, ]+$|(?<=,)[, ]+/g, '')
                  : (student.address || 'Not provided')
              }
              left={props => <List.Icon {...props} icon="map-marker" />}
            />
            <List.Item
              title="Date of Birth"
              description={student.dateOfBirth || 'Not provided'}
              left={props => <List.Icon {...props} icon="calendar" />}
            />
            <List.Item
              title="Gender"
              description={student.gender || 'Not provided'}
              left={props => <List.Icon {...props} icon="account" />}
            />
            <List.Item
              title="Blood Group"
              description={student.bloodGroup || 'Not provided'}
              left={props => <List.Icon {...props} icon="water" color="red" />}
            />
          </Card.Content>
        </Card>

        <Card style={styles.card}>
          <TouchableOpacity onPress={() => setShowAttendanceModal(true)}>
            <Card.Content>
              <View style={styles.sectionTitleContainer}>
                <Title style={styles.sectionTitle}>Attendance Summary</Title>
                <Text style={styles.viewDetailsText}>Tap to view details</Text>
              </View>
              <View style={styles.attendanceStats}>
                <View style={styles.statItem}>
                  <Text style={styles.statValue}>{attendanceData.present}</Text>
                  <Text style={styles.statLabel}>Present</Text>
                </View>
                <View style={styles.statItem}>
                  <Text style={styles.statValue}>{attendanceData.absent}</Text>
                  <Text style={styles.statLabel}>Absent</Text>
                </View>
                <View style={styles.statItem}>
                  <Text style={styles.statValue}>{attendanceData.total}</Text>
                  <Text style={styles.statLabel}>Total Days</Text>
                </View>
                <View style={styles.statItem}>
                  <Text style={styles.statValue}>{attendanceData.percentage}%</Text>
                  <Text style={styles.statLabel}>Attendance</Text>
                </View>
              </View>

              <View style={styles.progressBarContainer}>
                <View style={styles.progressBar}>
                  <View
                    style={[
                      styles.progressFill,
                      {
                        width: `${attendanceData.percentage}%`,
                        backgroundColor: parseFloat(attendanceData.percentage) < 75 ? '#FF6B6B' : '#4CAF50'
                      }
                    ]}
                  />
                </View>
                <Text style={styles.progressText}>
                  {parseFloat(attendanceData.percentage) < 75
                    ? 'Attendance below required minimum (75%)'
                    : 'Good attendance'}
                </Text>
              </View>
            </Card.Content>
          </TouchableOpacity>
        </Card>

        <Card style={styles.card}>
          <Card.Content>
            <Title style={styles.sectionTitle}>Academic Results</Title>

            {results.length > 0 ? (
              <DataTable>
                <DataTable.Header>
                  <DataTable.Title>Subject</DataTable.Title>
                  <DataTable.Title>Exam</DataTable.Title>
                  <DataTable.Title numeric>Marks</DataTable.Title>
                  <DataTable.Title numeric>Grade</DataTable.Title>
                </DataTable.Header>

                {results.map((result, index) => (
                  <Animatable.View
                    key={result.id || index}
                    animation="fadeInUp"
                    duration={300}
                    delay={index * 50}
                  >
                    <DataTable.Row>
                      <DataTable.Cell>{result.subjectName || 'Unknown'}</DataTable.Cell>
                      <DataTable.Cell>{result.examType || 'Unknown'}</DataTable.Cell>
                      <DataTable.Cell numeric>
                        {result.marks !== undefined ? `${result.marks}/${result.totalMarks || 100}` : '-'}
                      </DataTable.Cell>
                      <DataTable.Cell numeric>{result.grade || '-'}</DataTable.Cell>
                    </DataTable.Row>
                  </Animatable.View>
                ))}
              </DataTable>
            ) : (
              <Text style={styles.noDataText}>No results available</Text>
            )}
          </Card.Content>
        </Card>

        <Card style={styles.card}>
          <Card.Content>
            <Title style={styles.sectionTitle}>Parent/Guardian Information</Title>

            {student.parentName ? (
              <>
                <List.Item
                  title="Parent Name"
                  description={student.parentName || 'Not provided'}
                  left={props => <List.Icon {...props} icon="account-multiple" />}
                />
                <List.Item
                  title="Parent Phone"
                  description={student.parentPhone || 'Not provided'}
                  left={props => <List.Icon {...props} icon="phone" />}
                />
                <List.Item
                  title="Emergency Contact"
                  description={student.emergencyContact || 'Not provided'}
                  left={props => <List.Icon {...props} icon="phone-alert" />}
                />
              </>
            ) : (
              <Text style={styles.noDataText}>No parent/guardian information available</Text>
            )}
          </Card.Content>
        </Card>

        <View style={styles.buttonContainer}>
          <CustomButton
            mode="contained"
            onPress={() => navigation.goBack()}
            style={styles.button}
            icon="arrow-left"
          >
            Back to List
          </CustomButton>
        </View>
      </Animatable.View>

      {/* Attendance Details Modal */}
      <Portal>
        <Modal
          visible={showAttendanceModal}
          onDismiss={() => setShowAttendanceModal(false)}
          contentContainerStyle={styles.modalContainer}
          dismissable={true}
        >
          <Card style={styles.modalCard}>
            <Card.Title
              title="Attendance Details"
              right={(props) => (
                <IconButton
                  {...props}
                  icon="close"
                  onPress={() => setShowAttendanceModal(false)}
                />
              )}
            />
            <Card.Content>
              <ScrollView style={styles.attendanceList}>
                <DataTable>
                  <DataTable.Header>
                    <DataTable.Title style={styles.dateColumn}>Date</DataTable.Title>
                    <DataTable.Title style={styles.statusColumn}>Status</DataTable.Title>
                    <DataTable.Title style={styles.remarksColumn}>Remarks</DataTable.Title>
                  </DataTable.Header>

                  {attendanceRecords.map((record, index) => {
                    // Format the date to be more compact
                    const formattedDate = record.date ? new Date(record.date).toLocaleDateString() : 'N/A';

                    return (
                      <DataTable.Row key={record.id || index}>
                        <DataTable.Cell style={styles.dateColumn}>{formattedDate}</DataTable.Cell>
                        <DataTable.Cell style={styles.statusColumn}>
                          <Chip
                            style={{
                              backgroundColor: record.status === 'present' ? '#4CAF50' : '#FF6B6B',
                            }}
                            textStyle={{ color: 'white' }}
                          >
                            {record.status || 'N/A'}
                          </Chip>
                        </DataTable.Cell>
                        <DataTable.Cell style={styles.remarksColumn}>{record.remarks || '-'}</DataTable.Cell>
                      </DataTable.Row>
                    );
                  })}

                  {attendanceRecords.length === 0 && (
                    <DataTable.Row>
                      <DataTable.Cell style={{ flex: 4, alignItems: 'center', justifyContent: 'center' }}>
                        <Text style={styles.noDataText}>No attendance records found</Text>
                      </DataTable.Cell>
                    </DataTable.Row>
                  )}

                  {/* Show index creation message if needed */}
                  <View style={styles.indexMessageContainer}>
                    <Text style={styles.indexMessageText}>
                      Note: For better performance, an administrator should create the required index in Firebase.
                    </Text>
                  </View>
                </DataTable>
              </ScrollView>
              <Button
                mode="contained"
                onPress={() => setShowAttendanceModal(false)}
                style={styles.closeButton}
              >
                Close
              </Button>
            </Card.Content>
          </Card>
        </Modal>
      </Portal>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  sectionTitleContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  viewDetailsText: {
    fontSize: 12,
    color: '#666',
    fontStyle: 'italic',
  },
  modalContainer: {
    padding: 10,
    margin: 10,
    width: '95%',
    alignSelf: 'center',
  },
  modalCard: {
    borderRadius: 10,
    width: '100%',
  },
  attendanceList: {
    maxHeight: 400,
    width: '100%',
  },
  dateColumn: {
    flex: 1.2,
  },
  statusColumn: {
    flex: 0.8,
    justifyContent: 'center',
  },
  classColumn: {
    flex: 1,
  },
  remarksColumn: {
    flex: 1,
  },
  closeButton: {
    marginTop: 20,
  },
  indexMessageContainer: {
    marginTop: 10,
    padding: 10,
    backgroundColor: '#FFF9C4',
    borderRadius: 4,
  },
  indexMessageText: {
    fontSize: 12,
    color: '#795548',
    textAlign: 'center',
  },
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    marginBottom: 20,
    textAlign: 'center',
  },
  retryButton: {
    marginTop: 10,
  },
  profileCard: {
    marginBottom: 16,
    borderRadius: 10,
    overflow: 'hidden',
  },
  headerGradient: {
    padding: 20,
  },
  profileHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    marginRight: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  nameContainer: {
    flex: 1,
  },
  nameText: {
    color: 'white',
    fontSize: 22,
    fontWeight: 'bold',
  },
  idText: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 14,
    marginBottom: 8,
  },
  classChip: {
    alignSelf: 'flex-start',
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
  },
  cardContent: {
    paddingTop: 16,
  },
  card: {
    marginBottom: 16,
    borderRadius: 10,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  attendanceStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  statItem: {
    alignItems: 'center',
    flex: 1,
  },
  statValue: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
  },
  progressBarContainer: {
    marginTop: 8,
  },
  progressBar: {
    height: 10,
    backgroundColor: '#E0E0E0',
    borderRadius: 5,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 5,
  },
  progressText: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
    textAlign: 'right',
  },
  noDataText: {
    textAlign: 'center',
    fontStyle: 'italic',
    color: '#666',
    padding: 20,
  },
  buttonContainer: {
    marginVertical: 20,
    alignItems: 'center',
  },
  button: {
    width: '80%',
  },
});

export default StudentDetails;
