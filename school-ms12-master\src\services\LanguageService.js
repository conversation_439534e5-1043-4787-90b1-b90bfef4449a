import { db } from '../config/firebase';
import { collection, getDocs, query, where, doc, updateDoc } from 'firebase/firestore';

class LanguageService {
    static instance = null;

    constructor() {
        if (LanguageService.instance) {
            return LanguageService.instance;
        }
        LanguageService.instance = this;

        this.supportedLanguages = {
            en: 'English',
            am: 'አማርኛ',
            om: '<PERSON><PERSON><PERSON>',
            ctu: 'Ctu'
        };

        this.defaultLanguage = 'en';
        this.translations = {};
        this.loadTranslations();
    }

    // Load translations from Firestore and local files
    async loadTranslations() {
        try {
            // Load from Firestore
            const snapshot = await getDocs(collection(db, 'translations'));
            snapshot.forEach(doc => {
                const data = doc.data();
                this.translations[doc.id] = data;
            });

            // Load local translations
            this.loadLocalTranslations();
        } catch (error) {
            console.error('Failed to load translations:', error);
            // Fallback to local translations if Firestore fails
            this.loadLocalTranslations();
        }
    }

    // Load translations from local files
    loadLocalTranslations() {
        try {
            // Import local translation files
            const { classManagement } = require('../translations/classManagement');

            // Add them to the translations object
            Object.keys(classManagement).forEach(lang => {
                Object.keys(classManagement[lang]).forEach(key => {
                    if (!this.translations['classManagement']) {
                        this.translations['classManagement'] = {};
                    }

                    if (!this.translations['classManagement'][lang]) {
                        this.translations['classManagement'][lang] = {};
                    }

                    this.translations['classManagement'][lang][key] = classManagement[lang][key];
                });
            });
        } catch (error) {
            console.error('Failed to load local translations:', error);
        }
    }

    // Get translation
    translate(key, language = this.defaultLanguage, params = {}) {
        try {
            // Check if key contains a namespace (e.g., 'classManagement.title')
            if (key.includes('.')) {
                const [namespace, subKey] = key.split('.');
                if (this.translations[namespace] &&
                    this.translations[namespace][language] &&
                    this.translations[namespace][language][subKey]) {
                    let translation = this.translations[namespace][language][subKey];

                    // Replace parameters in translation
                    Object.entries(params).forEach(([param, value]) => {
                        translation = translation.replace(new RegExp(`\\{${param}\\}`, 'g'), value);
                    });

                    return translation;
                } else if (this.translations[namespace] &&
                           this.translations[namespace][this.defaultLanguage] &&
                           this.translations[namespace][this.defaultLanguage][subKey]) {
                    // Fallback to default language
                    let translation = this.translations[namespace][this.defaultLanguage][subKey];

                    // Replace parameters in translation
                    Object.entries(params).forEach(([param, value]) => {
                        translation = translation.replace(new RegExp(`\\{${param}\\}`, 'g'), value);
                    });

                    return translation;
                }
            }

            // Original behavior for non-namespaced keys
            if (!this.translations[key]) {
                return key; // Return key if translation not found
            }

            let translation = this.translations[key][language] || this.translations[key][this.defaultLanguage];

            // Replace parameters in translation
            Object.entries(params).forEach(([param, value]) => {
                translation = translation.replace(new RegExp(`\\{${param}\\}`, 'g'), value);
            });

            return translation;
        } catch (error) {
            console.error('Translation error:', error);
            return key;
        }
    }

    // Get all translations for a language
    getAllTranslations(language) {
        const translations = {};
        Object.keys(this.translations).forEach(key => {
            translations[key] = this.translate(key, language);
        });
        return translations;
    }

    // Add new translation
    async addTranslation(key, translations) {
        try {
            await updateDoc(doc(db, 'translations', key), translations);
            this.translations[key] = translations;
            return true;
        } catch (error) {
            console.error('Failed to add translation:', error);
            throw error;
        }
    }

    // Update translation
    async updateTranslation(key, translations) {
        try {
            await updateDoc(doc(db, 'translations', key), translations);
            this.translations[key] = {
                ...this.translations[key],
                ...translations
            };
            return true;
        } catch (error) {
            console.error('Failed to update translation:', error);
            throw error;
        }
    }

    // Get user's preferred language
    async getUserLanguage(userId) {
        try {
            const userDoc = await getDocs(
                query(collection(db, 'users'), where('id', '==', userId))
            );
            return userDoc.docs[0].data().preferredLanguage || this.defaultLanguage;
        } catch (error) {
            console.error('Failed to get user language:', error);
            return this.defaultLanguage;
        }
    }

    // Set user's preferred language
    async setUserLanguage(userId, language) {
        try {
            if (!this.supportedLanguages[language]) {
                throw new Error('Unsupported language');
            }

            await updateDoc(doc(db, 'users', userId), {
                preferredLanguage: language
            });

            return true;
        } catch (error) {
            console.error('Failed to set user language:', error);
            throw error;
        }
    }

    // Get language name in native script
    getLanguageName(language, displayLanguage = language) {
        return this.translate(`language_${language}`, displayLanguage);
    }

    // Get all supported languages
    getSupportedLanguages(displayLanguage = this.defaultLanguage) {
        return Object.keys(this.supportedLanguages).map(code => ({
            code,
            name: this.getLanguageName(code, displayLanguage)
        }));
    }

    // Check if text is in Amharic
    isAmharic(text) {
        return /[\u1200-\u137F]/.test(text);
    }

    // Check if text is in Afaan Oromoo
    isAfaanOromoo(text) {
        // Afaan Oromoo uses Latin script with specific patterns
        const oromoPatterns = /\b(sh|ch|dh|ny|ph)\b|\b[A-Za-z]+aa[A-Za-z]+\b/;
        return oromoPatterns.test(text);
    }

    // Get script direction
    getDirection(language) {
        return ['am'].includes(language) ? 'rtl' : 'ltr';
    }

    // Format date in selected language
    formatDate(date, language) {
        const ethiopianMonths = {
            am: ['መስከረም', 'ጥቅምት', 'ህዳር', 'ታህሳስ', 'ጥር', 'የካቲት', 'መጋቢት', 'ሚያዚያ', 'ግንቦት', 'ሰኔ', 'ሐምሌ', 'ነሐሴ', 'ጳጉሜ'],
            om: ['Fulbaana', 'Onkololeessa', 'Sadaasa', 'Muddee', 'Amajjii', 'Guraandhala', 'Bitootessa', 'Ebla', 'Caamsaa', 'Waxabajjii', 'Adoolessa', 'Hagayya', 'Qaammee']
        };

        if (language === 'am' || language === 'om') {
            const ethiopianDate = this.toEthiopianDate(date);
            const month = ethiopianMonths[language][ethiopianDate.month - 1];
            return `${ethiopianDate.date} ${month} ${ethiopianDate.year}`;
        }

        return date.toLocaleDateString();
    }

    // Format number in selected language
    formatNumber(number, language) {
        const amharicNumerals = ['០', '១', '២', '៣', '៤', '៥', '៦', '៧', '៨', '៩'];

        if (language === 'am') {
            return number.toString().replace(/[0-9]/g, digit => amharicNumerals[digit]);
        }

        return number.toString();
    }

    // Get grade name in selected language
    getGradeName(grade, language) {
        const gradeNames = {
            am: {
                9: '9ኛ ክፍል',
                10: '10ኛ ክፍል',
                11: '11ኛ ክፍል',
                12: '12ኛ ክፍል'
            },
            om: {
                9: 'Kutaa 9ffaa',
                10: 'Kutaa 10ffaa',
                11: 'Kutaa 11ffaa',
                12: 'Kutaa 12ffaa'
            }
        };

        return gradeNames[language]?.[grade] || `Grade ${grade}`;
    }

    // Get subject name in selected language
    getSubjectName(subject, language) {
        return this.translate(`subject_${subject}`, language);
    }

    // Initialize default translations
    async initializeDefaultTranslations() {
        const defaultTranslations = {
            'welcome_message': {
                en: 'Welcome to School Management System',
                am: 'እንኳን ወደ ትምህርት ቤት አስተዳደር ሲስተም በደህና መጡ',
                om: 'Baga Sirna Bulchiinsa Mana Barumssatti Dhuftan',
                ctu: 'Welcome to School Management System'
            },
            'dashboard': {
                en: 'Dashboard',
                am: 'ዳሽቦርድ',
                om: 'Daashboordii',
                ctu: 'Dashboard'
            },
            'students': {
                en: 'Students',
                am: 'ተማሪዎች',
                om: 'Barattoota',
                ctu: 'Students'
            },
            'teachers': {
                en: 'Teachers',
                am: 'መምህራን',
                om: 'Barsiisota',
                ctu: 'Teachers'
            },
            'subjects': {
                en: 'Subjects',
                am: 'ትምህርቶች',
                om: 'Gosa Barnootaa',
                ctu: 'Subjects'
            },
            'grades': {
                en: 'Grades',
                am: 'ውጤቶች',
                om: 'Qabxiiwwan',
                ctu: 'Grades'
            },
            'attendance': {
                en: 'Attendance',
                am: '출석',
                om: 'Hajummaa',
                ctu: 'Attendance'
            },
            // Add more default translations as needed
        };

        for (const [key, translations] of Object.entries(defaultTranslations)) {
            if (!this.translations[key]) {
                await this.addTranslation(key, translations);
            }
        }
    }
}

export default new LanguageService();
