const functions = require('firebase-functions');
const admin = require('firebase-admin');
const nodemailer = require('nodemailer');

admin.initializeApp();

// Configure nodemailer with your email service credentials
// For production, use environment variables for these values
const transporter = nodemailer.createTransport({
  service: 'gmail',  // Replace with your email service
  auth: {
    user: '<EMAIL>',  // Replace with your email
    pass: 'your-app-password'      // Replace with your app password
  }
});

/**
 * Cloud Function to send emails when a new document is added to the 'emails' collection
 */
exports.sendEmail = functions.firestore
  .document('emails/{emailId}')
  .onCreate(async (snapshot, context) => {
    const emailData = snapshot.data();
    
    if (!emailData || !emailData.to || !emailData.subject || !emailData.html) {
      console.error('Invalid email data:', emailData);
      await snapshot.ref.update({
        status: 'error',
        error: 'Invalid email data',
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      });
      return null;
    }
    
    try {
      // Update status to processing
      await snapshot.ref.update({
        status: 'processing',
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      });
      
      // Send the email
      const mailOptions = {
        from: emailData.from || '<EMAIL>',
        to: emailData.to,
        subject: emailData.subject,
        html: emailData.html
      };
      
      const info = await transporter.sendMail(mailOptions);
      
      // Update status to sent
      await snapshot.ref.update({
        status: 'sent',
        messageId: info.messageId,
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      });
      
      console.log(`Email sent to ${emailData.to} with subject: ${emailData.subject}`);
      return null;
    } catch (error) {
      console.error('Error sending email:', error);
      
      // Update status to error
      await snapshot.ref.update({
        status: 'error',
        error: error.message,
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      });
      
      return null;
    }
  });

/**
 * Cloud Function to verify emails when a document in 'app_verifications' is updated
 */
exports.handleEmailVerification = functions.firestore
  .document('app_verifications/{verificationId}')
  .onUpdate(async (change, context) => {
    const newData = change.after.data();
    const oldData = change.before.data();
    
    // Only proceed if the verification status changed from false to true
    if (oldData.verified === false && newData.verified === true) {
      try {
        // Find the user with this email
        const usersSnapshot = await admin.firestore()
          .collection('users')
          .where('email', '==', newData.email)
          .get();
        
        if (usersSnapshot.empty) {
          console.log(`No user found with email: ${newData.email}`);
          return null;
        }
        
        // Update the user document
        const userDoc = usersSnapshot.docs[0];
        await userDoc.ref.update({
          emailVerified: true,
          updatedAt: admin.firestore.FieldValue.serverTimestamp()
        });
        
        console.log(`Email verified for user: ${newData.email}`);
        return null;
      } catch (error) {
        console.error('Error handling email verification:', error);
        return null;
      }
    }
    
    return null;
  });
