import React, { useState, useRef } from 'react';
import { View, StyleSheet, Text, TouchableOpacity, Image, Platform, ScrollView } from 'react-native';
import { Button, Surface, IconButton, ProgressBar, Portal, Dialog, Divider, List, Menu } from 'react-native-paper';
import * as DocumentPicker from 'expo-document-picker';
import * as ImagePicker from 'expo-image-picker';
import * as FileSystem from 'expo-file-system';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import * as Animatable from 'react-native-animatable';
import { useLanguage } from '../../context/LanguageContext';
import CloudinaryService from '../../services/CloudinaryService';

const FileUploader = ({
  onFileUploaded,
  onError,
  multiple = false,
  accept = ['image/*', 'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
  maxSize = 5 * 1024 * 1024, // 5MB
  maxFiles = 5,
  storagePath = 'uploads',
  showPreview = true,
  previewType = 'grid', // 'grid' or 'list'
  buttonLabel = 'Upload Files',
  buttonIcon = 'upload',
  style,
  buttonStyle,
  previewStyle,
  allowCamera = true,
  allowDelete = true,
  allowDownload = true,
  initialFiles = [],
  disabled = false,
}) => {
  const { translate, isRTL } = useLanguage();
  const [files, setFiles] = useState(initialFiles);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState({});
  const [uploadError, setUploadError] = useState(null);
  const [menuVisible, setMenuVisible] = useState(false);
  const [selectedFile, setSelectedFile] = useState(null);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [optionsMenuVisible, setOptionsMenuVisible] = useState(false);
  const [optionsMenuPosition, setOptionsMenuPosition] = useState({ x: 0, y: 0 });
  const fileInputRef = useRef(null);

  // Request permissions for camera
  const requestCameraPermission = async () => {
    if (Platform.OS !== 'web') {
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      if (status !== 'granted') {
        setUploadError('Camera permission is required to take photos');
        return false;
      }
      return true;
    }
    return true;
  };

  // Handle file selection from document picker
  const handleFilePick = async () => {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: accept,
        multiple,
        copyToCacheDirectory: true,
      });

      if (result.canceled) {
        return;
      }

      const selectedFiles = multiple ? result.assets : [result.assets[0]];

      // Check file size
      const validFiles = selectedFiles.filter(file => file.size <= maxSize);

      if (validFiles.length < selectedFiles.length) {
        setUploadError(`Some files exceed the maximum size of ${formatFileSize(maxSize)}`);
      }

      // Check max files
      if (files.length + validFiles.length > maxFiles) {
        setUploadError(`You can upload a maximum of ${maxFiles} files`);
        return;
      }

      // Process files
      processFiles(validFiles);
    } catch (error) {
      console.error('Error picking document:', error);
      setUploadError('Error selecting files');
      if (onError) onError(error);
    }
  };

  // Handle image capture from camera
  const handleCamera = async () => {
    const hasPermission = await requestCameraPermission();
    if (!hasPermission) return;

    try {
      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        quality: 0.8,
      });

      if (result.canceled) {
        return;
      }

      const selectedFiles = [result.assets[0]];

      // Check file size
      if (selectedFiles[0].fileSize > maxSize) {
        setUploadError(`File exceeds the maximum size of ${formatFileSize(maxSize)}`);
        return;
      }

      // Check max files
      if (files.length + 1 > maxFiles) {
        setUploadError(`You can upload a maximum of ${maxFiles} files`);
        return;
      }

      // Process files
      processFiles(selectedFiles);
    } catch (error) {
      console.error('Error capturing image:', error);
      setUploadError('Error capturing image');
      if (onError) onError(error);
    }
  };

  // Process selected files
  const processFiles = async (selectedFiles) => {
    try {
      setUploading(true);

      const newFiles = [];

      for (const file of selectedFiles) {
        // Generate a unique file name
        const fileName = `${Date.now()}-${file.name.replace(/[^a-zA-Z0-9.]/g, '_')}`;
        const fileType = file.mimeType || file.type || getFileTypeFromName(file.name);

        // Create file object
        const fileObj = {
          id: fileName,
          name: file.name,
          uri: file.uri,
          type: fileType,
          size: file.size,
          progress: 0,
          uploading: true,
          error: null,
          downloadUrl: null,
        };

        newFiles.push(fileObj);

        // Start upload
        uploadFile(fileObj);
      }

      setFiles([...files, ...newFiles]);
    } catch (error) {
      console.error('Error processing files:', error);
      setUploadError('Error processing files');
      setUploading(false);
      if (onError) onError(error);
    }
  };

  // Upload file to Cloudinary
  const uploadFile = async (fileObj) => {
    try {
      // Determine resource type based on file type
      const fileType = fileObj.type || 'application/octet-stream';
      const resourceType = fileType.startsWith('image/') ? 'image' :
                          fileType.startsWith('video/') ? 'video' : 'raw';

      // Simulate progress updates since Cloudinary doesn't provide progress events
      const updateProgress = (progress) => {
        setUploadProgress(prev => ({
          ...prev,
          [fileObj.id]: progress
        }));

        // Update file object
        setFiles(prevFiles =>
          prevFiles.map(file =>
            file.id === fileObj.id
              ? { ...file, progress, uploading: true }
              : file
          )
        );
      };

      // Start with initial progress
      updateProgress(0.1);

      // Simulate progress updates
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          const currentProgress = prev[fileObj.id] || 0;
          if (currentProgress < 0.9) {
            const newProgress = currentProgress + 0.1;
            updateProgress(newProgress);
            return { ...prev, [fileObj.id]: newProgress };
          }
          return prev;
        });
      }, 500);

      try {
        // Upload to Cloudinary
        const result = await CloudinaryService.uploadFile(fileObj.uri, {
          folder: storagePath,
          resourceType: resourceType,
          tags: [fileObj.type, fileObj.id],
          publicId: `${storagePath}_${fileObj.id}`
        });

        // Clear the progress interval
        clearInterval(progressInterval);

        // Set progress to 100%
        updateProgress(1);

        // Update file object with Cloudinary URL
        const updatedFile = {
          ...fileObj,
          downloadUrl: result.url,
          publicId: result.publicId,
          uploading: false,
          progress: 1
        };

        setFiles(prevFiles =>
          prevFiles.map(file =>
            file.id === fileObj.id ? updatedFile : file
          )
        );

        // Check if all files are uploaded
        const allUploaded = files.every(file => !file.uploading);
        if (allUploaded) {
          setUploading(false);
        }

        // Notify parent component
        if (onFileUploaded) {
          onFileUploaded(updatedFile);
        }
      } catch (error) {
        // Clear the progress interval
        clearInterval(progressInterval);
        throw error;
      }
    } catch (error) {
      console.error('Error uploading file:', error);
      setFiles(prevFiles =>
        prevFiles.map(file =>
          file.id === fileObj.id
            ? { ...file, error: 'Upload failed', uploading: false }
            : file
        )
      );
      if (onError) onError(error);
    }
  };

  // Delete file
  const deleteFile = (fileId) => {
    setFiles(prevFiles => prevFiles.filter(file => file.id !== fileId));
    setOptionsMenuVisible(false);
  };

  // Download file
  const downloadFile = async (file) => {
    try {
      // Get the download URL (either Cloudinary URL or direct URL)
      const downloadUrl = file.downloadUrl || file.url;

      if (!downloadUrl) {
        throw new Error('No download URL available');
      }

      if (Platform.OS === 'web') {
        // For web, open the URL in a new tab
        window.open(downloadUrl, '_blank');
      } else {
        // For mobile, download the file
        const fileUri = FileSystem.documentDirectory + file.name;
        const downloadResumable = FileSystem.createDownloadResumable(
          downloadUrl,
          fileUri,
          {},
          (downloadProgress) => {
            const progress = downloadProgress.totalBytesWritten / downloadProgress.totalBytesExpectedToWrite;
            console.log(`Download progress: ${progress * 100}%`);
          }
        );

        const { uri } = await downloadResumable.downloadAsync();
        console.log('File downloaded to:', uri);
      }

      setOptionsMenuVisible(false);
    } catch (error) {
      console.error('Error downloading file:', error);
      setUploadError('Error downloading file');
      if (onError) onError(error);
    }
  };

  // Show file preview
  const showPreviewDialog = (file) => {
    setSelectedFile(file);
    setPreviewVisible(true);
    setOptionsMenuVisible(false);
  };

  // Show options menu
  const showOptionsMenu = (file, event) => {
    setSelectedFile(file);
    setOptionsMenuPosition({
      x: event.nativeEvent.pageX,
      y: event.nativeEvent.pageY,
    });
    setOptionsMenuVisible(true);
  };

  // Get file icon based on type
  const getFileIcon = (fileType) => {
    if (fileType.startsWith('image/')) return 'file-image';
    if (fileType.startsWith('video/')) return 'file-video';
    if (fileType.startsWith('audio/')) return 'file-music';
    if (fileType.includes('pdf')) return 'file-pdf-box';
    if (fileType.includes('word')) return 'file-word';
    if (fileType.includes('excel') || fileType.includes('sheet')) return 'file-excel';
    if (fileType.includes('powerpoint') || fileType.includes('presentation')) return 'file-powerpoint';
    if (fileType.includes('zip') || fileType.includes('compressed')) return 'file-zip';
    if (fileType.includes('text')) return 'file-document';
    return 'file';
  };

  // Get file type from name
  const getFileTypeFromName = (fileName) => {
    const extension = fileName.split('.').pop().toLowerCase();

    const mimeTypes = {
      jpg: 'image/jpeg',
      jpeg: 'image/jpeg',
      png: 'image/png',
      gif: 'image/gif',
      pdf: 'application/pdf',
      doc: 'application/msword',
      docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      xls: 'application/vnd.ms-excel',
      xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      ppt: 'application/vnd.ms-powerpoint',
      pptx: 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      txt: 'text/plain',
      zip: 'application/zip',
      mp3: 'audio/mpeg',
      mp4: 'video/mp4',
    };

    return mimeTypes[extension] || 'application/octet-stream';
  };

  // Format file size
  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Render file preview
  const renderFilePreview = () => {
    if (!showPreview || files.length === 0) return null;

    if (previewType === 'grid') {
      return (
        <View style={[styles.previewGrid, previewStyle]}>
          {files.map((file, index) => (
            <Animatable.View
              key={file.id}
              animation="fadeIn"
              duration={300}
              delay={index * 100}
            >
              <Surface style={styles.fileCard}>
                <TouchableOpacity
                  style={styles.fileCardContent}
                  onPress={() => showPreviewDialog(file)}
                  onLongPress={(e) => showOptionsMenu(file, e)}
                >
                  {file.type.startsWith('image/') ? (
                    <Image
                      source={{ uri: file.uri }}
                      style={styles.fileImage}
                      resizeMode="cover"
                    />
                  ) : (
                    <View style={styles.fileIconContainer}>
                      <MaterialCommunityIcons
                        name={getFileIcon(file.type)}
                        size={40}
                        color="#757575"
                      />
                    </View>
                  )}

                  <View style={styles.fileInfo}>
                    <Text style={styles.fileName} numberOfLines={1}>
                      {file.name}
                    </Text>
                    <Text style={styles.fileSize}>
                      {formatFileSize(file.size)}
                    </Text>
                  </View>

                  {file.uploading && (
                    <View style={styles.progressContainer}>
                      <ProgressBar
                        progress={uploadProgress[file.id] || 0}
                        color="#2196F3"
                        style={styles.progressBar}
                      />
                    </View>
                  )}

                  {file.error && (
                    <View style={styles.errorContainer}>
                      <MaterialCommunityIcons
                        name="alert-circle"
                        size={16}
                        color="#F44336"
                      />
                      <Text style={styles.errorText}>{file.error}</Text>
                    </View>
                  )}
                </TouchableOpacity>

                {allowDelete && !file.uploading && (
                  <IconButton
                    icon="close"
                    size={16}
                    style={styles.deleteButton}
                    onPress={() => deleteFile(file.id)}
                  />
                )}
              </Surface>
            </Animatable.View>
          ))}
        </View>
      );
    } else {
      return (
        <View style={[styles.previewList, previewStyle]}>
          {files.map((file, index) => (
            <Animatable.View
              key={file.id}
              animation="fadeIn"
              duration={300}
              delay={index * 100}
            >
              <Surface style={styles.fileListItem}>
                <TouchableOpacity
                  style={styles.fileListContent}
                  onPress={() => showPreviewDialog(file)}
                  onLongPress={(e) => showOptionsMenu(file, e)}
                >
                  {file.type.startsWith('image/') ? (
                    <Image
                      source={{ uri: file.uri }}
                      style={styles.fileListImage}
                      resizeMode="cover"
                    />
                  ) : (
                    <View style={styles.fileListIconContainer}>
                      <MaterialCommunityIcons
                        name={getFileIcon(file.type)}
                        size={24}
                        color="#757575"
                      />
                    </View>
                  )}

                  <View style={styles.fileListInfo}>
                    <Text style={styles.fileListName} numberOfLines={1}>
                      {file.name}
                    </Text>
                    <Text style={styles.fileListSize}>
                      {formatFileSize(file.size)}
                    </Text>
                  </View>

                  {file.uploading && (
                    <View style={styles.fileListProgressContainer}>
                      <ProgressBar
                        progress={uploadProgress[file.id] || 0}
                        color="#2196F3"
                        style={styles.fileListProgressBar}
                      />
                    </View>
                  )}

                  {file.error && (
                    <View style={styles.fileListErrorContainer}>
                      <MaterialCommunityIcons
                        name="alert-circle"
                        size={16}
                        color="#F44336"
                      />
                      <Text style={styles.fileListErrorText}>{file.error}</Text>
                    </View>
                  )}
                </TouchableOpacity>

                {allowDelete && !file.uploading && (
                  <IconButton
                    icon="close"
                    size={16}
                    style={styles.fileListDeleteButton}
                    onPress={() => deleteFile(file.id)}
                  />
                )}
              </Surface>

              {index < files.length - 1 && <Divider />}
            </Animatable.View>
          ))}
        </View>
      );
    }
  };

  return (
    <View style={[styles.container, style]}>
      <View style={styles.buttonContainer}>
        <Button
          mode="contained"
          icon={buttonIcon}
          onPress={() => setMenuVisible(true)}
          style={[styles.uploadButton, buttonStyle]}
          disabled={disabled || uploading || files.length >= maxFiles}
        >
          {translate(buttonLabel) || buttonLabel}
        </Button>

        <Menu
          visible={menuVisible}
          onDismiss={() => setMenuVisible(false)}
          anchor={{ x: 0, y: 0 }}
        >
          <Menu.Item
            icon="file-upload"
            onPress={() => {
              setMenuVisible(false);
              handleFilePick();
            }}
            title={translate('fileUploader.browseFiles') || 'Browse Files'}
          />

          {allowCamera && (
            <Menu.Item
              icon="camera"
              onPress={() => {
                setMenuVisible(false);
                handleCamera();
              }}
              title={translate('fileUploader.takePhoto') || 'Take Photo'}
            />
          )}
        </Menu>

        {uploadError && (
          <Animatable.View animation="shake" duration={500}>
            <Text style={styles.errorText}>{uploadError}</Text>
          </Animatable.View>
        )}
      </View>

      {renderFilePreview()}

      <Portal>
        <Dialog
          visible={previewVisible}
          onDismiss={() => setPreviewVisible(false)}
          style={styles.previewDialog}
        >
          {selectedFile && (
            <>
              <Dialog.Title>{selectedFile.name}</Dialog.Title>
              <Dialog.Content>
                {selectedFile.type.startsWith('image/') ? (
                  <Image
                    source={{ uri: selectedFile.uri }}
                    style={styles.previewImage}
                    resizeMode="contain"
                  />
                ) : (
                  <View style={styles.previewIconContainer}>
                    <MaterialCommunityIcons
                      name={getFileIcon(selectedFile.type)}
                      size={80}
                      color="#757575"
                    />
                    <Text style={styles.previewText}>
                      {formatFileSize(selectedFile.size)}
                    </Text>
                  </View>
                )}
              </Dialog.Content>
              <Dialog.Actions>
                {allowDownload && selectedFile.downloadUrl && (
                  <Button
                    onPress={() => downloadFile(selectedFile)}
                    icon="download"
                  >
                    {translate('fileUploader.download') || 'Download'}
                  </Button>
                )}
                <Button
                  onPress={() => setPreviewVisible(false)}
                >
                  {translate('common.close') || 'Close'}
                </Button>
              </Dialog.Actions>
            </>
          )}
        </Dialog>
      </Portal>

      <Menu
        visible={optionsMenuVisible}
        onDismiss={() => setOptionsMenuVisible(false)}
        anchor={optionsMenuPosition}
      >
        <Menu.Item
          icon="eye"
          onPress={() => showPreviewDialog(selectedFile)}
          title={translate('fileUploader.preview') || 'Preview'}
        />

        {allowDownload && selectedFile?.downloadUrl && (
          <Menu.Item
            icon="download"
            onPress={() => downloadFile(selectedFile)}
            title={translate('fileUploader.download') || 'Download'}
          />
        )}

        {allowDelete && (
          <Menu.Item
            icon="delete"
            onPress={() => deleteFile(selectedFile?.id)}
            title={translate('fileUploader.delete') || 'Delete'}
          />
        )}
      </Menu>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 8,
  },
  buttonContainer: {
    marginBottom: 16,
  },
  uploadButton: {
    borderRadius: 8,
  },
  errorText: {
    color: '#F44336',
    marginTop: 8,
  },
  previewGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -4,
  },
  fileCard: {
    width: 120,
    height: 160,
    margin: 4,
    borderRadius: 8,
    overflow: 'hidden',
    elevation: 2,
  },
  fileCardContent: {
    flex: 1,
  },
  fileImage: {
    width: '100%',
    height: 100,
    backgroundColor: '#f5f5f5',
  },
  fileIconContainer: {
    width: '100%',
    height: 100,
    backgroundColor: '#f5f5f5',
    justifyContent: 'center',
    alignItems: 'center',
  },
  fileInfo: {
    padding: 8,
  },
  fileName: {
    fontSize: 12,
    fontWeight: '500',
  },
  fileSize: {
    fontSize: 10,
    color: '#757575',
    marginTop: 2,
  },
  progressContainer: {
    padding: 8,
    paddingTop: 0,
  },
  progressBar: {
    height: 4,
    borderRadius: 2,
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    paddingTop: 0,
  },
  deleteButton: {
    position: 'absolute',
    top: 0,
    right: 0,
    margin: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  previewList: {
    borderRadius: 8,
    overflow: 'hidden',
    elevation: 2,
    backgroundColor: 'white',
  },
  fileListItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
  },
  fileListContent: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  fileListImage: {
    width: 48,
    height: 48,
    borderRadius: 4,
    backgroundColor: '#f5f5f5',
  },
  fileListIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 4,
    backgroundColor: '#f5f5f5',
    justifyContent: 'center',
    alignItems: 'center',
  },
  fileListInfo: {
    flex: 1,
    marginLeft: 16,
  },
  fileListName: {
    fontSize: 14,
    fontWeight: '500',
  },
  fileListSize: {
    fontSize: 12,
    color: '#757575',
    marginTop: 2,
  },
  fileListProgressContainer: {
    width: '100%',
    paddingHorizontal: 16,
    position: 'absolute',
    bottom: 0,
    left: 64,
  },
  fileListProgressBar: {
    height: 4,
    borderRadius: 2,
  },
  fileListErrorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 16,
  },
  fileListErrorText: {
    fontSize: 12,
    color: '#F44336',
    marginLeft: 4,
  },
  fileListDeleteButton: {
    margin: 0,
  },
  previewDialog: {
    borderRadius: 8,
  },
  previewImage: {
    width: '100%',
    height: 300,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
  },
  previewIconContainer: {
    width: '100%',
    height: 200,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  previewText: {
    marginTop: 16,
    color: '#757575',
  },
});

export default FileUploader;
