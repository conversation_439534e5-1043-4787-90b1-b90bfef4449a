import React, { useState } from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import { Text } from 'react-native-paper';
import CustomInput from '../../components/common/CustomInput';
import CustomButton from '../../components/common/CustomButton';
import { useAuth } from '../../context/AuthContext';

const RegisterScreen = ({ navigation }) => {
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    confirmPassword: '',
    role: 'student', // Default role
  });
  const [error, setError] = useState('');
  const { register } = useAuth();

  const handleRegister = async () => {
    // Validation
    if (!formData.firstName || !formData.lastName || !formData.email || !formData.password) {
      setError('Please fill in all fields');
      return;
    }
    
    if (formData.password !== formData.confirmPassword) {
      setError('Passwords do not match');
      return;
    }

    const userData = {
      firstName: formData.firstName,
      lastName: formData.lastName,
      displayName: `${formData.firstName} ${formData.lastName}`,
    };

    const result = await register(formData.email, formData.password, formData.role, userData);
    if (!result.success) {
      setError(result.error);
    }
  };

  const updateFormData = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>Create Account</Text>
      
      {error ? <Text style={styles.error}>{error}</Text> : null}
      
      <CustomInput
        label="First Name"
        value={formData.firstName}
        onChangeText={(value) => updateFormData('firstName', value)}
      />
      
      <CustomInput
        label="Last Name"
        value={formData.lastName}
        onChangeText={(value) => updateFormData('lastName', value)}
      />
      
      <CustomInput
        label="Email"
        value={formData.email}
        onChangeText={(value) => updateFormData('email', value)}
        autoCapitalize="none"
        keyboardType="email-address"
      />
      
      <CustomInput
        label="Password"
        value={formData.password}
        onChangeText={(value) => updateFormData('password', value)}
        secureTextEntry
      />
      
      <CustomInput
        label="Confirm Password"
        value={formData.confirmPassword}
        onChangeText={(value) => updateFormData('confirmPassword', value)}
        secureTextEntry
      />
      
      <CustomButton onPress={handleRegister}>
        Register
      </CustomButton>
      
      <CustomButton 
        mode="text" 
        onPress={() => navigation.navigate('Login')}
      >
        Already have an account? Login
      </CustomButton>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginVertical: 20,
  },
  error: {
    color: 'red',
    textAlign: 'center',
    marginBottom: 10,
  },
});

export default RegisterScreen;
