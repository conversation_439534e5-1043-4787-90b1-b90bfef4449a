import { Alert } from 'react-native';

/**
 * Displays all missing translations for debugging
 * @param {string} language - The current language code 
 */
export const displayMissingTranslations = (language) => {
  if (!global.missingTranslations || !global.missingTranslations[language]) {
    console.log(`No missing translations for language: ${language}`);
    return;
  }

  const missingCount = global.missingTranslations[language].size;
  console.log(`\n==== MISSING TRANSLATIONS FOR ${language.toUpperCase()} (${missingCount}) ====`);
  
  // Group missing translations by screen/section
  const grouped = {};
  
  global.missingTranslations[language].forEach(key => {
    const [section, ...rest] = key.split('.');
    if (!grouped[section]) {
      grouped[section] = [];
    }
    grouped[section].push(key);
  });
  
  // Log each group
  Object.entries(grouped).sort().forEach(([section, keys]) => {
    console.log(`\n[${section}] - ${keys.length} missing translations:`);
    keys.sort().forEach(key => {
      console.log(`  "${key}": "",`);
    });
  });
  
  console.log('\n==== END MISSING TRANSLATIONS ====\n');
};

/**
 * Exports all missing translations in JSON format for the given language
 * @param {string} language - The language code to export translations for
 * @returns {string} - JSON string of missing translations template
 */
export const exportMissingTranslationsTemplate = (language) => {
  if (!global.missingTranslations || !global.missingTranslations[language]) {
    return '{}';
  }
  
  // Create a structured object from flat keys
  const template = {};
  global.missingTranslations[language].forEach(key => {
    const parts = key.split('.');
    let current = template;
    
    // Build nested structure
    for (let i = 0; i < parts.length; i++) {
      const part = parts[i];
      if (i === parts.length - 1) {
        // Leaf node - actual translation key
        current[part] = '';
      } else {
        // Create intermediate node if doesn't exist
        if (!current[part]) {
          current[part] = {};
        }
        current = current[part];
      }
    }
  });
  
  return JSON.stringify(template, null, 2);
};

/**
 * Displays a popup with export options for missing translations
 * @param {string} language - The language code to export
 */
export const showTranslationExportMenu = (language) => {
  const template = exportMissingTranslationsTemplate(language);
  const missingCount = global.missingTranslations?.[language]?.size || 0;
  
  if (missingCount === 0) {
    Alert.alert(
      'No Missing Translations',
      `All translations are available for ${language}.`
    );
    return;
  }
  
  // Display alert with info about missing translations
  Alert.alert(
    'Missing Translations',
    `Found ${missingCount} missing translations for ${language}. Check console for details.`,
    [
      { text: 'OK' }
    ]
  );
  
  // Also output to console for easy copying
  console.log('\n==== TRANSLATION TEMPLATE ====');
  console.log(template);
  console.log('==== END TEMPLATE ====\n');
};

/**
 * Resets the missing translations tracker
 */
export const resetMissingTranslations = () => {
  global.missingTranslations = {};
  console.log('Cleared missing translations tracker.');
};

export default {
  displayMissingTranslations,
  exportMissingTranslationsTemplate,
  showTranslationExportMenu,
  resetMissingTranslations
}; 