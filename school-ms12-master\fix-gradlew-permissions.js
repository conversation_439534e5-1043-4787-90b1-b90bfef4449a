const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Paths to gradlew files
const gradlewPath = path.join(__dirname, 'android', 'gradlew');
const gradlewBatPath = path.join(__dirname, 'android', 'gradlew.bat');

// Ensure the gradlew file is executable
try {
  console.log('Making gradlew executable...');
  if (process.platform === 'win32') {
    // On Windows, we don't need to change permissions directly
    console.log('Windows detected, creating wrapper batch file...');
    
    // Create a wrapper batch file if it doesn't exist
    if (!fs.existsSync(gradlewBatPath)) {
      fs.writeFileSync(
        gradlewBatPath,
        '@echo off\r\ngradlew.bat %*\r\n',
        { encoding: 'utf8' }
      );
      console.log('Created gradlew.bat wrapper');
    }
  } else {
    // On Unix systems, use chmod to make gradlew executable
    execSync(`chmod +x ${gradlewPath}`);
    console.log('Made gradlew executable');
  }

  // Copy the gradlew files to a build directory for EAS
  const buildDir = path.join(__dirname, 'build');
  const androidBuildDir = path.join(buildDir, 'android');
  
  if (!fs.existsSync(buildDir)) {
    fs.mkdirSync(buildDir);
    console.log('Created build directory');
  }
  
  if (!fs.existsSync(androidBuildDir)) {
    fs.mkdirSync(androidBuildDir);
    console.log('Created build/android directory');
  }
  
  // Copy gradlew files
  if (fs.existsSync(gradlewPath)) {
    fs.copyFileSync(gradlewPath, path.join(androidBuildDir, 'gradlew'));
    console.log('Copied gradlew to build/android directory');
  }
  
  if (fs.existsSync(gradlewBatPath)) {
    fs.copyFileSync(gradlewBatPath, path.join(androidBuildDir, 'gradlew.bat'));
    console.log('Copied gradlew.bat to build/android directory');
  }
  
  console.log('Successfully prepared gradlew files for EAS build');
} catch (error) {
  console.error('Error processing gradlew files:', error);
} 