import React from 'react';
import { TextInput as PaperTextInput } from 'react-native-paper';
import { StyleSheet, Text } from 'react-native';

/**
 * A simple wrapper around React Native Paper's TextInput that directly passes props
 * without trying to modify the render functions
 */
const FixedTextInput = React.forwardRef((props, ref) => {
  // Just pass all props directly to PaperTextInput
  return (
    <PaperTextInput
      ref={ref}
      {...props}
    />
  );
});

// Add the Icon property from the original TextInput
FixedTextInput.Icon = PaperTextInput.Icon;

export default FixedTextInput;
