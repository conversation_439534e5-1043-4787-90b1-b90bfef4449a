import React, { useState, useEffect } from 'react';
import { View, ScrollView, StyleSheet } from 'react-native';
import {
  Card,
  Title,
  Text,
  Portal,
  Modal,
  ActivityIndicator,
  useTheme,
  Divider,
  List,
  Chip,
  DataTable,
} from 'react-native-paper';
import { auth } from '../../config/firebase';
import ReportGenerator from '../../utils/ReportGenerator';
import CustomButton from '../../components/common/CustomButton';
import EthiopianCalendar from '../../utils/EthiopianCalendar';

const ReportCenter = ({ navigation, route }) => {
  const [loading, setLoading] = useState(false);
  const [selectedReport, setSelectedReport] = useState(null);
  const [reportData, setReportData] = useState(null);
  const [modalVisible, setModalVisible] = useState(false);
  // No theme needed

  const reportTypes = [
    { id: 'academic', label: 'Academic Report', icon: 'school' },
    { id: 'attendance', label: 'Attendance Report', icon: 'calendar-check' },
    { id: 'behavior', label: 'Behavior Report', icon: 'account-alert' },
    { id: 'comprehensive', label: 'Comprehensive Report', icon: 'file-document' },
  ];

  const handleGenerateReport = async (type) => {
    try {
      setLoading(true);
      setSelectedReport(type);

      let data;
      if (route.params?.classId) {
        data = await ReportGenerator.generateClassReport(route.params.classId, type);
      } else if (route.params?.studentId) {
        data = await ReportGenerator.generateStudentReport(route.params.studentId, type);
      } else {
        // For students viewing their own reports
        data = await ReportGenerator.generateStudentReport(auth.currentUser.uid, type);
      }

      setReportData(data);
      setModalVisible(true);
    } catch (error) {
      console.error('Error generating report:', error);
      // Show error message to user
    } finally {
      setLoading(false);
    }
  };

  const handleExportReport = async () => {
    try {
      setLoading(true);
      const filename = `${selectedReport}_report_${new Date().getTime()}`;
      await ReportGenerator.exportToCSV(reportData, filename);
    } catch (error) {
      console.error('Error exporting report:', error);
      // Show error message to user
    } finally {
      setLoading(false);
    }
  };

  const renderAcademicReport = () => (
    <View>
      <Title style={styles.sectionTitle}>Academic Performance</Title>
      
      <DataTable>
        <DataTable.Header>
          <DataTable.Title>Subject</DataTable.Title>
          <DataTable.Title numeric>Average Score</DataTable.Title>
        </DataTable.Header>

        {reportData.academic.subjectAverages.map((subject, index) => (
          <DataTable.Row key={index}>
            <DataTable.Cell>{subject.subjectName}</DataTable.Cell>
            <DataTable.Cell numeric>{subject.average.toFixed(2)}%</DataTable.Cell>
          </DataTable.Row>
        ))}
      </DataTable>

      <Card style={styles.statsCard}>
        <Card.Content>
          <Title>Overall Average: {reportData.academic.overallAverage.toFixed(2)}%</Title>
        </Card.Content>
      </Card>
    </View>
  );

  const renderAttendanceReport = () => (
    <View>
      <Title style={styles.sectionTitle}>Attendance Summary</Title>
      
      <Card style={styles.statsCard}>
        <Card.Content>
          <View style={styles.statsRow}>
            <View style={styles.statItem}>
              <Text style={styles.statLabel}>Total Days</Text>
              <Text style={styles.statValue}>{reportData.attendance.statistics.totalDays}</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statLabel}>Present</Text>
              <Text style={styles.statValue}>{reportData.attendance.statistics.presentDays}</Text>
            </View>
          </View>
          <View style={styles.statsRow}>
            <View style={styles.statItem}>
              <Text style={styles.statLabel}>Absent</Text>
              <Text style={styles.statValue}>{reportData.attendance.statistics.absentDays}</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statLabel}>Late</Text>
              <Text style={styles.statValue}>{reportData.attendance.statistics.lateDays}</Text>
            </View>
          </View>
          <Divider style={styles.divider} />
          <Title>Attendance Rate: {reportData.attendance.statistics.attendanceRate.toFixed(2)}%</Title>
        </Card.Content>
      </Card>
    </View>
  );

  const renderBehaviorReport = () => (
    <View>
      <Title style={styles.sectionTitle}>Behavior Records</Title>
      
      <Card style={styles.statsCard}>
        <Card.Content>
          <Title>Total Records: {reportData.behavior.totalRecords}</Title>
          <Divider style={styles.divider} />
          <View style={styles.categoriesContainer}>
            {Object.entries(reportData.behavior.categories).map(([category, count]) => (
              <Chip
                key={category}
                style={styles.categoryChip}
                mode="outlined"
              >
                {category}: {count}
              </Chip>
            ))}
          </View>
        </Card.Content>
      </Card>

      <List.Section>
        <List.Subheader>Recent Records</List.Subheader>
        {reportData.behavior.records.slice(0, 5).map((record, index) => (
          <List.Item
            key={index}
            title={record.description}
            description={EthiopianCalendar.formatDate(new Date(record.date))}
            left={props => <List.Icon {...props} icon="bookmark" />}
          />
        ))}
      </List.Section>
    </View>
  );

  const renderComprehensiveReport = () => (
    <ScrollView>
      {renderAcademicReport()}
      <Divider style={styles.sectionDivider} />
      {renderAttendanceReport()}
      <Divider style={styles.sectionDivider} />
      {renderBehaviorReport()}
    </ScrollView>
  );

  const renderReportContent = () => {
    switch (selectedReport) {
      case 'academic':
        return renderAcademicReport();
      case 'attendance':
        return renderAttendanceReport();
      case 'behavior':
        return renderBehaviorReport();
      case 'comprehensive':
        return renderComprehensiveReport();
      default:
        return null;
    }
  };

  return (
    <View style={styles.container}>
      <ScrollView>
        <View style={styles.grid}>
          {reportTypes.map((type) => (
            <Card
              key={type.id}
              style={styles.card}
              onPress={() => handleGenerateReport(type.id)}
            >
              <Card.Content>
                <Title>{type.label}</Title>
                <Text>Generate {type.label.toLowerCase()}</Text>
              </Card.Content>
            </Card>
          ))}
        </View>
      </ScrollView>

      <Portal>
        <Modal
          visible={modalVisible}
          onDismiss={() => {
            setModalVisible(false);
            setSelectedReport(null);
            setReportData(null);
          }}
          contentContainerStyle={styles.modalContent}
        >
          {loading ? (
            <ActivityIndicator size="large" />
          ) : reportData ? (
            <ScrollView>
              <View style={styles.modalHeader}>
                <Title>
                  {reportTypes.find(t => t.id === selectedReport)?.label}
                </Title>
                <Text style={styles.dateText}>
                  Generated on: {EthiopianCalendar.formatDate(new Date(reportData.generatedAt))}
                </Text>
              </View>

              <Divider style={styles.divider} />

              {renderReportContent()}

              <View style={styles.modalActions}>
                <CustomButton
                  mode="contained"
                  onPress={handleExportReport}
                  style={styles.exportButton}
                >
                  Export to CSV
                </CustomButton>
                <CustomButton
                  mode="outlined"
                  onPress={() => {
                    setModalVisible(false);
                    setSelectedReport(null);
                    setReportData(null);
                  }}
                >
                  Close
                </CustomButton>
              </View>
            </ScrollView>
          ) : null}
        </Modal>
      </Portal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  grid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    padding: 8,
  },
  card: {
    width: '45%',
    margin: '2.5%',
  },
  modalContent: {
    backgroundColor: 'white',
    margin: 20,
    padding: 20,
    borderRadius: 8,
    maxHeight: '80%',
  },
  modalHeader: {
    marginBottom: 16,
  },
  dateText: {
    color: '#666',
    marginTop: 4,
  },
  divider: {
    marginVertical: 16,
  },
  sectionDivider: {
    marginVertical: 24,
  },
  sectionTitle: {
    marginBottom: 16,
  },
  statsCard: {
    marginVertical: 16,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statLabel: {
    color: '#666',
    marginBottom: 4,
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  categoriesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 8,
  },
  categoryChip: {
    margin: 4,
  },
  modalActions: {
    marginTop: 24,
  },
  exportButton: {
    marginBottom: 8,
  },
});

export default ReportCenter;
