import { db } from '../config/firebase';
import { collection, addDoc, query, where, getDocs, orderBy, limit } from 'firebase/firestore';

class MonitoringService {
    static instance = null;

    constructor() {
        if (MonitoringService.instance) {
            return MonitoringService.instance;
        }
        MonitoringService.instance = this;

        this.metrics = {
            performance: new Map(),
            errors: new Map(),
            usage: new Map()
        };

        this.startMonitoring();
    }

    // Start system monitoring
    startMonitoring() {
        // Monitor system performance
        this.monitorPerformance();

        // Monitor error rates
        this.monitorErrors();

        // Monitor system usage
        this.monitorUsage();

        // Regular health checks
        this.scheduleHealthChecks();
    }

    // Performance monitoring
    async monitorPerformance() {
        // Removed automatic monitoring interval that was causing disturbance
        // Performance metrics can be collected on-demand or through manual triggers
        console.log('Automatic performance monitoring disabled to prevent disturbance');

        // One-time collection of metrics for initial state
        const metrics = {
            timestamp: Date.now(),
            memory: performance.memory ? {
                usedJSHeapSize: performance.memory.usedJSHeapSize,
                totalJSHeapSize: performance.memory.totalJSHeapSize
            } : null,
            navigation: {
                type: performance.navigation.type,
                redirectCount: performance.navigation.redirectCount
            },
            timing: performance.timing.toJSON()
        };

        await this.logMetrics('performance', metrics);
    }

    // Error monitoring
    async monitorErrors() {
        window.onerror = async (message, source, lineno, colno, error) => {
            const errorData = {
                message,
                source,
                lineno,
                colno,
                stack: error ? error.stack : null,
                timestamp: Date.now()
            };

            await this.logMetrics('errors', errorData);

            // Alert if error rate is high
            this.analyzeErrorRate();
        };
    }

    // Usage monitoring
    async monitorUsage() {
        // Removed automatic usage monitoring interval that was causing disturbance
        // Usage metrics can be collected on-demand or through manual triggers
        console.log('Automatic usage monitoring disabled to prevent disturbance');

        // One-time collection of metrics for initial state
        const activeUsers = await this.getActiveUsers();
        const usageData = {
            timestamp: Date.now(),
            activeUsers,
            resourceUsage: await this.getResourceUsage()
        };

        await this.logMetrics('usage', usageData);
    }

    // Health checks
    scheduleHealthChecks() {
        // Removed automatic health check interval that was causing disturbance
        // Health checks can be performed on-demand or through manual triggers
        console.log('Automatic health checks disabled to prevent disturbance');

        // One-time health check for initial state
        this.performHealthCheck();
    },

    // Perform a single health check
    async performHealthCheck() {
        const health = await this.checkSystemHealth();
        if (!health.healthy) {
            await this.notifyAdmins(health.issues);
        }
    }

    // Log metrics to Firestore
    async logMetrics(type, data) {
        try {
            await addDoc(collection(db, 'system_metrics'), {
                type,
                data,
                timestamp: Date.now()
            });
        } catch (error) {
            console.error('Failed to log metrics:', error);
        }
    }

    // Analyze performance metrics
    async analyzePerformance(metrics) {
        const thresholds = {
            heapUsage: 0.9, // 90% heap usage
            loadTime: 5000, // 5 seconds
            responseTime: 2000 // 2 seconds
        };

        const issues = [];

        // Check heap usage
        if (metrics.memory) {
            const heapUsage = metrics.memory.usedJSHeapSize / metrics.memory.totalJSHeapSize;
            if (heapUsage > thresholds.heapUsage) {
                issues.push({
                    type: 'high_memory_usage',
                    value: heapUsage,
                    threshold: thresholds.heapUsage
                });
            }
        }

        // Check page load time
        const loadTime = metrics.timing.loadEventEnd - metrics.timing.navigationStart;
        if (loadTime > thresholds.loadTime) {
            issues.push({
                type: 'slow_page_load',
                value: loadTime,
                threshold: thresholds.loadTime
            });
        }

        if (issues.length > 0) {
            await this.notifyAdmins(issues);
        }
    }

    // Analyze error rates
    async analyzeErrorRate() {
        const timeWindow = 5 * 60 * 1000; // 5 minutes
        const maxErrors = 10; // Maximum allowed errors in time window

        const q = query(
            collection(db, 'system_metrics'),
            where('type', '==', 'errors'),
            where('timestamp', '>', Date.now() - timeWindow),
            orderBy('timestamp', 'desc')
        );

        const querySnapshot = await getDocs(q);
        if (querySnapshot.size > maxErrors) {
            await this.notifyAdmins([{
                type: 'high_error_rate',
                count: querySnapshot.size,
                timeWindow
            }]);
        }
    }

    // Get active users
    async getActiveUsers() {
        const timeWindow = 15 * 60 * 1000; // 15 minutes
        const q = query(
            collection(db, 'sessions'),
            where('lastActivity', '>', Date.now() - timeWindow)
        );

        const querySnapshot = await getDocs(q);
        return querySnapshot.size;
    }

    // Get resource usage
    async getResourceUsage() {
        return {
            cpu: await this.getCPUUsage(),
            memory: await this.getMemoryUsage(),
            storage: await this.getStorageUsage(),
            network: await this.getNetworkUsage()
        };
    }

    // Check system health
    async checkSystemHealth() {
        const issues = [];

        // Check database connectivity
        try {
            await getDocs(query(collection(db, 'health_check'), limit(1)));
        } catch (error) {
            issues.push({
                type: 'database_connectivity',
                error: error.message
            });
        }

        // Check resource usage
        const usage = await this.getResourceUsage();
        if (usage.memory > 90) {
            issues.push({
                type: 'high_memory_usage',
                value: usage.memory
            });
        }

        return {
            healthy: issues.length === 0,
            issues,
            timestamp: Date.now()
        };
    }

    // Notify administrators
    async notifyAdmins(issues) {
        try {
            await addDoc(collection(db, 'admin_notifications'), {
                type: 'system_alert',
                issues,
                timestamp: Date.now(),
                status: 'unread'
            });
        } catch (error) {
            console.error('Failed to notify admins:', error);
        }
    }

    // Get CPU usage
    async getCPUUsage() {
        // Implementation depends on hosting environment
        return 0;
    }

    // Get memory usage
    async getMemoryUsage() {
        if (performance.memory) {
            return (performance.memory.usedJSHeapSize / performance.memory.totalJSHeapSize) * 100;
        }
        return 0;
    }

    // Get storage usage
    async getStorageUsage() {
        try {
            const { quota, usage } = await navigator.storage.estimate();
            return (usage / quota) * 100;
        } catch (error) {
            return 0;
        }
    }

    // Get network usage
    async getNetworkUsage() {
        if (navigator.connection) {
            return {
                type: navigator.connection.effectiveType,
                downlink: navigator.connection.downlink,
                rtt: navigator.connection.rtt
            };
        }
        return null;
    }

    // Track resource usage events
    async trackResourceUsage(resourceType, action) {
        try {
            const usageData = {
                type: resourceType,
                action,
                timestamp: Date.now()
            };

            this.metrics.usage.set(`resource_${resourceType}_${action}`, usageData);
            await this.logMetrics('resource_usage', usageData);

            // Check resource thresholds
            await this.checkResourceThresholds(resourceType);
        } catch (error) {
            console.error('Failed to track resource usage:', error);
            await this.logMetrics('errors', {
                type: 'resource_tracking_error',
                error: error.message,
                timestamp: Date.now()
            });
        }
    }

    // Check resource thresholds
    async checkResourceThresholds(resourceType) {
        const thresholds = {
            library: {
                max_utilization: 0.9, // 90%
                low_inventory: 10
            },
            it: {
                max_utilization: 0.85, // 85%
                maintenance_interval: 30 // days
            },
            lab: {
                max_utilization: 0.8, // 80%
                safety_stock: 20
            },
            sports: {
                max_utilization: 0.75, // 75%
                maintenance_interval: 90 // days
            }
        };

        const currentUsage = await this.getResourceUsageByType(resourceType);
        const threshold = thresholds[resourceType];

        if (!threshold || !currentUsage) return;

        const issues = [];

        // Check utilization
        if (currentUsage.utilization > threshold.max_utilization) {
            issues.push({
                type: 'high_resource_utilization',
                resourceType,
                value: currentUsage.utilization,
                threshold: threshold.max_utilization
            });
        }

        // Check inventory levels
        if (currentUsage.inventory && currentUsage.inventory < threshold.low_inventory) {
            issues.push({
                type: 'low_inventory',
                resourceType,
                value: currentUsage.inventory,
                threshold: threshold.low_inventory
            });
        }

        // Check maintenance schedule
        if (threshold.maintenance_interval) {
            const lastMaintenance = await this.getLastMaintenance(resourceType);
            const daysSinceLastMaintenance = (Date.now() - lastMaintenance) / (1000 * 60 * 60 * 24);

            if (daysSinceLastMaintenance > threshold.maintenance_interval) {
                issues.push({
                    type: 'maintenance_required',
                    resourceType,
                    daysSinceLastMaintenance,
                    threshold: threshold.maintenance_interval
                });
            }
        }

        if (issues.length > 0) {
            await this.notifyAdmins(issues);
        }
    }

    // Get resource usage by type
    async getResourceUsageByType(resourceType) {
        try {
            const q = query(
                collection(db, 'resources'),
                where('type', '==', resourceType),
                orderBy('timestamp', 'desc'),
                limit(1)
            );

            const snapshot = await getDocs(q);
            if (!snapshot.empty) {
                return snapshot.docs[0].data();
            }
            return null;
        } catch (error) {
            console.error(`Failed to get ${resourceType} usage:`, error);
            return null;
        }
    }

    // Get last maintenance date
    async getLastMaintenance(resourceType) {
        try {
            const q = query(
                collection(db, 'maintenance_logs'),
                where('resourceType', '==', resourceType),
                where('status', '==', 'completed'),
                orderBy('timestamp', 'desc'),
                limit(1)
            );

            const snapshot = await getDocs(q);
            if (!snapshot.empty) {
                return snapshot.docs[0].data().timestamp;
            }
            return 0;
        } catch (error) {
            console.error(`Failed to get last maintenance for ${resourceType}:`, error);
            return 0;
        }
    }

    // Track report generation
    async trackReportGeneration(reportType) {
        try {
            const reportData = {
                type: reportType,
                timestamp: Date.now()
            };

            this.metrics.usage.set(`report_${reportType}`, reportData);
            await this.logMetrics('report_generation', reportData);
        } catch (error) {
            console.error('Failed to track report generation:', error);
            await this.logMetrics('errors', {
                type: 'report_tracking_error',
                error: error.message,
                timestamp: Date.now()
            });
        }
    }

    // Track user interaction
    async trackUserInteraction(component) {
        try {
            const interactionData = {
                component,
                timestamp: Date.now()
            };

            this.metrics.usage.set(`interaction_${component}`, interactionData);
            await this.logMetrics('user_interaction', interactionData);
        } catch (error) {
            console.error('Failed to track user interaction:', error);
            await this.logMetrics('errors', {
                type: 'interaction_tracking_error',
                error: error.message,
                timestamp: Date.now()
            });
        }
    }
}

export default new MonitoringService();
