# Push Notifications Setup Guide

This guide explains how to properly set up Firebase for push notifications in the School Management System app.

## Development Mode

In development mode, the app uses mock push tokens to avoid Firebase initialization issues. This allows you to test the notification UI and functionality without needing to set up Firebase properly.

## Production Setup

For production, you need to properly set up Firebase for push notifications:

### Android Setup

1. Make sure you have a Firebase project set up for your app.

2. Download the `google-services.json` file from the Firebase console and place it in the `android/app` directory.

3. In your `android/app/build.gradle` file, make sure you have the following:

```gradle
dependencies {
    // ... other dependencies
    implementation platform('com.google.firebase:firebase-bom:32.0.0')
    implementation 'com.google.firebase:firebase-analytics'
    implementation 'com.google.firebase:firebase-messaging'
}

apply plugin: 'com.google.gms.google-services'
```

4. In your `android/build.gradle` file, make sure you have:

```gradle
buildscript {
    // ... other configurations
    dependencies {
        // ... other dependencies
        classpath 'com.google.gms:google-services:4.3.15'
    }
}
```

5. Create a custom Application class that initializes Firebase:

```java
// android/app/src/main/java/com/yourapp/MainApplication.java
package com.yourapp;

import android.app.Application;
import com.google.firebase.FirebaseApp;

public class MainApplication extends Application {
    @Override
    public void onCreate() {
        super.onCreate();
        FirebaseApp.initializeApp(this);
    }
}
```

6. Update your `AndroidManifest.xml` to use this Application class:

```xml
<application
    android:name=".MainApplication"
    ...
>
```

### iOS Setup

1. Download the `GoogleService-Info.plist` file from the Firebase console and add it to your Xcode project.

2. Make sure you have the necessary capabilities enabled in Xcode:
   - Push Notifications
   - Background Modes > Remote Notifications

3. Update your `AppDelegate.m` to initialize Firebase:

```objective-c
#import <Firebase.h>

@implementation AppDelegate

- (BOOL)application:(UIApplication *)application didFinishLaunchingWithOptions:(NSDictionary *)launchOptions
{
    [FIRApp configure];
    // ... rest of your code
}
```

## Troubleshooting

If you encounter the error "Default FirebaseApp is not initialized", make sure:

1. You have properly placed the `google-services.json` file in the `android/app` directory.
2. You have initialized Firebase in your Application class.
3. You have applied the Google Services plugin in your Gradle files.

## Testing Push Notifications

To test push notifications:

1. Make sure your app is registered with Firebase and has a valid FCM token.
2. Use the Firebase console to send a test notification to your device.
3. Alternatively, use the Expo push notification tool to send a test notification.

## Additional Resources

- [Expo Push Notifications Documentation](https://docs.expo.dev/push-notifications/overview/)
- [Firebase Cloud Messaging Documentation](https://firebase.google.com/docs/cloud-messaging)
