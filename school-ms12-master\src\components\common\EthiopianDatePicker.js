import React, { useState, useEffect } from 'react';
import { View, StyleSheet, Modal, TouchableOpacity, ScrollView, TextInput, Platform } from 'react-native';
import { Button, Text, Surface, Portal, IconButton, Chip, Divider } from 'react-native-paper';
import DateTimePicker from '@react-native-community/datetimepicker';
import EthiopianCalendarPicker from './EthiopianCalendarPicker';
import EthiopianCalendar from '../../utils/EthiopianCalendar';
import { MaterialCommunityIcons } from '@expo/vector-icons';

/**
 * Enhanced Ethiopian Date Picker Component
 * A feature-rich date picker that uses the Ethiopian calendar with multiple display modes
 */
const EthiopianDatePicker = ({
  value = new Date(),
  onChange,
  label = 'Select Date',
  buttonMode = 'outlined', // outlined, contained - for the button style
  display = 'calendar', // 'calendar', 'spinner', 'default', 'compact'
  minDate,
  maxDate,
  language = 'en', // Default to English if no language provided
  displayFormat, // Custom display format
  placeholder,
  disabled = false,
  showIcon = true,
  iconPosition = 'right',
  allowClear = true,
  showToday = true,
  showYearMonthSelector = true,
  themeType = 'light', // 'light' or 'dark'
  customTheme = {
    primaryColor: '#2196F3',
    textColor: '#333',
    borderColor: '#ccc',
    backgroundColor: '#fff',
    disabledColor: '#e0e0e0',
    disabledTextColor: '#9e9e9e',
    weekendColor: '#f44336',
    todayColor: '#03A9F4'
  }
}) => {
  const [modalVisible, setModalVisible] = useState(false);
  const [tempDate, setTempDate] = useState(value || new Date());
  const [viewMode, setViewMode] = useState('calendar'); // 'calendar', 'year', 'month'
  const [yearInput, setYearInput] = useState('');
  const [selectedYear, setSelectedYear] = useState(null);
  const [selectedMonth, setSelectedMonth] = useState(null);
  const [showNativePicker, setShowNativePicker] = useState(false);
  const [pickerMode, setPickerMode] = useState('date');

  // Initialize year and month when value changes
  useEffect(() => {
    if (value) {
      const ethiopianDate = EthiopianCalendar.fromGregorian(value);
      setSelectedYear(ethiopianDate.year);
      setSelectedMonth(ethiopianDate.month);
    }
  }, [value]);

  // Format the date for display
  const getFormattedDate = () => {
    if (!value) return '';
    try {
      if (displayFormat === 'short') {
        return EthiopianCalendar.formatShortDate(value);
      } else if (displayFormat === 'weekday') {
        return EthiopianCalendar.formatDate(value, language, { weekday: true });
      } else {
        return EthiopianCalendar.formatDate(value, language);
      }
    } catch (error) {
      console.error('Error formatting date:', error);
      return '';
    }
  };

  // Clear the selected date
  const clearDate = () => {
    if (onChange) {
      onChange(null);
    }
  };

  // Handle native picker change
  const handleNativePickerChange = (event, selectedDate) => {
    setShowNativePicker(Platform.OS === 'ios'); // Keep open on iOS, close on Android

    if (selectedDate) {
      // Convert from Gregorian to Ethiopian and back to maintain consistency
      const ethiopianDate = EthiopianCalendar.fromGregorian(selectedDate);
      const adjustedDate = EthiopianCalendar.toGregorian(
        ethiopianDate.year,
        ethiopianDate.month,
        ethiopianDate.day
      );

      setTempDate(adjustedDate);
      setSelectedYear(ethiopianDate.year);
      setSelectedMonth(ethiopianDate.month);

      // On Android, we auto-confirm the selection
      if (Platform.OS === 'android') {
        if (onChange) {
          onChange(adjustedDate);
        }
      }
    }
  };

  // Show the appropriate picker based on platform and display mode
  const showPicker = () => {
    if (disabled) return;

    // Use native picker for default and spinner modes
    if (display === 'default' || display === 'spinner') {
      setShowNativePicker(true);
    } else {
      // Use custom modal picker for calendar, compact modes or fallback
      setModalVisible(true);
    }
  };

  // Set date to today
  const setToday = () => {
    const today = new Date();
    setTempDate(today);
    if (onChange) {
      onChange(today);
    }
    setModalVisible(false);
  };

  // Switch to year selection mode
  const showYearSelector = () => {
    setViewMode('year');
    const ethiopianDate = EthiopianCalendar.fromGregorian(tempDate);
    setYearInput(ethiopianDate.year.toString());
  };

  // Switch to month selection mode
  const showMonthSelector = () => {
    setViewMode('month');
  };

  // Handle year input change
  const handleYearInputChange = (text) => {
    setYearInput(text);
  };

  // Confirm year selection
  const confirmYearSelection = () => {
    const year = parseInt(yearInput, 10);
    if (!isNaN(year) && year > 0) {
      setSelectedYear(year);
      const newDate = EthiopianCalendar.toGregorian(year, selectedMonth, 1);
      setTempDate(newDate);
      setViewMode('calendar');
    }
  };

  // Handle month selection
  const handleMonthSelect = (month) => {
    setSelectedMonth(month);
    const newDate = EthiopianCalendar.toGregorian(selectedYear, month, 1);
    setTempDate(newDate);
    setViewMode('calendar');
  };

  // Handle date selection
  const handleDateSelect = (date) => {
    setTempDate(date);

    // If we want to auto-close on selection
    // if (onChange) {
    //   onChange(date);
    //   setModalVisible(false);
    // }
  };

  // Confirm date selection
  const confirmSelection = () => {
    if (onChange) {
      onChange(tempDate);
    }
    setModalVisible(false);
  };

  // Cancel date selection
  const cancelSelection = () => {
    setTempDate(value);
    setModalVisible(false);
  };

  // Render year selector
  const renderYearSelector = () => {
    return (
      <View style={styles.yearSelectorContainer}>
        <Text style={styles.selectorTitle}>Select Year</Text>
        <TextInput
          style={styles.yearInput}
          value={yearInput}
          onChangeText={handleYearInputChange}
          keyboardType="numeric"
          maxLength={4}
          autoFocus
        />
        <View style={styles.yearButtonsContainer}>
          <Button
            mode="outlined"
            onPress={() => setViewMode('calendar')}
            style={styles.yearButton}
          >
            Cancel
          </Button>
          <Button
            mode="contained"
            onPress={confirmYearSelection}
            style={styles.yearButton}
            color={customTheme.primaryColor}
          >
            Confirm
          </Button>
        </View>
      </View>
    );
  };

  // Render month selector
  const renderMonthSelector = () => {
    return (
      <View style={styles.monthSelectorContainer}>
        <Text style={styles.selectorTitle}>Select Month</Text>
        <ScrollView style={styles.monthScrollView}>
          <View style={styles.monthGrid}>
            {Array.from({ length: 13 }, (_, i) => (
              <Chip
                key={`month-${i}`}
                style={[
                  styles.monthChip,
                  selectedMonth === i && { backgroundColor: customTheme.primaryColor }
                ]}
                textStyle={[
                  selectedMonth === i && { color: '#fff' }
                ]}
                onPress={() => handleMonthSelect(i)}
              >
                {EthiopianCalendar.getMonthName(i, language)}
              </Chip>
            ))}
          </View>
        </ScrollView>
        <Button
          mode="outlined"
          onPress={() => setViewMode('calendar')}
          style={styles.cancelMonthButton}
        >
          Cancel
        </Button>
      </View>
    );
  };

  // Render the native picker
  const renderNativePicker = () => {
    if (!showNativePicker) return null;

    return (
      <DateTimePicker
        value={tempDate}
        mode="date"
        display={display === 'spinner' ? 'spinner' : 'default'}
        onChange={handleNativePickerChange}
        minimumDate={minDate}
        maximumDate={maxDate}
        themeVariant={themeType}
      />
    );
  };

  // Render compact date display
  const renderCompactDateDisplay = () => {
    const ethiopianDate = EthiopianCalendar.fromGregorian(tempDate);
    const monthName = EthiopianCalendar.getMonthName(ethiopianDate.month, language);

    return (
      <View style={styles.compactContainer}>
        <View style={styles.compactDateDisplay}>
          <Text style={[styles.compactDateText, { color: themeType === 'dark' ? '#fff' : '#333' }]}>
            {ethiopianDate.day}
          </Text>
          <Text style={[styles.compactMonthText, { color: themeType === 'dark' ? '#fff' : '#333' }]}>
            {monthName}
          </Text>
          <Text style={[styles.compactYearText, { color: themeType === 'dark' ? '#fff' : '#333' }]}>
            {ethiopianDate.year}
          </Text>
        </View>

        <Divider style={styles.compactDivider} />

        <View style={styles.compactControls}>
          <Button
            mode="text"
            onPress={setToday}
            color={customTheme.primaryColor}
          >
            Today
          </Button>

          <Button
            mode="contained"
            onPress={confirmSelection}
            color={customTheme.primaryColor}
          >
            OK
          </Button>
        </View>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.inputContainer}>
        {iconPosition === 'left' && showIcon && (
          <IconButton
            icon="calendar"
            size={20}
            color={disabled ? customTheme.disabledTextColor : customTheme.primaryColor}
            onPress={disabled ? null : showPicker}
            style={styles.leftIcon}
          />
        )}

        <Button
          mode={buttonMode}
          onPress={showPicker}
          icon={showIcon && iconPosition !== 'left' ? "calendar" : null}
          style={[
            styles.button,
            buttonMode === 'outlined' && { borderColor: customTheme.borderColor },
            disabled && { backgroundColor: customTheme.disabledColor, borderColor: customTheme.disabledColor }
          ]}
          labelStyle={{
            color: disabled ? customTheme.disabledTextColor : (buttonMode === 'contained' ? '#fff' : customTheme.textColor),
            textAlign: 'left'
          }}
          color={customTheme.primaryColor}
          disabled={disabled}
        >
          {getFormattedDate() || placeholder || label}
        </Button>

        {allowClear && value && !disabled && (
          <IconButton
            icon="close-circle"
            size={16}
            color={customTheme.textColor}
            onPress={clearDate}
            style={styles.clearButton}
          />
        )}
      </View>

      {/* Native Picker (iOS/Android) */}
      {renderNativePicker()}

      <Portal>
        <Modal
          visible={modalVisible}
          onDismiss={cancelSelection}
          transparent
          animationType="fade"
        >
          <TouchableOpacity
            style={styles.modalOverlay}
            activeOpacity={1}
            onPress={cancelSelection}
          >
            <Surface style={[styles.modalContent, { backgroundColor: themeType === 'dark' ? '#333' : '#fff' }]}>
              <View style={styles.modalHeader}>
                <Text style={[styles.modalTitle, { color: themeType === 'dark' ? '#fff' : '#333' }]}>{label}</Text>
                <IconButton
                  icon="close"
                  size={20}
                  onPress={cancelSelection}
                  color={themeType === 'dark' ? '#fff' : '#333'}
                />
              </View>

              {display === 'compact' ? (
                renderCompactDateDisplay()
              ) : viewMode === 'calendar' ? (
                <>
                  {showYearMonthSelector && (
                    <View style={styles.yearMonthSelector}>
                      <Button
                        mode="text"
                        onPress={showMonthSelector}
                        style={styles.yearMonthButton}
                        color={themeType === 'dark' ? '#fff' : customTheme.primaryColor}
                      >
                        {EthiopianCalendar.getMonthName(selectedMonth, language)}
                      </Button>
                      <Button
                        mode="text"
                        onPress={showYearSelector}
                        style={styles.yearMonthButton}
                        color={themeType === 'dark' ? '#fff' : customTheme.primaryColor}
                      >
                        {selectedYear}
                      </Button>
                    </View>
                  )}

                  <EthiopianCalendarPicker
                    selectedDate={tempDate}
                    onDateSelect={handleDateSelect}
                    minDate={minDate}
                    maxDate={maxDate}
                    language={language}
                    showWeekNumbers={true}
                    theme={{
                      primaryColor: customTheme.primaryColor,
                      selectedColor: customTheme.primaryColor,
                      todayColor: customTheme.todayColor,
                      textColor: themeType === 'dark' ? '#fff' : customTheme.textColor,
                      backgroundColor: themeType === 'dark' ? '#444' : customTheme.backgroundColor,
                      weekendColor: customTheme.weekendColor,
                      headerTextColor: themeType === 'dark' ? '#fff' : customTheme.textColor,
                      disabledColor: themeType === 'dark' ? '#666' : '#ccc'
                    }}
                  />

                  <View style={styles.buttonContainer}>
                    {showToday && (
                      <Button
                        mode="text"
                        onPress={setToday}
                        style={styles.todayButton}
                        color={themeType === 'dark' ? '#fff' : customTheme.primaryColor}
                      >
                        Today
                      </Button>
                    )}
                    <View style={styles.actionButtonsContainer}>
                      <Button
                        mode="outlined"
                        onPress={cancelSelection}
                        style={styles.actionButton}
                        color={themeType === 'dark' ? '#fff' : customTheme.primaryColor}
                      >
                        Cancel
                      </Button>
                      <Button
                        mode="contained"
                        onPress={confirmSelection}
                        style={styles.actionButton}
                        color={customTheme.primaryColor}
                      >
                        Confirm
                      </Button>
                    </View>
                  </View>
                </>
              ) : viewMode === 'year' ? (
                renderYearSelector()
              ) : (
                renderMonthSelector()
              )}
            </Surface>
          </TouchableOpacity>
        </Modal>
      </Portal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 8,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    position: 'relative',
  },
  button: {
    justifyContent: 'flex-start',
    flex: 1,
  },
  leftIcon: {
    margin: 0,
  },
  clearButton: {
    position: 'absolute',
    right: 5,
    margin: 0,
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    width: '90%',
    maxWidth: 400,
    padding: 20,
    borderRadius: 8,
    elevation: 5,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  yearMonthSelector: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 10,
  },
  yearMonthButton: {
    marginHorizontal: 5,
  },
  buttonContainer: {
    marginTop: 15,
  },
  actionButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  actionButton: {
    marginLeft: 10,
  },
  todayButton: {
    alignSelf: 'flex-start',
  },
  yearSelectorContainer: {
    padding: 10,
    alignItems: 'center',
  },
  selectorTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 15,
  },
  compactContainer: {
    padding: 10,
  },
  compactDateDisplay: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 20,
  },
  compactDateText: {
    fontSize: 48,
    fontWeight: 'bold',
  },
  compactMonthText: {
    fontSize: 24,
    marginVertical: 5,
  },
  compactYearText: {
    fontSize: 18,
  },
  compactDivider: {
    marginVertical: 10,
  },
  compactControls: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 10,
  },
  yearInput: {
    width: '50%',
    height: 40,
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 5,
    paddingHorizontal: 10,
    marginBottom: 20,
    textAlign: 'center',
    fontSize: 18,
  },
  yearButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '80%',
  },
  yearButton: {
    width: '45%',
  },
  monthSelectorContainer: {
    padding: 10,
    height: 300,
  },
  monthScrollView: {
    maxHeight: 250,
  },
  monthGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    paddingHorizontal: 10,
  },
  monthChip: {
    margin: 5,
    width: '45%',
  },
  cancelMonthButton: {
    marginTop: 10,
  },
});

export default EthiopianDatePicker;
