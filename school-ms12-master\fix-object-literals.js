const fs = require('fs');
const path = require('path');

// Function to recursively find all JS files in a directory
function findJsFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);

  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);

    if (stat.isDirectory() && file !== 'node_modules' && file !== '.git') {
      findJsFiles(filePath, fileList);
    } else if (stat.isFile() && (file.endsWith('.js') || file.endsWith('.jsx'))) {
      fileList.push(filePath);
    }
  });

  return fileList;
}

// Function to fix object literal syntax errors in a file
function fixObjectLiteralErrors(filePath) {
  console.log(`Checking ${filePath}...`);
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;

  // Fix incomplete object literals
  // Look for patterns like: { key: value, key2: value2,
  const incompleteObjectRegex = /{\s*([^{}]*,)\s*(?!\s*[},])/g;
  if (content.match(incompleteObjectRegex)) {
    console.log(`Found potential incomplete object literal in ${filePath}`);
    
    // Replace with fixed version by adding a closing brace
    content = content.replace(incompleteObjectRegex, (match, group) => {
      if (!match.trim().endsWith('}')) {
        console.log(`  Original: ${match}`);
        const replacement = `{ ${group} }`;
        console.log(`  Fixed: ${replacement}`);
        return replacement;
      }
      return match;
    });
    
    modified = true;
  }

  // Fix missing closing braces in object literals
  // This is a more aggressive fix and should be used with caution
  const lines = content.split('\n');
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    // Count opening and closing braces in the line
    const openBraces = (line.match(/{/g) || []).length;
    const closeBraces = (line.match(/}/g) || []).length;
    
    // If there are more opening braces than closing braces
    if (openBraces > closeBraces && line.includes('{') && !line.includes('//')) {
      // Check if any of the next 5 lines have a closing brace
      let hasClosingBrace = false;
      for (let j = 1; j <= 5 && i + j < lines.length; j++) {
        if (lines[i + j].includes('}')) {
          hasClosingBrace = true;
          break;
        }
      }
      
      // If no closing brace is found in the next 5 lines, add one
      if (!hasClosingBrace) {
        console.log(`Found unclosed object literal at line ${i + 1}`);
        console.log(`  Original: ${line}`);
        lines[i] = line + ' }';
        console.log(`  Fixed: ${lines[i]}`);
        modified = true;
      }
    }
  }
  
  if (modified) {
    content = lines.join('\n');
  }

  // Save the file if modified
  if (modified) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`Fixed object literal errors in ${filePath}`);
    return true;
  }

  return false;
}

// Main function
function main() {
  const srcDir = path.join(__dirname, 'src');
  const jsFiles = findJsFiles(srcDir);
  let fixedCount = 0;

  jsFiles.forEach(file => {
    if (fixObjectLiteralErrors(file)) {
      fixedCount++;
    }
  });

  console.log(`\nFixed object literal errors in ${fixedCount} files.`);
}

main();
