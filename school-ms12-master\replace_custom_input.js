const fs = require('fs');
const path = require('path');

// Path to the StudentAdmission.js file
const filePath = path.join(__dirname, 'src', 'screens', 'admin', 'StudentAdmission.js');

// Read the file
let content = fs.readFileSync(filePath, 'utf8');

// Replace all instances of CustomInput with SimpleTextInput
content = content.replace(/CustomInput/g, 'SimpleTextInput');

// Write the file back
fs.writeFileSync(filePath, content, 'utf8');

console.log('All instances of CustomInput have been replaced with SimpleTextInput');
