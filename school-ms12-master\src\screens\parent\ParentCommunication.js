import React, { useState, useEffect } from 'react';
import { View, ScrollView, StyleSheet } from 'react-native';
import { Card, Title, Text, FAB, Portal, Modal, List, Chip } from 'react-native-paper';
import { db } from '../../config/firebase';
import { collection, query, getDocs, addDoc, where, orderBy, serverTimestamp } from 'firebase/firestore';
import { useAuth } from '../../context/AuthContext';
import CustomButton from '../../components/common/CustomButton';
import CustomInput from '../../components/common/CustomInput';

const ParentCommunication = () => {
  const { user } = useAuth();
  const [messages, setMessages] = useState([]);
  const [announcements, setAnnouncements] = useState([]);
  const [teachers, setTeachers] = useState([]);
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [student, setStudent] = useState(null);
  
  const [messageForm, setMessageForm] = useState({
    subject: '',
    message: '',
    recipientType: '', // teacher or admin
    recipientId: '',
  });

  useEffect(() => {
    fetchStudentInfo();
    fetchMessages();
    fetchAnnouncements();
  }, []);

  useEffect(() => {
    if (student) {
      fetchTeachers();
    }
  }, [student]);

  const fetchStudentInfo = async () => {
    try {
      const parentsRef = collection(db, 'parents');
      const parentDoc = await getDocs(query(parentsRef, where('userId', '==', user.uid)));
      
      if (!parentDoc.empty) {
        const parentData = parentDoc.docs[0].data();
        const studentsRef = collection(db, 'students');
        const studentDoc = await getDocs(query(studentsRef, where('id', '==', parentData.studentId)));
        
        if (!studentDoc.empty) {
          setStudent(studentDoc.docs[0].data());
        }
      }
    } catch (error) {
      console.error('Error fetching student info:', error);
    }
  };

  const fetchMessages = async () => {
    try {
      const messagesRef = collection(db, 'messages');
      const q = query(
        messagesRef,
        where('participants', 'array-contains', user.uid),
        orderBy('createdAt', 'desc')
      );
      const querySnapshot = await getDocs(q);
      
      const messagesData = [];
      querySnapshot.forEach((doc) => {
        messagesData.push({ id: doc.id, ...doc.data() });
      });
      
      setMessages(messagesData);
    } catch (error) {
      console.error('Error fetching messages:', error);
    }
  };

  const fetchAnnouncements = async () => {
    try {
      const announcementsRef = collection(db, 'announcements');
      const q = query(
        announcementsRef,
        where('status', '==', 'published'),
        where('targetAudience', 'array-contains', 'parents'),
        orderBy('createdAt', 'desc')
      );
      const querySnapshot = await getDocs(q);
      
      const announcementsData = [];
      querySnapshot.forEach((doc) => {
        announcementsData.push({ id: doc.id, ...doc.data() });
      });
      
      setAnnouncements(announcementsData);
    } catch (error) {
      console.error('Error fetching announcements:', error);
    }
  };

  const fetchTeachers = async () => {
    try {
      const teachersRef = collection(db, 'teachers');
      const q = query(teachersRef, where('classId', '==', student.classId));
      const querySnapshot = await getDocs(teachersRef);
      
      const teachersData = [];
      querySnapshot.forEach((doc) => {
        teachersData.push({ id: doc.id, ...doc.data() });
      });
      
      setTeachers(teachersData);
    } catch (error) {
      console.error('Error fetching teachers:', error);
    }
  };

  const handleSendMessage = async () => {
    try {
      setLoading(true);
      
      const messageData = {
        ...messageForm,
        senderId: user.uid,
        senderName: user.displayName,
        senderRole: 'parent',
        studentId: student.id,
        studentName: student.name,
        participants: [user.uid, messageForm.recipientId],
        status: 'sent',
        createdAt: serverTimestamp(),
      };

      const messagesRef = collection(db, 'messages');
      await addDoc(messagesRef, messageData);
      
      setVisible(false);
      setMessageForm({
        subject: '',
        message: '',
        recipientType: '',
        recipientId: '',
      });
      fetchMessages();
    } catch (error) {
      console.error('Error sending message:', error);
    } finally {
      setLoading(false);
    }
  };

  const getMessageStatus = (message) => {
    return message.senderId === user.uid ? 'Sent' : 'Received';
  };

  const getAnnouncementType = (announcement) => {
    if (announcement.type === 'school') return 'School-wide';
    if (announcement.type === 'class') return 'Class';
    return 'General';
  };

  const formatDate = (timestamp) => {
    if (!timestamp) return 'Date not available';
    if (timestamp.toDate) {
      return timestamp.toDate().toLocaleDateString();
    }
    if (timestamp instanceof Date) {
      return timestamp.toLocaleDateString();
    }
    if (typeof timestamp === 'string') {
      return new Date(timestamp).toLocaleDateString();
    }
    return 'Invalid date';
  };

  return (
    <View style={styles.container}>
      <ScrollView>
        <Card style={styles.announcementsCard}>
          <Card.Content>
            <Title>School Announcements</Title>
            {announcements.map((announcement) => (
              <List.Item
                key={announcement.id}
                title={announcement.title}
                description={announcement.content}
                left={props => (
                  <List.Icon
                    {...props}
                    icon={
                      announcement.type === 'school' ? 'school' :
                      announcement.type === 'class' ? 'account-group' :
                      'bullhorn'
                    }
                  />
                )}
                right={() => (
                  <View style={styles.announcementMeta}>
                    <Chip size="small">
                      {getAnnouncementType(announcement)}
                    </Chip>
                    <Text style={styles.dateText}>
                      {formatDate(announcement.createdAt)}
                    </Text>
                  </View>
                )}
              />
            ))}
            {announcements.length === 0 && (
              <Text>No announcements available</Text>
            )}
          </Card.Content>
        </Card>

        <Card style={styles.messagesCard}>
          <Card.Content>
            <Title>Messages</Title>
            {messages.map((message) => (
              <List.Item
                key={message.id}
                title={message.subject}
                description={message.message}
                left={props => (
                  <List.Icon
                    {...props}
                    icon={message.senderId === user.uid ? 'send' : 'inbox'}
                  />
                )}
                right={() => (
                  <View style={styles.messageMeta}>
                    <Chip
                      size="small"
                      selectedColor={
                        message.senderId === user.uid ? '#2196F3' : '#4CAF50'
                      }
                    >
                      {getMessageStatus(message)}
                    </Chip>
                    <Text style={styles.dateText}>
                      {formatDate(message.createdAt)}
                    </Text>
                  </View>
                )}
              />
            ))}
            {messages.length === 0 && (
              <Text>No messages available</Text>
            )}
          </Card.Content>
        </Card>
      </ScrollView>

      <Portal>
        <Modal
          visible={visible}
          onDismiss={() => setVisible(false)}
          contentContainerStyle={styles.modalContent}
        >
          <ScrollView>
            <Title>New Message</Title>

            <List.Section title="Send To">
              <List.Item
                title="Teacher"
                left={props => <List.Icon {...props} icon="account-tie" />}
                onPress={() => setMessageForm({ ...messageForm, recipientType: 'teacher' })}
                right={() => (
                  messageForm.recipientType === 'teacher' ? 
                  <List.Icon icon="check" /> : null
                )}
              />
              <List.Item
                title="Admin"
                left={props => <List.Icon {...props} icon="shield-account" />}
                onPress={() => setMessageForm({ ...messageForm, recipientType: 'admin' })}
                right={() => (
                  messageForm.recipientType === 'admin' ? 
                  <List.Icon icon="check" /> : null
                )}
              />
            </List.Section>

            {messageForm.recipientType === 'teacher' && (
              <List.Section title="Select Teacher">
                {teachers.map((teacher) => (
                  <List.Item
                    key={teacher.id}
                    title={teacher.name}
                    description={teacher.subject}
                    onPress={() => setMessageForm({ ...messageForm, recipientId: teacher.id })}
                    right={() => (
                      messageForm.recipientId === teacher.id ? 
                      <List.Icon icon="check" /> : null
                    )}
                  />
                ))}
              </List.Section>
            )}

            <CustomInput
              label="Subject"
              value={messageForm.subject}
              onChangeText={(text) => setMessageForm({ ...messageForm, subject: text })}
            />

            <CustomInput
              label="Message"
              value={messageForm.message}
              onChangeText={(text) => setMessageForm({ ...messageForm, message: text })}
              multiline
              numberOfLines={4}
            />

            <View style={styles.modalButtons}>
              <CustomButton
                mode="contained"
                onPress={handleSendMessage}
                loading={loading}
                disabled={!messageForm.recipientType || !messageForm.subject || !messageForm.message}
              >
                Send Message
              </CustomButton>
              
              <CustomButton
                mode="outlined"
                onPress={() => setVisible(false)}
                style={styles.cancelButton}
              >
                Cancel
              </CustomButton>
            </View>
          </ScrollView>
        </Modal>
      </Portal>

      <FAB
        style={styles.fab}
        icon="plus"
        onPress={() => setVisible(true)}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  announcementsCard: {
    margin: 10,
  },
  messagesCard: {
    margin: 10,
  },
  modalContent: {
    backgroundColor: 'white',
    padding: 20,
    margin: 20,
    borderRadius: 10,
    maxHeight: '80%',
  },
  modalButtons: {
    marginTop: 20,
  },
  cancelButton: {
    marginTop: 10,
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
    backgroundColor: '#2196F3',
  },
  announcementMeta: {
    alignItems: 'flex-end',
  },
  messageMeta: {
    alignItems: 'flex-end',
  },
  dateText: {
    fontSize: 12,
    color: '#666',
    marginTop: 5,
  },
});

export default ParentCommunication;
