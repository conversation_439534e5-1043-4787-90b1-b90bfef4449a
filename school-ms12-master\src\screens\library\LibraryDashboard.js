import React, { useState, useEffect } from 'react';
import { View, ScrollView, StyleSheet } from 'react-native';
import { Card, Title, Text, List, Searchbar } from 'react-native-paper';
import { db } from '../../config/firebase';
import { collection, query, getDocs } from 'firebase/firestore';
import { useAuth } from '../../context/AuthContext';

const LibraryDashboard = ({ navigation }) => {
  const { user } = useAuth();
  const [statistics, setStatistics] = useState({
    totalBooks: 0,
    availableBooks: 0,
    borrowedBooks: 0,
    totalCategories: 0,
    activeMembers: 0,
  });
  const [recentActivities, setRecentActivities] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');

  useEffect(() => {
    fetchLibraryStatistics();
    fetchRecentActivities();
  }, []);

  const fetchLibraryStatistics = async () => {
    try {
      // Fetch books statistics
      const booksRef = collection(db, 'books');
      const booksSnapshot = await getDocs(booksRef);
      const totalBooks = booksSnapshot.size;
      
      // Fetch borrowed books
      const borrowedRef = collection(db, 'bookBorrowings');
      const borrowedSnapshot = await getDocs(borrowedRef);
      const borrowedBooks = borrowedSnapshot.size;

      // Fetch categories
      const categoriesRef = collection(db, 'bookCategories');
      const categoriesSnapshot = await getDocs(categoriesRef);
      const totalCategories = categoriesSnapshot.size;

      setStatistics({
        totalBooks,
        availableBooks: totalBooks - borrowedBooks,
        borrowedBooks,
        totalCategories,
        activeMembers: 0, // This would be calculated from active borrowers
      });
    } catch (error) {
      console.error('Error fetching library statistics:', error);
    }
  };

  const fetchRecentActivities = async () => {
    try {
      const activitiesRef = collection(db, 'libraryActivities');
      const q = query(activitiesRef);
      const querySnapshot = await getDocs(q);
      
      const activities = [];
      querySnapshot.forEach((doc) => {
        activities.push({ id: doc.id, ...doc.data() });
      });
      
      setRecentActivities(activities);
    } catch (error) {
      console.error('Error fetching recent activities:', error);
    }
  };

  const StatisticsCard = ({ title, value, icon }) => (
    <Card style={styles.statsCard}>
      <Card.Content>
        <List.Icon icon={icon} />
        <Text style={styles.statsTitle}>{title}</Text>
        <Text style={styles.statsValue}>{value}</Text>
      </Card.Content>
    </Card>
  );

  return (
    <ScrollView style={styles.container}>
      <Searchbar
        placeholder="Search books..."
        onChangeText={setSearchQuery}
        value={searchQuery}
        style={styles.searchBar}
      />

      <View style={styles.statsContainer}>
        <StatisticsCard
          title="Total Books"
          value={statistics.totalBooks}
          icon="book-multiple"
        />
        <StatisticsCard
          title="Available"
          value={statistics.availableBooks}
          icon="book-check"
        />
        <StatisticsCard
          title="Borrowed"
          value={statistics.borrowedBooks}
          icon="book-arrow-right"
        />
        <StatisticsCard
          title="Categories"
          value={statistics.totalCategories}
          icon="format-list-bulleted"
        />
      </View>

      <Card style={styles.quickActionsCard}>
        <Card.Content>
          <Title>Quick Actions</Title>
          <View style={styles.quickActions}>
            <List.Item
              title="Add Book"
              left={props => <List.Icon {...props} icon="book-plus" />}
              onPress={() => navigation.navigate('AddBook')}
            />
            <List.Item
              title="Manage Categories"
              left={props => <List.Icon {...props} icon="folder" />}
              onPress={() => navigation.navigate('BookCategories')}
            />
            <List.Item
              title="Issue Book"
              left={props => <List.Icon {...props} icon="book-arrow-right" />}
              onPress={() => navigation.navigate('IssueBook')}
            />
            <List.Item
              title="Return Book"
              left={props => <List.Icon {...props} icon="book-arrow-left" />}
              onPress={() => navigation.navigate('ReturnBook')}
            />
          </View>
        </Card.Content>
      </Card>

      <Card style={styles.recentActivitiesCard}>
        <Card.Content>
          <Title>Recent Activities</Title>
          {recentActivities.map((activity) => (
            <List.Item
              key={activity.id}
              title={activity.action}
              description={`${activity.user} - ${new Date(activity.timestamp).toLocaleDateString()}`}
              left={props => (
                <List.Icon
                  {...props}
                  icon={
                    activity.type === 'borrow' ? 'book-arrow-right' :
                    activity.type === 'return' ? 'book-arrow-left' :
                    'book'
                  }
                />
              )}
            />
          ))}
        </Card.Content>
      </Card>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  searchBar: {
    margin: 10,
    elevation: 2,
  },
  statsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    padding: 5,
    justifyContent: 'space-between',
  },
  statsCard: {
    width: '48%',
    marginBottom: 10,
    marginHorizontal: '1%',
  },
  statsTitle: {
    fontSize: 14,
    color: '#666',
  },
  statsValue: {
    fontSize: 24,
    fontWeight: 'bold',
    marginTop: 5,
  },
  quickActionsCard: {
    margin: 10,
  },
  quickActions: {
    marginTop: 10,
  },
  recentActivitiesCard: {
    margin: 10,
  },
});

export default LibraryDashboard;
