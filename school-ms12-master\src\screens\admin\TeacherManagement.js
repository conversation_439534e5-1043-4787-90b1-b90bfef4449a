import React, { useState, useEffect, useRef } from 'react';
import { View, ScrollView, StyleSheet, Text, Animated, I18nManager } from 'react-native';
import { Card, Title, FAB, Portal, Modal, DataTable, Searchbar, List, Chip, IconButton, Divider, Surface, useTheme } from 'react-native-paper';
import { db, auth } from '../../config/firebase';
import { collection, addDoc, query, getDocs, doc, updateDoc, where, setDoc, getDoc, serverTimestamp, writeBatch } from 'firebase/firestore';
import { deleteUser, sendEmailVerification } from 'firebase/auth';
import CustomButton from '../../components/common/CustomButton';
import CustomInput from '../../components/common/CustomInput';
import { useLanguage } from '../../context/LanguageContext';
import { useNotifications } from '../../context/NotificationContext';
import { Ionicons, MaterialCommunityIcons } from '@expo/vector-icons';
import LanguageSelector from '../../components/common/LanguageSelector';
import { LinearGradient } from 'expo-linear-gradient';
import AdminAppHeader from '../../components/common/AdminAppHeader';
import AdminSidebar from '../../components/common/AdminSidebar';
import SidebarBackdrop from '../../components/common/SidebarBackdrop';
import * as Animatable from 'react-native-animatable';
import { useNavigation } from '@react-navigation/native';
import VerificationStatus from '../../components/common/VerificationStatus';
import UserRegistrationService from '../../services/UserRegistrationService';
import EmailVerificationService from '../../services/EmailVerificationService';
import EmailNotificationService from '../../services/EmailNotificationService';

const TeacherManagement = () => {
  const navigation = useNavigation();
  const { translate, language, getTextStyle, isRTL } = useLanguage();
  const { showToast } = useNotifications();
  const theme = useTheme();
  const fadeAnim = useRef(new Animated.Value(1)).current;
  const slideAnim = useRef(new Animated.Value(0)).current;
  const [tooltipVisible, setTooltipVisible] = useState(false);
  const [tooltipText, setTooltipText] = useState('');
  const [tooltipPosition, setTooltipPosition] = useState({ x: 0, y: 0 });
  const [drawerOpen, setDrawerOpen] = useState(false);
  const drawerAnim = useRef(new Animated.Value(-300)).current;
  const backdropFadeAnim = useRef(new Animated.Value(0)).current;
  const [activeSidebarItem, setActiveSidebarItem] = useState('TeacherManagement');
  const defaultFormData = {
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    address: 'Asella',
    dateOfBirth: '',
    gender: '',
    qualification: '',
    specialization: '',
    experience: '',
    joiningDate: new Date().toISOString().split('T')[0],
    sections: [],
    status: 'active',
    password: '1234qwer',
    emailVerified: false,
  };

  const [teachers, setTeachers] = useState([]);
  const [classes, setClasses] = useState([]);
  const [visible, setVisible] = useState(false);
  const [selectedTeacher, setSelectedTeacher] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState(defaultFormData);
  const [showSectionsModal, setShowSectionsModal] = useState(false);
  const [selectedSections, setSelectedSections] = useState([]);
  const [expandedClass, setExpandedClass] = useState(null);
  const [emailVerified, setEmailVerified] = useState(false);
  const [verificationSent, setVerificationSent] = useState(false);
  const [verificationLoading, setVerificationLoading] = useState(false);
  const [createdUser, setCreatedUser] = useState(null);

  useEffect(() => {
    fetchTeachers();
    fetchClasses();
  }, []);

  useEffect(() => {
    navigation.setOptions({
      headerShown: false,
    });
  }, [navigation]);

  const toggleDrawer = () => {
    if (drawerOpen) {
      // Close drawer
      Animated.parallel([
        Animated.timing(drawerAnim, {
          toValue: -300,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(backdropFadeAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        })
      ]).start(() => setDrawerOpen(false));
    } else {
      // Open drawer
      setDrawerOpen(true);
      Animated.parallel([
        Animated.timing(drawerAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(backdropFadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        })
      ]).start();
    }
  };

  // Language transition effect
  useEffect(() => {
    // Fade out
    Animated.timing(fadeAnim, {
      toValue: 0.3,
      duration: 200,
      useNativeDriver: true,
    }).start(() => {
      // Slide out
      Animated.timing(slideAnim, {
        toValue: isRTL ? 50 : -50,
        duration: 0,
        useNativeDriver: true,
      }).start(() => {
        // Slide in
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }).start();
        // Fade in
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 200,
          useNativeDriver: true,
        }).start();
      });
    });
  }, [language]);

  const showTooltip = (text, event) => {
    const { pageX, pageY } = event.nativeEvent;
    setTooltipText(text);
    setTooltipPosition({ x: pageX, y: pageY - 60 });
    setTooltipVisible(true);

    // Auto-hide tooltip after 3 seconds
    setTimeout(() => {
      setTooltipVisible(false);
    }, 3000);
  };

  const fetchTeachers = async () => {
    try {
      const teachersRef = collection(db, 'users');
      const q = query(teachersRef, where('role', '==', 'teacher'));
      const querySnapshot = await getDocs(q);

      const teachersData = [];
      querySnapshot.forEach((doc) => {
        const data = doc.data();
        teachersData.push({
          id: doc.id,
          ...data,
          sections: data.sections || [],
          qualification: data.qualification || '',
          specialization: data.specialization || '',
          experience: data.experience || '',
          phone: data.phone || '',
          address: data.address || '',
          dateOfBirth: data.dateOfBirth || '',
          gender: data.gender || '',
          status: data.status || 'active'
        });
      });

      setTeachers(teachersData);
    } catch (error) {
      console.error('Error fetching teachers:', error);
    }
  };

  const fetchClasses = async () => {
    try {
      const classesRef = collection(db, 'classes');
      const querySnapshot = await getDocs(classesRef);

      const classesData = [];
      querySnapshot.forEach((doc) => {
        const classData = doc.data();
        classesData.push({
          id: doc.id,
          ...classData,
          sections: classData.sections || []
        });
      });

      setClasses(classesData);
    } catch (error) {
      console.error('Error fetching classes:', error);
    }
  };

  const handleSectionAssignment = async () => {
    if (!selectedTeacher) return;

    try {
      setLoading(true);
      // Update teacher's sections
      const teacherRef = doc(db, 'users', selectedTeacher.id);
      await updateDoc(teacherRef, {
        sections: selectedSections,
        updatedAt: new Date().toISOString()
      });

      // Update each class's sections with teacher info
      const updatePromises = selectedSections.map(async (section) => {
        const classRef = doc(db, 'classes', section.classId);
        const classDoc = await getDoc(classRef);

        if (classDoc.exists()) {
          const classData = classDoc.data();
          const updatedSections = classData.sections.map(s => {
            if (s.name === section.sectionName) {
              return {
                ...s,
                teacherId: selectedTeacher.id,
                teacherName: `${selectedTeacher.firstName} ${selectedTeacher.lastName}`
              };
            }
            return s;
          });

          await updateDoc(classRef, {
            sections: updatedSections,
            updatedAt: new Date().toISOString()
          });
        }
      });

      await Promise.all(updatePromises);

      setShowSectionsModal(false);
      fetchTeachers();
      fetchClasses();
    } catch (error) {
      console.error('Error assigning sections:', error);
      console.error('Failed to assign sections: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const toggleSection = (classId, section) => {
    const newSection = {
      classId,
      className: classes.find(c => c.id === classId)?.name || '',
      sectionName: section.name
    };

    setSelectedSections(prev => {
      const exists = prev.some(s =>
        s.classId === classId && s.sectionName === section.name
      );

      if (exists) {
        return prev.filter(s =>
          !(s.classId === classId && s.sectionName === section.name)
        );
      } else {
        return [...prev, newSection];
      }
    });
  };

  const handleTeacherPress = (teacher) => {
    setSelectedTeacher(teacher);
    setSelectedSections(teacher.sections || []);
    setShowSectionsModal(true);
  };

  const handleAddTeacher = async () => {
    try {
      setLoading(true);

      // Validate required fields
      if (!formData.firstName || !formData.lastName || !formData.email || !formData.password) {
        throw new Error(translate('validation.requiredFields') || 'Please fill in all required fields');
      }

      // Prepare user data for registration
      const userData = {
        firstName: formData.firstName,
        lastName: formData.lastName,
        displayName: `${formData.firstName} ${formData.lastName}`,
        phone: formData.phone || '',
        address: formData.address || {
          street: '',
          city: '',
          state: '',
          zipCode: '',
        },
        dateOfBirth: formData.dateOfBirth || '',
        gender: formData.gender || '',
        qualification: formData.qualification || '',
        specialization: formData.specialization || '',
        experience: formData.experience || '',
        joiningDate: formData.joiningDate || new Date().toISOString().split('T')[0],
        sections: formData.sections || [],
        status: formData.status || 'active',
        emailVerified: false,
        schedule: formData.schedule || {},
        departments: formData.departments || [],
        employmentType: formData.employmentType || 'full-time',
        salary: formData.salary || '',
        bankDetails: formData.bankDetails || {
          accountNumber: '',
          bankName: '',
          ifscCode: '',
        },
        documents: formData.documents || {
          resume: '',
          certificates: [],
          idProof: '',
        },
        emergencyContact: formData.emergencyContact || {
          name: '',
          relationship: '',
          phone: '',
        },
      };

      // Create auth user using the admin method to avoid signing out the current admin
      const result = await UserRegistrationService.adminCreateUser(
        formData.email,
        formData.password,
        'teacher',
        userData
      );

      if (!result.success) {
        throw new Error(result.error || 'Failed to register teacher');
      }

      const userCredential = { user: result.user };

      // Store the created user for verification
      setCreatedUser(userCredential.user);

      // Store the password in verification_credentials collection for later use
      try {
        await addDoc(collection(db, 'verification_credentials'), {
          userId: userCredential.user.uid,
          email: formData.email,
          password: formData.password,
          role: 'teacher',
          timestamp: serverTimestamp(),
          used: false
        });
      } catch (credentialsError) {
        console.error('Error storing credentials:', credentialsError);
        // Continue even if storing credentials fails
      }

      // Send verification email
      try {
        await sendEmailVerification(userCredential.user);

        // Log verification email sent
        await addDoc(collection(db, 'verification_logs'), {
          userId: userCredential.user.uid,
          email: userCredential.user.email,
          type: 'teacher_registration',
          timestamp: serverTimestamp(),
          status: 'sent'
        });

        // Try to send custom verification email as well
        try {
          // Generate verification token
          const { token } = await EmailVerificationService.generateVerificationToken(formData.email, 'teacher');

          // Send custom verification email
          await EmailNotificationService.sendVerificationEmail(formData.email, token, 'teacher');
        } catch (customEmailError) {
          console.error('Error sending custom verification email:', customEmailError);
          // Continue even if custom email fails, as Firebase will send its own
        }

        setVerificationSent(true);
      } catch (verificationError) {
        console.error('Error sending verification email:', verificationError);
        // Continue even if verification email fails
      }

      // The UserRegistrationService has already created the basic user and teacher documents
      // We'll update them with additional fields if needed

      // Add additional fields to the teacher document
      const additionalFields = {
        bankDetails: formData.bankDetails || {
          accountNumber: '',
          bankName: '',
          ifscCode: '',
        },
        documents: formData.documents || {
          resume: '',
          certificates: [],
          idProof: '',
        },
        emergencyContact: formData.emergencyContact || {
          name: '',
          relationship: '',
          phone: '',
        },
        updatedAt: serverTimestamp(),
      };

      // Update the teacher document with additional fields
      try {
        const teacherDocRef = doc(db, 'teachers', userCredential.user.uid);
        await updateDoc(teacherDocRef, additionalFields);
      } catch (error) {
        console.error('Error updating teacher document with additional fields:', error);
        // Continue even if this update fails
      }

      // For development mode, auto-verify the email
      setEmailVerified(true);

      // Update the user document to mark email as verified
      try {
        const userRef = doc(db, 'users', userCredential.user.uid);
        await updateDoc(userRef, {
          emailVerified: true,
          updatedAt: serverTimestamp()
        });
        console.log('User document updated with verified email status');
      } catch (updateError) {
        console.error('Error updating email verification status:', updateError);
      }

      // Send credentials email to the new teacher
      try {
        await EmailNotificationService.sendCredentialsEmail(
          formData.email,
          formData.password,
          userData,
          'teacher'
        );
        console.log(`Credentials email sent to teacher (${formData.email})`);
      } catch (emailError) {
        console.error(`Error sending credentials email to teacher:`, emailError);
        // Continue even if email sending fails
      }

      // Show success toast notification with verification instructions
      showToast(
        translate('teacher.management.success') || 'Success',
        translate('teacher.management.teacherCreated') || 'Teacher created successfully. Login credentials have been sent to the teacher.',
        'success',
        'UserManagement'
      );

      // Don't close the modal yet - keep it open to show verification UI
      // Instead, set the createdUser state to show verification UI
      setVerificationSent(true);

      // Refresh the teachers list in the background
      fetchTeachers();
    } catch (error) {
      console.error('Error adding teacher:', error);
      console.error(error.message || 'Failed to add teacher');

      // Show error toast notification
      showToast(
        translate('common.error') || 'Error',
        error.message || translate('teacher.management.errorCreating') || 'Failed to add teacher',
        'error'
      );
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateTeacher = async () => {
    try {
      if (!selectedTeacher?.id) {
        console.error('No teacher selected for update');
        showToast(
          translate('common.error') || 'Error',
          translate('teacher.management.noTeacherSelected') || 'No teacher selected for update',
          'error'
        );
        return;
      }

      setLoading(true);

      const updateData = {
        firstName: formData.firstName,
        lastName: formData.lastName,
        phone: formData.phone || '',
        address: {
          street: formData.address?.street || '',
          city: formData.address?.city || '',
          state: formData.address?.state || '',
          zipCode: formData.address?.zipCode || '',
        },
        dateOfBirth: formData.dateOfBirth || '',
        gender: formData.gender || '',
        qualification: formData.qualification || '',
        specialization: formData.specialization || '',
        experience: formData.experience || '',
        joiningDate: formData.joiningDate || new Date().toISOString().split('T')[0],
        sections: formData.sections || [],
        status: formData.status || 'active',
        schedule: formData.schedule || {},
        departments: formData.departments || [],
        employmentType: formData.employmentType || 'full-time',
        salary: formData.salary || '',
        bankDetails: {
          accountNumber: formData.bankDetails?.accountNumber || '',
          bankName: formData.bankDetails?.bankName || '',
          ifscCode: formData.bankDetails?.ifscCode || '',
        },
        documents: {
          resume: formData.documents?.resume || '',
          certificates: formData.documents?.certificates || [],
          idProof: formData.documents?.idProof || '',
        },
        emergencyContact: {
          name: formData.emergencyContact?.name || '',
          relationship: formData.emergencyContact?.relationship || '',
          phone: formData.emergencyContact?.phone || '',
        },
        updatedAt: serverTimestamp(),
      };

      // Create a batch to update both documents
      const batch = writeBatch(db);

      // Update user document in the users collection
      const userDocRef = doc(db, 'users', selectedTeacher.id);
      batch.update(userDocRef, updateData);

      // Check if teacher-specific document exists and update it
      try {
        const teacherDocRef = doc(db, 'teachers', selectedTeacher.id);
        const teacherDoc = await getDoc(teacherDocRef);

        if (teacherDoc.exists()) {
          // Update the teacher document
          batch.update(teacherDocRef, updateData);
        } else {
          // Create the teacher document if it doesn't exist
          batch.set(teacherDocRef, {
            ...updateData,
            userId: selectedTeacher.id,
            role: 'teacher',
            email: selectedTeacher.email,
            createdAt: serverTimestamp()
          });
        }
      } catch (error) {
        console.error('Error checking teacher document:', error);
        // Continue with the user document update even if teacher document check fails
      }

      // Commit the batch to update both documents
      await batch.commit();

      // Show success toast notification
      showToast(
        translate('teacher.management.success') || 'Success',
        translate('teacher.management.teacherUpdated') || 'Teacher updated successfully',
        'success',
        'UserManagement'
      );

      setVisible(false);
      resetForm();
      fetchTeachers();
    } catch (error) {
      console.error('Error updating teacher:', error);
      console.error(error.message || 'Failed to update teacher');

      // Show error toast notification
      showToast(
        translate('common.error') || 'Error',
        error.message || translate('teacher.management.errorUpdating') || 'Failed to update teacher',
        'error'
      );
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteTeacher = async (teacherId) => {
    try {
      // Get teacher data before deletion
      const teacherRef = doc(db, 'users', teacherId);
      const teacherDoc = await getDoc(teacherRef);

      if (!teacherDoc.exists()) {
        throw new Error('Teacher not found');
      }

      const teacherData = teacherDoc.data();

      // Create a batch to delete all related documents
      const batch = writeBatch(db);

      // Delete user document from users collection
      batch.delete(doc(db, 'users', teacherId));

      // Delete teacher document from teachers collection
      batch.delete(doc(db, 'teachers', teacherId));

      // Commit the batch to delete both documents
      await batch.commit();

      // Try to delete the user from Firebase Authentication
      // Note: This is a limitation in client-side Firebase Auth - we can only delete the current user
      let authDeleted = false;
      try {
        // Check if the user being deleted is the current user
        const currentUser = auth.currentUser;
        if (currentUser && currentUser.uid === teacherId) {
          await deleteUser(currentUser);
          console.log('Successfully deleted teacher from Firebase Auth');
          authDeleted = true;
        } else {
          // We can't delete other users from Auth in client-side code
          // This is a Firebase limitation - in a production app, you would use Firebase Admin SDK on a server

          // Log this action for future server-side cleanup
          await addDoc(collection(db, 'auth_deletion_queue'), {
            userId: teacherId,
            email: teacherData.email,
            role: 'teacher',
            requestedBy: auth.currentUser?.uid || 'unknown',
            timestamp: serverTimestamp(),
            processed: false
          });

          console.log('Teacher deleted from Firestore. Firebase Auth deletion requires server-side code.');
        }
      } catch (authError) {
        console.error('Error deleting teacher from Firebase Auth:', authError);
        // We continue even if auth deletion fails since we've already deleted from Firestore
      }

      // Show success toast notification
      const successMessage = translate('teacher.management.teacherDeleted') || 'Teacher deleted successfully';
      showToast(
        translate('teacher.management.success') || 'Success',
        authDeleted
          ? successMessage
          : successMessage + ' ' + (translate('teacher.management.authDeletionPending') || '(Auth account will be cleaned up later)'),
        'success',
        'UserManagement'
      );

      fetchTeachers();
    } catch (error) {
      console.error('Error deleting teacher:', error);

      // Show error toast notification
      showToast(
        translate('common.error') || 'Error',
        error.message || translate('teacher.management.errorDeleting') || 'Failed to delete teacher',
        'error'
      );
    }
  };

  const resetForm = () => {
    setFormData(defaultFormData);
    setSelectedTeacher(null);
    setEmailVerified(false);
    setVerificationSent(false);
    setCreatedUser(null);
  };

  const handleVerificationSent = () => {
    setVerificationSent(true);
  };

  const handleVerificationStatusChange = (status) => {
    setEmailVerified(status);

    // Update the user document if verified
    if (status && createdUser) {
      const userRef = doc(db, 'users', createdUser.uid);
      updateDoc(userRef, {
        emailVerified: true,
        updatedAt: serverTimestamp()
      }).catch(error => {
        console.error('Error updating email verification status:', error);
      });
    }
  };

  const filteredTeachers = teachers.filter(teacher =>
    `${teacher.firstName} ${teacher.lastName}`.toLowerCase().includes(searchQuery.toLowerCase()) ||
    teacher.email.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <View style={styles.container}>
      {/* Sidebar */}
      <AdminSidebar
        drawerAnim={drawerAnim}
        activeSidebarItem={activeSidebarItem}
        setActiveSidebarItem={setActiveSidebarItem}
        toggleDrawer={toggleDrawer}
      />

      {/* Backdrop */}
      <SidebarBackdrop
        visible={drawerOpen}
        onPress={toggleDrawer}
        fadeAnim={backdropFadeAnim}
      />

      {/* Admin App Header */}
      <AdminAppHeader
        title={translate('teacher.management.title')}
        onMenuPress={toggleDrawer}
      />

      <Animated.View
        style={[
          styles.contentContainer,
          { opacity: fadeAnim, transform: [{ translateX: slideAnim }] },
          isRTL && styles.rtlContainer
        ]}
      >
        <Animatable.View animation="fadeInDown" duration={800}>
          <Surface style={styles.headerCard}>
            <LinearGradient
              colors={['#1976d2' + '20', '#f5f5f5']}
              style={styles.headerGradient}
            >
              <View style={styles.headerContent}>
                <MaterialCommunityIcons name="human-male-board" size={24} color={'#1976d2'} />
                <Title style={[styles.headerTitle, isRTL && styles.rtlText]}>
                  {translate('teacher.management.title')}
                </Title>
              </View>
              <Text style={[styles.headerSubtitle, isRTL && styles.rtlText]}>
                {translate('teacher.management.subtitle')}
              </Text>

              <View style={styles.statsContainer}>
                <Animatable.View animation="fadeIn" delay={400} style={styles.statItem}>
                  <Surface style={styles.statItemSurface}>
                    <MaterialCommunityIcons name="account-group" size={20} color={'#1976d2'} />
                    <Text style={styles.statValue}>{teachers.length}</Text>
                    <Text style={styles.statLabel}>{translate('teacher.management.totalTeachers')}</Text>
                  </Surface>
                </Animatable.View>

                <Animatable.View animation="fadeIn" delay={500} style={styles.statItem}>
                  <Surface style={styles.statItemSurface}>
                    <MaterialCommunityIcons name="school" size={20} color="#4caf50" />
                    <Text style={[styles.statValue, { color: '#4caf50' }]}>{classes.length}</Text>
                    <Text style={styles.statLabel}>{translate('teacher.management.totalClasses')}</Text>
                  </Surface>
                </Animatable.View>
              </View>
            </LinearGradient>
          </Surface>
        </Animatable.View>

        <Animatable.View animation="fadeInUp" duration={800} delay={300}>
          <Searchbar
            placeholder={translate('teacher.management.searchTeachers')}
            onChangeText={setSearchQuery}
            value={searchQuery}
            style={[styles.searchBar, isRTL && styles.rtlSearchBar]}
            iconColor={'#1976d2'}
            inputStyle={getTextStyle({ textAlign: isRTL ? 'right' : 'left' })}
          />
        </Animatable.View>

        <ScrollView style={[styles.content, isRTL && styles.rtlContent]}>
        <Surface style={styles.tableContainer}>
          <DataTable>
            <DataTable.Header style={styles.tableHeader}>
              <DataTable.Title
                style={[styles.tableCell, isRTL && styles.rtlTableCell]}
                textStyle={getTextStyle(styles.tableHeaderText)}
              >
                {translate('teacher.management.firstName')}
              </DataTable.Title>
              <DataTable.Title
                style={[styles.tableCell, isRTL && styles.rtlTableCell]}
                textStyle={getTextStyle(styles.tableHeaderText)}
              >
                {translate('teacher.management.email')}
              </DataTable.Title>
              <DataTable.Title
                style={[styles.tableCell, isRTL && styles.rtlTableCell]}
                textStyle={getTextStyle(styles.tableHeaderText)}
              >
                {translate('teacher.management.sections')}
              </DataTable.Title>
              <DataTable.Title
                style={[styles.tableCell, isRTL && styles.rtlTableCell]}
                textStyle={getTextStyle(styles.tableHeaderText)}
              >
                {translate('actions.title')}
              </DataTable.Title>
            </DataTable.Header>

          {filteredTeachers
            .filter(teacher =>
              teacher.firstName.toLowerCase().includes(searchQuery.toLowerCase()) ||
              teacher.lastName.toLowerCase().includes(searchQuery.toLowerCase()) ||
              teacher.email.toLowerCase().includes(searchQuery.toLowerCase())
            )
            .map((teacher) => (
              <DataTable.Row
                key={teacher.id}
                style={[styles.tableRow, isRTL && styles.rtlTableRow]}
                onPress={() => {
                  setSelectedTeacher(teacher);
                  setFormData(teacher);
                  setVisible(true);
                }}
              >
                <DataTable.Cell
                  style={[styles.tableCell, isRTL && styles.rtlTableCell]}
                  textStyle={getTextStyle(styles.tableCellText)}
                >
                  <Text style={getTextStyle(styles.teacherName)}>
                    {`${teacher.firstName} ${teacher.lastName}`}
                  </Text>
                </DataTable.Cell>
                <DataTable.Cell
                  style={[styles.tableCell, isRTL && styles.rtlTableCell]}
                  textStyle={getTextStyle(styles.tableCellText)}
                >
                  {teacher.email}
                </DataTable.Cell>
                <DataTable.Cell
                  style={[styles.tableCell, isRTL && styles.rtlTableCell]}
                >
                  <Chip
                    onPress={(e) => {
                      e.stopPropagation();
                      handleTeacherPress(teacher);
                    }}
                    icon="school"
                    style={styles.sectionChip}
                    textStyle={getTextStyle(styles.chipText)}
                    onLongPress={(e) => showTooltip(translate('teacher.management.assignSections'), e)}
                  >
                    {teacher.sections?.length || 0} {translate('teacher.management.sectionsCount')}
                  </Chip>
                </DataTable.Cell>
                <DataTable.Cell
                  style={[styles.tableCell, styles.actionsCell, isRTL && styles.rtlTableCell]}
                >
                  <View style={[styles.actionButtons, isRTL && styles.rtlActionButtons]}>
                    <IconButton
                      icon="pencil"
                      size={20}
                      color={'#1976d2'}
                      style={styles.actionButton}
                      onPress={(e) => {
                        e.stopPropagation();
                        setSelectedTeacher(teacher);
                        setFormData(teacher);
                        setVisible(true);
                      }}
                      onLongPress={(e) => showTooltip(translate('actions.edit'), e)}
                    />
                    <IconButton
                      icon="delete"
                      size={20}
                      color={'#B00020'}
                      style={styles.actionButton}
                      onPress={(e) => {
                        e.stopPropagation();
                        handleDeleteTeacher(teacher.id);
                      }}
                      onLongPress={(e) => showTooltip(translate('actions.delete'), e)}
                    />
                  </View>
                </DataTable.Cell>
              </DataTable.Row>
            ))}
        </DataTable>
        </Surface>
        </ScrollView>

      <Portal>
        <Modal
          visible={showSectionsModal}
          onDismiss={() => {
            setShowSectionsModal(false);
            setSelectedTeacher(null);
            setSelectedSections([]);
            setExpandedClass(null);
          }}
          contentContainerStyle={[styles.modalContent, isRTL && styles.rtlModalContent]}
        >
          <LinearGradient
            colors={['#1976d2', '#005cb2']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
            style={styles.modalHeader}
          >
            <View style={styles.modalHeaderContent}>
              <MaterialCommunityIcons name="school" size={24} color="white" />
              <Title style={styles.modalHeaderTitle}>{translate('teacher.management.assignSections')}</Title>
              <IconButton
                icon="close"
                color="white"
                size={20}
                onPress={() => {
                  setShowSectionsModal(false);
                  setSelectedTeacher(null);
                  setSelectedSections([]);
                  setExpandedClass(null);
                }}
              />
            </View>
          </LinearGradient>

          {selectedTeacher && (
            <View style={[styles.selectedTeacherInfo, isRTL && styles.rtlSelectedTeacherInfo]}>
              <MaterialCommunityIcons name="account-tie" size={20} color={'#1976d2'} />
              <Text style={[styles.selectedTeacherName, getTextStyle()]}>
                {selectedTeacher.firstName} {selectedTeacher.lastName}
              </Text>
            </View>
          )}

          <ScrollView style={[styles.sectionsList, isRTL && styles.rtlSectionsList]}>
            {classes.map((classItem) => (
              <List.Accordion
                key={classItem.id}
                title={classItem.name}
                titleStyle={getTextStyle(styles.accordionTitle)}
                description={`${classItem.sections?.length || 0} ${translate('teacher.management.sections')}`}
                descriptionStyle={getTextStyle(styles.accordionDescription)}
                expanded={expandedClass === classItem.id}
                onPress={() => setExpandedClass(expandedClass === classItem.id ? null : classItem.id)}
                left={props => <List.Icon {...props} icon="school" color={'#1976d2'} />}
                style={[styles.classAccordion, isRTL && styles.rtlClassAccordion]}
              >
                {classItem.sections?.map((section, index) => (
                  <List.Item
                    key={`${classItem.id}-${section.name}-${index}`}
                    title={`${translate('teacher.management.section')} ${section.name}`}
                    titleStyle={getTextStyle(styles.sectionTitle)}
                    description={section.teacherId ?
                      `${translate('teacher.management.assignedTo')}: ${section.teacherName}` :
                      translate('teacher.management.notAssigned')
                    }
                    descriptionStyle={getTextStyle(styles.sectionDescription)}
                    left={props => <List.Icon {...props} icon="account-group" color={theme.colors.secondary} />}
                    right={props => (
                      <IconButton
                        {...props}
                        icon={selectedSections.some(s =>
                          s.classId === classItem.id && s.sectionName === section.name
                        ) ? "check-circle" : "checkbox-blank-circle-outline"}
                        color={selectedSections.some(s =>
                          s.classId === classItem.id && s.sectionName === section.name
                        ) ? '#1976d2' : '#757575'}
                        onPress={() => toggleSection(classItem.id, section)}
                      />
                    )}
                    onPress={() => toggleSection(classItem.id, section)}
                    style={[styles.sectionItem, isRTL && styles.rtlSectionItem]}
                  />
                ))}
              </List.Accordion>
            ))}
          </ScrollView>

          <View style={[styles.modalActions, isRTL && styles.rtlModalActions]}>
            <CustomButton
              mode="outlined"
              onPress={() => {
                setShowSectionsModal(false);
                setSelectedTeacher(null);
                setSelectedSections([]);
                setExpandedClass(null);
              }}
              labelStyle={getTextStyle(styles.buttonText)}
              style={styles.cancelButton}
            >
              {translate('actions.cancel')}
            </CustomButton>
            <CustomButton
              mode="contained"
              onPress={handleSectionAssignment}
              loading={loading}
              disabled={loading}
              labelStyle={getTextStyle(styles.buttonText)}
              style={styles.saveButton}
            >
              {translate('teacher.management.saveAssignments')}
            </CustomButton>
          </View>
        </Modal>
      </Portal>

      <Portal>
        <Modal
          visible={visible}
          onDismiss={() => {
            setVisible(false);
            resetForm();
          }}
          contentContainerStyle={[styles.modalContent, isRTL && styles.rtlModalContent]}
        >
          <LinearGradient
            colors={['#1976d2', '#005cb2']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
            style={styles.modalHeader}
          >
            <View style={styles.modalHeaderContent}>
              <MaterialCommunityIcons
                name={selectedTeacher ? "account-edit" : "account-plus"}
                size={24}
                color="white"
              />
              <Title style={styles.modalHeaderTitle}>
                {selectedTeacher ? translate('teacher.management.editTeacher') : translate('teacher.management.addTeacher')}
              </Title>
              <IconButton
                icon="close"
                color="white"
                size={20}
                onPress={() => {
                  setVisible(false);
                  resetForm();
                }}
              />
            </View>
          </LinearGradient>

          <ScrollView style={[styles.formScrollView, isRTL && styles.rtlFormScrollView]}>

            <CustomInput
              label={translate('teacher.management.firstName')}
              value={formData.firstName}
              onChangeText={(text) => setFormData({ ...formData, firstName: text })}
              style={[styles.formInput, isRTL && styles.rtlFormInput]}
              textInputStyle={getTextStyle({ textAlign: isRTL ? 'right' : 'left' })}
            />

            <CustomInput
              label={translate('teacher.management.lastName')}
              value={formData.lastName}
              onChangeText={(text) => setFormData({ ...formData, lastName: text })}
              style={[styles.formInput, isRTL && styles.rtlFormInput]}
              textInputStyle={getTextStyle({ textAlign: isRTL ? 'right' : 'left' })}
            />

            {!selectedTeacher && (
              <>
                <CustomInput
                  label={translate('teacher.management.email')}
                  value={formData.email}
                  onChangeText={(text) => setFormData({ ...formData, email: text })}
                  keyboardType="email-address"
                  style={[styles.formInput, isRTL && styles.rtlFormInput]}
                  textInputStyle={getTextStyle({ textAlign: isRTL ? 'right' : 'left' })}
                />

                <CustomInput
                  label={translate('teacher.management.password')}
                  value={formData.password}
                  onChangeText={(text) => setFormData({ ...formData, password: text })}
                  secureTextEntry
                  style={[styles.formInput, isRTL && styles.rtlFormInput]}
                  textInputStyle={getTextStyle({ textAlign: isRTL ? 'right' : 'left' })}
                />

                {createdUser && (
                  <View style={styles.verificationContainer}>
                    <Title style={[styles.sectionTitle, getTextStyle()]}>
                      {translate('verification.title') || 'Email Verification'}
                    </Title>
                    <VerificationStatus
                      email={formData.email}
                      isVerified={emailVerified}
                      verificationSent={verificationSent}
                      onVerificationSent={handleVerificationSent}
                      onVerificationStatusChange={handleVerificationStatusChange}
                      userType="teacher"
                      user={createdUser}
                    />

                    {verificationSent && !emailVerified && (
                      <Text style={[styles.verificationMessage, getTextStyle()]}>
                        {translate('verification.checkEmail') || 'Please check your email and verify your account before continuing.'}
                      </Text>
                    )}

                    {emailVerified && (
                      <Text style={[styles.verificationSuccess, getTextStyle()]}>
                        {translate('verification.success') || 'Email verified successfully!'}
                      </Text>
                    )}
                  </View>
                )}
              </>
            )}

            <CustomInput
              label={translate('teacher.management.phone')}
              value={formData.phone}
              onChangeText={(text) => setFormData({ ...formData, phone: text })}
              keyboardType="phone-pad"
              style={[styles.formInput, isRTL && styles.rtlFormInput]}
              textInputStyle={getTextStyle({ textAlign: isRTL ? 'right' : 'left' })}
            />

            <CustomInput
              label={translate('teacher.management.address')}
              value={formData.address}
              onChangeText={(text) => setFormData({ ...formData, address: text })}
              multiline
              numberOfLines={2}
              style={[styles.formInput, isRTL && styles.rtlFormInput]}
              textInputStyle={getTextStyle({ textAlign: isRTL ? 'right' : 'left' })}
            />

            <CustomInput
              label={translate('teacher.management.dateOfBirth')}
              value={formData.dateOfBirth}
              onChangeText={(text) => setFormData({ ...formData, dateOfBirth: text })}
              style={[styles.formInput, isRTL && styles.rtlFormInput]}
              textInputStyle={getTextStyle({ textAlign: isRTL ? 'right' : 'left' })}
            />

            <List.Section
              title={translate('teacher.management.gender')}
              titleStyle={getTextStyle(styles.sectionTitle)}
              style={[styles.formSection, isRTL && styles.rtlFormSection]}
            >
              {[translate('gender.male'), translate('gender.female'), translate('gender.other')].map((gender) => (
                <List.Item
                  key={gender}
                  title={gender}
                  titleStyle={getTextStyle()}
                  onPress={() => setFormData({ ...formData, gender })}
                  style={[styles.listItem, formData.gender === gender ? styles.selectedItem : null, isRTL && styles.rtlListItem]}
                  left={props => <List.Icon {...props} icon="account" color={'#1976d2'} />}
                />
              ))}
            </List.Section>

            <CustomInput
              label={translate('teacher.management.qualification')}
              value={formData.qualification}
              onChangeText={(text) => setFormData({ ...formData, qualification: text })}
              style={[styles.formInput, isRTL && styles.rtlFormInput]}
              textInputStyle={getTextStyle({ textAlign: isRTL ? 'right' : 'left' })}
            />

            <CustomInput
              label={translate('teacher.management.specialization')}
              value={formData.specialization}
              onChangeText={(text) => setFormData({ ...formData, specialization: text })}
              style={[styles.formInput, isRTL && styles.rtlFormInput]}
              textInputStyle={getTextStyle({ textAlign: isRTL ? 'right' : 'left' })}
            />

            <CustomInput
              label={translate('teacher.management.experience')}
              value={formData.experience}
              onChangeText={(text) => setFormData({ ...formData, experience: text })}
              keyboardType="numeric"
              style={[styles.formInput, isRTL && styles.rtlFormInput]}
              textInputStyle={getTextStyle({ textAlign: isRTL ? 'right' : 'left' })}
            />

            <CustomInput
              label={translate('teacher.management.joiningDate')}
              value={formData.joiningDate}
              onChangeText={(text) => setFormData({ ...formData, joiningDate: text })}
              style={[styles.formInput, isRTL && styles.rtlFormInput]}
              textInputStyle={getTextStyle({ textAlign: isRTL ? 'right' : 'left' })}
            />

            <List.Section
              title={translate('teacher.management.status')}
              titleStyle={getTextStyle(styles.sectionTitle)}
              style={[styles.formSection, isRTL && styles.rtlFormSection]}
            >
              {['active', 'inactive', 'on leave'].map((status) => (
                <List.Item
                  key={status}
                  title={status.charAt(0).toUpperCase() + status.slice(1)}
                  titleStyle={getTextStyle()}
                  onPress={() => setFormData({ ...formData, status })}
                  style={[styles.listItem, formData.status === status ? styles.selectedItem : null, isRTL && styles.rtlListItem]}
                  left={props => <List.Icon {...props} icon="account-check" color={'#1976d2'} />}
                />
              ))}
            </List.Section>

            <View style={[styles.modalButtons, isRTL && styles.rtlModalButtons]}>
              {!createdUser ? (
                // Show Add/Update button when no user has been created yet
                <CustomButton
                  mode="contained"
                  onPress={selectedTeacher ? handleUpdateTeacher : handleAddTeacher}
                  loading={loading}
                  labelStyle={getTextStyle(styles.buttonText)}
                  style={styles.saveButton}
                >
                  {selectedTeacher ? translate('actions.update') : translate('actions.add')}
                </CustomButton>
              ) : (
                // Show Finish button when user has been created and verification is complete
                <CustomButton
                  mode="contained"
                  onPress={() => {
                    // Show a final success message
                    showToast(
                      translate('teacher.management.success') || 'Success',
                      translate('teacher.management.teacherCreatedAndVerified') || 'Teacher account created and verified successfully.',
                      'success',
                      'UserManagement'
                    );

                    // Close the modal and reset form
                    setVisible(false);
                    resetForm();

                    // Refresh the teachers list
                    fetchTeachers();
                  }}
                  // In development mode, we auto-verify so no need to disable
                  // disabled={!emailVerified && !selectedTeacher}
                  labelStyle={getTextStyle(styles.buttonText)}
                  style={styles.saveButton}
                >
                  {translate('actions.finish') || 'Finish'}
                </CustomButton>
              )}

              {selectedTeacher && (
                <CustomButton
                  mode="outlined"
                  onPress={() => handleDeleteTeacher(selectedTeacher.id)}
                  style={styles.deleteButton}
                  labelStyle={getTextStyle(styles.deleteButtonText)}
                >
                  {translate('actions.delete')}
                </CustomButton>
              )}

              <CustomButton
                mode="outlined"
                onPress={() => {
                  setVisible(false);
                  resetForm();
                }}
                labelStyle={getTextStyle(styles.buttonText)}
                style={styles.cancelButton}
              >
                {translate('actions.cancel')}
              </CustomButton>
            </View>
          </ScrollView>
        </Modal>
      </Portal>

      {/* Tooltip */}
      {tooltipVisible && (
        <View
          style={[
            styles.tooltip,
            { top: tooltipPosition.y, left: tooltipPosition.x },
            isRTL && styles.rtlTooltip
          ]}
        >
          <Text style={styles.tooltipText}>{tooltipText}</Text>
          <View style={styles.tooltipArrow} />
        </View>
      )}

        <FAB
          style={[styles.fab, isRTL && styles.rtlFab]}
          icon="account-plus"
          label={translate('admin.teacherManagement.addTeacher') || 'Add New Teacher'}
          color="white"
          onPress={() => {
            setSelectedTeacher(null);
            setFormData(defaultFormData);
            setVisible(true);
          }}
          onLongPress={(e) => showTooltip(translate('teacher.management.addNew'), e)}
        />
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  contentContainer: {
    flex: 1,
  },
  rtlContainer: {
    flexDirection: 'row-reverse',
  },
  headerCard: {
    marginHorizontal: 16,
    marginTop: 16,
    marginBottom: 8,
    borderRadius: 12,
    overflow: 'hidden',
    elevation: 2,
  },
  headerGradient: {
    padding: 16,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginLeft: 8,
    color: '#333',
  },
  headerSubtitle: {
    marginTop: 4,
    marginLeft: 32,
    opacity: 0.7,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 16,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
    padding: 4,
  },
  statItemSurface: {
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    elevation: 1,
    backgroundColor: 'white',
    width: '90%',
  },
  statValue: {
    fontSize: 18,
    fontWeight: 'bold',
    marginVertical: 4,
    color: '#2196F3',
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
  },
  rtlText: {
    textAlign: 'right',
  },
  searchBar: {
    margin: 16,
    elevation: 2,
    borderRadius: 8,
  },
  rtlSearchBar: {
    flexDirection: 'row-reverse',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  rtlContent: {
    flexDirection: 'row-reverse',
  },
  tableContainer: {
    borderRadius: 8,
    overflow: 'hidden',
    elevation: 2,
    marginBottom: 16,
  },
  tableHeader: {
    backgroundColor: '#f0f0f0',
  },
  tableRow: {
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  rtlTableRow: {
    flexDirection: 'row-reverse',
  },
  tableCell: {
    justifyContent: 'center',
  },
  rtlTableCell: {
    justifyContent: 'flex-end',
  },
  tableHeaderText: {
    fontWeight: 'bold',
    color: '#333',
  },
  tableCellText: {
    color: '#555',
  },
  teacherName: {
    fontWeight: 'bold',
    color: '#333',
  },
  sectionChip: {
    backgroundColor: '#e3f2fd',
  },
  chipText: {
    fontSize: 12,
  },
  actionsCell: {
    justifyContent: 'center',
    paddingHorizontal: 0,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  rtlActionButtons: {
    flexDirection: 'row-reverse',
  },
  actionButton: {
    margin: 0,
  },
  tooltip: {
    position: 'absolute',
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    padding: 8,
    borderRadius: 4,
    zIndex: 1000,
    maxWidth: 200,
    transform: [{ translateX: -100 }],
  },
  rtlTooltip: {
    transform: [{ translateX: 0 }],
  },
  tooltipText: {
    color: 'white',
    fontSize: 12,
    textAlign: 'center',
  },
  tooltipArrow: {
    position: 'absolute',
    bottom: -8,
    left: '50%',
    marginLeft: -8,
    borderTopWidth: 8,
    borderLeftWidth: 8,
    borderRightWidth: 8,
    borderTopColor: 'rgba(0, 0, 0, 0.8)',
    borderLeftColor: 'transparent',
    borderRightColor: 'transparent',
  },
  modalContent: {
    backgroundColor: 'white',
    padding: 0,
    margin: 20,
    borderRadius: 10,
    maxHeight: '80%',
    overflow: 'hidden',
  },
  rtlModalContent: {
    flexDirection: 'row-reverse',
  },
  modalHeader: {
    paddingVertical: 16,
    paddingHorizontal: 20,
    width: '100%',
  },
  modalHeaderContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  modalHeaderTitle: {
    color: 'white',
    flex: 1,
    marginLeft: 12,
    fontSize: 18,
    fontWeight: 'bold',
  },
  formScrollView: {
    padding: 20,
  },
  rtlFormScrollView: {
    flexDirection: 'row-reverse',
  },
  formInput: {
    marginBottom: 12,
  },
  rtlFormInput: {
    flexDirection: 'row-reverse',
  },
  selectedTeacherInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    backgroundColor: '#f5f5f5',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  rtlSelectedTeacherInfo: {
    flexDirection: 'row-reverse',
  },
  selectedTeacherName: {
    marginLeft: 8,
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  selectedItem: {
    backgroundColor: '#e8f4f8',
  },
  sectionsList: {
    maxHeight: 400,
    marginVertical: 16,
    padding: 10,
  },
  rtlSectionsList: {
    flexDirection: 'row-reverse',
  },
  classAccordion: {
    marginBottom: 8,
    borderRadius: 8,
    overflow: 'hidden',
    elevation: 1,
  },
  rtlClassAccordion: {
    flexDirection: 'row-reverse',
  },
  accordionTitle: {
    fontWeight: 'bold',
    color: '#333',
  },
  accordionDescription: {
    color: '#666',
    fontSize: 12,
  },
  sectionItem: {
    paddingLeft: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  rtlSectionItem: {
    paddingLeft: 0,
    paddingRight: 16,
    flexDirection: 'row-reverse',
  },
  sectionTitle: {
    fontSize: 14,
  },
  sectionDescription: {
    fontSize: 12,
    color: '#666',
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
    backgroundColor: '#fafafa',
  },
  rtlModalActions: {
    flexDirection: 'row-reverse',
    justifyContent: 'flex-start',
  },
  teacherName: {
    fontSize: 16,
    marginBottom: 16,
    opacity: 0.7,
  },
  modalButtons: {
    marginTop: 20,
    flexDirection: 'row',
    justifyContent: 'space-between',
    flexWrap: 'wrap',
  },
  rtlModalButtons: {
    flexDirection: 'row-reverse',
  },
  saveButton: {
    marginHorizontal: 4,
    backgroundColor: '#2196F3',
  },
  cancelButton: {
    marginHorizontal: 4,
    borderColor: '#757575',
  },
  deleteButton: {
    marginVertical: 10,
    borderColor: '#f44336',
  },
  deleteButtonText: {
    color: '#f44336',
  },
  buttonText: {
    fontWeight: 'bold',
  },
  formSection: {
    marginBottom: 16,
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
    padding: 8,
  },
  rtlFormSection: {
    flexDirection: 'row-reverse',
  },
  listItem: {
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  rtlListItem: {
    flexDirection: 'row-reverse',
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 16,
    bottom: 16,
    backgroundColor: '#4CAF50',
    elevation: 12,
    zIndex: 999,
    borderRadius: 28,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.30,
    shadowRadius: 4.65,
  },
  rtlFab: {
    right: undefined,
    left: 0,
  },
  verificationContainer: {
    marginTop: 16,
    padding: 16,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: '#2196F3',
  },
  verificationMessage: {
    marginTop: 8,
    fontSize: 14,
    color: '#666',
    fontStyle: 'italic',
  },
  verificationSuccess: {
    marginTop: 8,
    fontSize: 14,
    color: '#4CAF50',
    fontWeight: 'bold',
  },
  disabledButton: {
    opacity: 0.6,
  },
  tooltip: {
    position: 'absolute',
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    padding: 8,
    borderRadius: 4,
    zIndex: 1000,
    maxWidth: 200,
  },
  tooltipText: {
    color: 'white',
    fontSize: 12,
    textAlign: 'center',
  },
  tooltipArrow: {
    position: 'absolute',
    bottom: -10,
    left: '50%',
    marginLeft: -5,
    borderWidth: 5,
    borderColor: 'transparent',
    borderTopColor: 'rgba(0, 0, 0, 0.8)',
  },
});

export default TeacherManagement;
