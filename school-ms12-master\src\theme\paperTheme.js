import { DefaultTheme } from 'react-native-paper';

// Create a simple paper theme without theme.js dependency
const paperTheme = {
  ...DefaultTheme,
  colors: {
    ...DefaultTheme.colors,
    primary: '#1976d2',
    accent: '#9c27b0',
    background: '#f5f5f5',
    surface: '#ffffff',
    text: '#333333',
    error: '#B00020',
    disabled: '#9e9e9e',
    placeholder: '#9E9E9E',
    backdrop: 'rgba(0, 0, 0, 0.5)',
    notification: '#f50057',
    success: '#4CAF50',
    warning: '#FF9800',
    info: '#2196F3',
  },
  fonts: {
    ...DefaultTheme.fonts,
    regular: {
      fontFamily: 'Roboto',
      fontWeight: 'normal',
    },
    medium: {
      fontFamily: 'Roboto',
      fontWeight: '500',
    },
    light: {
      fontFamily: 'Roboto',
      fontWeight: '300',
    },
    thin: {
      fontFamily: 'Roboto',
      fontWeight: '100',
    },
  },
  roundness: 8,
  animation: {
    scale: 1.0,
  },
};

export default paperTheme;
