import React from 'react';
import { View } from 'react-native';
import { useTranslation } from '../hooks/useTranslation';

/**
 * RTLWrapper component for React Native
 * This is a simplified version that works on mobile platforms
 */
const RTLWrapper = ({ children }) => {
    const { language, isRTL } = useTranslation();
    const direction = isRTL ? 'rtl' : 'ltr';

    return (
        <View style={{ width: '100%', direction }}>
            {children}
        </View>
    );
};

export default RTLWrapper;
