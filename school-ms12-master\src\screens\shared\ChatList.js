import React, { useState, useEffect } from 'react';
import { View, ScrollView, StyleSheet, TouchableOpacity } from 'react-native';
import {
  List,
  Avatar,
  Badge,
  Searchbar,
  FAB,
  Portal,
  Modal,
  Text,
  useTheme,
  Divider,
  ActivityIndicator,
} from 'react-native-paper';
import { useNavigation } from '@react-navigation/native';
import MessagingService from '../../utils/MessagingService';
import { useAuth } from '../../context/AuthContext';
import { db } from '../../config/firebase';
import { collection, query as firebaseQuery, where, getDocs } from 'firebase/firestore';
import EthiopianCalendar from '../../utils/EthiopianCalendar';

const ChatList = () => {
  const navigation = useNavigation();
  // No theme needed
  const { user } = useAuth();
  
  const [chats, setChats] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [newChatVisible, setNewChatVisible] = useState(false);
  const [users, setUsers] = useState([]);
  const [searchingUsers, setSearchingUsers] = useState(false);

  useEffect(() => {
    fetchChats();
  }, []);

  const fetchChats = async () => {
    try {
      setLoading(true);
      const userChats = await MessagingService.getUserChats();
      setChats(userChats);
    } catch (error) {
      console.error('Error fetching chats:', error);
    } finally {
      setLoading(false);
    }
  };

  const searchUsers = async (searchText) => {
    try {
      setSearchingUsers(true);
      const usersRef = collection(db, 'users');
      const q = firebaseQuery(
        usersRef,
        where('role', 'in', ['teacher', 'student', 'parent', 'admin'])
      );
      const querySnapshot = await getDocs(q);
      
      const usersData = [];
      querySnapshot.forEach((doc) => {
        const userData = doc.data();
        if (
          doc.id !== user.uid &&
          userData.displayName?.toLowerCase().includes(searchText.toLowerCase())
        ) {
          usersData.push({ id: doc.id, ...userData });
        }
      });
      
      setUsers(usersData);
    } catch (error) {
      console.error('Error searching users:', error);
    } finally {
      setSearchingUsers(false);
    }
  };

  const handleChatPress = (chat) => {
    const recipient = chat.participantDetails[0];
    navigation.navigate('Messaging', {
      chatId: chat.id,
      recipientId: recipient.id,
    });
  };

  const handleNewChat = async (selectedUser) => {
    try {
      setNewChatVisible(false);
      
      // Check if chat already exists
      const existingChat = chats.find(chat =>
        chat.participants.includes(selectedUser.id)
      );

      if (existingChat) {
        navigation.navigate('Messaging', {
          chatId: existingChat.id,
          recipientId: selectedUser.id,
        });
        return;
      }

      // Create new chat
      navigation.navigate('Messaging', {
        recipientId: selectedUser.id,
      });
    } catch (error) {
      console.error('Error starting new chat:', error);
    }
  };

  const getLastMessagePreview = (chat) => {
    if (!chat.lastMessage) return 'No messages yet';
    return chat.lastMessage.length > 30
      ? chat.lastMessage.substring(0, 27) + '...'
      : chat.lastMessage;
  };

  const getUnreadCount = (chat) => {
    return chat.unreadCount?.[user.uid] || 0;
  };

  const getLastMessageTime = (chat) => {
    if (!chat.lastMessageAt) return '';
    return EthiopianCalendar.formatRelativeTime(chat.lastMessageAt.toDate());
  };

  const renderChatItem = (chat) => {
    const recipient = chat.participantDetails[0];
    const unreadCount = getUnreadCount(chat);

    return (
      <TouchableOpacity
        key={chat.id}
        onPress={() => handleChatPress(chat)}
      >
        <List.Item
          title={recipient.displayName || 'Unknown User'}
          description={getLastMessagePreview(chat)}
          left={() => (
            <Avatar.Text
              size={50}
              label={recipient.displayName?.charAt(0) || '?'}
              style={{ backgroundColor: '#1976d2' }}
            />
          )}
          right={() => (
            <View style={styles.chatItemRight}>
              <Text style={styles.timeText}>
                {getLastMessageTime(chat)}
              </Text>
              {unreadCount > 0 && (
                <Badge style={styles.unreadBadge}>
                  {unreadCount}
                </Badge>
              )}
            </View>
          )}
        />
        <Divider />
      </TouchableOpacity>
    );
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={'#1976d2'} />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Searchbar
        placeholder="Search chats..."
        onChangeText={setSearchQuery}
        value={searchQuery}
        style={styles.searchBar}
      />

      <ScrollView>
        {chats
          .filter(chat =>
            chat.participantDetails[0].displayName
              ?.toLowerCase()
              .includes(searchQuery.toLowerCase())
          )
          .map(renderChatItem)}
      </ScrollView>

      <Portal>
        <Modal
          visible={newChatVisible}
          onDismiss={() => setNewChatVisible(false)}
          contentContainerStyle={styles.modal}
        >
          <Text style={styles.modalTitle}>New Chat</Text>
          <Searchbar
            placeholder="Search users..."
            onChangeText={searchUsers}
            style={styles.modalSearch}
          />

          <ScrollView style={styles.userList}>
            {searchingUsers ? (
              <ActivityIndicator style={styles.searchingLoader} />
            ) : (
              users.map(user => (
                <List.Item
                  key={user.id}
                  title={user.displayName || 'Unknown User'}
                  description={user.role}
                  left={() => (
                    <Avatar.Text
                      size={40}
                      label={user.displayName?.charAt(0) || '?'}
                    />
                  )}
                  onPress={() => handleNewChat(user)}
                />
              ))
            )}
          </ScrollView>
        </Modal>
      </Portal>

      <FAB
        style={[styles.fab, { backgroundColor: '#1976d2' }]}
        icon="plus"
        onPress={() => setNewChatVisible(true)}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  searchBar: {
    margin: 16,
    elevation: 4,
  },
  chatItemRight: {
    alignItems: 'flex-end',
    justifyContent: 'space-between',
    height: '100%',
    paddingVertical: 4,
  },
  timeText: {
    fontSize: 12,
    color: 'gray',
  },
  unreadBadge: {
    backgroundColor: '#2196F3',
    marginTop: 4,
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
  },
  modal: {
    backgroundColor: 'white',
    padding: 20,
    margin: 20,
    borderRadius: 8,
    maxHeight: '80%',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  modalSearch: {
    marginBottom: 16,
  },
  userList: {
    maxHeight: 400,
  },
  searchingLoader: {
    marginTop: 20,
  },
});

export default ChatList;
