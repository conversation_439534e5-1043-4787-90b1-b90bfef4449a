import { db, auth } from '../config/firebase';
import { collection, query, orderBy, limit, getDocs, addDoc, serverTimestamp, doc, updateDoc, where, Timestamp, getDoc, deleteDoc, writeBatch } from 'firebase/firestore';

class ActivityService {
  static async logActivity(activityData) {
    try {
      let finalActivityData;

      // If activityData is an object with all fields
      if (typeof activityData === 'object' && activityData !== null) {
        // Use the provided data directly
        finalActivityData = {
          userId: activityData.userId || (auth.currentUser ? auth.currentUser.uid : 'system'),
          type: activityData.type || 'general',
          title: activityData.title || '',
          description: activityData.description || '',
          timestamp: serverTimestamp(),
          read: false,
          referenceId: activityData.referenceId || null,
          metadata: activityData.details || activityData.metadata || {},
          createdAt: new Date().toISOString(),
          performedBy: activityData.performedBy || (auth.currentUser ? auth.currentUser.uid : 'system')
        };
      } else {
        // Legacy support for old method signature
        const [userId, type, title, description, referenceId = null, metadata = {}] = arguments;
        // If userId is not provided, use current user
        const currentUserId = userId || (auth.currentUser ? auth.currentUser.uid : 'system');

        finalActivityData = {
          userId: currentUserId,
          type,
          title,
          description,
          timestamp: serverTimestamp(),
          read: false,
          referenceId,
          metadata,
          createdAt: new Date().toISOString()
        };
      }

      // Add a safety check to ensure we have valid data
      if (!finalActivityData) {
        console.error('Invalid activity data:', activityData);
        finalActivityData = {
          userId: 'system',
          type: 'error',
          title: 'Invalid activity data',
          description: 'Failed to create proper activity data',
          timestamp: serverTimestamp(),
          read: false,
          createdAt: new Date().toISOString()
        };
      }

      try {
        const docRef = await addDoc(collection(db, 'activities'), finalActivityData);
        return docRef.id;
      } catch (addError) {
        console.error('Error adding activity to Firestore:', addError);
        // If we get a permission error, log to console but don't throw
        if (addError.code === 'permission-denied') {
          console.warn('Permission denied when logging activity. This is not critical and can be ignored.');
          return null;
        }
        throw addError;
      }
    } catch (error) {
      console.error('Error in logActivity:', error);
      // Don't throw the error to prevent app crashes
      return null;
    }
  }

  // Common activity logging methods

  // User authentication activities
  static async logLogin(userId, username) {
    return this.logActivity({
      userId,
      type: 'login',
      title: `User logged in`,
      description: `${username || userId} logged into the system`,
      performedBy: userId,
      metadata: {
        action: 'logged',
        entityType: 'user'
      }
    });
  }

  static async logLogout(userId, username) {
    return this.logActivity({
      userId,
      type: 'logout',
      title: `User logged out`,
      description: `${username || userId} logged out of the system`,
      performedBy: userId,
      metadata: {
        action: 'logged',
        entityType: 'user'
      }
    });
  }

  // User management activities
  static async logUserCreated(userId, createdUserId, userRole, userName) {
    return this.logActivity({
      userId,
      type: 'user',
      title: `New ${userRole} added`,
      description: `New ${userRole} ${userName} was added to the system`,
      referenceId: createdUserId,
      performedBy: userId,
      metadata: {
        action: 'created',
        entityType: userRole,
        entityName: userName
      }
    });
  }

  static async logUserUpdated(userId, updatedUserId, userRole, userName) {
    return this.logActivity({
      userId,
      type: 'user',
      title: `${userRole} information updated`,
      description: `${userRole} ${userName} information was updated`,
      referenceId: updatedUserId,
      performedBy: userId,
      metadata: {
        action: 'updated',
        entityType: userRole,
        entityName: userName
      }
    });
  }

  static async logUserDeleted(userId, deletedUserId, userRole, userName) {
    return this.logActivity({
      userId,
      type: 'user',
      title: `${userRole} removed`,
      description: `${userRole} ${userName} was removed from the system`,
      referenceId: deletedUserId,
      performedBy: userId,
      metadata: {
        action: 'deleted',
        entityType: userRole,
        entityName: userName
      }
    });
  }

  // Class management activities
  static async logClassCreated(userId, classId, className) {
    return this.logActivity({
      userId,
      type: 'class',
      title: `New class created`,
      description: `New class ${className} was created`,
      referenceId: classId,
      performedBy: userId,
      metadata: {
        action: 'created',
        entityType: 'class',
        entityName: className
      }
    });
  }

  static async logClassUpdated(userId, classId, className) {
    return this.logActivity({
      userId,
      type: 'class',
      title: `Class updated`,
      description: `Class ${className} information was updated`,
      referenceId: classId,
      performedBy: userId,
      metadata: {
        action: 'updated',
        entityType: 'class',
        entityName: className
      }
    });
  }

  static async logClassDeleted(userId, classId, className) {
    return this.logActivity({
      userId,
      type: 'class',
      title: `Class deleted`,
      description: `Class ${className} was deleted from the system`,
      referenceId: classId,
      performedBy: userId,
      metadata: {
        action: 'deleted',
        entityType: 'class',
        entityName: className
      }
    });
  }

  // Grades and attendance activities
  static async logGradesSubmitted(userId, classId, className, subject) {
    return this.logActivity({
      userId,
      type: 'grade',
      title: `Grades submitted`,
      description: `Grades for ${className} ${subject} were submitted`,
      referenceId: classId,
      performedBy: userId,
      metadata: {
        action: 'submitted',
        entityType: 'grade',
        className,
        subject
      }
    });
  }

  static async logGradesApproved(userId, classId, className, subject) {
    return this.logActivity({
      userId,
      type: 'grade',
      title: `Grades approved`,
      description: `Grades for ${className} ${subject} were approved`,
      referenceId: classId,
      performedBy: userId,
      metadata: {
        action: 'approved',
        entityType: 'grade',
        className,
        subject
      }
    });
  }

  static async logGradesRejected(userId, classId, className, subject, reason) {
    return this.logActivity({
      userId,
      type: 'grade',
      title: `Grades rejected`,
      description: `Grades for ${className} ${subject} were rejected`,
      referenceId: classId,
      performedBy: userId,
      metadata: {
        action: 'rejected',
        entityType: 'grade',
        className,
        subject,
        reason
      }
    });
  }

  static async logAttendanceSubmitted(userId, classId, className, date) {
    return this.logActivity({
      userId,
      type: 'attendance',
      title: `Attendance submitted`,
      description: `Attendance for ${className} on ${date} was submitted`,
      referenceId: classId,
      performedBy: userId,
      metadata: {
        action: 'submitted',
        entityType: 'attendance',
        className,
        date
      }
    });
  }

  static async getRecentActivities(limitCount = 5, filterOptions = {}) {
    try {
      const activitiesRef = collection(db, 'activities');
      let queryConstraints = [orderBy('timestamp', 'desc')];

      // Add filters if provided
      if (filterOptions.userId) {
        queryConstraints.push(where('userId', '==', filterOptions.userId));
      }

      if (filterOptions.type) {
        queryConstraints.push(where('type', '==', filterOptions.type));
      }

      if (filterOptions.read !== undefined) {
        queryConstraints.push(where('read', '==', filterOptions.read));
      }

      if (filterOptions.performedBy) {
        queryConstraints.push(where('performedBy', '==', filterOptions.performedBy));
      }

      if (filterOptions.startDate && filterOptions.endDate) {
        const startTimestamp = Timestamp.fromDate(new Date(filterOptions.startDate));
        const endTimestamp = Timestamp.fromDate(new Date(filterOptions.endDate));
        queryConstraints.push(where('timestamp', '>=', startTimestamp));
        queryConstraints.push(where('timestamp', '<=', endTimestamp));
      }

      // Add limit
      queryConstraints.push(limit(limitCount));

      const q = query(activitiesRef, ...queryConstraints);

      const snapshot = await getDocs(q);
      return snapshot.docs.map(doc => {
        const data = doc.data();
        const timestamp = data.timestamp?.toDate?.() ?
          data.timestamp.toDate() :
          data.timestamp ? new Date(data.timestamp) : new Date();

        // Calculate relative time (e.g., "2 hours ago")
        const relativeTime = this.getRelativeTime(timestamp);

        return {
          id: doc.id,
          ...data,
          timestamp: timestamp,
          relativeTime: relativeTime,
          formattedTimestamp: timestamp.toLocaleString()
        };
      });
    } catch (error) {
      console.error('Error fetching activities:', error);
      throw error;
    }
  }

  // Helper method to calculate relative time
  static getRelativeTime(date) {
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);

    if (diffInSeconds < 60) {
      return 'just now';
    }

    const diffInMinutes = Math.floor(diffInSeconds / 60);
    if (diffInMinutes < 60) {
      return `${diffInMinutes} minute${diffInMinutes > 1 ? 's' : ''} ago`;
    }

    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) {
      return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;
    }

    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 30) {
      return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`;
    }

    const diffInMonths = Math.floor(diffInDays / 30);
    if (diffInMonths < 12) {
      return `${diffInMonths} month${diffInMonths > 1 ? 's' : ''} ago`;
    }

    const diffInYears = Math.floor(diffInMonths / 12);
    return `${diffInYears} year${diffInYears > 1 ? 's' : ''} ago`;
  }

  static async markActivityAsRead(activityId) {
    try {
      const activityRef = doc(db, 'activities', activityId);
      await updateDoc(activityRef, {
        read: true,
        readAt: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error marking activity as read:', error);
      throw error;
    }
  }

  static async getActivityById(activityId) {
    try {
      const activityRef = doc(db, 'activities', activityId);
      const activityDoc = await getDoc(activityRef);

      if (!activityDoc.exists()) {
        throw new Error('Activity not found');
      }

      const data = activityDoc.data();
      return {
        id: activityDoc.id,
        ...data,
        timestamp: data.timestamp?.toDate?.() ?
          data.timestamp.toDate() :
          data.timestamp ? new Date(data.timestamp) : new Date()
      };
    } catch (error) {
      console.error('Error fetching activity:', error);
      throw error;
    }
  }

  static async getAllActivities(page = 1, pageSize = 20, filterOptions = {}) {
    try {
      const activitiesRef = collection(db, 'activities');
      let queryConstraints = [orderBy('timestamp', 'desc')];

      // Add filters if provided
      if (filterOptions.userId) {
        queryConstraints.push(where('userId', '==', filterOptions.userId));
      }

      if (filterOptions.type) {
        queryConstraints.push(where('type', '==', filterOptions.type));
      }

      if (filterOptions.read !== undefined) {
        queryConstraints.push(where('read', '==', filterOptions.read));
      }

      if (filterOptions.startDate && filterOptions.endDate) {
        const startTimestamp = Timestamp.fromDate(new Date(filterOptions.startDate));
        const endTimestamp = Timestamp.fromDate(new Date(filterOptions.endDate));
        queryConstraints.push(where('timestamp', '>=', startTimestamp));
        queryConstraints.push(where('timestamp', '<=', endTimestamp));
      }

      // Add pagination
      queryConstraints.push(limit(pageSize));

      const q = query(activitiesRef, ...queryConstraints);

      const snapshot = await getDocs(q);
      return {
        activities: snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          timestamp: doc.data().timestamp?.toDate?.() ?
            doc.data().timestamp.toDate().toLocaleString() :
            doc.data().timestamp ? new Date(doc.data().timestamp).toLocaleString() : 'N/A'
        })),
        totalCount: snapshot.size,
        page,
        pageSize
      };
    } catch (error) {
      console.error('Error fetching all activities:', error);
      throw error;
    }
  }

  static async deleteActivity(activityId) {
    try {
      const activityRef = doc(db, 'activities', activityId);
      await deleteDoc(activityRef);
    } catch (error) {
      console.error('Error deleting activity:', error);
      throw error;
    }
  }

  static async clearAllActivities() {
    try {
      const activitiesRef = collection(db, 'activities');
      const snapshot = await getDocs(activitiesRef);

      const batch = writeBatch(db);
      snapshot.docs.forEach(doc => {
        batch.delete(doc.ref);
      });

      await batch.commit();
    } catch (error) {
      console.error('Error clearing all activities:', error);
      throw error;
    }
  }
}

export default ActivityService;