import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  ActivityIndicator,
  Dimensions,
  Platform
} from 'react-native';
import {
  Text,
  Card,
  Avatar,
  Chip,
  Searchbar,
  IconButton,
  Divider,
  Button,
  Dialog,
  Portal,
  Modal,
  Title,
  Paragraph,
  List,
  Checkbox,
  Badge,
  ProgressBar,
  Snackbar,
  Surface
} from 'react-native-paper';
import { db } from '../../config/firebase';
import {
  collection,
  query,
  getDocs,
  doc,
  updateDoc,
  where,
  orderBy,
  limit,
  startAfter
} from 'firebase/firestore';
import { useLanguage } from '../../context/LanguageContext';
import { useNotifications } from '../../context/NotificationContext';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import * as Animatable from 'react-native-animatable';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import MobileScreenWrapper from '../../components/common/MobileScreenWrapper';
import NetInfo from '@react-native-community/netinfo';
import mobileTheme from '../../theme/mobileTheme';
import TeacherRegistrationForm from '../../components/admin/TeacherRegistrationForm';

const { width } = Dimensions.get('window');

const MobileTeacherManagement = () => {
  const navigation = useNavigation();
  const { translate, isRTL } = useLanguage();
  const { showToast } = useNotifications();
  
  // Data state
  const [teachers, setTeachers] = useState([]);
  const [classes, setClasses] = useState([]);
  const [filteredTeachers, setFilteredTeachers] = useState([]);
  
  // UI state
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [showAddTeacherModal, setShowAddTeacherModal] = useState(false);
  const [showSectionsModal, setShowSectionsModal] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [selectedTeacher, setSelectedTeacher] = useState(null);
  const [selectedSections, setSelectedSections] = useState([]);
  const [expandedClass, setExpandedClass] = useState(null);
  
  // Pagination state
  const [lastVisible, setLastVisible] = useState(null);
  const [loadingMore, setLoadingMore] = useState(false);
  const [hasMoreTeachers, setHasMoreTeachers] = useState(true);
  const [pageSize] = useState(20);
  
  // Network and error state
  const [isConnected, setIsConnected] = useState(true);
  const [errorMessage, setErrorMessage] = useState('');
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [lastUpdated, setLastUpdated] = useState(null);
  const [loadingProgress, setLoadingProgress] = useState(0);
  
  // Bottom navigation items
  const bottomNavItems = [
    { key: 'dashboard', icon: 'view-dashboard', label: 'common.dashboard', route: 'AdminDashboard' },
    { key: 'teachers', icon: 'teach', label: 'teacher.management.title', route: 'TeacherManagement' },
    { key: 'students', icon: 'account-school', label: 'student.title', route: 'StudentManagement' },
    { key: 'classes', icon: 'google-classroom', label: 'class.title', route: 'ClassManagement' },
    { key: 'more', icon: 'dots-horizontal', label: 'common.more', route: 'AdminMore' }
  ];
  
  // FAB actions
  const fabActions = [
    {
      icon: 'account-plus',
      label: translate('teacher.management.addTeacher'),
      onPress: () => setShowAddTeacherModal(true),
      style: { backgroundColor: mobileTheme.colors.admin }
    },
    {
      icon: 'refresh',
      label: translate('common.refresh'),
      onPress: onRefresh,
      style: { backgroundColor: mobileTheme.colors.info }
    }
  ];
  
  // Function to fetch all data
  const fetchAllData = useCallback(async () => {
    try {
      setErrorMessage('');
      
      // Check network connectivity
      const netInfo = await NetInfo.fetch();
      setIsConnected(netInfo.isConnected);
      
      if (!netInfo.isConnected) {
        setErrorMessage(translate('common.noInternetConnection'));
        setSnackbarVisible(true);
        return;
      }
      
      // Reset pagination
      setLastVisible(null);
      setHasMoreTeachers(true);
      
      // Fetch all data in parallel for better performance
      await Promise.all([
        fetchTeachers(true),
        fetchClasses()
      ]);
      
      // Update last updated timestamp
      setLastUpdated(new Date());
    } catch (error) {
      console.error('Error fetching data:', error);
      setErrorMessage(translate('common.errorFetchingData'));
      setSnackbarVisible(true);
    } finally {
      setRefreshing(false);
    }
  }, [translate]);
  
  // Pull-to-refresh handler
  const onRefresh = useCallback(() => {
    setRefreshing(true);
    fetchAllData();
  }, [fetchAllData]);
  
  // Initial data loading
  useEffect(() => {
    fetchAllData();
    
    // Set up network connectivity listener
    const unsubscribe = NetInfo.addEventListener(state => {
      setIsConnected(state.isConnected);
      
      // If connection is restored, refresh data
      if (state.isConnected && !isConnected) {
        fetchAllData();
      }
    });
    
    // Clean up listener on unmount
    return () => unsubscribe();
  }, [fetchAllData, isConnected]);
  
  // Refresh data when screen comes into focus
  useFocusEffect(
    useCallback(() => {
      fetchAllData();
    }, [fetchAllData])
  );
  
  // Fetch teachers with pagination
  const fetchTeachers = useCallback(async (reset = false) => {
    try {
      if (reset) {
        setLoading(true);
        setLoadingProgress(0.2);
      } else if (!hasMoreTeachers) {
        return;
      } else {
        setLoadingMore(true);
      }
      
      // Check network connectivity
      if (!isConnected) {
        setErrorMessage(translate('common.noInternetConnection'));
        setSnackbarVisible(true);
        return;
      }
      
      const teachersRef = collection(db, 'users');
      let q;
      
      if (reset || !lastVisible) {
        // First page query
        q = query(
          teachersRef, 
          where('role', '==', 'teacher'),
          orderBy('lastName'),
          orderBy('firstName'),
          limit(pageSize)
        );
        setLoadingProgress(0.4);
      } else {
        // Pagination query
        q = query(
          teachersRef, 
          where('role', '==', 'teacher'),
          orderBy('lastName'),
          orderBy('firstName'),
          startAfter(lastVisible),
          limit(pageSize)
        );
      }
      
      const querySnapshot = await getDocs(q);
      setLoadingProgress(0.7);
      
      // Check if we've reached the end
      if (querySnapshot.empty || querySnapshot.docs.length < pageSize) {
        setHasMoreTeachers(false);
      } else {
        // Set the last document for pagination
        setLastVisible(querySnapshot.docs[querySnapshot.docs.length - 1]);
      }
      
      const teachersData = [];
      querySnapshot.forEach((doc) => {
        const data = doc.data();
        teachersData.push({
          id: doc.id,
          ...data,
          sections: data.sections || [],
          qualification: data.qualification || '',
          specialization: data.specialization || '',
          experience: data.experience || '',
          phone: data.phone || '',
          address: data.address || '',
          dateOfBirth: data.dateOfBirth || '',
          gender: data.gender || '',
          status: data.status || 'active'
        });
      });
      
      setLoadingProgress(0.9);
      
      // Update the teachers list
      if (reset) {
        setTeachers(teachersData);
        setFilteredTeachers(teachersData);
      } else {
        setTeachers(prevTeachers => {
          const newTeachers = [...prevTeachers, ...teachersData];
          setFilteredTeachers(newTeachers);
          return newTeachers;
        });
      }
      
      setLoadingProgress(1);
      return true;
    } catch (error) {
      console.error('Error fetching teachers:', error);
      setErrorMessage(translate('teacher.management.errorFetchingTeachers') || 'Failed to fetch teachers');
      setSnackbarVisible(true);
      return false;
    } finally {
      setLoading(false);
      setLoadingMore(false);
      setLoadingProgress(0);
    }
  }, [isConnected, lastVisible, hasMoreTeachers, pageSize, translate]);
  
  // Fetch classes
  const fetchClasses = useCallback(async () => {
    try {
      setLoadingProgress(0.3);
      
      // Check network connectivity
      if (!isConnected) {
        return false;
      }
      
      const classesRef = collection(db, 'classes');
      const querySnapshot = await getDocs(classesRef);

      const classesData = [];
      querySnapshot.forEach((doc) => {
        const classData = doc.data();
        classesData.push({
          id: doc.id,
          ...classData,
          sections: classData.sections || []
        });
      });

      setClasses(classesData);
      setLoadingProgress(0.6);
      return true;
    } catch (error) {
      console.error('Error fetching classes:', error);
      return false;
    }
  }, [isConnected]);
  
  // Filter teachers based on search query
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredTeachers(teachers);
      return;
    }
    
    const query = searchQuery.toLowerCase().trim();
    const filtered = teachers.filter(teacher => {
      const fullName = `${teacher.firstName || ''} ${teacher.lastName || ''}`.toLowerCase();
      const email = (teacher.email || '').toLowerCase();
      const phone = (teacher.phone || '').toLowerCase();
      const specialization = (teacher.specialization || '').toLowerCase();
      
      return (
        fullName.includes(query) ||
        email.includes(query) ||
        phone.includes(query) ||
        specialization.includes(query)
      );
    });
    
    setFilteredTeachers(filtered);
  }, [searchQuery, teachers]);
  
  // Handle section assignment
  const handleSectionAssignment = async () => {
    if (!selectedTeacher) return;

    try {
      setLoading(true);
      // Update teacher's sections
      const teacherRef = doc(db, 'users', selectedTeacher.id);
      await updateDoc(teacherRef, {
        sections: selectedSections,
        updatedAt: new Date().toISOString()
      });

      // Update each class's sections with teacher info
      const updatePromises = selectedSections.map(async (section) => {
        const classRef = doc(db, 'classes', section.classId);
        const classDoc = await getDoc(classRef);

        if (classDoc.exists()) {
          const classData = classDoc.data();
          const updatedSections = classData.sections.map(s => {
            if (s.name === section.sectionName) {
              return {
                ...s,
                teacherId: selectedTeacher.id,
                teacherName: `${selectedTeacher.firstName} ${selectedTeacher.lastName}`
              };
            }
            return s;
          });

          await updateDoc(classRef, {
            sections: updatedSections,
            updatedAt: new Date().toISOString()
          });
        }
      });

      await Promise.all(updatePromises);

      setShowSectionsModal(false);
      fetchTeachers(true);
      fetchClasses();
      
      showToast(
        translate('common.success'),
        translate('teacher.management.sectionsAssigned'),
        'success'
      );
    } catch (error) {
      console.error('Error assigning sections:', error);
      setErrorMessage(translate('teacher.management.errorAssigningSections'));
      setSnackbarVisible(true);
    } finally {
      setLoading(false);
    }
  };
