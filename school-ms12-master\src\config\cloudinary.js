import { Cloudinary } from '@cloudinary/url-gen';
import { AdvancedImage } from '@cloudinary/react';
import { fill, scale, crop, thumbnail } from '@cloudinary/url-gen/actions/resize';
import { byRadius } from '@cloudinary/url-gen/actions/roundCorners';
import { format, quality } from '@cloudinary/url-gen/actions/delivery';
import { auto } from '@cloudinary/url-gen/qualifiers/quality';

// Parse Cloudinary URL from environment variable
const parseCloudinaryUrl = (url) => {
  try {
    // Format: cloudinary://api_key:api_secret@cloud_name
    const regex = /cloudinary:\/\/([^:]+):([^@]+)@(.+)/;
    const match = url.match(regex);

    if (match && match.length === 4) {
      return {
        apiKey: match[1],
        apiSecret: match[2],
        cloudName: match[3]
      };
    }

    throw new Error('Invalid Cloudinary URL format');
  } catch (error) {
    console.error('Error parsing Cloudinary URL:', error);
    return {
      apiKey: '173765679161433',
      apiSecret: 'TfMvTIc2j0kMeHijwsXwVgPyyGo',
      cloudName: 'dgonrknbt'
    };
  }
};

// Use the provided Cloudinary URL
const cloudinaryConfig = parseCloudinaryUrl('cloudinary://173765679161433:TfMvTIc2j0kMeHijwsXwVgPyyGo@dgonrknbt');

// Initialize Cloudinary instance
const cld = new Cloudinary({
  cloud: {
    cloudName: cloudinaryConfig.cloudName
  },
  url: {
    secure: true // Force HTTPS
  }
});

// Utility function to generate a Cloudinary URL for an image
export const getCloudinaryUrl = (publicId, options = {}) => {
  if (!publicId) return null;

  // Create a new image with the given public ID
  const image = cld.image(publicId);

  // Apply transformations based on options
  if (options.width && options.height) {
    if (options.crop === 'fill') {
      image.resize(fill().width(options.width).height(options.height));
    } else if (options.crop === 'scale') {
      image.resize(scale().width(options.width).height(options.height));
    } else if (options.crop === 'crop') {
      image.resize(crop().width(options.width).height(options.height));
    } else if (options.crop === 'thumb' || options.crop === 'thumbnail') {
      image.resize(thumbnail().width(options.width).height(options.height));
    }
  } else if (options.width) {
    image.resize(scale().width(options.width));
  } else if (options.height) {
    image.resize(scale().height(options.height));
  }

  // Apply round corners if specified
  if (options.radius) {
    image.roundCorners(byRadius(options.radius));
  }

  // Apply format if specified
  if (options.format) {
    image.delivery(format(options.format));
  }

  // Apply quality if specified
  if (options.quality) {
    if (options.quality === 'auto') {
      image.delivery(quality(auto()));
    } else {
      image.delivery(quality(options.quality));
    }
  }

  return image.toURL();
};

// Utility function to upload an image to Cloudinary
export const uploadToCloudinary = async (uri, options = {}) => {
  try {
    // Create form data for the upload
    const formData = new FormData();
    formData.append('file', {
      uri,
      type: 'image/jpeg',
      name: 'upload.jpg'
    });
    formData.append('upload_preset', options.uploadPreset || 'mobile_upload');
    formData.append('cloud_name', cloudinaryConfig.cloudName);

    // Add optional parameters
    if (options.folder) {
      formData.append('folder', options.folder);
    }

    if (options.publicId) {
      formData.append('public_id', options.publicId);
    }

    if (options.tags && Array.isArray(options.tags)) {
      formData.append('tags', options.tags.join(','));
    }

    // Upload to Cloudinary
    const response = await fetch(`https://api.cloudinary.com/v1_1/${cloudinaryConfig.cloudName}/image/upload`, {
      method: 'POST',
      body: formData,
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'multipart/form-data'
      }
    });

    const data = await response.json();

    if (response.ok) {
      return {
        publicId: data.public_id,
        url: data.secure_url,
        format: data.format,
        width: data.width,
        height: data.height,
        originalFilename: data.original_filename
      };
    } else {
      throw new Error(data.error?.message || 'Upload failed');
    }
  } catch (error) {
    console.error('Error uploading to Cloudinary:', error);
    throw error;
  }
};

// Utility function to delete an image from Cloudinary
export const deleteFromCloudinary = async (publicId) => {
  try {
    // Create form data for the delete request
    const formData = new FormData();
    formData.append('public_id', publicId);
    formData.append('api_key', cloudinaryConfig.apiKey);

    // Generate timestamp and signature for authentication
    const timestamp = Math.floor(Date.now() / 1000);
    formData.append('timestamp', timestamp);

    // In a real app, you would generate the signature on the server side
    // For this example, we'll use a simplified approach
    // Note: This is not secure for production use
    const signature = await generateSignature(publicId, timestamp);
    formData.append('signature', signature);

    // Send delete request to Cloudinary
    const response = await fetch(`https://api.cloudinary.com/v1_1/${cloudinaryConfig.cloudName}/image/destroy`, {
      method: 'POST',
      body: formData
    });

    const data = await response.json();

    if (response.ok && data.result === 'ok') {
      return true;
    } else {
      throw new Error(data.error?.message || 'Delete failed');
    }
  } catch (error) {
    console.error('Error deleting from Cloudinary:', error);
    throw error;
  }
};

// Helper function to generate a signature (simplified for example)
// In a real app, this should be done on the server side
const generateSignature = async (publicId, timestamp) => {
  // This is a placeholder. In a real app, you would call your backend to generate the signature
  // For security reasons, the API secret should never be exposed in client-side code
  console.warn('Generating signature client-side is not secure for production use');

  // For testing purposes only
  return 'generated_signature_placeholder';
};

// Export the Cloudinary components and configuration
export { AdvancedImage, cld, cloudinaryConfig };
export default cld;
