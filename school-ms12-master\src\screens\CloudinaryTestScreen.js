import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, Text } from 'react-native';
import { Button, Card, Title, Paragraph, SegmentedButtons } from 'react-native-paper';
import CloudinaryTest from '../components/common/CloudinaryTest';
import DirectCloudinaryTest from '../components/common/DirectCloudinaryTest';
import { useNavigation } from '@react-navigation/native';
import { useLanguage } from '../context/LanguageContext';

/**
 * A screen to test Cloudinary functionality
 */
const CloudinaryTestScreen = () => {
  const navigation = useNavigation();
  const { translate } = useLanguage();
  const [testMode, setTestMode] = useState('direct');

  return (
    <ScrollView style={styles.container}>
      <Card style={styles.headerCard}>
        <Card.Content>
          <Title>Cloudinary Configuration Test</Title>
          <Paragraph>
            This screen allows you to test the Cloudinary configuration and image upload functionality.
          </Paragraph>
        </Card.Content>
      </Card>

      <View style={styles.segmentContainer}>
        <SegmentedButtons
          value={testMode}
          onValueChange={setTestMode}
          buttons={[
            {
              value: 'direct',
              label: 'Direct API',
              icon: 'api'
            },
            {
              value: 'service',
              label: 'Service',
              icon: 'cloud-upload'
            }
          ]}
        />
      </View>

      {testMode === 'direct' ? (
        <DirectCloudinaryTest />
      ) : (
        <CloudinaryTest />
      )}

      <Card style={styles.infoCard}>
        <Card.Content>
          <Title>Cloudinary Information</Title>
          <Paragraph>
            Cloudinary is being used for image storage and processing in this application.
            If uploads are working correctly, you should be able to select and upload an image above.
          </Paragraph>
          <Paragraph style={styles.noteText}>
            Note: The "Direct API" mode uses direct API calls to Cloudinary without any dependencies,
            while the "Service" mode uses the CloudinaryService class.
          </Paragraph>
        </Card.Content>
      </Card>

      <Button
        mode="contained"
        style={styles.backButton}
        onPress={() => navigation.goBack()}
      >
        {translate('common.back') || 'Back'}
      </Button>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  headerCard: {
    marginBottom: 16,
  },
  segmentContainer: {
    marginBottom: 16,
    paddingHorizontal: 8,
  },
  infoCard: {
    marginTop: 16,
    marginBottom: 16,
  },
  noteText: {
    fontStyle: 'italic',
    marginTop: 8,
    fontSize: 12,
    color: '#666',
  },
  backButton: {
    marginTop: 16,
    marginBottom: 32,
  },
});

export default CloudinaryTestScreen;
