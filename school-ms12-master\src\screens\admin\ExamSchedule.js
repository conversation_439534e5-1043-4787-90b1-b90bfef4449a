import React, { useState, useEffect, useRef, useMemo } from 'react';
import { View, ScrollView, StyleSheet, Alert, Dimensions, TouchableOpacity, Animated, FlatList, RefreshControl, Platform } from 'react-native';
import { Card, Title, DataTable, FAB, Portal, Modal, TextInput, Button, Searchbar, List, Chip, Divider, Text, SegmentedButtons, Menu, useTheme, IconButton, ActivityIndicator, Avatar, Badge, Snackbar, Surface, ProgressBar, Switch, RadioButton, Checkbox } from 'react-native-paper';
import { db } from '../../config/firebase';
import { collection, query, getDocs, addDoc, doc, deleteDoc, updateDoc, where, getDoc, orderBy, limit, startAfter, Timestamp } from 'firebase/firestore';
import CustomButton from '../../components/common/CustomButton';
import CustomInput from '../../components/common/CustomInput';
import NotificationService from '../../services/NotificationService';
import ExamNotificationService from '../../services/ExamNotificationService';
import EthiopianDatePicker from '../../components/common/EthiopianDatePicker';
import EthiopianTimePicker from '../../components/common/EthiopianTimePicker';
import { useLanguage } from '../../context/LanguageContext';
import { useNavigation } from '@react-navigation/native';
import { SafeAreaView } from 'react-native-safe-area-context';
import * as Animatable from 'react-native-animatable';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import AdminAppHeader from '../../components/common/AdminAppHeader';
import AdminSidebar from '../../components/common/AdminSidebar';
import SidebarBackdrop from '../../components/common/SidebarBackdrop';
import { format, isAfter, isBefore, parseISO, addDays } from 'date-fns';
import { Calendar } from 'react-native-calendars';
import { useCallback } from 'react';

const ExamSchedule = () => {
  const navigation = useNavigation();
  // No theme needed
  const { translate, language, isRTL } = useLanguage();

  // Sidebar state
  const [sidebarVisible, setSidebarVisible] = useState(false);
  const [activeSidebarItem, setActiveSidebarItem] = useState('ExamSchedule');
  const drawerAnim = useRef(new Animated.Value(-300)).current;

  // Data state
  const [exams, setExams] = useState([]);
  const [subjects, setSubjects] = useState([]);
  const [classes, setClasses] = useState([]);
  const [visible, setVisible] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(false);
  const [selectedExam, setSelectedExam] = useState(null);
  const [examTypeFilter, setExamTypeFilter] = useState('all');
  const [publishLoading, setPublishLoading] = useState(false);

  // Form state
  const [teachers, setTeachers] = useState([]);
  const [showSubjectMenu, setShowSubjectMenu] = useState(false);
  const [showClassMenu, setShowClassMenu] = useState(false);
  const [showSectionMenu, setShowSectionMenu] = useState(false);
  const [showSupervisorMenu, setShowSupervisorMenu] = useState(false);
  const [sections, setSections] = useState([]);
  const [showActionModal, setShowActionModal] = useState(false);
  const [actionExam, setActionExam] = useState(null);

  // Additional filters
  const [classFilter, setClassFilter] = useState('all');
  const [subjectFilter, setSubjectFilter] = useState('all');
  const [dateFilter, setDateFilter] = useState('all'); // 'all', 'upcoming', 'past'

  // Enhanced UI state
  const [refreshing, setRefreshing] = useState(false);
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [viewMode, setViewMode] = useState('list'); // 'list', 'calendar', 'grid'
  const [showFilters, setShowFilters] = useState(true); // Control filter visibility

  // Confirmation dialog state
  const [confirmDialogVisible, setConfirmDialogVisible] = useState(false);
  const [confirmDialogAction, setConfirmDialogAction] = useState(null);
  const [confirmDialogParams, setConfirmDialogParams] = useState(null);
  const [confirmDialogTitle, setConfirmDialogTitle] = useState('');
  const [confirmDialogMessage, setConfirmDialogMessage] = useState('');

  // Search and view state
  const [isSearchFocused, setIsSearchFocused] = useState(false);
  const [calendarMarkedDates, setCalendarMarkedDates] = useState({});
  const [selectedDate, setSelectedDate] = useState(null);

  // Pagination state
  const [pagination, setPagination] = useState({
    limit: 20,
    lastVisible: null,
    hasMore: true,
    loading: false
  });

  const [formData, setFormData] = useState({
    title: '',
    subjectId: '',
    classId: '',
    section: '',
    date: '',
    startTime: '',
    duration: '',
    totalMarks: '100',
    passingMarks: '40',
    examType: 'Final', // Final, Midterm, Quiz
    description: '',
    instructions: '',
    published: false,
    room: '',
    supervisor: ''
  });

  // Toggle sidebar
  const toggleSidebar = () => {
    const toValue = sidebarVisible ? -300 : 0;
    Animated.timing(drawerAnim, {
      toValue,
      duration: 250,
      useNativeDriver: true,
    }).start();
    setSidebarVisible(!sidebarVisible);
  };

  // Toggle view mode between list, calendar, and grid
  const toggleViewMode = (mode) => {
    setViewMode(mode);
  };

  // Toggle filter visibility
  const toggleFilters = () => {
    setShowFilters(!showFilters);
  };

  // Show confirmation dialog
  const showConfirmDialog = (action, params, title, message) => {
    setConfirmDialogAction(() => action);
    setConfirmDialogParams(params);
    setConfirmDialogTitle(title);
    setConfirmDialogMessage(message);
    setConfirmDialogVisible(true);
  };

  // Handle confirmation dialog confirm action
  const handleConfirmDialogConfirm = () => {
    if (confirmDialogAction) {
      confirmDialogAction(confirmDialogParams);
    }
    setConfirmDialogVisible(false);
  };

  // Handle confirmation dialog dismiss
  const handleConfirmDialogDismiss = () => {
    setConfirmDialogVisible(false);
  };

  useEffect(() => {
    // Hide header in this screen as we're using our own
    navigation.setOptions({
      headerShown: false,
    });

    // Set active sidebar item
    setActiveSidebarItem('ExamSchedule');

    // Set today's date for calendar view
    const today = new Date().toISOString().split('T')[0];
    setSelectedDate(today);

    // Fetch initial data
    fetchExams();
    fetchSubjects();
    fetchClasses();
    fetchTeachers();
  }, [navigation]);

  // Refresh function for pull-to-refresh
  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      await fetchExams(true);
      setSnackbarMessage(translate('examSchedule.refreshSuccess') || 'Exam schedule refreshed');
      setSnackbarVisible(true);
    } catch (error) {
      console.error('Error refreshing exams:', error);
      setSnackbarMessage(translate('examSchedule.refreshError') || 'Failed to refresh exams');
      setSnackbarVisible(true);
    } finally {
      setRefreshing(false);
    }
  }, []);

  // Fetch exams with pagination support
  const fetchExams = async (reset = false) => {
    try {
      setLoading(true);

      // Reset pagination if requested
      if (reset) {
        setPagination(prev => ({
          ...prev,
          lastVisible: null,
          hasMore: true
        }));
      }

      // Don't fetch more if we're already at the end and not resetting
      if (!pagination.hasMore && !reset) {
        setLoading(false);
        return;
      }

      // Set up the query
      const examsRef = collection(db, 'exams');
      let q;

      if (pagination.lastVisible && !reset) {
        // Paginated query
        q = query(
          examsRef,
          orderBy('createdAt', 'desc'),
          startAfter(pagination.lastVisible),
          limit(pagination.limit)
        );
      } else {
        // Initial query
        q = query(
          examsRef,
          orderBy('createdAt', 'desc'),
          limit(pagination.limit)
        );
      }

      const querySnapshot = await getDocs(q);

      // Update pagination state
      const lastVisible = querySnapshot.docs[querySnapshot.docs.length - 1];
      const hasMore = querySnapshot.docs.length === pagination.limit;

      setPagination(prev => ({
        ...prev,
        lastVisible,
        hasMore
      }));

      // Process the results
      const examsData = [];
      querySnapshot.forEach((doc) => {
        examsData.push({ id: doc.id, ...doc.data() });
      });

      // Update the exams state
      if (reset) {
        setExams(examsData);
      } else {
        setExams(prev => [...prev, ...examsData]);
      }

      // Update calendar marked dates
      updateCalendarMarkedDates(reset ? examsData : [...exams, ...examsData]);

    } catch (error) {
      console.error('Error fetching exams:', error);
      setSnackbarMessage(translate('examSchedule.fetchError') || 'Failed to load exams');
      setSnackbarVisible(true);
    } finally {
      setLoading(false);
    }
  };

  // Update calendar marked dates based on exams
  const updateCalendarMarkedDates = (examsList) => {
    const markedDates = {};

    examsList.forEach(exam => {
      if (!exam.date) return;

      const dateStr = exam.date;
      const today = new Date().toISOString().split('T')[0];
      const isPast = dateStr < today;
      const isPublished = exam.published;

      // Determine dot color based on exam status
      let dotColor;
      if (isPublished) {
        dotColor = isPast ? '#4CAF50' : '#2196F3';
      } else {
        dotColor = '#FF9800';
      }

      // Add or update the marked date
      if (markedDates[dateStr]) {
        // Add another dot if the date already exists
        markedDates[dateStr].dots.push({
          key: exam.id,
          color: dotColor,
          selectedDotColor: 'white'
        });
      } else {
        // Create a new marked date
        markedDates[dateStr] = {
          dots: [{
            key: exam.id,
            color: dotColor,
            selectedDotColor: 'white'
          }],
          selected: dateStr === selectedDate,
          selectedColor: 'rgba(33, 150, 243, 0.1)'
        };
      }
    });

    setCalendarMarkedDates(markedDates);
  };

  const fetchSubjects = async () => {
    try {
      const subjectsRef = collection(db, 'subjects');
      const q = query(subjectsRef);
      const querySnapshot = await getDocs(q);

      const subjectsData = [];
      querySnapshot.forEach((doc) => {
        subjectsData.push({ id: doc.id, ...doc.data() });
      });

      setSubjects(subjectsData);
    } catch (error) {
      console.error('Error fetching subjects:', error);
    }
  };

  const fetchClasses = async () => {
    try {
      const classesRef = collection(db, 'classes');
      const q = query(classesRef);
      const querySnapshot = await getDocs(q);

      const classesData = [];
      querySnapshot.forEach((doc) => {
        classesData.push({ id: doc.id, ...doc.data() });
      });

      setClasses(classesData);
    } catch (error) {
      console.error('Error fetching classes:', error);
    }
  };

  const fetchTeachers = async () => {
    try {
      const teachersRef = collection(db, 'users');
      const q = query(teachersRef, where('role', '==', 'teacher'));
      const querySnapshot = await getDocs(q);

      const teachersData = [];
      querySnapshot.forEach((doc) => {
        teachersData.push({
          id: doc.id,
          ...doc.data(),
          displayName: `${doc.data().firstName || ''} ${doc.data().lastName || ''}`.trim() || doc.data().email
        });
      });

      setTeachers(teachersData);
    } catch (error) {
      console.error('Error fetching teachers:', error);
    }
  };

  const handleAddExam = async () => {
    try {
      // Validate required fields
      if (!formData.title || !formData.subjectId || !formData.classId || !formData.section || !formData.date || !formData.startTime) {
        Alert.alert('Missing Information', 'Please fill in all required fields (title, subject, class, section, date, and time)');
        return;
      }

      setLoading(true);
      const examsRef = collection(db, 'exams');
      await addDoc(examsRef, {
        ...formData,
        totalMarks: parseInt(formData.totalMarks),
        passingMarks: parseInt(formData.passingMarks),
        published: false,
        createdAt: new Date().toISOString(),
      });

      setVisible(false);
      resetForm();
      fetchExams();
    } catch (error) {
      console.error('Error adding exam:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateExam = async () => {
    try {
      // Validate required fields
      if (!formData.title || !formData.subjectId || !formData.classId || !formData.section || !formData.date || !formData.startTime) {
        Alert.alert('Missing Information', 'Please fill in all required fields (title, subject, class, section, date, and time)');
        return;
      }

      setLoading(true);
      const examRef = doc(db, 'exams', selectedExam.id);
      await updateDoc(examRef, {
        ...formData,
        totalMarks: parseInt(formData.totalMarks),
        passingMarks: parseInt(formData.passingMarks),
        updatedAt: new Date().toISOString(),
      });

      setVisible(false);
      resetForm();
      fetchExams();
    } catch (error) {
      console.error('Error updating exam:', error);
    } finally {
      setLoading(false);
    }
  };

  const handlePublishExam = async (examId) => {
    try {
      // Show confirmation dialog
      showConfirmDialog(
        async () => {
          try {
            setPublishLoading(true);
            const examRef = doc(db, 'exams', examId);
            const examDoc = await getDoc(examRef);

            if (!examDoc.exists()) {
              setSnackbarMessage(translate('examSchedule.examNotFound') || 'Exam not found');
              setSnackbarVisible(true);
              return;
            }

            const examData = examDoc.data();

            // Get class and subject details for the notification
            const classRef = doc(db, 'classes', examData.classId);
            const classDoc = await getDoc(classRef);
            const className = classDoc.exists() ? classDoc.data().name : 'Unknown Class';

            const subjectRef = doc(db, 'subjects', examData.subjectId);
            const subjectDoc = await getDoc(subjectRef);
            const subjectName = subjectDoc.exists() ? subjectDoc.data().name : 'Unknown Subject';

            // Prepare exam data for notifications
            const examWithDetails = {
              ...examData,
              id: examId,
              className,
              subjectName
            };

            // Send notifications to all relevant users using our specialized service
            await ExamNotificationService.notifyAllUsers(examWithDetails);

            // Refresh the exams list
            await fetchExams(true);
            setSnackbarMessage(translate('examSchedule.publishSuccess') || `${examData.examType} exam schedule published successfully`);
            setSnackbarVisible(true);
            setShowActionModal(false);
          } catch (error) {
            console.error('Error publishing exam schedule:', error);
            setSnackbarMessage(translate('examSchedule.publishError') || 'Failed to publish exam schedule');
            setSnackbarVisible(true);
          } finally {
            setPublishLoading(false);
          }
        },
        examId,
        translate('examSchedule.confirmPublish') || 'Confirm Publish',
        translate('examSchedule.confirmPublishMessage') || 'Are you sure you want to publish this exam? This will send notifications to all users.'
      );
    } catch (error) {
      console.error('Error preparing to publish exam:', error);
    }
  };

  const handlePublishAllExams = async (examType) => {
    try {
      // First check if there are any unpublished exams of this type
      const examsRef = collection(db, 'exams');
      const q = query(examsRef, where('examType', '==', examType), where('published', '==', false));
      const querySnapshot = await getDocs(q);

      if (querySnapshot.empty) {
        setSnackbarMessage(translate('examSchedule.noUnpublishedExams') || `No unpublished ${examType} exams found.`);
        setSnackbarVisible(true);
        return;
      }

      // Show confirmation dialog
      showConfirmDialog(
        async () => {
          try {
            setPublishLoading(true);

            // Collect all exams with their details for notification
            const examsToPublish = [];

            for (const examDoc of querySnapshot.docs) {
              const examId = examDoc.id;
              const examData = examDoc.data();

              // Get class and subject details for the notification
              const classRef = doc(db, 'classes', examData.classId);
              const classDoc = await getDoc(classRef);
              const className = classDoc.exists() ? classDoc.data().name : 'Unknown Class';

              const subjectRef = doc(db, 'subjects', examData.subjectId);
              const subjectDoc = await getDoc(subjectRef);
              const subjectName = subjectDoc.exists() ? subjectDoc.data().name : 'Unknown Subject';

              // Add to the list of exams to publish
              examsToPublish.push({
                ...examData,
                id: examId,
                className,
                subjectName
              });
            }

            // Send notifications for all exams
            await ExamNotificationService.notifyAllUsersForExams(examsToPublish);

            // Refresh the exams list
            await fetchExams(true);
            setSnackbarMessage(translate('examSchedule.publishAllSuccess') || `All ${examType} exam schedules published successfully`);
            setSnackbarVisible(true);
          } catch (error) {
            console.error('Error publishing exam schedules:', error);
            setSnackbarMessage(translate('examSchedule.publishAllError') || 'Failed to publish exam schedules');
            setSnackbarVisible(true);
          } finally {
            setPublishLoading(false);
          }
        },
        examType,
        translate('examSchedule.confirmPublishAll') || `Confirm Publish All ${examType} Exams`,
        translate('examSchedule.confirmPublishAllMessage') || `Are you sure you want to publish all unpublished ${examType} exams? This will send notifications to all users.`
      );
    } catch (error) {
      console.error('Error preparing to publish all exams:', error);
    }
  };

  const handleDeleteExam = async (examId) => {
    try {
      // Show confirmation dialog
      showConfirmDialog(
        async () => {
          try {
            setLoading(true);
            await deleteDoc(doc(db, 'exams', examId));
            await fetchExams(true);
            setSnackbarMessage(translate('examSchedule.deleteSuccess') || 'Exam deleted successfully');
            setSnackbarVisible(true);
            setShowActionModal(false);
          } catch (error) {
            console.error('Error deleting exam:', error);
            setSnackbarMessage(translate('examSchedule.deleteError') || 'Failed to delete exam');
            setSnackbarVisible(true);
          } finally {
            setLoading(false);
          }
        },
        examId,
        translate('examSchedule.confirmDelete') || 'Confirm Delete',
        translate('examSchedule.confirmDeleteMessage') || 'Are you sure you want to delete this exam? This action cannot be undone.'
      );
    } catch (error) {
      console.error('Error preparing to delete exam:', error);
    }
  };

  const resetForm = () => {
    setFormData({
      title: '',
      subjectId: '',
      classId: '',
      section: '',
      date: '',
      startTime: '',
      duration: '',
      totalMarks: '100',
      passingMarks: '40',
      examType: 'Final',
      description: '',
      instructions: '',
      published: false,
      room: '',
      supervisor: ''
    });
    setSelectedExam(null);
    setSections([]);
  };

  const filteredExams = exams.filter(exam => {
    // Search query matching
    const matchesSearch =
      exam.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      exam.examType.toLowerCase().includes(searchQuery.toLowerCase()) ||
      getSubjectName(exam.subjectId).toLowerCase().includes(searchQuery.toLowerCase()) ||
      getClassName(exam.classId).toLowerCase().includes(searchQuery.toLowerCase());

    // Exam type filter
    const matchesExamType = examTypeFilter === 'all' || exam.examType === examTypeFilter;

    // Class filter
    const matchesClass = classFilter === 'all' || exam.classId === classFilter;

    // Subject filter
    const matchesSubject = subjectFilter === 'all' || exam.subjectId === subjectFilter;

    // Date filter
    let matchesDate = true;
    if (dateFilter !== 'all') {
      const today = new Date().toISOString().split('T')[0];
      const examDate = exam.date;

      if (dateFilter === 'upcoming') {
        matchesDate = examDate >= today;
      } else if (dateFilter === 'past') {
        matchesDate = examDate < today;
      }
    }

    return matchesSearch && matchesExamType && matchesClass && matchesSubject && matchesDate;
  });

  const getSubjectName = (subjectId) => {
    const subject = subjects.find(s => s.id === subjectId);
    return subject ? subject.name : 'N/A';
  };

  const getClassName = (classId) => {
    const classObj = classes.find(c => c.id === classId);
    return classObj ? classObj.name : 'N/A';
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Admin App Header */}
      <AdminAppHeader
        title={translate('examSchedule.title') || "Exam Schedule"}
        onMenuPress={toggleSidebar}
      />

      {/* Sidebar Backdrop - only visible when sidebar is open */}
      {sidebarVisible && (
        <SidebarBackdrop onPress={toggleSidebar} />
      )}

      {/* Admin Sidebar */}
      <AdminSidebar
        drawerAnim={drawerAnim}
        activeSidebarItem={activeSidebarItem}
        setActiveSidebarItem={setActiveSidebarItem}
        toggleDrawer={toggleSidebar}
      />

      {/* Main Content */}
      <View style={styles.content}>
        {/* Enhanced Search Bar with Filter Toggle */}
        <Animatable.View animation="fadeIn" duration={500}>
          <View style={styles.searchContainer}>
            <Searchbar
              placeholder={translate('examSchedule.searchPlaceholder') || "Search exams..."}
              onChangeText={query => setSearchQuery(query)}
              value={searchQuery}
              style={styles.searchBar}
              icon="magnify"
              iconColor={'#1976d2'}
              onFocus={() => setIsSearchFocused(true)}
              onBlur={() => setIsSearchFocused(false)}
            />
            <IconButton
              icon={showFilters ? "filter-variant" : "filter-variant-plus"}
              size={24}
              style={styles.filterToggleButton}
              onPress={toggleFilters}
              color={'#1976d2'}
            />
          </View>
        </Animatable.View>

      {showFilters && (
        <Animatable.View
          animation={showFilters ? "fadeIn" : "fadeOut"}
          duration={300}
          style={styles.filterContainer}
        >
          <View style={styles.filterHeader}>
            <Text style={styles.filterTitle}>{translate('examSchedule.filters') || "Filters"}</Text>
            <IconButton
              icon="chevron-up"
              size={24}
              onPress={toggleFilters}
              color={'#1976d2'}
            />
          </View>

        {/* Exam Type Filter */}
        <Text style={styles.filterLabel}>{translate('examSchedule.examType') || "Exam Type"}</Text>
        <SegmentedButtons
          value={examTypeFilter}
          onValueChange={setExamTypeFilter}
          buttons={[
            { value: 'all', label: translate('common.all') || 'All' },
            { value: 'Midterm', label: translate('examSchedule.midterm') || 'Midterm' },
            { value: 'Final', label: translate('examSchedule.final') || 'Final' },
            { value: 'Quiz', label: translate('examSchedule.quiz') || 'Quiz' },
          ]}
          style={styles.segmentedButtons}
        />

        {/* Class Filter */}
        <Text style={styles.filterLabel}>{translate('examSchedule.class') || "Class"}</Text>
        <SegmentedButtons
          value={classFilter}
          onValueChange={setClassFilter}
          buttons={[
            { value: 'all', label: translate('common.all') || 'All' },
            ...classes.slice(0, 3).map(cls => ({ value: cls.id, label: cls.name }))
          ]}
          style={styles.segmentedButtons}
        />

        {/* Date Filter */}
        <Text style={styles.filterLabel}>{translate('examSchedule.date') || "Date"}</Text>
        <SegmentedButtons
          value={dateFilter}
          onValueChange={setDateFilter}
          buttons={[
            { value: 'all', label: translate('common.all') || 'All' },
            { value: 'upcoming', label: translate('examSchedule.upcoming') || 'Upcoming' },
            { value: 'past', label: translate('examSchedule.past') || 'Past' },
          ]}
          style={styles.segmentedButtons}
        />

        <View style={styles.publishButtonsContainer}>
          <CustomButton
            mode="contained"
            onPress={() => handlePublishAllExams('Midterm')}
            loading={publishLoading}
            style={[styles.publishButton, { backgroundColor: '#FF9800' }]}
            icon="send"
          >
            {translate('examSchedule.publishAllMidterm') || "Publish All Midterm"}
          </CustomButton>
          <CustomButton
            mode="contained"
            onPress={() => handlePublishAllExams('Final')}
            loading={publishLoading}
            style={[styles.publishButton, { backgroundColor: '#4CAF50' }]}
            icon="send"
          >
            {translate('examSchedule.publishAllFinal') || "Publish All Final"}
          </CustomButton>
        </View>
        </Animatable.View>
      )}

      <ScrollView
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={['#1976d2']}
            tintColor={'#1976d2'}
          />
        }>
        <Animatable.View animation="fadeIn" duration={500} delay={200}>
          <Card style={styles.card}>
            <Card.Content>
              <View style={styles.cardHeader}>
                <Title style={styles.cardTitle}>{translate('examSchedule.title') || "Exam Schedule"}</Title>
                <View style={styles.cardActions}>
                  <SegmentedButtons
                    value={viewMode}
                    onValueChange={toggleViewMode}
                    buttons={[
                      { value: 'list', icon: 'format-list-bulleted', label: '' },
                      { value: 'calendar', icon: 'calendar-month', label: '' },
                      { value: 'grid', icon: 'grid', label: '' },
                    ]}
                    style={styles.viewToggle}
                  />
                  <Chip
                    icon="filter-variant"
                    mode="outlined"
                    style={styles.countChip}
                  >
                    {filteredExams.length} {translate('examSchedule.exams') || "exams"}
                  </Chip>
                </View>
              </View>

              {loading ? (
                <View style={styles.loadingContainer}>
                  <ActivityIndicator size="large" color={'#1976d2'} />
                  <Text style={styles.loadingText}>{translate('common.loading') || "Loading..."}</Text>
                </View>
              ) : filteredExams.length === 0 ? (
                <View style={styles.emptyContainer}>
                  <IconButton icon="calendar-alert" size={50} color={'#9e9e9e'} />
                  <Text style={styles.emptyText}>{translate('examSchedule.noExams') || "No exams found"}</Text>
                  <Text style={styles.emptySubtext}>{translate('examSchedule.tryDifferentFilters') || "Try different filters or add a new exam"}</Text>
                </View>
              ) : viewMode === 'calendar' ? (
                // Calendar View
                <View style={styles.calendarContainer}>
                  <Calendar
                    markingType={'multi-dot'}
                    markedDates={calendarMarkedDates}
                    onDayPress={(day) => {
                      setSelectedDate(day.dateString);
                      // Update marked dates to highlight the selected date
                      updateCalendarMarkedDates(exams, day.dateString);
                    }}
                    theme={{
                      selectedDayBackgroundColor: '#1976d2',
                      todayTextColor: '#1976d2',
                      arrowColor: '#1976d2',
                    }}
                  />

                  {selectedDate && (
                    <View style={styles.selectedDateExams}>
                      <View style={styles.selectedDateHeader}>
                        <Text style={styles.selectedDateTitle}>
                          {translate('examSchedule.examsOn') || "Exams on"} {selectedDate}
                        </Text>
                      </View>

                      {filteredExams.filter(exam => exam.date === selectedDate).length > 0 ? (
                        filteredExams
                          .filter(exam => exam.date === selectedDate)
                          .map(exam => {
                            const today = new Date().toISOString().split('T')[0];
                            const isPast = exam.date < today;

                            return (
                              <Surface key={exam.id} style={styles.examCard} elevation={1}>
                                <TouchableOpacity
                                  onPress={() => {
                                    setActionExam(exam);
                                    setShowActionModal(true);
                                  }}
                                  style={styles.examCardContent}
                                >
                                  <View style={styles.examCardHeader}>
                                    <Text style={styles.examCardTitle}>{exam.title}</Text>
                                    <Chip
                                      mode="flat"
                                      style={{
                                        backgroundColor: exam.published
                                          ? (isPast ? '#E8F5E9' : '#E3F2FD')
                                          : '#FFF3E0',
                                        height: 24
                                      }}
                                      textStyle={{ fontSize: 10 }}
                                      icon={exam.published
                                        ? (isPast ? 'check-circle' : 'clock-outline')
                                        : 'pencil'}
                                    >
                                      {exam.published
                                        ? (isPast
                                          ? (translate('examSchedule.completed') || "Completed")
                                          : (translate('examSchedule.scheduled') || "Scheduled"))
                                        : (translate('examSchedule.draft') || "Draft")}
                                    </Chip>
                                  </View>

                                  <View style={styles.examCardDetails}>
                                    <View style={styles.examCardDetail}>
                                      <MaterialCommunityIcons name="book-open-variant" size={16} color="#666" />
                                      <Text style={styles.examCardDetailText}>{getSubjectName(exam.subjectId)}</Text>
                                    </View>

                                    <View style={styles.examCardDetail}>
                                      <MaterialCommunityIcons name="account-group" size={16} color="#666" />
                                      <Text style={styles.examCardDetailText}>{getClassName(exam.classId)} {exam.section}</Text>
                                    </View>

                                    <View style={styles.examCardDetail}>
                                      <MaterialCommunityIcons name="clock-outline" size={16} color="#666" />
                                      <Text style={styles.examCardDetailText}>{exam.startTime} ({exam.duration} min)</Text>
                                    </View>
                                  </View>
                                </TouchableOpacity>
                              </Surface>
                            );
                          })
                      ) : (
                        <View style={styles.noExamsOnDate}>
                          <Text style={styles.noExamsText}>
                            {translate('examSchedule.noExamsOnDate') || "No exams scheduled on this date"}
                          </Text>
                        </View>
                      )}
                    </View>
                  )}
                </View>
              ) : viewMode === 'grid' ? (
                // Grid View
                <ScrollView contentContainerStyle={styles.gridContainer}>
                  {filteredExams.map(exam => {
                    const today = new Date().toISOString().split('T')[0];
                    const examDate = exam.date;
                    const isPast = examDate < today;

                    return (
                      <Surface key={exam.id} style={styles.gridCard} elevation={2}>
                        <TouchableOpacity
                          onPress={() => {
                            setActionExam(exam);
                            setShowActionModal(true);
                          }}
                          style={styles.gridCardContent}
                        >
                          <View style={styles.gridCardHeader}>
                            <View style={styles.gridCardBadge}>
                              <Badge
                                size={24}
                                style={{
                                  backgroundColor: exam.published
                                    ? (isPast ? '#4CAF50' : '#2196F3')
                                    : '#FF9800'
                                }}
                              >{exam.examType.charAt(0)}</Badge>
                            </View>
                            <Text style={styles.gridCardTitle} numberOfLines={1}>{exam.title}</Text>
                          </View>

                          <Divider style={styles.gridCardDivider} />

                          <View style={styles.gridCardDetails}>
                            <View style={styles.gridCardDetail}>
                              <MaterialCommunityIcons name="book-open-variant" size={14} color="#666" />
                              <Text style={styles.gridCardDetailText} numberOfLines={1}>{getSubjectName(exam.subjectId)}</Text>
                            </View>

                            <View style={styles.gridCardDetail}>
                              <MaterialCommunityIcons name="account-group" size={14} color="#666" />
                              <Text style={styles.gridCardDetailText} numberOfLines={1}>{getClassName(exam.classId)} {exam.section}</Text>
                            </View>

                            <View style={styles.gridCardDetail}>
                              <MaterialCommunityIcons name="calendar" size={14} color="#666" />
                              <Text style={styles.gridCardDetailText}>{exam.date}</Text>
                            </View>

                            <View style={styles.gridCardDetail}>
                              <MaterialCommunityIcons name="clock-outline" size={14} color="#666" />
                              <Text style={styles.gridCardDetailText}>{exam.startTime}</Text>
                            </View>
                          </View>

                          <View style={styles.gridCardFooter}>
                            <Chip
                              mode="flat"
                              style={{
                                backgroundColor: exam.published
                                  ? (isPast ? '#E8F5E9' : '#E3F2FD')
                                  : '#FFF3E0',
                                height: 24
                              }}
                              textStyle={{ fontSize: 10 }}
                              icon={exam.published
                                ? (isPast ? 'check-circle' : 'clock-outline')
                                : 'pencil'}
                            >
                              {exam.published
                                ? (isPast
                                  ? (translate('examSchedule.completed') || "Completed")
                                  : (translate('examSchedule.scheduled') || "Scheduled"))
                                : (translate('examSchedule.draft') || "Draft")}
                            </Chip>
                          </View>
                        </TouchableOpacity>
                      </Surface>
                    );
                  })}
                </ScrollView>
              ) : (
                // List View (Default)
                <DataTable>
                  <DataTable.Header style={styles.tableHeader}>
                    <DataTable.Title style={[styles.tableHeaderCell, { flex: 2 }]}>
                      <Text style={styles.tableHeaderText}>{translate('examSchedule.subject') || "Subject"}</Text>
                    </DataTable.Title>
                    <DataTable.Title style={[styles.tableHeaderCell, { flex: 2 }]}>
                      <Text style={styles.tableHeaderText}>{translate('examSchedule.class') || "Class"}</Text>
                    </DataTable.Title>
                    <DataTable.Title style={[styles.tableHeaderCell, { flex: 1.5 }]}>
                      <Text style={styles.tableHeaderText}>{translate('examSchedule.date') || "Date"}</Text>
                    </DataTable.Title>
                    <DataTable.Title style={[styles.tableHeaderCell, { flex: 1 }]}>
                      <Text style={styles.tableHeaderText}>{translate('examSchedule.time') || "Time"}</Text>
                    </DataTable.Title>
                    <DataTable.Title style={[styles.tableHeaderCell, { flex: 1 }]}>
                      <Text style={styles.tableHeaderText}>{translate('examSchedule.status') || "Status"}</Text>
                    </DataTable.Title>
                  </DataTable.Header>

                  {filteredExams.map((exam) => {
                    // Determine if exam is upcoming, ongoing, or past
                    const today = new Date().toISOString().split('T')[0];
                    const examDate = exam.date;
                    const isUpcoming = examDate > today;
                    const isPast = examDate < today;

                    return (
                      <DataTable.Row
                        key={exam.id}
                        onPress={() => {
                          setActionExam(exam);
                          setShowActionModal(true);
                        }}
                        style={[styles.tableRow, exam.published ? styles.publishedRow : styles.unpublishedRow]}
                      >
                        <DataTable.Cell style={[styles.tableCell, { flex: 2 }]}>
                          <Text style={styles.tableCellText}>{getSubjectName(exam.subjectId)}</Text>
                        </DataTable.Cell>
                        <DataTable.Cell style={[styles.tableCell, { flex: 2 }]}>
                          <View style={styles.classSection}>
                            <Text style={styles.tableCellText}>{getClassName(exam.classId)}</Text>
                            {exam.section && (
                              <Chip size="small" style={styles.sectionChip}>
                                {exam.section}
                              </Chip>
                            )}
                          </View>
                        </DataTable.Cell>
                        <DataTable.Cell style={[styles.tableCell, { flex: 1.5 }]}>
                          <Text style={styles.tableCellText}>{exam.date}</Text>
                        </DataTable.Cell>
                        <DataTable.Cell style={[styles.tableCell, { flex: 1 }]}>
                          <Text style={styles.tableCellText}>{exam.startTime}</Text>
                        </DataTable.Cell>
                        <DataTable.Cell style={[styles.tableCell, { flex: 1 }]}>
                          <Chip
                            mode="flat"
                            style={{
                              backgroundColor: exam.published
                                ? (isPast ? '#E8F5E9' : '#E3F2FD')
                                : '#FFF3E0',
                            }}
                            textStyle={{ fontSize: 10 }}
                            icon={exam.published
                              ? (isPast ? 'check-circle' : 'clock-outline')
                              : 'pencil'}
                          >
                            {exam.published
                              ? (isPast
                                ? (translate('examSchedule.completed') || "Completed")
                                : (translate('examSchedule.scheduled') || "Scheduled"))
                              : (translate('examSchedule.draft') || "Draft")}
                          </Chip>
                        </DataTable.Cell>
                      </DataTable.Row>
                    );
                  })}
                </DataTable>
              )}
            </Card.Content>
          </Card>
        </Animatable.View>
      </ScrollView>

      <Portal>
        <Modal
          visible={visible}
          onDismiss={() => {
            setVisible(false);
            resetForm();
          }}
          contentContainerStyle={styles.modalContent}
        >
          <ScrollView
            showsVerticalScrollIndicator={true}
            contentContainerStyle={styles.modalScrollContent}>
            <View style={styles.modalHeader}>
              <Title style={styles.modalTitle}>
                {selectedExam
                  ? (translate('examSchedule.editExam') || 'Edit Exam')
                  : (translate('examSchedule.addNewExam') || 'Add New Exam')}
              </Title>
              <IconButton
                icon="close"
                size={24}
                onPress={() => {
                  setVisible(false);
                  resetForm();
                }}
              />
            </View>

            <Divider style={styles.modalDivider} />

            <CustomInput
              label={translate('examSchedule.examTitle') || "Title"}
              value={formData.title}
              onChangeText={(text) => setFormData({ ...formData, title: text })}
              placeholder={translate('examSchedule.examTitlePlaceholder') || "Enter exam title"}
              style={styles.formInput}
            />

            <View style={styles.menuContainer}>
              <Text style={styles.inputLabel}>Select Subject</Text>
              <Menu
                visible={showSubjectMenu}
                onDismiss={() => setShowSubjectMenu(false)}
                anchor={
                  <Button
                    mode="outlined"
                    onPress={() => setShowSubjectMenu(true)}
                    style={styles.selectButton}
                    icon="book"
                  >
                    {formData.subjectId ? getSubjectName(formData.subjectId) : 'Select Subject'}
                  </Button>
                }
                contentStyle={styles.menuContent}
              >
                <ScrollView style={styles.menuScrollView} contentContainerStyle={styles.menuScrollContent}>
                  {subjects.map(subject => (
                    <Menu.Item
                      key={subject.id}
                      title={subject.name}
                      onPress={() => {
                        setFormData({ ...formData, subjectId: subject.id });
                        setShowSubjectMenu(false);
                      }}
                    />
                  ))}
                </ScrollView>
              </Menu>
            </View>

            <View style={styles.menuContainer}>
              <Text style={styles.inputLabel}>Select Class</Text>
              <Menu
                visible={showClassMenu}
                onDismiss={() => setShowClassMenu(false)}
                anchor={
                  <Button
                    mode="outlined"
                    onPress={() => setShowClassMenu(true)}
                    style={styles.selectButton}
                    icon="school"
                  >
                    {formData.classId ? getClassName(formData.classId) : 'Select Class'}
                  </Button>
                }
                contentStyle={styles.menuContent}
              >
                <ScrollView style={styles.menuScrollView} contentContainerStyle={styles.menuScrollContent}>
                  {classes.map(cls => (
                    <Menu.Item
                      key={cls.id}
                      title={cls.name}
                      onPress={() => {
                        setFormData({ ...formData, classId: cls.id, section: '' });
                        setShowClassMenu(false);

                        // Fetch sections for the selected class
                        const selectedClass = classes.find(c => c.id === cls.id);
                        if (selectedClass && selectedClass.sections) {
                          setSections(selectedClass.sections.map(section =>
                            typeof section === 'string' ? section : section.name
                          ));
                        } else {
                          setSections([]);
                        }
                      }}
                    />
                  ))}
                </ScrollView>
              </Menu>
            </View>

            <View style={styles.menuContainer}>
              <Text style={styles.inputLabel}>Select Section</Text>
              <Menu
                visible={showSectionMenu}
                onDismiss={() => setShowSectionMenu(false)}
                anchor={
                  <Button
                    mode="outlined"
                    onPress={() => setShowSectionMenu(true)}
                    style={styles.selectButton}
                    icon="view-grid-outline"
                    disabled={!formData.classId || sections.length === 0}
                  >
                    {formData.section ? `Section ${formData.section}` : 'Select Section'}
                  </Button>
                }
                contentStyle={styles.menuContent}
              >
                <ScrollView style={styles.menuScrollView} contentContainerStyle={styles.menuScrollContent}>
                  {sections.map((section, index) => (
                    <Menu.Item
                      key={index}
                      title={`Section ${section}`}
                      onPress={() => {
                        setFormData({ ...formData, section });
                        setShowSectionMenu(false);
                      }}
                    />
                  ))}
                </ScrollView>
              </Menu>
            </View>

            <View style={styles.datePickerContainer}>
              <Text style={styles.inputLabel}>Date</Text>
              <EthiopianDatePicker
                value={formData.date ? new Date(formData.date) : new Date()}
                onChange={(selectedDate) => {
                  setFormData({
                    ...formData,
                    date: selectedDate.toISOString().split('T')[0]
                  });
                }}
                label="Select Exam Date"
                language={language}
              />
            </View>

            <View style={styles.datePickerContainer}>
              <Text style={styles.inputLabel}>Start Time</Text>
              <EthiopianTimePicker
                value={formData.startTime ?
                  new Date(`2023-01-01T${formData.startTime}:00`) :
                  new Date()}
                onChange={(selectedTime) => {
                  const hours = selectedTime.getHours().toString().padStart(2, '0');
                  const minutes = selectedTime.getMinutes().toString().padStart(2, '0');
                  setFormData({
                    ...formData,
                    startTime: `${hours}:${minutes}`
                  });
                }}
                label="Select Start Time"
                is24Hour={true}
              />
            </View>

            <CustomInput
              label="Duration (minutes)"
              value={formData.duration}
              onChangeText={(text) => setFormData({ ...formData, duration: text })}
              keyboardType="numeric"
            />

            <CustomInput
              label="Room/Location"
              value={formData.room}
              onChangeText={(text) => setFormData({ ...formData, room: text })}
            />

            <View style={styles.menuContainer}>
              <Text style={styles.inputLabel}>Select Supervisor</Text>
              <Menu
                visible={showSupervisorMenu}
                onDismiss={() => setShowSupervisorMenu(false)}
                anchor={
                  <Button
                    mode="outlined"
                    onPress={() => setShowSupervisorMenu(true)}
                    style={styles.selectButton}
                    icon="account"
                  >
                    {formData.supervisor ?
                      teachers.find(t => t.id === formData.supervisor)?.displayName || 'Select Supervisor' :
                      'Select Supervisor'}
                  </Button>
                }
              >
                {teachers.map(teacher => (
                  <Menu.Item
                    key={teacher.id}
                    title={teacher.displayName}
                    onPress={() => {
                      setFormData({ ...formData, supervisor: teacher.id });
                      setShowSupervisorMenu(false);
                    }}
                  />
                ))}
              </Menu>
            </View>

            <View style={styles.menuContainer}>
              <Text style={styles.inputLabel}>Exam Type</Text>
              <SegmentedButtons
                value={formData.examType}
                onValueChange={(value) => setFormData({ ...formData, examType: value })}
                buttons={[
                  { value: 'Final', label: 'Final' },
                  { value: 'Midterm', label: 'Midterm' },
                  { value: 'Quiz', label: 'Quiz' }
                ]}
                style={styles.segmentedButtons}
              />
            </View>


            <View style={styles.formActions}>
              <CustomButton
                mode="outlined"
                onPress={() => {
                  setVisible(false);
                  resetForm();
                }}
                style={styles.cancelButton}
                icon="close"
              >
                {translate('common.cancel') || "Cancel"}
              </CustomButton>

              <CustomButton
                mode="contained"
                onPress={selectedExam ? handleUpdateExam : handleAddExam}
                loading={loading}
                style={styles.submitButton}
                icon={selectedExam ? "content-save" : "plus"}
              >
                {selectedExam
                  ? (translate('examSchedule.updateExam') || 'Update Exam')
                  : (translate('examSchedule.addExam') || 'Add Exam')}
              </CustomButton>
            </View>
          </ScrollView>
        </Modal>
      </Portal>

      <Portal>
        <Modal
          visible={showActionModal}
          onDismiss={() => setShowActionModal(false)}
          contentContainerStyle={styles.actionModalContent}
        >
          {actionExam && (
            <Animatable.View animation="fadeIn" duration={300}>
              <Card style={styles.actionCard}>
                <LinearGradient
                  colors={actionExam.published ? ['#E8F5E9', '#C8E6C9'] : ['#FFF8E1', '#FFECB3']}
                  style={styles.actionCardHeader}
                >
                  <View style={styles.actionCardHeaderContent}>
                    <View>
                      <Title style={styles.actionCardTitle}>{actionExam.title}</Title>
                      <Text style={styles.actionCardSubtitle}>
                        {`${getSubjectName(actionExam.subjectId)} | ${actionExam.examType} Exam`}
                      </Text>
                    </View>
                    <Chip
                      mode="flat"
                      style={{
                        backgroundColor: actionExam.published ? '#4CAF50' : '#FF9800',
                        height: 28
                      }}
                      textStyle={{ color: 'white', fontWeight: 'bold' }}
                      icon={actionExam.published ? 'check-circle' : 'clock-outline'}
                    >
                      {actionExam.published
                        ? (translate('examSchedule.published') || "Published")
                        : (translate('examSchedule.draft') || "Draft")}
                    </Chip>
                  </View>
                </LinearGradient>

                <Card.Content style={styles.actionCardContent}>
                  <ScrollView showsVerticalScrollIndicator={true} style={styles.actionCardScroll}>
                  <View style={styles.examDetailsGrid}>
                    <View style={styles.examDetailItem}>
                      <MaterialCommunityIcons name="account-group" size={20} color={'#1976d2'} />
                      <View style={styles.examDetailItemContent}>
                        <Text style={styles.examDetailItemLabel}>{translate('examSchedule.class') || "Class"}:</Text>
                        <Text style={styles.examDetailItemValue}>{getClassName(actionExam.classId)}</Text>
                      </View>
                    </View>

                    <View style={styles.examDetailItem}>
                      <MaterialCommunityIcons name="tag" size={20} color={'#1976d2'} />
                      <View style={styles.examDetailItemContent}>
                        <Text style={styles.examDetailItemLabel}>{translate('examSchedule.section') || "Section"}:</Text>
                        <Text style={styles.examDetailItemValue}>{actionExam.section ? `Section ${actionExam.section}` : 'Not specified'}</Text>
                      </View>
                    </View>

                    <View style={styles.examDetailItem}>
                      <MaterialCommunityIcons name="calendar" size={20} color={'#1976d2'} />
                      <View style={styles.examDetailItemContent}>
                        <Text style={styles.examDetailItemLabel}>{translate('examSchedule.date') || "Date"}:</Text>
                        <Text style={styles.examDetailItemValue}>{actionExam.date}</Text>
                      </View>
                    </View>

                    <View style={styles.examDetailItem}>
                      <MaterialCommunityIcons name="clock-outline" size={20} color={'#1976d2'} />
                      <View style={styles.examDetailItemContent}>
                        <Text style={styles.examDetailItemLabel}>{translate('examSchedule.time') || "Time"}:</Text>
                        <Text style={styles.examDetailItemValue}>{actionExam.startTime}</Text>
                      </View>
                    </View>

                    <View style={styles.examDetailItem}>
                      <MaterialCommunityIcons name="timer-sand" size={20} color={'#1976d2'} />
                      <View style={styles.examDetailItemContent}>
                        <Text style={styles.examDetailItemLabel}>{translate('examSchedule.duration') || "Duration"}:</Text>
                        <Text style={styles.examDetailItemValue}>{actionExam.duration} minutes</Text>
                      </View>
                    </View>

                    <View style={styles.examDetailItem}>
                      <MaterialCommunityIcons name="door" size={20} color={'#1976d2'} />
                      <View style={styles.examDetailItemContent}>
                        <Text style={styles.examDetailItemLabel}>{translate('examSchedule.room') || "Room"}:</Text>
                        <Text style={styles.examDetailItemValue}>{actionExam.room || 'Not specified'}</Text>
                      </View>
                    </View>

                    <View style={styles.examDetailItem}>
                      <MaterialCommunityIcons name="account-tie" size={20} color={'#1976d2'} />
                      <View style={styles.examDetailItemContent}>
                        <Text style={styles.examDetailItemLabel}>{translate('examSchedule.supervisor') || "Supervisor"}:</Text>
                        <Text style={styles.examDetailItemValue}>
                          {actionExam.supervisor ?
                            teachers.find(t => t.id === actionExam.supervisor)?.displayName || 'Not specified' :
                            'Not specified'}
                        </Text>
                      </View>
                    </View>

                    <View style={styles.examDetailItem}>
                      <MaterialCommunityIcons name="format-list-checks" size={20} color={'#1976d2'} />
                      <View style={styles.examDetailItemContent}>
                        <Text style={styles.examDetailItemLabel}>{translate('examSchedule.examType') || "Exam Type"}:</Text>
                        <Text style={styles.examDetailItemValue}>{actionExam.examType}</Text>
                      </View>
                    </View>

                    {actionExam.published && (
                      <View style={styles.examDetailItem}>
                        <MaterialCommunityIcons name="calendar-check" size={20} color={'#1976d2'} />
                        <View style={styles.examDetailItemContent}>
                          <Text style={styles.examDetailItemLabel}>{translate('examSchedule.published') || "Published"}:</Text>
                          <Text style={styles.examDetailItemValue}>
                            {actionExam.publishedAt ? new Date(actionExam.publishedAt).toLocaleString() : 'Unknown'}
                          </Text>
                        </View>
                      </View>
                    )}
                  </View>
                  </ScrollView>
                </Card.Content>

                <Divider />

                <Card.Actions style={styles.actionCardActions}>
                  <Button
                    mode="outlined"
                    onPress={() => {
                      setSelectedExam(actionExam);
                      setFormData(actionExam);
                      setShowActionModal(false);
                      setVisible(true);
                    }}
                    icon="pencil"
                    style={styles.actionButton}
                  >
                    {translate('common.edit') || "Edit"}
                  </Button>

                  {!actionExam.published && (
                    <Button
                      mode="outlined"
                      onPress={() => {
                        handlePublishExam(actionExam.id);
                      }}
                      icon="send"
                      disabled={publishLoading}
                      loading={publishLoading}
                      style={[styles.actionButton, {backgroundColor: '#E8F5E9'}]}
                    >
                      {translate('examSchedule.publish') || "Publish"}
                    </Button>
                  )}

                  <Button
                    mode="outlined"
                    onPress={() => {
                      handleDeleteExam(actionExam.id);
                    }}
                    icon="delete"
                    style={[styles.actionButton, {backgroundColor: '#FFEBEE'}]}
                  >
                    {translate('common.delete') || "Delete"}
                  </Button>
                </Card.Actions>
              </Card>
            </Animatable.View>
          )}
        </Modal>
      </Portal>

      {/* Floating Action Button */}
      <FAB
        style={styles.fab}
        icon="plus"
        onPress={() => {
          resetForm();
          setVisible(true);
        }}
      />

      {/* Confirmation Dialog */}
      <Portal>
        <Modal
          visible={confirmDialogVisible}
          onDismiss={handleConfirmDialogDismiss}
          contentContainerStyle={styles.confirmDialog}
        >
          <View style={styles.confirmDialogContent}>
            <Title style={styles.confirmDialogTitle}>{confirmDialogTitle}</Title>
            <Text style={styles.confirmDialogMessage}>{confirmDialogMessage}</Text>

            <View style={styles.confirmDialogActions}>
              <Button
                mode="outlined"
                onPress={handleConfirmDialogDismiss}
                style={styles.confirmDialogButton}
              >
                {translate('common.cancel') || 'Cancel'}
              </Button>
              <Button
                mode="contained"
                onPress={handleConfirmDialogConfirm}
                style={[styles.confirmDialogButton, { marginLeft: 8 }]}
              >
                {translate('common.confirm') || 'Confirm'}
              </Button>
            </View>
          </View>
        </Modal>
      </Portal>

      {/* Snackbar for notifications */}
      <Snackbar
        visible={snackbarVisible}
        onDismiss={() => setSnackbarVisible(false)}
        duration={3000}
        action={{
          label: translate('common.dismiss') || 'Dismiss',
          onPress: () => setSnackbarVisible(false),
        }}
        style={styles.snackbar}
      >
        {snackbarMessage}
      </Snackbar>


    </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
    paddingTop: 8,
  },
  card: {
    margin: 16,
    elevation: 4,
    borderRadius: 8,
    overflow: 'hidden',
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  cardActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  cardTitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  countChip: {
    backgroundColor: '#E3F2FD',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    margin: 16,
    marginBottom: 8,
  },
  searchBar: {
    flex: 1,
    elevation: 4,
    borderRadius: 25,
    height: 50,
  },
  filterToggleButton: {
    marginLeft: 8,
    backgroundColor: '#E3F2FD',
  },
  filterContainer: {
    marginHorizontal: 16,
    marginBottom: 8,
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 16,
    elevation: 2,
  },
  filterHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  filterTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  filterLabel: {
    fontSize: 14,
    fontWeight: 'bold',
    marginTop: 8,
    marginBottom: 4,
    color: '#555',
  },
  segmentedButtons: {
    marginBottom: 12,
  },
  publishButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
    marginBottom: 8,
  },
  publishButton: {
    flex: 1,
    marginHorizontal: 4,
  },
  loadingContainer: {
    padding: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  emptyContainer: {
    padding: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyText: {
    marginTop: 16,
    fontSize: 18,
    fontWeight: 'bold',
    color: '#666',
  },
  emptySubtext: {
    marginTop: 8,
    fontSize: 14,
    color: '#888',
    textAlign: 'center',
  },
  tableHeader: {
    backgroundColor: '#f0f0f0',
    borderTopLeftRadius: 4,
    borderTopRightRadius: 4,
  },
  tableHeaderCell: {
    padding: 8,
  },
  tableHeaderText: {
    fontWeight: 'bold',
    color: '#555',
  },
  tableRow: {
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  tableCell: {
    padding: 8,
  },
  tableCellText: {
    fontSize: 14,
  },
  modalContent: {
    backgroundColor: 'white',
    padding: 20,
    margin: 20,
    maxHeight: '90%',
    borderRadius: 12,
    elevation: 5,
  },
  modalScrollContent: {
    paddingBottom: 20,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  modalTitle: {
    fontSize: 22,
    fontWeight: 'bold',
  },
  modalDivider: {
    height: 1,
    marginBottom: 16,
  },
  formInput: {
    marginBottom: 12,
  },
  actionModalContent: {
    margin: 20,
    maxWidth: '95%',
    alignSelf: 'center',
  },
  actionCard: {
    borderRadius: 12,
    overflow: 'hidden',
  },
  actionCardHeader: {
    padding: 16,
  },
  actionCardHeaderContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  actionCardTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  actionCardSubtitle: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  actionCardContent: {
    paddingVertical: 16,
    maxHeight: 400,
  },
  actionCardScroll: {
    flex: 1,
  },
  examDetailsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  examDetailItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    width: '48%',
    marginBottom: 16,
  },
  examDetailItemContent: {
    marginLeft: 8,
    flex: 1,
  },
  examDetailItemLabel: {
    fontSize: 12,
    color: '#666',
    fontWeight: 'bold',
  },
  examDetailItemValue: {
    fontSize: 14,
    color: '#333',
    marginTop: 2,
  },
  examAdditionalInfo: {
    marginTop: 8,
  },
  examAdditionalInfoDivider: {
    marginVertical: 12,
  },
  examAdditionalInfoItem: {
    marginBottom: 12,
  },
  examAdditionalInfoLabel: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#555',
    marginBottom: 4,
  },
  examAdditionalInfoValue: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  actionCardActions: {
    justifyContent: 'flex-end',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  actionButton: {
    marginLeft: 8,
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
  },
  submitButton: {
    marginTop: 16,
    flex: 1,
    marginLeft: 8,
  },
  cancelButton: {
    marginTop: 16,
    flex: 1,
    marginRight: 8,
  },
  formActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
    marginBottom: 8,
  },
  actionButtons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  datePickerContainer: {
    marginVertical: 8,
  },
  dateButton: {
    marginTop: 4,
    width: '100%',
    justifyContent: 'flex-start',
  },
  inputLabel: {
    fontSize: 14,
    marginBottom: 4,
    color: '#666',
  },
  menuContainer: {
    marginVertical: 8,
  },
  selectButton: {
    marginTop: 4,
    width: '100%',
    justifyContent: 'flex-start',
  },
  menuContent: {
    maxHeight: 300,
    width: 250,
  },
  menuScrollView: {
    maxHeight: 300,
  },
  menuScrollContent: {
    paddingVertical: 0,
  },
  publishedRow: {
    backgroundColor: '#F1F8E9',
  },
  unpublishedRow: {
    backgroundColor: '#FFF8E1',
  },
  classSection: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  sectionChip: {
    marginLeft: 4,
    height: 24,
    backgroundColor: '#E3F2FD',
  },
  examDetailRow: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  examDetailLabel: {
    fontWeight: 'bold',
    width: 100,
  },
  examDetailValue: {
    flex: 1,
  },
  cardActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  confirmDialog: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 20,
    margin: 20,
    maxWidth: 500,
    alignSelf: 'center',
  },
  confirmDialogContent: {
    alignItems: 'center',
  },
  confirmDialogTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
    textAlign: 'center',
  },
  confirmDialogMessage: {
    fontSize: 14,
    marginBottom: 20,
    textAlign: 'center',
    color: '#666',
  },
  confirmDialogActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    width: '100%',
    marginTop: 16,
  },
  confirmDialogButton: {
    minWidth: 100,
  },
  snackbar: {
    bottom: 16,
  },
  viewToggle: {
    marginRight: 8,
    height: 36,
  },
  // Calendar view styles
  calendarContainer: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
  },
  selectedDateExams: {
    marginTop: 16,
  },
  selectedDateHeader: {
    marginBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    paddingBottom: 8,
  },
  selectedDateTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#555',
  },
  noExamsOnDate: {
    padding: 16,
    alignItems: 'center',
  },
  noExamsText: {
    color: '#888',
    fontSize: 14,
  },
  examCard: {
    marginBottom: 8,
    borderRadius: 8,
  },
  examCardContent: {
    padding: 12,
  },
  examCardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  examCardTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    flex: 1,
  },
  examCardDetails: {
    marginTop: 8,
  },
  examCardDetail: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  examCardDetailText: {
    fontSize: 12,
    color: '#666',
    marginLeft: 8,
  },
  // Grid view styles
  gridContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    padding: 8,
    paddingBottom: 16,
  },
  gridCard: {
    width: '48%',
    marginBottom: 16,
    borderRadius: 8,
  },
  gridCardContent: {
    padding: 12,
  },
  gridCardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  gridCardBadge: {
    marginRight: 8,
  },
  gridCardTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    flex: 1,
  },
  gridCardDivider: {
    marginVertical: 8,
  },
  gridCardDetails: {
    marginTop: 8,
  },
  gridCardDetail: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  gridCardDetailText: {
    fontSize: 12,
    color: '#666',
    marginLeft: 4,
    flex: 1,
  },
  gridCardFooter: {
    marginTop: 8,
    alignItems: 'flex-start',
  },
});

export default ExamSchedule;
