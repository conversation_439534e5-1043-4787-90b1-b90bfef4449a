import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:provider/provider.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_localizations/flutter_localizations.dart';

import 'config/firebase_config.dart';
import 'context/auth_context.dart';
import 'context/language_context.dart';
import 'context/notification_context.dart';
import 'navigation/app_navigator.dart';
import 'theme/app_theme.dart';
import 'utils/error_handler.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Initialize Firebase
  await Firebase.initializeApp(
    options: FirebaseConfig.currentPlatform,
  );
  
  // Initialize EasyLocalization
  await EasyLocalization.ensureInitialized();
  
  // Set preferred orientations
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);
  
  // Set up error handling
  FlutterError.onError = ErrorHandler.handleFlutterError;
  
  runApp(
    EasyLocalization(
      supportedLocales: const [
        Locale('en', 'US'),
        Locale('am', 'ET'), // Amharic
        Locale('om', 'ET'), // Oromo
        Locale('or', 'ET'), // Oromo alternative
      ],
      path: 'assets/translations',
      fallbackLocale: const Locale('en', 'US'),
      child: const SchoolManagementApp(),
    ),
  );
}

class SchoolManagementApp extends StatelessWidget {
  const SchoolManagementApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AuthContext()),
        ChangeNotifierProvider(create: (_) => LanguageContext()),
        ChangeNotifierProvider(create: (_) => NotificationContext()),
      ],
      child: Consumer<LanguageContext>(
        builder: (context, languageContext, child) {
          return MaterialApp.router(
            title: 'School Management System',
            debugShowCheckedModeBanner: false,
            
            // Localization
            localizationsDelegates: [
              ...context.localizationDelegates,
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            supportedLocales: context.supportedLocales,
            locale: context.locale,
            
            // Theme
            theme: AppTheme.lightTheme,
            darkTheme: AppTheme.darkTheme,
            themeMode: ThemeMode.system,
            
            // Navigation
            routerConfig: AppNavigator.router,
            
            // Error handling
            builder: (context, child) {
              ErrorWidget.builder = ErrorHandler.buildErrorWidget;
              return child ?? const SizedBox.shrink();
            },
          );
        },
      ),
    );
  }
}
