import React, { useEffect } from 'react';
import { db, auth } from '../../config/firebase';
import { collection, query, where, getDocs, doc, getDoc, updateDoc, orderBy, limit } from 'firebase/firestore';
import { useNavigation } from '@react-navigation/native';

/**
 * Component to handle grade-related notifications
 * This component doesn't render anything, it just handles the logic
 */
const GradeNotificationHandler = ({ notification, onHandled }) => {
  const navigation = useNavigation();

  useEffect(() => {
    if (!notification) return;

    handleNotification();
  }, [notification]);

  const handleNotification = async () => {
    try {
      if (!notification) return;

      console.log('Handling grade notification:', notification);

      // Mark notification as read
      if (!notification.read) {
        const notificationRef = doc(db, 'notifications', notification.id);
        await updateDoc(notificationRef, { read: true });
      }

      // Handle different notification types
      switch (notification.type) {
        case 'grade_submission':
          handleGradeSubmissionNotification();
          break;
        case 'grade_published':
          handleGradePublishedNotification();
          break;
        case 'grade_approved':
          handleGradeApprovedNotification();
          break;
        case 'grade_rejected':
          handleGradeRejectedNotification();
          break;
        default:
          console.log('Unknown notification type:', notification.type);
      }

      // Call the onHandled callback
      if (onHandled) {
        onHandled(notification);
      }
    } catch (error) {
      console.error('Error handling notification:', error);
    }
  };

  const handleGradeSubmissionNotification = () => {
    // For admin: Navigate to the grade approval screen
    if (notification.role === 'admin') {
      const { submissionId } = notification.data || {};
      
      if (submissionId) {
        navigation.navigate('AdminGradeApproval', { submissionId });
      } else {
        navigation.navigate('AdminGradeApproval');
      }
    }
  };

  const handleGradePublishedNotification = () => {
    // For student: Navigate to the grades screen
    if (notification.role === 'student') {
      const { classId, subject } = notification.data || {};
      
      if (classId && subject) {
        navigation.navigate('StudentGrades', { classId, subject });
      } else {
        navigation.navigate('StudentGrades');
      }
    }
    
    // For parent: Navigate to the child's grades screen
    if (notification.role === 'parent') {
      const { childId, classId, subject } = notification.data || {};
      
      if (childId && classId && subject) {
        navigation.navigate('ParentChildGrades', { childId, classId, subject });
      } else if (childId) {
        navigation.navigate('ParentChildGrades', { childId });
      } else {
        navigation.navigate('ParentChildGrades');
      }
    }
  };

  const handleGradeApprovedNotification = () => {
    // For teacher: Navigate to the grade management screen
    if (notification.role === 'teacher') {
      const { classId, subject } = notification.data || {};
      
      if (classId && subject) {
        navigation.navigate('TeacherGradeManagement', { classId, subject });
      } else {
        navigation.navigate('TeacherGradeManagement');
      }
    }
  };

  const handleGradeRejectedNotification = () => {
    // For teacher: Navigate to the grade management screen
    if (notification.role === 'teacher') {
      const { classId, subject, reason } = notification.data || {};
      
      if (classId && subject) {
        navigation.navigate('TeacherGradeManagement', { 
          classId, 
          subject,
          showRejectionReason: true,
          rejectionReason: reason || 'No reason provided'
        });
      } else {
        navigation.navigate('TeacherGradeManagement');
      }
    }
  };

  // This component doesn't render anything
  return null;
};

export default GradeNotificationHandler;
