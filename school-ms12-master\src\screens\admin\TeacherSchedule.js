import React, { useState, useEffect } from 'react';
import { View, ScrollView, StyleSheet } from 'react-native';
import { Card, Title, Text, Portal, Modal, ActivityIndicator, DataTable, IconButton } from 'react-native-paper';
import { db } from '../../config/firebase';
import { collection, addDoc, query, getDocs, doc, updateDoc, deleteDoc, where } from 'firebase/firestore';
import CustomButton from '../../components/common/CustomButton';
import CustomInput from '../../components/common/CustomInput';

const TeacherSchedule = ({ route, navigation }) => {
  const [schedule, setSchedule] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedSchedule, setSelectedSchedule] = useState(null);
  const [classes, setClasses] = useState([]);
  const [subjects, setSubjects] = useState([]);
  const [error, setError] = useState(null);

  // Validate route params
  const teacherId = route?.params?.teacherId;

  const [formData, setFormData] = useState({
    day: '',
    startTime: '',
    endTime: '',
    classId: '',
    subjectId: '',
    room: '',
  });

  const days = [
    'Monday',
    'Tuesday',
    'Wednesday',
    'Thursday',
    'Friday',
    'Saturday',
    'Sunday',
  ];

  // Redirect if teacherId is missing
  useEffect(() => {
    if (!teacherId) {
      console.error('TeacherSchedule: teacherId is missing in route params');
      setError('Teacher ID is missing. Please select a teacher first.');
      // Optionally navigate back to teacher list
      // navigation.goBack();
      return;
    }

    fetchSchedule();
    fetchClasses();
    fetchSubjects();
  }, [teacherId]);

  const fetchSchedule = async () => {
    if (!teacherId) return;

    try {
      setLoading(true);
      setError(null);

      const scheduleRef = collection(db, 'schedule');
      const q = query(
        scheduleRef,
        where('teacherId', '==', teacherId)
      );
      const querySnapshot = await getDocs(q);

      const scheduleData = [];
      querySnapshot.forEach((doc) => {
        scheduleData.push({ id: doc.id, ...doc.data() });
      });

      setSchedule(scheduleData);
    } catch (error) {
      console.error('Error fetching schedule:', error);
      setError('Failed to load schedule. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const fetchClasses = async () => {
    try {
      const classesRef = collection(db, 'classes');
      const querySnapshot = await getDocs(classesRef);

      const classesData = [];
      querySnapshot.forEach((doc) => {
        classesData.push({ id: doc.id, ...doc.data() });
      });

      setClasses(classesData);
    } catch (error) {
      console.error('Error fetching classes:', error);
    }
  };

  const fetchSubjects = async () => {
    try {
      const subjectsRef = collection(db, 'subjects');
      const querySnapshot = await getDocs(subjectsRef);

      const subjectsData = [];
      querySnapshot.forEach((doc) => {
        subjectsData.push({ id: doc.id, ...doc.data() });
      });

      setSubjects(subjectsData);
    } catch (error) {
      console.error('Error fetching subjects:', error);
    }
  };

  const handleAddSchedule = async () => {
    if (!teacherId) {
      setError('Cannot add schedule: Teacher ID is missing');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const scheduleRef = collection(db, 'schedule');
      await addDoc(scheduleRef, {
        ...formData,
        teacherId: teacherId,
        createdAt: new Date().toISOString(),
      });

      setModalVisible(false);
      resetForm();
      fetchSchedule();
    } catch (error) {
      console.error('Error adding schedule:', error);
      setError('Failed to add schedule. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateSchedule = async () => {
    try {
      setLoading(true);
      const scheduleRef = doc(db, 'schedule', selectedSchedule.id);
      await updateDoc(scheduleRef, {
        ...formData,
        updatedAt: new Date().toISOString(),
      });

      setModalVisible(false);
      resetForm();
      fetchSchedule();
    } catch (error) {
      console.error('Error updating schedule:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteSchedule = async (scheduleId) => {
    try {
      await deleteDoc(doc(db, 'schedule', scheduleId));
      fetchSchedule();
    } catch (error) {
      console.error('Error deleting schedule:', error);
    }
  };

  const resetForm = () => {
    setSelectedSchedule(null);
    setFormData({
      day: '',
      startTime: '',
      endTime: '',
      classId: '',
      subjectId: '',
      room: '',
    });
  };

  const renderScheduleTable = () => {
    const scheduleByDay = days.map(day => ({
      day,
      classes: schedule.filter(s => s.day === day),
    }));

    return (
      <DataTable>
        <DataTable.Header>
          <DataTable.Title>Day</DataTable.Title>
          <DataTable.Title>Time</DataTable.Title>
          <DataTable.Title>Class</DataTable.Title>
          <DataTable.Title>Subject</DataTable.Title>
          <DataTable.Title>Room</DataTable.Title>
          <DataTable.Title>Actions</DataTable.Title>
        </DataTable.Header>

        {scheduleByDay.map(({ day, classes }) => (
          classes.map((scheduleItem, index) => (
            <DataTable.Row key={scheduleItem.id}>
              {index === 0 && (
                <DataTable.Cell>{day}</DataTable.Cell>
              )}
              {index !== 0 && (
                <DataTable.Cell></DataTable.Cell>
              )}
              <DataTable.Cell>{`${scheduleItem.startTime} - ${scheduleItem.endTime}`}</DataTable.Cell>
              <DataTable.Cell>
                {classes.find(c => c.id === scheduleItem.classId)?.name || 'N/A'}
              </DataTable.Cell>
              <DataTable.Cell>
                {subjects.find(s => s.id === scheduleItem.subjectId)?.name || 'N/A'}
              </DataTable.Cell>
              <DataTable.Cell>{scheduleItem.room}</DataTable.Cell>
              <DataTable.Cell>
                <View style={styles.actionButtons}>
                  <IconButton
                    icon="pencil"
                    size={20}
                    onPress={() => {
                      setSelectedSchedule(scheduleItem);
                      setFormData({
                        day: scheduleItem.day,
                        startTime: scheduleItem.startTime,
                        endTime: scheduleItem.endTime,
                        classId: scheduleItem.classId,
                        subjectId: scheduleItem.subjectId,
                        room: scheduleItem.room,
                      });
                      setModalVisible(true);
                    }}
                  />
                  <IconButton
                    icon="delete"
                    size={20}
                    onPress={() => handleDeleteSchedule(scheduleItem.id)}
                  />
                </View>
              </DataTable.Cell>
            </DataTable.Row>
          ))
        ))}
      </DataTable>
    );
  };

  return (
    <View style={styles.container}>
      <Card style={styles.headerCard}>
        <Card.Content>
          <Title>Teaching Schedule</Title>
          <Text>Manage teacher's weekly schedule</Text>
        </Card.Content>
      </Card>

      <ScrollView>
        {loading ? (
          <ActivityIndicator style={styles.loader} />
        ) : error ? (
          <Card style={styles.errorCard}>
            <Card.Content>
              <Text style={styles.errorText}>{error}</Text>
              {teacherId && (
                <CustomButton
                  mode="contained"
                  onPress={fetchSchedule}
                  style={styles.retryButton}
                >
                  Retry
                </CustomButton>
              )}
            </Card.Content>
          </Card>
        ) : schedule.length === 0 ? (
          <Card style={styles.emptyCard}>
            <Card.Content>
              <Text style={styles.emptyText}>No schedule found for this teacher.</Text>
            </Card.Content>
          </Card>
        ) : (
          renderScheduleTable()
        )}
      </ScrollView>

      <Portal>
        <Modal
          visible={modalVisible}
          onDismiss={() => {
            setModalVisible(false);
            resetForm();
          }}
          contentContainerStyle={styles.modalContent}
        >
          <ScrollView>
            <Title>{selectedSchedule ? 'Edit Schedule' : 'Add Schedule'}</Title>

            <CustomInput
              label="Day"
              value={formData.day}
              onChangeText={(text) => setFormData({ ...formData, day: text })}
            />

            <CustomInput
              label="Start Time"
              value={formData.startTime}
              onChangeText={(text) => setFormData({ ...formData, startTime: text })}
            />

            <CustomInput
              label="End Time"
              value={formData.endTime}
              onChangeText={(text) => setFormData({ ...formData, endTime: text })}
            />

            <CustomInput
              label="Room"
              value={formData.room}
              onChangeText={(text) => setFormData({ ...formData, room: text })}
            />

            <View style={styles.modalButtons}>
              <CustomButton
                mode="contained"
                onPress={selectedSchedule ? handleUpdateSchedule : handleAddSchedule}
                loading={loading}
              >
                {selectedSchedule ? 'Update' : 'Add'}
              </CustomButton>

              <CustomButton
                mode="outlined"
                onPress={() => {
                  setModalVisible(false);
                  resetForm();
                }}
              >
                Cancel
              </CustomButton>
            </View>
          </ScrollView>
        </Modal>
      </Portal>

      <CustomButton
        mode="contained"
        icon="plus"
        onPress={() => setModalVisible(true)}
        style={styles.addButton}
      >
        Add Schedule
      </CustomButton>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  headerCard: {
    margin: 10,
    elevation: 4,
  },
  loader: {
    marginTop: 20,
  },
  modalContent: {
    backgroundColor: 'white',
    padding: 20,
    margin: 20,
    borderRadius: 10,
    maxHeight: '80%',
  },
  modalButtons: {
    marginTop: 20,
  },
  addButton: {
    margin: 16,
  },
  actionButtons: {
    flexDirection: 'row',
  },
  errorCard: {
    margin: 10,
    backgroundColor: '#ffebee',
  },
  errorText: {
    color: '#d32f2f',
    textAlign: 'center',
    marginBottom: 10,
  },
  retryButton: {
    marginTop: 10,
  },
  emptyCard: {
    margin: 10,
    padding: 20,
  },
  emptyText: {
    textAlign: 'center',
    color: '#757575',
  },
});

export default TeacherSchedule;
