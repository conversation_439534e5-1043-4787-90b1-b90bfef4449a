import React, { useState, useEffect } from 'react';
import { View, ScrollView, StyleSheet } from 'react-native';
import { Card, Title, Text, DataTable, Portal, Modal, TextInput, ActivityIndicator, Chip } from 'react-native-paper';
import { db, auth } from '../../config/firebase';
import { collection, query, where, getDocs, addDoc, doc, getDoc } from 'firebase/firestore';
import CustomButton from '../../components/common/CustomButton';
import { Picker } from '@react-native-picker/picker';
import EthiopianCalendar from '../../utils/EthiopianCalendar';

const ResourceRequest = () => {
  const [resources, setResources] = useState([]);
  const [requests, setRequests] = useState([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [loading, setLoading] = useState(true);
  const [requestForm, setRequestForm] = useState({
    resourceId: '',
    quantity: '1',
    startDate: new Date().toISOString(),
    endDate: new Date().toISOString(),
    purpose: '',
    status: 'pending', // pending, approved, rejected
  });

  useEffect(() => {
    fetchResources();
    fetchRequests();
  }, []);

  const fetchResources = async () => {
    try {
      const resourcesRef = collection(db, 'resources');
      const q = query(resourcesRef, where('status', '==', 'available'));
      const querySnapshot = await getDocs(q);
      
      const resourceData = [];
      querySnapshot.forEach((doc) => {
        resourceData.push({ id: doc.id, ...doc.data() });
      });
      
      setResources(resourceData.sort((a, b) => a.name.localeCompare(b.name)));
      setLoading(false);
    } catch (error) {
      console.error('Error fetching resources:', error);
      setLoading(false);
    }
  };

  const fetchRequests = async () => {
    try {
      const requestsRef = collection(db, 'resourceRequests');
      const q = query(requestsRef, where('teacherId', '==', auth.currentUser.uid));
      const querySnapshot = await getDocs(q);
      
      const requestData = [];
      for (const doc of querySnapshot.docs) {
        const request = { id: doc.id, ...doc.data() };
        // Fetch resource details
        const resourceDoc = await getDoc(doc(db, 'resources', request.resourceId));
        request.resource = { id: resourceDoc.id, ...resourceDoc.data() };
        requestData.push(request);
      }
      
      setRequests(requestData.sort((a, b) => 
        new Date(b.requestDate) - new Date(a.requestDate)
      ));
    } catch (error) {
      console.error('Error fetching requests:', error);
    }
  };

  const handleSubmitRequest = async () => {
    try {
      setLoading(true);

      const userDoc = await getDoc(doc(db, 'users', auth.currentUser.uid));
      const teacherName = userDoc.data().displayName;

      const requestData = {
        ...requestForm,
        teacherId: auth.currentUser.uid,
        teacherName,
        requestDate: new Date().toISOString(),
        status: 'pending',
      };

      await addDoc(collection(db, 'resourceRequests'), requestData);
      
      setModalVisible(false);
      resetForm();
      fetchRequests();
    } catch (error) {
      console.error('Error submitting request:', error);
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setRequestForm({
      resourceId: '',
      quantity: '1',
      startDate: new Date().toISOString(),
      endDate: new Date().toISOString(),
      purpose: '',
      status: 'pending',
    });
  };

  const formatDate = (date) => {
    return EthiopianCalendar.formatDate(new Date(date));
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'approved': return '#4CAF50';
      case 'rejected': return '#f44336';
      default: return '#FF9800';
    }
  };

  return (
    <ScrollView style={styles.container}>
      <Card style={styles.card}>
        <Card.Content>
          <View style={styles.headerContainer}>
            <Title>Resource Requests</Title>
            <CustomButton
              mode="contained"
              onPress={() => setModalVisible(true)}
            >
              New Request
            </CustomButton>
          </View>

          {loading ? (
            <ActivityIndicator style={styles.loader} />
          ) : (
            <DataTable>
              <DataTable.Header>
                <DataTable.Title>Resource</DataTable.Title>
                <DataTable.Title numeric>Quantity</DataTable.Title>
                <DataTable.Title>Start Date</DataTable.Title>
                <DataTable.Title>End Date</DataTable.Title>
                <DataTable.Title>Status</DataTable.Title>
              </DataTable.Header>

              {requests.map((request) => (
                <DataTable.Row key={request.id}>
                  <DataTable.Cell>{request.resource.name}</DataTable.Cell>
                  <DataTable.Cell numeric>{request.quantity}</DataTable.Cell>
                  <DataTable.Cell>{formatDate(request.startDate)}</DataTable.Cell>
                  <DataTable.Cell>{formatDate(request.endDate)}</DataTable.Cell>
                  <DataTable.Cell>
                    <Chip
                      style={[
                        styles.statusChip,
                        { backgroundColor: getStatusColor(request.status) }
                      ]}
                    >
                      {request.status}
                    </Chip>
                  </DataTable.Cell>
                </DataTable.Row>
              ))}
            </DataTable>
          )}
        </Card.Content>
      </Card>

      <Portal>
        <Modal
          visible={modalVisible}
          onDismiss={() => {
            setModalVisible(false);
            resetForm();
          }}
          contentContainerStyle={styles.modalContent}
        >
          <ScrollView>
            <Title>New Resource Request</Title>

            <View style={styles.pickerContainer}>
              <Text style={styles.pickerLabel}>Resource</Text>
              <Picker
                selectedValue={requestForm.resourceId}
                onValueChange={(value) => setRequestForm({ ...requestForm, resourceId: value })}
                style={styles.picker}
              >
                <Picker.Item label="Select..." value="" />
                {resources.map((resource) => (
                  <Picker.Item 
                    key={resource.id} 
                    label={resource.name} 
                    value={resource.id} 
                  />
                ))}
              </Picker>
            </View>

            <TextInput
              label="Quantity"
              value={requestForm.quantity}
              onChangeText={(text) => setRequestForm({ ...requestForm, quantity: text })}
              keyboardType="numeric"
              style={styles.input}
            />

            <TextInput
              label="Start Date"
              value={formatDate(requestForm.startDate)}
              onChangeText={(text) => setRequestForm({ ...requestForm, startDate: text })}
              style={styles.input}
            />

            <TextInput
              label="End Date"
              value={formatDate(requestForm.endDate)}
              onChangeText={(text) => setRequestForm({ ...requestForm, endDate: text })}
              style={styles.input}
            />

            <TextInput
              label="Purpose"
              value={requestForm.purpose}
              onChangeText={(text) => setRequestForm({ ...requestForm, purpose: text })}
              multiline
              numberOfLines={3}
              style={styles.input}
            />

            <View style={styles.buttonContainer}>
              <CustomButton
                mode="outlined"
                onPress={() => {
                  setModalVisible(false);
                  resetForm();
                }}
                style={[styles.button, styles.cancelButton]}
              >
                Cancel
              </CustomButton>
              <CustomButton
                mode="contained"
                onPress={handleSubmitRequest}
                style={[styles.button, styles.submitButton]}
                loading={loading}
              >
                Submit Request
              </CustomButton>
            </View>
          </ScrollView>
        </Modal>
      </Portal>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  card: {
    margin: 16,
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  loader: {
    marginVertical: 20,
  },
  modalContent: {
    backgroundColor: 'white',
    padding: 20,
    margin: 20,
    borderRadius: 8,
    maxHeight: '80%',
  },
  input: {
    marginBottom: 16,
  },
  pickerContainer: {
    marginBottom: 16,
  },
  pickerLabel: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
  },
  picker: {
    backgroundColor: '#f5f5f5',
    borderRadius: 4,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: 8,
    marginTop: 16,
  },
  button: {
    minWidth: 100,
  },
  cancelButton: {
    borderColor: '#666',
  },
  submitButton: {
    backgroundColor: '#2196F3',
  },
  statusChip: {
    alignSelf: 'flex-start',
  },
});

export default ResourceRequest;
