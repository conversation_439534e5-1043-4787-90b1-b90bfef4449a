import React, { useState, useEffect } from 'react';
import { View, ScrollView, StyleSheet } from 'react-native';
import { Card, Title, Text, FAB, Portal, Modal, DataTable, Searchbar, List, Chip } from 'react-native-paper';
import { db } from '../../config/firebase';
import { collection, addDoc, query, getDocs, doc, updateDoc, deleteDoc } from 'firebase/firestore';
import { useAuth } from '../../context/AuthContext';
import CustomButton from '../../components/common/CustomButton';
import CustomInput from '../../components/common/CustomInput';
import ScrollableTable from '../../components/common/ScrollableTable';
import { tableStyles } from '../../styles/tableStyles';

const LibraryManagement = () => {
  const { user } = useAuth();
  const [books, setBooks] = useState([]);
  const [categories, setCategories] = useState([]);
  const [visible, setVisible] = useState(false);
  const [selectedBook, setSelectedBook] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState('');

  const [formData, setFormData] = useState({
    title: '',
    author: '',
    isbn: '',
    category: '',
    description: '',
    publisher: '',
    publishYear: '',
    copies: '1',
    availableCopies: '1',
    location: '',
    status: 'available', // available, borrowed, reserved
  });

  useEffect(() => {
    fetchBooks();
    fetchCategories();
  }, []);

  const fetchBooks = async () => {
    try {
      const booksRef = collection(db, 'books');
      const q = query(booksRef);
      const querySnapshot = await getDocs(q);

      const booksData = [];
      querySnapshot.forEach((doc) => {
        booksData.push({ id: doc.id, ...doc.data() });
      });

      setBooks(booksData);
    } catch (error) {
      console.error('Error fetching books:', error);
    }
  };

  const fetchCategories = async () => {
    try {
      const categoriesRef = collection(db, 'bookCategories');
      const querySnapshot = await getDocs(categoriesRef);

      const categoriesData = [];
      querySnapshot.forEach((doc) => {
        categoriesData.push({ id: doc.id, ...doc.data() });
      });

      setCategories(categoriesData);
    } catch (error) {
      console.error('Error fetching categories:', error);
    }
  };

  const handleAddBook = async () => {
    try {
      setLoading(true);
      const booksRef = collection(db, 'books');
      await addDoc(booksRef, {
        ...formData,
        addedBy: user.uid,
        createdAt: new Date().toISOString(),
      });

      setVisible(false);
      resetForm();
      fetchBooks();
    } catch (error) {
      console.error('Error adding book:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateBook = async () => {
    try {
      setLoading(true);
      const bookRef = doc(db, 'books', selectedBook.id);
      await updateDoc(bookRef, {
        ...formData,
        updatedAt: new Date().toISOString(),
      });

      setVisible(false);
      resetForm();
      fetchBooks();
    } catch (error) {
      console.error('Error updating book:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteBook = async (bookId) => {
    try {
      await deleteDoc(doc(db, 'books', bookId));
      fetchBooks();
    } catch (error) {
      console.error('Error deleting book:', error);
    }
  };

  const resetForm = () => {
    setFormData({
      title: '',
      author: '',
      isbn: '',
      category: '',
      description: '',
      publisher: '',
      publishYear: '',
      copies: '1',
      availableCopies: '1',
      location: '',
      status: 'available',
    });
    setSelectedBook(null);
  };

  const filteredBooks = books.filter(book =>
    book.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    book.author.toLowerCase().includes(searchQuery.toLowerCase()) ||
    book.isbn.includes(searchQuery)
  );

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Searchbar
          placeholder="Search books..."
          onChangeText={setSearchQuery}
          value={searchQuery}
          style={styles.searchBar}
        />

        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {categories.map((category) => (
            <Chip
              key={category.id}
              selected={selectedCategory === category.id}
              onPress={() => setSelectedCategory(category.id)}
              style={styles.chip}
            >
              {category.name}
            </Chip>
          ))}
        </ScrollView>
      </View>

      <ScrollableTable
        header={
          <DataTable.Header style={tableStyles.tableHeader}>
            <DataTable.Title style={{ width: 180 }}>Title</DataTable.Title>
            <DataTable.Title style={{ width: 150 }}>Author</DataTable.Title>
            <DataTable.Title style={{ width: 150 }}>Category</DataTable.Title>
            <DataTable.Title style={{ width: 120 }}>Status</DataTable.Title>
            <DataTable.Title style={{ width: 100 }}>Actions</DataTable.Title>
          </DataTable.Header>
        }
      >
        {filteredBooks
          .filter(book => !selectedCategory || book.category === selectedCategory)
          .map((book) => (
            <DataTable.Row key={book.id} style={tableStyles.row}>
              <DataTable.Cell style={{ width: 180 }}>{book.title}</DataTable.Cell>
              <DataTable.Cell style={{ width: 150 }}>{book.author}</DataTable.Cell>
              <DataTable.Cell style={{ width: 150 }}>
                {categories.find(c => c.id === book.category)?.name}
              </DataTable.Cell>
              <DataTable.Cell style={{ width: 120 }}>
                <Chip mode="outlined">{book.status}</Chip>
              </DataTable.Cell>
              <DataTable.Cell style={{ width: 100 }}>
                <View style={styles.actionButtons}>
                  <CustomButton
                    mode="text"
                    onPress={() => {
                      setSelectedBook(book);
                      setFormData({
                        title: book.title,
                        author: book.author,
                        isbn: book.isbn,
                        category: book.category,
                        description: book.description,
                        publisher: book.publisher,
                        publishYear: book.publishYear,
                        copies: book.copies.toString(),
                        availableCopies: book.availableCopies.toString(),
                        location: book.location,
                        status: book.status,
                      });
                      setVisible(true);
                    }}
                  >
                    Edit
                  </CustomButton>
                </View>
              </DataTable.Cell>
            </DataTable.Row>
          ))}
      </ScrollableTable>

      <Portal>
        <Modal
          visible={visible}
          onDismiss={() => {
            setVisible(false);
            resetForm();
          }}
          contentContainerStyle={styles.modalContent}
        >
          <ScrollView>
            <Title>{selectedBook ? 'Edit Book' : 'Add New Book'}</Title>

            <CustomInput
              label="Title"
              value={formData.title}
              onChangeText={(text) => setFormData({ ...formData, title: text })}
            />

            <CustomInput
              label="Author"
              value={formData.author}
              onChangeText={(text) => setFormData({ ...formData, author: text })}
            />

            <CustomInput
              label="ISBN"
              value={formData.isbn}
              onChangeText={(text) => setFormData({ ...formData, isbn: text })}
            />

            <List.Section title="Category">
              {categories.map((category) => (
                <List.Item
                  key={category.id}
                  title={category.name}
                  left={props => <List.Icon {...props} icon="book" />}
                  onPress={() => setFormData({ ...formData, category: category.id })}
                  style={formData.category === category.id ? styles.selectedItem : null}
                />
              ))}
            </List.Section>

            <CustomInput
              label="Description"
              value={formData.description}
              onChangeText={(text) => setFormData({ ...formData, description: text })}
              multiline
              numberOfLines={3}
            />

            <CustomInput
              label="Publisher"
              value={formData.publisher}
              onChangeText={(text) => setFormData({ ...formData, publisher: text })}
            />

            <CustomInput
              label="Publish Year"
              value={formData.publishYear}
              onChangeText={(text) => setFormData({ ...formData, publishYear: text })}
              keyboardType="numeric"
            />

            <CustomInput
              label="Number of Copies"
              value={formData.copies}
              onChangeText={(text) => setFormData({ ...formData, copies: text })}
              keyboardType="numeric"
            />

            <CustomInput
              label="Available Copies"
              value={formData.availableCopies}
              onChangeText={(text) => setFormData({ ...formData, availableCopies: text })}
              keyboardType="numeric"
            />

            <CustomInput
              label="Location"
              value={formData.location}
              onChangeText={(text) => setFormData({ ...formData, location: text })}
            />

            <List.Section title="Status">
              {['available', 'borrowed', 'reserved'].map((status) => (
                <List.Item
                  key={status}
                  title={status.charAt(0).toUpperCase() + status.slice(1)}
                  left={props => <List.Icon {...props} icon="book-open" />}
                  onPress={() => setFormData({ ...formData, status })}
                  style={formData.status === status ? styles.selectedItem : null}
                />
              ))}
            </List.Section>

            <View style={styles.modalButtons}>
              <CustomButton
                mode="contained"
                onPress={selectedBook ? handleUpdateBook : handleAddBook}
                loading={loading}
              >
                {selectedBook ? 'Update' : 'Add'}
              </CustomButton>

              {selectedBook && (
                <CustomButton
                  mode="outlined"
                  onPress={() => handleDeleteBook(selectedBook.id)}
                  style={styles.deleteButton}
                >
                  Delete
                </CustomButton>
              )}

              <CustomButton
                mode="outlined"
                onPress={() => {
                  setVisible(false);
                  resetForm();
                }}
              >
                Cancel
              </CustomButton>
            </View>
          </ScrollView>
        </Modal>
      </Portal>

      <FAB
        style={styles.fab}
        icon="plus"
        onPress={() => setVisible(true)}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#ffffff',
    padding: 10,
    elevation: 2,
  },
  searchBar: {
    marginBottom: 10,
    elevation: 0,
  },
  modalContent: {
    backgroundColor: '#ffffff',
    padding: 20,
    margin: 20,
    borderRadius: 10,
    maxHeight: '80%',
  },
  selectedItem: {
    backgroundColor: '#e8f4f8',
  },
  actionButtons: {
    flexDirection: 'row',
  },
  chip: {
    margin: 4,
  },
  modalButtons: {
    marginTop: 20,
  },
  deleteButton: {
    marginVertical: 10,
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
  },
});

export default LibraryManagement;
