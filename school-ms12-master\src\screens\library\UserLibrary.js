import React, { useState, useEffect } from 'react';
import { View, ScrollView, StyleSheet, Image } from 'react-native';
import { Card, Title, Text, Searchbar, Chip, Portal, Modal, ActivityIndicator } from 'react-native-paper';
import { db } from '../../config/firebase';
import { collection, query, getDocs, where, orderBy } from 'firebase/firestore';
import { useAuth } from '../../context/AuthContext';
import CustomButton from '../../components/common/CustomButton';

const UserLibrary = () => {
  const { user } = useAuth();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilters, setSelectedFilters] = useState([]);
  const [books, setBooks] = useState([]);
  const [categories, setCategories] = useState([]);
  const [myBorrowings, setMyBorrowings] = useState([]);
  const [selectedBook, setSelectedBook] = useState(null);
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    fetchBooks();
    fetchCategories();
    fetchMyBorrowings();
  }, []);

  const fetchBooks = async () => {
    try {
      const booksRef = collection(db, 'books');
      const q = query(booksRef, where('availableQuantity', '>', 0));
      const querySnapshot = await getDocs(q);
      
      const booksData = [];
      querySnapshot.forEach((doc) => {
        booksData.push({ id: doc.id, ...doc.data() });
      });
      
      setBooks(booksData);
    } catch (error) {
      console.error('Error fetching books:', error);
    }
  };

  const fetchCategories = async () => {
    try {
      const categoriesRef = collection(db, 'bookCategories');
      const querySnapshot = await getDocs(categoriesRef);
      
      const categoriesData = [];
      querySnapshot.forEach((doc) => {
        categoriesData.push({ id: doc.id, ...doc.data() });
      });
      
      setCategories(categoriesData);
    } catch (error) {
      console.error('Error fetching categories:', error);
    }
  };

  const fetchMyBorrowings = async () => {
    try {
      const borrowingsRef = collection(db, 'bookBorrowings');
      const q = query(
        borrowingsRef,
        where('userId', '==', user.uid),
        where('status', '==', 'borrowed'),
        orderBy('borrowDate', 'desc')
      );
      
      const querySnapshot = await getDocs(q);
      const borrowingsData = [];
      querySnapshot.forEach((doc) => {
        borrowingsData.push({ id: doc.id, ...doc.data() });
      });
      
      // Fetch book details for each borrowing
      const bookDetails = await Promise.all(
        borrowingsData.map(async (borrowing) => {
          const bookDoc = await getDoc(doc(db, 'books', borrowing.bookId));
          return {
            ...borrowing,
            book: { id: bookDoc.id, ...bookDoc.data() },
          };
        })
      );
      
      setMyBorrowings(bookDetails);
    } catch (error) {
      console.error('Error fetching borrowings:', error);
    }
  };

  const handleSearch = (query) => {
    setSearchQuery(query);
  };

  const toggleFilter = (category) => {
    if (selectedFilters.includes(category)) {
      setSelectedFilters(selectedFilters.filter(f => f !== category));
    } else {
      setSelectedFilters([...selectedFilters, category]);
    }
  };

  const filteredBooks = books.filter(book => {
    const matchesSearch = book.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      book.author.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesFilters = selectedFilters.length === 0 ||
      selectedFilters.includes(book.category);
    
    return matchesSearch && matchesFilters;
  });

  const renderBookCard = (book) => (
    <Card
      key={book.id}
      style={styles.bookCard}
      onPress={() => {
        setSelectedBook(book);
        setVisible(true);
      }}
    >
      <Card.Content style={styles.bookCardContent}>
        {book.coverImage ? (
          <Image
            source={{ uri: book.coverImage }}
            style={styles.bookCover}
          />
        ) : (
          <View style={[styles.bookCover, styles.placeholderCover]}>
            <Text>No Cover</Text>
          </View>
        )}
        
        <View style={styles.bookInfo}>
          <Title>{book.title}</Title>
          <Text>By {book.author}</Text>
          <Text>Available: {book.availableQuantity}</Text>
          
          <View style={styles.categoryChips}>
            <Chip>{categories.find(c => c.id === book.category)?.name || 'Uncategorized'}</Chip>
          </View>
        </View>
      </Card.Content>
    </Card>
  );

  const BookDetailsModal = () => (
    <Portal>
      <Modal
        visible={visible}
        onDismiss={() => {
          setVisible(false);
          setSelectedBook(null);
        }}
        contentContainerStyle={styles.modalContent}
      >
        {selectedBook && (
          <ScrollView>
            {selectedBook.coverImage && (
              <Image
                source={{ uri: selectedBook.coverImage }}
                style={styles.modalCover}
              />
            )}

            <Title>{selectedBook.title}</Title>
            <Text style={styles.modalAuthor}>By {selectedBook.author}</Text>
            
            <View style={styles.modalDetails}>
              <Text>ISBN: {selectedBook.isbn}</Text>
              <Text>Publisher: {selectedBook.publisher}</Text>
              <Text>Edition: {selectedBook.edition}</Text>
              <Text>Available Copies: {selectedBook.availableQuantity}</Text>
              <Text>Location: {selectedBook.location}</Text>
            </View>

            <Text style={styles.modalDescription}>{selectedBook.description}</Text>

            <CustomButton
              mode="contained"
              onPress={() => {
                // Navigate to book request screen
                navigation.navigate('RequestBook', { bookId: selectedBook.id });
              }}
              style={styles.requestButton}
            >
              Request Book
            </CustomButton>
          </ScrollView>
        )}
      </Modal>
    </Portal>
  );

  return (
    <View style={styles.container}>
      <Searchbar
        placeholder="Search books..."
        onChangeText={handleSearch}
        value={searchQuery}
        style={styles.searchBar}
      />

      <ScrollView horizontal style={styles.filterContainer}>
        {categories.map((category) => (
          <Chip
            key={category.id}
            selected={selectedFilters.includes(category.id)}
            onPress={() => toggleFilter(category.id)}
            style={styles.filterChip}
          >
            {category.name}
          </Chip>
        ))}
      </ScrollView>

      {myBorrowings.length > 0 && (
        <Card style={styles.borrowingsCard}>
          <Card.Content>
            <Title>My Borrowed Books</Title>
            <ScrollView horizontal>
              {myBorrowings.map((borrowing) => (
                <Card
                  key={borrowing.id}
                  style={styles.borrowedBookCard}
                  onPress={() => {
                    setSelectedBook(borrowing.book);
                    setVisible(true);
                  }}
                >
                  <Card.Content>
                    <Title style={styles.borrowedBookTitle}>
                      {borrowing.book.title}
                    </Title>
                    <Text>Due: {new Date(borrowing.dueDate).toLocaleDateString()}</Text>
                  </Card.Content>
                </Card>
              ))}
            </ScrollView>
          </Card.Content>
        </Card>
      )}

      <ScrollView style={styles.booksContainer}>
        {loading ? (
          <ActivityIndicator size="large" style={styles.loader} />
        ) : (
          filteredBooks.map(renderBookCard)
        )}
      </ScrollView>

      <BookDetailsModal />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  searchBar: {
    margin: 10,
    elevation: 2,
  },
  filterContainer: {
    padding: 10,
  },
  filterChip: {
    marginRight: 8,
  },
  borrowingsCard: {
    margin: 10,
  },
  borrowedBookCard: {
    width: 200,
    marginRight: 10,
  },
  borrowedBookTitle: {
    fontSize: 16,
  },
  booksContainer: {
    padding: 10,
  },
  bookCard: {
    marginBottom: 10,
  },
  bookCardContent: {
    flexDirection: 'row',
  },
  bookCover: {
    width: 100,
    height: 150,
    marginRight: 15,
  },
  placeholderCover: {
    backgroundColor: '#e1e1e1',
    justifyContent: 'center',
    alignItems: 'center',
  },
  bookInfo: {
    flex: 1,
  },
  categoryChips: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 5,
  },
  modalContent: {
    backgroundColor: 'white',
    padding: 20,
    margin: 20,
    borderRadius: 10,
    maxHeight: '80%',
  },
  modalCover: {
    width: '100%',
    height: 300,
    marginBottom: 15,
    borderRadius: 8,
  },
  modalAuthor: {
    fontSize: 16,
    color: '#666',
    marginBottom: 15,
  },
  modalDetails: {
    backgroundColor: '#f5f5f5',
    padding: 15,
    borderRadius: 8,
    marginVertical: 15,
  },
  modalDescription: {
    marginVertical: 15,
    lineHeight: 24,
  },
  requestButton: {
    marginTop: 20,
  },
  loader: {
    marginTop: 20,
  },
});

export default UserLibrary;
