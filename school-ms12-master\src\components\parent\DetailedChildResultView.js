import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import { Card, Title, Text, DataTable, ActivityIndicator, Button, Chip, Menu } from 'react-native-paper';
import { db, auth } from '../../config/firebase';
import { collection, query, where, getDocs, doc, getDoc } from 'firebase/firestore';
import { useLanguage } from '../../context/LanguageContext';
import ScrollableTable from '../common/ScrollableTable';

const DetailedChildResultView = () => {
  const { translate } = useLanguage();
  const [loading, setLoading] = useState(true);
  const [childrenLoading, setChildrenLoading] = useState(true);
  const [resultsLoading, setResultsLoading] = useState(false);
  const [children, setChildren] = useState([]);
  const [selectedChild, setSelectedChild] = useState(null);
  const [results, setResults] = useState([]);
  const [error, setError] = useState(null);
  const [childMenuVisible, setChildMenuVisible] = useState(false);

  useEffect(() => {
    fetchChildren();
  }, []);

  useEffect(() => {
    if (selectedChild) {
      fetchChildResults(selectedChild.id);
    }
  }, [selectedChild]);

  const fetchChildren = async () => {
    try {
      setChildrenLoading(true);
      setError(null);

      // Get parent data to check for children array
      const parentId = auth.currentUser.uid;
      const parentRef = doc(db, 'users', parentId);
      const parentDoc = await getDoc(parentRef);

      if (!parentDoc.exists()) {
        setError('Parent data not found');
        setChildren([]);
        setChildrenLoading(false);
        return;
      }

      const parentData = parentDoc.data();
      const childrenIds = parentData.children || [];

      if (childrenIds.length === 0) {
        setError('No children found for this account');
        setChildren([]);
        setChildrenLoading(false);
        return;
      }

      // Fetch each child's data
      const childrenData = [];

      for (const childId of childrenIds) {
        try {
          const childRef = doc(db, 'users', childId);
          const childDoc = await getDoc(childRef);

          if (childDoc.exists()) {
            const childData = { id: childId, ...childDoc.data() };

            // Ensure child has a name property for display
            if (!childData.displayName) {
              childData.displayName = `${childData.firstName || ''} ${childData.lastName || ''}`.trim() || 'Unknown';
            }

            childrenData.push(childData);
          }
        } catch (childError) {
          console.error(`Error fetching child ${childId}:`, childError);
        }
      }

      setChildren(childrenData);

      // Select the first child by default
      if (childrenData.length > 0) {
        setSelectedChild(childrenData[0]);
      }
    } catch (error) {
      console.error('Error fetching children:', error);
      setError('Failed to load children data');
    } finally {
      setChildrenLoading(false);
      setLoading(false);
    }
  };

  const fetchChildResults = async (childId) => {
    try {
      setResultsLoading(true);
      setError(null);

      // Query the results collection for approved results for this child
      const resultsRef = collection(db, 'results');
      const q = query(
        resultsRef,
        where('studentId', '==', childId),
        where('status', '==', 'approved')
      );

      const snapshot = await getDocs(q);

      if (snapshot.empty) {
        setResults([]);
        setResultsLoading(false);
        return;
      }

      const resultData = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      // Sort by creation date (newest first)
      resultData.sort((a, b) => {
        const dateA = a.createdAt?.toDate?.() || new Date(0);
        const dateB = b.createdAt?.toDate?.() || new Date(0);
        return dateB - dateA;
      });

      setResults(resultData);
    } catch (error) {
      console.error('Error fetching results:', error);
      setError('Failed to load results');
    } finally {
      setResultsLoading(false);
    }
  };

  const getGradeColor = (percentage) => {
    if (percentage >= 90) return '#4CAF50'; // A - Green
    if (percentage >= 80) return '#8BC34A'; // B - Light Green
    if (percentage >= 70) return '#CDDC39'; // C - Lime
    if (percentage >= 60) return '#FFC107'; // D - Amber
    return '#F44336'; // F - Red
  };

  const getGradeLetter = (percentage) => {
    if (percentage >= 90) return 'A';
    if (percentage >= 80) return 'B';
    if (percentage >= 70) return 'C';
    if (percentage >= 60) return 'D';
    return 'F';
  };

  if (loading || childrenLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#1976d2" />
        <Text style={styles.loadingText}>{translate('parent.childGrades.loadingChildren') || 'Loading children data...'}</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>{error}</Text>
        <Button mode="contained" onPress={fetchChildren}>
          {translate('common.retry') || 'Retry'}
        </Button>
      </View>
    );
  }

  if (children.length === 0) {
    return (
      <Card style={styles.emptyCard}>
        <Card.Content>
          <Text style={styles.emptyText}>{translate('parent.childGrades.noChildrenFound') || 'No children found for this account.'}</Text>
        </Card.Content>
      </Card>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <Card style={styles.childSelectorCard}>
        <Card.Content>
          <View style={styles.childSelectorRow}>
            <Text style={styles.childSelectorLabel}>{translate('parent.childGrades.selectChild') || 'Select Child'}:</Text>
            <Menu
              visible={childMenuVisible}
              onDismiss={() => setChildMenuVisible(false)}
              anchor={
                <Button
                  mode="outlined"
                  onPress={() => setChildMenuVisible(true)}
                  style={styles.childSelectorButton}
                >
                  {selectedChild?.displayName || selectedChild?.firstName + ' ' + selectedChild?.lastName}
                </Button>
              }
            >
              {children.map(child => (
                <Menu.Item
                  key={child.id}
                  onPress={() => {
                    setSelectedChild(child);
                    setChildMenuVisible(false);
                  }}
                  title={child.displayName || child.firstName + ' ' + child.lastName}
                />
              ))}
            </Menu>
          </View>
        </Card.Content>
      </Card>

      {resultsLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#1976d2" />
          <Text style={styles.loadingText}>{translate('parent.childGrades.loadingResults') || 'Loading results...'}</Text>
        </View>
      ) : results.length === 0 ? (
        <Card style={styles.emptyCard}>
          <Card.Content>
            <Text style={styles.emptyText}>{translate('parent.childGrades.noApprovedResults') || 'No approved results available for this child yet.'}</Text>
          </Card.Content>
        </Card>
      ) : (
        <>
          {/* Use the most recent result for detailed view */}
          {(() => {
            const latestResult = results[0];
            const subjectScores = latestResult.subjectScores || {};
            const subjects = Object.keys(subjectScores);

            return (
              <>
                <Card style={styles.studentInfoCard}>
                  <Card.Content>
                    <Title style={styles.cardTitle}>{translate('parent.childGrades.studentInfo') || 'Student Information'}</Title>
                    <View style={styles.infoRow}>
                      <View style={styles.infoItem}>
                        <Text style={styles.infoLabel}>{translate('common.name') || 'Name'}:</Text>
                        <Text style={styles.infoValue}>{selectedChild?.displayName || latestResult.studentName}</Text>
                      </View>
                      <View style={styles.infoItem}>
                        <Text style={styles.infoLabel}>{translate('common.rollNumber') || 'Roll Number'}:</Text>
                        <Text style={styles.infoValue}>{selectedChild?.rollNumber || latestResult.rollNumber}</Text>
                      </View>
                    </View>
                    <View style={styles.infoRow}>
                      <View style={styles.infoItem}>
                        <Text style={styles.infoLabel}>{translate('common.class') || 'Class'}:</Text>
                        <Text style={styles.infoValue}>{latestResult.className}</Text>
                      </View>
                      <View style={styles.infoItem}>
                        <Text style={styles.infoLabel}>{translate('common.section') || 'Section'}:</Text>
                        <Text style={styles.infoValue}>{latestResult.sectionName}</Text>
                      </View>
                    </View>
                  </Card.Content>
                </Card>

                <Card style={styles.summaryCard}>
                  <Card.Content>
                    <Title style={styles.cardTitle}>{translate('parent.childGrades.resultSummary') || 'Result Summary'}</Title>
                    <View style={styles.summaryRow}>
                      <View style={styles.summaryItem}>
                        <Text style={styles.summaryLabel}>{translate('parent.childGrades.totalScore') || 'Total Score'}:</Text>
                        <Text style={styles.summaryValue}>
                          {latestResult.totalPoints} / {latestResult.totalMaxPoints}
                        </Text>
                      </View>
                      <View style={styles.summaryItem}>
                        <Text style={styles.summaryLabel}>{translate('parent.childGrades.percentage') || 'Percentage'}:</Text>
                        <Text style={[styles.summaryValue, { color: getGradeColor(latestResult.totalPercentage) }]}>
                          {latestResult.totalPercentage?.toFixed(2)}%
                        </Text>
                      </View>
                      <View style={styles.summaryItem}>
                        <Text style={styles.summaryLabel}>{translate('common.grade') || 'Grade'}:</Text>
                        <Chip style={[styles.gradeChip, { backgroundColor: getGradeColor(latestResult.totalPercentage) }]}>
                          {getGradeLetter(latestResult.totalPercentage)}
                        </Chip>
                      </View>
                      <View style={styles.summaryItem}>
                        <Text style={styles.summaryLabel}>{translate('parent.childGrades.rank') || 'Rank'}:</Text>
                        <Text style={styles.summaryValue}>#{latestResult.rank || 'N/A'}</Text>
                      </View>
                    </View>
                  </Card.Content>
                </Card>

                <Card style={styles.tableCard}>
                  <Card.Content>
                    <Title style={styles.cardTitle}>{translate('parent.childGrades.subjectResults') || 'Subject-wise Results'}</Title>

                    <ScrollableTable
                      header={
                        <DataTable.Header style={styles.tableHeader}>
                          <DataTable.Title style={styles.subjectColumn}>{translate('common.subject') || 'Subject'}</DataTable.Title>
                          <DataTable.Title numeric style={styles.scoreColumn}>{translate('common.score') || 'Score'}</DataTable.Title>
                          <DataTable.Title numeric style={styles.maxScoreColumn}>{translate('common.maxScore') || 'Max Score'}</DataTable.Title>
                          <DataTable.Title numeric style={styles.percentageColumn}>{translate('common.percentage') || 'Percentage'}</DataTable.Title>
                          <DataTable.Title style={styles.gradeColumn}>{translate('common.grade') || 'Grade'}</DataTable.Title>
                          <DataTable.Title style={styles.teacherColumn}>{translate('common.teacher') || 'Teacher'}</DataTable.Title>
                        </DataTable.Header>
                      }
                      maxHeight={300}
                    >
                      {subjects.map((subject, index) => {
                        const subjectData = subjectScores[subject];
                        const score = subjectData.totalScore || 0;
                        const maxScore = 100; // Assuming max score is 100
                        const percentage = (score / maxScore) * 100;

                        return (
                          <DataTable.Row key={index} style={index % 2 === 0 ? styles.evenRow : styles.oddRow}>
                            <DataTable.Cell style={styles.subjectColumn}>{subject}</DataTable.Cell>
                            <DataTable.Cell numeric style={styles.scoreColumn}>{score.toFixed(2)}</DataTable.Cell>
                            <DataTable.Cell numeric style={styles.maxScoreColumn}>{maxScore}</DataTable.Cell>
                            <DataTable.Cell numeric style={styles.percentageColumn}>
                              <Text style={{ color: getGradeColor(percentage) }}>
                                {percentage.toFixed(2)}%
                              </Text>
                            </DataTable.Cell>
                            <DataTable.Cell style={styles.gradeColumn}>
                              <Chip
                                style={[styles.gradeChip, { backgroundColor: getGradeColor(percentage) }]}
                                textStyle={styles.gradeChipText}
                              >
                                {getGradeLetter(percentage)}
                              </Chip>
                            </DataTable.Cell>
                            <DataTable.Cell style={styles.teacherColumn}>{subjectData.teacherName || 'N/A'}</DataTable.Cell>
                          </DataTable.Row>
                        );
                      })}
                    </ScrollableTable>
                  </Card.Content>
                </Card>

                {/* Assessment Details Section */}
                <Card style={styles.tableCard}>
                  <Card.Content>
                    <Title style={styles.cardTitle}>{translate('parent.childGrades.assessmentDetails') || 'Assessment Details'}</Title>

                    {subjects.map((subject, subjectIndex) => {
                      const subjectData = subjectScores[subject];
                      const assessments = subjectData.assessmentScores || [];

                      if (assessments.length === 0) return null;

                      return (
                        <View key={subjectIndex} style={styles.assessmentSection}>
                          <Title style={styles.subjectTitle}>{subject}</Title>

                          <ScrollableTable
                            header={
                              <DataTable.Header style={styles.tableHeader}>
                                <DataTable.Title style={styles.assessmentColumn}>{translate('common.assessment') || 'Assessment'}</DataTable.Title>
                                <DataTable.Title style={styles.typeColumn}>{translate('common.type') || 'Type'}</DataTable.Title>
                                <DataTable.Title numeric style={styles.scoreColumn}>{translate('common.score') || 'Score'}</DataTable.Title>
                                <DataTable.Title numeric style={styles.maxScoreColumn}>{translate('common.maxScore') || 'Max Score'}</DataTable.Title>
                                <DataTable.Title numeric style={styles.weightColumn}>{translate('common.weight') || 'Weight'}</DataTable.Title>
                                <DataTable.Title numeric style={styles.percentageColumn}>{translate('common.percentage') || 'Percentage'}</DataTable.Title>
                              </DataTable.Header>
                            }
                            maxHeight={200}
                          >
                            {assessments.map((assessment, index) => {
                              const score = assessment.score || 0;
                              const maxScore = assessment.maxPoints || 100;
                              const percentage = (score / maxScore) * 100;

                              return (
                                <DataTable.Row key={index} style={index % 2 === 0 ? styles.evenRow : styles.oddRow}>
                                  <DataTable.Cell style={styles.assessmentColumn}>{assessment.title}</DataTable.Cell>
                                  <DataTable.Cell style={styles.typeColumn}>
                                    <Chip size="small" style={styles.typeChip}>
                                      {assessment.type}
                                    </Chip>
                                  </DataTable.Cell>
                                  <DataTable.Cell numeric style={styles.scoreColumn}>{score}</DataTable.Cell>
                                  <DataTable.Cell numeric style={styles.maxScoreColumn}>{maxScore}</DataTable.Cell>
                                  <DataTable.Cell numeric style={styles.weightColumn}>{assessment.weight || 1}</DataTable.Cell>
                                  <DataTable.Cell numeric style={styles.percentageColumn}>
                                    <Text style={{ color: getGradeColor(percentage) }}>
                                      {percentage.toFixed(2)}%
                                    </Text>
                                  </DataTable.Cell>
                                </DataTable.Row>
                              );
                            })}
                          </ScrollableTable>
                        </View>
                      );
                    })}
                  </Card.Content>
                </Card>

                <View style={styles.footer}>
                  <Text style={styles.footerText}>
                    {translate('parent.childGrades.resultsApprovedOn') || 'Results approved on'}: {latestResult.approvedAt?.toDate?.().toLocaleDateString() || 'N/A'}
                  </Text>
                </View>
              </>
            );
          })()}
        </>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    minHeight: 200,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    marginBottom: 20,
    fontSize: 16,
    color: '#F44336',
    textAlign: 'center',
  },
  emptyCard: {
    margin: 16,
    padding: 8,
  },
  emptyText: {
    textAlign: 'center',
    fontSize: 16,
  },
  childSelectorCard: {
    marginBottom: 16,
    elevation: 2,
  },
  childSelectorRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  childSelectorLabel: {
    fontSize: 16,
    fontWeight: '500',
  },
  childSelectorButton: {
    minWidth: 150,
  },
  studentInfoCard: {
    marginBottom: 16,
    elevation: 2,
  },
  summaryCard: {
    marginBottom: 16,
    elevation: 2,
  },
  tableCard: {
    marginBottom: 16,
    elevation: 2,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  infoItem: {
    flex: 1,
  },
  infoLabel: {
    fontSize: 14,
    color: '#666',
  },
  infoValue: {
    fontSize: 16,
    fontWeight: '500',
  },
  summaryRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  summaryItem: {
    width: '48%',
    marginBottom: 12,
  },
  summaryLabel: {
    fontSize: 14,
    color: '#666',
  },
  summaryValue: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  gradeChip: {
    alignSelf: 'flex-start',
    height: 28,
  },
  gradeChipText: {
    color: 'white',
    fontWeight: 'bold',
  },
  tableHeader: {
    backgroundColor: '#f5f5f5',
  },
  evenRow: {
    backgroundColor: '#ffffff',
  },
  oddRow: {
    backgroundColor: '#f9f9f9',
  },
  subjectColumn: {
    flex: 3,
  },
  scoreColumn: {
    flex: 1,
  },
  maxScoreColumn: {
    flex: 1,
  },
  percentageColumn: {
    flex: 1.5,
  },
  gradeColumn: {
    flex: 1,
  },
  teacherColumn: {
    flex: 2,
  },
  assessmentSection: {
    marginBottom: 20,
  },
  subjectTitle: {
    fontSize: 16,
    marginBottom: 8,
  },
  assessmentColumn: {
    flex: 3,
  },
  typeColumn: {
    flex: 1.5,
  },
  weightColumn: {
    flex: 1,
  },
  typeChip: {
    backgroundColor: '#E0E0E0',
  },
  footer: {
    marginBottom: 24,
    alignItems: 'center',
  },
  footerText: {
    fontSize: 12,
    color: '#666',
    fontStyle: 'italic',
  },
});

export default DetailedChildResultView;
