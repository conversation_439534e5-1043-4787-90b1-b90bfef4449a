import React, { useState, useEffect } from 'react';
import { View, StyleSheet } from 'react-native';
import { Appbar, Badge, Menu, useTheme } from 'react-native-paper';
import { useNavigation } from '@react-navigation/native';
import { db } from '../../config/firebase';
import { collection, query, where, onSnapshot } from 'firebase/firestore';
import { useAuth } from '../../context/AuthContext';

const AppHeader = ({ title, showBack = false, showMenu = true }) => {
  const navigation = useNavigation();
  // No theme needed
  const { user } = useAuth();
  const [unreadCount, setUnreadCount] = useState(0);
  const [menuVisible, setMenuVisible] = useState(false);

  useEffect(() => {
    if (user) {
      // Subscribe to unread messages count
      const chatsRef = collection(db, 'chats');
      const q = query(
        chatsRef,
        where('participants', 'array-contains', user.uid)
      );

      const unsubscribe = onSnapshot(q, (snapshot) => {
        let count = 0;
        snapshot.forEach((doc) => {
          const chat = doc.data();
          count += chat.unreadCount?.[user.uid] || 0;
        });
        setUnreadCount(count);
      });

      return () => unsubscribe();
    }
  }, [user]);

  return (
    <Appbar.Header style={styles.header}>
      {showBack && (
        <Appbar.BackAction onPress={() => navigation.goBack()} />
      )}

      <Appbar.Content title={title} />

      <View style={styles.actions}>
        <Appbar.Action
          icon="message"
          onPress={() => navigation.navigate('ChatList')}
        />
        {unreadCount > 0 && (
          <Badge
            size={20}
            style={[styles.badge, { backgroundColor: '#f50057' }]}
          >
            {unreadCount}
          </Badge>
        )}

        {showMenu && (
          <Menu
            visible={menuVisible}
            onDismiss={() => setMenuVisible(false)}
            anchor={
              <Appbar.Action
                icon="dots-vertical"
                onPress={() => setMenuVisible(true)}
              />
            }
          >
            <Menu.Item
              onPress={() => {
                setMenuVisible(false);
                navigation.navigate('Profile');
              }}
              title="Profile"
            />
            <Menu.Item
              onPress={() => {
                setMenuVisible(false);
                navigation.navigate('Settings');
              }}
              title="Settings"
            />
          </Menu>
        )}
      </View>
    </Appbar.Header>
  );
};

const styles = StyleSheet.create({
  header: {
    elevation: 4,
  },
  actions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  badge: {
    position: 'absolute',
    top: 5,
    right: 5,
  },
});

export default AppHeader;
