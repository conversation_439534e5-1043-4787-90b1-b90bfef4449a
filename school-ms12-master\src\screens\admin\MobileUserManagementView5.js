// Styles
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: mobileTheme.colors.background,
  },
  searchContainer: {
    padding: 16,
    backgroundColor: mobileTheme.colors.surface,
    elevation: 2,
  },
  searchBar: {
    elevation: 0,
    borderRadius: 8,
  },
  statsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    padding: 8,
  },
  statCardContainer: {
    width: '48%',
    marginBottom: 16,
  },
  statCard: {
    padding: 16,
    borderRadius: 8,
    elevation: 2,
    alignItems: 'center',
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    marginVertical: 8,
  },
  statLabel: {
    fontSize: 12,
    color: mobileTheme.colors.placeholder,
  },
  userCard: {
    marginHorizontal: 16,
    marginVertical: 8,
    borderRadius: 8,
    elevation: 2,
  },
  userCardContent: {
    padding: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  avatar: {
    marginRight: 16,
  },
  userDetails: {
    flex: 1,
  },
  userName: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  userEmail: {
    fontSize: 14,
    color: mobileTheme.colors.placeholder,
    marginBottom: 8,
  },
  userChips: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  roleChip: {
    marginRight: 8,
    marginBottom: 4,
  },
  statusChip: {
    marginBottom: 4,
  },
  noResultsContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
    marginTop: 32,
  },
  noResultsText: {
    marginTop: 16,
    marginBottom: 24,
    fontSize: 16,
    textAlign: 'center',
  },
  addUserButton: {
    marginTop: 16,
  },
  clearSearchButton: {
    marginTop: 16,
  },
  // SwipeListView styles
  rowBack: {
    alignItems: 'center',
    backgroundColor: mobileTheme.colors.background,
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingLeft: 15,
    marginHorizontal: 16,
    marginVertical: 8,
    borderRadius: 8,
  },
  backLeftBtn: {
    alignItems: 'center',
    bottom: 0,
    justifyContent: 'center',
    position: 'absolute',
    top: 0,
    width: 75,
    borderTopLeftRadius: 8,
    borderBottomLeftRadius: 8,
  },
  backLeftBtnLeft: {
    backgroundColor: mobileTheme.colors.primary,
    left: 0,
  },
  backRightBtn: {
    alignItems: 'center',
    bottom: 0,
    justifyContent: 'center',
    position: 'absolute',
    top: 0,
    width: 75,
  },
  backRightBtnLeft: {
    backgroundColor: mobileTheme.colors.warning,
    right: 75,
  },
  backRightBtnRight: {
    backgroundColor: mobileTheme.colors.error,
    right: 0,
    borderTopRightRadius: 8,
    borderBottomRightRadius: 8,
  },
  backTextWhite: {
    color: '#FFF',
    fontSize: 12,
    textAlign: 'center',
  },
  // Dialog styles
  dialog: {
    borderRadius: 8,
  },
  userSummary: {
    padding: 16,
    borderRadius: 8,
    marginTop: 16,
    backgroundColor: mobileTheme.colors.surface,
    elevation: 1,
  },
  userSummaryName: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  userSummaryEmail: {
    fontSize: 14,
    color: mobileTheme.colors.placeholder,
  },
  // Modal styles
  modalContainer: {
    margin: 20,
  },
  modalSurface: {
    borderRadius: 8,
    elevation: 4,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  modalTitle: {
    fontSize: 20,
  },
  modalContent: {
    padding: 16,
    maxHeight: 400,
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    padding: 16,
  },
  actionButton: {
    marginLeft: 8,
  },
  // User details modal
  userDetailHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  detailAvatar: {
    marginRight: 16,
  },
  userDetailInfo: {
    flex: 1,
  },
  detailName: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  detailEmail: {
    fontSize: 14,
    color: mobileTheme.colors.placeholder,
    marginBottom: 8,
  },
  divider: {
    marginVertical: 8,
  },
  // Snackbar
  snackbar: {
    bottom: 70, // Above bottom navigation
  },
});

export default MobileUserManagementView;
