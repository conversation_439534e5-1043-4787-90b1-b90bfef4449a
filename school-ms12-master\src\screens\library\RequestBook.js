import React, { useState, useEffect } from 'react';
import { View, ScrollView, StyleSheet, Alert } from 'react-native';
import { Card, Title, Text, ActivityIndicator } from 'react-native-paper';
import { db } from '../../config/firebase';
import { collection, addDoc, doc, getDoc, updateDoc, increment } from 'firebase/firestore';
import { useAuth } from '../../context/AuthContext';
import CustomButton from '../../components/common/CustomButton';
import CustomInput from '../../components/common/CustomInput';

const RequestBook = ({ route, navigation }) => {
  const { bookId } = route.params;
  const { user } = useAuth();
  const [book, setBook] = useState(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [notes, setNotes] = useState('');

  useEffect(() => {
    fetchBookDetails();
  }, []);

  const fetchBookDetails = async () => {
    try {
      const bookDoc = await getDoc(doc(db, 'books', bookId));
      if (bookDoc.exists()) {
        setBook({ id: bookDoc.id, ...bookDoc.data() });
      }
      setLoading(false);
    } catch (error) {
      console.error('Error fetching book details:', error);
      setLoading(false);
    }
  };

  const handleRequest = async () => {
    try {
      setSubmitting(true);

      // Check if user has any overdue books
      const borrowingsRef = collection(db, 'bookBorrowings');
      const overdueQuery = query(
        borrowingsRef,
        where('userId', '==', user.uid),
        where('status', '==', 'borrowed'),
        where('dueDate', '<', new Date().toISOString())
      );
      const overdueSnapshot = await getDocs(overdueQuery);

      if (!overdueSnapshot.empty) {
        Alert.alert(
          'Cannot Request Book',
          'You have overdue books. Please return them before requesting new books.'
        );
        return;
      }

      // Check if user has reached maximum allowed borrows
      const activeQuery = query(
        borrowingsRef,
        where('userId', '==', user.uid),
        where('status', '==', 'borrowed')
      );
      const activeSnapshot = await getDocs(activeQuery);

      if (activeSnapshot.size >= 3) { // Maximum 3 books at a time
        Alert.alert(
          'Cannot Request Book',
          'You have reached the maximum number of books you can borrow at once.'
        );
        return;
      }

      // Create borrowing request
      const requestRef = collection(db, 'bookRequests');
      await addDoc(requestRef, {
        userId: user.uid,
        bookId: book.id,
        requestDate: new Date().toISOString(),
        status: 'pending',
        notes,
      });

      // Add to library activities
      const activitiesRef = collection(db, 'libraryActivities');
      await addDoc(activitiesRef, {
        type: 'request',
        action: `${user.displayName} requested ${book.title}`,
        user: user.displayName,
        timestamp: new Date().toISOString(),
      });

      Alert.alert(
        'Request Submitted',
        'Your book request has been submitted successfully. The librarian will process your request soon.',
        [
          {
            text: 'OK',
            onPress: () => navigation.goBack(),
          },
        ]
      );
    } catch (error) {
      console.error('Error requesting book:', error);
      Alert.alert(
        'Error',
        'Failed to submit book request. Please try again.'
      );
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <View style={styles.container}>
        <ActivityIndicator size="large" />
      </View>
    );
  }

  if (!book) {
    return (
      <View style={styles.container}>
        <Text>Book not found</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <Card style={styles.bookCard}>
        <Card.Content>
          <Title>{book.title}</Title>
          <Text style={styles.author}>By {book.author}</Text>
          
          <View style={styles.detailsContainer}>
            <Text>ISBN: {book.isbn}</Text>
            <Text>Available Copies: {book.availableQuantity}</Text>
            <Text>Location: {book.location}</Text>
          </View>

          <CustomInput
            label="Notes (Optional)"
            value={notes}
            onChangeText={setNotes}
            multiline
            numberOfLines={3}
            style={styles.notesInput}
          />

          <View style={styles.termsContainer}>
            <Text style={styles.termsTitle}>Borrowing Terms:</Text>
            <Text>1. Maximum borrowing period is 14 days</Text>
            <Text>2. Late returns will incur penalties</Text>
            <Text>3. Damaged books must be replaced</Text>
            <Text>4. Maximum 3 books can be borrowed at once</Text>
          </View>

          <CustomButton
            mode="contained"
            onPress={handleRequest}
            loading={submitting}
            disabled={submitting || book.availableQuantity === 0}
            style={styles.requestButton}
          >
            {book.availableQuantity === 0 ? 'Not Available' : 'Submit Request'}
          </CustomButton>

          <CustomButton
            mode="outlined"
            onPress={() => navigation.goBack()}
            style={styles.cancelButton}
          >
            Cancel
          </CustomButton>
        </Card.Content>
      </Card>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  bookCard: {
    margin: 10,
  },
  author: {
    fontSize: 16,
    color: '#666',
    marginBottom: 15,
  },
  detailsContainer: {
    backgroundColor: '#f5f5f5',
    padding: 15,
    borderRadius: 8,
    marginVertical: 15,
  },
  notesInput: {
    marginVertical: 15,
  },
  termsContainer: {
    backgroundColor: '#fff3e0',
    padding: 15,
    borderRadius: 8,
    marginVertical: 15,
  },
  termsTitle: {
    fontWeight: 'bold',
    marginBottom: 10,
  },
  requestButton: {
    marginTop: 20,
  },
  cancelButton: {
    marginTop: 10,
  },
});

export default RequestBook;
