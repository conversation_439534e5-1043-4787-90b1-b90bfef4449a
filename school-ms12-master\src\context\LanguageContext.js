import React, { createContext, useState, useContext, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { I18nManager } from 'react-native';
import languageUtils from '../utils/languageUtils';
import TranslationUpdater from '../utils/TranslationUpdater';

// Import translation files
import enTranslations from '../translations/en.json';
import amTranslations from '../translations/am.json';
import orTranslations from '../translations/om.json';
import ctuTranslations from '../translations/ctu.json';
import resultViewTranslations from '../translations/resultView.json';

const LanguageContext = createContext();

// Define the required properties that must exist in all translations
const requiredProperties = {
  adminRequired: {
    classManagement: {
      title: "Class Management",
      addClass: "Add Class",
      editClass: "Edit Class",
      deleteClass: "Delete Class",
      classDetails: "Class Details",
      classList: "Class List"
    },
    navigation: {
      examSchedule: "Exam Schedule",
      weeklyClassSchedule: "Weekly Class Schedule"
    }
  },
  teacher: {
    classManagement: {
      title: "Class Management",
      myClasses: "My Classes",
      allClasses: "All Classes",
      activeClasses: "Active Classes",
      pastClasses: "Past Classes",
      upcomingClasses: "Upcoming Classes"
    }
  },
  parent: {
    childSchedule: "Child's Schedule",
    dashboard: {
      noChildrenRegistered: "No children registered in the system.",
      errorLoadingChildData: "Failed to load children information",
      title: "Parent Dashboard",
      welcome: "Welcome",
      selectChild: "Select Child",
      updateProfile: "Update Profile",
      searchMenuItems: "Search menu items...",
      active: "Active",
      gpa: "GPA",
      attendance: "Attendance",
      behavior: "Behavior",
      classStatus: "Class Status",
      academicProgress: "Academic Progress",
      academicSummary: "Your child is performing well in most subjects, with excellent progress in English and Math.",
      viewGrades: "View Detailed Grades",
      attendanceOverview: "Attendance Overview",
      noSearchResults: "No results found for your search",
      clearSearch: "Clear Search",
      errorLoadingData: "Failed to load dashboard data. Please try again.",
      present: "Present",
      absent: "Absent",
      late: "Late",
      viewAttendance: "View Attendance Records",
      behaviorAnalysis: "Behavior Analysis",
      behaviorSummary: "Your child demonstrates excellent behavior in class, actively participating and showing respect to teachers and peers.",
      viewBehaviorRecords: "View Behavior Records",
      upcomingEvents: "Upcoming Events",
      viewCalendar: "View Full Calendar",
      quickAccess: "Quick Access",
      showLess: "Show Less",
      showAll: "Show All",
      showMoreCategories: "Show More Categories",
      examSchedule: "Exam Schedule"
    }
  }
};

// Create a deep merge function to ensure all required properties exist
const deepMerge = (target, source) => {
  const output = { ...target };

  for (const key in source) {
    if (source.hasOwnProperty(key)) {
      if (source[key] instanceof Object && key in target && target[key] instanceof Object) {
        output[key] = deepMerge(target[key], source[key]);
      } else if (!(key in target)) {
        output[key] = source[key];
      }
    }
  }

  return output;
};

// Apply the required properties to each translation
const enhancedTranslations = {
  en: deepMerge(enTranslations, requiredProperties),
  am: deepMerge(amTranslations, requiredProperties),
  om: deepMerge(orTranslations, requiredProperties),
  ctu: deepMerge(ctuTranslations, requiredProperties)
};

// Combine all translations into one object with required properties
export const translations = {
  en: { ...enhancedTranslations.en, ...resultViewTranslations },
  am: { ...enhancedTranslations.am, ...resultViewTranslations },
  om: { ...enhancedTranslations.om, ...resultViewTranslations },
  ctu: { ...enhancedTranslations.ctu, ...resultViewTranslations }
};

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (!context) {
    console.error('Language context not available. Make sure to use useLanguage within a LanguageProvider.');
    // Return a fallback context instead of throwing an error
    return {
      language: 'en',
      setLanguage: () => console.warn('Language context not initialized'),
      isLoading: false,
      supportedLanguages: { en: 'English' },
      translate: (key) => key, // Return the key as fallback
      getTextStyle: (style) => style,
      isRTL: false,
      translations: { en: {} }
    };
  }
  return context;
};

export const LanguageProvider = ({ children }) => {
  // Check if we're already inside a LanguageProvider
  try {
    const existingContext = useContext(LanguageContext);

    // If we already have a context, just render children
    if (existingContext) {
      return children;
    }
  } catch (error) {
    console.error('Error checking existing language context:', error);
    // Continue with creating a new context
  }

  const [language, setLanguage] = useState('en');
  const [isLoading, setIsLoading] = useState(false); // Start with false to avoid blank screen

  const supportedLanguages = {
    en: 'English',
    am: 'አማርኛ',
    om: 'Afaan Oromoo',
    ctu: 'Ctu'
  };

  const languageConfig = {
    en: { isRTL: false, fontScale: 1 },
    am: { isRTL: false, fontScale: 1.1 }, // Slightly larger for Amharic
    om: { isRTL: false, fontScale: 1 },
    ctu: { isRTL: false, fontScale: 1 }
  };

  useEffect(() => {
    loadLanguagePreference();
  }, []);

  const loadLanguagePreference = async () => {
    try {
      setIsLoading(true);

      // First try to get from AsyncStorage
      try {
        const savedLanguage = await AsyncStorage.getItem('language');
        if (savedLanguage && supportedLanguages[savedLanguage]) {
          console.log('Using saved language preference:', savedLanguage);
          await setLanguageAndSave(savedLanguage);
          return;
        }
      } catch (storageError) {
        console.error('Error accessing AsyncStorage:', storageError);
        // Continue to next method if AsyncStorage fails
      }

      // If no saved preference, try to get device locale
      try {
        const deviceLocale = languageUtils.getDeviceLocale();
        if (deviceLocale && supportedLanguages[deviceLocale]) {
          console.log('Using device locale:', deviceLocale);
          await setLanguageAndSave(deviceLocale);
          return;
        }
      } catch (localeError) {
        console.error('Error getting device locale:', localeError);
        // Continue to default if locale detection fails
      }

      // Default to English if all else fails
      console.log('Defaulting to English language');
      await setLanguageAndSave('en');

    } catch (error) {
      console.error('Error loading language preference:', error);
      // Set to English as fallback without saving
      setLanguage('en');
    } finally {
      setIsLoading(false);
    }
  };

  const setLanguageAndSave = async (newLanguage) => {
    try {
      console.log(`Setting language to: ${newLanguage}`);
      if (!supportedLanguages[newLanguage]) {
        throw new Error(`Unsupported language: ${newLanguage}`);
      }

      // Update RTL setting
      const isRTL = languageConfig[newLanguage].isRTL;
      if (I18nManager.isRTL !== isRTL) {
        I18nManager.forceRTL(isRTL);
      }

      // Save to storage first
      await AsyncStorage.setItem('language', newLanguage);

      // Trigger loading state to indicate language change is in progress
      setIsLoading(true);

      // Small delay to ensure UI updates properly
      setTimeout(() => {
        // Update state to trigger context update
        setLanguage(newLanguage);

        // End loading state
        setIsLoading(false);

        console.log(`Language successfully changed to: ${newLanguage}`);
      }, 100);

      return true;
    } catch (error) {
      console.error('Error saving language preference:', error);
      setIsLoading(false);
      throw error;
    }
  };

  // Cache for translations to improve performance
  const translationCache = {};

  const translate = (key, params = {}) => {
    try {
      // Handle null or undefined key
      if (!key) return '';

      // Check if key exists in cache
      const cacheKey = `${key}_${language}_${JSON.stringify(params)}`;
      if (translationCache[cacheKey]) {
        return TranslationUpdater.withDebugInfo(key, translationCache[cacheKey]);
      }

      // Split key into sections (e.g., "admin.dashboard.title" -> ["admin", "dashboard", "title"])
      const keyParts = key.split('.');

      // Try to find the translation in the nested structure
      let translatedValue = translations[language];
      let fallbackValue = translations.en; // Use English as fallback

      // Navigate through the nested structure for current language
      for (const part of keyParts) {
        if (!translatedValue || typeof translatedValue !== 'object') {
          translatedValue = undefined;
          break;
        }
        translatedValue = translatedValue[part];
      }

      // If not found, try to find in the default language (English)
      if (translatedValue === undefined) {
        for (const part of keyParts) {
          if (!fallbackValue || typeof fallbackValue !== 'object') {
            fallbackValue = undefined;
            break;
          }
          fallbackValue = fallbackValue[part];
        }
      }

      // If still not found, generate a more useful error message and return the key as is
      if (translatedValue === undefined && fallbackValue === undefined) {
        // Extract the screen name from the key (typically the first part)
        const screenName = keyParts[0] || 'unknown';

        // Log missing translation in a structured way for easier debugging
        // console.warn(`MISSING TRANSLATION: [${screenName}] [${language}] "${key}"`);

        // Add to a global list of missing translations for easy export/debugging
        if (global.missingTranslations === undefined) {
          global.missingTranslations = {};
        }

        if (!global.missingTranslations[language]) {
          global.missingTranslations[language] = new Set();
        }

        global.missingTranslations[language].add(key);

        return key;
      }

      // Use the translated value or fallback
      let result = translatedValue !== undefined ? translatedValue : fallbackValue;

      // Replace parameters if any
      if (typeof result === 'string' && params && Object.keys(params).length > 0) {
        Object.entries(params).forEach(([paramKey, paramValue]) => {
          result = result.replace(new RegExp(`{{\\s*${paramKey}\\s*}}`, 'g'), paramValue);
            });
      }

      // Cache the result
      translationCache[cacheKey] = result;

      return TranslationUpdater.withDebugInfo(key, result);
    } catch (error) {
      console.error(`Error translating key: ${key}`, error);
      return key;
    }
  };

  const getTextStyle = (customStyle = {}) => {
    try {
      const config = languageConfig[language] || languageConfig.en;
      return {
        ...customStyle,
        writingDirection: config.isRTL ? 'rtl' : 'ltr',
        fontSize: (customStyle.fontSize || 14) * (config.fontScale || 1),
        fontFamily: language === 'am' ? 'Roboto' : undefined, // Use platform default
      };
    } catch (error) {
      console.error('Error in getTextStyle:', error);
      return customStyle || {};
    }
  };

  // Create a safe context value
  const contextValue = {
    language,
    setLanguage: setLanguageAndSave,
    isLoading,
    supportedLanguages,
    translate,
    getTextStyle,
    isRTL: (languageConfig[language] || languageConfig.en).isRTL,
    translations // Add translations to the context
  };

  try {
    return (
      <LanguageContext.Provider value={contextValue}>
        {children}
      </LanguageContext.Provider>
    );
  } catch (error) {
    console.error('Error rendering LanguageProvider:', error);
    // Return children without context as fallback
    return children;
  }
};

export default LanguageContext;
