import React, { useState, useEffect, useRef } from 'react';
import { View, ScrollView, StyleSheet, StatusBar, SafeAreaView, Animated, Dimensions } from 'react-native';
import {
  Card,
  Title,
  Text,
  Button,
  IconButton,
  Divider,
  ActivityIndicator,
  useTheme,
  Surface,
  Chip,
  DataTable,
  Searchbar,
  Menu,
  List,
  Avatar
} from 'react-native-paper';
import { db } from '../../config/firebase';
import {
  collection,
  query,
  where,
  getDocs,
  orderBy,
  limit,
  Timestamp
} from 'firebase/firestore';
import { useAuth } from '../../context/AuthContext';
import { useLanguage } from '../../context/LanguageContext';
import * as Animatable from 'react-native-animatable';
import { LinearGradient } from 'expo-linear-gradient';
import AdminAppHeader from '../../components/common/AdminAppHeader';
import AdminSidebar from '../../components/common/AdminSidebar';
import SidebarBackdrop from '../../components/common/SidebarBackdrop';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'react-native-chart-kit';
import { sanitize<PERSON>hartData, sanitizeChartDatasets } from '../../utils/ChartUtils';

const AttendanceReports = ({ navigation }) => {
  const { user } = useAuth();
  const { translate, language, isRTL } = useLanguage();
  // No theme needed
  const { width } = Dimensions.get('window');
  
  // Animation refs
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const backdropFadeAnim = useRef(new Animated.Value(0)).current;
  const drawerAnim = useRef(new Animated.Value(-300)).current;
  
  // State variables
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [activeSidebarItem, setActiveSidebarItem] = useState('AttendanceReports');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [attendanceData, setAttendanceData] = useState([]);
  const [filteredData, setFilteredData] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedClass, setSelectedClass] = useState(null);
  const [selectedSection, setSelectedSection] = useState(null);
  const [selectedDateRange, setSelectedDateRange] = useState('week'); // 'week', 'month', 'semester', 'year'
  const [classMenuVisible, setClassMenuVisible] = useState(false);
  const [sectionMenuVisible, setSectionMenuVisible] = useState(false);
  const [dateRangeMenuVisible, setDateRangeMenuVisible] = useState(false);
  const [classes, setClasses] = useState([]);
  const [sections, setSections] = useState([]);
  const [statistics, setStatistics] = useState({
    total: 0,
    present: 0,
    absent: 0,
    percentage: 0,
    byClass: {},
    byDate: {}
  });
  
  // Start animations when component mounts
  useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 1000,
      useNativeDriver: true,
    }).start();
    
    // Hide the default header
    navigation.setOptions({
      headerShown: false,
    });
    
    // Fetch initial data
    fetchClasses();
    fetchAttendanceData();
  }, []);
  
  // Toggle drawer
  const toggleDrawer = () => {
    if (drawerOpen) {
      // Close drawer
      Animated.timing(backdropFadeAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }).start();
      
      Animated.timing(drawerAnim, {
        toValue: -300,
        duration: 300,
        useNativeDriver: true,
      }).start(() => setDrawerOpen(false));
    } else {
      // Open drawer
      setDrawerOpen(true);
      
      Animated.timing(drawerAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }).start();
      
      Animated.timing(backdropFadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }).start();
    }
  };
  
  // Fetch classes
  const fetchClasses = async () => {
    try {
      const classesRef = collection(db, 'classes');
      const classesSnapshot = await getDocs(classesRef);
      
      if (!classesSnapshot.empty) {
        const classesData = classesSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));
        
        setClasses(classesData);
        
        // Set default selected class
        if (classesData.length > 0 && !selectedClass) {
          setSelectedClass(classesData[0]);
          fetchSections(classesData[0].id);
        }
      }
    } catch (error) {
      console.error('Error fetching classes:', error);
      setError(translate('attendanceReports.fetchClassesError'));
    }
  };
  
  // Fetch sections for a class
  const fetchSections = async (classId) => {
    try {
      const sectionsRef = collection(db, 'sections');
      const sectionsQuery = query(sectionsRef, where('classId', '==', classId));
      const sectionsSnapshot = await getDocs(sectionsQuery);
      
      if (!sectionsSnapshot.empty) {
        const sectionsData = sectionsSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));
        
        setSections(sectionsData);
        
        // Set default selected section
        if (sectionsData.length > 0 && !selectedSection) {
          setSelectedSection(sectionsData[0]);
        }
      } else {
        setSections([]);
        setSelectedSection(null);
      }
    } catch (error) {
      console.error('Error fetching sections:', error);
      setError(translate('attendanceReports.fetchSectionsError'));
    }
  };
  
  // Fetch attendance data
  const fetchAttendanceData = async () => {
    try {
      setLoading(true);
      
      // Calculate date range
      const now = new Date();
      let startDate;
      
      switch (selectedDateRange) {
        case 'week':
          startDate = new Date(now);
          startDate.setDate(now.getDate() - 7);
          break;
        case 'month':
          startDate = new Date(now);
          startDate.setMonth(now.getMonth() - 1);
          break;
        case 'semester':
          startDate = new Date(now);
          startDate.setMonth(now.getMonth() - 6);
          break;
        case 'year':
          startDate = new Date(now);
          startDate.setFullYear(now.getFullYear() - 1);
          break;
        default:
          startDate = new Date(now);
          startDate.setDate(now.getDate() - 7);
      }
      
      const attendanceRef = collection(db, 'attendance');
      let attendanceQuery;
      
      if (selectedClass && selectedSection) {
        attendanceQuery = query(
          attendanceRef,
          where('classId', '==', selectedClass.id),
          where('sectionName', '==', selectedSection.name),
          where('date', '>=', startDate.toISOString().split('T')[0]),
          orderBy('date', 'desc')
        );
      } else if (selectedClass) {
        attendanceQuery = query(
          attendanceRef,
          where('classId', '==', selectedClass.id),
          where('date', '>=', startDate.toISOString().split('T')[0]),
          orderBy('date', 'desc')
        );
      } else {
        attendanceQuery = query(
          attendanceRef,
          where('date', '>=', startDate.toISOString().split('T')[0]),
          orderBy('date', 'desc'),
          limit(100)
        );
      }
      
      const attendanceSnapshot = await getDocs(attendanceQuery);
      
      if (!attendanceSnapshot.empty) {
        const attendanceData = attendanceSnapshot.docs.map(doc => {
          const data = doc.data();
          const attendanceDate = data.date ? 
            (typeof data.date === 'string' ? new Date(data.date) : new Date(data.date.seconds * 1000)) : 
            new Date();
          
          return {
            id: doc.id,
            ...data,
            date: attendanceDate,
            formattedDate: attendanceDate.toISOString().split('T')[0],
            dayName: attendanceDate.toLocaleDateString(language === 'en' ? 'en-US' : 'am-ET', { weekday: 'long' }),
            monthName: attendanceDate.toLocaleDateString(language === 'en' ? 'en-US' : 'am-ET', { month: 'long' }),
          };
        });
        
        setAttendanceData(attendanceData);
        setFilteredData(attendanceData);
        
        // Calculate statistics
        calculateStatistics(attendanceData);
      } else {
        setAttendanceData([]);
        setFilteredData([]);
        setStatistics({
          total: 0,
          present: 0,
          absent: 0,
          percentage: 0,
          byClass: {},
          byDate: {}
        });
      }
    } catch (error) {
      console.error('Error fetching attendance data:', error);
      setError(translate('attendanceReports.fetchDataError'));
    } finally {
      setLoading(false);
    }
  };
  
  // Calculate statistics from attendance data
  const calculateStatistics = (data) => {
    const total = data.length;
    const present = data.filter(item => item.status === 'present').length;
    const absent = data.filter(item => item.status === 'absent').length;
    const percentage = total > 0 ? (present / total) * 100 : 0;
    
    // Group by class
    const byClass = data.reduce((acc, item) => {
      const className = item.className || 'Unknown';
      if (!acc[className]) {
        acc[className] = {
          total: 0,
          present: 0,
          absent: 0,
          percentage: 0
        };
      }
      
      acc[className].total += 1;
      if (item.status === 'present') {
        acc[className].present += 1;
      } else if (item.status === 'absent') {
        acc[className].absent += 1;
      }
      
      acc[className].percentage = acc[className].total > 0 
        ? (acc[className].present / acc[className].total) * 100 
        : 0;
      
      return acc;
    }, {});
    
    // Group by date
    const byDate = data.reduce((acc, item) => {
      const date = item.formattedDate;
      if (!acc[date]) {
        acc[date] = {
          total: 0,
          present: 0,
          absent: 0,
          percentage: 0
        };
      }
      
      acc[date].total += 1;
      if (item.status === 'present') {
        acc[date].present += 1;
      } else if (item.status === 'absent') {
        acc[date].absent += 1;
      }
      
      acc[date].percentage = acc[date].total > 0 
        ? (acc[date].present / acc[date].total) * 100 
        : 0;
      
      return acc;
    }, {});
    
    setStatistics({
      total,
      present,
      absent,
      percentage,
      byClass,
      byDate
    });
  };
  
  // Filter attendance data
  const filterAttendanceData = () => {
    let filtered = [...attendanceData];
    
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(item => 
        (item.studentName && item.studentName.toLowerCase().includes(query)) ||
        (item.className && item.className.toLowerCase().includes(query)) ||
        (item.sectionName && item.sectionName.toLowerCase().includes(query)) ||
        (item.status && item.status.toLowerCase().includes(query))
      );
    }
    
    setFilteredData(filtered);
  };
  
  // Handle search query change
  useEffect(() => {
    filterAttendanceData();
  }, [searchQuery, attendanceData]);
  
  // Handle class selection
  const handleClassSelect = (classItem) => {
    setSelectedClass(classItem);
    setClassMenuVisible(false);
    fetchSections(classItem.id);
    fetchAttendanceData();
  };
  
  // Handle section selection
  const handleSectionSelect = (sectionItem) => {
    setSelectedSection(sectionItem);
    setSectionMenuVisible(false);
    fetchAttendanceData();
  };
  
  // Handle date range selection
  const handleDateRangeSelect = (range) => {
    setSelectedDateRange(range);
    setDateRangeMenuVisible(false);
    fetchAttendanceData();
  };
  
  // Render loading state
  const renderLoading = () => (
    <View style={styles.loadingContainer}>
      <ActivityIndicator size="large" color={'#1976d2'} />
      <Text style={styles.loadingText}>{translate('common.loading')}</Text>
    </View>
  );
  
  // Render error state
  const renderError = () => (
    <View style={styles.errorContainer}>
      <IconButton
        icon="alert-circle"
        size={48}
        color={'#B00020'}
      />
      <Text style={styles.errorText}>{error}</Text>
      <Button
        mode="contained"
        onPress={fetchAttendanceData}
        style={styles.retryButton}
      >
        {translate('common.retry')}
      </Button>
    </View>
  );
  
  // Render filters
  const renderFilters = () => (
    <Card style={styles.filtersCard}>
      <Card.Content>
        <Title style={styles.cardTitle}>{translate('attendanceReports.filters')}</Title>
        
        <View style={styles.filtersContainer}>
          <View style={styles.filterItem}>
            <Text style={styles.filterLabel}>{translate('attendanceReports.class')}</Text>
            <Menu
              visible={classMenuVisible}
              onDismiss={() => setClassMenuVisible(false)}
              anchor={
                <Button
                  mode="outlined"
                  onPress={() => setClassMenuVisible(true)}
                  style={styles.filterButton}
                >
                  {selectedClass ? selectedClass.name : translate('attendanceReports.selectClass')}
                </Button>
              }
            >
              {classes.map(classItem => (
                <Menu.Item
                  key={classItem.id}
                  title={classItem.name}
                  onPress={() => handleClassSelect(classItem)}
                />
              ))}
            </Menu>
          </View>
          
          <View style={styles.filterItem}>
            <Text style={styles.filterLabel}>{translate('attendanceReports.section')}</Text>
            <Menu
              visible={sectionMenuVisible}
              onDismiss={() => setSectionMenuVisible(false)}
              anchor={
                <Button
                  mode="outlined"
                  onPress={() => setSectionMenuVisible(true)}
                  style={styles.filterButton}
                  disabled={!selectedClass || sections.length === 0}
                >
                  {selectedSection ? selectedSection.name : translate('attendanceReports.selectSection')}
                </Button>
              }
            >
              {sections.map(sectionItem => (
                <Menu.Item
                  key={sectionItem.id}
                  title={sectionItem.name}
                  onPress={() => handleSectionSelect(sectionItem)}
                />
              ))}
            </Menu>
          </View>
          
          <View style={styles.filterItem}>
            <Text style={styles.filterLabel}>{translate('attendanceReports.dateRange')}</Text>
            <Menu
              visible={dateRangeMenuVisible}
              onDismiss={() => setDateRangeMenuVisible(false)}
              anchor={
                <Button
                  mode="outlined"
                  onPress={() => setDateRangeMenuVisible(true)}
                  style={styles.filterButton}
                >
                  {translate(`attendanceReports.${selectedDateRange}`)}
                </Button>
              }
            >
              <Menu.Item
                title={translate('attendanceReports.week')}
                onPress={() => handleDateRangeSelect('week')}
              />
              <Menu.Item
                title={translate('attendanceReports.month')}
                onPress={() => handleDateRangeSelect('month')}
              />
              <Menu.Item
                title={translate('attendanceReports.semester')}
                onPress={() => handleDateRangeSelect('semester')}
              />
              <Menu.Item
                title={translate('attendanceReports.year')}
                onPress={() => handleDateRangeSelect('year')}
              />
            </Menu>
          </View>
        </View>
        
        <Searchbar
          placeholder={translate('attendanceReports.search')}
          onChangeText={setSearchQuery}
          value={searchQuery}
          style={styles.searchBar}
        />
      </Card.Content>
    </Card>
  );
  
  // Render statistics
  const renderStatistics = () => (
    <Card style={styles.statsCard}>
      <Card.Content>
        <Title style={styles.cardTitle}>{translate('attendanceReports.statistics')}</Title>
        
        <View style={styles.statsContainer}>
          <View style={styles.statItem}>
            <Surface style={[styles.statBadge, { backgroundColor: '#E3F2FD' }]}>
              <IconButton icon="account-group" size={24} color="#2196F3" />
            </Surface>
            <Text style={styles.statLabel}>{translate('attendanceReports.total')}</Text>
            <Text style={styles.statValue}>{statistics.total}</Text>
          </View>
          
          <View style={styles.statItem}>
            <Surface style={[styles.statBadge, { backgroundColor: '#E8F5E9' }]}>
              <IconButton icon="check-circle" size={24} color="#4CAF50" />
            </Surface>
            <Text style={styles.statLabel}>{translate('attendanceReports.present')}</Text>
            <Text style={[styles.statValue, { color: '#4CAF50' }]}>{statistics.present}</Text>
          </View>
          
          <View style={styles.statItem}>
            <Surface style={[styles.statBadge, { backgroundColor: '#FFEBEE' }]}>
              <IconButton icon="close-circle" size={24} color="#F44336" />
            </Surface>
            <Text style={styles.statLabel}>{translate('attendanceReports.absent')}</Text>
            <Text style={[styles.statValue, { color: '#F44336' }]}>{statistics.absent}</Text>
          </View>
          
          <View style={styles.statItem}>
            <Surface style={[styles.statBadge, { backgroundColor: '#FFF8E1' }]}>
              <IconButton icon="percent" size={24} color="#FFC107" />
            </Surface>
            <Text style={styles.statLabel}>{translate('attendanceReports.percentage')}</Text>
            <Text style={[
              styles.statValue,
              { color: statistics.percentage >= 75 ? '#4CAF50' : '#F44336' }
            ]}>
              {statistics.percentage.toFixed(1)}%
            </Text>
          </View>
        </View>
      </Card.Content>
    </Card>
  );
  
  // Render charts
  const renderCharts = () => {
    // Prepare data for pie chart
    const pieChartData = [
      {
        name: translate('attendanceReports.present'),
        population: statistics.present,
        color: '#4CAF50',
        legendFontColor: '#7F7F7F',
        legendFontSize: 12
      },
      {
        name: translate('attendanceReports.absent'),
        population: statistics.absent,
        color: '#F44336',
        legendFontColor: '#7F7F7F',
        legendFontSize: 12
      }
    ];
    
    // Prepare data for bar chart
    const barChartData = {
      labels: Object.keys(statistics.byClass).slice(0, 5),
      datasets: [
        {
          data: sanitizeChartData(Object.values(statistics.byClass)
            .slice(0), 5)
            .map(item => item.percentage)
        }
      ]
    };
    
    // Prepare data for line chart
    const dates = Object.keys(statistics.byDate).slice(-7);
    const percentages = dates.map(date => statistics.byDate[date].percentage);
    
    const lineChartData = {
      labels: dates.map(date => date.split('-')[2]), // Just show day
      datasets: [
        {
          data: sanitizeChartData(percentages),
          color: (opacity = 1) => `rgba(33, 150, 243, ${opacity})`,
          strokeWidth: 2
        }
      ]
    };
    
    const chartConfig = {
      backgroundGradientFrom: '#FFFFFF',
      backgroundGradientTo: '#FFFFFF',
      color: (opacity = 1) => `rgba(0, 0, 0, ${opacity})`,
      strokeWidth: 2,
      barPercentage: 0.5,
      useShadowColorFromDataset: false
    };
    
    return (
      <Card style={styles.chartsCard}>
        <Card.Content>
          <Title style={styles.cardTitle}>{translate('attendanceReports.charts')}</Title>
          
          <View style={styles.chartContainer}>
            <Text style={styles.chartTitle}>{translate('attendanceReports.attendanceDistribution')}</Text>
            <PieChart
              data={pieChartData.map(item => ({
        ...item,
        population: isNaN(item.population) || item.population === undefined || 
                    item.population === null || item.population === Infinity || 
                    item.population === -Infinity ? 0 : item.population
      }))}
              width={width - 64}
              height={200}
              chartConfig={chartConfig}
              accessor="population"
              backgroundColor="transparent"
              paddingLeft="15"
              absolute
            />
          </View>
          
          <Divider style={styles.divider} />
          
          <View style={styles.chartContainer}>
            <Text style={styles.chartTitle}>{translate('attendanceReports.attendanceByClass')}</Text>
            <BarChart
              data={sanitizeChartDatasets(barChartData)}
              width={width - 64}
              height={200}
              chartConfig={{
                ...chartConfig,
                color: (opacity = 1) => `rgba(76, 175, 80, ${opacity})`,
              }}
              style={styles.chart}
            />
          </View>
          
          <Divider style={styles.divider} />
          
          <View style={styles.chartContainer}>
            <Text style={styles.chartTitle}>{translate('attendanceReports.attendanceTrend')}</Text>
            <LineChart
              data={sanitizeChartDatasets(lineChartData)}
              width={width - 64}
              height={200}
              chartConfig={chartConfig}
              bezier
              style={styles.chart}
            />
          </View>
        </Card.Content>
      </Card>
    );
  };
  
  // Render attendance table
  const renderAttendanceTable = () => (
    <Card style={styles.tableCard}>
      <Card.Content>
        <Title style={styles.cardTitle}>{translate('attendanceReports.attendanceRecords')}</Title>
        
        <DataTable>
          <DataTable.Header>
            <DataTable.Title>{translate('attendanceReports.date')}</DataTable.Title>
            <DataTable.Title>{translate('attendanceReports.class')}</DataTable.Title>
            <DataTable.Title>{translate('attendanceReports.section')}</DataTable.Title>
            <DataTable.Title>{translate('attendanceReports.student')}</DataTable.Title>
            <DataTable.Title>{translate('attendanceReports.status')}</DataTable.Title>
          </DataTable.Header>
          
          {filteredData.slice(0, 10).map((item, index) => (
            <DataTable.Row key={item.id || index}>
              <DataTable.Cell>{item.formattedDate}</DataTable.Cell>
              <DataTable.Cell>{item.className || '-'}</DataTable.Cell>
              <DataTable.Cell>{item.sectionName || '-'}</DataTable.Cell>
              <DataTable.Cell>{item.studentName || '-'}</DataTable.Cell>
              <DataTable.Cell>
                <Chip
                  mode="outlined"
                  style={[
                    styles.statusChip,
                    item.status === 'present' ? styles.presentChip : styles.absentChip
                  ]}
                >
                  {item.status === 'present' 
                    ? translate('attendanceReports.present')
                    : translate('attendanceReports.absent')}
                </Chip>
              </DataTable.Cell>
            </DataTable.Row>
          ))}
        </DataTable>
        
        {filteredData.length > 10 && (
          <Button
            mode="text"
            onPress={() => {}}
            style={styles.viewMoreButton}
          >
            {translate('attendanceReports.viewMore')}
          </Button>
        )}
      </Card.Content>
    </Card>
  );
  
  return (
    <SafeAreaView style={styles.safeArea}>
      <StatusBar backgroundColor={'#1976d2'} barStyle="light-content" />
      
      {/* Admin App Header */}
      <AdminAppHeader
        title={translate('attendanceReports.title') || "Attendance Reports"}
        onMenuPress={toggleDrawer}
        showNotification={true}
      />
      
      {/* Admin Sidebar */}
      <AdminSidebar
        visible={drawerOpen}
        onClose={toggleDrawer}
        navigation={navigation}
        activeSidebarItem={activeSidebarItem}
        setActiveSidebarItem={setActiveSidebarItem}
      />
      
      {/* Sidebar Backdrop */}
      <SidebarBackdrop
        visible={drawerOpen}
        onPress={toggleDrawer}
        fadeAnim={backdropFadeAnim}
      />
      
      {/* Main Content */}
      <Animated.View 
        style={[
          styles.content,
          { opacity: fadeAnim }
        ]}
      >
        <ScrollView style={styles.scrollView}>
          {loading ? (
            renderLoading()
          ) : error ? (
            renderError()
          ) : (
            <>
              {renderFilters()}
              {renderStatistics()}
              {renderCharts()}
              {renderAttendanceTable()}
            </>
          )}
        </ScrollView>
      </Animated.View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  scrollView: {
    flex: 1,
  },
  loadingContainer: {
    padding: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#666',
  },
  errorContainer: {
    padding: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  errorText: {
    marginTop: 12,
    marginBottom: 16,
    fontSize: 16,
    color: '#F44336',
    textAlign: 'center',
  },
  retryButton: {
    marginTop: 8,
  },
  filtersCard: {
    marginBottom: 16,
    borderRadius: 8,
  },
  cardTitle: {
    fontSize: 18,
    marginBottom: 16,
  },
  filtersContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 16,
  },
  filterItem: {
    marginRight: 16,
    marginBottom: 16,
  },
  filterLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  filterButton: {
    minWidth: 120,
  },
  searchBar: {
    marginBottom: 8,
    elevation: 0,
    backgroundColor: '#f0f0f0',
  },
  statsCard: {
    marginBottom: 16,
    borderRadius: 8,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flexWrap: 'wrap',
  },
  statItem: {
    alignItems: 'center',
    width: '23%',
    marginBottom: 16,
  },
  statBadge: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  statLabel: {
    color: '#666',
    fontSize: 12,
    marginBottom: 4,
    textAlign: 'center',
  },
  statValue: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  chartsCard: {
    marginBottom: 16,
    borderRadius: 8,
  },
  chartContainer: {
    marginVertical: 16,
    alignItems: 'center',
  },
  chartTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
  },
  chart: {
    borderRadius: 8,
  },
  divider: {
    marginVertical: 16,
  },
  tableCard: {
    marginBottom: 16,
    borderRadius: 8,
  },
  statusChip: {
    height: 28,
  },
  presentChip: {
    backgroundColor: '#E8F5E9',
    borderColor: '#4CAF50',
  },
  absentChip: {
    backgroundColor: '#FFEBEE',
    borderColor: '#F44336',
  },
  viewMoreButton: {
    marginTop: 16,
    alignSelf: 'center',
  },
});

export default AttendanceReports;
