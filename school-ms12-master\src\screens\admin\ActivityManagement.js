import React, { useState, useEffect, useCallback, useRef } from 'react';
import { View, StyleSheet, FlatList, RefreshControl, TouchableOpacity, ScrollView } from 'react-native';
import {
  Surface,
  Text,
  Title,
  Paragraph,
  Divider,
  Button,
  Searchbar,
  Chip,
  Avatar,
  ActivityIndicator,
  IconButton,
  Menu,
  useTheme,
  TouchableRipple,
  Portal,
  Modal
} from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import * as Animatable from 'react-native-animatable';
import { useLanguage } from '../../context/LanguageContext';
import { useAuth } from '../../context/AuthContext';
import ActivityService from '../../services/ActivityService';
import { useEthiopianCalendar } from '../../hooks/useEthiopianCalendar';
import EthiopianDatePicker from '../../components/common/EthiopianDatePicker';
import AdminAppHeader from '../../components/common/AdminAppHeader';
import AdminSidebar from '../../components/common/AdminSidebar';
import SidebarBackdrop from '../../components/common/SidebarBackdrop';
import { Animated } from 'react-native';
import { translateActivity } from '../../utils/TranslationUtils';

const ActivityManagement = ({ navigation }) => {
  // No theme needed
  const { translate } = useLanguage();
  const { user } = useAuth();
  const { formatDate } = useEthiopianCalendar();

  const [activities, setActivities] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterMenuVisible, setFilterMenuVisible] = useState(false);
  const [filterType, setFilterType] = useState(null);
  const [filterRead, setFilterRead] = useState(null);
  const [startDate, setStartDate] = useState(null);
  const [endDate, setEndDate] = useState(null);
  const [datePickerVisible, setDatePickerVisible] = useState(false);
  const [datePickerMode, setDatePickerMode] = useState('start');
  const [selectedActivity, setSelectedActivity] = useState(null);
  const [activityMenuVisible, setActivityMenuVisible] = useState(false);

  // Sidebar state
  const [drawerOpen, setDrawerOpen] = useState(false);
  const drawerAnim = useRef(new Animated.Value(-300)).current;
  const backdropFadeAnim = useRef(new Animated.Value(0)).current;
  const [activeSidebarItem, setActiveSidebarItem] = useState('ActivityManagement');

  const pageSize = 20;

  const fetchActivities = useCallback(async (pageNum = 1, refresh = false) => {
    try {
      if (refresh) {
        setLoading(true);
        setPage(1);
        pageNum = 1;
      } else if (pageNum === 1) {
        setLoading(true);
      }

      const filterOptions = {};

      if (filterType) {
        filterOptions.type = filterType;
      }

      if (filterRead !== null) {
        filterOptions.read = filterRead;
      }

      if (startDate && endDate) {
        filterOptions.startDate = startDate;
        filterOptions.endDate = endDate;
      }

      const result = await ActivityService.getAllActivities(pageNum, pageSize, filterOptions);

      if (refresh || pageNum === 1) {
        setActivities(result.activities);
      } else {
        setActivities(prev => [...prev, ...result.activities]);
      }

      setHasMore(result.activities.length === pageSize);
    } catch (error) {
      console.error('Error fetching activities:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, [filterType, filterRead, startDate, endDate]);

  useEffect(() => {
    fetchActivities(1, true);

    // Hide the default header
    navigation.setOptions({
      headerShown: false,
    });
  }, [fetchActivities, navigation]);

  const toggleDrawer = () => {
    if (drawerOpen) {
      // Close drawer
      Animated.parallel([
        Animated.timing(drawerAnim, {
          toValue: -300,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(backdropFadeAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        })
      ]).start(() => setDrawerOpen(false));
    } else {
      // Open drawer
      setDrawerOpen(true);
      Animated.parallel([
        Animated.timing(drawerAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(backdropFadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        })
      ]).start();
    }
  };

  const handleRefresh = () => {
    setRefreshing(true);
    fetchActivities(1, true);
  };

  const handleLoadMore = () => {
    if (!loading && hasMore) {
      const nextPage = page + 1;
      setPage(nextPage);
      fetchActivities(nextPage);
    }
  };

  const handleSearch = () => {
    fetchActivities(1, true);
  };

  const handleClearFilters = () => {
    setFilterType(null);
    setFilterRead(null);
    setStartDate(null);
    setEndDate(null);
    setSearchQuery('');
    fetchActivities(1, true);
  };

  const handleActivityPress = (activity) => {
    navigation.navigate('ActivityDetailScreen', { activityId: activity.id });
  };

  const handleMarkAsRead = async (activityId) => {
    try {
      await ActivityService.markActivityAsRead(activityId);
      setActivities(prev =>
        prev.map(activity =>
          activity.id === activityId
            ? { ...activity, read: true }
            : activity
        )
      );
    } catch (error) {
      console.error('Error marking activity as read:', error);
    }
  };

  const handleDeleteActivity = async (activityId) => {
    try {
      await ActivityService.deleteActivity(activityId);
      setActivities(prev => prev.filter(activity => activity.id !== activityId));
    } catch (error) {
      console.error('Error deleting activity:', error);
    }
  };

  const getActivityIcon = (type) => {
    switch (type) {
      case 'user':
        return 'account';
      case 'class':
        return 'school';
      case 'exam':
        return 'file-document-edit';
      case 'academic':
        return 'calendar';
      case 'attendance':
        return 'calendar-check';
      case 'grade':
        return 'chart-box';
      case 'announcement':
        return 'bullhorn';
      case 'system':
        return 'cog';
      default:
        return 'information';
    }
  };

  const getActivityColor = (type) => {
    switch (type) {
      case 'user':
        return '#2196F3';
      case 'class':
        return '#4CAF50';
      case 'exam':
        return '#FF9800';
      case 'academic':
        return '#9C27B0';
      case 'attendance':
        return '#00BCD4';
      case 'grade':
        return '#F44336';
      case 'announcement':
        return '#E91E63';
      case 'system':
        return '#607D8B';
      default:
        return '#757575';
    }
  };

  const renderActivityItem = ({ item, index }) => {
    // Translate the activity using the translation utility
    const translatedActivity = translateActivity(item, translate);
    
    return (
      <Animatable.View
        animation="fadeIn"
        duration={300}
        delay={index * 50}
        key={item.id}
      >
        <TouchableRipple
          onPress={() => handleActivityPress(item)}
          rippleColor="rgba(0, 0, 0, .05)"
        >
          <Surface style={styles.activityItem}>
            <View style={styles.activityContent}>
              <Avatar.Icon
                icon={getActivityIcon(item.type)}
                size={40}
                style={[styles.activityIcon, { backgroundColor: getActivityColor(item.type) }]}
              />
              <View style={styles.activityTextContainer}>
                <Text style={styles.activityTitle}>{translatedActivity.title || item.title}</Text>
                <Paragraph numberOfLines={2} style={styles.activityDescription}>
                  {translatedActivity.description || item.description}
                </Paragraph>
                <View style={styles.activityMeta}>
                  <Text style={styles.activityTime}>
                    {formatDate(item.timestamp?.toDate?.() || item.timestamp)}
                  </Text>
                  <Chip
                    style={[styles.typeChip, { backgroundColor: getActivityColor(item.type) + '20' }]}
                    textStyle={{ color: getActivityColor(item.type), fontSize: 12 }}
                  >
                    {translatedActivity.typeLabel || translate(`activities.activityTypes.${item.type}`, item.type)}
                  </Chip>
                </View>
              </View>
              <Menu
                visible={activityMenuVisible && selectedActivity?.id === item.id}
                onDismiss={() => setActivityMenuVisible(false)}
                anchor={
                  <IconButton
                    icon="dots-vertical"
                    size={20}
                    onPress={() => {
                      setSelectedActivity(item);
                      setActivityMenuVisible(true);
                    }}
                  />
                }
              >
                <Menu.Item
                  icon="eye"
                  onPress={() => {
                    setActivityMenuVisible(false);
                    handleActivityPress(item);
                  }}
                  title={translate('common.view', 'View')}
                />
                {!item.read && (
                  <Menu.Item
                    icon="check-circle"
                    onPress={() => {
                      setActivityMenuVisible(false);
                      handleMarkAsRead(item.id);
                    }}
                    title={translate('activities.markAsRead', 'Mark as Read')}
                  />
                )}
                <Menu.Item
                  icon="delete"
                  onPress={() => {
                    setActivityMenuVisible(false);
                    handleDeleteActivity(item.id);
                  }}
                  title={translate('common.delete', 'Delete')}
                />
              </Menu>
            </View>
            {!item.read && <View style={styles.unreadIndicator} />}
          </Surface>
        </TouchableRipple>
        {index < activities.length - 1 && <Divider />}
      </Animatable.View>
    );
  };

  const renderFooter = () => {
    if (!loading || refreshing) return null;

    return (
      <View style={styles.footerLoader}>
        <ActivityIndicator size="small" color="#1976d2" />
        <Text style={styles.footerText}>{translate('common.loadingMore')}</Text>
      </View>
    );
  };

  const renderEmpty = () => {
    if (loading && !refreshing) return null;

    return (
      <View style={styles.emptyContainer}>
        <MaterialCommunityIcons name="bell-off-outline" size={64} color="#757575" />
        <Title style={styles.emptyTitle}>{translate('activities.noActivities')}</Title>
        <Paragraph style={styles.emptyText}>{translate('activities.noActivitiesDescription')}</Paragraph>
        <Button
          mode="contained"
          onPress={handleClearFilters}
          style={styles.clearFiltersButton}
        >
          {translate('common.clearFilters')}
        </Button>
      </View>
    );
  };

  const renderDatePicker = () => {
    if (!datePickerVisible) return null;

    return (
      <Portal>
        <Modal
          visible={datePickerVisible}
          onDismiss={() => setDatePickerVisible(false)}
          transparent
          animationType="fade"
        >
          <View style={styles.datePickerModalContainer}>
            <Surface style={styles.datePickerModalContent}>
              <View style={styles.datePickerHeader}>
                <Text style={styles.datePickerTitle}>
                  {datePickerMode === 'start' ? translate('activities.startDate') : translate('activities.endDate')}
                </Text>
                <IconButton
                  icon="close"
                  size={20}
                  onPress={() => setDatePickerVisible(false)}
                />
              </View>

              <EthiopianDatePicker
                value={datePickerMode === 'start' ? (startDate ? new Date(startDate) : new Date()) : (endDate ? new Date(endDate) : new Date())}
                onChange={(date) => {
                  if (datePickerMode === 'start') {
                    setStartDate(date.toISOString());
                  } else {
                    setEndDate(date.toISOString());
                  }
                  setDatePickerVisible(false);
                  fetchActivities(1, true);
                }}
                display="calendar"
                showYearMonthSelector={true}
                showToday={true}
              />
            </Surface>
          </View>
        </Modal>
      </Portal>
    );
  };

  return (
    <View style={styles.container}>
      {/* Sidebar */}
      <AdminSidebar
        drawerAnim={drawerAnim}
        activeSidebarItem={activeSidebarItem}
        setActiveSidebarItem={setActiveSidebarItem}
        toggleDrawer={toggleDrawer}
      />

      {/* Backdrop */}
      <SidebarBackdrop
        visible={drawerOpen}
        onPress={toggleDrawer}
        fadeAnim={backdropFadeAnim}
      />

      {/* Admin App Header */}
      <AdminAppHeader
        title={translate('activities.title')}
        onMenuPress={toggleDrawer}
      />
      <Surface style={styles.header}>
        <View style={styles.searchContainer}>
          <Searchbar
            placeholder={translate('common.search')}
            onChangeText={setSearchQuery}
            value={searchQuery}
            style={styles.searchBar}
            onSubmitEditing={handleSearch}
          />
          <Menu
            visible={filterMenuVisible}
            onDismiss={() => setFilterMenuVisible(false)}
            anchor={
              <Button
                mode="outlined"
                onPress={() => setFilterMenuVisible(true)}
                icon="filter-variant"
                style={styles.filterButton}
              >
                {translate('common.filter')}
              </Button>
            }
          >
            <Menu.Item
              title={translate('activities.filterByType')}
              disabled
              titleStyle={styles.menuHeader}
            />
            <Divider />
            <Menu.Item
              title={translate('activities.allTypes')}
              onPress={() => {
                setFilterType(null);
                setFilterMenuVisible(false);
                fetchActivities(1, true);
              }}
              titleStyle={filterType === null ? styles.selectedFilter : null}
            />
            <Menu.Item
              title={translate('activities.userType')}
              onPress={() => {
                setFilterType('user');
                setFilterMenuVisible(false);
                fetchActivities(1, true);
              }}
              titleStyle={filterType === 'user' ? styles.selectedFilter : null}
            />
            <Menu.Item
              title={translate('activities.classType')}
              onPress={() => {
                setFilterType('class');
                setFilterMenuVisible(false);
                fetchActivities(1, true);
              }}
              titleStyle={filterType === 'class' ? styles.selectedFilter : null}
            />
            <Menu.Item
              title={translate('activities.examType')}
              onPress={() => {
                setFilterType('exam');
                setFilterMenuVisible(false);
                fetchActivities(1, true);
              }}
              titleStyle={filterType === 'exam' ? styles.selectedFilter : null}
            />
            <Divider />
            <Menu.Item
              title={translate('activities.filterByStatus')}
              disabled
              titleStyle={styles.menuHeader}
            />
            <Divider />
            <Menu.Item
              title={translate('activities.allStatus')}
              onPress={() => {
                setFilterRead(null);
                setFilterMenuVisible(false);
                fetchActivities(1, true);
              }}
              titleStyle={filterRead === null ? styles.selectedFilter : null}
            />
            <Menu.Item
              title={translate('activities.read')}
              onPress={() => {
                setFilterRead(true);
                setFilterMenuVisible(false);
                fetchActivities(1, true);
              }}
              titleStyle={filterRead === true ? styles.selectedFilter : null}
            />
            <Menu.Item
              title={translate('activities.unread')}
              onPress={() => {
                setFilterRead(false);
                setFilterMenuVisible(false);
                fetchActivities(1, true);
              }}
              titleStyle={filterRead === false ? styles.selectedFilter : null}
            />
            <Divider />
            <Menu.Item
              title={translate('activities.filterByDate')}
              disabled
              titleStyle={styles.menuHeader}
            />
            <Divider />
            <Menu.Item
              title={translate('activities.startDate')}
              onPress={() => {
                setDatePickerMode('start');
                setDatePickerVisible(true);
                setFilterMenuVisible(false);
              }}
              right={() => <Text>{startDate ? formatDate(new Date(startDate)) : '-'}</Text>}
            />
            <Menu.Item
              title={translate('activities.endDate')}
              onPress={() => {
                setDatePickerMode('end');
                setDatePickerVisible(true);
                setFilterMenuVisible(false);
              }}
              right={() => <Text>{endDate ? formatDate(new Date(endDate)) : '-'}</Text>}
            />
            <Divider />
            <Menu.Item
              title={translate('common.applyFilters')}
              onPress={() => {
                setFilterMenuVisible(false);
                fetchActivities(1, true);
              }}
              titleStyle={styles.applyFilters}
            />
          </Menu>
        </View>

        {(filterType || filterRead !== null || startDate || endDate) && (
          <View style={styles.activeFilters}>
            <Text style={styles.activeFiltersLabel}>{translate('common.activeFilters')}:</Text>
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              {filterType && (
                <Chip
                  style={styles.filterChip}
                  onClose={() => {
                    setFilterType(null);
                    fetchActivities(1, true);
                  }}
                  icon={getActivityIcon(filterType)}
                >
                  {filterType}
                </Chip>
              )}
              {filterRead !== null && (
                <Chip
                  style={styles.filterChip}
                  onClose={() => {
                    setFilterRead(null);
                    fetchActivities(1, true);
                  }}
                  icon={filterRead ? 'eye' : 'eye-off'}
                >
                  {filterRead ? translate('activities.read') : translate('activities.unread')}
                </Chip>
              )}
              {startDate && endDate && (
                <Chip
                  style={styles.filterChip}
                  onClose={() => {
                    setStartDate(null);
                    setEndDate(null);
                    fetchActivities(1, true);
                  }}
                  icon="calendar-range"
                >
                  {formatDate(new Date(startDate))} - {formatDate(new Date(endDate))}
                </Chip>
              )}
              <Button
                mode="text"
                onPress={handleClearFilters}
                style={styles.clearButton}
              >
                {translate('common.clearAll')}
              </Button>
            </ScrollView>
          </View>
        )}
      </Surface>

      <FlatList
        data={activities}
        renderItem={renderActivityItem}
        keyExtractor={item => item.id}
        contentContainerStyle={styles.listContent}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={['#2196F3']}
          />
        }
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.5}
        ListFooterComponent={renderFooter}
        ListEmptyComponent={renderEmpty}
      />

      <Menu
        visible={activityMenuVisible}
        onDismiss={() => setActivityMenuVisible(false)}
        anchor={{ x: 0, y: 0 }}
        style={styles.activityMenu}
      >
        <Menu.Item
          title={translate('activities.viewDetails')}
          icon="eye"
          onPress={() => {
            setActivityMenuVisible(false);
            if (selectedActivity) {
              handleActivityPress(selectedActivity);
            }
          }}
        />
        {selectedActivity && !selectedActivity.read && (
          <Menu.Item
            title={translate('activities.markAsRead')}
            icon="check"
            onPress={() => {
              setActivityMenuVisible(false);
              if (selectedActivity) {
                handleMarkAsRead(selectedActivity.id);
              }
            }}
          />
        )}
        <Menu.Item
          title={translate('activities.delete')}
          icon="delete"
          onPress={() => {
            setActivityMenuVisible(false);
            if (selectedActivity) {
              handleDeleteActivity(selectedActivity.id);
            }
          }}
        />
      </Menu>

      {renderDatePicker()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    padding: 16,
    elevation: 4,
    marginTop: -20, // Adjust for the AdminAppHeader
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  searchBar: {
    flex: 1,
    marginRight: 8,
    elevation: 0,
  },
  filterButton: {
    height: 40,
    justifyContent: 'center',
  },
  activeFilters: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 12,
  },
  activeFiltersLabel: {
    marginRight: 8,
    fontSize: 12,
    color: '#757575',
  },
  filterChip: {
    marginRight: 8,
  },
  clearButton: {
    marginLeft: 8,
  },
  listContent: {
    padding: 16,
    paddingTop: 8,
  },
  activityItem: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: 'white',
  },
  unreadActivity: {
    backgroundColor: 'rgba(33, 150, 243, 0.05)',
  },
  activityItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  activityIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  activityTextContainer: {
    flex: 1,
  },
  activityHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  activityTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    flex: 1,
  },
  unreadDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#2196F3',
    marginLeft: 8,
  },
  activityDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 6,
  },
  activityFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  activityTimestamp: {
    fontSize: 12,
    color: '#999',
    marginRight: 16,
  },
  activityUser: {
    fontSize: 12,
    color: '#999',
    marginRight: 16,
  },
  activityType: {
    marginLeft: 'auto',
  },
  typeChip: {
    height: 24,
  },
  footerLoader: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
  },
  footerText: {
    marginTop: 8,
    color: '#757575',
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 32,
  },
  emptyTitle: {
    marginTop: 16,
    textAlign: 'center',
  },
  emptyText: {
    textAlign: 'center',
    color: '#757575',
    marginTop: 8,
    marginBottom: 16,
  },
  clearFiltersButton: {
    marginTop: 16,
  },
  menuHeader: {
    fontWeight: 'bold',
    color: '#757575',
  },
  selectedFilter: {
    fontWeight: 'bold',
    color: '#2196F3',
  },
  applyFilters: {
    fontWeight: 'bold',
    color: '#4CAF50',
  },
  activityMenu: {
    marginTop: 40,
  },
  datePickerModalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    padding: 20,
  },
  datePickerModalContent: {
    width: '100%',
    maxWidth: 400,
    borderRadius: 8,
    padding: 16,
    elevation: 4,
  },
  datePickerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  datePickerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
});

export default ActivityManagement;
