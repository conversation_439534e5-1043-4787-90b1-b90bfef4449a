import React from 'react';
import {
    Paper,
    Typography,
    Box,
    Grid,
    LinearProgress,
    Card,
    CardContent,
    Radar,
    RadarChart,
    PolarGrid,
    PolarAngleAxis,
    ResponsiveContainer,
    Tooltip
} from '@mui/material';
import { useTranslation } from '../../hooks/useTranslation';
import { styled } from '@mui/material/styles';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import TrendingDownIcon from '@mui/icons-material/TrendingDown';
import TrendingFlatIcon from '@mui/icons-material/TrendingFlat';

const StyledPaper = styled(Paper)(({ theme }) => ({
    padding: theme.spacing(3),
    margin: theme.spacing(2)
}));

const ProgressBar = styled(LinearProgress)(({ theme, value }) => ({
    height: 10,
    borderRadius: 5,
    backgroundColor: theme.palette.grey[200],
    '& .MuiLinearProgress-bar': {
        borderRadius: 5,
        backgroundColor: value < 50 ? theme.palette.error.main :
                        value < 70 ? theme.palette.warning.main :
                        theme.palette.success.main
    }
}));

const TrendIcon = ({ trend }) => {
    switch(trend) {
        case 'up':
            return <TrendingUpIcon color="success" />;
        case 'down':
            return <TrendingDownIcon color="error" />;
        default:
            return <TrendingFlatIcon color="action" />;
    }
};

const ProgressReport = ({
    studentInfo,
    academicProgress,
    skillsProgress,
    goals,
    recommendations,
    schoolInfo,
    reportPeriod
}) => {
    const {
        t,
        language,
        formatDate,
        formatNumber,
        getGradeName
    } = useTranslation();

    const renderHeader = () => (
        <Box textAlign="center" mb={3}>
            <Typography variant="h5" gutterBottom>
                {schoolInfo.name[language]}
            </Typography>
            <Typography variant="h6" gutterBottom>
                {t('student_progress_report')}
            </Typography>
            <Typography variant="body1">
                {t('report_period')}: {reportPeriod}
            </Typography>
        </Box>
    );

    const renderStudentInfo = () => (
        <Card variant="outlined" sx={{ mb: 3 }}>
            <CardContent>
                <Grid container spacing={2}>
                    <Grid item xs={12} md={6}>
                        <Typography variant="subtitle2" color="textSecondary">
                            {t('student_name')}
                        </Typography>
                        <Typography variant="body1" gutterBottom>
                            {studentInfo.name[language]}
                        </Typography>
                        <Typography variant="subtitle2" color="textSecondary">
                            {t('grade_section')}
                        </Typography>
                        <Typography variant="body1">
                            {getGradeName(studentInfo.grade)} - {studentInfo.section}
                        </Typography>
                    </Grid>
                    <Grid item xs={12} md={6}>
                        <Typography variant="subtitle2" color="textSecondary">
                            {t('student_id')}
                        </Typography>
                        <Typography variant="body1" gutterBottom>
                            {studentInfo.id}
                        </Typography>
                        <Typography variant="subtitle2" color="textSecondary">
                            {t('academic_year')}
                        </Typography>
                        <Typography variant="body1">
                            {studentInfo.academicYear}
                        </Typography>
                    </Grid>
                </Grid>
            </CardContent>
        </Card>
    );

    const renderAcademicProgress = () => (
        <Box mb={3}>
            <Typography variant="h6" gutterBottom>
                {t('academic_progress')}
            </Typography>
            <Grid container spacing={2}>
                {academicProgress.subjects.map((subject, index) => (
                    <Grid item xs={12} key={index}>
                        <Card variant="outlined">
                            <CardContent>
                                <Grid container spacing={2} alignItems="center">
                                    <Grid item xs={12} md={3}>
                                        <Typography variant="subtitle1">
                                            {subject.name[language]}
                                        </Typography>
                                    </Grid>
                                    <Grid item xs={12} md={7}>
                                        <Box mb={1}>
                                            <ProgressBar
                                                variant="determinate"
                                                value={subject.progress}
                                            />
                                        </Box>
                                        <Typography variant="body2" color="textSecondary">
                                            {formatNumber(subject.progress)}% {t('complete')}
                                        </Typography>
                                    </Grid>
                                    <Grid item xs={12} md={2}>
                                        <Box display="flex" alignItems="center" gap={1}>
                                            <TrendIcon trend={subject.trend} />
                                            <Typography variant="body2">
                                                {formatNumber(subject.improvement)}%
                                            </Typography>
                                        </Box>
                                    </Grid>
                                </Grid>
                            </CardContent>
                        </Card>
                    </Grid>
                ))}
            </Grid>
        </Box>
    );

    const renderSkillsProgress = () => (
        <Box mb={3}>
            <Typography variant="h6" gutterBottom>
                {t('skills_development')}
            </Typography>
            <Card variant="outlined">
                <CardContent>
                    <Box height={300}>
                        <ResponsiveContainer width="100%" height="100%">
                            <RadarChart data={skillsProgress}>
                                <PolarGrid />
                                <PolarAngleAxis
                                    dataKey="skill"
                                    tick={({ payload, x, y }) => (
                                        <text
                                            x={x}
                                            y={y}
                                            textAnchor="middle"
                                            fill="#666"
                                        >
                                            {t(`skill_${payload.value}`)}
                                        </text>
                                    )}
                                />
                                <Radar
                                    name={t('current_level')}
                                    dataKey="current"
                                    stroke="#8884d8"
                                    fill="#8884d8"
                                    fillOpacity={0.6}
                                />
                                <Radar
                                    name={t('previous_level')}
                                    dataKey="previous"
                                    stroke="#82ca9d"
                                    fill="#82ca9d"
                                    fillOpacity={0.6}
                                />
                                <Tooltip />
                            </RadarChart>
                        </ResponsiveContainer>
                    </Box>
                </CardContent>
            </Card>
        </Box>
    );

    const renderGoals = () => (
        <Box mb={3}>
            <Typography variant="h6" gutterBottom>
                {t('learning_goals')}
            </Typography>
            <Grid container spacing={2}>
                {goals.map((goal, index) => (
                    <Grid item xs={12} md={6} key={index}>
                        <Card variant="outlined">
                            <CardContent>
                                <Typography variant="subtitle1" gutterBottom>
                                    {goal.title[language]}
                                </Typography>
                                <Typography variant="body2" color="textSecondary" paragraph>
                                    {goal.description[language]}
                                </Typography>
                                <Box>
                                    <Typography variant="body2" gutterBottom>
                                        {t('progress')}: {formatNumber(goal.progress)}%
                                    </Typography>
                                    <ProgressBar
                                        variant="determinate"
                                        value={goal.progress}
                                    />
                                </Box>
                            </CardContent>
                        </Card>
                    </Grid>
                ))}
            </Grid>
        </Box>
    );

    const renderRecommendations = () => (
        <Box mb={3}>
            <Typography variant="h6" gutterBottom>
                {t('recommendations')}
            </Typography>
            <Grid container spacing={2}>
                {recommendations.map((recommendation, index) => (
                    <Grid item xs={12} key={index}>
                        <Card variant="outlined">
                            <CardContent>
                                <Typography variant="subtitle1" gutterBottom>
                                    {recommendation.area[language]}
                                </Typography>
                                <Typography variant="body2" paragraph>
                                    {recommendation.suggestion[language]}
                                </Typography>
                                <Typography variant="body2" color="textSecondary">
                                    {t('action_items')}:
                                </Typography>
                                <ul>
                                    {recommendation.actionItems.map((item, itemIndex) => (
                                        <li key={itemIndex}>
                                            <Typography variant="body2">
                                                {item[language]}
                                            </Typography>
                                        </li>
                                    ))}
                                </ul>
                            </CardContent>
                        </Card>
                    </Grid>
                ))}
            </Grid>
        </Box>
    );

    const renderSignatures = () => (
        <Grid container spacing={4} mt={4}>
            <Grid item xs={12} md={4}>
                <Typography variant="body2" gutterBottom>
                    {t('class_teacher')}
                </Typography>
                <Box mt={4} borderTop={1} width={200} />
                <Typography variant="body2" mt={1}>
                    {formatDate(new Date())}
                </Typography>
            </Grid>
            <Grid item xs={12} md={4}>
                <Typography variant="body2" gutterBottom>
                    {t('academic_coordinator')}
                </Typography>
                <Box mt={4} borderTop={1} width={200} />
                <Typography variant="body2" mt={1}>
                    {formatDate(new Date())}
                </Typography>
            </Grid>
            <Grid item xs={12} md={4}>
                <Typography variant="body2" gutterBottom>
                    {t('parent_signature')}
                </Typography>
                <Box mt={4} borderTop={1} width={200} />
                <Typography variant="body2" mt={1}>
                    {t('signature_date')}
                </Typography>
            </Grid>
        </Grid>
    );

    return (
        <StyledPaper>
            {renderHeader()}
            {renderStudentInfo()}
            {renderAcademicProgress()}
            {renderSkillsProgress()}
            {renderGoals()}
            {renderRecommendations()}
            {renderSignatures()}
        </StyledPaper>
    );
};

export default ProgressReport;
