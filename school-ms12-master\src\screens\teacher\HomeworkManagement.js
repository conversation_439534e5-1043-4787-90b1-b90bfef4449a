import React, { useState, useEffect } from 'react';
import { View, ScrollView, StyleSheet } from 'react-native';
import {
  DataTable,
  Card,
  Title,
  Button,
  IconButton,
  Text,
  ActivityIndicator,
  Snackbar,
  Portal,
  Dialog,
  TextInput,
  FAB,
  List,
  Chip
} from 'react-native-paper';
import { db } from '../../config/firebase';
import {
  collection,
  query,
  where,
  getDocs,
  addDoc,
  orderBy,
  Timestamp,
  doc,
  updateDoc
} from 'firebase/firestore';
import EthiopianDatePicker from '../../components/common/EthiopianDatePicker';
import { useTranslation } from '../../hooks/useTranslation';

const HomeworkManagement = ({ route, navigation }) => {
  const { t, language, getTextStyle } = useTranslation();
  // Add default values and error handling for route params
  const params = route?.params || {};
  const classId = params.classId;
  const className = params.className;
  const sectionName = params.sectionName;

  // Redirect to ClassManagement if required params are missing
  useEffect(() => {
    if (!classId || !className || !sectionName) {
      navigation.replace('ClassManagement');
      return;
    }
  }, [classId, className, sectionName]);

  const [homeworks, setHomeworks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [dialogVisible, setDialogVisible] = useState(false);
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [selectedHomework, setSelectedHomework] = useState(null);
  // State variables for the component
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    dueDate: new Date(),
    subject: '',
    maxScore: '100'
  });

  useEffect(() => {
    if (classId && className && sectionName) {
      fetchHomeworks();
    }
  }, [classId, className, sectionName]);

  const fetchHomeworks = async () => {
    try {
      setLoading(true);
      setError(null);
      const homeworkRef = collection(db, 'homework');
      const q = query(
        homeworkRef,
        where('classId', '==', classId),
        where('sectionName', '==', sectionName),
        orderBy('dueDate', 'desc')
      );

      const snapshot = await getDocs(q);
      const homeworkData = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        dueDate: doc.data().dueDate?.toDate() || new Date()
      }));

      setHomeworks(homeworkData);
    } catch (err) {
      console.error('Error fetching homework:', err);
      setError(t('teacher.homework.fetchError'));
      setSnackbarMessage(t('teacher.homework.loadingError'));
      setSnackbarVisible(true);
    } finally {
      setLoading(false);
    }
  };

  const handleDateChange = (selectedDate) => {
    setFormData(prev => ({ ...prev, dueDate: selectedDate }));
  };

  const handleSubmit = async () => {
    try {
      setLoading(true);
      const homeworkData = {
        ...formData,
        classId,
        className,
        sectionName,
        dueDate: Timestamp.fromDate(formData.dueDate),
        status: 'active',
        createdAt: Timestamp.now()
      };

      if (selectedHomework) {
        // Update existing homework
        await updateDoc(doc(db, 'homework', selectedHomework.id), homeworkData);
        setSnackbarMessage(t('teacher.homework.homeworkUpdated'));
      } else {
        // Create new homework
        await addDoc(collection(db, 'homework'), homeworkData);
        setSnackbarMessage(t('teacher.homework.homeworkAssigned'));
      }

      setSnackbarVisible(true);
      setDialogVisible(false);
      clearForm();
      fetchHomeworks();
    } catch (err) {
      console.error('Error saving homework:', err);
      setSnackbarMessage(t('teacher.homework.saveError'));
      setSnackbarVisible(true);
    } finally {
      setLoading(false);
    }
  };

  const editHomework = (homework) => {
    setSelectedHomework(homework);
    setFormData({
      title: homework.title,
      description: homework.description,
      dueDate: homework.dueDate,
      subject: homework.subject,
      maxScore: homework.maxScore.toString()
    });
    setDialogVisible(true);
  };

  const clearForm = () => {
    setSelectedHomework(null);
    setFormData({
      title: '',
      description: '',
      dueDate: new Date(),
      subject: '',
      maxScore: '100'
    });
  };

  const getStatusColor = (dueDate) => {
    const now = new Date();
    const due = new Date(dueDate);
    if (due < now) return '#ff5252'; // overdue
    if (due.getTime() - now.getTime() < 86400000) return '#ffd740'; // due within 24 hours
    return '#4caf50'; // due later
  };

  if (!classId || !className || !sectionName) {
    return null; // Component will redirect in useEffect
  }

  if (loading && homeworks.length === 0) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" />
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <Text style={[styles.errorText, getTextStyle()]}>{error}</Text>
        <Button mode="contained" onPress={fetchHomeworks}>
          {t('common.retry')}
        </Button>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Card style={styles.headerCard}>
        <Card.Content>
          <Title style={getTextStyle()}>{t('teacher.homework.class')} {className} - {t('teacher.homework.section')} {sectionName}</Title>
          <Text style={getTextStyle()}>{t('teacher.homework.title')}</Text>
        </Card.Content>
      </Card>

      <ScrollView>
        {homeworks.map((homework) => (
          <Card key={homework.id} style={styles.homeworkCard}>
            <Card.Content>
              <View style={styles.homeworkHeader}>
                <Title>{homework.title}</Title>
                <Chip
                  style={[styles.statusChip, { backgroundColor: getStatusColor(homework.dueDate) }]}
                >
                  {homework.dueDate.toLocaleDateString()}
                </Chip>
              </View>
              <Text style={[styles.subject, getTextStyle()]}>{t('teacher.homework.subject')}: {homework.subject}</Text>
              <Text style={[styles.description, getTextStyle()]}>{homework.description}</Text>
              <View style={styles.scoreContainer}>
                <Text style={getTextStyle()}>{t('teacher.homework.maxScore')}: {homework.maxScore}</Text>
                <IconButton
                  icon="pencil"
                  size={20}
                  onPress={() => editHomework(homework)}
                />
              </View>
            </Card.Content>
          </Card>
        ))}

        {homeworks.length === 0 && (
          <Card style={styles.emptyCard}>
            <Card.Content>
              <Text style={[styles.emptyText, getTextStyle()]}>{t('teacher.homework.noHomework')}</Text>
            </Card.Content>
          </Card>
        )}
      </ScrollView>

      <FAB
        style={styles.fab}
        icon="plus"
        onPress={() => {
          clearForm();
          setDialogVisible(true);
        }}
      />

      <Portal>
        <Dialog visible={dialogVisible} onDismiss={() => setDialogVisible(false)}>
          <Dialog.Title style={getTextStyle()}>
            {selectedHomework ? t('teacher.homework.editHomework') : t('teacher.homework.createHomework')}
          </Dialog.Title>
          <Dialog.Content>
            <TextInput
              label={t('teacher.homework.title')}
              value={formData.title}
              onChangeText={title => setFormData(prev => ({ ...prev, title }))}
              mode="outlined"
              style={styles.input}
            />
            <TextInput
              label={t('teacher.homework.subject')}
              value={formData.subject}
              onChangeText={subject => setFormData(prev => ({ ...prev, subject }))}
              mode="outlined"
              style={styles.input}
            />
            <TextInput
              label={t('teacher.homework.description')}
              value={formData.description}
              onChangeText={description => setFormData(prev => ({ ...prev, description }))}
              mode="outlined"
              multiline
              numberOfLines={3}
              style={styles.input}
            />
            <TextInput
              label={t('teacher.homework.maxScore')}
              value={formData.maxScore}
              onChangeText={maxScore => setFormData(prev => ({ ...prev, maxScore }))}
              keyboardType="numeric"
              mode="outlined"
              style={styles.input}
            />
            <View style={styles.datePickerContainer}>
              <Text style={[styles.datePickerLabel, getTextStyle()]}>{t('teacher.homework.dueDate')}</Text>
              <EthiopianDatePicker
                value={formData.dueDate}
                onChange={handleDateChange}
                label={t('teacher.homework.selectDueDate')}
                language={language}
                display="spinner"
                themeType="light"
                buttonMode="outlined"
                showIcon={true}
                iconPosition="right"
              />
            </View>
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={() => setDialogVisible(false)}>{t('common.cancel')}</Button>
            <Button onPress={handleSubmit} loading={loading}>{t('common.save')}</Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>

      {/* Ethiopian Date Picker is used instead of DateTimePicker */}

      <Snackbar
        visible={snackbarVisible}
        onDismiss={() => setSnackbarVisible(false)}
        duration={3000}
      >
        {snackbarMessage}
      </Snackbar>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  headerCard: {
    margin: 16,
    marginBottom: 8,
  },
  homeworkCard: {
    margin: 16,
    marginTop: 8,
    marginBottom: 8,
  },
  homeworkHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  subject: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  description: {
    marginVertical: 8,
  },
  scoreContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 8,
  },
  statusChip: {
    minWidth: 100,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  errorText: {
    color: 'red',
    marginBottom: 16,
    textAlign: 'center',
  },
  emptyCard: {
    margin: 16,
  },
  emptyText: {
    textAlign: 'center',
    color: '#666',
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
  },
  input: {
    marginBottom: 16,
  },
  dateButton: {
    marginTop: 8,
  },
  datePickerContainer: {
    marginBottom: 16,
  },
  datePickerLabel: {
    fontSize: 14,
    marginBottom: 8,
    color: '#666',
  },
});

export default HomeworkManagement;
