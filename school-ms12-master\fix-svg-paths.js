const fs = require('fs');
const path = require('path');

// Function to recursively find all JS files in a directory
function findJsFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);

  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);

    if (stat.isDirectory() && file !== 'node_modules' && file !== '.git') {
      findJsFiles(filePath, fileList);
    } else if (stat.isFile() && (file.endsWith('.js') || file.endsWith('.jsx'))) {
      fileList.push(filePath);
    }
  });

  return fileList;
}

// Function to fix SVG path issues in a file
function fixSvgPathIssues(filePath) {
  console.log(`Processing ${filePath}...`);
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;

  // Check if the file uses SVG paths
  if (content.includes('react-native-svg') || 
      content.includes('Path') || 
      content.includes('Svg') || 
      content.includes('d=')) {
    
    // Fix any path data with Infinity values
    const pathRegex = /d=["']([^"']+)["']/g;
    let match;
    while ((match = pathRegex.exec(content)) !== null) {
      const pathData = match[1];
      
      // Check if the path data contains Infinity
      if (pathData.includes('Infinity')) {
        const fixedPathData = pathData.replace(/[-]?Infinity/g, '0');
        content = content.replace(pathData, fixedPathData);
        console.log(`Fixed Infinity in path data: ${pathData} -> ${fixedPathData}`);
        modified = true;
      }
    }
  }

  // Save the file if modified
  if (modified) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`Fixed SVG path issues in ${filePath}`);
    return true;
  }

  return false;
}

// Main function
function main() {
  const srcDir = path.join(__dirname, 'src');
  const jsFiles = findJsFiles(srcDir);
  let fixedCount = 0;

  jsFiles.forEach(file => {
    if (fixSvgPathIssues(file)) {
      fixedCount++;
    }
  });

  console.log(`\nFixed SVG path issues in ${fixedCount} files.`);
}

main();
