name: flutter_schoolms
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.6.0

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # UI and Material Design
  cupertino_icons: ^1.0.8
  material_color_utilities: ^0.11.1

  # Firebase
  firebase_core: ^3.6.0
  firebase_auth: ^5.3.1
  cloud_firestore: ^5.4.3
  firebase_storage: ^12.3.2
  firebase_messaging: ^15.1.3
  firebase_analytics: ^11.3.3
  firebase_crashlytics: ^4.1.3

  # State Management
  provider: ^6.1.2
  riverpod: ^2.5.1
  flutter_riverpod: ^2.5.1

  # Navigation
  go_router: ^14.6.1
  auto_route: ^9.2.2

  # HTTP and API
  http: ^1.2.2
  dio: ^5.7.0
  retrofit: ^4.4.1

  # Local Storage
  shared_preferences: ^2.3.2
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  sqflite: ^2.4.0

  # Image and Media
  image_picker: ^1.1.2
  cached_network_image: ^3.4.1
  photo_view: ^0.15.0
  image: ^4.2.0

  # Charts and Analytics
  fl_chart: ^0.69.0
  syncfusion_flutter_charts: ^27.1.58
  charts_flutter: ^0.12.0

  # Date and Time
  intl: ^0.19.0
  table_calendar: ^3.1.2
  calendar_view: ^1.2.0

  # Utilities
  uuid: ^4.5.1
  path_provider: ^2.1.4
  permission_handler: ^11.3.1
  device_info_plus: ^10.1.2
  package_info_plus: ^8.0.2
  url_launcher: ^6.3.1
  share_plus: ^10.0.2

  # Notifications
  flutter_local_notifications: ^17.2.3
  awesome_notifications: ^0.9.3+1

  # PDF and Documents
  pdf: ^3.11.1
  printing: ^5.13.2
  open_file: ^3.5.7
  file_picker: ^8.1.2

  # Animations
  lottie: ^3.1.2
  flutter_animate: ^4.5.0

  # Connectivity
  connectivity_plus: ^6.0.5
  internet_connection_checker: ^2.0.0

  # Security
  crypto: ^3.0.5
  encrypt: ^5.0.3

  # Internationalization
  flutter_localizations:
    sdk: flutter
  easy_localization: ^3.0.7

  # Forms and Validation
  reactive_forms: ^17.0.1
  form_validator: ^2.1.1

  # Swipe and Gestures
  flutter_slidable: ^3.1.1
  swipe_to: ^1.0.6

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # Assets
  assets:
    - assets/images/
    - assets/icons/
    - assets/translations/
    - assets/sounds/
    - assets/fonts/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
