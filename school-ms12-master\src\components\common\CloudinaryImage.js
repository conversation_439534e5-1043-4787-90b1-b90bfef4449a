import React, { useState, useEffect } from 'react';
import { Image, View, ActivityIndicator, StyleSheet } from 'react-native';
import CloudinaryService from '../../services/CloudinaryService';

/**
 * CloudinaryImage component for displaying images from Cloudinary
 * This component supports both Cloudinary public IDs and Firebase Storage URLs
 *
 * @param {Object} props - Component props
 * @param {string} props.publicId - Cloudinary public ID of the image (deprecated, use source instead)
 * @param {string} props.source - Cloudinary public ID or Firebase Storage URL
 * @param {string} props.fallbackUri - Fallback image URI to display if the main image fails to load
 * @param {Object} props.style - Style object for the image
 * @param {number} props.width - Width of the image
 * @param {number} props.height - Height of the image
 * @param {string} props.resizeMode - Resize mode for the image (cover, contain, stretch, etc.)
 * @param {boolean} props.circle - Whether to display the image as a circle
 * @param {number} props.borderRadius - Border radius for the image
 * @param {boolean} props.showLoadingIndicator - Whether to show a loading indicator while the image is loading
 * @param {Function} props.onLoad - Callback function to call when the image is loaded
 * @param {Function} props.onError - Callback function to call when the image fails to load
 */
const CloudinaryImage = ({
  publicId,
  source,
  fallbackUri,
  style = {},
  width,
  height,
  resizeMode = 'cover',
  circle = false,
  borderRadius,
  showLoadingIndicator = true,
  onLoad,
  onError,
  ...props
}) => {
  // For backward compatibility, use publicId if source is not provided
  const imageSource = source || publicId;
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);
  const [imageUrl, setImageUrl] = useState(null);

  useEffect(() => {
    if (!imageSource) {
      setError(true);
      setLoading(false);
      return;
    }

    try {
      // Generate Cloudinary URL with transformations
      const options = {
        width: width || style.width,
        height: height || style.height,
        crop: 'fill',
        quality: 'auto'
      };

      // Add border radius if specified or if circle is true
      if (circle) {
        options.radius = 'max';
      } else if (borderRadius) {
        options.radius = borderRadius;
      }

      // Get the URL using CloudinaryService
      const url = CloudinaryService.getImageUrl(imageSource, options);
      setImageUrl(url);
    } catch (err) {
      console.error('Error generating Cloudinary URL:', err);
      setError(true);
      if (onError) onError(err);
    }
  }, [imageSource, width, height, circle, borderRadius, style.width, style.height]);

  const handleLoad = () => {
    setLoading(false);
    if (onLoad) onLoad();
  };

  const handleError = (err) => {
    console.error('Error loading Cloudinary image:', err);
    setError(true);
    setLoading(false);
    if (onError) onError(err);
  };

  // Calculate container style
  const containerStyle = {
    ...style,
    width: width || style.width,
    height: height || style.height,
    borderRadius: circle ? (width || style.width || height || style.height) / 2 : borderRadius || style.borderRadius || 0,
    overflow: 'hidden',
    justifyContent: 'center',
    alignItems: 'center',
  };

  // Calculate image style
  const imageStyle = {
    width: '100%',
    height: '100%',
    resizeMode,
  };

  // If there's an error and a fallback URI is provided, show the fallback image
  if (error && fallbackUri) {
    return (
      <Image
        source={{ uri: fallbackUri }}
        style={containerStyle}
        resizeMode={resizeMode}
        {...props}
      />
    );
  }

  return (
    <View style={containerStyle}>
      {imageUrl && (
        <Image
          source={{ uri: imageUrl }}
          style={imageStyle}
          onLoad={handleLoad}
          onError={handleError}
          {...props}
        />
      )}

      {loading && showLoadingIndicator && (
        <ActivityIndicator
          size="small"
          color="#999"
          style={styles.loadingIndicator}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  loadingIndicator: {
    position: 'absolute',
  }
});

export default CloudinaryImage;
