const fs = require('fs');
const path = require('path');

// Function to recursively find all JS files in a directory
function findJsFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);

  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);

    if (stat.isDirectory() && file !== 'node_modules' && file !== '.git') {
      findJsFiles(filePath, fileList);
    } else if (stat.isFile() && (file.endsWith('.js') || file.endsWith('.jsx'))) {
      fileList.push(filePath);
    }
  });

  return fileList;
}

// Function to fix chart errors in a file
function fixChartErrors(filePath) {
  console.log(`Processing ${filePath}...`);
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;

  // Check if the file uses react-native-chart-kit
  if (content.includes('react-native-chart-kit') || 
      content.includes('LineChart') || 
      content.includes('BarChart') || 
      content.includes('PieChart')) {
    
    // Add import for ChartUtils if not already present
    if (!content.includes('ChartUtils')) {
      // Find the last import statement
      const importRegex = /^import .+ from .+;$/gm;
      const imports = [...content.matchAll(importRegex)];
      
      if (imports.length > 0) {
        const lastImport = imports[imports.length - 1][0];
        const lastImportIndex = content.indexOf(lastImport) + lastImport.length;
        
        const importStatement = "\nimport { sanitizeChartData, sanitizeChartDatasets } from '../utils/ChartUtils';";
        
        // Adjust the path based on the file location
        let relativePath = path.relative(path.dirname(filePath), path.join(__dirname, 'src', 'utils')).replace(/\\/g, '/');
        if (!relativePath.startsWith('.')) {
          relativePath = './' + relativePath;
        }
        
        const adjustedImportStatement = importStatement.replace('../utils', relativePath);
        
        content = content.slice(0, lastImportIndex) + adjustedImportStatement + content.slice(lastImportIndex);
        modified = true;
      }
    }
    
    // Fix LineChart components
    const lineChartRegex = /<LineChart[^>]*data=\{([^}]+)\}[^>]*>/g;
    let match;
    while ((match = lineChartRegex.exec(content)) !== null) {
      const dataExpression = match[1];
      
      // Skip if already using sanitizeChartDatasets
      if (dataExpression.includes('sanitizeChartDatasets')) {
        continue;
      }
      
      // Replace the data prop with sanitized version
      const newDataProp = `data={sanitizeChartDatasets(${dataExpression})}`;
      content = content.replace(`data={${dataExpression}}`, newDataProp);
      modified = true;
    }
    
    // Fix BarChart components
    const barChartRegex = /<BarChart[^>]*data=\{([^}]+)\}[^>]*>/g;
    while ((match = barChartRegex.exec(content)) !== null) {
      const dataExpression = match[1];
      
      // Skip if already using sanitizeChartDatasets
      if (dataExpression.includes('sanitizeChartDatasets')) {
        continue;
      }
      
      // Replace the data prop with sanitized version
      const newDataProp = `data={sanitizeChartDatasets(${dataExpression})}`;
      content = content.replace(`data={${dataExpression}}`, newDataProp);
      modified = true;
    }
    
    // Fix PieChart components
    const pieChartRegex = /<PieChart[^>]*data=\{([^}]+)\}[^>]*>/g;
    while ((match = pieChartRegex.exec(content)) !== null) {
      const dataExpression = match[1];
      
      // Skip if already sanitizing
      if (dataExpression.includes('map(item =>') && dataExpression.includes('population')) {
        continue;
      }
      
      // For PieChart, we need to handle the population property
      const newDataProp = `data={${dataExpression}.map(item => ({
        ...item,
        population: isNaN(item.population) || item.population === undefined || 
                    item.population === null || item.population === Infinity || 
                    item.population === -Infinity ? 0 : item.population
      }))}`;
      
      content = content.replace(`data={${dataExpression}}`, newDataProp);
      modified = true;
    }
    
    // Fix dataset definitions
    const datasetRegex = /data:\s*([^,\]]+)(?=[,\]])/g;
    while ((match = datasetRegex.exec(content)) !== null) {
      const dataExpression = match[1].trim();
      
      // Skip if already using sanitizeChartData
      if (dataExpression.includes('sanitizeChartData')) {
        continue;
      }
      
      // Skip if it's just a simple array literal
      if (dataExpression.startsWith('[') && dataExpression.endsWith(']')) {
        continue;
      }
      
      // Replace with sanitized version
      const newDataProp = `data: sanitizeChartData(${dataExpression})`;
      content = content.replace(`data: ${dataExpression}`, newDataProp);
      modified = true;
    }
  }

  // Save the file if modified
  if (modified) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`Fixed chart errors in ${filePath}`);
    return true;
  }

  return false;
}

// Main function
function main() {
  const srcDir = path.join(__dirname, 'src');
  const jsFiles = findJsFiles(srcDir);
  let fixedCount = 0;

  jsFiles.forEach(file => {
    if (fixChartErrors(file)) {
      fixedCount++;
    }
  });

  console.log(`\nFixed chart errors in ${fixedCount} files.`);
}

main();
