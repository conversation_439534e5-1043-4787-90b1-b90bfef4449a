import React from 'react';
import { View, StyleSheet, SafeAreaView, StatusBar } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import AdminAppHeader from './AdminAppHeader';
import AdminSidebar from './AdminSidebar';
import SidebarBackdrop from './SidebarBackdrop';
import { useAdminSidebar } from '../../context/AdminSidebarContext';

/**
 * A wrapper component for admin screens that includes the header and sidebar
 * 
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - The screen content
 * @param {string} props.title - The screen title
 * @param {boolean} props.showBack - Whether to show the back button
 * @param {boolean} props.showNotification - Whether to show the notification icon
 * @returns {React.ReactElement} The AdminScreenWrapper component
 */
const AdminScreenWrapper = ({ 
  children, 
  title, 
  showBack = false,
  showNotification = true
}) => {
  const navigation = useNavigation();
  const { 
    drawerOpen, 
    activeSidebarItem, 
    setActiveSidebarItem, 
    drawerAnim, 
    backdropFadeAnim, 
    toggleDrawer 
  } = useAdminSidebar();

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar backgroundColor={'#1976d2'} barStyle="light-content" />
      
      {/* Admin App Header */}
      <AdminAppHeader
        title={title}
        onMenuPress={toggleDrawer}
        showBack={showBack}
        showNotification={showNotification}
      />
      
      {/* Admin Sidebar */}
      <AdminSidebar
        visible={drawerOpen}
        drawerAnim={drawerAnim}
        toggleDrawer={toggleDrawer}
        navigation={navigation}
        activeSidebarItem={activeSidebarItem}
        setActiveSidebarItem={setActiveSidebarItem}
      />
      
      {/* Sidebar Backdrop */}
      <SidebarBackdrop
        visible={drawerOpen}
        onPress={toggleDrawer}
        fadeAnim={backdropFadeAnim}
      />
      
      {/* Screen Content */}
      <View style={styles.content}>
        {children}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
    padding: 16,
  },
});

export default AdminScreenWrapper;
