import React, { useState, useEffect } from 'react';
import { View, ScrollView, StyleSheet } from 'react-native';
import { Card, Title, Text, DataTable, Portal, Modal, ActivityIndicator } from 'react-native-paper';
import { db, auth } from '../../config/firebase';
import { collection, query, where, getDocs, doc, getDoc } from 'firebase/firestore';
import CustomButton from '../../components/common/CustomButton';
import { Picker } from '@react-native-picker/picker';
import { LineChart } from 'react-native-chart-kit';
import EthiopianCalendar from '../../utils/EthiopianCalendar';
import { sanitizeChartData, sanitizeChartDatasets } from '../../utils/ChartUtils';

const StudentProgressReport = () => {
  const [children, setChildren] = useState([]);
  const [selectedChild, setSelectedChild] = useState('');
  const [subjects, setSubjects] = useState([]);
  const [markSheets, setMarkSheets] = useState([]);
  const [attendance, setAttendance] = useState([]);
  const [loading, setLoading] = useState(true);
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedSubject, setSelectedSubject] = useState(null);

  useEffect(() => {
    fetchChildren();
  }, []);

  useEffect(() => {
    if (selectedChild) {
      fetchSubjects();
      fetchMarkSheets();
      fetchAttendance();
    }
  }, [selectedChild]);

  const fetchChildren = async () => {
    try {
      const childrenRef = collection(db, 'users');
      const q = query(
        childrenRef,
        where('role', '==', 'student'),
        where('parentId', '==', auth.currentUser.uid)
      );
      const querySnapshot = await getDocs(q);
      
      const childrenData = [];
      querySnapshot.forEach((doc) => {
        childrenData.push({ id: doc.id, ...doc.data() });
      });
      
      setChildren(childrenData);
      setLoading(false);
    } catch (error) {
      console.error('Error fetching children:', error);
      setLoading(false);
    }
  };

  const fetchSubjects = async () => {
    try {
      const student = children.find(child => child.id === selectedChild);
      if (student && student.classId) {
        const subjectsRef = collection(db, 'subjects');
        const q = query(subjectsRef, where('classId', '==', student.classId));
        const querySnapshot = await getDocs(q);
        
        const subjectData = [];
        querySnapshot.forEach((doc) => {
          subjectData.push({ id: doc.id, ...doc.data() });
        });
        
        setSubjects(subjectData);
      }
    } catch (error) {
      console.error('Error fetching subjects:', error);
    }
  };

  const fetchMarkSheets = async () => {
    try {
      const markSheetsRef = collection(db, 'markSheets');
      const q = query(markSheetsRef, where('classId', '==', children.find(c => c.id === selectedChild)?.classId));
      const querySnapshot = await getDocs(q);
      
      const markSheetData = [];
      querySnapshot.forEach((doc) => {
        const data = doc.data();
        if (data.marks[selectedChild]) {
          markSheetData.push({
            id: doc.id,
            ...data,
            studentMarks: data.marks[selectedChild],
          });
        }
      });
      
      setMarkSheets(markSheetData);
    } catch (error) {
      console.error('Error fetching mark sheets:', error);
    }
  };

  const fetchAttendance = async () => {
    try {
      const attendanceRef = collection(db, 'attendance');
      const q = query(attendanceRef, where('studentId', '==', selectedChild));
      const querySnapshot = await getDocs(q);
      
      const attendanceData = [];
      querySnapshot.forEach((doc) => {
        attendanceData.push({ id: doc.id, ...doc.data() });
      });
      
      setAttendance(attendanceData);
    } catch (error) {
      console.error('Error fetching attendance:', error);
    }
  };

  const calculateSubjectProgress = (subjectId) => {
    const subjectMarks = markSheets.filter(ms => ms.subjectId === subjectId);
    if (subjectMarks.length === 0) return 0;

    const totalMarks = subjectMarks.reduce((sum, sheet) => {
      return sum + (sheet.studentMarks?.marks ? parseInt(sheet.studentMarks.marks) : 0);
    }, 0);

    const totalMaxMarks = subjectMarks.reduce((sum, sheet) => {
      return sum + (sheet.maxMarks ? parseInt(sheet.maxMarks) : 0);
    }, 0);

    return totalMaxMarks > 0 ? (totalMarks / totalMaxMarks) * 100 : 0;
  };

  const calculateAttendanceRate = () => {
    if (attendance.length === 0) return 0;
    const present = attendance.filter(a => a.status === 'present').length;
    return (present / attendance.length) * 100;
  };

  const getPerformanceData = (subjectId) => {
    const subjectMarks = markSheets
      .filter(ms => ms.subjectId === subjectId)
      .sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));

    return {
      labels: subjectMarks.map(ms => ms.examType),
      datasets: [{
        data: sanitizeChartData(subjectMarks.map(ms => 
          ms.studentMarks?.marks ? (parseInt(ms.studentMarks.marks) / parseInt(ms.maxMarks)) * 100 : 0
        )),
      }],
    };
  };

  const renderSubjectDetails = () => {
    if (!selectedSubject) return null;

    const performanceData = getPerformanceData(selectedSubject.id);

    return (
      <View>
        <Title>{selectedSubject.name}</Title>
        
        <View style={styles.chartContainer}>
          <Text style={styles.chartTitle}>Performance Trend</Text>
          <LineChart
            data={sanitizeChartDatasets(performanceData)}
            width={300}
            height={200}
            chartConfig={{
              backgroundColor: '#ffffff',
              backgroundGradientFrom: '#ffffff',
              backgroundGradientTo: '#ffffff',
              decimalPlaces: 0,
              color: (opacity = 1) => `rgba(33, 150, 243, ${opacity})`,
              style: {
                borderRadius: 16,
              },
            }}
            bezier
            style={styles.chart}
          />
        </View>

        <DataTable>
          <DataTable.Header>
            <DataTable.Title>Exam Type</DataTable.Title>
            <DataTable.Title numeric>Marks</DataTable.Title>
            <DataTable.Title numeric>Max Marks</DataTable.Title>
            <DataTable.Title>Status</DataTable.Title>
          </DataTable.Header>

          {markSheets
            .filter(ms => ms.subjectId === selectedSubject.id)
            .map((markSheet, index) => (
              <DataTable.Row key={index}>
                <DataTable.Cell>{markSheet.examType}</DataTable.Cell>
                <DataTable.Cell numeric>
                  {markSheet.studentMarks?.marks || '-'}
                </DataTable.Cell>
                <DataTable.Cell numeric>{markSheet.maxMarks}</DataTable.Cell>
                <DataTable.Cell>
                  {markSheet.studentMarks?.remarks || '-'}
                </DataTable.Cell>
              </DataTable.Row>
            ))}
        </DataTable>
      </View>
    );
  };

  return (
    <ScrollView style={styles.container}>
      <Card style={styles.card}>
        <Card.Content>
          <Title>Student Progress Report</Title>

          <View style={styles.pickerContainer}>
            <Text style={styles.pickerLabel}>Select Child</Text>
            <Picker
              selectedValue={selectedChild}
              onValueChange={(value) => setSelectedChild(value)}
              style={styles.picker}
            >
              <Picker.Item label="Select a child" value="" />
              {children.map((child) => (
                <Picker.Item 
                  key={child.id} 
                  label={child.displayName} 
                  value={child.id} 
                />
              ))}
            </Picker>
          </View>

          {loading ? (
            <ActivityIndicator style={styles.loader} />
          ) : (
            <View>
              <Card style={styles.summaryCard}>
                <Card.Content>
                  <Title>Overall Summary</Title>
                  <Text>Attendance Rate: {calculateAttendanceRate().toFixed(1)}%</Text>
                  <Text>Total Subjects: {subjects.length}</Text>
                </Card.Content>
              </Card>

              <DataTable>
                <DataTable.Header>
                  <DataTable.Title>Subject</DataTable.Title>
                  <DataTable.Title numeric>Progress</DataTable.Title>
                  <DataTable.Title>Actions</DataTable.Title>
                </DataTable.Header>

                {subjects.map((subject) => (
                  <DataTable.Row key={subject.id}>
                    <DataTable.Cell>{subject.name}</DataTable.Cell>
                    <DataTable.Cell numeric>
                      {calculateSubjectProgress(subject.id).toFixed(1)}%
                    </DataTable.Cell>
                    <DataTable.Cell>
                      <CustomButton
                        mode="outlined"
                        onPress={() => {
                          setSelectedSubject(subject);
                          setModalVisible(true);
                        }}
                        style={styles.actionButton}
                      >
                        View Details
                      </CustomButton>
                    </DataTable.Cell>
                  </DataTable.Row>
                ))}
              </DataTable>
            </View>
          )}
        </Card.Content>
      </Card>

      <Portal>
        <Modal
          visible={modalVisible}
          onDismiss={() => setModalVisible(false)}
          contentContainerStyle={styles.modalContent}
        >
          <ScrollView>
            {renderSubjectDetails()}
            <CustomButton
              mode="contained"
              onPress={() => setModalVisible(false)}
              style={styles.closeButton}
            >
              Close
            </CustomButton>
          </ScrollView>
        </Modal>
      </Portal>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  card: {
    margin: 16,
  },
  summaryCard: {
    marginBottom: 16,
    backgroundColor: '#E3F2FD',
  },
  pickerContainer: {
    marginBottom: 16,
  },
  pickerLabel: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
  },
  picker: {
    backgroundColor: '#f5f5f5',
    borderRadius: 4,
  },
  loader: {
    marginVertical: 20,
  },
  actionButton: {
    marginHorizontal: 4,
  },
  modalContent: {
    backgroundColor: 'white',
    padding: 20,
    margin: 20,
    borderRadius: 8,
    maxHeight: '80%',
  },
  chartContainer: {
    alignItems: 'center',
    marginVertical: 16,
  },
  chartTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  chart: {
    marginVertical: 8,
    borderRadius: 16,
  },
  closeButton: {
    marginTop: 20,
  },
});

export default StudentProgressReport;
