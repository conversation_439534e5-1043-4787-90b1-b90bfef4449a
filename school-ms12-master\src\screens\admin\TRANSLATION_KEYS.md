# Translation Keys for Mobile User Management

To ensure proper localization of the Mobile User Management screen, add the following translation keys to your language files.

## English Translation Keys

```json
{
  "userManagement": {
    "title": "User Management",
    "subtitle": "Manage all users in the system",
    "searchUsers": "Search users...",
    "addUser": "Add User",
    "addFirstUser": "Add First User",
    "clearSearch": "Clear Search",
    "noUsersFound": "No users found",
    "noSearchResults": "No users match your search",
    "totalUsers": "Total Users",
    "activeUsers": "Active Users",
    "inactiveUsers": "Inactive Users",
    "newUsers": "New Users",
    "userActivated": "User activated successfully",
    "userDeactivated": "User deactivated successfully",
    "userDeleted": "User deleted successfully",
    "errorFetching": "Error fetching users",
    "errorUpdating": "Error updating user status",
    "errorDeleting": "Error deleting user",
    "confirmDelete": "Confirm Delete",
    "deleteWarning": "Are you sure you want to delete this user? This action cannot be undone.",
    "confirmActivate": "Confirm Activate",
    "activateWarning": "Are you sure you want to activate this user?",
    "confirmDeactivate": "Confirm Deactivate",
    "deactivateWarning": "Are you sure you want to deactivate this user?",
    "activate": "Activate",
    "deactivate": "Deactivate",
    "resetPassword": "Reset Password",
    "resetPasswordWarning": "Send a password reset email to this user?",
    "sendResetLink": "Send Reset Link",
    "passwordResetSent": "Password reset email sent successfully",
    "errorPasswordReset": "Failed to send password reset email",
    "userDetails": "User Details",
    "accountInfo": "Account Information",
    "contactInfo": "Contact Information",
    "role": "Role",
    "email": "Email",
    "phone": "Phone",
    "createdAt": "Created At",
    "lastLogin": "Last Login",
    "roles": {
      "admin": "Administrator",
      "teacher": "Teacher",
      "student": "Student",
      "parent": "Parent",
      "unknown": "Unknown"
    },
    "status": {
      "status": "Status",
      "active": "Active",
      "inactive": "Inactive"
    }
  },
  "common": {
    "dashboard": "Dashboard",
    "filter": "Filter",
    "sort": "Sort",
    "refresh": "Refresh",
    "edit": "Edit",
    "delete": "Delete",
    "cancel": "Cancel",
    "dismiss": "Dismiss",
    "noInternetConnection": "No internet connection",
    "errorFetchingData": "Error fetching data",
    "notAvailable": "Not available",
    "more": "More"
  },
  "auth": {
    "errors": {
      "userNotFound": "User not found with this email",
      "invalidEmail": "Invalid email format"
    }
  }
}
```

## Arabic Translation Keys (for RTL Testing)

```json
{
  "userManagement": {
    "title": "إدارة المستخدمين",
    "subtitle": "إدارة جميع المستخدمين في النظام",
    "searchUsers": "البحث عن المستخدمين...",
    "addUser": "إضافة مستخدم",
    "addFirstUser": "إضافة أول مستخدم",
    "clearSearch": "مسح البحث",
    "noUsersFound": "لم يتم العثور على مستخدمين",
    "noSearchResults": "لا يوجد مستخدمين مطابقين لبحثك",
    "totalUsers": "إجمالي المستخدمين",
    "activeUsers": "المستخدمين النشطين",
    "inactiveUsers": "المستخدمين غير النشطين",
    "newUsers": "المستخدمين الجدد",
    "userActivated": "تم تنشيط المستخدم بنجاح",
    "userDeactivated": "تم إلغاء تنشيط المستخدم بنجاح",
    "userDeleted": "تم حذف المستخدم بنجاح",
    "errorFetching": "خطأ في جلب المستخدمين",
    "errorUpdating": "خطأ في تحديث حالة المستخدم",
    "errorDeleting": "خطأ في حذف المستخدم",
    "confirmDelete": "تأكيد الحذف",
    "deleteWarning": "هل أنت متأكد أنك تريد حذف هذا المستخدم؟ لا يمكن التراجع عن هذا الإجراء.",
    "confirmActivate": "تأكيد التنشيط",
    "activateWarning": "هل أنت متأكد أنك تريد تنشيط هذا المستخدم؟",
    "confirmDeactivate": "تأكيد إلغاء التنشيط",
    "deactivateWarning": "هل أنت متأكد أنك تريد إلغاء تنشيط هذا المستخدم؟",
    "activate": "تنشيط",
    "deactivate": "إلغاء التنشيط",
    "resetPassword": "إعادة تعيين كلمة المرور",
    "resetPasswordWarning": "إرسال بريد إلكتروني لإعادة تعيين كلمة المرور لهذا المستخدم؟",
    "sendResetLink": "إرسال رابط إعادة التعيين",
    "passwordResetSent": "تم إرسال بريد إعادة تعيين كلمة المرور بنجاح",
    "errorPasswordReset": "فشل في إرسال بريد إعادة تعيين كلمة المرور",
    "userDetails": "تفاصيل المستخدم",
    "accountInfo": "معلومات الحساب",
    "contactInfo": "معلومات الاتصال",
    "role": "الدور",
    "email": "البريد الإلكتروني",
    "phone": "الهاتف",
    "createdAt": "تم الإنشاء في",
    "lastLogin": "آخر تسجيل دخول",
    "roles": {
      "admin": "مدير",
      "teacher": "معلم",
      "student": "طالب",
      "parent": "ولي أمر",
      "unknown": "غير معروف"
    },
    "status": {
      "status": "الحالة",
      "active": "نشط",
      "inactive": "غير نشط"
    }
  },
  "common": {
    "dashboard": "لوحة التحكم",
    "filter": "تصفية",
    "sort": "ترتيب",
    "refresh": "تحديث",
    "edit": "تعديل",
    "delete": "حذف",
    "cancel": "إلغاء",
    "dismiss": "تجاهل",
    "noInternetConnection": "لا يوجد اتصال بالإنترنت",
    "errorFetchingData": "خطأ في جلب البيانات",
    "notAvailable": "غير متوفر",
    "more": "المزيد"
  },
  "auth": {
    "errors": {
      "userNotFound": "لم يتم العثور على مستخدم بهذا البريد الإلكتروني",
      "invalidEmail": "تنسيق البريد الإلكتروني غير صالح"
    }
  }
}
```

## How to Add Translation Keys

1. Locate your language files in the project. They are typically in a directory like `src/locales/` or `src/i18n/`.

2. For each language file (e.g., `en.json`, `ar.json`), add the translation keys shown above.

3. Make sure to merge these keys with any existing keys in your language files. Don't replace the entire file.

4. If you're using a translation management system, make sure to update it with these new keys.

## Testing Translations

To test that translations are working correctly:

1. Run the app and navigate to the Mobile User Management screen
2. Change the language in the app settings
3. Verify that all text on the screen is translated correctly
4. Pay special attention to RTL languages to ensure the layout adjusts properly

## Missing Translations

If you notice any missing translations while testing:

1. Identify the missing translation key
2. Add it to your language files
3. If the key is not in the list above, consider adding it to this document for future reference
