# REFERENCES

[1] "React Native Documentation," [Online]. Available: https://reactnative.dev/docs/getting-started. [Accessed 15 May 2023].

[2] "Expo Documentation," [Online]. Available: https://docs.expo.dev/. [Accessed 18 May 2023].

[3] "Firebase Documentation," [Online]. Available: https://firebase.google.com/docs. [Accessed 20 May 2023].

[4] "React Native Paper," [Online]. Available: https://callstack.github.io/react-native-paper/. [Accessed 22 May 2023].

[5] "Software Development Life Cycle Models," [Online]. Available: https://www.tutorialspoint.com/sdlc/sdlc_overview.htm. [Accessed 10 April 2023].

[6] "Mobile Application Security Best Practices," [Online]. Available: https://owasp.org/www-project-mobile-security/. [Accessed 25 May 2023].

[7] "NoSQL Database Design," [Online]. Available: https://firebase.google.com/docs/firestore/manage-data/structure-data. [Accessed 28 May 2023].

[8] "Offline First Applications," [Online]. Available: https://developer.mozilla.org/en-US/docs/Web/Progressive_web_apps/Offline_Service_workers. [Accessed 30 May 2023].

[9] "Ethiopian Calendar System," [Online]. Available: https://en.wikipedia.org/wiki/Ethiopian_calendar. [Accessed 2 June 2023].

[10] "Cross-Platform Mobile Development Comparison," [Online]. Available: https://www.altexsoft.com/blog/engineering/native-vs-cross-platform-mobile-development-pros-and-cons/. [Accessed 5 June 2023].

[11] "User Experience Design for Mobile Applications," [Online]. Available: https://www.nngroup.com/articles/mobile-ux/. [Accessed 8 June 2023].

[12] "Testing React Native Applications," [Online]. Available: https://reactnative.dev/docs/testing-overview. [Accessed 12 June 2023].

[13] "Role-Based Access Control," [Online]. Available: https://csrc.nist.gov/projects/role-based-access-control. [Accessed 15 June 2023].

[14] "Real-time Database Systems," [Online]. Available: https://firebase.google.com/docs/database/rtdb-vs-firestore. [Accessed 18 June 2023].

[15] "Multilingual Application Development," [Online]. Available: https://developer.mozilla.org/en-US/docs/Web/API/Intl. [Accessed 20 June 2023].

[16] "Mobile Application Performance Optimization," [Online]. Available: https://developer.android.com/topic/performance. [Accessed 22 June 2023].

[17] "Educational Management Information Systems," [Online]. Available: https://www.worldbank.org/en/topic/edutech/brief/educational-management-information-systems. [Accessed 25 June 2023].

[18] "School Management System Requirements Analysis," [Online]. Available: https://www.researchgate.net/publication/329964557_Requirements_Analysis_for_a_School_Management_System. [Accessed 28 June 2023].

[19] "Data Security in Educational Applications," [Online]. Available: https://www.ed.gov/protecting-student-privacy. [Accessed 30 June 2023].

[20] "Mobile UI Design Patterns," [Online]. Available: https://material.io/design/guidelines-overview. [Accessed 2 July 2023].
