import React, { useState, useEffect, useRef, useCallback } from 'react';
import { View, ScrollView, StyleSheet, Animated, TouchableOpacity, RefreshControl, Platform } from 'react-native';
import { Title, Card, DataTable, FAB, Portal, Modal, useTheme, Text, IconButton, Surface, Divider, Avatar, Chip, Button, Snackbar, ProgressBar, Menu, Searchbar, ActivityIndicator, Badge } from 'react-native-paper';
import { collection, addDoc, getDocs, updateDoc, doc, deleteDoc, query, orderBy, limit, startAfter, where, Timestamp } from 'firebase/firestore';
import { db } from '../../config/firebase';
import CustomButton from '../../components/common/CustomButton';
import CustomInput from '../../components/common/CustomInput';
import { useLanguage } from '../../context/LanguageContext';
import { LinearGradient } from 'expo-linear-gradient';
import * as Animatable from 'react-native-animatable';
import ActivityService from '../../services/ActivityService';
import { useNavigation } from '@react-navigation/native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import AdminAppHeader from '../../components/common/AdminAppHeader';
import AdminSidebar from '../../components/common/AdminSidebar';
import SidebarBackdrop from '../../components/common/SidebarBackdrop';

const GradeSettings = () => {
  const { translate, language } = useLanguage();
  // No theme needed
  const navigation = useNavigation();

  // Data state
  const [gradeScales, setGradeScales] = useState([]);
  const [visible, setVisible] = useState(false);
  const [selectedGrade, setSelectedGrade] = useState(null);
  const [loading, setLoading] = useState(false);
  const [formErrors, setFormErrors] = useState({});

  // UI Animation state
  const [fadeAnim] = useState(new Animated.Value(0));
  const [slideAnim] = useState(new Animated.Value(50));

  // Sidebar state
  const [sidebarVisible, setSidebarVisible] = useState(false);
  const [activeSidebarItem, setActiveSidebarItem] = useState('GradeSettings');
  const drawerAnim = useRef(new Animated.Value(-300)).current;

  // Enhanced UI state
  const [refreshing, setRefreshing] = useState(false);
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilters, setShowFilters] = useState(true);

  // Confirmation dialog state
  const [confirmDialogVisible, setConfirmDialogVisible] = useState(false);
  const [confirmDialogAction, setConfirmDialogAction] = useState(null);
  const [confirmDialogParams, setConfirmDialogParams] = useState(null);
  const [confirmDialogTitle, setConfirmDialogTitle] = useState('');
  const [confirmDialogMessage, setConfirmDialogMessage] = useState('');

  const [formData, setFormData] = useState({
    grade: '',
    minScore: '',
    maxScore: '',
    average: '',
    description: ''
  });

  // Toggle sidebar
  const toggleSidebar = () => {
    const toValue = sidebarVisible ? -300 : 0;
    Animated.timing(drawerAnim, {
      toValue,
      duration: 250,
      useNativeDriver: true,
    }).start();
    setSidebarVisible(!sidebarVisible);
  };

  // Toggle filter visibility
  const toggleFilters = () => {
    setShowFilters(!showFilters);
  };

  // Show confirmation dialog
  const showConfirmDialog = (action, params, title, message) => {
    setConfirmDialogAction(() => action);
    setConfirmDialogParams(params);
    setConfirmDialogTitle(title);
    setConfirmDialogMessage(message);
    setConfirmDialogVisible(true);
  };

  // Handle confirmation dialog confirm action
  const handleConfirmDialogConfirm = () => {
    if (confirmDialogAction) {
      confirmDialogAction(confirmDialogParams);
    }
    setConfirmDialogVisible(false);
  };

  // Handle confirmation dialog dismiss
  const handleConfirmDialogDismiss = () => {
    setConfirmDialogVisible(false);
  };

  // Refresh function for pull-to-refresh
  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      await fetchGradeScales();
      setSnackbarMessage(translate('grade.refreshSuccess') || 'Grade settings refreshed');
      setSnackbarVisible(true);
    } catch (error) {
      console.error('Error refreshing grade settings:', error);
      setSnackbarMessage(translate('grade.refreshError') || 'Failed to refresh grade settings');
      setSnackbarVisible(true);
    } finally {
      setRefreshing(false);
    }
  }, []);

  useEffect(() => {
    // Hide header in this screen as we're using our own
    navigation.setOptions({
      headerShown: false,
    });

    // Set active sidebar item
    setActiveSidebarItem('GradeSettings');

    // Fetch data and start animations
    fetchGradeScales();
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      })
    ]).start();
  }, [navigation]);

  const fetchGradeScales = async () => {
    try {
      setLoading(true);
      const gradesRef = collection(db, 'gradeScales');
      const q = query(gradesRef, orderBy('minScore', 'desc'));
      const snapshot = await getDocs(q);
      const scales = [];
      snapshot.forEach(doc => scales.push({ id: doc.id, ...doc.data() }));
      setGradeScales(scales);
    } catch (error) {
      console.error('Error fetching grade scales:', error);
      setSnackbarMessage(translate('grade.fetchError') || 'Failed to load grade settings');
      setSnackbarVisible(true);
    } finally {
      setLoading(false);
    }
  };

  // Filter grades based on search query
  const filteredGradeScales = gradeScales.filter(scale => {
    if (!searchQuery.trim()) return true;
    const query = searchQuery.toLowerCase();
    return (
      scale.grade.toLowerCase().includes(query) ||
      scale.description?.toLowerCase().includes(query) ||
      scale.minScore.toString().includes(query) ||
      scale.maxScore.toString().includes(query) ||
      scale.average.toString().includes(query)
    );
  });

  const validateForm = () => {
    const errors = {};
    if (!formData.grade.trim()) {
      errors.grade = translate('errors.required');
    }
    if (!formData.minScore || isNaN(formData.minScore) || Number(formData.minScore) < 0) {
      errors.minScore = translate('errors.invalidScore');
    }
    if (!formData.maxScore || isNaN(formData.maxScore) || Number(formData.maxScore) < 0) {
      errors.maxScore = translate('errors.invalidScore');
    }
    if (Number(formData.minScore) >= Number(formData.maxScore)) {
      errors.minScore = translate('errors.minScoreGreater');
    }
    if (!formData.average || isNaN(formData.average) || Number(formData.average) < 0) {
      errors.average = translate('errors.invalidAverage');
    }
    return errors;
  };

  const handleAddGradeScale = async () => {
    const errors = validateForm();
    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }

    try {
      setLoading(true);
      const newGradeScale = {
        ...formData,
        minScore: parseFloat(formData.minScore),
        maxScore: parseFloat(formData.maxScore),
        average: parseFloat(formData.average),
        createdAt: new Date().toISOString()
      };

      await addDoc(collection(db, 'gradeScales'), newGradeScale);

      // Log activity
      await ActivityService.logActivity(
        'admin',
        'academic',
        translate('activities.newGradeScale'),
        translate('activities.newGradeScaleDesc', { grade: formData.grade })
      );

      setVisible(false);
      resetForm();
      await fetchGradeScales();
      setSnackbarMessage(translate('grade.addSuccess') || `Grade scale "${formData.grade}" added successfully`);
      setSnackbarVisible(true);
    } catch (error) {
      console.error('Error adding grade scale:', error);
      setSnackbarMessage(translate('grade.addError') || 'Failed to add grade scale');
      setSnackbarVisible(true);
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateGradeScale = async () => {
    const errors = validateForm();
    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }

    try {
      setLoading(true);
      const gradeRef = doc(db, 'gradeScales', selectedGrade.id);
      const updatedGrade = {
        ...formData,
        minScore: parseFloat(formData.minScore),
        maxScore: parseFloat(formData.maxScore),
        average: parseFloat(formData.average),
        updatedAt: new Date().toISOString()
      };

      await updateDoc(gradeRef, updatedGrade);

      // Log activity
      await ActivityService.logActivity(
        'admin',
        'academic',
        translate('activities.updateGradeScale'),
        translate('activities.updateGradeScaleDesc', { grade: formData.grade })
      );

      setVisible(false);
      resetForm();
      await fetchGradeScales();
      setSnackbarMessage(translate('grade.updateSuccess') || `Grade scale "${formData.grade}" updated successfully`);
      setSnackbarVisible(true);
    } catch (error) {
      console.error('Error updating grade scale:', error);
      setSnackbarMessage(translate('grade.updateError') || 'Failed to update grade scale');
      setSnackbarVisible(true);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteGradeScale = async (gradeId) => {
    try {
      // Show confirmation dialog
      showConfirmDialog(
        async () => {
          try {
            setLoading(true);
            await deleteDoc(doc(db, 'gradeScales', gradeId));

            // Log activity
            await ActivityService.logActivity(
              'admin',
              'academic',
              translate('activities.deleteGradeScale'),
              translate('activities.deleteGradeScaleDesc', { grade: selectedGrade.grade })
            );

            setVisible(false);
            resetForm();
            await fetchGradeScales();
            setSnackbarMessage(translate('grade.deleteSuccess') || 'Grade scale deleted successfully');
            setSnackbarVisible(true);
          } catch (error) {
            console.error('Error deleting grade scale:', error);
            setSnackbarMessage(translate('grade.deleteError') || 'Failed to delete grade scale');
            setSnackbarVisible(true);
          } finally {
            setLoading(false);
          }
        },
        gradeId,
        translate('grade.confirmDelete') || 'Confirm Delete',
        translate('grade.confirmDeleteMessage') || `Are you sure you want to delete the grade scale "${selectedGrade.grade}"? This action cannot be undone.`
      );
    } catch (error) {
      console.error('Error preparing to delete grade scale:', error);
    }
  };

  const resetForm = () => {
    setFormData({
      grade: '',
      minScore: '',
      maxScore: '',
      average: '',
      description: ''
    });
    setSelectedGrade(null);
    setFormErrors({});
  };

  const getGradeColor = (average) => {
    if (average >= 90) return '#4CAF50'; // Excellent
    if (average >= 80) return '#8BC34A'; // Very Good
    if (average >= 70) return '#FFC107'; // Good
    if (average >= 60) return '#FF9800'; // Satisfactory
    return '#F44336'; // Poor
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Admin App Header */}
      <AdminAppHeader
        title={translate('grade.title') || "Grade Settings"}
        onMenuPress={toggleSidebar}
      />

      {/* Sidebar Backdrop - only visible when sidebar is open */}
      {sidebarVisible && (
        <SidebarBackdrop onPress={toggleSidebar} />
      )}

      {/* Admin Sidebar */}
      <AdminSidebar
        drawerAnim={drawerAnim}
        activeSidebarItem={activeSidebarItem}
        setActiveSidebarItem={setActiveSidebarItem}
        toggleDrawer={toggleSidebar}
      />

      {/* Main Content */}
      <Animated.View
        style={[
          styles.content,
          {
            opacity: fadeAnim,
            transform: [{ translateY: slideAnim }]
          }
        ]}
      >
        {/* Search Bar */}
        <Animatable.View animation="fadeIn" duration={500}>
          <View style={styles.searchContainer}>
            <Searchbar
              placeholder={translate('grade.searchPlaceholder') || "Search grades..."}
              onChangeText={query => setSearchQuery(query)}
              value={searchQuery}
              style={styles.searchBar}
              icon="magnify"
              iconColor={'#1976d2'}
            />
            <IconButton
              icon={showFilters ? "filter-variant" : "filter-variant-plus"}
              size={24}
              style={styles.filterToggleButton}
              onPress={toggleFilters}
              color={'#1976d2'}
            />
          </View>
        </Animatable.View>

        {/* Filter Section */}
        {showFilters && (
          <Animatable.View
            animation={showFilters ? "fadeIn" : "fadeOut"}
            duration={300}
            style={styles.filterContainer}
          >
            <View style={styles.filterHeader}>
              <Text style={styles.filterTitle}>{translate('grade.filters') || "Filters"}</Text>
              <IconButton
                icon="chevron-up"
                size={24}
                onPress={toggleFilters}
                color={'#1976d2'}
              />
            </View>

            <View style={styles.filterChips}>
              <Chip
                selected={true}
                onPress={() => {}}
                style={styles.filterChip}
                icon="sort-ascending"
              >
                {translate('grade.sortByScore') || "Sort by Score"}
              </Chip>
              <Chip
                selected={false}
                onPress={() => {}}
                style={styles.filterChip}
                icon="sort-alphabetical-ascending"
              >
                {translate('grade.sortByGrade') || "Sort by Grade"}
              </Chip>
            </View>
          </Animatable.View>
        )}

        <ScrollView
          style={styles.scrollView}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={['#1976d2']}
              tintColor={'#1976d2'}
            />
          }
        >
        <Card style={styles.card}>
          <Card.Content>
            <View style={styles.cardHeader}>
              <Title style={styles.cardTitle}>{translate('grade.gradeScales') || "Grade Scales"}</Title>
              <Chip
                icon="information"
                mode="outlined"
                style={styles.countChip}
              >
                {filteredGradeScales.length} {translate('grade.scales') || "scales"}
              </Chip>
            </View>

            {loading && <ProgressBar indeterminate color={'#1976d2'} style={styles.progressBar} />}

            {filteredGradeScales.length === 0 ? (
              <View style={styles.emptyContainer}>
                <IconButton icon="scale-balance" size={50} color={'#9e9e9e'} />
                <Text style={styles.emptyText}>{translate('grade.noGrades') || "No grade scales found"}</Text>
                <Text style={styles.emptySubtext}>
                  {searchQuery ?
                    (translate('grade.noGradesSearch') || "No grades match your search criteria") :
                    (translate('grade.addNewGrade') || "Add a new grade scale using the + button")}
                </Text>
              </View>
            ) : (
              <DataTable style={styles.dataTable}>
                <DataTable.Header style={styles.tableHeader}>
                  <DataTable.Title textStyle={styles.headerText}>{translate('grade.grade') || "Grade"}</DataTable.Title>
                  <DataTable.Title textStyle={styles.headerText} numeric>{translate('grade.minScore') || "Min Score"}</DataTable.Title>
                  <DataTable.Title textStyle={styles.headerText} numeric>{translate('grade.maxScore') || "Max Score"}</DataTable.Title>
                  <DataTable.Title textStyle={styles.headerText} numeric>{translate('grade.average') || "Average"}</DataTable.Title>
                  <DataTable.Title textStyle={styles.headerText}>{translate('grade.description') || "Description"}</DataTable.Title>
                  <DataTable.Title textStyle={styles.headerText}>{translate('actions') || "Actions"}</DataTable.Title>
                </DataTable.Header>

                {filteredGradeScales.map((scale, index) => (
                <Animatable.View
                  key={scale.id}
                  animation="fadeInUp"
                  duration={500}
                  delay={index * 100}
                >
                  <DataTable.Row
                    style={styles.tableRow}
                    onPress={() => {
                      setSelectedGrade(scale);
                      setFormData({
                        grade: scale.grade,
                        minScore: scale.minScore.toString(),
                        maxScore: scale.maxScore.toString(),
                        average: scale.average.toString(),
                        description: scale.description || ''
                      });
                      setVisible(true);
                    }}
                  >
                    <DataTable.Cell>
                      <Chip
                        textStyle={{ color: '#fff' }}
                        style={{ backgroundColor: getGradeColor(scale.average) }}
                      >
                        {scale.grade}
                      </Chip>
                    </DataTable.Cell>
                  <DataTable.Cell numeric>{scale.minScore}</DataTable.Cell>
                  <DataTable.Cell numeric>{scale.maxScore}</DataTable.Cell>
                    <DataTable.Cell numeric>{scale.average}</DataTable.Cell>
                    <DataTable.Cell>{scale.description || '-'}</DataTable.Cell>
                    <DataTable.Cell>
                      <IconButton
                        icon="pencil"
                        size={20}
                        onPress={() => {
                          setSelectedGrade(scale);
                          setFormData({
                            grade: scale.grade,
                            minScore: scale.minScore.toString(),
                            maxScore: scale.maxScore.toString(),
                            average: scale.average.toString(),
                            description: scale.description || ''
                          });
                          setVisible(true);
                        }}
                      />
                    </DataTable.Cell>
                </DataTable.Row>
                </Animatable.View>
              ))}
              </DataTable>
            )}
          </Card.Content>
        </Card>
        </ScrollView>

      <Portal>
        <Modal
          visible={visible}
          onDismiss={() => {
            setVisible(false);
            resetForm();
          }}
          contentContainerStyle={styles.modalContent}
        >
          <ScrollView>
            <LinearGradient
              colors={['#1976d2', '#005cb2']}
              style={styles.modalHeader}
            >
              <Title style={styles.modalTitle}>
                {selectedGrade ? translate('grade.editGrade') : translate('grade.addGrade')}
              </Title>
            </LinearGradient>

            <View style={styles.formContainer}>
            <CustomInput
                label={translate('grade.grade')}
                value={formData.grade}
                onChangeText={(text) => {
                  setFormData({ ...formData, grade: text });
                  setFormErrors({ ...formErrors, grade: '' });
                }}
                error={formErrors.grade}
              />

            <CustomInput
                label={translate('grade.minScore')}
                value={formData.minScore}
                onChangeText={(text) => {
                  setFormData({ ...formData, minScore: text });
                  setFormErrors({ ...formErrors, minScore: '' });
                }}
              keyboardType="numeric"
                error={formErrors.minScore}
            />

            <CustomInput
                label={translate('grade.maxScore')}
                value={formData.maxScore}
                onChangeText={(text) => {
                  setFormData({ ...formData, maxScore: text });
                  setFormErrors({ ...formErrors, maxScore: '' });
                }}
              keyboardType="numeric"
                error={formErrors.maxScore}
            />

            <CustomInput
                label={translate('grade.average')}
                value={formData.average}
                onChangeText={(text) => {
                  setFormData({ ...formData, average: text });
                  setFormErrors({ ...formErrors, average: '' });
                }}
              keyboardType="numeric"
                error={formErrors.average}
            />

            <CustomInput
                label={translate('grade.description')}
                value={formData.description}
                onChangeText={(text) => setFormData({ ...formData, description: text })}
              multiline
                numberOfLines={3}
              />

              <View style={styles.modalButtons}>
                <CustomButton
                  mode="contained"
                  onPress={selectedGrade ? handleUpdateGradeScale : handleAddGradeScale}
                  loading={loading}
                  style={styles.saveButton}
                >
                  {selectedGrade ? translate('actions.update') : translate('actions.add')}
                </CustomButton>

                {selectedGrade && (
                  <CustomButton
                    mode="outlined"
                    onPress={() => handleDeleteGradeScale(selectedGrade.id)}
                    style={styles.deleteButton}
                  >
                    {translate('actions.delete')}
                  </CustomButton>
                )}

                <CustomButton
                  mode="outlined"
                  onPress={() => {
                    setVisible(false);
                    resetForm();
                  }}
                >
                  {translate('actions.cancel')}
            </CustomButton>
          </View>
            </View>
      </ScrollView>
        </Modal>
      </Portal>

      </Animated.View>

      {/* Floating Action Button */}
      <FAB
        style={[styles.fab, { backgroundColor: '#1976d2' }]}
        icon="plus"
        onPress={() => setVisible(true)}
        color={'#ffffff'}
      />

      {/* Confirmation Dialog */}
      <Portal>
        <Modal
          visible={confirmDialogVisible}
          onDismiss={handleConfirmDialogDismiss}
          contentContainerStyle={styles.confirmDialog}
        >
          <View style={styles.confirmDialogContent}>
            <Title style={styles.confirmDialogTitle}>{confirmDialogTitle}</Title>
            <Text style={styles.confirmDialogMessage}>{confirmDialogMessage}</Text>

            <View style={styles.confirmDialogActions}>
              <Button
                mode="outlined"
                onPress={handleConfirmDialogDismiss}
                style={styles.confirmDialogButton}
              >
                {translate('common.cancel') || 'Cancel'}
              </Button>
              <Button
                mode="contained"
                onPress={handleConfirmDialogConfirm}
                style={[styles.confirmDialogButton, { marginLeft: 8 }]}
              >
                {translate('common.confirm') || 'Confirm'}
              </Button>
            </View>
          </View>
        </Modal>
      </Portal>

      {/* Snackbar for notifications */}
      <Snackbar
        visible={snackbarVisible}
        onDismiss={() => setSnackbarVisible(false)}
        duration={3000}
        action={{
          label: translate('common.dismiss') || 'Dismiss',
          onPress: () => setSnackbarVisible(false),
        }}
        style={styles.snackbar}
      >
        {snackbarMessage}
      </Snackbar>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
    paddingTop: 8,
  },
  scrollView: {
    flex: 1,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    margin: 16,
    marginBottom: 8,
  },
  searchBar: {
    flex: 1,
    elevation: 4,
    borderRadius: 25,
    height: 50,
  },
  filterToggleButton: {
    marginLeft: 8,
    backgroundColor: '#E3F2FD',
  },
  filterContainer: {
    marginHorizontal: 16,
    marginBottom: 8,
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 16,
    elevation: 2,
  },
  filterHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  filterTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  filterChips: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 8,
  },
  filterChip: {
    marginRight: 8,
    marginBottom: 8,
  },
  card: {
    margin: 16,
    borderRadius: 8,
    elevation: 4,
    overflow: 'hidden',
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  cardTitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  countChip: {
    backgroundColor: '#E3F2FD',
  },
  progressBar: {
    marginVertical: 8,
  },
  emptyContainer: {
    padding: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyText: {
    marginTop: 16,
    fontSize: 18,
    fontWeight: 'bold',
    color: '#666',
  },
  emptySubtext: {
    marginTop: 8,
    fontSize: 14,
    color: '#888',
    textAlign: 'center',
  },
  dataTable: {
    borderRadius: 8,
  },
  tableHeader: {
    backgroundColor: '#f5f5f5',
  },
  headerText: {
    fontWeight: 'bold',
    color: '#555',
  },
  tableRow: {
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  modalContent: {
    backgroundColor: 'white',
    margin: 20,
    borderRadius: 10,
    maxHeight: '80%',
  },
  modalHeader: {
    padding: 20,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
  },
  modalTitle: {
    color: '#fff',
    fontSize: 20,
  },
  formContainer: {
    padding: 20,
  },
  modalButtons: {
    marginTop: 20,
  },
  saveButton: {
    marginBottom: 10,
  },
  deleteButton: {
    marginBottom: 10,
    backgroundColor: '#FFEBEE',
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
  },
  confirmDialog: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 20,
    margin: 20,
    maxWidth: 500,
    alignSelf: 'center',
  },
  confirmDialogContent: {
    alignItems: 'center',
  },
  confirmDialogTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
    textAlign: 'center',
  },
  confirmDialogMessage: {
    fontSize: 14,
    marginBottom: 20,
    textAlign: 'center',
    color: '#666',
  },
  confirmDialogActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    width: '100%',
    marginTop: 16,
  },
  confirmDialogButton: {
    minWidth: 100,
  },
  snackbar: {
    bottom: 16,
  },
});

export default GradeSettings;
