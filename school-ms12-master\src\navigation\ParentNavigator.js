import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { useLanguage } from '../context/LanguageContext';
import ParentDashboard from '../screens/parent/ParentDashboard';
import StudentMonitoring from '../screens/parent/StudentMonitoring';
import ParentCommunication from '../screens/parent/ParentCommunication';
import CalendarManagement from '../screens/parent/CalendarManagement';
import ProfileManagement from '../screens/parent/ProfileManagement';
import ChildProgress from '../screens/parent/ChildProgress';
import ParentTeacherCommunication from '../screens/parent/ParentTeacherCommunication';
import StudentProgressReport from '../screens/parent/StudentProgressReport';
import ParentChildGrades from '../screens/parent/ParentChildGrades';
import ParentAttendanceView from '../screens/parent/ParentAttendanceView';
import MeetingScheduler from '../screens/shared/MeetingScheduler';
import SchoolEventCalendar from '../screens/shared/SchoolEventCalendar';
import BehaviorView from '../screens/shared/BehaviorView';
import Communication from '../screens/shared/Communication';
import NotificationCenter from '../screens/shared/NotificationCenter';
import NotificationSettings from '../screens/shared/NotificationSettings';
import ReportCenter from '../screens/shared/ReportCenter';
import ParentMessaging from '../screens/parent/ParentMessaging';
import ParentExamSchedule from '../screens/parent/ParentExamSchedule';
import ParentClassSchedule from '../screens/parent/ParentClassSchedule';
import { ParentSidebarProvider } from '../context/ParentSidebarContext';

const Stack = createStackNavigator();

const ParentNavigator = () => {
  const { translate } = useLanguage();

  return (
    <ParentSidebarProvider>
      <Stack.Navigator>
      <Stack.Screen
        name="ParentDashboard"
        component={ParentDashboard}
        options={{ title: translate('parent.dashboard.title') || 'Parent Dashboard' }}
      />
      <Stack.Screen
        name="StudentMonitoring"
        component={StudentMonitoring}
        options={{ title: translate('parent.studentMonitoring.title') || 'Student Progress' }}
      />
      <Stack.Screen
        name="ParentAttendanceView"
        component={ParentAttendanceView}
        options={{ title: translate('parent.attendance.title') || 'Attendance Records', headerShown: false }}
      />
      <Stack.Screen
        name="Communication"
        component={Communication}
        options={{ title: translate('parent.communication.title') || 'Communication' }}
      />
      <Stack.Screen
        name="ParentTeacherCommunication"
        component={ParentTeacherCommunication}
        options={{ title: translate('parent.teacherCommunication.title') || 'Teacher Communication' }}
      />
      <Stack.Screen
        name="CalendarManagement"
        component={CalendarManagement}
        options={{ title: translate('parent.calendar.title') || 'School Calendar' }}
      />
      <Stack.Screen
        name="ProfileManagement"
        component={ProfileManagement}
        options={{ title: translate('parent.profile.title') || 'My Profile' }}
      />
      <Stack.Screen
        name="ChildProgress"
        component={ChildProgress}
        options={{ title: translate('parent.childProgress.title') || 'Child Progress' }}
      />
      <Stack.Screen
        name="StudentProgressReport"
        component={StudentProgressReport}
        options={{ title: translate('parent.progressReport.title') || 'Progress Report' }}
      />
      <Stack.Screen
        name="ParentChildGrades"
        component={ParentChildGrades}
        options={{ title: translate('parent.childGrades.title') || "Child's Grades", headerShown: false }}
      />
      <Stack.Screen
        name="ParentExamSchedule"
        component={ParentExamSchedule}
        options={{ title: translate('parent.examSchedule.title') || "Child's Exam Schedule", headerShown: false }}
      />
      <Stack.Screen
        name="ParentClassSchedule"
        component={ParentClassSchedule}
        options={{ title: translate('parent.classSchedule.title') || "Child's Class Schedule", headerShown: false }}
      />
      <Stack.Screen
        name="MeetingScheduler"
        component={MeetingScheduler}
        options={{ title: translate('parent.meetingScheduler.title') || 'Schedule Meeting' }}
      />
      <Stack.Screen
        name="SchoolEventCalendar"
        component={SchoolEventCalendar}
        options={{ title: translate('parent.eventCalendar.title') || 'School Events' }}
      />
      <Stack.Screen
        name="BehaviorView"
        component={BehaviorView}
        options={{ title: translate('parent.behavior.title') || 'Behavior Report' }}
      />
      <Stack.Screen
        name="ParentMessaging"
        component={ParentMessaging}
        options={{ title: translate('parent.messaging.title') || 'Messaging', headerShown: false }}
      />
      <Stack.Screen
        name="NotificationCenter"
        component={NotificationCenter}
        options={{ title: translate('parent.notifications.title') || 'Notifications', headerShown: false }}
      />
      <Stack.Screen
        name="NotificationSettings"
        component={NotificationSettings}
        options={{ title: translate('parent.notificationSettings.title') || 'Notification Settings', headerShown: false }}
      />
      <Stack.Screen
        name="Reports"
        component={ReportCenter}
        options={{ title: translate('parent.reports.title') || 'Report Center' }}
      />
    </Stack.Navigator>
    </ParentSidebarProvider>
  );
};

export default ParentNavigator;