const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Get the current directory
const currentDir = __dirname;
console.log(`Current directory: ${currentDir}`);

// Check if firebase-tools is installed
try {
  console.log('Checking for Firebase CLI...');
  execSync('firebase --version', { stdio: 'inherit' });
} catch (error) {
  console.error('Firebase CLI not found. Installing...');
  try {
    execSync('npm install -g firebase-tools', { stdio: 'inherit' });
  } catch (installError) {
    console.error('Failed to install Firebase CLI. Please install it manually with:');
    console.error('npm install -g firebase-tools');
    process.exit(1);
  }
}

// Check if user is logged in to Firebase
try {
  console.log('Checking Firebase login status...');
  execSync('firebase projects:list', { stdio: 'pipe' });
  console.log('Already logged in to Firebase.');
} catch (error) {
  console.log('Not logged in to Firebase. Please log in:');
  try {
    execSync('firebase login', { stdio: 'inherit' });
  } catch (loginError) {
    console.error('Failed to log in to Firebase. Please try again manually.');
    process.exit(1);
  }
}

// Check if firebase.json exists
const firebaseConfigPath = path.join(__dirname, 'firebase.json');
if (!fs.existsSync(firebaseConfigPath)) {
  console.error('firebase.json not found. Please create it first.');
  process.exit(1);
}

// Check if firestore.rules exists
const firestoreRulesPath = path.join(__dirname, 'firestore.rules');
if (!fs.existsSync(firestoreRulesPath)) {
  console.error('firestore.rules not found. Please create it first.');
  process.exit(1);
}

// Check if storage.rules exists
const storageRulesPath = path.join(__dirname, 'storage.rules');
if (!fs.existsSync(storageRulesPath)) {
  console.error('storage.rules not found. Please create it first.');
  process.exit(1);
}

// Deploy Firestore security rules
console.log('Deploying Firestore security rules...');
try {
  execSync('firebase deploy --only firestore:rules', { stdio: 'inherit' });
  console.log('Successfully deployed Firestore security rules!');
} catch (error) {
  console.error('Failed to deploy Firestore security rules:', error.message);
  process.exit(1);
}

// Deploy Storage security rules
console.log('Deploying Storage security rules...');
try {
  execSync('firebase deploy --only storage:rules', { stdio: 'inherit' });
  console.log('Successfully deployed Storage security rules!');
} catch (error) {
  console.error('Failed to deploy Storage security rules:', error.message);
  process.exit(1);
}

console.log('All Firebase rules deployed successfully!');
