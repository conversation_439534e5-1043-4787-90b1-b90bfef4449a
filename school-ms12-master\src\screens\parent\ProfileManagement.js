import React, { useState, useEffect } from 'react';
import { View, ScrollView, StyleSheet } from 'react-native';
import { Card, Title, Text, Portal, Modal, List, ActivityIndicator } from 'react-native-paper';
import { db } from '../../config/firebase';
import { doc, getDoc, updateDoc } from 'firebase/firestore';
import { useAuth } from '../../context/AuthContext';
import { useLanguage } from '../../context/LanguageContext';
import CustomButton from '../../components/common/CustomButton';
import CustomInput from '../../components/common/CustomInput';
import ChangePasswordModal from '../../components/common/ChangePasswordModal';
import * as ImagePicker from 'expo-image-picker';
import ProfileImageService from '../../services/ProfileImageService';
import CloudinaryAvatar from '../../components/common/CloudinaryAvatar';

const ProfileManagement = () => {
  const { user, updateUserProfile, updatePassword } = useAuth();
  const { translate, getTextStyle } = useLanguage();
  const [profile, setProfile] = useState(null);
  const [visible, setVisible] = useState(false);
  const [passwordVisible, setPasswordVisible] = useState(false);
  const [loading, setLoading] = useState(true);
  const [imageUri, setImageUri] = useState(null);

  const [formData, setFormData] = useState({
    displayName: '',
    email: '',
    phone: '',
    address: '',
    occupation: '',
    alternatePhone: '',
    emergencyContact: '',
    relationship: '',
    bio: '',
  });



  const formatAddress = (address) => {
    if (!address) return '';
    if (typeof address === 'string') return address;

    const { street, city, state, zipCode, country } = address;
    const parts = [street, city, state, zipCode, country].filter(Boolean);
    return parts.join(', ');
  };

  useEffect(() => {
    fetchProfile();
  }, []);

  const fetchProfile = async () => {
    try {
      setLoading(true);
      const profileRef = doc(db, 'users', user.uid);
      const docSnap = await getDoc(profileRef);

      if (docSnap.exists()) {
        const profileData = docSnap.data();
        setProfile(profileData);
        setFormData({
          displayName: profileData.displayName || user.displayName || '',
          email: profileData.email || user.email || '',
          phone: profileData.phone || '',
          address: profileData.address || '',
          occupation: profileData.occupation || '',
          alternatePhone: profileData.alternatePhone || '',
          emergencyContact: profileData.emergencyContact || '',
          relationship: profileData.relationship || '',
          bio: profileData.bio || '',
        });
        setImageUri(profileData.photoURL || user.photoURL);
      } else {
        // If no profile exists, create one with user auth data
        const initialProfile = {
          displayName: user.displayName || '',
          email: user.email || '',
          photoURL: user.photoURL || '',
          role: 'parent',
          createdAt: new Date().toISOString(),
        };
        await updateDoc(profileRef, initialProfile);
        setProfile(initialProfile);
        setFormData({
          ...formData,
          displayName: user.displayName || '',
          email: user.email || '',
        });
        setImageUri(user.photoURL);
      }
    } catch (error) {
      console.error('Error fetching profile:', error);
    } finally {
      setLoading(false);
    }
  };

  const pickImage = async () => {
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [1, 1],
      quality: 1,
    });

    if (!result.canceled) {
      setImageUri(result.assets[0].uri);
    }
  };

  const handleUpdateProfile = async () => {
    try {
      setLoading(true);

      // Handle profile image upload if changed
      let photoURL = imageUri;
      if (imageUri && !imageUri.startsWith('http')) {
        try {
          // Upload and update profile image using the ProfileImageService
          photoURL = await ProfileImageService.uploadAndUpdateProfileImage(
            imageUri,
            user,
            'parent'
          );
        } catch (imageError) {
          console.error('Error uploading profile image:', imageError);
          // Continue with other updates even if image upload fails
        }
      }

      // Update profile in Firestore
      const profileRef = doc(db, 'users', user.uid);
      await updateDoc(profileRef, {
        ...formData,
        photoURL,
        updatedAt: new Date().toISOString(),
      });

      // Update auth profile
      await updateUserProfile({
        displayName: formData.displayName,
        photoURL,
      });

      setVisible(false);
      fetchProfile();
    } catch (error) {
      console.error('Error updating profile:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#2196F3" />
      </View>
    );
  }

  if (!profile) {
    return (
      <View style={styles.container}>
        <Text>Error loading profile. Please try again.</Text>
        <CustomButton onPress={fetchProfile} mode="contained" style={styles.retryButton}>
          Retry
        </CustomButton>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <Card style={styles.profileCard}>
        <View style={styles.avatarContainer}>
          <CloudinaryAvatar
            source={imageUri}
            label={profile.displayName?.substring(0, 2) || 'PR'}
            size={120}
            backgroundColor="#ffa726"
          />
          <Title style={styles.name}>{profile.displayName}</Title>
          <Text style={styles.role}>{translate('roles.parent') || 'Parent'}</Text>
          <Text style={styles.studentInfo}>
            {translate('parentManagement.parentOf') || 'Parent of'} {profile.studentName || translate('parentManagement.unknownStudent') || 'Unknown Student'}
          </Text>
        </View>

        <Card.Content>
          <List.Section>
            <List.Item
              title="Email"
              description={profile.email}
              left={props => <List.Icon {...props} icon="email" />}
            />
            <List.Item
              title="Phone"
              description={profile.phone || 'Not provided'}
              left={props => <List.Icon {...props} icon="phone" />}
            />
            <List.Item
              title="Alternate Phone"
              description={profile.alternatePhone || 'Not provided'}
              left={props => <List.Icon {...props} icon="phone-plus" />}
            />
            <List.Item
              title="Address"
              description={formatAddress(profile.address) || 'Not provided'}
              left={props => <List.Icon {...props} icon="map-marker" />}
            />
            <List.Item
              title="Occupation"
              description={profile.occupation || 'Not provided'}
              left={props => <List.Icon {...props} icon="briefcase" />}
            />
            <List.Item
              title="Emergency Contact"
              description={profile.emergencyContact || 'Not provided'}
              left={props => <List.Icon {...props} icon="phone-alert" />}
            />
            <List.Item
              title="Relationship"
              description={profile.relationship || 'Not provided'}
              left={props => <List.Icon {...props} icon="account-child" />}
            />
          </List.Section>

          <Card style={styles.bioCard}>
            <Card.Content>
              <Title>Bio</Title>
              <Text>{profile.bio}</Text>
            </Card.Content>
          </Card>

          <CustomButton
            mode="contained"
            onPress={() => setVisible(true)}
            style={styles.editButton}
          >
            Edit Profile
          </CustomButton>

          <CustomButton
            mode="outlined"
            onPress={() => setPasswordVisible(true)}
            style={styles.passwordButton}
            icon="lock-reset"
          >
            {translate('profileManagement.changePassword') || 'Change Password'}
          </CustomButton>
        </Card.Content>
      </Card>

      {/* Change Password Modal */}
      <ChangePasswordModal
        visible={passwordVisible}
        onDismiss={() => setPasswordVisible(false)}
        onSuccess={() => setPasswordVisible(false)}
      />

      <Portal>
        <Modal
          visible={visible}
          onDismiss={() => setVisible(false)}
          contentContainerStyle={styles.modalContent}
        >
          <ScrollView>
            <Title>Edit Profile</Title>

            <View style={styles.imagePickerContainer}>
              <CloudinaryAvatar
                source={imageUri}
                label={profile.displayName?.substring(0, 2) || 'PR'}
                size={100}
                backgroundColor="#ffa726"
              />
              <CustomButton
                mode="outlined"
                onPress={pickImage}
                style={styles.imagePickerButton}
              >
                {translate('profileManagement.changePhoto') || 'Change Photo'}
              </CustomButton>
            </View>

            <CustomInput
              label="Display Name"
              value={formData.displayName}
              onChangeText={(text) => setFormData({ ...formData, displayName: text })}
            />

            <CustomInput
              label="Email"
              value={formData.email}
              onChangeText={(text) => setFormData({ ...formData, email: text })}
              keyboardType="email-address"
            />

            <CustomInput
              label="Phone"
              value={formData.phone}
              onChangeText={(text) => setFormData({ ...formData, phone: text })}
              keyboardType="phone-pad"
            />

            <CustomInput
              label="Alternate Phone"
              value={formData.alternatePhone}
              onChangeText={(text) => setFormData({ ...formData, alternatePhone: text })}
              keyboardType="phone-pad"
            />

            <CustomInput
              label="Address"
              value={formData.address}
              onChangeText={(text) => setFormData({ ...formData, address: text })}
              multiline
              numberOfLines={2}
            />

            <CustomInput
              label="Occupation"
              value={formData.occupation}
              onChangeText={(text) => setFormData({ ...formData, occupation: text })}
            />

            <CustomInput
              label="Emergency Contact"
              value={formData.emergencyContact}
              onChangeText={(text) => setFormData({ ...formData, emergencyContact: text })}
              keyboardType="phone-pad"
            />

            <CustomInput
              label="Relationship"
              value={formData.relationship}
              onChangeText={(text) => setFormData({ ...formData, relationship: text })}
            />

            <CustomInput
              label="Bio"
              value={formData.bio}
              onChangeText={(text) => setFormData({ ...formData, bio: text })}
              multiline
              numberOfLines={4}
            />

            <View style={styles.modalButtons}>
              <CustomButton
                mode="contained"
                onPress={handleUpdateProfile}
                loading={loading}
              >
                Save Changes
              </CustomButton>

              <CustomButton
                mode="outlined"
                onPress={() => setVisible(false)}
                style={styles.cancelButton}
              >
                Cancel
              </CustomButton>
            </View>
          </ScrollView>
        </Modal>


      </Portal>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  retryButton: {
    marginTop: 16,
  },
  profileCard: {
    margin: 10,
  },
  avatarContainer: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  name: {
    marginTop: 10,
    fontSize: 24,
  },
  role: {
    color: '#666',
    marginTop: 5,
  },
  studentInfo: {
    color: '#666',
    marginTop: 5,
  },
  bioCard: {
    marginTop: 20,
    backgroundColor: '#f9f9f9',
  },
  editButton: {
    marginTop: 20,
  },
  passwordButton: {
    marginTop: 10,
  },
  modalContent: {
    backgroundColor: 'white',
    padding: 20,
    margin: 20,
    borderRadius: 10,
    maxHeight: '80%',
  },
  imagePickerContainer: {
    alignItems: 'center',
    marginVertical: 20,
  },
  imagePickerButton: {
    marginTop: 10,
  },
  modalButtons: {
    marginTop: 20,
  },
  cancelButton: {
    marginTop: 10,
  },
});

export default ProfileManagement;
