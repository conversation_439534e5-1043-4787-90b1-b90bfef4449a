import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';

class FirebaseConfig {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyBHkGAhm80xBIECyaHbZ_DbeNzu3jnVuDg',
    appId: '1:999485613068:web:e9c0c3e0a4c7f4e4c8b8b8',
    messagingSenderId: '999485613068',
    projectId: 'schoolmn-16cbc',
    authDomain: 'schoolmn-16cbc.firebaseapp.com',
    storageBucket: 'schoolmn-16cbc.appspot.com',
    measurementId: 'G-XXXXXXXXXX',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyBHkGAhm80xBIECyaHbZ_DbeNzu3jnVuDg',
    appId: '1:999485613068:android:abcdefghijklmnop',
    messagingSenderId: '999485613068',
    projectId: 'schoolmn-16cbc',
    storageBucket: 'schoolmn-16cbc.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyBHkGAhm80xBIECyaHbZ_DbeNzu3jnVuDg',
    appId: '1:999485613068:ios:abcdefghijklmnop',
    messagingSenderId: '999485613068',
    projectId: 'schoolmn-16cbc',
    storageBucket: 'schoolmn-16cbc.appspot.com',
    iosBundleId: 'com.example.flutterSchoolms',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyBHkGAhm80xBIECyaHbZ_DbeNzu3jnVuDg',
    appId: '1:999485613068:macos:abcdefghijklmnop',
    messagingSenderId: '999485613068',
    projectId: 'schoolmn-16cbc',
    storageBucket: 'schoolmn-16cbc.appspot.com',
    iosBundleId: 'com.example.flutterSchoolms',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyBHkGAhm80xBIECyaHbZ_DbeNzu3jnVuDg',
    appId: '1:999485613068:windows:abcdefghijklmnop',
    messagingSenderId: '999485613068',
    projectId: 'schoolmn-16cbc',
    storageBucket: 'schoolmn-16cbc.appspot.com',
  );
}
