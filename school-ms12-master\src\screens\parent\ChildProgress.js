import React, { useState, useEffect } from 'react';
import { View, ScrollView, StyleSheet } from 'react-native';
import { Card, Title, Text, List, Divider, Portal, Modal } from 'react-native-paper';
import { db } from '../../config/firebase';
import { collection, query, where, getDocs, doc, getDoc } from 'firebase/firestore';
import CustomButton from '../../components/common/CustomButton';

const ChildProgress = ({ route }) => {
  const { childId } = route.params;
  const [childData, setChildData] = useState(null);
  const [grades, setGrades] = useState([]);
  const [attendance, setAttendance] = useState([]);
  const [behavior, setBehavior] = useState([]);
  const [homework, setHomework] = useState([]);
  const [selectedItem, setSelectedItem] = useState(null);
  const [modalVisible, setModalVisible] = useState(false);

  useEffect(() => {
    fetchChildData();
    fetchGrades();
    fetchAttendance();
    fetchBehavior();
    fetchHomework();
  }, [childId]);

  const fetchChildData = async () => {
    try {
      const childDoc = await getDoc(doc(db, 'users', childId));
      if (childDoc.exists()) {
        setChildData(childDoc.data());
      }
    } catch (error) {
      console.error('Error fetching child data:', error);
    }
  };

  const fetchGrades = async () => {
    try {
      const gradesRef = collection(db, 'grades');
      const q = query(gradesRef, where('studentId', '==', childId));
      const querySnapshot = await getDocs(q);
      
      const gradesData = [];
      querySnapshot.forEach((doc) => {
        gradesData.push({ id: doc.id, ...doc.data() });
      });
      
      setGrades(gradesData);
    } catch (error) {
      console.error('Error fetching grades:', error);
    }
  };

  const fetchAttendance = async () => {
    try {
      const attendanceRef = collection(db, 'attendance');
      const q = query(attendanceRef, where('studentId', '==', childId));
      const querySnapshot = await getDocs(q);
      
      const attendanceData = [];
      querySnapshot.forEach((doc) => {
        attendanceData.push({ id: doc.id, ...doc.data() });
      });
      
      setAttendance(attendanceData);
    } catch (error) {
      console.error('Error fetching attendance:', error);
    }
  };

  const fetchBehavior = async () => {
    try {
      const behaviorRef = collection(db, 'behavior');
      const q = query(behaviorRef, where('studentId', '==', childId));
      const querySnapshot = await getDocs(q);
      
      const behaviorData = [];
      querySnapshot.forEach((doc) => {
        behaviorData.push({ id: doc.id, ...doc.data() });
      });
      
      setBehavior(behaviorData);
    } catch (error) {
      console.error('Error fetching behavior:', error);
    }
  };

  const fetchHomework = async () => {
    try {
      const homeworkRef = collection(db, 'homework');
      const q = query(homeworkRef, where('studentId', '==', childId));
      const querySnapshot = await getDocs(q);
      
      const homeworkData = [];
      querySnapshot.forEach((doc) => {
        homeworkData.push({ id: doc.id, ...doc.data() });
      });
      
      setHomework(homeworkData);
    } catch (error) {
      console.error('Error fetching homework:', error);
    }
  };

  const calculateOverallGrade = () => {
    if (grades.length === 0) return 'N/A';
    const sum = grades.reduce((acc, grade) => acc + grade.score, 0);
    return (sum / grades.length).toFixed(1);
  };

  const calculateAttendancePercentage = () => {
    if (attendance.length === 0) return 'N/A';
    const present = attendance.filter(record => record.present).length;
    return ((present / attendance.length) * 100).toFixed(1) + '%';
  };

  const renderHomeworkSection = () => (
    <Card style={styles.sectionCard}>
      <Card.Content>
        <Title>Homework Status</Title>
        {homework.length > 0 ? (
          homework.map((item) => (
            <List.Item
              key={item.id}
              title={item.subject}
              description={`Due: ${item.dueDate}`}
              right={() => (
                <Text style={[
                  styles.status,
                  { color: item.status === 'completed' ? '#4CAF50' : '#FF9800' }
                ]}>
                  {item.status}
                </Text>
              )}
              onPress={() => {
                setSelectedItem(item);
                setModalVisible(true);
              }}
            />
          ))
        ) : (
          <Text>No homework assignments found</Text>
        )}
      </Card.Content>
    </Card>
  );

  const DetailModal = () => (
    <Portal>
      <Modal
        visible={modalVisible}
        onDismiss={() => setModalVisible(false)}
        contentContainerStyle={styles.modalContent}
      >
        {selectedItem && (
          <View>
            <Title>{selectedItem.subject}</Title>
            <Divider style={styles.divider} />
            <Text>Description: {selectedItem.description}</Text>
            <Text>Due Date: {selectedItem.dueDate}</Text>
            <Text>Status: {selectedItem.status}</Text>
            {selectedItem.feedback && (
              <Text>Teacher's Feedback: {selectedItem.feedback}</Text>
            )}
            <CustomButton
              mode="contained"
              onPress={() => setModalVisible(false)}
              style={styles.modalButton}
            >
              Close
            </CustomButton>
          </View>
        )}
      </Modal>
    </Portal>
  );

  return (
    <ScrollView style={styles.container}>
      <Card style={styles.overviewCard}>
        <Card.Content>
          <Title>{childData?.displayName}</Title>
          <Text>Class: {childData?.class}</Text>
          <View style={styles.statsContainer}>
            <View style={styles.statItem}>
              <Text style={styles.statLabel}>Overall Grade</Text>
              <Text style={styles.statValue}>{calculateOverallGrade()}</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statLabel}>Attendance</Text>
              <Text style={styles.statValue}>{calculateAttendancePercentage()}</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statLabel}>Behavior</Text>
              <Text style={styles.statValue}>Good</Text>
            </View>
          </View>
        </Card.Content>
      </Card>

      <Card style={styles.sectionCard}>
        <Card.Content>
          <Title>Recent Grades</Title>
          {grades.slice(0, 5).map((grade) => (
            <List.Item
              key={grade.id}
              title={grade.subject}
              description={`Score: ${grade.score}/${grade.maxScore}`}
              right={() => <Text style={styles.grade}>{((grade.score/grade.maxScore) * 100).toFixed(1)}%</Text>}
              onPress={() => {
                setSelectedItem({
                  title: 'Grade Details',
                  description: grade.subject,
                  details: {
                    Score: `${grade.score}/${grade.maxScore}`,
                    Percentage: `${((grade.score/grade.maxScore) * 100).toFixed(1)}%`,
                    Date: new Date(grade.date.seconds * 1000).toLocaleDateString(),
                    Comments: grade.comments || 'No comments'
                  }
                });
                setModalVisible(true);
              }}
            />
          ))}
        </Card.Content>
      </Card>

      <Card style={styles.sectionCard}>
        <Card.Content>
          <Title>Recent Attendance</Title>
          {attendance.slice(0, 5).map((record) => (
            <List.Item
              key={record.id}
              title={new Date(record.date.seconds * 1000).toLocaleDateString()}
              description={record.present ? 'Present' : 'Absent'}
              right={() => (
                <Text style={record.present ? styles.present : styles.absent}>
                  {record.present ? '✓' : '✗'}
                </Text>
              )}
            />
          ))}
        </Card.Content>
      </Card>

      {renderHomeworkSection()}

      <Card style={styles.sectionCard}>
        <Card.Content>
          <Title>Behavior Reports</Title>
          {behavior.map((report) => (
            <List.Item
              key={report.id}
              title={report.type}
              description={report.description}
              left={props => (
                <List.Icon 
                  {...props} 
                  icon={report.type === 'positive' ? 'emoticon-happy' : 'emoticon-sad'} 
                />
              )}
            />
          ))}
        </Card.Content>
      </Card>

      <DetailModal />
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  overviewCard: {
    margin: 10,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 15,
  },
  statItem: {
    alignItems: 'center',
  },
  statLabel: {
    color: '#666',
    fontSize: 12,
  },
  statValue: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 5,
  },
  sectionCard: {
    margin: 10,
  },
  grade: {
    alignSelf: 'center',
    fontWeight: 'bold',
  },
  present: {
    color: 'green',
    fontWeight: 'bold',
    alignSelf: 'center',
  },
  absent: {
    color: 'red',
    fontWeight: 'bold',
    alignSelf: 'center',
  },
  modalContent: {
    backgroundColor: 'white',
    padding: 20,
    margin: 20,
    borderRadius: 10,
  },
  divider: {
    marginVertical: 10,
  },
  modalButton: {
    marginTop: 20,
  },
  status: {
    fontWeight: 'bold',
  },
});

export default ChildProgress;
