import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  ActivityIndicator
} from 'react-native';
import {
  Text,
  Surface,
  Avatar,
  Chip,
  Searchbar,
  IconButton,
  Menu,
  Divider,
  Button,
  Portal,
  Modal,
  Title,
  Paragraph,
  List,
  Badge,
  ProgressBar,
  Snackbar,
  Dialog,
  FAB
} from 'react-native-paper';
import { db, auth } from '../../config/firebase';
import {
  collection,
  query,
  getDocs,
  doc,
  getDoc,
  updateDoc,
  deleteDoc,
  addDoc,
  orderBy,
  where,
  limit,
  writeBatch
} from 'firebase/firestore';
import { sendPasswordResetEmail } from 'firebase/auth';
import { useLanguage } from '../../context/LanguageContext';
import { useNotifications } from '../../context/NotificationContext';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import * as Animatable from 'react-native-animatable';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import MobileScreenWrapper from '../../components/common/MobileScreenWrapper';
import NetInfo from '@react-native-community/netinfo';
import mobileTheme from '../../theme/mobileTheme';
import { format, formatDistance } from 'date-fns';
import { SwipeListView } from 'react-native-swipe-list-view';

const { width } = Dimensions.get('window');

const MobileUserManagement = () => {
  const navigation = useNavigation();
  const { translate, isRTL } = useLanguage();
  const { showToast } = useNotifications();
  
  // Data state
  const [users, setUsers] = useState([]);
  const [filteredUsers, setFilteredUsers] = useState([]);
  const [userStats, setUserStats] = useState({
    total: 0,
    active: 0,
    inactive: 0,
    new: 0
  });
  
  // UI state
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [showUserDetailsModal, setShowUserDetailsModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [showFilterMenu, setShowFilterMenu] = useState(false);
  const [showSortMenu, setShowSortMenu] = useState(false);
  const [filterRole, setFilterRole] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');
  const [sortBy, setSortBy] = useState('name');
  const [sortDirection, setSortDirection] = useState('asc');
  
  // Confirmation dialogs
  const [showDeleteConfirmDialog, setShowDeleteConfirmDialog] = useState(false);
  const [showActivateConfirmDialog, setShowActivateConfirmDialog] = useState(false);
  const [showDeactivateConfirmDialog, setShowDeactivateConfirmDialog] = useState(false);
  const [showPasswordResetDialog, setShowPasswordResetDialog] = useState(false);
  const [passwordResetLoading, setPasswordResetLoading] = useState(false);
  
  // Network and error state
  const [isConnected, setIsConnected] = useState(true);
  const [errorMessage, setErrorMessage] = useState('');
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [lastUpdated, setLastUpdated] = useState(null);
  const [loadingProgress, setLoadingProgress] = useState(0);
  
  // Bottom navigation items
  const bottomNavItems = [
    { key: 'dashboard', icon: 'view-dashboard', label: 'common.dashboard', route: 'AdminDashboard' },
    { key: 'users', icon: 'account-group', label: 'userManagement.title', route: 'UserManagement' },
    { key: 'teachers', icon: 'teach', label: 'teacher.management.title', route: 'TeacherManagement' },
    { key: 'students', icon: 'account-school', label: 'student.title', route: 'StudentManagement' },
    { key: 'more', icon: 'dots-horizontal', label: 'common.more', route: 'AdminMore' }
  ];
  
  // FAB actions - optimized for mobile
  const fabActions = [
    {
      icon: 'account-plus',
      label: translate('userManagement.addUser'),
      onPress: () => navigation.navigate('UserRegistration'),
      style: { backgroundColor: mobileTheme.colors.admin }
    },
    {
      icon: 'filter-variant',
      label: translate('common.filter'),
      onPress: () => setShowFilterMenu(true),
      style: { backgroundColor: mobileTheme.colors.secondary }
    },
    {
      icon: 'sort',
      label: translate('common.sort'),
      onPress: () => setShowSortMenu(true),
      style: { backgroundColor: mobileTheme.colors.info }
    },
    {
      icon: 'refresh',
      label: translate('common.refresh'),
      onPress: onRefresh,
      style: { backgroundColor: mobileTheme.colors.success }
    }
  ];
  
  // Function to fetch all data
  const fetchAllData = useCallback(async () => {
    try {
      setErrorMessage('');
      setLoadingProgress(0.1);
      
      // Check network connectivity
      const netInfo = await NetInfo.fetch();
      setIsConnected(netInfo.isConnected);
      
      if (!netInfo.isConnected) {
        setErrorMessage(translate('common.noInternetConnection'));
        setSnackbarVisible(true);
        return;
      }
      
      await fetchUsers();
      
      // Update last updated timestamp
      setLastUpdated(new Date());
    } catch (error) {
      console.error('Error fetching data:', error);
      setErrorMessage(translate('common.errorFetchingData'));
      setSnackbarVisible(true);
    } finally {
      setRefreshing(false);
      setLoadingProgress(0);
    }
  }, [translate]);
  
  // Pull-to-refresh handler
  const onRefresh = useCallback(() => {
    setRefreshing(true);
    fetchAllData();
  }, [fetchAllData]);
  
  // Initial data loading
  useEffect(() => {
    fetchAllData();
    
    // Set up network connectivity listener
    const unsubscribe = NetInfo.addEventListener(state => {
      setIsConnected(state.isConnected);
      
      // If connection is restored, refresh data
      if (state.isConnected && !isConnected) {
        fetchAllData();
      }
    });
    
    // Clean up listener on unmount
    return () => unsubscribe();
  }, [fetchAllData, isConnected]);
  
  // Refresh data when screen comes into focus
  useFocusEffect(
    useCallback(() => {
      fetchAllData();
    }, [fetchAllData])
  );
  
  // Refresh data when filters or sort options change
  useEffect(() => {
    if (!loading) {
      fetchUsers();
    }
  }, [filterRole, filterStatus, sortBy, sortDirection]);
  
  // Fetch users
  const fetchUsers = async () => {
    try {
      setLoading(true);
      setErrorMessage('');
      setLoadingProgress(0.2);
      
      const usersRef = collection(db, 'users');
      
      // Create query
      let q = query(usersRef);
      
      // Apply role filter if not 'all'
      if (filterRole !== 'all') {
        q = query(q, where('role', '==', filterRole));
      }
      
      // Apply status filter if not 'all'
      if (filterStatus !== 'all') {
        q = query(q, where('status', '==', filterStatus));
      }
      
      // Apply sorting if field exists
      if (sortBy === 'name') {
        // Special case for name sorting since it's a combination of firstName and lastName
        q = query(q, orderBy('firstName', sortDirection));
      } else if (sortBy) {
        q = query(q, orderBy(sortBy, sortDirection));
      }
      
      // Limit to 100 users for performance
      q = query(q, limit(100));
      
      setLoadingProgress(0.4);
      const querySnapshot = await getDocs(q);
      setLoadingProgress(0.6);
      
      const usersData = [];
      let activeCount = 0;
      let inactiveCount = 0;
      let newCount = 0;
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      
      querySnapshot.forEach((docSnapshot) => {
        const userData = { id: docSnapshot.id, ...docSnapshot.data() };
        
        // Ensure all required fields exist
        const processedUser = {
          ...userData,
          firstName: userData.firstName || '',
          lastName: userData.lastName || '',
          email: userData.email || '',
          role: userData.role || 'unknown',
          status: userData.status || 'inactive',
          createdAt: userData.createdAt || null,
          lastLogin: userData.lastLogin || null,
          phoneNumber: userData.phoneNumber || '',
          key: docSnapshot.id, // Add key for SwipeListView
        };
        
        // Count for stats
        if (processedUser.status === 'active') activeCount++;
        else inactiveCount++;
        
        if (processedUser.createdAt && new Date(processedUser.createdAt) > thirtyDaysAgo) {
          newCount++;
        }
        
        usersData.push(processedUser);
      });
      
      setLoadingProgress(0.8);
      
      setUsers(usersData);
      setFilteredUsers(usersData);
      setUserStats({
        total: usersData.length,
        active: activeCount,
        inactive: inactiveCount,
        new: newCount
      });
      
      setLoadingProgress(1);
      setLoading(false);
      setRefreshing(false);
      
      return true;
    } catch (error) {
      console.error('Error fetching users:', error);
      setErrorMessage(translate('userManagement.errorFetching'));
      setLoading(false);
      setRefreshing(false);
      return false;
    }
  };
