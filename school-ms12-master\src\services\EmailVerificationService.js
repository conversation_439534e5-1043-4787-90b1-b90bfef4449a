import { db, auth } from '../config/firebase';
import {
  collection,
  addDoc,
  query,
  where,
  getDocs,
  updateDoc,
  doc,
  deleteDoc,
  serverTimestamp,
  Timestamp,
  setDoc
} from 'firebase/firestore';
import {
  sendEmailVerification,
  applyActionCode,
  checkActionCode,
  sendPasswordResetEmail,
  verifyPasswordResetCode,
  confirmPasswordReset,
  EmailAuthProvider,
  reauthenticateWithCredential,
  updatePassword,
  getAuth,
  signInWithEmailAndPassword
} from 'firebase/auth';
import { v4 as uuidv4 } from 'uuid';
import '../utils/uuidPolyfill'; // Import the UUID polyfill
import { generateSimpleUUID } from '../utils/uuidPolyfill';

class EmailVerificationService {
  // Generate a verification token
  static async generateVerificationToken(email, type = 'student', expiresInHours = 24) {
    try {
      // Check if there's an existing token for this email
      const existingTokens = await this.getVerificationTokens(email);

      // Delete any existing tokens for this email
      for (const token of existingTokens) {
        await deleteDoc(doc(db, 'verificationTokens', token.id));
      }

      // Generate a new token with fallback
      let token;
      try {
        token = uuidv4();
      } catch (uuidError) {
        console.error('UUID generation failed, using fallback:', uuidError);
        token = generateSimpleUUID();
      }

      const expiresAt = new Date();
      expiresAt.setHours(expiresAt.getHours() + expiresInHours);

      // Store the token in Firestore
      const tokenRef = collection(db, 'verificationTokens');
      const tokenDoc = await addDoc(tokenRef, {
        email,
        token,
        type,
        createdAt: serverTimestamp(),
        expiresAt: Timestamp.fromDate(expiresAt),
        verified: false
      });

      return {
        token,
        id: tokenDoc.id,
        expiresAt
      };
    } catch (error) {
      console.error('Error generating verification token:', error);
      throw error;
    }
  }

  // Get verification tokens for an email
  static async getVerificationTokens(email) {
    try {
      const tokenRef = collection(db, 'verificationTokens');
      const q = query(tokenRef, where('email', '==', email));
      const querySnapshot = await getDocs(q);

      const tokens = [];
      querySnapshot.forEach(doc => {
        tokens.push({
          id: doc.id,
          ...doc.data()
        });
      });

      return tokens;
    } catch (error) {
      console.error('Error getting verification tokens:', error);
      throw error;
    }
  }

  // Verify a token
  static async verifyToken(email, token) {
    try {
      const tokenRef = collection(db, 'verificationTokens');
      const q = query(
        tokenRef,
        where('email', '==', email),
        where('token', '==', token)
      );
      const querySnapshot = await getDocs(q);

      if (querySnapshot.empty) {
        return {
          valid: false,
          message: 'Invalid verification token'
        };
      }

      const tokenDoc = querySnapshot.docs[0];
      const tokenData = tokenDoc.data();

      // Check if token is expired
      const now = new Date();
      const expiresAt = tokenData.expiresAt.toDate();

      if (now > expiresAt) {
        return {
          valid: false,
          message: 'Verification token has expired'
        };
      }

      // Mark token as verified
      await updateDoc(doc(db, 'verificationTokens', tokenDoc.id), {
        verified: true,
        verifiedAt: serverTimestamp()
      });

      return {
        valid: true,
        message: 'Email verified successfully',
        type: tokenData.type
      };
    } catch (error) {
      console.error('Error verifying token:', error);
      throw error;
    }
  }

  // Check if an email is verified
  static async isEmailVerified(email) {
    try {
      // Validate email format first
      if (!email || !this.isValidEmail(email)) {
        console.log('Invalid email format:', email);
        return false;
      }

      // Check app_verifications collection first
      try {
        const verificationQuery = query(
          collection(db, 'app_verifications'),
          where('email', '==', email),
          where('verified', '==', true)
        );

        const querySnapshot = await getDocs(verificationQuery);

        if (!querySnapshot.empty) {
          console.log('Email verified in app_verifications:', email);
          return true;
        }
      } catch (error) {
        console.error('Error checking app_verifications:', error);
      }

      // Check users collection
      try {
        const usersRef = collection(db, 'users');
        const q = query(usersRef, where('email', '==', email));
        const querySnapshot = await getDocs(q);

        if (!querySnapshot.empty) {
          const userData = querySnapshot.docs[0].data();
          if (userData.emailVerified) {
            console.log('Email already verified in Firestore:', email);
            return true;
          }
        }
      } catch (error) {
        console.error('Error checking user verification status:', error);
      }

      // Check verification tokens
      try {
        const tokenRef = collection(db, 'verificationTokens');
        const q = query(
          tokenRef,
          where('email', '==', email),
          where('verified', '==', true)
        );
        const querySnapshot = await getDocs(q);

        if (!querySnapshot.empty) {
          console.log('Valid verification token found for:', email);
          return true;
        }
      } catch (error) {
        console.error('Error checking verification tokens:', error);
      }

      console.log('Email not verified:', email);
      return false;
    } catch (error) {
      console.error('Error checking email verification:', error);
      return false; // Return false instead of throwing to prevent app crashes
    }
  }

  // Helper method to validate email format
  static isValidEmail(email) {
    if (!email || typeof email !== 'string' || email.length < 5) {
      return false;
    }

    // More comprehensive email validation
    try {
      // Simpler but effective email regex
      const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

      // Check if the email matches the regex
      const isValid = emailRegex.test(email);

      if (!isValid) {
        console.log('Email validation failed for:', email);
      }

      return isValid;
    } catch (e) {
      console.log('Error in email validation:', e);
      return false;
    }
  }

  // Send Firebase Auth email verification
  static async sendFirebaseVerificationEmail(user) {
    try {
      await sendEmailVerification(user);

      // Log the verification request
      await addDoc(collection(db, 'verification_logs'), {
        userId: user.uid,
        email: user.email,
        type: 'firebase_auth',
        timestamp: serverTimestamp()
      });

      return true;
    } catch (error) {
      console.error('Error sending Firebase verification email:', error);
      throw error;
    }
  }

  // Apply verification code (for Firebase Auth verification)
  static async applyVerificationCode(code) {
    try {
      // Check the action code first
      const actionInfo = await checkActionCode(auth, code);
      const email = actionInfo.data.email;

      // Apply the verification code
      await applyActionCode(auth, code);

      // Log the verification
      await addDoc(collection(db, 'verification_logs'), {
        email: email,
        type: 'firebase_auth_verified',
        timestamp: serverTimestamp()
      });

      // Update user document to mark as verified
      const usersRef = collection(db, 'users');
      const q = query(usersRef, where('email', '==', email));
      const querySnapshot = await getDocs(q);

      if (!querySnapshot.empty) {
        const userDoc = querySnapshot.docs[0];
        const userData = userDoc.data();

        // Update user document
        await updateDoc(doc(db, 'users', userDoc.id), {
          emailVerified: true,
          updatedAt: serverTimestamp()
        });

        // Send credentials email with user details
        try {
          const EmailNotificationService = (await import('./EmailNotificationService')).default;

          // Get the user's password from verification_credentials collection
          const credentialsRef = collection(db, 'verification_credentials');
          const credentialsQuery = query(credentialsRef, where('email', '==', email));
          const credentialsSnapshot = await getDocs(credentialsQuery);

          if (!credentialsSnapshot.empty) {
            const credentialsDoc = credentialsSnapshot.docs[0];
            const credentialsData = credentialsDoc.data();
            const password = credentialsData.password;

            // Send credentials email
            await EmailNotificationService.sendCredentialsEmail(
              email,
              password,
              userData,
              userData.role
            );

            // Delete the credentials document for security
            await deleteDoc(doc(db, 'verification_credentials', credentialsDoc.id));
          }
        } catch (emailError) {
          console.error('Error sending credentials email:', emailError);
          // Continue even if sending credentials email fails
        }
      }

      return {
        valid: true,
        email: email,
        message: 'Email verified successfully'
      };
    } catch (error) {
      console.error('Error applying verification code:', error);
      return {
        valid: false,
        message: 'Invalid or expired verification code'
      };
    }
  }

  // Send password reset email
  static async sendPasswordResetEmail(email) {
    try {
      // Use Firebase's built-in password reset functionality
      await sendPasswordResetEmail(auth, email);

      // Log the password reset request with a try-catch to handle permission issues
      try {
        await addDoc(collection(db, 'password_reset_logs'), {
          email,
          timestamp: serverTimestamp(),
          status: 'requested'
        });
      } catch (logError) {
        // If logging fails, just log to console but don't fail the operation
        console.warn('Failed to log password reset request:', logError.message);
      }

      return true;
    } catch (error) {
      console.error('Error sending password reset email:', error);
      // Return a structured error instead of throwing
      return {
        success: false,
        message: 'Failed to send password reset email: ' + error.message
      };
    }
  }

  // Verify password reset code
  static async verifyPasswordResetCode(code) {
    try {
      const email = await verifyPasswordResetCode(auth, code);

      return {
        valid: true,
        email,
        message: 'Password reset code is valid'
      };
    } catch (error) {
      console.error('Error verifying password reset code:', error);
      return {
        valid: false,
        message: 'Invalid or expired password reset code'
      };
    }
  }

  // Complete password reset
  static async confirmPasswordReset(code, newPassword) {
    try {
      // First verify the code is valid
      let email;
      try {
        email = await verifyPasswordResetCode(auth, code);
      } catch (verifyError) {
        console.error('Error verifying password reset code:', verifyError);
        return {
          success: false,
          message: 'Invalid or expired password reset code'
        };
      }

      // Confirm the password reset
      await confirmPasswordReset(auth, code, newPassword);

      // Log the password reset completion with error handling
      try {
        await addDoc(collection(db, 'password_reset_logs'), {
          email,
          timestamp: serverTimestamp(),
          status: 'completed'
        });
      } catch (logError) {
        // If logging fails, just log to console but don't fail the operation
        console.warn('Failed to log password reset completion:', logError.message);
      }

      return {
        success: true,
        message: 'Password has been reset successfully'
      };
    } catch (error) {
      console.error('Error confirming password reset:', error);
      return {
        success: false,
        message: 'Failed to reset password: ' + error.message
      };
    }
  }

  // Change password (for authenticated users)
  static async changePassword(currentPassword, newPassword) {
    try {
      const user = auth.currentUser;

      if (!user) {
        throw new Error('No authenticated user found');
      }

      // Re-authenticate user
      const credential = EmailAuthProvider.credential(user.email, currentPassword);
      await reauthenticateWithCredential(user, credential);

      // Update password
      await updatePassword(user, newPassword);

      // Log the password change
      await addDoc(collection(db, 'password_change_logs'), {
        userId: user.uid,
        email: user.email,
        timestamp: serverTimestamp()
      });

      return {
        success: true,
        message: 'Password changed successfully'
      };
    } catch (error) {
      console.error('Error changing password:', error);

      let errorMessage = 'Failed to change password';
      if (error.code === 'auth/wrong-password') {
        errorMessage = 'Current password is incorrect';
      } else if (error.code === 'auth/weak-password') {
        errorMessage = 'New password is too weak';
      } else if (error.code === 'auth/requires-recent-login') {
        errorMessage = 'Please log in again before changing your password';
      }

      return {
        success: false,
        message: errorMessage
      };
    }
  }
}

export default EmailVerificationService;
