import React, { useState, useEffect, useRef, useCallback } from 'react';
import { View, ScrollView, StyleSheet, Animated, StatusBar, TouchableOpacity, FlatList, KeyboardAvoidingView, Platform, AppState, Dimensions } from 'react-native';
import { Card, Title, Text, FAB, Portal, Modal, Searchbar, List, Chip, Avatar, ActivityIndicator, IconButton, Button, Divider, Snackbar, Provider as PaperProvider, DefaultTheme, useTheme, Menu, Badge, SegmentedButtons } from 'react-native-paper';
import { useAuth } from '../../context/AuthContext';
import { useNavigation } from '@react-navigation/native';
import { useLanguage } from '../../context/LanguageContext';
import EnhancedMessageService from '../../services/EnhancedMessageService';
import CustomButton from '../../components/common/CustomButton';
import CustomInput from '../../components/common/CustomInput';
import { SafeAreaView } from 'react-native-safe-area-context';
import * as Animatable from 'react-native-animatable';
import { LinearGradient } from 'expo-linear-gradient';

// Role-specific headers and sidebars
import AdminAppHeader from '../../components/common/AdminAppHeader';
import AdminSidebar from '../../components/common/AdminSidebar';
import TeacherAppHeader from '../../components/common/TeacherAppHeader';
import TeacherSidebar from '../../components/common/TeacherSidebar';
import StudentAppHeader from '../../components/common/StudentAppHeader';
import StudentSidebar from '../../components/common/StudentSidebar';
import ParentAppHeader from '../../components/common/ParentAppHeader';
import ParentSidebar from '../../components/common/ParentSidebar';
import SidebarBackdrop from '../../components/common/SidebarBackdrop';

// Notifications handling will be implemented later
import { db } from '../../config/firebase';
import { doc, updateDoc, serverTimestamp } from 'firebase/firestore';

const CommunicationCenter = ({ customHeader, customSidebar, userRole = 'admin' }) => {
  const { user } = useAuth();
  const navigation = useNavigation();
  const theme = useTheme();
  const { translate, language, isRTL } = useLanguage();

  // UI state variables
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [menuVisible, setMenuVisible] = useState(false);
  const [logoutDialogVisible, setLogoutDialogVisible] = useState(false);

  // Sidebar state
  const [drawerOpen, setDrawerOpen] = useState(false);
  const drawerAnim = useRef(new Animated.Value(-300)).current;
  const backdropFadeAnim = useRef(new Animated.Value(0)).current;
  const [activeSidebarItem, setActiveSidebarItem] = useState('CommunicationCenter');

  // Communication state
  const [conversations, setConversations] = useState([]);
  const [messages, setMessages] = useState([]);
  const [selectedConversation, setSelectedConversation] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(false);

  // User selection state
  const [userSelectVisible, setUserSelectVisible] = useState(false);
  const [users, setUsers] = useState([]);
  const [selectedUserRole, setSelectedUserRole] = useState('all');
  const [userSearchQuery, setUserSearchQuery] = useState('');
  const [filteredUsers, setFilteredUsers] = useState([]);
  const [loadingUsers, setLoadingUsers] = useState(false);

  // Message composition state
  const [newMessageVisible, setNewMessageVisible] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);

  // Online status tracking
  const [onlineUsers, setOnlineUsers] = useState({});
  const messagesEndRef = useRef(null);

  // Message form data
  const [formData, setFormData] = useState({
    recipientId: '',
    subject: '',
    content: '',
    attachments: []
  });

  // Conversation list visibility state
  const [conversationListVisible, setConversationListVisible] = useState(true);

  // Chat interface visibility state - initially hidden until a conversation is selected
  const [chatInterfaceVisible, setChatInterfaceVisible] = useState(false);

  // Toggle drawer function
  const toggleDrawer = () => {
    if (drawerOpen) {
      // Close drawer
      Animated.parallel([
        Animated.timing(drawerAnim, {
          toValue: -300,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(backdropFadeAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        })
      ]).start(() => setDrawerOpen(false));
    } else {
      // Open drawer
      setDrawerOpen(true);
      Animated.parallel([
        Animated.timing(drawerAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(backdropFadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        })
      ]).start();
    }
  };

  // Handle logout
  const handleLogout = () => {
    navigation.reset({
      index: 0,
      routes: [{ name: 'Login' }],
    });
  };

  // Setup navigation
  useEffect(() => {
    // Hide the default header
    navigation.setOptions({
      headerShown: false,
    });
  }, [navigation]);

  // Handle screen dimension changes
  useEffect(() => {
    const handleDimensionChange = ({ window }) => {
      // On small screens, hide conversation list when a conversation is selected
      if (window.width < 768 && selectedConversation) {
        setConversationListVisible(false);
      } else if (window.width >= 1200) {
        // On large screens, always show conversation list
        setConversationListVisible(true);
      }
    };

    // Add event listener
    const subscription = Dimensions.addEventListener('change', handleDimensionChange);

    // Initial check
    handleDimensionChange({ window: Dimensions.get('window') });

    // Cleanup
    return () => subscription.remove();
  }, [selectedConversation]);

  // Initialize app and fetch conversations
  useEffect(() => {
    // Register for push notifications
    const registerForPushNotifications = async () => {
      try {
        // Update user's online status
        const userRef = doc(db, 'users', user.uid);
        await updateDoc(userRef, {
          isOnline: true,
          lastSeen: serverTimestamp(),
          lastUpdated: serverTimestamp()
        });

        // In a real app, you would implement proper push notification registration
        // This would typically involve:
        // 1. Checking if the device supports notifications
        // 2. Requesting permission from the user
        // 3. Getting a push token from the notification service
        // 4. Storing that token in your database

        console.log('Push notification registration would happen here in a production app');
      } catch (error) {
        console.error('Error updating user status:', error);
      }
    };

    // Set user as online
    const setUserOnline = async () => {
      try {
        await EnhancedMessageService.updateUserStatus(user.uid, true);
      } catch (error) {
        console.error('Error setting user online:', error);
      }
    };

    // Fetch initial data
    fetchConversations();
    registerForPushNotifications();
    setUserOnline();

    // Set up interval to refresh conversations
    const conversationsInterval = setInterval(fetchConversations, 30000); // Refresh every 30 seconds

    // Set up interval to update online status (heartbeat)
    const onlineStatusInterval = setInterval(() => {
      updateOnlineStatus(true);
    }, 60000); // Update every minute

    // Set up event listener for app state changes to update online status
    const subscription = AppState.addEventListener('change', nextAppState => {
      if (nextAppState === 'active') {
        updateOnlineStatus(true);
      } else if (nextAppState === 'background' || nextAppState === 'inactive') {
        updateOnlineStatus(false);
      }
    });

    // Cleanup function
    return () => {
      clearInterval(conversationsInterval);
      clearInterval(onlineStatusInterval);
      subscription.remove();
      updateOnlineStatus(false); // Set user as offline when component unmounts
    };
  }, []);

  // Handle selected conversation changes
  useEffect(() => {
    if (selectedConversation) {
      // Subscribe to messages in the conversation
      const unsubscribe = subscribeToMessages(selectedConversation.id);

      // Subscribe to other participant's online status
      const otherParticipantId = selectedConversation.participants.find(p => p !== user.uid);
      if (otherParticipantId) {
        subscribeToUserStatus(otherParticipantId);
      }

      // Mark all messages as read when conversation is selected
      EnhancedMessageService.markAllAsRead(selectedConversation.id, user.uid)
        .then(() => {
          // Refresh conversations to update unread counts
          fetchConversations();
        })
        .catch(error => {
          console.error('Error marking messages as read:', error);
        });

      // Cleanup function
      return () => {
        unsubscribe();
        if (otherParticipantId) {
          EnhancedMessageService.unsubscribeFromUserStatus(otherParticipantId);
        }
      };
    }
  }, [selectedConversation]);

  // Fetch conversations for the current user
  const fetchConversations = async () => {
    try {
      setLoading(true);
      const conversationsData = await EnhancedMessageService.getConversations(user.uid);
      setConversations(conversationsData);
    } catch (error) {
      console.error('Error fetching conversations:', error);
      setSnackbarMessage(translate('communication.errorFetchingConversations') || 'Error fetching conversations');
      setSnackbarVisible(true);
    } finally {
      setLoading(false);
    }
  };

  // Fetch messages for a conversation
  const fetchMessages = async (conversationId) => {
    try {
      const messagesData = await EnhancedMessageService.getMessages(conversationId);
      setMessages(messagesData);

      // Mark all messages as read
      await EnhancedMessageService.markAllAsRead(conversationId, user.uid);

      // Scroll to bottom of messages
      if (messagesEndRef.current) {
        messagesEndRef.current.scrollToEnd({ animated: true });
      }
    } catch (error) {
      console.error('Error fetching messages:', error);
      setSnackbarMessage(translate('communication.errorFetchingMessages') || 'Error fetching messages');
      setSnackbarVisible(true);
    }
  };

  // Fetch users by role
  const fetchUsersByRole = async (role = 'all') => {
    try {
      setLoadingUsers(true);

      // Modify the role filter based on the current user's role
      let effectiveRole = role;

      // If role is 'all', we need to restrict based on user role
      if (role === 'all') {
        if (userRole === 'teacher') {
          // Teachers can only message students, parents, and admins
          effectiveRole = ['student', 'parent', 'admin'];
        } else if (userRole === 'student') {
          // Students can only message teachers and admins
          effectiveRole = ['teacher', 'admin'];
        } else if (userRole === 'parent') {
          // Parents can only message teachers and admins
          effectiveRole = ['teacher', 'admin'];
        }
        // Admins can message anyone, so keep 'all'
      }

      const usersData = await EnhancedMessageService.getUsersByRole(effectiveRole, userRole);
      setUsers(usersData);
      setFilteredUsers(usersData);

      if (usersData.length === 0) {
        setSnackbarMessage(translate('communication.noUsersFound') || 'No users found');
        setSnackbarVisible(true);
      }
    } catch (error) {
      console.error('Error fetching users:', error);
      setSnackbarMessage(translate('communication.errorFetchingUsers') || 'Error fetching users');
      setSnackbarVisible(true);
    } finally {
      setLoadingUsers(false);
    }
  };

  // Search users
  const searchUsers = async (query, role = selectedUserRole) => {
    try {
      setLoadingUsers(true);

      // Modify the role filter based on the current user's role
      let effectiveRole = role;

      // If role is 'all', we need to restrict based on user role
      if (role === 'all') {
        if (userRole === 'teacher') {
          // Teachers can only message students, parents, and admins
          effectiveRole = ['student', 'parent', 'admin'];
        } else if (userRole === 'student') {
          // Students can only message teachers and admins
          effectiveRole = ['teacher', 'admin'];
        } else if (userRole === 'parent') {
          // Parents can only message teachers and admins
          effectiveRole = ['teacher', 'admin'];
        }
        // Admins can message anyone, so keep 'all'
      }

      const results = await EnhancedMessageService.searchUsers(query, effectiveRole, userRole);
      setFilteredUsers(results);

      if (query.trim() && results.length === 0) {
        setSnackbarMessage(translate('communication.noSearchResults') || 'No users found matching your search');
        setSnackbarVisible(true);
      }
    } catch (error) {
      console.error('Error searching users:', error);
      setSnackbarMessage(translate('communication.errorSearchingUsers') || 'Error searching users');
      setSnackbarVisible(true);
    } finally {
      setLoadingUsers(false);
    }
  };

  // Subscribe to user online status
  const subscribeToUserStatus = (userId) => {
    EnhancedMessageService.subscribeToUserStatus(userId, (status) => {
      setOnlineUsers(prev => ({
        ...prev,
        [userId]: status
      }));
    });
  };

  // Subscribe to messages in a conversation
  const subscribeToMessages = (conversationId) => {
    return EnhancedMessageService.subscribeToMessages(conversationId, (messagesData) => {
      setMessages(messagesData);

      // Mark messages as read
      messagesData.forEach(message => {
        if (!message.readBy.includes(user.uid)) {
          EnhancedMessageService.markAsRead(message.id, user.uid);
        }
      });

      // Scroll to bottom of messages
      if (messagesEndRef.current) {
        messagesEndRef.current.scrollToEnd({ animated: true });
      }
    });
  };

  // Send a message
  const handleSendMessage = async () => {
    try {
      setLoading(true);

      // If we're in a conversation, use the existing conversation
      const recipientId = selectedConversation
        ? selectedConversation.participants.find(p => p !== user.uid)
        : (selectedUser ? selectedUser.id : formData.recipientId);

      if (!recipientId) {
        setSnackbarMessage(translate('communication.noRecipient') || 'Please select a recipient');
        setSnackbarVisible(true);
        setLoading(false);
        return;
      }

      if (!formData.content.trim()) {
        setSnackbarMessage(translate('communication.emptyMessage') || 'Message cannot be empty');
        setSnackbarVisible(true);
        setLoading(false);
        return;
      }

      const messageData = {
        senderId: user.uid,
        recipientId: recipientId,
        subject: formData.subject || (selectedConversation ? 'Re: ' + selectedConversation.subject : 'No Subject'),
        content: formData.content,
        attachments: formData.attachments || []
      };

      await EnhancedMessageService.sendMessage(messageData);

      // Reset form if it was a new message
      if (!selectedConversation) {
        setFormData({
          recipientId: '',
          subject: '',
          content: '',
          attachments: []
        });
        setNewMessageVisible(false);
      } else {
        // Just clear the content if replying in a conversation
        setFormData(prev => ({
          ...prev,
          content: ''
        }));
      }

      // Show success message
      setSnackbarMessage(translate('communication.messageSent') || 'Message sent successfully');
      setSnackbarVisible(true);

      // Refresh conversations
      fetchConversations();

      // Refresh messages if in a conversation
      if (selectedConversation) {
        fetchMessages(selectedConversation.id);
      }
    } catch (error) {
      console.error('Error sending message:', error);
      setSnackbarMessage(translate('communication.errorSending') || 'Error sending message');
      setSnackbarVisible(true);
    } finally {
      setLoading(false);
    }
  };

  // Search conversations and messages
  const handleSearch = async () => {
    if (!searchQuery.trim()) {
      // If search query is empty, fetch all conversations
      fetchConversations();
      return;
    }

    try {
      setLoading(true);
      const results = await EnhancedMessageService.searchMessages(user.uid, searchQuery);
      setConversations(results);

      // Show results count
      const resultMessage = results.length > 0
        ? `${results.length} ${translate('communication.resultsFound') || 'results found'}`
        : translate('communication.noResults') || 'No results found';

      setSnackbarMessage(resultMessage);
      setSnackbarVisible(true);
    } catch (error) {
      console.error('Error searching messages:', error);
      setSnackbarMessage(translate('communication.searchError') || 'Error searching messages');
      setSnackbarVisible(true);
    } finally {
      setLoading(false);
    }
  };

  // Open user selection modal
  const openUserSelection = () => {
    setUserSelectVisible(true);
    setUserSearchQuery('');
    fetchUsersByRole('all');
  };

  // Filter users by role
  const filterUsersByRole = (role) => {
    setSelectedUserRole(role);
    fetchUsersByRole(role);
  };

  // Handle search query change for users
  const handleUserSearchChange = (query) => {
    setUserSearchQuery(query);
    searchUsers(query, selectedUserRole);
  };

  // Start a new conversation with a user
  const startNewConversation = (user) => {
    setSelectedUser(user);
    setUserSelectVisible(false);
    setNewMessageVisible(true);
    setFormData({
      recipientId: user.id,
      subject: '',
      content: '',
      attachments: []
    });
  };

  // Find existing conversation with a user
  const findExistingConversation = (userId) => {
    return conversations.find(conv =>
      conv.participants.includes(userId) && conv.participants.includes(user.uid)
    );
  };

  // Start or continue conversation with a user
  const startOrContinueConversation = (selectedUser) => {
    // Check if there's an existing conversation
    const existingConversation = findExistingConversation(selectedUser.id);

    if (existingConversation) {
      // Continue existing conversation
      setSelectedConversation(existingConversation);
      setNewMessageVisible(false);
    } else {
      // Start new conversation
      startNewConversation(selectedUser);
    }
  };

  // Update user online status
  const updateOnlineStatus = async (isOnline) => {
    try {
      await EnhancedMessageService.updateUserStatus(user.uid, isOnline);
    } catch (error) {
      console.error('Error updating online status:', error);
    }
  };

  // Toggle conversation list visibility
  const toggleConversationList = () => {
    setConversationListVisible(prev => !prev);
  };

  // Handle back button press - return to conversation list
  const handleBackToList = () => {
    setSelectedConversation(null);
    setChatInterfaceVisible(false);
  };

  // Handle conversation selection
  const handleConversationSelect = (conversation) => {
    setSelectedConversation(conversation);
    // Show chat interface when a conversation is selected
    setChatInterfaceVisible(true);
  };

  // Render a conversation card
  const renderConversation = (conversation) => {
    // Get the other participant in the conversation
    const otherParticipant = conversation.otherParticipant || {};
    const displayName = otherParticipant.displayName || 'Unknown User';
    const lastMessage = conversation.lastMessage || '';
    const lastMessageTime = conversation.lastMessageAt ? new Date(conversation.lastMessageAt.seconds * 1000) : new Date();
    const isSelected = selectedConversation && selectedConversation.id === conversation.id;
    const unreadCount = conversation.unreadCount?.[user.uid] || 0;
    const isOnline = onlineUsers[otherParticipant.id] || false;

    // Get user initials for avatar
    const initials = getInitials(displayName);
    
    // Get appropriate color based on user role
    const roleColor = getRoleColor(otherParticipant.role || 'unknown');

    return (
        <TouchableOpacity
          onPress={() => handleConversationSelect(conversation)}
        style={{ width: '100%' }}
      >
        <Animatable.View 
          animation="fadeIn" 
          duration={300} 
          delay={100}
        >
          <Card
            style={[
              styles.conversationCard,
              isSelected && styles.selectedCard
            ]}
            elevation={isSelected ? 4 : 2}
          >
            <Card.Content>
              <View style={styles.conversationHeader}>
                <View style={styles.avatarContainer}>
                  {otherParticipant.avatarUrl ? (
                    <Avatar.Image 
                      size={50} 
                      source={{ uri: otherParticipant.avatarUrl }} 
                    />
                  ) : (
                  <Avatar.Text
                      size={50} 
                      label={initials} 
                      style={{ backgroundColor: roleColor }}
                  />
                  )}
                  {isOnline && (
                    <View style={styles.onlineIndicator} />
                  )}
                </View>

                <View style={styles.conversationInfo}>
                  <View style={styles.conversationNameRow}>
                    <Text 
                      style={styles.conversationName} 
                      numberOfLines={1}
                    >
                      {displayName}
                    </Text>
                    {otherParticipant.role && (
                      <Chip 
                        compact 
                        mode="outlined" 
                        style={[styles.roleChip, { borderColor: roleColor }]}
                      >
                        <Text style={{ color: roleColor, fontSize: 10 }}>
                          {translate(`admin.roles.${otherParticipant.role.toLowerCase()}`) || otherParticipant.role}
                    </Text>
                      </Chip>
                  )}
                  </View>

                    <Text
                    style={styles.lastMessage} 
                      numberOfLines={1}
                    >
                    {lastMessage || translate('communication.noMessages') || 'No messages yet'}
                  </Text>
                  
                  <View style={styles.messageMetaRow}>
                    <Text style={styles.messageTime}>
                      {formatMessageTime(lastMessageTime)}
                    </Text>

                    {unreadCount > 0 && (
                      <Badge style={styles.unreadBadge}>
                        {unreadCount}
                      </Badge>
                    )}
                  </View>
                </View>
              </View>
            </Card.Content>
          </Card>
      </Animatable.View>
      </TouchableOpacity>
    );
  };

  // Helper function to get user initials
  const getInitials = (name) => {
    if (!name || name === 'Unknown User') return 'UN';
    return name
      .split(' ')
      .map((n) => n[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  // Helper function to get role color
  const getRoleColor = (role) => {
    switch (role?.toLowerCase()) {
      case 'admin':
        return '#F44336'; // Red
      case 'teacher':
        return '#2196F3'; // Blue
      case 'student':
        return '#4CAF50'; // Green
      case 'parent':
        return '#FF9800'; // Orange
      default:
        return '#9E9E9E'; // Grey
    }
  };

  // Helper function to format message time
  const formatMessageTime = (date) => {
    if (!date) return '';
    
    const now = new Date();
    const diffMs = now - date;
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) {
      // Today - show time only
      return date.toLocaleTimeString(language === 'am' ? 'am-ET' : 'en-US', {
        hour: '2-digit',
        minute: '2-digit'
      });
    } else if (diffDays === 1) {
      // Yesterday
      return translate('common.yesterday') || 'Yesterday';
    } else if (diffDays < 7) {
      // Within a week - show day name
      return date.toLocaleDateString(language === 'am' ? 'am-ET' : 'en-US', {
        weekday: 'short'
      });
    } else {
      // Older - show date
      return date.toLocaleDateString(language === 'am' ? 'am-ET' : 'en-US', {
        month: 'short',
        day: 'numeric'
      });
    }
  };

  return (
    <PaperProvider theme={DefaultTheme}>
      <StatusBar style="auto" />
      <SafeAreaView style={styles.safeArea}>
        {/* Role-specific Sidebar */}
        {customSidebar ? (
          customSidebar
        ) : (
          <>
            {userRole === 'admin' && (
              <AdminSidebar
                drawerAnim={drawerAnim}
                activeSidebarItem={activeSidebarItem}
                setActiveSidebarItem={setActiveSidebarItem}
                toggleDrawer={toggleDrawer}
              />
            )}
            {userRole === 'teacher' && (
              <TeacherSidebar
                drawerAnim={drawerAnim}
                activeSidebarItem={activeSidebarItem}
                setActiveSidebarItem={setActiveSidebarItem}
                toggleDrawer={toggleDrawer}
              />
            )}
            {userRole === 'student' && (
              <StudentSidebar
                drawerAnim={drawerAnim}
                activeSidebarItem={activeSidebarItem}
                setActiveSidebarItem={setActiveSidebarItem}
                toggleDrawer={toggleDrawer}
              />
            )}
            {userRole === 'parent' && (
              <ParentSidebar
                drawerAnim={drawerAnim}
                activeSidebarItem={activeSidebarItem}
                setActiveSidebarItem={setActiveSidebarItem}
                toggleDrawer={toggleDrawer}
              />
            )}
          </>
        )}

        {/* Backdrop */}
        <SidebarBackdrop
          visible={drawerOpen}
          onPress={toggleDrawer}
          fadeAnim={backdropFadeAnim}
        />

        {/* Role-specific App Header */}
        {customHeader ? (
          customHeader
        ) : (
          <>
            {userRole === 'admin' && (
              <AdminAppHeader
                title={translate('communication.adminTitle') || 'Admin Communication Center'}
                onMenuPress={toggleDrawer}
              />
            )}
            {userRole === 'teacher' && (
              <TeacherAppHeader
                title={translate('communication.teacherTitle') || 'Teacher Communication Center'}
                onMenuPress={toggleDrawer}
              />
            )}
            {userRole === 'student' && (
              <StudentAppHeader
                title={translate('communication.studentTitle') || 'Student Communication Center'}
                onMenuPress={toggleDrawer}
              />
            )}
            {userRole === 'parent' && (
              <ParentAppHeader
                title={translate('communication.parentTitle') || 'Parent Communication Center'}
                onMenuPress={toggleDrawer}
              />
            )}
          </>
        )}

        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          style={{ flex: 1, width: '100%' }}
          keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0}
        >
          {!chatInterfaceVisible ? (
            // Full screen conversation list when no conversation is selected
            <View style={[styles.container, { flexWrap: 'nowrap', alignItems: 'stretch' }]}>
              <Animatable.View animation="fadeInLeft" duration={500} style={styles.sidebar}>
              <LinearGradient
                colors={['#2196F3', '#1976D2']}
                style={styles.sidebarHeader}
              >
                <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', width: '100%' }}>
                  <Text style={styles.sidebarHeaderTitle}>
                    {translate('communication.chats') || "Chats"}
                  </Text>
                  <IconButton
                    icon="magnify"
                    size={20}
                    color="white"
                    onPress={() => {}}
                    style={{ margin: 0, padding: 0 }}
                  />
                </View>
              </LinearGradient>

              <View style={{ padding: 12, backgroundColor: '#f0f0f0' }}>
                <Searchbar
                  placeholder={translate('communication.searchMessages') || "Search messages..."}
                  onChangeText={setSearchQuery}
                  value={searchQuery}
                  onSubmitEditing={handleSearch}
                  style={[styles.searchbar, { height: 50, borderRadius: 25 }]}
                  icon={isRTL ? 'magnify' : 'magnify'}
                  iconColor={theme.colors.primary}
                  inputStyle={{ fontSize: 16 }}
                />
                <View style={{ flexDirection: 'row', justifyContent: 'flex-end', marginTop: 8 }}>
                  <Button
                    mode="text"
                    icon="account-search"
                    onPress={openUserSelection}
                    style={{ marginRight: 8 }}
                  >
                    {translate('communication.findUsers') || "Find Users"}
                  </Button>
                </View>
              </View>

              {loading && conversations.length === 0 ? (
                <View style={styles.emptyState}>
                  <ActivityIndicator size="large" color={theme.colors.primary} />
                  <Text style={styles.emptyStateText}>
                    {translate('communication.loading') || "Loading conversations..."}
                  </Text>
                </View>
              ) : conversations.length === 0 ? (
                <View style={styles.emptyState}>
                  <IconButton
                    icon="chat-outline"
                    size={64}
                    color={theme.colors.disabled}
                  />
                  <Text style={styles.emptyStateText}>
                    {translate('communication.noConversations') || "No conversations yet"}
                  </Text>
                  <CustomButton
                    mode="contained"
                    onPress={openUserSelection}
                    style={{ marginTop: 16 }}
                    icon="account-plus"
                  >
                    {translate('communication.startNew') || "Start New Chat"}
                  </CustomButton>
                </View>
              ) : (
                <FlatList
                  data={conversations}
                  renderItem={({ item }) => renderConversation(item)}
                  keyExtractor={(item) => item.id}
                  contentContainerStyle={{ paddingBottom: 80 }}
                  initialNumToRender={10}
                  maxToRenderPerBatch={20}
                  windowSize={10}
                  removeClippedSubviews={true}
                  showsVerticalScrollIndicator={false}
                />
              )}

              <FAB
                style={styles.fab}
                icon="plus"
                color="white"
                size="small"
                onPress={openUserSelection}
                theme={{ colors: { accent: theme.colors.primary } }}
              />
            </Animatable.View>

            {/* Message Area */}
            <Animatable.View
              animation="fadeInRight"
              duration={500}
              style={[
                styles.messageArea,
                { flex: 1, width: conversationListVisible ? '80%' : '100%' }
              ]}>
              {selectedConversation ? (
                <>
                  {/* Message Header with User Info */}
                  <LinearGradient
                    colors={['#2196F3', '#1976D2']}
                    style={styles.messageHeader}
                  >
                    <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                      <TouchableOpacity
                        style={{ padding: 8 }}
                        onPress={handleBackToList}
                      >
                        <IconButton
                          icon="arrow-left"
                          size={24}
                          color="white"
                          style={{ margin: 0 }}
                        />
                      </TouchableOpacity>

                      <TouchableOpacity
                        style={{ padding: 8 }}
                        onPress={toggleConversationList}
                      >
                        <IconButton
                          icon={conversationListVisible ? "menu-open" : "menu"}
                          size={24}
                          color="white"
                          style={{ margin: 0 }}
                        />
                      </TouchableOpacity>
                    </View>

                    <View style={{ flexDirection: 'row', alignItems: 'center', flex: 1 }}>
                      <Avatar.Text
                        size={40}
                        label={(() => {
                          const otherParticipantId = selectedConversation.participants.find(p => p !== user.uid);
                          const otherParticipant = selectedConversation.otherParticipant || {};
                          const displayName = otherParticipant.displayName || otherParticipantId.substring(0, 8);
                          const parts = displayName.split(' ');
                          if (parts.length === 1) return displayName.substring(0, 2).toUpperCase();
                          return (parts[0].charAt(0) + parts[1].charAt(0)).toUpperCase();
                        })()}
                        color="white"
                        style={{ backgroundColor: 'rgba(255,255,255,0.2)' }}
                      />
                      <View style={styles.messageHeaderInfo}>
                        <Text style={[styles.messageHeaderName, { color: 'white' }]}>
                          {(() => {
                            const otherParticipantId = selectedConversation.participants.find(p => p !== user.uid);
                            const otherParticipant = selectedConversation.otherParticipant || {};
                            return otherParticipant.displayName || otherParticipantId.substring(0, 8);
                          })()}
                        </Text>
                        <Text style={[styles.messageHeaderStatus, { color: 'rgba(255,255,255,0.8)' }]}>
                          {(() => {
                            const otherParticipantId = selectedConversation.participants.find(p => p !== user.uid);
                            const isOnline = onlineUsers[otherParticipantId]?.isOnline || false;
                            const lastSeen = onlineUsers[otherParticipantId]?.lastSeen;

                            if (isOnline) return translate('status.online') || 'Online';
                            if (lastSeen) {
                              const lastSeenDate = lastSeen.toDate ? lastSeen.toDate() : new Date(lastSeen);
                              const now = new Date();
                              const diffMs = now - lastSeenDate;
                              const diffMins = Math.floor(diffMs / (1000 * 60));

                              if (diffMins < 1) return translate('status.justNow') || 'Just now';
                              if (diffMins < 60) return `${translate('status.lastSeen') || 'Last seen'} ${diffMins} ${translate('time.minutesAgo') || 'min ago'}`;

                              const diffHours = Math.floor(diffMins / 60);
                              if (diffHours < 24) return `${translate('status.lastSeen') || 'Last seen'} ${diffHours} ${translate('time.hoursAgo') || 'h ago'}`;

                              return `${translate('status.lastSeen') || 'Last seen'} ${lastSeenDate.toLocaleDateString()}`;
                            }

                            return translate('status.offline') || 'Offline';
                          })()}
                        </Text>
                      </View>
                    </View>

                  </LinearGradient>

                  {/* Messages List */}
                  <FlatList
                    ref={messagesEndRef}
                    style={styles.messageList}
                    contentContainerStyle={styles.messageListContent}
                    data={messages}
                    keyExtractor={(item) => item.id}
                    initialNumToRender={15}
                    maxToRenderPerBatch={20}
                    windowSize={10}
                    removeClippedSubviews={true}
                    showsVerticalScrollIndicator={true}
                    renderItem={({ item: message }) => {
                      const isSent = message.senderId === user.uid;
                      const messageTime = message.createdAt ?
                        (message.createdAt.toDate ? message.createdAt.toDate() : new Date(message.createdAt)) :
                        new Date();

                      return (
                        <Animatable.View
                          animation={isSent ? "fadeInRight" : "fadeInLeft"}
                          duration={300}
                          delay={100}
                          style={[
                            styles.messageContainer,
                            isSent ? styles.sentMessage : styles.receivedMessage
                          ]}
                        >
                          <Card style={[
                            styles.messageCard,
                            isSent ?
                              { backgroundColor: '#e3f2fd', borderBottomRightRadius: 0, borderBottomLeftRadius: 18 } :
                              { backgroundColor: '#ffffff', borderBottomLeftRadius: 0, borderBottomRightRadius: 18 }
                          ]}>
                            <Card.Content>
                              {message.subject && (
                                <Text style={styles.messageSubject}>{message.subject}</Text>
                              )}
                              <Text style={styles.messageContent}>{message.content}</Text>
                              {message.attachments?.length > 0 && (
                                <View style={styles.attachments}>
                                  {message.attachments.map((attachment, index) => (
                                    <Chip
                                      key={index}
                                      icon="attachment"
                                      style={{ margin: 4 }}
                                      textStyle={{ color: theme.colors.primary }}
                                    >
                                      {attachment.name}
                                    </Chip>
                                  ))}
                                </View>
                              )}
                              <Text style={styles.messageTime}>
                                {messageTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                              </Text>
                              {isSent && (
                                <View style={styles.messageStatus}>
                                  <IconButton
                                    icon={message.readBy?.length > 1 ? "check-all" : "check"}
                                    size={12}
                                    color={message.readBy?.length > 1 ? '#4CAF50' : '#757575'}
                                    style={{ margin: 0, padding: 0 }}
                                  />
                                </View>
                              )}
                            </Card.Content>
                          </Card>
                        </Animatable.View>
                      );
                    }}
                    ListEmptyComponent={
                      <View style={styles.emptyState}>
                        <Text style={styles.emptyStateText}>
                          {translate('communication.noMessages') || "No messages yet"}
                        </Text>
                      </View>
                    }
                    onContentSizeChange={() => messagesEndRef.current?.scrollToEnd({ animated: true })}
                    onLayout={() => messagesEndRef.current?.scrollToEnd({ animated: true })}
                  />

                  {/* Message Input */}
                  <View style={styles.messageInput}>
                    <View style={styles.messageInputContainer}>
                      <IconButton
                        icon="emoticon-outline"
                        size={24}
                        color="#757575"
                        onPress={() => {}}
                        style={styles.inputIcon}
                      />
                      <CustomInput
                        placeholder={translate('communication.typeMessage') || "Type a message..."}
                        value={formData.content}
                        onChangeText={(text) => setFormData(prev => ({ ...prev, content: text }))}
                        multiline
                        style={styles.textInput}
                        maxLength={1000}
                      />
                      <IconButton
                        icon="paperclip"
                        size={24}
                        color="#757575"
                        onPress={() => {}}
                        style={styles.inputIcon}
                      />
                      <IconButton
                        icon="camera"
                        size={24}
                        color="#757575"
                        onPress={() => {}}
                        style={styles.inputIcon}
                      />
                    </View>
                    <View style={styles.sendButtonContainer}>
                      {formData.content.trim() ? (
                        <IconButton
                          icon="send"
                          size={28}
                          color="white"
                          style={styles.sendButton}
                          onPress={handleSendMessage}
                        />
                      ) : (
                        <IconButton
                          icon="microphone"
                          size={28}
                          color="white"
                          style={styles.sendButton}
                          onPress={() => {}}
                        />
                      )}
                    </View>
                  </View>
                </>
              ) : (
                <View style={styles.noSelection}>
                  {!conversationListVisible && (
                    <TouchableOpacity
                      style={{ position: 'absolute', top: 16, left: 16 }}
                      onPress={toggleConversationList}
                    >
                      <IconButton
                        icon="menu"
                        size={28}
                        color={theme.colors.primary}
                      />
                    </TouchableOpacity>
                  )}
                  <Animatable.View animation="fadeIn" duration={1000}>
                    <IconButton
                      icon="chat-outline"
                      size={64}
                      color={theme.colors.disabled}
                    />
                    <Text style={styles.emptyStateText}>
                      {translate('communication.selectConversation') || "Select a conversation to start messaging"}
                    </Text>
                    <CustomButton
                      mode="contained"
                      onPress={openUserSelection}
                      style={{ marginTop: 16 }}
                      icon="account-plus"
                    >
                      {translate('communication.startNew') || "Start New Chat"}
                    </CustomButton>
                  </Animatable.View>
                </View>
              )}
            </Animatable.View>
            </View>
          ) : (
            // Chat interface view when a conversation is selected
            <View style={[styles.container, { flexWrap: 'nowrap', alignItems: 'stretch' }]}>
              {/* Message Area - Full Screen */}
              <Animatable.View
                animation="fadeInRight"
                duration={500}
                style={[
                  styles.messageArea,
                  { flex: 1, width: '100%' }
                ]}>
                {selectedConversation ? (
                  <>
                    {/* Message Header with User Info */}
                    <LinearGradient
                      colors={['#2196F3', '#1976D2']}
                      style={styles.messageHeader}
                    >
                      <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                        <TouchableOpacity
                          style={{ padding: 8 }}
                          onPress={handleBackToList}
                        >
                          <IconButton
                            icon="arrow-left"
                            size={24}
                            color="white"
                            style={{ margin: 0 }}
                          />
                        </TouchableOpacity>
                      </View>

                      <View style={{ flexDirection: 'row', alignItems: 'center', flex: 1 }}>
                        <Avatar.Text
                          size={40}
                          label={(() => {
                            const otherParticipantId = selectedConversation.participants.find(p => p !== user.uid);
                            const otherParticipant = selectedConversation.otherParticipant || {};
                            const displayName = otherParticipant.displayName || otherParticipantId.substring(0, 8);
                            const parts = displayName.split(' ');
                            if (parts.length === 1) return displayName.substring(0, 2).toUpperCase();
                            return (parts[0].charAt(0) + parts[1].charAt(0)).toUpperCase();
                          })()}
                          color="white"
                          style={{ backgroundColor: 'rgba(255,255,255,0.2)' }}
                        />
                        <View style={styles.messageHeaderInfo}>
                          <Text style={[styles.messageHeaderName, { color: 'white' }]}>
                            {(() => {
                              const otherParticipantId = selectedConversation.participants.find(p => p !== user.uid);
                              const otherParticipant = selectedConversation.otherParticipant || {};
                              return otherParticipant.displayName || otherParticipantId.substring(0, 8);
                            })()}
                          </Text>
                          <Text style={[styles.messageHeaderStatus, { color: 'rgba(255,255,255,0.8)' }]}>
                            {(() => {
                              const otherParticipantId = selectedConversation.participants.find(p => p !== user.uid);
                              const isOnline = onlineUsers[otherParticipantId]?.isOnline || false;
                              const lastSeen = onlineUsers[otherParticipantId]?.lastSeen;

                              if (isOnline) return translate('status.online') || 'Online';
                              if (lastSeen) {
                                const lastSeenDate = lastSeen.toDate ? lastSeen.toDate() : new Date(lastSeen);
                                const now = new Date();
                                const diffMs = now - lastSeenDate;
                                const diffMins = Math.floor(diffMs / (1000 * 60));

                                if (diffMins < 1) return translate('status.justNow') || 'Just now';
                                if (diffMins < 60) return `${translate('status.lastSeen') || 'Last seen'} ${diffMins} ${translate('time.minutesAgo') || 'min ago'}`;

                                const diffHours = Math.floor(diffMins / 60);
                                if (diffHours < 24) return `${translate('status.lastSeen') || 'Last seen'} ${diffHours} ${translate('time.hoursAgo') || 'h ago'}`;

                                return `${translate('status.lastSeen') || 'Last seen'} ${lastSeenDate.toLocaleDateString()}`;
                              }

                              return translate('status.offline') || 'Offline';
                            })()}
                          </Text>
                        </View>
                      </View>

                    </LinearGradient>

                    {/* Messages List */}
                    <FlatList
                      ref={messagesEndRef}
                      style={styles.messageList}
                      contentContainerStyle={styles.messageListContent}
                      data={messages}
                      keyExtractor={(item) => item.id}
                      initialNumToRender={15}
                      maxToRenderPerBatch={20}
                      windowSize={10}
                      removeClippedSubviews={true}
                      showsVerticalScrollIndicator={true}
                      renderItem={({ item: message }) => {
                        const isSent = message.senderId === user.uid;
                        const messageTime = message.createdAt ?
                          (message.createdAt.toDate ? message.createdAt.toDate() : new Date(message.createdAt)) :
                          new Date();

                        return (
                          <Animatable.View
                            animation={isSent ? "fadeInRight" : "fadeInLeft"}
                            duration={300}
                            delay={100}
                            style={[
                              styles.messageContainer,
                              isSent ? styles.sentMessage : styles.receivedMessage
                            ]}
                          >
                            <Card style={[
                              styles.messageCard,
                              isSent ?
                                { backgroundColor: '#e3f2fd', borderBottomRightRadius: 0, borderBottomLeftRadius: 18 } :
                                { backgroundColor: '#ffffff', borderBottomLeftRadius: 0, borderBottomRightRadius: 18 }
                            ]}>
                              <Card.Content>
                                {message.subject && (
                                  <Text style={styles.messageSubject}>{message.subject}</Text>
                                )}
                                <Text style={styles.messageContent}>{message.content}</Text>
                                {message.attachments?.length > 0 && (
                                  <View style={styles.attachments}>
                                    {message.attachments.map((attachment, index) => (
                                      <Chip
                                        key={index}
                                        icon="attachment"
                                        style={{ margin: 4 }}
                                        textStyle={{ color: theme.colors.primary }}
                                      >
                                        {attachment.name}
                                      </Chip>
                                    ))}
                                  </View>
                                )}
                                <Text style={styles.messageTime}>
                                  {messageTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                                </Text>
                                {isSent && (
                                  <View style={styles.messageStatus}>
                                    <IconButton
                                      icon={message.readBy?.length > 1 ? "check-all" : "check"}
                                      size={12}
                                      color={message.readBy?.length > 1 ? '#4CAF50' : '#757575'}
                                      style={{ margin: 0, padding: 0 }}
                                    />
                                  </View>
                                )}
                              </Card.Content>
                            </Card>
                          </Animatable.View>
                        );
                      }}
                      ListEmptyComponent={
                        <View style={styles.emptyState}>
                          <Text style={styles.emptyStateText}>
                            {translate('communication.noMessages') || "No messages yet"}
                          </Text>
                        </View>
                      }
                      onContentSizeChange={() => messagesEndRef.current?.scrollToEnd({ animated: true })}
                      onLayout={() => messagesEndRef.current?.scrollToEnd({ animated: true })}
                    />

                    {/* Message Input */}
                    <View style={styles.messageInput}>
                      <View style={styles.messageInputContainer}>
                        <IconButton
                          icon="emoticon-outline"
                          size={24}
                          color="#757575"
                          onPress={() => {}}
                          style={styles.inputIcon}
                        />
                        <CustomInput
                          placeholder={translate('communication.typeMessage') || "Type a message..."}
                          value={formData.content}
                          onChangeText={(text) => setFormData(prev => ({ ...prev, content: text }))}
                          multiline
                          style={styles.textInput}
                          maxLength={1000}
                        />
                        <IconButton
                          icon="paperclip"
                          size={24}
                          color="#757575"
                          onPress={() => {}}
                          style={styles.inputIcon}
                        />
                        <IconButton
                          icon="camera"
                          size={24}
                          color="#757575"
                          onPress={() => {}}
                          style={styles.inputIcon}
                        />
                      </View>
                      <View style={styles.sendButtonContainer}>
                        {formData.content.trim() ? (
                          <IconButton
                            icon="send"
                            size={28}
                            color="white"
                            style={styles.sendButton}
                            onPress={handleSendMessage}
                          />
                        ) : (
                          <IconButton
                            icon="microphone"
                            size={28}
                            color="white"
                            style={styles.sendButton}
                            onPress={() => {}}
                          />
                        )}
                      </View>
                    </View>
                  </>
                ) : (
                  <View style={styles.noSelection}>
                    <Animatable.View animation="fadeIn" duration={1000}>
                      <IconButton
                        icon="chat-outline"
                        size={64}
                        color={theme.colors.disabled}
                      />
                      <Text style={styles.emptyStateText}>
                        {translate('communication.selectConversation') || "Select a conversation to start messaging"}
                      </Text>
                      <CustomButton
                        mode="contained"
                        onPress={openUserSelection}
                        style={{ marginTop: 16 }}
                        icon="account-plus"
                      >
                        {translate('communication.startNew') || "Start New Chat"}
                      </CustomButton>
                    </Animatable.View>
                  </View>
                )}
              </Animatable.View>
            </View>
          )}
        </KeyboardAvoidingView>

        {/* User Selection Modal */}
        <Portal>
          <Modal
            visible={userSelectVisible}
            onDismiss={() => setUserSelectVisible(false)}
            contentContainerStyle={styles.modal}
          >
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>
                {translate('communication.selectUser') || "Select User"}
              </Text>
              <IconButton
                icon="close"
                size={24}
                onPress={() => setUserSelectVisible(false)}
              />
            </View>

            <Searchbar
              placeholder={translate('communication.searchUsers') || "Search users..."}
              onChangeText={handleUserSearchChange}
              value={userSearchQuery}
              style={[styles.searchbar, { marginBottom: 16 }]}
              inputStyle={{ fontSize: 16 }}
              icon="account-search"
              iconColor={theme.colors.primary}
            />

            <ScrollView horizontal style={styles.userRoleFilter}>
              <Chip
                selected={selectedUserRole === 'all'}
                onPress={() => filterUsersByRole('all')}
                style={styles.roleChip}
              >
                {translate('roles.all') || "All"}
              </Chip>

              {/* Show Admin chip for all users */}
              <Chip
                selected={selectedUserRole === 'admin'}
                onPress={() => filterUsersByRole('admin')}
                style={styles.roleChip}
              >
                {translate('roles.admin') || "Admins"}
              </Chip>

              {/* Show Teacher chip for all users except teachers */}
              {userRole !== 'teacher' && (
                <Chip
                  selected={selectedUserRole === 'teacher'}
                  onPress={() => filterUsersByRole('teacher')}
                  style={styles.roleChip}
                >
                  {translate('roles.teacher') || "Teachers"}
                </Chip>
              )}

              {/* Show Student chip for admins and teachers */}
              {(userRole === 'admin' || userRole === 'teacher') && (
                <Chip
                  selected={selectedUserRole === 'student'}
                  onPress={() => filterUsersByRole('student')}
                  style={styles.roleChip}
                >
                  {translate('roles.student') || "Students"}
                </Chip>
              )}

              {/* Show Parent chip for admins and teachers */}
              {(userRole === 'admin' || userRole === 'teacher') && (
                <Chip
                  selected={selectedUserRole === 'parent'}
                  onPress={() => filterUsersByRole('parent')}
                  style={styles.roleChip}
                >
                  {translate('roles.parent') || "Parents"}
                </Chip>
              )}
            </ScrollView>

            {loadingUsers ? (
              <View style={{ padding: 20, alignItems: 'center' }}>
                <ActivityIndicator size="large" color={theme.colors.primary} />
              </View>
            ) : filteredUsers.length === 0 ? (
              <View style={{ padding: 20, alignItems: 'center' }}>
                <Text>{translate('communication.noUsersFound') || "No users found"}</Text>
              </View>
            ) : (
              <FlatList
                data={filteredUsers}
                keyExtractor={(item) => item.id}
                style={{ maxHeight: 400 }}
                renderItem={({ item }) => (
                  <List.Item
                    title={item.displayName || item.email || item.id.substring(0, 8)}
                    description={item.email || item.role}
                    left={props => (
                      <Avatar.Text
                        {...props}
                        size={40}
                        label={(() => {
                          const name = item.displayName || item.email || item.id.substring(0, 8);
                          const parts = name.split(' ');
                          if (parts.length === 1) return name.substring(0, 2).toUpperCase();
                          return (parts[0].charAt(0) + parts[1].charAt(0)).toUpperCase();
                        })()}
                        style={{ backgroundColor: (() => {
                          switch(item.role) {
                            case 'admin': return '#5c6bc0';
                            case 'teacher': return '#26a69a';
                            case 'student': return '#ef5350';
                            case 'parent': return '#ffa726';
                            default: return '#757575';
                          }
                        })() }}
                      />
                    )}
                    onPress={() => startOrContinueConversation(item)}
                    style={styles.userItem}
                  />
                )}
              />
            )}
          </Modal>
        </Portal>

        {/* New Message Modal */}
        <Portal>
          <Modal
            visible={newMessageVisible}
            onDismiss={() => setNewMessageVisible(false)}
            contentContainerStyle={styles.modal}
          >
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>
                {translate('communication.newMessage') || "New Message"}
              </Text>
              <IconButton
                icon="close"
                size={24}
                onPress={() => setNewMessageVisible(false)}
              />
            </View>

            <View style={{ marginBottom: 16, flexDirection: 'row', alignItems: 'center' }}>
              <Text style={{ fontWeight: 'bold', marginRight: 8 }}>
                {translate('communication.to') || "To:"}
              </Text>
              <Text>{selectedUser?.displayName || selectedUser?.email || formData.recipientId}</Text>
            </View>

            <CustomInput
              label={translate('communication.subject') || "Subject (optional)"}
              value={formData.subject}
              onChangeText={(text) => setFormData(prev => ({ ...prev, subject: text }))}
              style={{ marginBottom: 12 }}
            />

            <CustomInput
              label={translate('communication.message') || "Message"}
              value={formData.content}
              onChangeText={(text) => setFormData(prev => ({ ...prev, content: text }))}
              multiline
              numberOfLines={6}
              style={{ marginBottom: 16 }}
            />

            <View style={styles.modalActions}>
              <CustomButton
                mode="outlined"
                onPress={() => setNewMessageVisible(false)}
                style={{ marginRight: 8 }}
              >
                {translate('actions.cancel') || "Cancel"}
              </CustomButton>
              <CustomButton
                mode="contained"
                onPress={handleSendMessage}
                loading={loading}
                disabled={!formData.content.trim()}
              >
                {translate('actions.send') || "Send"}
              </CustomButton>
            </View>
          </Modal>
        </Portal>

        {loading && (
          <View style={styles.loadingOverlay}>
            <ActivityIndicator size="large" color={theme.colors.primary} />
          </View>
        )}

        <Snackbar
          visible={snackbarVisible}
          onDismiss={() => setSnackbarVisible(false)}
          duration={3000}
          style={{ backgroundColor: theme.colors.surface }}
        >
          <Text style={{ color: theme.colors.text }}>{snackbarMessage}</Text>
        </Snackbar>
      </SafeAreaView>
    </PaperProvider>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  container: {
    flex: 1,
    flexDirection: 'row',
    backgroundColor: '#f5f5f5',
    padding: 8,
    paddingTop: 0,
    width: '100%',
    justifyContent: 'space-between',
  },
  sidebar: {
    width: '100%',
    backgroundColor: '#ffffff',
    borderRadius: 8,
    overflow: 'hidden',
    elevation: 4,
  },
  messageArea: {
    flex: 1,
    width: '100%',
    backgroundColor: '#ffffff',
    borderRadius: 8,
    overflow: 'hidden',
    elevation: 4,
    display: 'flex',
    flexDirection: 'column',
  },
  searchbar: {
    margin: 0,
    elevation: 2,
    borderRadius: 25,
    backgroundColor: '#ffffff',
    height: 50,
    fontSize: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.5,
  },
  conversationCard: {
    margin: 1,
    marginBottom: 1,
    elevation: 0,
    borderRadius: 0,
    borderLeftWidth: 2,
    borderLeftColor: '#e0e0e0',
    paddingVertical: 0,
  },
  selectedCard: {
    backgroundColor: '#e3f2fd',
    borderLeftColor: '#2196F3',
  },
  unreadCard: {
    borderLeftColor: '#4CAF50',
  },
  conversationHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 4,
  },
  avatarContainer: {
    position: 'relative',
    marginRight: 4,
  },
  onlineIndicator: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#4CAF50',
    borderWidth: 1,
    borderColor: '#ffffff',
  },
  conversationInfo: {
    flex: 1,
    marginLeft: 2,
  },
  conversationNameRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 2,
  },
  conversationName: {
    fontSize: 12,
    fontWeight: 'bold',
    flex: 1,
  },
  lastMessage: {
    fontSize: 10,
    color: '#757575',
    flex: 1,
  },
  messageMetaRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  messageTime: {
    fontSize: 10,
    color: '#757575',
    marginRight: 4,
  },
  unreadBadge: {
    backgroundColor: '#4CAF50',
  },
  messageHeader: {
    paddingVertical: 1,
    paddingHorizontal: 4,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    elevation: 4,
  },
  messageHeaderInfo: {
    flex: 1,
    flexDirection: 'column',
    marginLeft: 12,
  },
  messageHeaderName: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  messageHeaderStatus: {
    fontSize: 12,
    color: '#757575',
  },
  messageList: {
    flex: 1,
    width: '100%',
    backgroundColor: '#f5f5f5',
  },
  messageListContent: {
    padding: 16,
    paddingBottom: 24,
    width: '100%',
  },
  messageContainer: {
    maxWidth: '75%',
    marginVertical: 4,
  },
  sentMessage: {
    alignSelf: 'flex-end',
    marginLeft: 40,
  },
  receivedMessage: {
    alignSelf: 'flex-start',
    marginRight: 40,
  },
  messageCard: {
    elevation: 1,
    borderRadius: 18,
    borderBottomLeftRadius: 0,
    overflow: 'hidden',
  },
  messageSubject: {
    fontWeight: 'bold',
    marginBottom: 4,
    fontSize: 16,
  },
  messageContent: {
    fontSize: 15,
    lineHeight: 20,
    marginBottom: 4,
  },
  messageInput: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
    backgroundColor: '#f9f9f9',
  },
  messageInputContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#ffffff',
    borderRadius: 24,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  textInput: {
    flex: 1,
    maxHeight: 100,
    paddingHorizontal: 8,
    fontSize: 16,
  },
  inputIcon: {
    margin: 0,
  },
  sendButtonContainer: {
    marginLeft: 8,
  },
  sendButton: {
    backgroundColor: '#2196F3',
    borderRadius: 50,
    margin: 0,
  },
  noSelection: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f9f9f9',
  },
  modal: {
    backgroundColor: '#ffffff',
    padding: 24,
    margin: 20,
    borderRadius: 12,
    elevation: 5,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 16,
  },
  fab: {
    position: 'absolute',
    right: 8,
    bottom: 8,
    elevation: 4,
    width: 40,
    height: 40,
  },
  loadingOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(255, 255, 255, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  fullScreenList: {
    flex: 1,
    width: '100%',
    backgroundColor: '#ffffff',
    borderRadius: 8,
    overflow: 'hidden',
    elevation: 4,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginVertical: 12,
    marginHorizontal: 16,
  },
  sidebarHeader: {
    padding: 8,
    paddingTop: 10,
    paddingBottom: 10,
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  sidebarHeaderTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: 'white',
    marginLeft: 8,
  },
  userRoleFilter: {
    flexDirection: 'row',
    padding: 8,
    marginBottom: 8,
  },
  roleChip: {
    marginRight: 8,
  },
  userItem: {
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  emptyStateText: {
    fontSize: 16,
    color: '#757575',
    textAlign: 'center',
    marginTop: 16,
  },
});

export default CommunicationCenter;
