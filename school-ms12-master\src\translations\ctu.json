{"common": {"appName": "School Management System", "loading": "Loading...", "error": "Error", "success": "Success", "save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "view": "View", "submit": "Submit", "back": "Back", "next": "Next", "previous": "Previous", "search": "Search", "clearSearch": "Clear Search", "filter": "Filter", "sort": "Sort", "refresh": "Refresh", "noData": "No data available", "yes": "Yes", "no": "No", "confirm": "Confirm", "reject": "Reject", "approve": "Approve", "logout": "Logout", "login": "<PERSON><PERSON>", "register": "Register", "today": "Today", "yesterday": "Yesterday", "daysAgo": "{{count}} days ago", "date": "Date", "time": "Time", "status": "Status", "active": "Active", "inactive": "Inactive", "pending": "Pending", "completed": "Completed", "rejected": "Rejected", "approved": "Approved", "all": "All", "retry": "Retry", "viewAll": "View All", "comingSoon": "Coming Soon", "photo": "Photo", "name": "Name", "remarks": "Remarks", "roll": "Roll", "dismiss": "<PERSON><PERSON><PERSON>", "notes": "Notes", "unnamed": "Unnamed", "notAssigned": "Not Assigned", "addRemarks": "Add Remarks...", "selectLanguage": "Select Language", "details": "Details", "confirmLogout": "Confirm <PERSON>ut", "confirmLogoutMessage": "Are you sure you want to logout from the system?", "actions": "Actions", "create": "Create", "update": "Update", "add": "Add", "profile": "Profile", "settings": "Settings", "invalidDate": "Invalid Date", "viewDetails": "View Details", "markAllAsRead": "<PERSON> as <PERSON>", "help": "Help", "goToClass": "Go to Class", "dashboard": "Dashboard", "period": "Period", "room": "Room", "class": "Class", "in": "in", "minutes": "minutes", "minutesLeft": "minutes left", "viewClass": "View Class", "prepare": "Prepare", "ongoing": "Ongoing", "now": "Now", "email": "Email", "password": "Password", "close": "Close", "unknownSubject": "Unknown Subject", "unknownTeacher": "Unknown Teacher", "menuOpened": "Menu opened", "menuClosed": "Menu closed", "showMore": "Show More", "tapToView": "Tap to view details", "version": "Version", "section": "Section", "step": "Step", "of": "of", "or": "or", "ok": "OK", "minutesAgo": "{{count}} minutes ago", "just now": "just now", "24 minutes ago": "24 minutes ago", "9 minutes ago": "9 minutes ago", "3 days ago": "3 days ago", "1 hour ago": "1 hour ago", "notAvailable": "N/A", "offlineMode": "You are currently offline. Some features may be limited."}, "auth": {"login": {"title": "Login to your account", "subtitle": "Enter your credentials to access your account", "email": "Email", "emailPlaceholder": "Enter your email", "password": "Password", "passwordPlaceholder": "Enter your password", "forgotPassword": "Forgot Password?", "submit": "<PERSON><PERSON>", "back": "Back to Home", "noAccount": "Don't have an account?", "createAccount": "Create Account", "rememberMe": "Remember Me", "loginAs": "<PERSON><PERSON> as", "admin": "Admin", "teacher": "Teacher", "student": "Student", "parent": "Parent"}, "teacher": "Teacher", "forgotPassword": {"title": "Reset Password", "subtitle": "Enter your email to receive a password reset link", "email": "Email", "emailPlaceholder": "Enter your email", "submit": "Send Reset Link", "back": "Back to Login", "checkEmail": "Please check your email", "resetSent": "Password reset email has been sent. Please check your inbox.", "resetInstructions": "Follow the instructions in the email to reset your password.", "emailLabel": "Enter your email address", "resetButton": "Reset Password", "backToLogin": "Back to Login"}, "landing": {"title": "ASELLA SECONDARY SCHOOL", "subtitle": "Excellence in Education", "schoolName": "School Management System", "schoolNameFull": "Asella Secondary School Management System", "schoolNameShort": "ASS", "motto": "Excellence in Education", "features": {"title": "Why Choose Us", "qualityEducation": "Quality Education", "qualityEducationDesc": "We provide a comprehensive educational program that meets international standards and prepares students for future success.", "experiencedTeachers": "Experienced Teachers", "experiencedTeachersDesc": "Our team of dedicated experienced teachers is committed to nurturing each student's potential through personal attention and innovative teaching methods.", "modernFacilities": "Modern Facilities", "modernFacilitiesDesc": "Our campus includes modern classrooms, well-equipped laboratories, a comprehensive library, and modern sports facilities to enhance the learning experience.", "holisticDevelopment": "Holistic Development", "holisticDevelopmentDesc": "We focus on developing the whole child - intellectual, physical, social, and emotional - through a balanced approach to academics and extracurricular activities."}, "buttons": {"getstarted": "Get Started", "learnMore": "Learn More", "contactUs": "Contact Us", "quickLogin": "<PERSON>gin"}, "languageSelector": {"title": "Select Language", "english": "English", "amharic": "Amharic", "afaanOromo": "<PERSON><PERSON><PERSON>", "ctu": "Ctu"}, "footer": {"copyright": "© 2024 School Management System. All rights reserved.", "motto": "Excellence in Education", "rights": "All Rights Reserved", "contactInfo": "Contact Information", "address": "Asella, Ethiopia", "phone": "+251 123 456 789", "email": "<EMAIL>"}}, "errors": {"invalidEmail": "Invalid email address", "invalidPassword": "Password must be at least 6 characters", "wrongPassword": "Wrong password", "userNotFound": "User not found", "emailAlreadyInUse": "Email address is already in use", "weakPassword": "Password is too weak", "networkError": "Network error. Please check your connection", "unexpected": "An unexpected error occurred", "resetPasswordFailed": "Failed to send password reset email", "emailNotVerified": "Email not verified. Please verify your email first."}, "resetPasswordSuccess": "Password reset email has been sent. Please check your inbox.", "confirmLogout": "Confirm <PERSON>ut", "logoutConfirmMessage": "Are you sure you want to logout from the system?", "logout": "Logout", "User logged in": "User logged in", "User logged out": "User logged out", "Admin User logged into the system": "Admin User logged into the system", "Admin User logged out of the system": "Admin User logged out of the system"}, "student": {"title": "Students", "subtitle": "Manage student information", "name": "Name", "email": "Email", "class": "Class", "section": "Section", "parent": "Parent", "notAssigned": "Not Assigned", "noParent": "No Parent", "endOfList": "End of list", "loadingStudents": "Loading students...", "loadingMoreStudents": "Loading more students...", "noStudentsFound": "No students found", "addFirstStudent": "Add Your First Student", "searchPlaceholder": "Search by name, email, class, section...", "searchingFor": "Searching for", "searchResults": "Found {{count}} student(s)", "noSearchResults": "No students match your search criteria", "clearSearch": "Clear Search", "totalStudents": "Total Students", "totalClasses": "Total Classes", "errorFetchingStudents": "Error fetching students. Please try again.", "errorFetchingParents": "Failed to fetch parents", "selectParentFirst": "Please select a parent first", "parentAssigned": "Parent assigned successfully", "errorAssigningParent": "Failed to assign parent", "confirmDelete": "Confirm Delete", "deleteConfirmMessage": "Are you sure you want to delete {{name}}? This action cannot be undone.", "deleteSuccess": "Student deleted successfully", "deleteError": "Failed to delete student", "assignSection": "Assign Section", "selectClass": "Select Class", "selectSection": "Select Section", "cancel": "Cancel", "assign": "Assign", "assignParent": "Assign <PERSON>", "selectParentForStudent": "Select Parent for Student", "selectParent": "Select Parent", "addNewParent": "Add New Parent", "parentInfo": "Parent Information"}, "admin": {"dashboard": {"lastUpdated": "Last Updated"}, "studentManagement": {"addStudent": "Add New Student", "bulkImport": "Bulk Import Students"}}, "activities": {"assignSection": "Assigned Section", "assignSectionDesc": "Assigned section {{section}} to student {{name}}", "assignParent": "Assigned Parent", "assignParentDesc": "Assigned parent {{parent}} to student {{student}}", "deleteStudent": "Deleted Student", "deleteStudentDesc": "Deleted student {{student}}"}}