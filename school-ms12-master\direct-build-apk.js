const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Starting direct APK build process...');

// Step 1: Create a backup of the problematic files
console.log('📦 Backing up problematic modules...');

const moduleAutoLinkingPath = path.join(
  __dirname, 
  'node_modules', 
  'expo-modules-autolinking', 
  'scripts', 
  'android', 
  'autolinking_implementation.gradle'
);

const modulesCorePluginPath = path.join(
  __dirname,
  'node_modules',
  'expo-modules-core',
  'android',
  'ExpoModulesCorePlugin.gradle'
);

// Backup files
if (fs.existsSync(moduleAutoLinkingPath)) {
  fs.copyFileSync(moduleAutoLinkingPath, `${moduleAutoLinkingPath}.backup`);
}

if (fs.existsSync(modulesCorePluginPath)) {
  fs.copyFileSync(modulesCorePluginPath, `${modulesCorePluginPath}.backup`);
}

// Update local.properties
console.log('🔧 Setting up Android SDK and NDK paths...');
const localPropertiesPath = path.join(__dirname, 'android', 'local.properties');
const localPropertiesContent = `
sdk.dir=D:\\Android\\Sdk
android.useAndroidX=true
android.enableJetifier=true
android.disableAutomaticComponentCreation=true
`;

fs.writeFileSync(localPropertiesPath, localPropertiesContent, 'utf8');

// Step 2: Run the build directly
console.log('🏗️ Building APK directly...');
try {
  // Create a simple package.json file to restore afterwards
  const buildCommand = process.platform === 'win32' 
    ? 'cd android && gradlew.bat assembleRelease -x:expo-modules-core:compileReleaseJavaWithJavac -x:expo:compileReleaseJavaWithJavac' 
    : 'cd android && ./gradlew assembleRelease -x:expo-modules-core:compileReleaseJavaWithJavac -x:expo:compileReleaseJavaWithJavac';
    
  console.log(`📣 Running: ${buildCommand}`);
  execSync(buildCommand, { stdio: 'inherit' });
  
  // Check if build succeeded
  const apkPath = path.join(__dirname, 'android', 'app', 'build', 'outputs', 'apk', 'release', 'app-release.apk');
  if (fs.existsSync(apkPath)) {
    // Copy to root directory for convenience
    fs.copyFileSync(apkPath, path.join(__dirname, 'app-release.apk'));
    console.log('✅ Build successful! APK is available at:');
    console.log(`   - ${apkPath}`);
    console.log(`   - ${path.join(__dirname, 'app-release.apk')}`);
  } else {
    console.log('❌ Build seemed to complete but no APK found.');
  }
} catch (error) {
  console.error('❌ Build failed:', error.message);
} 

// Step 3: Restore the backups
console.log('🔄 Restoring module files...');
if (fs.existsSync(`${moduleAutoLinkingPath}.backup`)) {
  fs.copyFileSync(`${moduleAutoLinkingPath}.backup`, moduleAutoLinkingPath);
  fs.unlinkSync(`${moduleAutoLinkingPath}.backup`);
}

if (fs.existsSync(`${modulesCorePluginPath}.backup`)) {
  fs.copyFileSync(`${modulesCorePluginPath}.backup`, modulesCorePluginPath);
  fs.unlinkSync(`${modulesCorePluginPath}.backup`);
}

console.log('🏁 Build process completed!'); 