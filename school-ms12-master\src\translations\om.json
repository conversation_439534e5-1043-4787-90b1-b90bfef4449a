{"common": {"appName": "<PERSON><PERSON>", "loading": "Fe'amu...", "error": "Dogoggora", "success": "<PERSON><PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON>", "delete": "<PERSON><PERSON><PERSON>", "edit": "<PERSON><PERSON><PERSON>", "view": "<PERSON><PERSON><PERSON>", "submit": "<PERSON><PERSON><PERSON>", "back": "<PERSON><PERSON><PERSON>", "next": "<PERSON><PERSON>", "previous": "<PERSON><PERSON>", "search": "Barbaadi", "clearSearch": "<PERSON><PERSON><PERSON><PERSON>", "filter": "<PERSON><PERSON><PERSON>", "sort": "<PERSON><PERSON><PERSON><PERSON>", "refresh": "<PERSON><PERSON><PERSON><PERSON>", "noData": "<PERSON><PERSON><PERSON> hin jiru", "yes": "<PERSON><PERSON><PERSON><PERSON>", "no": "<PERSON><PERSON><PERSON>", "confirm": "<PERSON><PERSON><PERSON><PERSON>", "reject": "<PERSON><PERSON><PERSON><PERSON>", "approve": "<PERSON><PERSON><PERSON>", "logout": "<PERSON>'i", "login": "<PERSON><PERSON>", "register": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "today": "Har'a", "yesterday": "<PERSON><PERSON><PERSON>", "daysAgo": "Guyyaa {{count}} dura", "date": "<PERSON><PERSON><PERSON>", "time": "Yeroo", "status": "Ha<PERSON>", "active": "Hojjetaa", "inactive": "<PERSON><PERSON> ho<PERSON><PERSON><PERSON>", "pending": "<PERSON><PERSON><PERSON><PERSON>", "completed": "<PERSON><PERSON><PERSON>", "rejected": "Kuffiifame", "approved": "<PERSON><PERSON><PERSON>", "all": "<PERSON><PERSON>", "retry": "<PERSON><PERSON> de<PERSON>i yaali", "viewAll": "<PERSON><PERSON> il<PERSON>", "comingSoon": "<PERSON><PERSON><PERSON><PERSON>i ni dhufa", "photo": "<PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "remarks": "<PERSON><PERSON>", "roll": "Lakkoofsa G<PERSON>mee", "dismiss": "<PERSON><PERSON><PERSON>", "notes": "<PERSON><PERSON><PERSON><PERSON>", "unnamed": "Kan maqaa hin qabne", "notAssigned": "<PERSON><PERSON>", "addRemarks": "<PERSON><PERSON> da<PERSON>...", "selectLanguage": "<PERSON><PERSON><PERSON>", "details": "<PERSON><PERSON><PERSON>a", "confirmLogout": "<PERSON><PERSON> k<PERSON>a bahuu bar<PERSON>u?", "confirmLogoutMessage": "<PERSON><PERSON><PERSON><PERSON> sir<PERSON> k<PERSON>a bahuu barba<PERSON>duu?", "actions": "Tarkaanfiiwwan", "create": "<PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON><PERSON>", "add": "<PERSON><PERSON><PERSON><PERSON>", "profile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settings": "<PERSON><PERSON><PERSON><PERSON><PERSON>a", "invalidDate": "<PERSON><PERSON><PERSON>", "viewDetails": "<PERSON><PERSON><PERSON><PERSON>", "markAllAsRead": "Hunda A<PERSON>", "help": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "goToClass": "<PERSON><PERSON>", "dashboard": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "period": "Yeroo", "room": "<PERSON><PERSON><PERSON>", "class": "<PERSON><PERSON><PERSON>", "in": "<PERSON><PERSON><PERSON><PERSON>", "minutes": "<PERSON><PERSON><PERSON><PERSON>", "minutesLeft": "<PERSON><PERSON><PERSON><PERSON> hafe", "viewClass": "<PERSON><PERSON><PERSON>", "prepare": "Qopha<PERSON>'<PERSON>", "ongoing": "<PERSON><PERSON> fufaa jira", "now": "<PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON><PERSON>", "password": "<PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "unknownSubject": "<PERSON><PERSON>", "unknownTeacher": "<PERSON><PERSON><PERSON><PERSON>", "menuOpened": "<PERSON><PERSON><PERSON><PERSON>", "menuClosed": "<PERSON><PERSON><PERSON><PERSON> cu<PERSON>", "showMore": "<PERSON><PERSON><PERSON>", "tapToView": "<PERSON><PERSON>a bal'aa il<PERSON><PERSON> tuqi", "version": "<PERSON><PERSON><PERSON><PERSON>", "section": "Qubee", "step": "Tarka<PERSON><PERSON><PERSON>", "of": "kan", "or": "ykn", "ok": "<PERSON><PERSON>", "minutesAgo": "<PERSON><PERSON><PERSON>aa {{count}} dura", "just now": "<PERSON><PERSON>", "24 minutes ago": "Daqiiqaa 24 dura", "9 minutes ago": "Daqiiqaa 9 dura", "3 days ago": "Guyyaa 3 dura", "1 hour ago": "Sa'aatii 1 dura", "3 hours ago": "Sa'aatii 3 dura"}, "auth": {"login": {"title": "Akkaawunt<PERSON>", "subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> keessan seen<PERSON>uf ragaa keessan gal<PERSON>a", "email": "<PERSON><PERSON><PERSON><PERSON>", "emailPlaceholder": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>a", "password": "<PERSON><PERSON>", "passwordPlaceholder": "<PERSON><PERSON> darbii keessan gal<PERSON>a", "forgotPassword": "<PERSON><PERSON>?", "submit": "<PERSON><PERSON>", "back": "Gara Jalqabaatti Deebi'i", "noAccount": "Akka<PERSON>untii hin qabduu?", "createAccount": "Akkaawuntii Uumi", "rememberMe": "<PERSON>", "loginAs": "<PERSON>kka itti seeni", "admin": "<PERSON><PERSON><PERSON><PERSON>", "teacher": "<PERSON><PERSON><PERSON><PERSON>", "student": "Barataa", "parent": "<PERSON><PERSON><PERSON>"}, "teacher": "<PERSON><PERSON><PERSON><PERSON>", "forgotPassword": {"title": "<PERSON><PERSON>", "subtitle": "<PERSON><PERSON><PERSON><PERSON> ha<PERSON>a jecha darbii fudha<PERSON><PERSON> im<PERSON>ii keessan gal<PERSON>a", "email": "<PERSON><PERSON><PERSON><PERSON>", "emailPlaceholder": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>a", "submit": "<PERSON><PERSON><PERSON><PERSON>", "back": "<PERSON><PERSON>", "checkEmail": "<PERSON><PERSON><PERSON> im<PERSON><PERSON> k<PERSON> il<PERSON>", "resetSent": "<PERSON><PERSON><PERSON><PERSON> ha<PERSON>a jecha darbii er<PERSON>. <PERSON><PERSON><PERSON> bakka imeelii k<PERSON>.", "resetInstructions": "<PERSON><PERSON> darbii keessan ha<PERSON><PERSON><PERSON> qaj<PERSON> imeelii keessa jiru hordo<PERSON>.", "emailLabel": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>a", "resetButton": "<PERSON><PERSON>", "backToLogin": "<PERSON><PERSON><PERSON><PERSON>"}, "landing": {"title": "MANA BARUMSAA SADARKAA LAMMAFFAA ASALLAA", "subtitle": "Barnoota<PERSON>", "schoolName": "<PERSON><PERSON>", "schoolNameFull": "<PERSON><PERSON> Sadarkaa Lammaffaa Asallaa", "schoolNameShort": "MBS", "motto": "Barnoota<PERSON>", "features": {"title": "<PERSON><PERSON><PERSON>", "qualityEducation": "Barnoota Qulqullina Qabu", "qualityEducationDesc": "Barnoota qulqullina qabu kan sirnaa barnootaa guutuu ta'e kan sadarkaa addunyaa guutu fi barattoota milkaa'ina gara fuulduraatiif qopheessu ni dhiyeessina.", "experiencedTeachers": "Barsiisota Muuxannoo <PERSON>", "experiencedTeachersDesc": "Gareen keenya barsiisota muuxannoo qaban kan dande<PERSON>i barataa tokkoo tokkoo xiyyeeffannaa dhuunfaa fi maloota barsiisuu haaraa fayyadamuun guddisuuf kutannoo qaban.", "modernFacilities": "<PERSON><PERSON><PERSON><PERSON>", "modernFacilitiesDesc": "<PERSON><PERSON><PERSON><PERSON> keenya kutaalee barnootaa ammayyaa, lab<PERSON><PERSON><PERSON><PERSON><PERSON> sir<PERSON>i qop<PERSON><PERSON><PERSON>, mana kitaabaa guutuu, fi meesh<PERSON>ee ispoortii ammayyaa kan muuxannoo barachuu fooy<PERSON>san of keessaa qaba.", "holisticDevelopment": "<PERSON><PERSON><PERSON>", "holisticDevelopmentDesc": "<PERSON><PERSON><PERSON><PERSON>a guutuu - sa<PERSON>u<PERSON>, <PERSON><PERSON><PERSON><PERSON>, ha<PERSON><PERSON><PERSON><PERSON><PERSON>, fi miira - karaa barnootaa fi gochaalee barnootaa dabalataa irratti xiyyeeffannoo ni goona."}, "buttons": {"getstarted": "<PERSON><PERSON><PERSON><PERSON>", "learnMore": "<PERSON><PERSON><PERSON><PERSON>", "contactUs": "<PERSON><PERSON>", "quickLogin": "<PERSON><PERSON><PERSON>"}, "languageSelector": {"title": "<PERSON><PERSON><PERSON>", "english": "Ingiliffaa", "amharic": "<PERSON>aar<PERSON><PERSON>", "afaanOromo": "<PERSON><PERSON><PERSON>"}, "footer": {"copyright": "© 2024 <PERSON><PERSON>. <PERSON><PERSON> isaa hunda kan of k<PERSON><PERSON>a qabu.", "motto": "Barnoota<PERSON>", "rights": "<PERSON><PERSON>", "contactInfo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "address": "Asallaa, Itoophiyaa", "phone": "+251 123 456 789", "email": "<EMAIL>"}}, "errors": {"invalidEmail": "<PERSON><PERSON><PERSON> im<PERSON> dogoggora", "invalidPassword": "<PERSON><PERSON> darbiin yoo xiqqaate qubeewwan 6 ta'uu qaba", "wrongPassword": "<PERSON><PERSON> da<PERSON>ii dogoggora", "userNotFound": "<PERSON><PERSON><PERSON><PERSON> hin argamne", "emailAlreadyInUse": "<PERSON><PERSON><PERSON><PERSON> duraan fayyadamaa jira", "weakPassword": "<PERSON><PERSON> darbiin baay'ee dadhabaa dha", "networkError": "Dogog<PERSON><PERSON> ne<PERSON>ii. <PERSON><PERSON><PERSON> quunnamtii k<PERSON>san mi<PERSON>", "unexpected": "<PERSON><PERSON><PERSON><PERSON> hin e<PERSON><PERSON><PERSON>", "resetPasswordFailed": "<PERSON><PERSON><PERSON><PERSON> ha<PERSON>msa jecha darbii erguu hin danda'amne", "emailNotVerified": "<PERSON><PERSON><PERSON><PERSON> hin mirkano<PERSON><PERSON>. <PERSON><PERSON><PERSON> dursa imeelii keessan mirka<PERSON>."}, "resetPasswordSuccess": "<PERSON><PERSON><PERSON><PERSON> ha<PERSON>a jecha darbii er<PERSON>. <PERSON><PERSON><PERSON> bakka imeelii k<PERSON>.", "confirmLogout": "<PERSON><PERSON><PERSON><PERSON>", "logoutConfirmMessage": "<PERSON><PERSON><PERSON><PERSON> sistemaa kana keessaa bahuu barba<PERSON>duu?", "User logged in": "<PERSON><PERSON><PERSON><PERSON>", "User logged out": "<PERSON><PERSON><PERSON><PERSON> ba'e<PERSON>", "Admin User logged into the system": "<PERSON><PERSON><PERSON><PERSON>", "Admin User logged out of the system": "<PERSON><PERSON><PERSON><PERSON> ba'e<PERSON>"}, "teacher": {"dashboard": {"title": "Daash<PERSON><PERSON><PERSON>", "welcome": "<PERSON>ga nagaan dhuftan, {{name}}", "quickStats": "Lakkoofsa <PERSON>ffisa<PERSON>", "quickAccess": "Dhaqqabbiinsa Saffisaa", "todayClasses": "<PERSON><PERSON><PERSON><PERSON>", "upcomingClasses": "<PERSON><PERSON><PERSON><PERSON>", "pendingTasks": "<PERSON><PERSON><PERSON>", "recentActivities": "<PERSON><PERSON><PERSON><PERSON>", "messages": "<PERSON><PERSON><PERSON><PERSON>", "notifications": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "noClasses": "<PERSON><PERSON><PERSON><PERSON> har'aa hin jiran", "noUpcomingClasses": "<PERSON><PERSON><PERSON><PERSON> dhufan hin jiran", "noPendingTasks": "<PERSON><PERSON><PERSON> e<PERSON>a hin jiru", "noActivities": "<PERSON><PERSON><PERSON><PERSON> dhiyoo hin jiran", "classesCount": "{{count}} kutaa har'aa", "tasksCount": "{{count}} hojii e<PERSON><PERSON>a", "viewAllClasses": "Ku<PERSON><PERSON><PERSON>", "viewAllTasks": "<PERSON><PERSON><PERSON>", "quickLinks": "Geessitu<PERSON>", "markAttendance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "submitGrades": "Qab<PERSON><PERSON>", "createAssignment": "<PERSON><PERSON><PERSON>", "scheduleExam": "<PERSON><PERSON><PERSON><PERSON>", "uploadResources": "Qabeenya <PERSON>i", "communicateWithParents": "<PERSON><PERSON><PERSON>", "viewStudentPerformance": "Raawwii Barata<PERSON>aal<PERSON>", "classesToday": "<PERSON><PERSON><PERSON><PERSON>", "period": "Yeroo", "class": "<PERSON><PERSON><PERSON>", "section": "<PERSON><PERSON><PERSON>", "time": "Yeroo", "room": "<PERSON><PERSON><PERSON>", "dashboard": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "drawerMenu": {"academic": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "personal": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "classManagement": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "myClasses": "<PERSON><PERSON><PERSON><PERSON>", "allClasses": "<PERSON><PERSON><PERSON><PERSON>", "activeClasses": "Kutaalee <PERSON>", "pastClasses": "Kutaale<PERSON>", "upcomingClasses": "<PERSON><PERSON><PERSON><PERSON>", "noClasses": "<PERSON><PERSON><PERSON><PERSON> hin jiran", "searchClass": "<PERSON><PERSON><PERSON>", "filterBySubject": "<PERSON><PERSON>", "filterByGrade": "<PERSON><PERSON><PERSON>", "filterByStatus": "<PERSON><PERSON>", "class": "<PERSON><PERSON><PERSON>", "section": "<PERSON><PERSON><PERSON>", "subject": "<PERSON><PERSON>", "schedule": "Sagantaa", "students": "<PERSON><PERSON><PERSON><PERSON>", "status": "Ha<PERSON>", "actions": "Tarkaanfiiwwan", "viewClass": "<PERSON><PERSON><PERSON>", "takeAttendance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "manageAssignments": "<PERSON><PERSON><PERSON>", "viewStudents": "<PERSON><PERSON><PERSON><PERSON>", "viewResources": "<PERSON><PERSON><PERSON><PERSON>", "classDetails": "<PERSON><PERSON><PERSON>", "className": "<PERSON><PERSON><PERSON>", "grade": "<PERSON><PERSON><PERSON>", "classTeacher": "<PERSON><PERSON><PERSON><PERSON>", "classTiming": "<PERSON><PERSON>", "classRoom": "<PERSON><PERSON><PERSON>", "classDescription": "<PERSON><PERSON><PERSON>", "studentList": "<PERSON><PERSON><PERSON>", "attendanceHistory": "<PERSON><PERSON><PERSON>", "assignmentHistory": "<PERSON><PERSON><PERSON>", "resourceList": "<PERSON><PERSON><PERSON>", "examSchedule": "<PERSON><PERSON><PERSON>", "communicationLog": "<PERSON><PERSON><PERSON><PERSON>", "lessonPlan": "Ka<PERSON>ra <PERSON>", "addResource": "<PERSON><PERSON><PERSON><PERSON>", "createAssignment": "<PERSON><PERSON><PERSON>", "scheduleExam": "<PERSON><PERSON><PERSON><PERSON>", "sendMessage": "<PERSON><PERSON><PERSON>", "viewAttendance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "viewAssignments": "<PERSON><PERSON><PERSON>", "viewExams": "<PERSON><PERSON><PERSON><PERSON>"}, "attendance": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "markAttendance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "viewAttendance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attendanceHistory": "<PERSON><PERSON><PERSON>", "noAttendanceRecords": "<PERSON><PERSON><PERSON><PERSON> dhiyeenyaa hin jiru", "searchStudent": "Barataa <PERSON>adi", "filterByDate": "<PERSON><PERSON><PERSON>", "filterByStatus": "<PERSON><PERSON>", "class": "<PERSON><PERSON><PERSON>", "section": "<PERSON><PERSON><PERSON>", "date": "<PERSON><PERSON><PERSON>", "student": "Barataa", "status": "Ha<PERSON>", "remarks": "<PERSON><PERSON>", "actions": "Tarkaanfiiwwan", "save": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON>"}, "gradeManagement": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enterGrades": "Qab<PERSON><PERSON>", "viewGrades": "<PERSON><PERSON><PERSON><PERSON>", "gradeHistory": "<PERSON><PERSON><PERSON>", "noGradeRecords": "<PERSON><PERSON><PERSON><PERSON> qab<PERSON>i hin jiru", "searchStudent": "Barataa <PERSON>adi", "filterByClass": "<PERSON><PERSON><PERSON>", "filterBySubject": "<PERSON><PERSON>", "filterByExam": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "class": "<PERSON><PERSON><PERSON>", "section": "<PERSON><PERSON><PERSON>", "subject": "<PERSON><PERSON>", "exam": "<PERSON><PERSON><PERSON><PERSON>", "student": "Barataa", "grade": "<PERSON><PERSON><PERSON><PERSON>", "score": "<PERSON><PERSON><PERSON><PERSON>", "maxScore": "Qab<PERSON><PERSON>", "percentage": "Dhibbeentaa", "remarks": "<PERSON><PERSON>", "actions": "Tarkaanfiiwwan", "save": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON>", "gradesSaved": "<PERSON><PERSON><PERSON>in milk<PERSON>'inaan olk<PERSON>'ame", "gradesSubmitted": "<PERSON><PERSON><PERSON>in milk<PERSON>'inaan dhiyaate", "gradesApproved": "Qab<PERSON>in milk<PERSON>'inaan hayyamame", "gradesRejected": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "submitForApproval": "<PERSON><PERSON><PERSON>", "editGrades": "<PERSON><PERSON><PERSON><PERSON>", "viewDetails": "<PERSON><PERSON><PERSON><PERSON>", "gradeStats": "Lakkoofsa Qabxii", "averageScore": "<PERSON><PERSON><PERSON><PERSON>: {{score}}", "highestScore": "<PERSON><PERSON><PERSON><PERSON>: {{score}}", "lowestScore": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>: {{score}}", "passRate": "Hanga Darbe: {{rate}}%", "selectExam": "<PERSON><PERSON><PERSON><PERSON>", "selectClass": "<PERSON><PERSON><PERSON>", "selectSection": "<PERSON><PERSON><PERSON>", "selectSubject": "<PERSON><PERSON>", "gradeDistribution": "<PERSON><PERSON><PERSON><PERSON>", "excellent": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "good": "<PERSON><PERSON><PERSON>", "average": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "belowAverage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "poor": "<PERSON><PERSON><PERSON>", "assessmentName": "<PERSON><PERSON><PERSON>", "assessmentType": "<PERSON><PERSON>", "assessmentDate": "<PERSON><PERSON><PERSON>", "assessmentWeight": "<PERSON><PERSON><PERSON>", "addAssessment": "<PERSON><PERSON><PERSON><PERSON>", "editAssessment": "<PERSON><PERSON><PERSON><PERSON>", "deleteAssessment": "<PERSON><PERSON><PERSON><PERSON>", "confirmDeleteAssessment": "<PERSON><PERSON><PERSON>i kana haquu barba<PERSON>du?", "assessmentDeleted": "<PERSON><PERSON><PERSON><PERSON> milk<PERSON>'inaan haqame", "assessmentAdded": "<PERSON><PERSON><PERSON><PERSON> milk<PERSON>'<PERSON>aan da<PERSON>", "assessmentUpdated": "<PERSON><PERSON><PERSON><PERSON> milk<PERSON>'inaan ha<PERSON>me"}, "assignments": {"title": "<PERSON>", "createAssignment": "<PERSON><PERSON><PERSON>", "viewAssignments": "<PERSON><PERSON><PERSON>", "assignmentHistory": "<PERSON><PERSON><PERSON>", "noAssignments": "<PERSON><PERSON><PERSON> manaa hin jiru", "searchAssignment": "<PERSON><PERSON><PERSON>", "filterByClass": "<PERSON><PERSON><PERSON>", "filterBySubject": "<PERSON><PERSON>", "filterByStatus": "<PERSON><PERSON>", "class": "<PERSON><PERSON><PERSON>", "section": "<PERSON><PERSON><PERSON>", "subject": "<PERSON><PERSON>", "description": "<PERSON><PERSON>a", "dueDate": "<PERSON><PERSON><PERSON>", "status": "Ha<PERSON>", "actions": "Tarkaanfiiwwan", "save": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON>", "assignmentSaved": "<PERSON><PERSON><PERSON> manaa milkaa'inaan olkaa'ame", "assignmentPublished": "<PERSON><PERSON><PERSON> manaa milkaa'inaan maxxanfame", "assignmentDeleted": "<PERSON><PERSON><PERSON> manaa milkaa'inaan haqame", "editAssignment": "<PERSON><PERSON><PERSON>", "deleteAssignment": "<PERSON><PERSON><PERSON>", "viewDetails": "<PERSON><PERSON><PERSON><PERSON>", "assignmentStats": "Lakkoofsa Ho<PERSON> Man<PERSON>", "submittedCount": "{{count}} dhiyaate", "pendingCount": "{{count}} eeg<PERSON>a", "lateCount": "{{count}} tureef", "totalStudents": "<PERSON><PERSON><PERSON><PERSON> wali<PERSON>a {{count}}", "submissionRate": "<PERSON>a Dhiyeessii: {{rate}}%", "selectDueDate": "<PERSON><PERSON><PERSON>", "selectClass": "<PERSON><PERSON><PERSON>", "selectSection": "<PERSON><PERSON><PERSON>", "selectSubject": "<PERSON><PERSON>", "assignmentType": "<PERSON><PERSON>", "maxScore": "Qab<PERSON><PERSON>", "attachments": "Wal<PERSON><PERSON><PERSON>sawwan", "addAttachment": "Walqa<PERSON><PERSON><PERSON>", "removeAttachment": "Walqa<PERSON><PERSON><PERSON>", "draft": "Qabxee", "published": "Maxxanfame", "closed": "<PERSON><PERSON><PERSON>", "upcoming": "<PERSON><PERSON>", "active": "Hojjetaa", "past": "<PERSON><PERSON>", "viewSubmissions": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gradeSubmissions": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "publishAssignment": "<PERSON><PERSON>i Man<PERSON>i", "unpublishAssignment": "<PERSON><PERSON><PERSON> Man<PERSON>", "closeAssignment": "<PERSON><PERSON><PERSON>", "reopenAssignment": "Hojii Manaa Irra Deebi'i Bani", "confirmDeleteAssignment": "Hojii manaa kana haquu barba<PERSON>du?", "studentName": "<PERSON><PERSON><PERSON>", "submissionDate": "<PERSON><PERSON><PERSON>", "submissionStatus": "<PERSON><PERSON>", "grade": "<PERSON><PERSON><PERSON><PERSON>", "feedback": "<PERSON><PERSON>", "submitted": "<PERSON><PERSON><PERSON><PERSON>", "notSubmitted": "<PERSON>n dhi<PERSON>", "late": "<PERSON><PERSON>", "graded": "Qab<PERSON>effame", "notGraded": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "downloadSubmission": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "provideFeedback": "<PERSON><PERSON>", "assignGrade": "<PERSON><PERSON><PERSON><PERSON>"}, "resources": {"title": "Qabeenya", "uploadResource": "Qabeenya <PERSON>i", "viewResources": "<PERSON><PERSON><PERSON><PERSON>", "resourceLibrary": "<PERSON><PERSON>", "noResources": "<PERSON><PERSON><PERSON><PERSON> hin jiru", "searchResource": "Qabeenya <PERSON>adi", "filterByType": "<PERSON><PERSON>", "filterByClass": "<PERSON><PERSON><PERSON>", "filterBySubject": "<PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "type": "<PERSON><PERSON>", "class": "<PERSON><PERSON><PERSON>", "subject": "<PERSON><PERSON>", "uploadDate": "<PERSON><PERSON><PERSON>", "size": "<PERSON><PERSON>", "actions": "Tarkaanfiiwwan", "save": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON>", "resourceSaved": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>'inaan olkaa'ame", "resourceDeleted": "<PERSON><PERSON><PERSON><PERSON> milk<PERSON>'inaan haqame", "resourceShared": "<PERSON><PERSON><PERSON><PERSON> milk<PERSON>'<PERSON>aan qoodame", "editResource": "<PERSON><PERSON><PERSON><PERSON>", "deleteResource": "Qabe<PERSON><PERSON>", "shareResource": "<PERSON><PERSON><PERSON><PERSON>", "viewDetails": "<PERSON><PERSON><PERSON><PERSON>", "downloadResource": "<PERSON><PERSON><PERSON><PERSON>", "resourceStats": "Lakkoofsa <PERSON>enya", "totalResources": "<PERSON><PERSON><PERSON><PERSON> wali<PERSON>a {{count}}", "selectClass": "<PERSON><PERSON><PERSON>", "selectSubject": "<PERSON><PERSON>", "resourceType": "<PERSON><PERSON>", "description": "<PERSON><PERSON>a", "file": "<PERSON><PERSON><PERSON><PERSON>", "selectFile": "<PERSON><PERSON><PERSON><PERSON>", "removeFile": "<PERSON><PERSON><PERSON><PERSON>", "document": "Sanadoota", "video": "Viidiyoo", "audio": "<PERSON><PERSON>", "image": "<PERSON><PERSON><PERSON>", "presentation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "worksheet": "<PERSON><PERSON><PERSON>", "other": "<PERSON><PERSON>", "shareWith": "<PERSON><PERSON><PERSON>", "allStudents": "<PERSON><PERSON><PERSON><PERSON>", "specificClasses": "Kutaalee <PERSON>e", "specificStudents": "<PERSON><PERSON><PERSON><PERSON>e", "confirmDeleteResource": "Qabe<PERSON>ya kana haquu barba<PERSON>du?", "viewCount": "{{count}} il<PERSON>e", "downloadCount": "{{count}} buuse", "uploadSuccess": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>'inaan olkaa'ame", "uploadError": "<PERSON><PERSON><PERSON><PERSON> o<PERSON>'uu irratti dogoggora"}, "communication": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "messages": "<PERSON><PERSON><PERSON><PERSON>", "announcements": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "notifications": "<PERSON><PERSON>effan<PERSON><PERSON>", "noMessages": "<PERSON><PERSON><PERSON> hin jiru", "noAnnouncements": "<PERSON><PERSON><PERSON><PERSON> hin jiru", "noNotifications": "<PERSON><PERSON><PERSON><PERSON><PERSON> hin jiru", "compose": "<PERSON><PERSON><PERSON>", "newMessage": "<PERSON><PERSON><PERSON>", "newAnnouncement": "<PERSON><PERSON><PERSON>", "inbox": "<PERSON><PERSON><PERSON><PERSON>", "sent": "<PERSON><PERSON>", "drafts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "trash": "<PERSON><PERSON><PERSON>", "from": "<PERSON><PERSON><PERSON>", "to": "<PERSON><PERSON>", "subject": "<PERSON>", "message": "<PERSON><PERSON><PERSON>", "date": "<PERSON><PERSON><PERSON>", "actions": "Tarkaanfiiwwan", "send": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON>", "messageSent": "<PERSON><PERSON><PERSON> milk<PERSON>'inaan ergame", "announcementPosted": "Beeksisni milkaa'inaan maxxanfame", "draftSaved": "Qabxeen milk<PERSON>'inaan olkaa'ame", "messageDeleted": "<PERSON><PERSON><PERSON> milk<PERSON>'<PERSON>aan haqame", "viewMessage": "<PERSON><PERSON><PERSON>", "deleteMessage": "<PERSON><PERSON><PERSON>", "replyMessage": "<PERSON><PERSON><PERSON>", "forwardMessage": "<PERSON><PERSON><PERSON>", "viewAnnouncement": "<PERSON><PERSON><PERSON>", "deleteAnnouncement": "<PERSON><PERSON><PERSON>", "selectRecipients": "<PERSON><PERSON><PERSON><PERSON>", "allStudents": "<PERSON><PERSON><PERSON><PERSON>", "allParents": "<PERSON><PERSON><PERSON>", "specificClasses": "Kutaalee <PERSON>e", "specificStudents": "<PERSON><PERSON><PERSON><PERSON>e", "specificParents": "<PERSON><PERSON><PERSON> Mu<PERSON>", "attachments": "Wal<PERSON><PERSON><PERSON>sawwan", "addAttachment": "Walqa<PERSON><PERSON><PERSON>", "removeAttachment": "Walqa<PERSON><PERSON><PERSON>", "confirmDeleteMessage": "Ergaa kana haquu barba<PERSON>du?", "confirmDeleteAnnouncement": "<PERSON><PERSON><PERSON> kana haquu barba<PERSON>du?", "markAsRead": "<PERSON><PERSON>", "markAsUnread": "<PERSON><PERSON>", "reply": "<PERSON><PERSON><PERSON>", "replyAll": "<PERSON><PERSON><PERSON>", "forward": "<PERSON><PERSON><PERSON>", "saveDraft": "<PERSON><PERSON><PERSON><PERSON>", "discard": "<PERSON><PERSON><PERSON>", "minutesAgo": "<PERSON><PERSON><PERSON>aa {{count}} dura"}, "profile": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "personalInfo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "academicInfo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "contactInfo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "changePassword": "<PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON><PERSON>", "phone": "<PERSON><PERSON><PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON>", "dateOfBirth": "<PERSON><PERSON><PERSON>", "gender": "Saala", "male": "<PERSON><PERSON><PERSON>", "female": "<PERSON><PERSON><PERSON>", "other": "<PERSON><PERSON>", "qualification": "<PERSON><PERSON><PERSON><PERSON>", "experience": "<PERSON><PERSON><PERSON><PERSON>", "joinDate": "<PERSON><PERSON><PERSON>", "department": "<PERSON><PERSON>", "subjects": "<PERSON><PERSON>", "classes": "Kutaalee", "currentPassword": "<PERSON><PERSON>", "newPassword": "<PERSON><PERSON>", "confirmPassword": "<PERSON><PERSON>", "save": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON>", "profileUpdated": "<PERSON><PERSON><PERSON><PERSON><PERSON> milk<PERSON>'<PERSON>aan ha<PERSON>me", "passwordChanged": "<PERSON><PERSON> darbiin milk<PERSON>'<PERSON>aan jij<PERSON>me", "uploadPhoto": "<PERSON><PERSON><PERSON>", "removePhoto": "<PERSON><PERSON><PERSON>", "photoUpdated": "<PERSON><PERSON><PERSON> milk<PERSON>'<PERSON>aan ha<PERSON>me", "minutesAgo": "<PERSON><PERSON><PERSON>aa {{count}} dura"}, "schedule": {"title": "Bulchiinsa <PERSON>"}, "directory": {"title": "<PERSON><PERSON><PERSON>", "searchPlaceholder": "Barsiisota barbaadi...", "allDepartments": "<PERSON><PERSON><PERSON><PERSON>", "subjects": "<PERSON><PERSON>", "noSubjects": "<PERSON><PERSON> barn<PERSON>aa hin ramadamne"}}, "admin": {"classManagement": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "addClass": "<PERSON><PERSON><PERSON>", "editClass": "<PERSON><PERSON><PERSON>", "deleteClass": "<PERSON><PERSON><PERSON>", "classDetails": "<PERSON><PERSON><PERSON>", "classList": "<PERSON><PERSON><PERSON>", "class": "<PERSON><PERSON><PERSON>", "section": "Qubee", "sections": "<PERSON><PERSON><PERSON><PERSON>", "capacity": "<PERSON><PERSON><PERSON><PERSON>", "studentsCount": "<PERSON><PERSON><PERSON>a {{current}}/{{capacity}}", "lowOccupancy": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>a", "mediumOccupancy": "<PERSON><PERSON><PERSON><PERSON>", "highOccupancy": "<PERSON><PERSON><PERSON><PERSON>", "students": "<PERSON><PERSON><PERSON><PERSON>", "teachers": "Barsiisota", "teachersTitle": "Barsiisota", "noTeachers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "addTeacher": "<PERSON><PERSON><PERSON><PERSON>", "viewStudents": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON>a", "noSections": "<PERSON><PERSON><PERSON><PERSON>", "addSection": "<PERSON><PERSON><PERSON>", "assignTeacherToSection": "<PERSON><PERSON><PERSON><PERSON>", "selectSubject": "<PERSON><PERSON>", "noSubjectsAvailable": "<PERSON><PERSON>", "retry": "<PERSON><PERSON>", "searchTeachers": "Barsiisota Barbaadi", "assign": "<PERSON><PERSON>", "cancel": "<PERSON><PERSON>", "teacherAssignedSuccess": "<PERSON><PERSON><PERSON><PERSON> milk<PERSON>'<PERSON><PERSON> ramada<PERSON>ra", "errorAssigningTeacher": "<PERSON><PERSON><PERSON><PERSON> ram<PERSON>u irratti dogoggora", "classNameRequired": "<PERSON><PERSON><PERSON> k<PERSON>a bar<PERSON>", "descriptionRequired": "<PERSON><PERSON><PERSON>", "academicYearRequired": "<PERSON><PERSON><PERSON><PERSON> barn<PERSON>aa barba<PERSON>", "sectionRequired": "<PERSON><PERSON> xiq<PERSON>ate qubee tokko bar<PERSON>ha", "sectionsValidation": "<PERSON><PERSON><PERSON><PERSON> hundi maqaa fi dandeettii qabaachuu qabu", "groupRequired": "<PERSON><PERSON> xiq<PERSON>ate garee tokko bar<PERSON>", "classAddedSuccess": "<PERSON><PERSON><PERSON> milk<PERSON>'<PERSON><PERSON> da<PERSON>", "errorAddingClass": "<PERSON><PERSON><PERSON>", "errorUpdatingClass": "<PERSON><PERSON><PERSON> dog<PERSON>a", "errorFetchingClasses": "Ku<PERSON>ale<PERSON> fidu<PERSON>a", "errorFetchingTeachers": "Barsiisota fiduuf dog<PERSON>gora", "cannotDeleteClassWithStudents": "<PERSON><PERSON><PERSON> barattoota qabu haquu hin danda'amu", "errorDeletingClass": "<PERSON><PERSON><PERSON> haqu<PERSON>a", "topPerformers": "Raawwattoota Ol'aanaa", "loadingStudents": "<PERSON><PERSON>oota fe'amu...", "searchStudents": "<PERSON><PERSON><PERSON><PERSON> bar<PERSON>", "name": "<PERSON><PERSON><PERSON>", "rollNumber": "Lakkoofsa G<PERSON>mee", "email": "<PERSON><PERSON><PERSON><PERSON>", "averageScore": "<PERSON><PERSON><PERSON><PERSON>", "actions": "Tarkaanfiiwwan"}, "dashboard": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>ul<PERSON>", "analytics": "<PERSON><PERSON><PERSON><PERSON>", "welcome": "<PERSON>ga nagaan dhuftan, {{name}}", "overview": "Il<PERSON><PERSON>", "quickAccess": "Dhaqqabbiinsa Saffisaa", "recentActivities": "<PERSON><PERSON><PERSON><PERSON>", "noActivities": "<PERSON><PERSON><PERSON><PERSON> dhiyoo hin jiran", "totalStudents": "<PERSON><PERSON><PERSON><PERSON>", "totalTeachers": "Barsiisota Waliigalaa", "totalClasses": "Kutaale<PERSON>", "activeUsers": "Fayyadamtoota Hojjetaa", "enrollment": "<PERSON><PERSON><PERSON>", "performance": "Raaw<PERSON><PERSON>", "resources": "Qabeenya", "showMore": "<PERSON><PERSON><PERSON>", "showLess": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "searchResults": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "administrator": "<PERSON><PERSON><PERSON><PERSON>", "present": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "absent": "<PERSON><PERSON>", "gradeDistribution": "<PERSON><PERSON><PERSON><PERSON>", "performanceTrends": "<PERSON><PERSON><PERSON>", "attendanceRate": "<PERSON><PERSON>", "topPerformers": "Raawwattoota Ol'aanaa", "improvementAreas": "<PERSON><PERSON><PERSON>", "average": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attendance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "averageScore": "<PERSON><PERSON><PERSON><PERSON>", "academicManagement": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "userManagement": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "classExam": "Kutaa & Qormaata", "teacherTools": "<PERSON><PERSON><PERSON><PERSON>", "resourcesLibrary": "Qabeenya & Mana <PERSON>", "communication": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "systemReports": "Sirna & Gabaasa", "attendanceManagement": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "attendanceApproval": "<PERSON><PERSON>", "attendanceReports": "<PERSON><PERSON><PERSON>", "schoolOverview": "Ilaalcha <PERSON>", "lastUpdated": "<PERSON><PERSON> dhumaa kan ha<PERSON>m<PERSON>me", "dataUpdated": "<PERSON><PERSON><PERSON>", "errorFetchingStats": "Lakkoofsa fiduuf dogoggora", "systemStarted": "<PERSON><PERSON><PERSON>", "systemInitialized": "<PERSON><PERSON><PERSON><PERSON>aan jalqa<PERSON>ra", "justNow": "<PERSON><PERSON>"}}, "navigation": {"chat": "<PERSON><PERSON>"}, "activities": {"details": "<PERSON><PERSON><PERSON>", "management": "Bulchiinsa <PERSON>e"}, "administrative": {"roles": {"admin": "<PERSON><PERSON><PERSON><PERSON>"}}, "studentDashboard": {"title": "Daashboo<PERSON><PERSON> Bar<PERSON>", "dashboard": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "welcome": "Baga Na<PERSON>", "gpa": "GPA", "attendance": "<PERSON><PERSON><PERSON>", "quickAccess": "<PERSON><PERSON><PERSON><PERSON>", "showAll": "<PERSON><PERSON>", "searchMenu": "<PERSON><PERSON>", "todaySchedule": "<PERSON><PERSON><PERSON><PERSON>", "upcomingExams": "Imala <PERSON>", "noExams": "<PERSON><PERSON><PERSON> I<PERSON>", "homework": "<PERSON><PERSON><PERSON>", "noHomework": "<PERSON><PERSON><PERSON>", "analytics": "Furma<PERSON>", "mainMenu": "<PERSON><PERSON>", "courses": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "assignments": "Hojiiwwan", "grades": "Sadarkaa", "calendar": "<PERSON><PERSON>darii", "messaging": "<PERSON><PERSON><PERSON>", "settings": "<PERSON><PERSON><PERSON><PERSON><PERSON>a", "profile": "<PERSON><PERSON><PERSON>", "logout": "<PERSON>'i", "menuCategories": {"academic": "Barums<PERSON>", "scheduleAttendance": "<PERSON><PERSON><PERSON><PERSON>", "resources": "Qabeenya", "communication": "<PERSON><PERSON><PERSON>", "personalTools": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "menuItems": {"grades": "Sadarkaa", "results": "Bifaa", "subjects": "<PERSON>g<PERSON><PERSON><PERSON>", "homework": "<PERSON><PERSON><PERSON>", "schedule": "<PERSON><PERSON><PERSON><PERSON>", "attendance": "<PERSON><PERSON><PERSON>", "calendar": "<PERSON><PERSON>darii", "examSchedule": "<PERSON><PERSON><PERSON><PERSON>", "library": "Kit<PERSON><PERSON> Man<PERSON>", "portfolio": "Portfolio", "behavior": "Amala", "achievements": "<PERSON><PERSON><PERSON><PERSON>", "messages": "<PERSON><PERSON><PERSON>", "reports": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "notifications": "<PERSON><PERSON><PERSON><PERSON>", "profile": "<PERSON><PERSON><PERSON>", "settings": "<PERSON><PERSON><PERSON><PERSON><PERSON>a", "help": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "student": {"notAssigned": "<PERSON><PERSON>", "noParent": "<PERSON><PERSON><PERSON>", "endOfList": "<PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON><PERSON>", "subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bar<PERSON> to'adhaa", "searchPlaceholder": "<PERSON><PERSON><PERSON><PERSON> barba<PERSON>...", "totalStudents": "<PERSON><PERSON><PERSON><PERSON>", "totalClasses": "Kutaalee <PERSON>", "name": "<PERSON><PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON><PERSON>", "class": "<PERSON><PERSON><PERSON>", "section": "Qubee", "parent": {"selectParent": "<PERSON><PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON><PERSON>", "addNew": "<PERSON><PERSON><PERSON>", "instructions": "<PERSON><PERSON><PERSON> duraan jiru filachuu ykn haaraa dabaluu dandeessu", "found": "<PERSON><PERSON><PERSON>", "foundMessage": "<PERSON><PERSON><PERSON> im<PERSON>ii kana qabu duraan jira", "linkQuestion": "Ma<PERSON>i kana barataa waliin walqabsii<PERSON>u barba<PERSON>a?"}, "assignSection": "<PERSON><PERSON><PERSON>", "selectClass": "<PERSON><PERSON><PERSON>", "selectSection": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON>", "assign": "<PERSON><PERSON>", "assignParent": "<PERSON><PERSON><PERSON>", "selectParentForStudent": "<PERSON><PERSON><PERSON>", "selectParent": "<PERSON><PERSON><PERSON>", "addNewParent": "<PERSON><PERSON><PERSON>", "registration": "<PERSON><PERSON><PERSON>", "profileImage": {"add": "<PERSON><PERSON><PERSON>"}, "details": {"personalInfo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "fields": {"firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON><PERSON>", "dateOfBirth": "<PERSON><PERSON><PERSON>", "selectDateOfBirth": "<PERSON><PERSON><PERSON>", "gender": {"male": "<PERSON><PERSON><PERSON>", "female": "<PERSON><PERSON><PERSON>", "other": "<PERSON><PERSON>"}, "admissionNumber": "Lakkoofsa G<PERSON>mee", "phone": "<PERSON><PERSON><PERSON><PERSON>", "emergencyContact": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "address": {"street": "<PERSON><PERSON><PERSON>", "city": "<PERSON><PERSON><PERSON><PERSON>", "state": "<PERSON><PERSON><PERSON>", "country": "B<PERSON>yya"}, "parentInformation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "academicDetails": {"previousSchool": "<PERSON><PERSON>", "previousGrade": "<PERSON><PERSON><PERSON>", "reasonForLeaving": "Sababaa Bahu", "previousResultImage": "<PERSON><PERSON><PERSON><PERSON>", "addResultImage": "<PERSON><PERSON><PERSON><PERSON>"}, "add": "<PERSON><PERSON><PERSON>", "messages": {"registrationSuccess": "<PERSON><PERSON><PERSON><PERSON>", "addSuccess": "<PERSON><PERSON><PERSON><PERSON>"}}, "validation": {"password": "<PERSON><PERSON> darbiin yoo xiqqaate qubeewwan 6 ta'uu qaba"}, "bulkImport": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> hedduu yeroo tok<PERSON>ti <PERSON> faayila CSV ol fe'i", "requiredColumns": "<PERSON><PERSON><PERSON><PERSON>", "optionalColumns": "<PERSON><PERSON><PERSON><PERSON>", "parentColumns": "<PERSON><PERSON><PERSON><PERSON>", "selectFile": "<PERSON><PERSON><PERSON><PERSON>"}, "parent": {"dashboard": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "welcome": "<PERSON>ga nagaan dhuftan", "selectChild": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "noChildrenRegistered": "<PERSON><PERSON><PERSON><PERSON><PERSON> sirna kees<PERSON>ti hin galmo<PERSON>.", "updateProfile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "searchMenuItems": "Tarreewwan menu barbaadi...", "active": "Hojjetaa", "gpa": "GPA", "attendance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "behavior": "Amala", "classStatus": "<PERSON><PERSON>", "academicProgress": "<PERSON><PERSON><PERSON>", "academicSummary": "<PERSON><PERSON><PERSON><PERSON><PERSON> keessan gosa barnootaa hedduu keessatti gaarii hoj<PERSON><PERSON> jira, <PERSON><PERSON><PERSON> fi Herregaa keessattis fooyya'insa gaarii agar<PERSON>.", "viewGrades": "Qabxiiwwan <PERSON>aan <PERSON>aali", "attendanceOverview": "<PERSON><PERSON><PERSON>", "noSearchResults": "<PERSON><PERSON><PERSON><PERSON> k<PERSON><PERSON><PERSON> bu'aan hin arga<PERSON>ne", "clearSearch": "<PERSON><PERSON><PERSON><PERSON>", "errorLoadingData": "<PERSON><PERSON><PERSON> da<PERSON><PERSON>rdii fe'uuf hin danda'amne. Maaloo irra deebi'ii yaali.", "errorLoadingChildData": "Da<PERSON>a daa'imaa fe'uuf hin danda'amne. Maaloo irra deebi'ii yaali.", "present": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "absent": "<PERSON><PERSON>", "late": "<PERSON><PERSON><PERSON>", "viewAttendance": "<PERSON><PERSON><PERSON>", "behaviorAnalysis": "<PERSON><PERSON><PERSON><PERSON>", "behaviorSummary": "<PERSON><PERSON><PERSON><PERSON><PERSON> keessan kutaa keessatti amala baay'ee gaarii agar<PERSON>, hi<PERSON><PERSON><PERSON> c<PERSON>a qaba, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> hi<PERSON><PERSON><PERSON> is<PERSON>ii<PERSON>s kabaja agar<PERSON>.", "viewBehaviorRecords": "<PERSON><PERSON><PERSON>", "upcomingEvents": "<PERSON><PERSON><PERSON><PERSON>", "viewCalendar": "Kaaleend<PERSON>", "quickAccess": "Dhaqqabbiinsa Saffisaa", "showLess": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "showAll": "<PERSON><PERSON>", "showMoreCategories": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "examSchedule": "<PERSON><PERSON><PERSON>", "classSchedule": "<PERSON><PERSON><PERSON>", "messages": "<PERSON><PERSON><PERSON><PERSON>", "noChildrenTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "noChildrenMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON> her<PERSON><PERSON> k<PERSON>san waliin walqabatan hin jiran. <PERSON><PERSON><PERSON> bul<PERSON>a quunnamaa.", "errorTitle": "Dogoggora", "childDataError": "Odeeffannoo daa'ima k<PERSON>ii fe'uuf rakkoo ture. Odeeffannoon tokko tokko argamuu dhiisuu danda'a.", "parentDataError": "<PERSON><PERSON><PERSON><PERSON><PERSON> maatii keessanii argamuu hin dandeenye. Ma<PERSON><PERSON> bulchaa quunnamaa.", "dataLoadError": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> keessan fe'uuf rakkoo ture. <PERSON><PERSON>o yeroo muraasa booda irra deebi'aa yaala."}, "childSchedule": "<PERSON><PERSON><PERSON>", "selectChild": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "navigation": {"yourChildren": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dashboard": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "academics": "Barnootaa", "grades": "Qabxiiwwan & Bu'aawwan", "homework": "<PERSON><PERSON><PERSON>", "classSchedule": "<PERSON><PERSON><PERSON>", "examSchedule": "<PERSON><PERSON><PERSON>", "attendance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attendanceView": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attendanceReport": "<PERSON><PERSON><PERSON>", "behavior": "Amala", "behaviorView": "<PERSON><PERSON>", "behaviorReport": "G<PERSON><PERSON>", "communication": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "notifications": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "profile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settings": "<PERSON><PERSON><PERSON><PERSON><PERSON>a", "selectChildFirst": "<PERSON><PERSON><PERSON> dursa daa'ima filadhu", "weeklyClassSchedule": "<PERSON><PERSON><PERSON>", "messages": "<PERSON><PERSON><PERSON><PERSON>", "parentTeacherMeetings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>"}, "menu": {"academicMonitoring": "<PERSON><PERSON><PERSON><PERSON>", "academicProgress": "<PERSON><PERSON><PERSON>", "viewGrades": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attendanceRecords": "<PERSON><PERSON><PERSON>", "examSchedule": "<PERSON><PERSON><PERSON>", "classSchedule": "<PERSON><PERSON><PERSON>", "homeworkStatus": "<PERSON><PERSON>", "subjectPerformance": "Raawwii Go<PERSON>", "behaviorRecords": "<PERSON><PERSON><PERSON>", "communication": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "messageCenter": "<PERSON><PERSON><PERSON><PERSON>", "schoolAnnouncements": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parentTeacherMeetings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>", "eventCalendar": "Kaaleendara Gochaa", "notifications": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settings": "<PERSON><PERSON><PERSON><PERSON><PERSON>a", "profileManagement": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "classSchedule": {"loadingSchedule": "Sagantaa har'aa fe'aa jira...", "failedToLoadSchedule": "Sagantaa fe'uuf hin danda'amne. Maaloo irra deebi'ii yaali.", "noClassesToday": "<PERSON><PERSON><PERSON><PERSON><PERSON> sagantaa kutaa hin jiru", "viewFullSchedule": "<PERSON><PERSON><PERSON>", "today": "Har'a", "classes": "Kutaalee", "current": "<PERSON><PERSON><PERSON>", "completed": "<PERSON><PERSON><PERSON>", "upcoming": "<PERSON><PERSON><PERSON><PERSON> jira", "complete": "xum<PERSON><PERSON>", "teacher": "<PERSON><PERSON><PERSON><PERSON>", "room": "<PERSON><PERSON><PERSON>", "contactTeacher": "<PERSON><PERSON><PERSON><PERSON>"}, "sidebar": {"yourChildren": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dashboard": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "academics": "Barnootaa", "attendance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "behavior": "Amala", "communication": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "profile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settings": "<PERSON><PERSON><PERSON><PERSON><PERSON>a", "logout": "<PERSON>'i"}, "studentMonitoring": {"title": "<PERSON><PERSON><PERSON>"}, "attendance": {"title": "<PERSON><PERSON><PERSON>"}, "communication": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parentTitle": "<PERSON><PERSON><PERSON><PERSON>"}, "reports": {"title": "Wiirtuu Gabaasaa"}}, "userManagement": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "addUser": "<PERSON><PERSON><PERSON><PERSON>", "editUser": "<PERSON><PERSON><PERSON><PERSON>", "deleteUser": "Fay<PERSON><PERSON><PERSON>", "userDetails": "<PERSON><PERSON><PERSON>", "userList": "<PERSON><PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON><PERSON>", "phone": "<PERSON><PERSON><PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON>", "joinDate": "<PERSON><PERSON><PERSON>", "actions": "Tarkaanfiiwwan", "searchUser": "Fayyadamaa <PERSON>", "filterByRole": "<PERSON><PERSON><PERSON>", "filterByStatus": "<PERSON><PERSON>", "sortBy": "<PERSON><PERSON><PERSON><PERSON>", "userDeleted": "Fayyadamaan milk<PERSON>'inaan haqameera", "userAdded": "Fayyadamaan milk<PERSON>'inaan da<PERSON>", "userUpdated": "Fayyadamaan milk<PERSON>'inaan ha<PERSON>", "clearFilters": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "clearFilter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "loading": "Fayyadamtoota fe'amu...", "noUsers": "Fayyadamtoonni hin argamne", "errorFetching": "Fayyadamtoota fiduu irratti dogoggora", "errorUpdating": "Haala fayyadamaa haaromsuuf dogoggora", "errorDeleting": "<PERSON><PERSON><PERSON><PERSON> haquuf dog<PERSON>a", "userActivated": "Fayyadamaan milkaa'inaan hojjetaa ta'e", "userDeactivated": "Fayyadamaan milkaa'inaan hojjetaa hin taane", "passwordResetSent": "<PERSON><PERSON><PERSON><PERSON> ha<PERSON>a jecha darbii milk<PERSON>'inaan er<PERSON>era", "errorPasswordReset": "<PERSON><PERSON><PERSON><PERSON> ha<PERSON>msa jecha darbii erguu hin danda'amne", "authDeletionPending": "<PERSON><PERSON><PERSON><PERSON>iin fayyadamaa booda ni qulqullaa'a", "success": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "notProvided": "<PERSON>n dhi<PERSON>", "fetchedUsers": "Fayyadamtoota {{count}} fidame", "teachingCount": "Ku<PERSON><PERSON><PERSON> {{count}} bars<PERSON><PERSON><PERSON> jira", "quickAccess": "Dhaqqabbiinsa Saffisaa", "addAdmin": "<PERSON><PERSON><PERSON><PERSON>", "addTeacher": "<PERSON><PERSON><PERSON><PERSON>", "addStudent": "<PERSON><PERSON><PERSON>", "addParent": "<PERSON><PERSON><PERSON>", "userStatistics": "Lakkoofsa <PERSON>", "newUsers": "Fayyadamtoota Haara<PERSON>", "search": "Barbaadi", "tooltipFilter": "Fayyadamtoota Calleessi", "tooltipSort": "Fayyadamtoota Tarreessi", "tooltipChangeView": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "admin": "<PERSON><PERSON><PERSON><PERSON>", "active": "Hojjetaa", "tooltipEdit": "<PERSON><PERSON><PERSON><PERSON>", "resetPassword": "<PERSON><PERSON>", "deactivate": "Hojjet<PERSON>", "student": "Barataa", "unknown": "<PERSON><PERSON>", "parent": "<PERSON><PERSON><PERSON>", "teacher": "<PERSON><PERSON><PERSON><PERSON>", "allRoles": "<PERSON><PERSON><PERSON>", "allStatuses": "<PERSON><PERSON>", "inactive": "<PERSON><PERSON>", "sortName": "<PERSON><PERSON><PERSON>", "sortEmail": "<PERSON><PERSON><PERSON><PERSON>", "sortRole": "<PERSON><PERSON><PERSON>", "sortStatus": "<PERSON><PERSON>", "sortCreated": "<PERSON><PERSON><PERSON>", "ascending": "Tartiiba Ol Deemaa", "descending": "Tartiiba Gadi Deemaa", "confirmDelete": "<PERSON><PERSON><PERSON><PERSON>", "confirmDeleteMessage": "<PERSON><PERSON><PERSON>aa kana haquu akka barbaaddu mirkanee<PERSON><PERSON>hu?", "confirmActivate": "<PERSON><PERSON><PERSON><PERSON>", "confirmActivateMessage": "<PERSON><PERSON><PERSON>aa kana hojjetaa taasisuu akka barbaaddu mirkanee<PERSON><PERSON>hu?", "activate": "<PERSON><PERSON><PERSON><PERSON>", "confirmDeactivate": "<PERSON>j<PERSON><PERSON>", "confirmDeactivateMessage": "<PERSON><PERSON><PERSON><PERSON> kana hojjetaa hin ta<PERSON>in akka barbaaddu mirkanee<PERSON><PERSON>?", "resetPasswordMessage": "<PERSON><PERSON> darbii fayyadamaa kanaa haaromsuuf barba<PERSON>daa?", "sendResetEmail": "<PERSON><PERSON><PERSON><PERSON>", "role": "<PERSON><PERSON><PERSON>", "status": "Ha<PERSON>", "phoneNumber": "Lakkoofsa Bilbilaa", "lastLogin": "<PERSON><PERSON><PERSON>", "never": "<PERSON><PERSON><PERSON>", "createdAt": "<PERSON><PERSON>", "edit": "<PERSON><PERSON><PERSON>"}, "Cloudinary Test": "Qorannoo Cloudinary", "Admin Dashboard": "<PERSON><PERSON><PERSON><PERSON><PERSON>ul<PERSON>", "time": {"minutesAgo": "da<PERSON><PERSON><PERSON> dura"}, "status": {"lastSeen": "<PERSON><PERSON> dhumaa mul'ate"}}