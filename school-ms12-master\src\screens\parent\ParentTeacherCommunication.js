import React, { useState, useEffect } from 'react';
import { View, ScrollView, StyleSheet } from 'react-native';
import { Card, Title, Text, Portal, Modal, TextInput, ActivityIndicator, Avatar } from 'react-native-paper';
import { db, auth } from '../../config/firebase';
import { collection, query, where, getDocs, addDoc, orderBy, onSnapshot } from 'firebase/firestore';
import CustomButton from '../../components/common/CustomButton';
import { Picker } from '@react-native-picker/picker';
import EthiopianCalendar from '../../utils/EthiopianCalendar';
import { useLanguage } from '../../context/LanguageContext';
import EthiopianDatePicker from '../../components/common/EthiopianDatePicker';
import EthiopianTimePicker from '../../components/common/EthiopianTimePicker';

const ParentTeacherCommunication = () => {
  const { language } = useLanguage();
  const [children, setChildren] = useState([]);
  const [selectedChild, setSelectedChild] = useState('');
  const [teachers, setTeachers] = useState([]);
  const [selectedTeacher, setSelectedTeacher] = useState('');
  const [messages, setMessages] = useState([]);
  const [newMessage, setNewMessage] = useState('');
  const [loading, setLoading] = useState(true);
  const [modalVisible, setModalVisible] = useState(false);
  const [meetingRequest, setMeetingRequest] = useState({
    date: '',
    time: '',
    reason: '',
  });

  useEffect(() => {
    fetchChildren();
  }, []);

  useEffect(() => {
    if (selectedChild) {
      fetchTeachers();
    }
  }, [selectedChild]);

  useEffect(() => {
    if (selectedChild && selectedTeacher) {
      setupMessageListener();
    }
  }, [selectedChild, selectedTeacher]);

  const fetchChildren = async () => {
    try {
      const childrenRef = collection(db, 'users');
      const q = query(
        childrenRef,
        where('role', '==', 'student'),
        where('parentId', '==', auth.currentUser.uid)
      );
      const querySnapshot = await getDocs(q);

      const childrenData = [];
      querySnapshot.forEach((doc) => {
        childrenData.push({ id: doc.id, ...doc.data() });
      });

      setChildren(childrenData);
      setLoading(false);
    } catch (error) {
      console.error('Error fetching children:', error);
      setLoading(false);
    }
  };

  const fetchTeachers = async () => {
    try {
      const studentRef = collection(db, 'users');
      const student = children.find(child => child.id === selectedChild);

      if (student && student.classId) {
        const teachersRef = collection(db, 'users');
        const q = query(
          teachersRef,
          where('role', '==', 'teacher'),
          where('assignedClasses', 'array-contains', student.classId)
        );
        const querySnapshot = await getDocs(q);

        const teacherData = [];
        querySnapshot.forEach((doc) => {
          teacherData.push({ id: doc.id, ...doc.data() });
        });

        setTeachers(teacherData);
      }
    } catch (error) {
      console.error('Error fetching teachers:', error);
    }
  };

  const setupMessageListener = () => {
    const messagesRef = collection(db, 'messages');
    const q = query(
      messagesRef,
      where('participants', 'array-contains', auth.currentUser.uid),
      orderBy('timestamp', 'desc')
    );

    const unsubscribe = onSnapshot(q, (snapshot) => {
      const messageData = [];
      snapshot.forEach((doc) => {
        const message = doc.data();
        if (message.studentId === selectedChild &&
            (message.senderId === selectedTeacher || message.receiverId === selectedTeacher)) {
          messageData.push({ id: doc.id, ...message });
        }
      });
      setMessages(messageData);
    });

    return unsubscribe;
  };

  const sendMessage = async () => {
    if (!newMessage.trim()) return;

    try {
      const messageData = {
        senderId: auth.currentUser.uid,
        senderRole: 'parent',
        receiverId: selectedTeacher,
        receiverRole: 'teacher',
        studentId: selectedChild,
        content: newMessage,
        timestamp: new Date().toISOString(),
        participants: [auth.currentUser.uid, selectedTeacher],
      };

      await addDoc(collection(db, 'messages'), messageData);
      setNewMessage('');
    } catch (error) {
      console.error('Error sending message:', error);
    }
  };

  const requestMeeting = async () => {
    try {
      const meetingData = {
        parentId: auth.currentUser.uid,
        teacherId: selectedTeacher,
        studentId: selectedChild,
        date: meetingRequest.date,
        time: meetingRequest.time,
        reason: meetingRequest.reason,
        status: 'pending',
        timestamp: new Date().toISOString(),
      };

      await addDoc(collection(db, 'meetingRequests'), meetingData);
      setModalVisible(false);
      setMeetingRequest({ date: '', time: '', reason: '' });
    } catch (error) {
      console.error('Error requesting meeting:', error);
    }
  };

  const formatTimestamp = (timestamp) => {
    const date = new Date(timestamp);
    return EthiopianCalendar.formatDate(date) + ' ' +
           date.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' });
  };

  return (
    <ScrollView style={styles.container}>
      <Card style={styles.card}>
        <Card.Content>
          <Title>Parent-Teacher Communication</Title>

          <View style={styles.pickerContainer}>
            <Text style={styles.pickerLabel}>Select Child</Text>
            <Picker
              selectedValue={selectedChild}
              onValueChange={(value) => setSelectedChild(value)}
              style={styles.picker}
            >
              <Picker.Item label="Select a child" value="" />
              {children.map((child) => (
                <Picker.Item
                  key={child.id}
                  label={child.displayName}
                  value={child.id}
                />
              ))}
            </Picker>
          </View>

          <View style={styles.pickerContainer}>
            <Text style={styles.pickerLabel}>Select Teacher</Text>
            <Picker
              selectedValue={selectedTeacher}
              onValueChange={(value) => setSelectedTeacher(value)}
              style={styles.picker}
            >
              <Picker.Item label="Select a teacher" value="" />
              {teachers.map((teacher) => (
                <Picker.Item
                  key={teacher.id}
                  label={teacher.displayName}
                  value={teacher.id}
                />
              ))}
            </Picker>
          </View>

          {loading ? (
            <ActivityIndicator style={styles.loader} />
          ) : (
            <View>
              <View style={styles.messageContainer}>
                {messages.map((message) => (
                  <View
                    key={message.id}
                    style={[
                      styles.messageBox,
                      message.senderId === auth.currentUser.uid
                        ? styles.sentMessage
                        : styles.receivedMessage
                    ]}
                  >
                    <Text style={styles.messageText}>{message.content}</Text>
                    <Text style={styles.timestamp}>
                      {formatTimestamp(message.timestamp)}
                    </Text>
                  </View>
                ))}
              </View>

              <View style={styles.inputContainer}>
                <TextInput
                  value={newMessage}
                  onChangeText={setNewMessage}
                  placeholder="Type your message..."
                  style={styles.input}
                  multiline
                />
                <CustomButton
                  mode="contained"
                  onPress={sendMessage}
                  style={styles.sendButton}
                >
                  Send
                </CustomButton>
              </View>

              <CustomButton
                mode="contained"
                onPress={() => setModalVisible(true)}
                style={styles.meetingButton}
              >
                Request Meeting
              </CustomButton>
            </View>
          )}
        </Card.Content>
      </Card>

      <Portal>
        <Modal
          visible={modalVisible}
          onDismiss={() => setModalVisible(false)}
          contentContainerStyle={styles.modalContent}
        >
          <ScrollView>
            <Title>Request Meeting</Title>

            <View style={styles.datePickerContainer}>
              <Text style={styles.datePickerLabel}>Date</Text>
              <EthiopianDatePicker
                value={meetingRequest.date ? new Date(meetingRequest.date) : new Date()}
                onChange={(date) => {
                  if (date) {
                    setMeetingRequest({ ...meetingRequest, date: date.toISOString().split('T')[0] });
                  }
                }}
                label="Select Meeting Date"
                language={language}
                display="default"
                themeType="light"
                buttonMode="outlined"
                showIcon={true}
                iconPosition="left"
              />
            </View>

            <View style={styles.datePickerContainer}>
              <Text style={styles.datePickerLabel}>Time</Text>
              <EthiopianTimePicker
                value={meetingRequest.time ? new Date(`2023-01-01T${meetingRequest.time}:00`) : new Date()}
                onChange={(time) => {
                  if (time) {
                    const hours = time.getHours().toString().padStart(2, '0');
                    const minutes = time.getMinutes().toString().padStart(2, '0');
                    setMeetingRequest({ ...meetingRequest, time: `${hours}:${minutes}` });
                  }
                }}
                label="Select Meeting Time"
                display="compact"
                is24Hour={false}
                themeType="light"
                minuteInterval={5}
                buttonMode="contained"
              />
            </View>

            <TextInput
              label="Reason for Meeting"
              value={meetingRequest.reason}
              onChangeText={(text) =>
                setMeetingRequest({ ...meetingRequest, reason: text })
              }
              multiline
              numberOfLines={3}
              style={styles.input}
            />

            <View style={styles.buttonContainer}>
              <CustomButton
                mode="outlined"
                onPress={() => setModalVisible(false)}
                style={[styles.button, styles.cancelButton]}
              >
                Cancel
              </CustomButton>
              <CustomButton
                mode="contained"
                onPress={requestMeeting}
                style={[styles.button, styles.submitButton]}
              >
                Submit Request
              </CustomButton>
            </View>
          </ScrollView>
        </Modal>
      </Portal>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  card: {
    margin: 16,
  },
  pickerContainer: {
    marginBottom: 16,
  },
  pickerLabel: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
  },
  datePickerContainer: {
    marginBottom: 16,
  },
  datePickerLabel: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
  },
  picker: {
    backgroundColor: '#f5f5f5',
    borderRadius: 4,
  },
  loader: {
    marginVertical: 20,
  },
  messageContainer: {
    marginVertical: 16,
  },
  messageBox: {
    padding: 12,
    borderRadius: 8,
    marginVertical: 4,
    maxWidth: '80%',
  },
  sentMessage: {
    backgroundColor: '#DCF8C6',
    alignSelf: 'flex-end',
  },
  receivedMessage: {
    backgroundColor: '#E8E8E8',
    alignSelf: 'flex-start',
  },
  messageText: {
    fontSize: 16,
  },
  timestamp: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 16,
  },
  input: {
    flex: 1,
    marginRight: 8,
  },
  sendButton: {
    minWidth: 100,
  },
  meetingButton: {
    marginTop: 16,
  },
  modalContent: {
    backgroundColor: 'white',
    padding: 20,
    margin: 20,
    borderRadius: 8,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: 8,
    marginTop: 16,
  },
  button: {
    minWidth: 100,
  },
  cancelButton: {
    borderColor: '#666',
  },
  submitButton: {
    backgroundColor: '#2196F3',
  },
});

export default ParentTeacherCommunication;
