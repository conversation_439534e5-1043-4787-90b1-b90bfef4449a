@echo off
echo ===== School Management System - Restart =====

echo Step 1: Stopping any running Metro servers...
taskkill /f /im node.exe >nul 2>&1

echo Step 2: Cleaning temporary files...
if exist node_modules\.cache rmdir /s /q node_modules\.cache
if exist .expo rmdir /s /q .expo
if exist android\app\build\intermediates\incremental rmdir /s /q android\app\build\intermediates\incremental

echo Step 3: Clearing watchman watches...
watchman watch-del-all 2>nul

echo Step 4: Clearing Metro bundler cache...
if exist node_modules\.cache\metro rmdir /s /q node_modules\.cache\metro

echo Step 5: Restarting Metro server...
start cmd /k "npm start -- --reset-cache"

echo ===== Restart Complete =====
echo The Metro server should be starting in a new window.
echo If the app doesn't connect automatically, run "npm run android" in a new terminal.
