import { db } from '../config/firebase';
import { collection, addDoc, updateDoc, deleteDoc, getDocs, query, where, doc } from 'firebase/firestore';
import { EthiopianCalendar } from '../utils/EthiopianCalendar';

class LibraryManagementService {
    static instance = null;
    
    constructor() {
        if (LibraryManagementService.instance) {
            return LibraryManagementService.instance;
        }
        LibraryManagementService.instance = this;

        this.bookStatus = {
            AVAILABLE: 'available',
            BORROWED: 'borrowed',
            RESERVED: 'reserved',
            MAINTENANCE: 'maintenance',
            LOST: 'lost'
        };

        this.borrowDuration = 14; // Default loan period in days
        this.maxBooksPerStudent = 3;
        this.maxBooksPerTeacher = 5;
    }

    // Add new book
    async addBook(bookData) {
        try {
            const timestamp = Date.now();
            const ethiopianDate = EthiopianCalendar.toEthiopian(new Date());

            const book = {
                ...bookData,
                status: this.bookStatus.AVAILABLE,
                addedOn: timestamp,
                addedOnEthiopian: ethiopianDate,
                borrowHistory: [],
                maintenanceHistory: [],
                copies: bookData.copies || 1,
                availableCopies: bookData.copies || 1,
                location: bookData.location || 'main'
            };

            const docRef = await addDoc(collection(db, 'library_books'), book);
            
            // Add to catalog
            await this.updateCatalog(book);

            return docRef.id;
        } catch (error) {
            console.error('Failed to add book:', error);
            throw error;
        }
    }

    // Borrow book
    async borrowBook(bookId, userId, userRole) {
        try {
            const book = await this.getBook(bookId);
            const user = await this.getUser(userId);

            // Check if user can borrow more books
            const currentBorrowings = await this.getUserBorrowings(userId);
            const maxBooks = userRole === 'teacher' ? this.maxBooksPerTeacher : this.maxBooksPerStudent;

            if (currentBorrowings.length >= maxBooks) {
                throw new Error('Maximum borrowing limit reached');
            }

            if (book.availableCopies < 1) {
                throw new Error('No copies available');
            }

            const borrowing = {
                userId,
                userName: user.name,
                borrowDate: Date.now(),
                dueDate: Date.now() + (this.borrowDuration * 24 * 60 * 60 * 1000),
                returnDate: null,
                status: 'active'
            };

            await updateDoc(doc(db, 'library_books', bookId), {
                borrowHistory: arrayUnion(borrowing),
                availableCopies: book.availableCopies - 1,
                status: book.availableCopies === 1 ? this.bookStatus.BORROWED : book.status
            });

            // Add to user's borrowings
            await this.addToBorrowings(userId, bookId, borrowing);

            return borrowing;
        } catch (error) {
            console.error('Failed to borrow book:', error);
            throw error;
        }
    }

    // Return book
    async returnBook(bookId, userId) {
        try {
            const book = await this.getBook(bookId);
            const borrowing = await this.getBorrowing(bookId, userId);

            if (!borrowing) {
                throw new Error('No active borrowing found');
            }

            // Update borrowing record
            borrowing.returnDate = Date.now();
            borrowing.status = 'returned';

            await updateDoc(doc(db, 'library_books', bookId), {
                availableCopies: book.availableCopies + 1,
                status: book.availableCopies + 1 === book.copies ? this.bookStatus.AVAILABLE : book.status,
                'borrowHistory': book.borrowHistory.map(b => 
                    b.userId === userId && b.status === 'active' ? borrowing : b
                )
            });

            // Update user's borrowings
            await this.updateBorrowing(userId, bookId, borrowing);

            return borrowing;
        } catch (error) {
            console.error('Failed to return book:', error);
            throw error;
        }
    }

    // Reserve book
    async reserveBook(bookId, userId) {
        try {
            const book = await this.getBook(bookId);

            if (book.status !== this.bookStatus.BORROWED) {
                throw new Error('Book is available for direct borrowing');
            }

            const reservation = {
                userId,
                reservationDate: Date.now(),
                status: 'active'
            };

            await updateDoc(doc(db, 'library_books', bookId), {
                reservations: arrayUnion(reservation)
            });

            return reservation;
        } catch (error) {
            console.error('Failed to reserve book:', error);
            throw error;
        }
    }

    // Search books
    async searchBooks(filters = {}) {
        try {
            let queryRef = collection(db, 'library_books');

            // Apply filters
            Object.entries(filters).forEach(([field, value]) => {
                queryRef = query(queryRef, where(field, '==', value));
            });

            const snapshot = await getDocs(queryRef);
            return snapshot.docs.map(doc => ({
                id: doc.id,
                ...doc.data()
            }));
        } catch (error) {
            console.error('Failed to search books:', error);
            throw error;
        }
    }

    // Get overdue books
    async getOverdueBooks() {
        try {
            const now = Date.now();
            const books = await this.searchBooks();

            return books.reduce((overdue, book) => {
                const overdueBookings = book.borrowHistory
                    .filter(b => b.status === 'active' && b.dueDate < now);

                if (overdueBookings.length > 0) {
                    overdue.push({
                        bookId: book.id,
                        title: book.title,
                        overdueBookings
                    });
                }

                return overdue;
            }, []);
        } catch (error) {
            console.error('Failed to get overdue books:', error);
            throw error;
        }
    }

    // Generate library reports
    async generateLibraryReport(type = 'general') {
        try {
            const books = await this.searchBooks();
            let report;

            switch (type) {
                case 'circulation':
                    report = this.generateCirculationReport(books);
                    break;
                case 'inventory':
                    report = this.generateInventoryReport(books);
                    break;
                case 'overdue':
                    report = await this.getOverdueBooks();
                    break;
                default:
                    report = this.generateGeneralReport(books);
            }

            return report;
        } catch (error) {
            console.error('Failed to generate library report:', error);
            throw error;
        }
    }

    // Generate circulation report
    generateCirculationReport(books) {
        const now = Date.now();
        const thirtyDaysAgo = now - (30 * 24 * 60 * 60 * 1000);

        return {
            totalBorrowings: books.reduce((total, book) => 
                total + book.borrowHistory.filter(b => b.borrowDate > thirtyDaysAgo).length, 0),
            activeLoans: books.reduce((total, book) => 
                total + book.borrowHistory.filter(b => b.status === 'active').length, 0),
            overdueLoans: books.reduce((total, book) => 
                total + book.borrowHistory.filter(b => b.status === 'active' && b.dueDate < now).length, 0),
            mostBorrowedBooks: this.getMostBorrowedBooks(books),
            borrowingsByUserType: this.getBorrowingsByUserType(books)
        };
    }

    // Generate inventory report
    generateInventoryReport(books) {
        return {
            totalBooks: books.length,
            totalCopies: books.reduce((total, book) => total + book.copies, 0),
            availableCopies: books.reduce((total, book) => total + book.availableCopies, 0),
            booksByStatus: this.groupBooksByStatus(books),
            booksByCategory: this.groupBooksByCategory(books),
            booksByLanguage: this.groupBooksByLanguage(books)
        };
    }

    // Generate general report
    generateGeneralReport(books) {
        return {
            totalBooks: books.length,
            totalCopies: books.reduce((total, book) => total + book.copies, 0),
            availableCopies: books.reduce((total, book) => total + book.availableCopies, 0),
            activeLoans: books.reduce((total, book) => 
                total + book.borrowHistory.filter(b => b.status === 'active').length, 0),
            popularCategories: this.getPopularCategories(books),
            recentAcquisitions: this.getRecentAcquisitions(books)
        };
    }

    // Get most borrowed books
    getMostBorrowedBooks(books) {
        return books
            .map(book => ({
                id: book.id,
                title: book.title,
                borrowCount: book.borrowHistory.length
            }))
            .sort((a, b) => b.borrowCount - a.borrowCount)
            .slice(0, 10);
    }

    // Group books by various criteria
    groupBooksByStatus(books) {
        return books.reduce((groups, book) => {
            const status = book.status;
            if (!groups[status]) {
                groups[status] = 0;
            }
            groups[status] += book.copies;
            return groups;
        }, {});
    }

    groupBooksByCategory(books) {
        return books.reduce((groups, book) => {
            const category = book.category;
            if (!groups[category]) {
                groups[category] = 0;
            }
            groups[category] += book.copies;
            return groups;
        }, {});
    }

    groupBooksByLanguage(books) {
        return books.reduce((groups, book) => {
            const language = book.language;
            if (!groups[language]) {
                groups[language] = 0;
            }
            groups[language] += book.copies;
            return groups;
        }, {});
    }

    // Update catalog
    async updateCatalog(book) {
        try {
            const catalogRef = doc(db, 'library_catalog', book.category);
            const catalogDoc = await getDocs(catalogRef);

            if (catalogDoc.exists()) {
                await updateDoc(catalogRef, {
                    books: arrayUnion(book.id),
                    count: catalogDoc.data().count + 1
                });
            } else {
                await addDoc(collection(db, 'library_catalog'), {
                    category: book.category,
                    books: [book.id],
                    count: 1
                });
            }
        } catch (error) {
            console.error('Failed to update catalog:', error);
            throw error;
        }
    }
}

export default new LibraryManagementService();
