import React, { useEffect, useRef } from 'react';
import { db } from '../../config/firebase';
import { collection, query, where, onSnapshot, doc, updateDoc, Timestamp, getDocs } from 'firebase/firestore';
import { useNavigation } from '@react-navigation/native';
import { useNotifications } from '../../context/NotificationContext';
import { useAuth } from '../../context/AuthContext';

// Helper functions outside the component to avoid re-creation on each render
const getDefaultTitle = (type) => {
  switch (type) {
    case 'grade_published': return 'Grades Published';
    case 'grade_approval': return 'Grades Approved';
    case 'grade_rejection': return 'Grades Rejected';
    case 'grade_submission': return 'New Grade Submission';
    default: return 'Grade Notification';
  }
};

const getDefaultBody = (type, userRole) => {
  switch (type) {
    case 'grade_published':
      return userRole === 'student' ?
        'Your grades have been published. Tap to view.' :
        'Grades have been published for your child. Tap to view.';
    case 'grade_approval': return 'Your grade submission has been approved.';
    case 'grade_rejection': return 'Your grade submission has been rejected. Please review and resubmit.';
    case 'grade_submission': return 'A teacher has submitted grades for approval.';
    default: return 'You have a new grade notification.';
  }
};

const getNotificationChannel = (type) => {
  switch (type) {
    case 'grade_published': return 'grade';
    case 'grade_approval': return 'grade_approval';
    case 'grade_rejection': return 'grade_approval';
    case 'grade_submission': return 'grade_submission';
    default: return 'academic';
  }
};

const shouldNavigateToNotification = (notification) => {
  // Only navigate for notifications less than 1 hour old
  const notificationTime = notification.timestamp ?
    (notification.timestamp instanceof Timestamp ?
      notification.timestamp.toDate() :
      new Date(notification.timestamp)) :
    new Date();

  const now = new Date();
  const minutesDiff = (now - notificationTime) / (1000 * 60);

  return minutesDiff < 60; // Only navigate if less than 1 hour old
};

// Use a module-level variable to track if we've already set up listeners
// This prevents setting up multiple listeners across re-renders
let isListenerActive = false;

const GradeNotificationHandler = () => {
  const navigation = useNavigation();
  const { sendLocalNotification } = useNotifications();
  const { user } = useAuth();

  // Use refs to store mutable values without causing re-renders
  const navigationRef = useRef(navigation);
  const sendLocalNotificationRef = useRef(sendLocalNotification);
  const userRef = useRef(user);

  // Update refs when dependencies change
  useEffect(() => {
    navigationRef.current = navigation;
    sendLocalNotificationRef.current = sendLocalNotification;
    userRef.current = user;
  }, [navigation, sendLocalNotification, user]);

  // Set up notification listener once
  useEffect(() => {
    // Skip if no user or already listening
    if (!user || isListenerActive) return;

    const userId = user.uid;
    const userRole = user.role || 'student';

    console.log('GradeNotificationHandler initialized for user:', userId, 'with role:', userRole);

    // Mark as active before setting up listener
    isListenerActive = true;

    // Create query for unread grade notifications
    const notificationsRef = collection(db, 'notifications');
    const q = query(
      notificationsRef,
      where('userId', '==', userId),
      where('read', '==', false),
      where('type', 'in', ['grade_published', 'grade_approval', 'grade_rejection', 'grade_submission'])
    );

    // Function to handle a notification
    const handleNotification = async (notification) => {
      try {
        if (!notification || !notification.id) return;

        // Get current refs
        const nav = navigationRef.current;
        const sendNotification = sendLocalNotificationRef.current;
        const currentUser = userRef.current;
        const currentUserRole = currentUser?.role || userRole;

        // Prepare notification data
        const title = notification.title || getDefaultTitle(notification.type);
        const body = notification.body || getDefaultBody(notification.type, currentUserRole);
        const data = notification.data || {};
        const channel = getNotificationChannel(notification.type);

        // Send local notification
        await sendNotification({
          title,
          body,
          data: { ...data, type: notification.type },
          type: channel
        });

        console.log(`Local notification sent: ${title}`);

        // Mark as read in database
        try {
          await updateDoc(doc(db, 'notifications', notification.id), {
            read: true,
            readAt: new Date().toISOString()
          });
        } catch (err) {
          console.error('Error marking notification as read:', err);
        }

        // Navigate if needed
        if (!nav || !shouldNavigateToNotification(notification)) return;

        // Handle navigation based on notification data
        setTimeout(() => {
          try {
            if (notification.data?.viewPath) {
              nav.navigate(notification.data.viewPath, notification.data.viewParams || {});
              return;
            }

            // Default navigation based on type
            switch (notification.type) {
              case 'grade_published':
                if (currentUserRole === 'student') {
                  nav.navigate('StudentGrades');
                } else if (currentUserRole === 'parent') {
                  nav.navigate('ParentChildGrades', {
                    childId: notification.data?.studentId
                  });
                }
                break;

              case 'grade_approval':
                if (currentUserRole === 'teacher') {
                  nav.navigate('GradeManagement', {
                    classId: notification.data?.classId,
                    className: notification.data?.className,
                    sectionName: notification.data?.sectionName,
                    subject: notification.data?.subject,
                    viewMode: 'results'
                  });
                }
                break;

              case 'grade_rejection':
                if (currentUserRole === 'teacher') {
                  nav.navigate('GradeManagement', {
                    classId: notification.data?.classId,
                    className: notification.data?.className,
                    sectionName: notification.data?.sectionName,
                    subject: notification.data?.subject,
                    viewMode: 'submission'
                  });
                }
                break;

              case 'grade_submission':
                if (currentUserRole === 'admin') {
                  nav.navigate('AdminGradeApproval', {
                    submissionId: notification.data?.submissionId
                  });
                }
                break;
            }
          } catch (navError) {
            console.error('Navigation error:', navError);
          }
        }, 500);
      } catch (error) {
        console.error('Error handling notification:', error);
      }
    };

    // Set up listener for new notifications
    const unsubscribe = onSnapshot(q, (snapshot) => {
      snapshot.docChanges().forEach((change) => {
        if (change.type === 'added') {
          const notification = {
            id: change.doc.id,
            ...change.doc.data()
          };
          console.log('New grade notification received:', notification.type);
          handleNotification(notification);
        }
      });
    }, (error) => {
      console.error('Error listening to grade notifications:', error);
    });

    // Check for existing notifications once
    const checkExistingNotifications = async () => {
      try {
        const snapshot = await getDocs(q);
        if (snapshot.empty) return;

        console.log(`Found ${snapshot.size} existing unread grade notifications`);

        // Process notifications one by one with delay
        let index = 0;
        const notifications = [];

        snapshot.forEach(doc => {
          const notification = { id: doc.id, ...doc.data() };

          // Filter out old notifications (older than 24 hours)
          const notificationTime = notification.timestamp ?
            (notification.timestamp instanceof Timestamp ?
              notification.timestamp.toDate() :
              new Date(notification.timestamp)) :
            new Date();

          const now = new Date();
          const hoursDiff = (now - notificationTime) / (1000 * 60 * 60);

          if (hoursDiff < 24) {
            notifications.push(notification);
          } else {
            // Mark old notifications as read
            updateDoc(doc.ref, { read: true })
              .catch(err => console.error('Error marking old notification as read:', err));
          }
        });

        // Process notifications with delay
        if (notifications.length > 0) {
          const processNext = () => {
            if (index < notifications.length) {
              handleNotification(notifications[index]);
              index++;
              setTimeout(processNext, 1000);
            }
          };
          setTimeout(processNext, 2000); // Start after a delay
        }
      } catch (error) {
        console.error('Error checking existing notifications:', error);
      }
    };

    // Check existing notifications after a delay
    const timeoutId = setTimeout(checkExistingNotifications, 3000);

    // Cleanup function
    return () => {
      clearTimeout(timeoutId);
      unsubscribe();
      // Don't set isListenerActive to false here to prevent setting up
      // multiple listeners if the component remounts
    };
  }, [user?.uid]); // Only depend on user ID

  // This component doesn't render anything
  return null;
};

export default React.memo(GradeNotificationHandler);
