import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_messaging/firebase_messaging.dart';

class NotificationModel {
  final String id;
  final String title;
  final String body;
  final String type;
  final String senderId;
  final String recipientId;
  final bool isRead;
  final DateTime createdAt;
  final Map<String, dynamic> data;
  final String? imageUrl;
  final String? actionUrl;
  final NotificationPriority priority;

  NotificationModel({
    required this.id,
    required this.title,
    required this.body,
    required this.type,
    required this.senderId,
    required this.recipientId,
    required this.isRead,
    required this.createdAt,
    this.data = const {},
    this.imageUrl,
    this.actionUrl,
    this.priority = NotificationPriority.normal,
  });

  factory NotificationModel.fromMap(Map<String, dynamic> map) {
    return NotificationModel(
      id: map['id'] ?? '',
      title: map['title'] ?? '',
      body: map['body'] ?? '',
      type: map['type'] ?? 'general',
      senderId: map['senderId'] ?? '',
      recipientId: map['recipientId'] ?? '',
      isRead: map['isRead'] ?? false,
      createdAt: (map['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      data: Map<String, dynamic>.from(map['data'] ?? {}),
      imageUrl: map['imageUrl'],
      actionUrl: map['actionUrl'],
      priority: NotificationPriority.values.firstWhere(
        (p) => p.name == map['priority'],
        orElse: () => NotificationPriority.normal,
      ),
    );
  }

  factory NotificationModel.fromFirebaseMessage(RemoteMessage message) {
    return NotificationModel(
      id: message.messageId ?? DateTime.now().millisecondsSinceEpoch.toString(),
      title: message.notification?.title ?? '',
      body: message.notification?.body ?? '',
      type: message.data['type'] ?? 'general',
      senderId: message.data['senderId'] ?? '',
      recipientId: message.data['recipientId'] ?? '',
      isRead: false,
      createdAt: DateTime.now(),
      data: message.data,
      imageUrl: message.notification?.android?.imageUrl,
      actionUrl: message.data['actionUrl'],
      priority: NotificationPriority.values.firstWhere(
        (p) => p.name == message.data['priority'],
        orElse: () => NotificationPriority.normal,
      ),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'body': body,
      'type': type,
      'senderId': senderId,
      'recipientId': recipientId,
      'isRead': isRead,
      'createdAt': Timestamp.fromDate(createdAt),
      'data': data,
      'imageUrl': imageUrl,
      'actionUrl': actionUrl,
      'priority': priority.name,
    };
  }

  NotificationModel copyWith({
    String? id,
    String? title,
    String? body,
    String? type,
    String? senderId,
    String? recipientId,
    bool? isRead,
    DateTime? createdAt,
    Map<String, dynamic>? data,
    String? imageUrl,
    String? actionUrl,
    NotificationPriority? priority,
  }) {
    return NotificationModel(
      id: id ?? this.id,
      title: title ?? this.title,
      body: body ?? this.body,
      type: type ?? this.type,
      senderId: senderId ?? this.senderId,
      recipientId: recipientId ?? this.recipientId,
      isRead: isRead ?? this.isRead,
      createdAt: createdAt ?? this.createdAt,
      data: data ?? this.data,
      imageUrl: imageUrl ?? this.imageUrl,
      actionUrl: actionUrl ?? this.actionUrl,
      priority: priority ?? this.priority,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is NotificationModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'NotificationModel(id: $id, title: $title, type: $type, isRead: $isRead)';
  }
}

enum NotificationPriority {
  low,
  normal,
  high,
  urgent;

  String get displayName {
    switch (this) {
      case NotificationPriority.low:
        return 'Low';
      case NotificationPriority.normal:
        return 'Normal';
      case NotificationPriority.high:
        return 'High';
      case NotificationPriority.urgent:
        return 'Urgent';
    }
  }

  int get value {
    switch (this) {
      case NotificationPriority.low:
        return 1;
      case NotificationPriority.normal:
        return 2;
      case NotificationPriority.high:
        return 3;
      case NotificationPriority.urgent:
        return 4;
    }
  }
}

enum NotificationType {
  general,
  attendance,
  grades,
  assignment,
  exam,
  announcement,
  message,
  event,
  library,
  fee,
  disciplinary;

  String get displayName {
    switch (this) {
      case NotificationType.general:
        return 'General';
      case NotificationType.attendance:
        return 'Attendance';
      case NotificationType.grades:
        return 'Grades';
      case NotificationType.assignment:
        return 'Assignment';
      case NotificationType.exam:
        return 'Exam';
      case NotificationType.announcement:
        return 'Announcement';
      case NotificationType.message:
        return 'Message';
      case NotificationType.event:
        return 'Event';
      case NotificationType.library:
        return 'Library';
      case NotificationType.fee:
        return 'Fee';
      case NotificationType.disciplinary:
        return 'Disciplinary';
    }
  }

  String get iconName {
    switch (this) {
      case NotificationType.general:
        return 'notifications';
      case NotificationType.attendance:
        return 'check_circle';
      case NotificationType.grades:
        return 'grade';
      case NotificationType.assignment:
        return 'assignment';
      case NotificationType.exam:
        return 'quiz';
      case NotificationType.announcement:
        return 'campaign';
      case NotificationType.message:
        return 'message';
      case NotificationType.event:
        return 'event';
      case NotificationType.library:
        return 'library_books';
      case NotificationType.fee:
        return 'payment';
      case NotificationType.disciplinary:
        return 'warning';
    }
  }
}
