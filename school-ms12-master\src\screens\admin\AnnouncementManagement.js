import React, { useState, useEffect, useRef } from 'react';
import { View, ScrollView, StyleSheet, Text, Animated, TouchableOpacity, Alert } from 'react-native';
import { Card, Title, FAB, Portal, Modal, DataTable, Searchbar, List, Chip, useTheme, IconButton, Badge, Divider, Menu, Avatar, Button, Paragraph, Surface, ProgressBar, HelperText, Snackbar, TextInput } from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import * as Animatable from 'react-native-animatable';
import { LinearGradient } from 'expo-linear-gradient';
import { db } from '../../config/firebase';
import { collection, addDoc, query, getDocs, doc, updateDoc, deleteDoc, where } from 'firebase/firestore';
// CustomButton replaced with Button from react-native-paper
import CustomInput from '../../components/common/CustomInput';
import { useLanguage } from '../../context/LanguageContext';
import EthiopianDatePicker from '../../components/common/EthiopianDatePicker';
import AdminAppHeader from '../../components/common/AdminAppHeader';
import AdminSidebar from '../../components/common/AdminSidebar';
import SidebarBackdrop from '../../components/common/SidebarBackdrop';
import { useNavigation } from '@react-navigation/native';

const AnnouncementManagement = () => {
  const { translate: t } = useLanguage();
  // No theme needed
  const navigation = useNavigation();
  const [announcements, setAnnouncements] = useState([]);
  const [visible, setVisible] = useState(false);
  const [selectedAnnouncement, setSelectedAnnouncement] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(false);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [dateType, setDateType] = useState('start');
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [menuVisible, setMenuVisible] = useState(false);
  const [selectedAnnouncementId, setSelectedAnnouncementId] = useState(null);
  const [confirmDelete, setConfirmDelete] = useState(false);
  const [viewMode, setViewMode] = useState('table'); // 'table' or 'card'
  const [formErrors, setFormErrors] = useState({});

  // Sidebar state
  const [drawerOpen, setDrawerOpen] = useState(false);
  const drawerAnim = useRef(new Animated.Value(-300)).current;
  const backdropFadeAnim = useRef(new Animated.Value(0)).current;
  const [activeSidebarItem, setActiveSidebarItem] = useState('AnnouncementManagement');

  const [formData, setFormData] = useState({
    title: '',
    content: '',
    type: 'general',
    priority: 'normal',
    targetAudience: [],
    startDate: new Date(),
    endDate: new Date(new Date().setDate(new Date().getDate() + 7)),
    status: 'draft',
  });

  useEffect(() => {
    fetchAnnouncements();

    // Hide the default header
    navigation.setOptions({
      headerShown: false,
    });
  }, [navigation]);

  const toggleDrawer = () => {
    if (drawerOpen) {
      // Close drawer
      Animated.parallel([
        Animated.timing(drawerAnim, {
          toValue: -300,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(backdropFadeAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        })
      ]).start(() => setDrawerOpen(false));
    } else {
      // Open drawer
      setDrawerOpen(true);
      Animated.parallel([
        Animated.timing(drawerAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(backdropFadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        })
      ]).start();
    }
  };

  const fetchAnnouncements = async () => {
    try {
      const announcementsRef = collection(db, 'announcements');
      const querySnapshot = await getDocs(announcementsRef);

      const announcementsData = [];
      querySnapshot.forEach((doc) => {
        announcementsData.push({ id: doc.id, ...doc.data() });
      });

      setAnnouncements(announcementsData);
    } catch (error) {
      console.error('Error fetching announcements:', error);
    }
  };

  const validateForm = () => {
    const errors = {};

    if (!formData.title.trim()) {
      errors.title = t('validation.required');
    }

    if (!formData.content.trim()) {
      errors.content = t('validation.required');
    }

    if (formData.targetAudience.length === 0) {
      errors.targetAudience = t('validation.selectAtLeastOne');
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleAddAnnouncement = async () => {
    if (!validateForm()) {
      showSnackbar(t('validation.pleaseFixErrors'));
      return;
    }

    try {
      setLoading(true);
      const announcementsRef = collection(db, 'announcements');
      await addDoc(announcementsRef, {
        ...formData,
        startDate: formData.startDate.toISOString(),
        endDate: formData.endDate.toISOString(),
        createdAt: new Date().toISOString(),
        createdBy: 'admin',
      });

      setVisible(false);
      resetForm();
      fetchAnnouncements();
      showSnackbar(t('announcements.addSuccess'));
    } catch (error) {
      console.error('Error adding announcement:', error);
      showSnackbar(t('announcements.addError'));
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateAnnouncement = async () => {
    if (!validateForm()) {
      showSnackbar(t('validation.pleaseFixErrors'));
      return;
    }

    try {
      setLoading(true);
      const announcementRef = doc(db, 'announcements', selectedAnnouncement.id);
      await updateDoc(announcementRef, {
        ...formData,
        startDate: formData.startDate.toISOString(),
        endDate: formData.endDate.toISOString(),
        updatedAt: new Date().toISOString(),
      });

      setVisible(false);
      resetForm();
      fetchAnnouncements();
      showSnackbar(t('announcements.updateSuccess'));
    } catch (error) {
      console.error('Error updating announcement:', error);
      showSnackbar(t('announcements.updateError'));
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteAnnouncement = async (announcementId) => {
    try {
      setLoading(true);
      await deleteDoc(doc(db, 'announcements', announcementId));
      fetchAnnouncements();
      showSnackbar(t('announcements.deleteSuccess'));
      setConfirmDelete(false);
    } catch (error) {
      console.error('Error deleting announcement:', error);
      showSnackbar(t('announcements.deleteError'));
    } finally {
      setLoading(false);
    }
  };

  const showSnackbar = (message) => {
    setSnackbarMessage(message);
    setSnackbarVisible(true);
  };

  const confirmDeleteAnnouncement = (id) => {
    setSelectedAnnouncementId(id);
    setConfirmDelete(true);
  };

  const resetForm = () => {
    setFormData({
      title: '',
      content: '',
      type: 'general',
      priority: 'normal',
      targetAudience: [],
      startDate: new Date(),
      endDate: new Date(new Date().setDate(new Date().getDate() + 7)),
      status: 'draft',
    });
    setSelectedAnnouncement(null);
  };

  const handleDateChange = (selectedDate, type) => {
    setFormData({
      ...formData,
      [type]: selectedDate,
    });
  };

  const toggleAudience = (audience) => {
    const audiences = formData.targetAudience.includes(audience)
      ? formData.targetAudience.filter(a => a !== audience)
      : [...formData.targetAudience, audience];
    setFormData({ ...formData, targetAudience: audiences });
  };

  const filteredAnnouncements = announcements.filter(announcement =>
    announcement.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    announcement.content.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'urgent': return '#FF0000';
      case 'high': return '#FF6B6B';
      case 'normal': return '#4CAF50';
      case 'low': return '#2196F3';
      default: return '#000000';
    }
  };

  return (
    <View style={styles.container}>
      {/* Sidebar */}
      <AdminSidebar
        drawerAnim={drawerAnim}
        activeSidebarItem={activeSidebarItem}
        setActiveSidebarItem={setActiveSidebarItem}
        toggleDrawer={toggleDrawer}
      />

      {/* Backdrop */}
      <SidebarBackdrop
        visible={drawerOpen}
        onPress={toggleDrawer}
        fadeAnim={backdropFadeAnim}
      />

      {/* Admin App Header */}
      <AdminAppHeader
        title={t('announcements.title')}
        onMenuPress={toggleDrawer}
      />

      <View style={styles.content}>
        <Animatable.View animation="fadeIn" duration={800}>
          <Surface style={styles.headerSurface}>
            <LinearGradient
              colors={['#1976d2', '#005cb2']}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
              style={styles.headerGradient}
            >
              <View style={styles.headerContent}>
                <View style={styles.headerTitleContainer}>
                  <MaterialCommunityIcons name="bullhorn" size={28} color="white" style={styles.headerIcon} />
                  <Title style={styles.headerTitle}>{t('announcements.title')}</Title>
                </View>
                <View style={styles.viewToggleContainer}>
                  <IconButton
                    icon="view-list"
                    color={viewMode === 'table' ? 'white' : 'rgba(255,255,255,0.6)'}
                    size={24}
                    onPress={() => setViewMode('table')}
                    style={styles.viewToggleButton}
                  />
                  <IconButton
                    icon="view-grid"
                    color={viewMode === 'card' ? 'white' : 'rgba(255,255,255,0.6)'}
                    size={24}
                    onPress={() => setViewMode('card')}
                    style={styles.viewToggleButton}
                  />
                </View>
              </View>
            </LinearGradient>
          </Surface>

          <View style={styles.searchContainer}>
            <Searchbar
              placeholder={t('announcements.searchPlaceholder')}
              onChangeText={setSearchQuery}
              value={searchQuery}
              style={styles.searchBar}
              icon="magnify"
              clearIcon="close-circle"
            />
          </View>
        </Animatable.View>

        {filteredAnnouncements.length === 0 ? (
          <Animatable.View animation="fadeIn" duration={800} style={styles.emptyContainer}>
            <MaterialCommunityIcons name="clipboard-text-off" size={80} color={'#9e9e9e'} />
            <Title style={styles.emptyText}>{t('announcements.noAnnouncements')}</Title>
            <Paragraph style={styles.emptySubText}>{t('announcements.createFirst')}</Paragraph>
            <Button
              mode="contained"
              onPress={() => {
                resetForm();
                setVisible(true);
              }}
              style={styles.emptyButton}
            >
              {t('announcements.createNew')}
            </Button>
          </Animatable.View>
        ) : viewMode === 'table' ? (
          <ScrollView style={styles.tableContainer}>
            <Animatable.View animation="fadeInUp" duration={800} delay={100}>
              <DataTable style={styles.dataTable}>
                <DataTable.Header style={styles.tableHeader}>
                  <DataTable.Title style={{flex: 2}}>{t('announcements.columns.title')}</DataTable.Title>
                  <DataTable.Title>{t('announcements.columns.type')}</DataTable.Title>
                  <DataTable.Title>{t('announcements.columns.priority')}</DataTable.Title>
                  <DataTable.Title>{t('announcements.columns.status')}</DataTable.Title>
                  <DataTable.Title style={{flex: 0.5}}>{t('announcements.columns.actions')}</DataTable.Title>
                </DataTable.Header>

                {filteredAnnouncements.map((announcement, index) => (
                  <Animatable.View key={announcement.id} animation="fadeIn" duration={400} delay={100 + (index * 50)}>
                    <DataTable.Row
                      onPress={() => {
                        setSelectedAnnouncement(announcement);
                        setFormData({
                          title: announcement.title,
                          content: announcement.content,
                          type: announcement.type,
                          priority: announcement.priority,
                          targetAudience: announcement.targetAudience || [],
                          startDate: new Date(announcement.startDate),
                          endDate: new Date(announcement.endDate),
                          status: announcement.status,
                        });
                        setVisible(true);
                      }}
                      style={styles.tableRow}
                    >
                      <DataTable.Cell style={{flex: 2}}>{announcement.title}</DataTable.Cell>
                      <DataTable.Cell>
                        <Chip
                          mode="outlined"
                          style={[styles.typeChip, {borderColor: '#1976d2'}]}
                        >
                          {t(`announcements.types.${announcement.type}`)}
                        </Chip>
                      </DataTable.Cell>
                      <DataTable.Cell>
                        <Chip
                          mode="outlined"
                          style={[styles.priorityChip, {borderColor: getPriorityColor(announcement.priority)}]}
                          textStyle={{ color: getPriorityColor(announcement.priority) }}
                        >
                          {t(`announcements.priorities.${announcement.priority}`)}
                        </Chip>
                      </DataTable.Cell>
                      <DataTable.Cell>
                        <Chip
                          mode="outlined"
                          style={styles.statusChip}
                        >
                          {t(`announcements.statuses.${announcement.status}`)}
                        </Chip>
                      </DataTable.Cell>
                      <DataTable.Cell style={{flex: 0.5}}>
                        <Menu
                          visible={menuVisible && selectedAnnouncementId === announcement.id}
                          onDismiss={() => setMenuVisible(false)}
                          anchor={
                            <IconButton
                              icon="dots-vertical"
                              onPress={() => {
                                setSelectedAnnouncementId(announcement.id);
                                setMenuVisible(true);
                              }}
                            />
                          }
                        >
                          <Menu.Item
                            onPress={() => {
                              setMenuVisible(false);
                              setSelectedAnnouncement(announcement);
                              setFormData({
                                title: announcement.title,
                                content: announcement.content,
                                type: announcement.type,
                                priority: announcement.priority,
                                targetAudience: announcement.targetAudience || [],
                                startDate: new Date(announcement.startDate),
                                endDate: new Date(announcement.endDate),
                                status: announcement.status,
                              });
                              setVisible(true);
                            }}
                            title={t('actions.edit')}
                            icon="pencil"
                          />
                          <Menu.Item
                            onPress={() => {
                              setMenuVisible(false);
                              confirmDeleteAnnouncement(announcement.id);
                            }}
                            title={t('actions.delete')}
                            icon="delete"
                          />
                        </Menu>
                      </DataTable.Cell>
                    </DataTable.Row>
                  </Animatable.View>
                ))}
              </DataTable>
            </Animatable.View>
          </ScrollView>
        ) : (
          <ScrollView style={styles.cardsContainer}>
            <View style={styles.cardsGrid}>
              {filteredAnnouncements.map((announcement, index) => (
                <Animatable.View
                  key={announcement.id}
                  animation="zoomIn"
                  duration={400}
                  delay={100 + (index * 50)}
                  style={styles.cardWrapper}
                >
                  <Card
                    style={styles.announcementCard}
                    onPress={() => {
                      setSelectedAnnouncement(announcement);
                      setFormData({
                        title: announcement.title,
                        content: announcement.content,
                        type: announcement.type,
                        priority: announcement.priority,
                        targetAudience: announcement.targetAudience || [],
                        startDate: new Date(announcement.startDate),
                        endDate: new Date(announcement.endDate),
                        status: announcement.status,
                      });
                      setVisible(true);
                    }}
                  >
                    <View style={styles.cardHeader}>
                      <Badge
                        style={[styles.priorityBadge, {backgroundColor: getPriorityColor(announcement.priority)}]}
                      >
                        {t(`announcements.priorities.${announcement.priority}`).toUpperCase()}
                      </Badge>
                    </View>
                    <Card.Content>
                      <Title numberOfLines={1} style={styles.cardTitle}>{announcement.title}</Title>
                      <Paragraph numberOfLines={2} style={styles.cardContent}>{announcement.content}</Paragraph>

                      <View style={styles.cardChips}>
                        <Chip
                          mode="outlined"
                          style={styles.cardChip}
                          textStyle={styles.cardChipText}
                        >
                          {t(`announcements.types.${announcement.type}`)}
                        </Chip>
                        <Chip
                          mode="outlined"
                          style={styles.cardChip}
                          textStyle={styles.cardChipText}
                        >
                          {t(`announcements.statuses.${announcement.status}`)}
                        </Chip>
                      </View>
                    </Card.Content>
                    <Card.Actions style={styles.cardActions}>
                      <IconButton
                        icon="pencil"
                        size={20}
                        onPress={() => {
                          setSelectedAnnouncement(announcement);
                          setFormData({
                            title: announcement.title,
                            content: announcement.content,
                            type: announcement.type,
                            priority: announcement.priority,
                            targetAudience: announcement.targetAudience || [],
                            startDate: new Date(announcement.startDate),
                            endDate: new Date(announcement.endDate),
                            status: announcement.status,
                          });
                          setVisible(true);
                        }}
                      />
                      <IconButton
                        icon="delete"
                        size={20}
                        color={'#B00020'}
                        onPress={() => confirmDeleteAnnouncement(announcement.id)}
                      />
                    </Card.Actions>
                  </Card>
                </Animatable.View>
              ))}
            </View>
          </ScrollView>
        )}

      <Portal>
        <Modal
          visible={visible}
          onDismiss={() => {
            setVisible(false);
            resetForm();
          }}
          contentContainerStyle={styles.modal}
        >
          <Animatable.View animation="fadeIn" duration={300} style={styles.modalContainer}>
            <LinearGradient
              colors={['#1976d2', '#005cb2']}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
              style={styles.modalHeader}
            >
              <View style={styles.modalHeaderContent}>
                <MaterialCommunityIcons
                  name={selectedAnnouncement ? "pencil" : "plus-circle"}
                  size={24}
                  color="white"
                  style={styles.modalHeaderIcon}
                />
                <Title style={styles.modalHeaderTitle}>
                  {selectedAnnouncement ? t('announcements.editAnnouncement') : t('announcements.createAnnouncement')}
                </Title>
              </View>
            </LinearGradient>

            <ScrollView style={styles.modalScrollView} contentContainerStyle={styles.modalScrollContent}>
              <Animatable.View animation="fadeInLeft" duration={500} delay={100}>
                <View style={styles.formSection}>
                  <View style={styles.sectionHeader}>
                    <MaterialCommunityIcons name="format-title" size={20} color={'#1976d2'} />
                    <Text style={styles.sectionHeaderText}>{t('announcements.form.basicInfo')}</Text>
                  </View>
                  <Divider style={styles.sectionDivider} />

                  <CustomInput
                    label={t('announcements.form.title')}
                    value={formData.title}
                    onChangeText={(text) => {
                      setFormData({ ...formData, title: text });
                      if (formErrors.title) {
                        setFormErrors({...formErrors, title: null});
                      }
                    }}
                    error={!!formErrors.title}
                    left={<TextInput.Icon icon="format-title" color={'#1976d2'} />}
                    style={styles.formInput}
                  />
                  {formErrors.title && <HelperText type="error">{formErrors.title}</HelperText>}

                  <CustomInput
                    label={t('announcements.form.content')}
                    value={formData.content}
                    onChangeText={(text) => {
                      setFormData({ ...formData, content: text });
                      if (formErrors.content) {
                        setFormErrors({...formErrors, content: null});
                      }
                    }}
                    multiline
                    numberOfLines={4}
                    error={!!formErrors.content}
                    left={<TextInput.Icon icon="text-box-outline" color={'#1976d2'} />}
                    style={styles.formInput}
                  />
                  {formErrors.content && <HelperText type="error">{formErrors.content}</HelperText>}
                </View>
              </Animatable.View>

              <Animatable.View animation="fadeInLeft" duration={500} delay={200}>
                <View style={styles.formSection}>
                  <View style={styles.sectionHeader}>
                    <MaterialCommunityIcons name="calendar-range" size={20} color={'#1976d2'} />
                    <Text style={styles.sectionHeaderText}>{t('announcements.form.dateRange')}</Text>
                  </View>
                  <Divider style={styles.sectionDivider} />

                  <View style={styles.row}>
                    <View style={styles.datePickerContainer}>
                      <Text style={styles.datePickerLabel}>{t('announcements.form.startDate')}</Text>
                      <View style={styles.datePickerWrapper}>
                        <EthiopianDatePicker
                          value={formData.startDate}
                          onChange={(date) => handleDateChange(date, 'startDate')}
                          label={t('announcements.form.selectStartDate')}
                          language={t('language')}
                        />
                        <MaterialCommunityIcons name="calendar-start" size={24} color={'#1976d2'} style={styles.datePickerIcon} />
                      </View>
                    </View>

                    <View style={styles.datePickerContainer}>
                      <Text style={styles.datePickerLabel}>{t('announcements.form.endDate')}</Text>
                      <View style={styles.datePickerWrapper}>
                        <EthiopianDatePicker
                          value={formData.endDate}
                          onChange={(date) => handleDateChange(date, 'endDate')}
                          label={t('announcements.form.selectEndDate')}
                          minDate={formData.startDate}
                          language={t('language')}
                        />
                        <MaterialCommunityIcons name="calendar-end" size={24} color={'#1976d2'} style={styles.datePickerIcon} />
                      </View>
                    </View>
                  </View>
                </View>
              </Animatable.View>

              <Animatable.View animation="fadeInLeft" duration={500} delay={300}>
                <View style={styles.formSection}>
                  <View style={styles.sectionHeader}>
                    <MaterialCommunityIcons name="account-group" size={20} color={'#1976d2'} />
                    <Text style={styles.sectionHeaderText}>{t('announcements.form.targetAudience')}</Text>
                  </View>
                  <Divider style={styles.sectionDivider} />

                  <View style={styles.chipContainer}>
                    {['teachers', 'students', 'parents'].map((audience, index) => (
                      <Animatable.View key={audience} animation="zoomIn" duration={300} delay={300 + (index * 100)}>
                        <Chip
                          selected={formData.targetAudience.includes(audience)}
                          onPress={() => {
                            toggleAudience(audience);
                            if (formErrors.targetAudience) {
                              setFormErrors({...formErrors, targetAudience: null});
                            }
                          }}
                          style={[styles.audienceChip, formData.targetAudience.includes(audience) && styles.selectedChip]}
                          selectedColor={'#1976d2'}
                          showSelectedCheck={true}
                          icon={audience === 'teachers' ? 'school' : audience === 'students' ? 'account-school' : 'account-child'}
                        >
                          {t(`announcements.audiences.${audience}`)}
                        </Chip>
                      </Animatable.View>
                    ))}
                  </View>
                  {formErrors.targetAudience && <HelperText type="error">{formErrors.targetAudience}</HelperText>}
                </View>
              </Animatable.View>

              <Animatable.View animation="fadeInLeft" duration={500} delay={400}>
                <View style={styles.formSection}>
                  <View style={styles.sectionHeader}>
                    <MaterialCommunityIcons name="flag" size={20} color={'#1976d2'} />
                    <Text style={styles.sectionHeaderText}>{t('announcements.form.priority')}</Text>
                  </View>
                  <Divider style={styles.sectionDivider} />

                  <View style={styles.chipContainer}>
                    {['low', 'normal', 'high', 'urgent'].map((priority, index) => (
                      <Animatable.View key={priority} animation="zoomIn" duration={300} delay={400 + (index * 100)}>
                        <Chip
                          selected={formData.priority === priority}
                          onPress={() => setFormData({ ...formData, priority })}
                          style={[styles.priorityFormChip, { borderColor: getPriorityColor(priority) }]}
                          selectedColor={getPriorityColor(priority)}
                        >
                          {t(`announcements.priorities.${priority}`)}
                        </Chip>
                      </Animatable.View>
                    ))}
                  </View>
                </View>
              </Animatable.View>

              <Animatable.View animation="fadeInLeft" duration={500} delay={500}>
                <View style={styles.formSection}>
                  <View style={styles.sectionHeader}>
                    <MaterialCommunityIcons name="check-circle" size={20} color={'#1976d2'} />
                    <Text style={styles.sectionHeaderText}>{t('announcements.form.status')}</Text>
                  </View>
                  <Divider style={styles.sectionDivider} />

                  <View style={styles.chipContainer}>
                    {['draft', 'published', 'archived'].map((status, index) => (
                      <Animatable.View key={status} animation="zoomIn" duration={300} delay={500 + (index * 100)}>
                        <Chip
                          key={status}
                          selected={formData.status === status}
                          onPress={() => setFormData({ ...formData, status })}
                          style={[styles.statusFormChip, formData.status === status && styles.selectedStatusChip]}
                          selectedColor={'#1976d2'}
                        >
                          {t(`announcements.statuses.${status}`)}
                        </Chip>
                      </Animatable.View>
                    ))}
                  </View>
                </View>
              </Animatable.View>

              <Animatable.View animation="fadeInUp" duration={500} delay={600} style={styles.modalActions}>
                <Button
                  mode="outlined"
                  onPress={() => {
                    setVisible(false);
                    resetForm();
                  }}
                  style={styles.cancelButton}
                  icon="close"
                >
                  {t('actions.cancel')}
                </Button>
                <Button
                  mode="contained"
                  onPress={selectedAnnouncement ? handleUpdateAnnouncement : handleAddAnnouncement}
                  loading={loading}
                  style={styles.submitButton}
                  icon={selectedAnnouncement ? "content-save" : "plus"}
                >
                  {t(selectedAnnouncement ? 'actions.update' : 'actions.create')}
                </Button>
              </Animatable.View>
            </ScrollView>
          </Animatable.View>
        </Modal>

        <Modal
          visible={confirmDelete}
          onDismiss={() => setConfirmDelete(false)}
          contentContainerStyle={styles.confirmModal}
        >
          <Animatable.View animation="zoomIn" duration={300}>
            <View style={styles.confirmModalContent}>
              <MaterialCommunityIcons name="alert-circle" size={56} color={'#B00020'} style={styles.confirmIcon} />
              <Title style={styles.confirmTitle}>{t('announcements.confirmDelete')}</Title>
              <Paragraph style={styles.confirmText}>{t('announcements.confirmDeleteText')}</Paragraph>

              <View style={styles.confirmActions}>
                <Button
                  mode="outlined"
                  onPress={() => setConfirmDelete(false)}
                  style={styles.cancelButton}
                >
                  {t('actions.cancel')}
                </Button>
                <Button
                  mode="contained"
                  onPress={() => handleDeleteAnnouncement(selectedAnnouncementId)}
                  loading={loading}
                  style={[styles.submitButton, styles.deleteButton]}
                  buttonColor={'#B00020'}
                >
                  {t('actions.delete')}
                </Button>
              </View>
            </View>
          </Animatable.View>
        </Modal>

        <Snackbar
          visible={snackbarVisible}
          onDismiss={() => setSnackbarVisible(false)}
          duration={3000}
          style={styles.snackbar}
          action={{
            label: t('actions.dismiss'),
            onPress: () => setSnackbarVisible(false),
          }}
        >
          {snackbarMessage}
        </Snackbar>
      </Portal>

      {/* Ethiopian Date Picker is used instead of DateTimePicker */}

      <FAB
        style={styles.fab}
        icon="plus"
        onPress={() => {
          resetForm();
          setVisible(true);
        }}
      />
    </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
    padding: 16,
    paddingTop: 0,
  },
  headerSurface: {
    elevation: 4,
    borderRadius: 8,
    overflow: 'hidden',
    marginBottom: 16,
  },
  headerGradient: {
    padding: 16,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerIcon: {
    marginRight: 8,
  },
  headerTitle: {
    color: 'white',
    fontSize: 24,
    fontWeight: 'bold',
  },
  viewToggleContainer: {
    flexDirection: 'row',
  },
  viewToggleButton: {
    margin: 0,
  },
  searchContainer: {
    marginBottom: 16,
  },
  searchBar: {
    elevation: 2,
    borderRadius: 8,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    fontSize: 20,
    marginTop: 16,
    textAlign: 'center',
  },
  emptySubText: {
    textAlign: 'center',
    marginTop: 8,
    marginBottom: 24,
    color: '#666',
  },
  emptyButton: {
    marginTop: 16,
  },
  tableContainer: {
    flex: 1,
  },
  dataTable: {
    backgroundColor: 'white',
    borderRadius: 8,
    elevation: 2,
    marginBottom: 16,
  },
  tableHeader: {
    backgroundColor: '#f0f0f0',
  },
  tableRow: {
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  typeChip: {
    height: 28,
  },
  priorityChip: {
    height: 28,
  },
  statusChip: {
    height: 28,
  },
  cardsContainer: {
    flex: 1,
  },
  cardsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    padding: 4,
  },
  cardWrapper: {
    width: '48%',
    marginBottom: 16,
  },
  announcementCard: {
    elevation: 2,
  },
  cardHeader: {
    padding: 8,
    alignItems: 'flex-end',
  },
  priorityBadge: {
    fontSize: 10,
  },
  cardTitle: {
    fontSize: 16,
  },
  cardContent: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  cardChips: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 8,
  },
  cardChip: {
    marginRight: 4,
    marginBottom: 4,
    height: 24,
  },
  cardChipText: {
    fontSize: 10,
  },
  cardActions: {
    justifyContent: 'flex-end',
  },
  modal: {
    backgroundColor: 'white',
    margin: 20,
    borderRadius: 8,
    overflow: 'hidden',
    maxHeight: '90%',
  },
  modalContainer: {
    maxHeight: '100%',
    flexDirection: 'column',
  },
  modalScrollView: {
    flexGrow: 0,
  },
  modalScrollContent: {
    paddingBottom: 20,
  },
  modalHeader: {
    padding: 16,
  },
  modalHeaderContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  modalHeaderIcon: {
    marginRight: 8,
  },
  modalHeaderTitle: {
    color: 'white',
    fontSize: 20,
  },
  modalBody: {
    padding: 16,
  },
  formSection: {
    marginTop: 16,
    marginBottom: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  sectionHeaderText: {
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
    color: '#333',
  },
  sectionDivider: {
    marginBottom: 16,
    height: 1,
  },
  row: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginVertical: 8,
    gap: 8,
  },
  chipContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginBottom: 8,
  },
  audienceChip: {
    marginBottom: 8,
  },
  selectedChip: {
    backgroundColor: 'rgba(98, 0, 238, 0.1)',
  },
  priorityFormChip: {
    marginBottom: 8,
  },
  statusFormChip: {
    marginBottom: 8,
  },
  selectedStatusChip: {
    backgroundColor: 'rgba(98, 0, 238, 0.1)',
  },
  datePickerContainer: {
    flex: 1,
    marginRight: 8,
    marginBottom: 8,
  },
  datePickerWrapper: {
    position: 'relative',
    flexDirection: 'row',
    alignItems: 'center',
  },
  datePickerIcon: {
    position: 'absolute',
    right: 10,
    top: 12,
  },
  datePickerLabel: {
    fontSize: 14,
    marginBottom: 8,
    color: '#666',
  },
  formInput: {
    marginBottom: 8,
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 24,
    gap: 8,
  },
  cancelButton: {
    marginRight: 8,
  },
  submitButton: {
    minWidth: 100,
  },
  deleteButton: {
    backgroundColor: 'red',
  },
  confirmModal: {
    backgroundColor: 'white',
    margin: 20,
    borderRadius: 8,
    padding: 0,
    overflow: 'hidden',
  },
  confirmModalContent: {
    padding: 24,
    alignItems: 'center',
  },
  confirmIcon: {
    marginBottom: 16,
  },
  confirmTitle: {
    fontSize: 20,
    marginBottom: 8,
    textAlign: 'center',
  },
  confirmText: {
    textAlign: 'center',
    marginBottom: 24,
    color: '#666',
  },
  confirmActions: {
    flexDirection: 'row',
    justifyContent: 'center',
    width: '100%',
  },
  snackbar: {
    bottom: 16,
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
    backgroundColor: '#6200ee',
  },
});

export default AnnouncementManagement;
