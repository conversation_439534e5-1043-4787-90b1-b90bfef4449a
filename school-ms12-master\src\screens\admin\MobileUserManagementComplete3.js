  // Render user card
  const renderUserCard = useCallback((user) => {
    const initials = `${user.firstName?.[0] || ''}${user.lastName?.[0] || ''}`.toUpperCase();
    const fullName = `${user.firstName || ''} ${user.lastName || ''}`;
    
    // Get role-specific color
    const getRoleColor = (role) => {
      switch(role) {
        case 'admin': return mobileTheme.colors.admin;
        case 'teacher': return mobileTheme.colors.teacher;
        case 'student': return mobileTheme.colors.student;
        case 'parent': return mobileTheme.colors.parent;
        default: return mobileTheme.colors.primary;
      }
    };
    
    return (
      <Animatable.View 
        animation="fadeIn" 
        duration={500} 
        key={user.id}
      >
        <Surface style={styles.userCard}>
          <TouchableOpacity 
            style={styles.userCardContent}
            onPress={() => handleUserPress(user)}
          >
            <View style={styles.userInfo}>
              {user.photoURL ? (
                <Avatar.Image 
                  source={{ uri: user.photoURL }} 
                  size={50} 
                  style={styles.avatar}
                />
              ) : (
                <Avatar.Text 
                  label={initials} 
                  size={50} 
                  style={[styles.avatar, { backgroundColor: getRoleColor(user.role) }]}
                />
              )}
              
              <View style={styles.userDetails}>
                <Text style={styles.userName}>{fullName}</Text>
                <Text style={styles.userEmail}>{user.email}</Text>
                
                <View style={styles.userChips}>
                  <Chip 
                    mode="outlined" 
                    style={[styles.roleChip, { borderColor: getRoleColor(user.role) }]}
                    textStyle={{ color: getRoleColor(user.role) }}
                  >
                    {translate(`userManagement.roles.${user.role}`) || user.role}
                  </Chip>
                  
                  <Chip 
                    mode="outlined" 
                    style={[
                      styles.statusChip, 
                      { 
                        borderColor: user.status === 'active' ? mobileTheme.colors.success : mobileTheme.colors.error,
                        backgroundColor: user.status === 'active' ? mobileTheme.colors.success + '20' : mobileTheme.colors.error + '20'
                      }
                    ]}
                    textStyle={{ 
                      color: user.status === 'active' ? mobileTheme.colors.success : mobileTheme.colors.error 
                    }}
                  >
                    {translate(`userManagement.status.${user.status}`) || user.status}
                  </Chip>
                </View>
              </View>
            </View>
            
            <View style={styles.userActions}>
              <IconButton 
                icon="pencil" 
                size={20} 
                color={mobileTheme.colors.primary}
                onPress={() => navigation.navigate('UserEdit', { userId: user.id })}
              />
              <IconButton 
                icon={user.status === 'active' ? 'account-off' : 'account-check'}
                size={20} 
                color={user.status === 'active' ? mobileTheme.colors.error : mobileTheme.colors.success}
                onPress={() => {
                  setSelectedUser(user);
                  if (user.status === 'active') {
                    setShowDeactivateConfirmDialog(true);
                  } else {
                    setShowActivateConfirmDialog(true);
                  }
                }}
              />
            </View>
          </TouchableOpacity>
        </Surface>
      </Animatable.View>
    );
  }, [translate, navigation]);
  
  // Render user list
  const renderUserList = useCallback(() => {
    // Show loading indicator when initially loading
    if (loading && users.length === 0) {
      return (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={mobileTheme.colors.admin} />
          <Text style={styles.loadingText}>
            {translate('userManagement.loadingUsers') || 'Loading users...'}
          </Text>
          <ProgressBar 
            progress={loadingProgress} 
            color={mobileTheme.colors.admin} 
            style={styles.progressBar} 
          />
        </View>
      );
    }
    
    // Show a message when no users are found
    if (users.length === 0 && !loading) {
      return (
        <View style={styles.noResultsContainer}>
          <MaterialCommunityIcons name="account-question" size={48} color="#9e9e9e" />
          <Text style={styles.noResultsText}>
            {translate('userManagement.noUsersFound') || 'No users found'}
          </Text>
          <Button
            mode="contained"
            onPress={() => navigation.navigate('UserRegistration')}
            style={styles.addUserButton}
            icon="account-plus"
          >
            {translate('userManagement.addFirstUser') || 'Add Your First User'}
          </Button>
        </View>
      );
    }

    // Show a message when no users match the search criteria
    if (searchQuery && filteredUsers.length === 0) {
      return (
        <View style={styles.noResultsContainer}>
          <MaterialCommunityIcons name="magnify-close" size={48} color="#9e9e9e" />
          <Text style={styles.noResultsText}>
            {translate('userManagement.noSearchResults') || 'No users match your search criteria'}
          </Text>
          <Button
            mode="outlined"
            onPress={() => setSearchQuery('')}
            style={styles.clearSearchButton}
          >
            {translate('userManagement.clearSearch') || 'Clear Search'}
          </Button>
        </View>
      );
    }
    
    return (
      <View style={styles.userListContainer}>
        {filteredUsers.map(user => renderUserCard(user))}
      </View>
    );
  }, [
    loading, 
    users, 
    filteredUsers, 
    searchQuery,
    loadingProgress,
    translate,
    renderUserCard
  ]);
