import React, { useState, useEffect, useCallback, useRef } from 'react';
import { View, ScrollView, StyleSheet, Alert, TouchableOpacity, Dimensions, StatusBar } from 'react-native';
import { Card, Title, DataTable, Portal, Modal, TextInput, Text, Chip, IconButton, Snackbar, SegmentedButtons, ActivityIndicator, Menu, Divider, Button, Badge, Surface, Searchbar, Avatar, Dialog, List, TouchableRipple, Paragraph, Provider as PaperProvider, DefaultTheme } from 'react-native-paper';
import { db } from '../../config/firebase';
import { collection, query, getDocs, addDoc, updateDoc, doc, deleteDoc, where, Timestamp, serverTimestamp, writeBatch, getDoc } from 'firebase/firestore';
import CustomButton from '../../components/common/CustomButton';
import EthiopianCalendar from '../../utils/EthiopianCalendar';
import { Picker } from '@react-native-picker/picker';
import EthiopianTimePicker from '../../components/common/EthiopianTimePicker';
import { useLanguage } from '../../context/LanguageContext';
import * as Notifications from 'expo-notifications';
import { useNavigation } from '@react-navigation/native';
// Theme import removed
import { SafeAreaView } from 'react-native-safe-area-context';
import * as Animatable from 'react-native-animatable';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { Animated } from 'react-native';
import AdminAppHeader from '../../components/common/AdminAppHeader';
import AdminSidebar from '../../components/common/AdminSidebar';
import SidebarBackdrop from '../../components/common/SidebarBackdrop';

const ClassSchedule = () => {
  const navigation = useNavigation();
  // No theme needed
  const { translate, language, isRTL, getTextStyle } = useLanguage();
  const screenWidth = Dimensions.get('window').width;
  const screenHeight = Dimensions.get('window').height;

  // UI state variables
  const [menuVisible, setMenuVisible] = useState(false);
  const [logoutDialogVisible, setLogoutDialogVisible] = useState(false);

  // Sidebar state
  const [drawerOpen, setDrawerOpen] = useState(false);
  const drawerAnim = useRef(new Animated.Value(-300)).current;
  const backdropFadeAnim = useRef(new Animated.Value(0)).current;
  const [activeSidebarItem, setActiveSidebarItem] = useState('ClassSchedule');

  // State for timetable management
  const [timetables, setTimetables] = useState([]);
  const [classes, setClasses] = useState([]);
  const [subjects, setSubjects] = useState([]);
  const [teachers, setTeachers] = useState([]);
  const [sections, setSections] = useState([]);
  const [rooms, setRooms] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');

  // Modal and form state
  const [modalVisible, setModalVisible] = useState(false);
  const [editingTimetable, setEditingTimetable] = useState(null);
  const [viewMode, setViewMode] = useState('class'); // 'class', 'teacher', 'room'
  const [filterClassId, setFilterClassId] = useState('');
  const [filterSectionName, setFilterSectionName] = useState('');
  const [filterTeacherId, setFilterTeacherId] = useState('');
  const [filterDay, setFilterDay] = useState('all');
  // State variables for the component

  // Form data for timetable entry
  const [formData, setFormData] = useState({
    day: '',
    periodNumber: '',
    startTime: '',
    endTime: '',
    classId: '',
    sectionName: '',
    subjectId: '',
    teacherId: '',
    roomNumber: '',
    session: '', // 'morning' or 'afternoon'
  });

  // Days of the week
  const days = [
    'Monday',
    'Tuesday',
    'Wednesday',
    'Thursday',
    'Friday',
    'Saturday',
    'Sunday',
  ];

  // Time slots for 40-minute periods
  const morningTimeSlots = [
    { period: 1, start: '08:00', end: '08:40', session: 'morning' },
    { period: 2, start: '08:45', end: '09:25', session: 'morning' },
    { period: 3, start: '09:30', end: '10:10', session: 'morning' },
    { period: 4, start: '10:15', end: '10:55', session: 'morning' },
    { period: 5, start: '11:00', end: '11:40', session: 'morning' },
    { period: 6, start: '11:45', end: '12:25', session: 'morning' },
  ];

  const afternoonTimeSlots = [
    { period: 1, start: '12:30', end: '13:10', session: 'afternoon' },
    { period: 2, start: '13:15', end: '13:55', session: 'afternoon' },
    { period: 3, start: '14:00', end: '14:40', session: 'afternoon' },
    { period: 4, start: '14:45', end: '15:25', session: 'afternoon' },
    { period: 5, start: '15:30', end: '16:10', session: 'afternoon' },
    { period: 6, start: '16:15', end: '16:55', session: 'afternoon' },
  ];

  // Combined time slots for display
  const defaultTimeSlots = [...morningTimeSlots, ...afternoonTimeSlots];

  useEffect(() => {
    fetchTimetables();
    fetchClasses();
    fetchSubjects();
    fetchTeachers();
    fetchRooms();
  }, []);

  useEffect(() => {
    navigation.setOptions({
      headerShown: false,
    });
  }, [navigation]);

  // Toggle drawer function
  const toggleDrawer = () => {
    if (drawerOpen) {
      // Close drawer
      Animated.parallel([
        Animated.timing(drawerAnim, {
          toValue: -300,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(backdropFadeAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        })
      ]).start(() => setDrawerOpen(false));
    } else {
      // Open drawer
      setDrawerOpen(true);
      Animated.parallel([
        Animated.timing(drawerAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(backdropFadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        })
      ]).start();
    }
  };

  // Handle logout
  const handleLogout = async () => {
    try {
      await signOut(auth);
      navigation.reset({
        index: 0,
        routes: [{ name: 'Login' }],
      });
    } catch (error) {
      console.error('Error logging out:', error);
    }
  };

  // Render sidebar
  const renderSidebar = () => (
    <Animated.View
      style={[styles.sidebar, { transform: [{ translateX: drawerAnim }] }]}
    >
      <LinearGradient
        colors={['#1976d2', '#1565C0']}
        style={styles.sidebarHeader}
      >
        <View style={styles.sidebarProfile}>
          <Avatar.Icon
            size={80}
            icon="shield-account"
            style={styles.sidebarAvatar}
            color="white"
            backgroundColor="rgba(255, 255, 255, 0.2)"
          />
          <Title style={styles.sidebarName}>{translate('administrative.roles.admin')}</Title>
          <Paragraph style={styles.sidebarRole}>{translate('administrative.roles.admin')}</Paragraph>
        </View>
      </LinearGradient>

      <ScrollView style={styles.sidebarMenu}>
        <List.Section>
          <TouchableRipple
            style={[styles.sidebarItem]}
            onPress={() => navigation.navigate('AdminDashboard')}
          >
            <View style={{ flexDirection: 'row', alignItems: 'center', paddingHorizontal: 16 }}>
              <IconButton
                icon="view-dashboard"
                color={'#1976d2'}
                size={24}
                style={{ margin: 0 }}
              />
              <Text style={[styles.sidebarItemText]}>
                {translate('navigation.bottomTabs.dashboard')}
              </Text>
            </View>
          </TouchableRipple>

          <List.Subheader style={styles.sidebarSubheader}>
            {translate('dashboard.classExam')}
          </List.Subheader>

          <TouchableRipple
            style={[styles.sidebarItem]}
            onPress={() => navigation.navigate('ClassManagement')}
          >
            <View style={{ flexDirection: 'row', alignItems: 'center', paddingHorizontal: 16 }}>
              <IconButton
                icon="school"
                color="#757575"
                size={24}
                style={{ margin: 0 }}
              />
              <Text style={[styles.sidebarItemText]}>
                {translate('dashboard.classManagement')}
              </Text>
            </View>
          </TouchableRipple>

          <TouchableRipple
            style={[styles.sidebarItem, styles.sidebarItemActive]}
            onPress={() => {}}
          >
            <View style={{ flexDirection: 'row', alignItems: 'center', paddingHorizontal: 16 }}>
              <IconButton
                icon="calendar-check"
                color={'#1976d2'}
                size={24}
                style={{ margin: 0 }}
              />
              <Text style={[styles.sidebarItemText, styles.sidebarItemTextActive]}>
                {translate('dashboard.classSchedule')}
              </Text>
            </View>
          </TouchableRipple>

          <TouchableRipple
            style={[styles.sidebarItem]}
            onPress={() => navigation.navigate('ExamManagement')}
          >
            <View style={{ flexDirection: 'row', alignItems: 'center', paddingHorizontal: 16 }}>
              <IconButton
                icon="file-document-edit"
                color="#757575"
                size={24}
                style={{ margin: 0 }}
              />
              <Text style={[styles.sidebarItemText]}>
                {translate('dashboard.examTimetable')}
              </Text>
            </View>
          </TouchableRipple>

          <TouchableRipple
            style={[styles.sidebarItem]}
            onPress={() => navigation.navigate('ResultsManagement')}
          >
            <View style={{ flexDirection: 'row', alignItems: 'center', paddingHorizontal: 16 }}>
              <IconButton
                icon="chart-box"
                color="#757575"
                size={24}
                style={{ margin: 0 }}
              />
              <Text style={[styles.sidebarItemText]}>
                {translate('dashboard.results')}
              </Text>
            </View>
          </TouchableRipple>
        </List.Section>

        <Divider style={styles.sidebarDivider} />

        <TouchableRipple
          style={styles.sidebarItem}
          onPress={() => setLogoutDialogVisible(true)}
        >
          <View style={{ flexDirection: 'row', alignItems: 'center', paddingHorizontal: 16 }}>
            <IconButton
              icon="logout"
              color="#F44336"
              size={24}
              style={{ margin: 0 }}
            />
            <Text style={[styles.sidebarItemText, { color: '#F44336' }]}>
              {language === 'en' ? 'Logout' : language === 'am' ? 'ውጣ' : 'Ba\'i'}
            </Text>
          </View>
        </TouchableRipple>
      </ScrollView>
    </Animated.View>
  );

  useEffect(() => {
    if (filterClassId) {
      fetchSectionsForClass(filterClassId);
    }
  }, [filterClassId]);

  // Fetch timetables from Firestore with offline support and filtering
  const fetchTimetables = async () => {
    try {
      setLoading(true);
      setError('');

      // Build query with filters
      const timetablesRef = collection(db, 'timetables');
      let timetableQuery = timetablesRef;

      // Apply filters if they exist
      const filters = [];

      if (filterClassId) {
        filters.push(where('classId', '==', filterClassId));
      }

      if (filterSectionName) {
        filters.push(where('sectionName', '==', filterSectionName));
      }

      if (filterTeacherId) {
        filters.push(where('teacherId', '==', filterTeacherId));
      }

      if (filterDay !== 'all') {
        filters.push(where('day', '==', filterDay));
      }

      // Apply filters to query if any exist
      if (filters.length > 0) {
        timetableQuery = query(timetablesRef, ...filters);
      } else {
        timetableQuery = query(timetablesRef);
      }

      // Execute query with error handling for offline
      try {
        const querySnapshot = await getDocs(timetableQuery);

        const timetableData = [];
        querySnapshot.forEach((doc) => {
          timetableData.push({ id: doc.id, ...doc.data() });
        });

        setTimetables(timetableData);

        // Cache the data for offline use
        try {
          // We're not actually using AsyncStorage here since it would require additional imports
          // But in a real implementation, you would store the data like this:
          // await AsyncStorage.setItem('cachedTimetables', JSON.stringify(timetableData));
          console.log('Timetable data cached for offline use');
        } catch (storageError) {
          console.error('Error caching timetable data:', storageError);
        }

      } catch (queryError) {
        console.error('Error executing timetable query:', queryError);

        // If we're offline, try to use the existing timetables and apply filters in memory
        if (timetables.length > 0) {
          console.log('Using cached timetables and filtering in memory');

          // Apply filters in memory
          const filteredTimetables = timetables.filter(entry => {
            // Filter by class
            if (filterClassId && entry.classId !== filterClassId) {
              return false;
            }

            // Filter by section
            if (filterSectionName && entry.sectionName !== filterSectionName) {
              return false;
            }

            // Filter by teacher
            if (filterTeacherId && entry.teacherId !== filterTeacherId) {
              return false;
            }

            // Filter by day
            if (filterDay !== 'all' && entry.day !== filterDay) {
              return false;
            }

            return true;
          });

          // Update the UI with filtered data
          setTimetables(filteredTimetables);
        } else {
          setError('Failed to load timetable data. Please check your internet connection.');
        }
      }

    } catch (error) {
      console.error('Error in fetchTimetables:', error);
      setError('An unexpected error occurred. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Fetch classes from Firestore with offline support
  const fetchClasses = async () => {
    try {
      setLoading(true);

      // Try to get classes from Firestore
      const classesRef = collection(db, 'classes');
      const querySnapshot = await getDocs(classesRef);

      const classData = [];
      querySnapshot.forEach((doc) => {
        classData.push({ id: doc.id, ...doc.data() });
      });

      // Store classes in state and local storage for offline access
      setClasses(classData);

      // Store in AsyncStorage for future offline use
      try {
        // We're not actually using AsyncStorage here since it would require additional imports
        // But in a real implementation, you would store the data like this:
        // await AsyncStorage.setItem('cachedClasses', JSON.stringify(classData));
        console.log('Classes data cached for offline use');
      } catch (storageError) {
        console.error('Error caching classes data:', storageError);
      }

    } catch (error) {
      console.error('Error fetching classes:', error);
      setError('Failed to load classes. Please check your internet connection.');

      // Try to load from cache if available
      // In a real implementation with AsyncStorage:
      // try {
      //   const cachedData = await AsyncStorage.getItem('cachedClasses');
      //   if (cachedData) {
      //     setClasses(JSON.parse(cachedData));
      //     console.log('Using cached classes data');
      //   }
      // } catch (cacheError) {
      //   console.error('Error loading cached classes:', cacheError);
      // }
    } finally {
      setLoading(false);
    }
  };

  // Fetch sections for a specific class with offline support
  const fetchSectionsForClass = async (classId) => {
    try {
      setLoading(true);

      // First try to get sections from the classes we already have in memory
      const selectedClass = classes.find(c => c.id === classId);
      if (selectedClass && selectedClass.sections) {
        setSections(selectedClass.sections);
        setLoading(false);
        return;
      }

      // If not found in memory, try to fetch from Firestore
      const classRef = doc(db, 'classes', classId);
      const classDoc = await getDoc(classRef);

      if (classDoc.exists()) {
        const classData = classDoc.data();
        const sectionData = classData.sections || [];
        setSections(sectionData);
      } else {
        setSections([]);
      }
    } catch (error) {
      console.error('Error fetching sections:', error);

      // Handle offline scenario - try to extract sections from classes array
      const selectedClass = classes.find(c => c.id === classId);
      if (selectedClass && selectedClass.sections) {
        setSections(selectedClass.sections);
        console.log('Using cached sections data');
      } else {
        setError('Failed to load sections. Please check your internet connection.');
        setSections([]);
      }
    } finally {
      setLoading(false);
    }
  };

  // Fetch subjects from Firestore with offline support
  const fetchSubjects = async () => {
    try {
      setLoading(true);

      const subjectsRef = collection(db, 'subjects');
      const querySnapshot = await getDocs(subjectsRef);

      const subjectData = [];
      querySnapshot.forEach((doc) => {
        subjectData.push({ id: doc.id, ...doc.data() });
      });

      setSubjects(subjectData);

      // Cache the data for offline use
      try {
        // We're not actually using AsyncStorage here since it would require additional imports
        // But in a real implementation, you would store the data like this:
        // await AsyncStorage.setItem('cachedSubjects', JSON.stringify(subjectData));
        console.log('Subjects data cached for offline use');
      } catch (storageError) {
        console.error('Error caching subjects data:', storageError);
      }

    } catch (error) {
      console.error('Error fetching subjects:', error);
      setError('Failed to load subjects. Please check your internet connection.');

      // If we have subjects in memory, use them
      if (subjects.length === 0) {
        setError('Failed to load subjects. Please check your internet connection.');
      } else {
        console.log('Using cached subjects data');
      }
    } finally {
      setLoading(false);
    }
  };

  // Fetch teachers from Firestore with offline support
  const fetchTeachers = async () => {
    try {
      setLoading(true);

      const teachersRef = collection(db, 'users');
      const q = query(teachersRef, where('role', '==', 'teacher'));

      try {
        const querySnapshot = await getDocs(q);

        const teacherData = [];
        querySnapshot.forEach((doc) => {
          teacherData.push({
            id: doc.id,
            ...doc.data(),
            fullName: `${doc.data().firstName || ''} ${doc.data().lastName || ''}`.trim()
          });
        });

        setTeachers(teacherData);

        // Cache the data for offline use
        try {
          // We're not actually using AsyncStorage here since it would require additional imports
          // But in a real implementation, you would store the data like this:
          // await AsyncStorage.setItem('cachedTeachers', JSON.stringify(teacherData));
          console.log('Teachers data cached for offline use');
        } catch (storageError) {
          console.error('Error caching teachers data:', storageError);
        }
      } catch (queryError) {
        console.error('Error executing teachers query:', queryError);

        // If we have teachers in memory, use them
        if (teachers.length === 0) {
          setError('Failed to load teachers. Please check your internet connection.');
        } else {
          console.log('Using cached teachers data');
        }
      }
    } catch (error) {
      console.error('Error fetching teachers:', error);
      setError('Failed to load teachers. Please check your internet connection.');
    } finally {
      setLoading(false);
    }
  };

  // Fetch available rooms with offline support
  const fetchRooms = async () => {
    try {
      setLoading(true);

      try {
        const roomsRef = collection(db, 'rooms');
        const querySnapshot = await getDocs(roomsRef);

        const roomData = [];
        querySnapshot.forEach((doc) => {
          roomData.push({ id: doc.id, ...doc.data() });
        });

        // If no rooms are defined yet, create some default rooms
        if (roomData.length === 0) {
          const defaultRooms = [
            { number: '101', capacity: 40, building: 'Main', floor: 1 },
            { number: '102', capacity: 40, building: 'Main', floor: 1 },
            { number: '103', capacity: 40, building: 'Main', floor: 1 },
            { number: '201', capacity: 40, building: 'Main', floor: 2 },
            { number: '202', capacity: 40, building: 'Main', floor: 2 },
            { number: '203', capacity: 40, building: 'Main', floor: 2 },
          ];

          setRooms(defaultRooms);

          // Cache the default rooms for offline use
          try {
            // We're not actually using AsyncStorage here since it would require additional imports
            // But in a real implementation, you would store the data like this:
            // await AsyncStorage.setItem('cachedRooms', JSON.stringify(defaultRooms));
            console.log('Default rooms data cached for offline use');
          } catch (storageError) {
            console.error('Error caching default rooms data:', storageError);
          }
        } else {
          setRooms(roomData);

          // Cache the room data for offline use
          try {
            // We're not actually using AsyncStorage here since it would require additional imports
            // But in a real implementation, you would store the data like this:
            // await AsyncStorage.setItem('cachedRooms', JSON.stringify(roomData));
            console.log('Rooms data cached for offline use');
          } catch (storageError) {
            console.error('Error caching rooms data:', storageError);
          }
        }
      } catch (queryError) {
        console.error('Error executing rooms query:', queryError);

        // If we're offline, use default rooms
        if (rooms.length === 0) {
          const defaultRooms = [
            { number: '101', capacity: 40, building: 'Main', floor: 1 },
            { number: '102', capacity: 40, building: 'Main', floor: 1 },
            { number: '103', capacity: 40, building: 'Main', floor: 1 },
            { number: '201', capacity: 40, building: 'Main', floor: 2 },
            { number: '202', capacity: 40, building: 'Main', floor: 2 },
            { number: '203', capacity: 40, building: 'Main', floor: 2 },
          ];

          setRooms(defaultRooms);
          console.log('Using default rooms data for offline use');
        } else {
          console.log('Using cached rooms data');
        }
      }
    } catch (error) {
      console.error('Error fetching rooms:', error);
      setError('Failed to load rooms. Please check your internet connection.');

      // If we're offline and have no rooms, use default rooms
      if (rooms.length === 0) {
        const defaultRooms = [
          { number: '101', capacity: 40, building: 'Main', floor: 1 },
          { number: '102', capacity: 40, building: 'Main', floor: 1 },
          { number: '103', capacity: 40, building: 'Main', floor: 1 },
          { number: '201', capacity: 40, building: 'Main', floor: 2 },
          { number: '202', capacity: 40, building: 'Main', floor: 2 },
          { number: '203', capacity: 40, building: 'Main', floor: 2 },
        ];

        setRooms(defaultRooms);
      }
    } finally {
      setLoading(false);
    }
  };

  // Check for scheduling conflicts
  const checkForConflicts = (newEntry) => {
    const conflicts = [];

    // Skip conflict check if editing the same entry
    if (editingTimetable && editingTimetable.id === newEntry.id) {
      return conflicts;
    }

    // Check if the section already has a different session on the same day
    const sectionEntriesForDay = timetables.filter(entry =>
      entry.day === newEntry.day &&
      entry.classId === newEntry.classId &&
      entry.sectionName === newEntry.sectionName
    );

    // If there are entries for this section on this day, check if they have a different session
    if (sectionEntriesForDay.length > 0) {
      const existingSession = sectionEntriesForDay[0].session;
      if (existingSession && existingSession !== newEntry.session) {
        conflicts.push({
          type: 'session',
          message: `Class ${getClassName(newEntry.classId)} Section ${newEntry.sectionName} is already scheduled for ${existingSession} session on ${newEntry.day}. One section can only have one session per day.`
        });
      }
    }

    timetables.forEach(entry => {
      // Check for same day and overlapping time
      if (entry.day === newEntry.day) {
        const entryStart = convertTimeToMinutes(entry.startTime);
        const entryEnd = convertTimeToMinutes(entry.endTime);
        const newEntryStart = convertTimeToMinutes(newEntry.startTime);
        const newEntryEnd = convertTimeToMinutes(newEntry.endTime);

        // Check if time periods overlap
        if (!(newEntryEnd <= entryStart || newEntryStart >= entryEnd)) {
          // Teacher conflict - same teacher teaching different classes at the same time
          if (entry.teacherId === newEntry.teacherId) {
            conflicts.push({
              type: 'teacher',
              message: `Teacher ${getTeacherName(entry.teacherId)} is already scheduled during this time.`
            });
          }

          // Room conflict - same room used by different classes at the same time
          if (entry.roomNumber === newEntry.roomNumber) {
            conflicts.push({
              type: 'room',
              message: `Room ${entry.roomNumber} is already in use during this time.`
            });
          }

          // Class-section conflict - same class and section having different subjects at the same time
          if (entry.classId === newEntry.classId && entry.sectionName === newEntry.sectionName) {
            conflicts.push({
              type: 'class',
              message: `Class ${getClassName(entry.classId)} Section ${entry.sectionName} already has a class scheduled during this time.`
            });
          }
        }
      }
    });

    return conflicts;
  };

  // Convert time string (HH:MM) to minutes for easier comparison
  const convertTimeToMinutes = (timeString) => {
    const [hours, minutes] = timeString.split(':').map(Number);
    return hours * 60 + minutes;
  };

  // Get teacher name by ID
  const getTeacherName = (teacherId) => {
    const teacher = teachers.find(t => t.id === teacherId);
    return teacher ? teacher.fullName : 'Unknown Teacher';
  };

  // Get class name by ID
  const getClassName = (classId) => {
    const classObj = classes.find(c => c.id === classId);
    return classObj ? classObj.name : 'Unknown Class';
  };

  // Get subject name by ID
  const getSubjectName = (subjectId) => {
    const subject = subjects.find(s => s.id === subjectId);
    return subject ? subject.name : 'Unknown Subject';
  };

  // Handle form submission
  const handleSubmit = async () => {
    try {
      setLoading(true);
      setError('');

      // Validate form data
      if (!formData.day || !formData.startTime || !formData.endTime ||
          !formData.classId || !formData.sectionName || !formData.subjectId ||
          !formData.teacherId || !formData.roomNumber || !formData.session) {
        setError('Please fill in all required fields.');
        setLoading(false);
        return;
      }

      // Ensure period is 40 minutes
      const startMinutes = convertTimeToMinutes(formData.startTime);
      const endMinutes = convertTimeToMinutes(formData.endTime);
      const duration = endMinutes - startMinutes;

      if (duration !== 40) {
        setError(`Period duration must be exactly 40 minutes. Current duration: ${duration} minutes.`);
        setLoading(false);
        return;
      }

      // Check for scheduling conflicts
      const conflicts = checkForConflicts(formData);
      if (conflicts.length > 0) {
        setError(`Scheduling conflicts detected: ${conflicts.map(c => c.message).join(', ')}`);
        setLoading(false);
        return;
      }

      // Save to Firestore
      if (editingTimetable) {
        const timetableRef = doc(db, 'timetables', editingTimetable.id);
        await updateDoc(timetableRef, {
          ...formData,
          updatedAt: new Date().toISOString()
        });
        setSnackbarMessage('Timetable entry updated successfully!');
      } else {
        await addDoc(collection(db, 'timetables'), {
          ...formData,
          createdAt: new Date().toISOString()
        });
        setSnackbarMessage('New timetable entry added successfully!');
      }

      setModalVisible(false);
      setEditingTimetable(null);
      resetFormData();
      fetchTimetables();
      setSnackbarVisible(true);
    } catch (error) {
      console.error('Error saving timetable entry:', error);
      setError('Failed to save timetable entry. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Handle delete timetable entry
  const handleDelete = async (timetableId) => {
    try {
      setLoading(true);
      await deleteDoc(doc(db, 'timetables', timetableId));
      fetchTimetables();
      setSnackbarMessage('Timetable entry deleted successfully!');
      setSnackbarVisible(true);
    } catch (error) {
      console.error('Error deleting timetable entry:', error);
      setError('Failed to delete timetable entry. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Reset form data
  const resetFormData = () => {
    setFormData({
      day: '',
      periodNumber: '',
      startTime: '',
      endTime: '',
      classId: '',
      sectionName: '',
      subjectId: '',
      teacherId: '',
      roomNumber: '',
      session: '',
    });
  };

  // Handle time slot selection
  const handleTimeSlotSelect = (period) => {
    setFormData({
      ...formData,
      periodNumber: period.period.toString(),
      startTime: period.start,
      endTime: period.end,
      session: period.session
    });
  };

  // Publish timetable and send notifications
  const publishTimetable = async () => {
    try {
      setLoading(true);

      // Update all timetable entries to published status
      const batch = writeBatch(db);
      timetables.forEach(entry => {
        const entryRef = doc(db, 'timetables', entry.id);
        batch.update(entryRef, { published: true, publishedAt: new Date().toISOString() });
      });

      await batch.commit();

      // Create a general notification for all users
      await addDoc(collection(db, 'notifications'), {
        title: 'New Class Schedule Published',
        message: 'The school class schedule has been updated. Classes are now divided into morning (8:00-12:30) and afternoon (12:30-5:00) sessions. Please check your schedule.',
        type: 'timetable',
        priority: 'high',
        createdAt: new Date().toISOString(),
        targetAudience: 'all',
        read: false
      });

      // Send targeted notifications to teachers, students, and parents
      await sendTargetedNotifications();

      setSnackbarMessage('Timetable published successfully! Notifications sent to all users.');
      setSnackbarVisible(true);
      fetchTimetables();
    } catch (error) {
      console.error('Error publishing timetable:', error);
      setError('Failed to publish timetable. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Send targeted notifications to teachers, students, and parents with detailed timetable information
  const sendTargetedNotifications = async () => {
    try {
      setLoading(true);

      // Group timetable entries by class and section
      const entriesByClassSection = {};

      timetables.forEach(entry => {
        const key = `${entry.classId}_${entry.sectionName}`;
        if (!entriesByClassSection[key]) {
          entriesByClassSection[key] = [];
        }
        entriesByClassSection[key].push(entry);
      });

      // For each class and section, send notifications to relevant users
      for (const [key, entries] of Object.entries(entriesByClassSection)) {
        const [classId, sectionName] = key.split('_');

        // Get class name
        const className = getClassName(classId);

        // Generate a summary of the schedule for this class and section
        const morningSchedule = {};
        const afternoonSchedule = {};

        days.forEach(day => {
          morningSchedule[day] = [];
          afternoonSchedule[day] = [];
        });

        // Organize entries by day and session
        entries.forEach(entry => {
          if (entry.session === 'morning') {
            morningSchedule[entry.day].push(entry);
          } else {
            afternoonSchedule[entry.day].push(entry);
          }
        });

        // Sort entries by start time
        Object.keys(morningSchedule).forEach(day => {
          morningSchedule[day].sort((a, b) => {
            return convertTimeToMinutes(a.startTime) - convertTimeToMinutes(b.startTime);
          });
        });

        Object.keys(afternoonSchedule).forEach(day => {
          afternoonSchedule[day].sort((a, b) => {
            return convertTimeToMinutes(a.startTime) - convertTimeToMinutes(b.startTime);
          });
        });

        // 1. Notify teachers who teach this class and section
        const teacherIds = [...new Set(entries.map(entry => entry.teacherId))];
        for (const teacherId of teacherIds) {
          // Get teacher's subjects for this class
          const teacherSubjects = entries
            .filter(entry => entry.teacherId === teacherId)
            .map(entry => getSubjectName(entry.subjectId));

          // Create a detailed message for the teacher
          const subjectsList = [...new Set(teacherSubjects)].join(', ');

          // Create a detailed schedule summary for this teacher
          let scheduleDetails = `\n\nYour teaching schedule for ${className} - ${sectionName}:\n`;

          // Add days with classes
          const teacherEntries = entries.filter(entry => entry.teacherId === teacherId);
          const teacherScheduleByDay = {};

          days.forEach(day => {
            teacherScheduleByDay[day] = teacherEntries.filter(entry => entry.day === day);
          });

          Object.keys(teacherScheduleByDay).forEach(day => {
            if (teacherScheduleByDay[day].length > 0) {
              scheduleDetails += `\n${day}: `;
              teacherScheduleByDay[day].sort((a, b) => {
                return convertTimeToMinutes(a.startTime) - convertTimeToMinutes(b.startTime);
              });

              teacherScheduleByDay[day].forEach(entry => {
                scheduleDetails += `${entry.startTime}-${entry.endTime} (${getSubjectName(entry.subjectId)}), `;
              });

              // Remove trailing comma and space
              scheduleDetails = scheduleDetails.slice(0, -2);
            }
          });

          const teacherMessage = `You have been assigned to teach ${subjectsList} for ${className} - ${sectionName}. ${scheduleDetails}`;

          // Add notification to database
          await addDoc(collection(db, 'notifications'), {
            userId: teacherId,
            title: 'New Teaching Schedule Assigned',
            message: teacherMessage,
            type: 'timetable',
            priority: 'high',
            createdAt: new Date().toISOString(),
            read: false,
            data: {
              classId,
              sectionName,
              viewPath: 'TeacherClassSchedule'
            }
          });
        }

        // 2. Notify students in this class and section
        const studentsRef = collection(db, 'users');
        const studentsQuery = query(
          studentsRef,
          where('role', '==', 'student'),
          where('classId', '==', classId),
          where('section', '==', sectionName)
        );

        const studentsSnapshot = await getDocs(studentsQuery);

        // Create a detailed schedule summary for students
        let studentScheduleDetails = `\n\nYour class schedule for ${className} - ${sectionName}:\n`;

        days.forEach(day => {
          const dayEntries = [...morningSchedule[day], ...afternoonSchedule[day]];
          if (dayEntries.length > 0) {
            studentScheduleDetails += `\n${day}: `;
            dayEntries.sort((a, b) => {
              return convertTimeToMinutes(a.startTime) - convertTimeToMinutes(b.startTime);
            });

            dayEntries.forEach(entry => {
              studentScheduleDetails += `${entry.startTime}-${entry.endTime} (${getSubjectName(entry.subjectId)} - ${getTeacherName(entry.teacherId)}), `;
            });

            // Remove trailing comma and space
            studentScheduleDetails = studentScheduleDetails.slice(0, -2);
          }
        });

        studentsSnapshot.forEach(async (studentDoc) => {
          const studentId = studentDoc.id;

          // Add notification to database
          await addDoc(collection(db, 'notifications'), {
            userId: studentId,
            title: 'Your Class Schedule Updated',
            message: `The class schedule for ${className} - ${sectionName} has been updated. ${studentScheduleDetails}`,
            type: 'timetable',
            priority: 'high',
            createdAt: new Date().toISOString(),
            read: false,
            data: {
              classId,
              sectionName,
              viewPath: 'ScheduleManagement'
            }
          });
        });

        // 3. Notify parents of students in this class and section
        studentsSnapshot.forEach(async (studentDoc) => {
          const studentData = studentDoc.data();
          const parentId = studentData.parentId;
          const studentName = `${studentData.firstName || ''} ${studentData.lastName || ''}`.trim();

          if (parentId) {
            // Add notification to database
            await addDoc(collection(db, 'notifications'), {
              userId: parentId,
              title: 'Your Child\'s Class Schedule Updated',
              message: `The class schedule for ${studentName} (${className} - ${sectionName}) has been updated. ${studentScheduleDetails}`,
              type: 'timetable',
              priority: 'high',
              createdAt: new Date().toISOString(),
              read: false,
              data: {
                childId: studentDoc.id,
                classId,
                sectionName,
                viewPath: 'ParentClassSchedule'
              }
            });
          }
        });
      }

      // Send push notifications to all users
      const usersRef = collection(db, 'users');
      const usersSnapshot = await getDocs(usersRef);

      usersSnapshot.forEach(async (userDoc) => {
        const userData = userDoc.data();

        if (userData.expoPushToken) {
          try {
            await Notifications.scheduleNotificationAsync({
              content: {
                title: 'Class Schedule Updated',
                body: 'The school class schedule has been updated. Please check your schedule.',
                data: { type: 'timetable' },
              },
              trigger: null, // Send immediately
            });
          } catch (error) {
            console.error('Error sending push notification:', error);
          }
        }
      });

      console.log('All notifications sent successfully');

    } catch (error) {
      console.error('Error sending targeted notifications:', error);
      setError('Failed to send notifications. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Format date for display
  const formatDate = (dateString) => {
    return EthiopianCalendar.formatDate(new Date(dateString));
  };

  // Filter timetable entries based on selected filters
  const getFilteredTimetables = () => {
    return timetables.filter(entry => {
      // Filter by class
      if (filterClassId && entry.classId !== filterClassId) {
        return false;
      }

      // Filter by section
      if (filterSectionName && entry.sectionName !== filterSectionName) {
        return false;
      }

      // Filter by teacher
      if (filterTeacherId && entry.teacherId !== filterTeacherId) {
        return false;
      }

      // Filter by day
      if (filterDay !== 'all' && entry.day !== filterDay) {
        return false;
      }

      return true;
    });
  };

  // Get timetable entries for a specific class and section
  const getTimetableForClassSection = (classId, sectionName) => {
    if (!classId) return [];

    let filtered = timetables.filter(entry => entry.classId === classId);

    if (sectionName) {
      filtered = filtered.filter(entry => entry.sectionName === sectionName);
    }

    return filtered;
  };

  // Get timetable entries for a specific teacher
  const getTimetableForTeacher = (teacherId) => {
    if (!teacherId) return [];

    return timetables.filter(entry => entry.teacherId === teacherId);
  };

  // Group timetable entries by day and period for display
  const getTimetableByDay = () => {
    const filteredEntries = getFilteredTimetables();
    const timetableByDay = {};

    days.forEach(day => {
      timetableByDay[day] = [];
    });

    filteredEntries.forEach(entry => {
      if (timetableByDay[entry.day]) {
        timetableByDay[entry.day].push(entry);
      }
    });

    // Sort entries by period number
    Object.keys(timetableByDay).forEach(day => {
      timetableByDay[day].sort((a, b) => {
        return parseInt(a.periodNumber) - parseInt(b.periodNumber);
      });
    });

    return timetableByDay;
  };

  // Get all periods for a specific day
  const getPeriodsForDay = (day) => {
    const timetableByDay = getTimetableByDay();
    return timetableByDay[day] || [];
  };

  // Handle time picker
  const handleTimeChange = (field, selectedTime) => {
    // Format time as HH:MM
    const hours = selectedTime.getHours().toString().padStart(2, '0');
    const minutes = selectedTime.getMinutes().toString().padStart(2, '0');
    const timeString = `${hours}:${minutes}`;

    setFormData({
      ...formData,
      [field]: timeString
    });
  };

  // View detailed timetable for a specific class and section
  const [detailViewVisible, setDetailViewVisible] = useState(false);
  const [detailViewData, setDetailViewData] = useState({
    classId: '',
    sectionName: '',
    entries: []
  });

  // Open detailed view for a class and section
  const openDetailView = (classId, sectionName) => {
    const entries = getTimetableForClassSection(classId, sectionName);
    setDetailViewData({
      classId,
      sectionName,
      entries
    });
    setDetailViewVisible(true);
  };

  // Generate a weekly schedule view for a class and section
  const getWeeklyScheduleForClassSection = (classId, sectionName) => {
    const entries = getTimetableForClassSection(classId, sectionName);
    const weeklySchedule = {
      morning: {},
      afternoon: {}
    };

    // Initialize empty schedule for all days and periods
    days.forEach(day => {
      weeklySchedule.morning[day] = Array(morningTimeSlots.length).fill(null);
      weeklySchedule.afternoon[day] = Array(afternoonTimeSlots.length).fill(null);
    });

    // Fill in the schedule with entries
    entries.forEach(entry => {
      const session = entry.session;
      const day = entry.day;
      const timeSlots = session === 'morning' ? morningTimeSlots : afternoonTimeSlots;
      const periodIndex = timeSlots.findIndex(slot =>
        slot.start === entry.startTime && slot.end === entry.endTime
      );

      if (periodIndex !== -1) {
        weeklySchedule[session][day][periodIndex] = entry;
      }
    });

    return weeklySchedule;
  };

  return (
    <PaperProvider theme={DefaultTheme}>
      <StatusBar style="auto" />
      <SafeAreaView style={styles.safeArea}>
        {/* Sidebar */}
        <AdminSidebar
          drawerAnim={drawerAnim}
          activeSidebarItem={activeSidebarItem}
          setActiveSidebarItem={setActiveSidebarItem}
          toggleDrawer={toggleDrawer}
        />

        {/* Backdrop */}
        <SidebarBackdrop
          visible={drawerOpen}
          onPress={toggleDrawer}
          fadeAnim={backdropFadeAnim}
        />

        {/* Admin App Header */}
        <AdminAppHeader
          title={translate('dashboard.classSchedule') || 'Class Schedule'}
          onMenuPress={toggleDrawer}
        />

      <ScrollView style={styles.scrollView}>
        <Card style={styles.card}>
          <Card.Content>
            <Animatable.View animation="fadeInDown" duration={500}>
              <View style={styles.headerContainer}>
                <Title style={[styles.pageTitle, { color: '#1976d2' }]}>{translate('dashboard.classSchedule') || 'Class Schedule'}</Title>
                <View style={styles.headerButtons}>
                  <CustomButton
                    mode="contained"
                    onPress={() => {
                      setEditingTimetable(null);
                      resetFormData();
                      setModalVisible(true);
                    }}
                    style={styles.addButton}
                    icon="plus"
                  >
                    {translate('actions.addNew') || 'Add New'}
                  </CustomButton>
                  <CustomButton
                    mode="contained"
                    onPress={publishTimetable}
                    style={styles.publishButton}
                    icon="publish"
                  >
                    {translate('actions.publish') || 'Publish'}
                  </CustomButton>
                </View>
              </View>
            </Animatable.View>

          {/* Filter controls - Optimized for performance */}
          <Animatable.View animation="fadeInUp" duration={300} delay={50} style={styles.filterContainer}>
            <Surface style={styles.filterSurface}>
              <View style={styles.filterHeader}>
                <Title style={[styles.filterTitle, { color: '#1976d2' }]}>{translate('filters.title') || 'Filters'}</Title>
                <IconButton icon="filter-variant" size={24} color={'#1976d2'} />
              </View>

              <View style={styles.filterRow}>
                <View style={styles.filterItem}>
                  <Text style={styles.filterLabel}>{translate('filters.class')}</Text>
                  <View style={styles.pickerContainer}>
                    {loading ? (
                      <ActivityIndicator size="small" color="#1976d2" style={styles.pickerLoader} />
                    ) : (
                      <Picker
                        selectedValue={filterClassId}
                        onValueChange={(value) => {
                          setFilterClassId(value);
                          setFilterSectionName('');
                          if (value) fetchSectionsForClass(value);
                        }}
                        style={styles.filterPicker}
                      >
                        <Picker.Item label={translate('filters.allClasses') || "All Classes"} value="" />
                        {classes.map((cls) => (
                          <Picker.Item key={cls.id} label={cls.name} value={cls.id} />
                        ))}
                      </Picker>
                    )}
                  </View>
                </View>

                <View style={styles.filterItem}>
                  <Text style={styles.filterLabel}>{translate('filters.section')}</Text>
                  <View style={styles.pickerContainer}>
                    {loading && filterClassId ? (
                      <ActivityIndicator size="small" color="#1976d2" style={styles.pickerLoader} />
                    ) : (
                      <Picker
                        selectedValue={filterSectionName}
                        onValueChange={(value) => setFilterSectionName(value)}
                        style={styles.filterPicker}
                        enabled={!!filterClassId}
                      >
                        <Picker.Item label={translate('filters.allSections') || "All Sections"} value="" />
                        {sections.map((section) => (
                          <Picker.Item key={section.name} label={section.name} value={section.name} />
                        ))}
                      </Picker>
                    )}
                  </View>
                </View>
              </View>

              <View style={styles.filterRow}>
                <View style={styles.filterItem}>
                  <Text style={styles.filterLabel}>{translate('filters.teacher')}</Text>
                  <Picker
                    selectedValue={filterTeacherId}
                    onValueChange={(value) => setFilterTeacherId(value)}
                    style={styles.filterPicker}
                  >
                    <Picker.Item label={translate('filters.allTeachers') || "All Teachers"} value="" />
                    {teachers.map((teacher) => (
                      <Picker.Item key={teacher.id} label={teacher.fullName} value={teacher.id} />
                    ))}
                  </Picker>
                </View>

                <View style={styles.filterItem}>
                  <Text style={styles.filterLabel}>{translate('filters.day')}</Text>
                  <Picker
                    selectedValue={filterDay}
                    onValueChange={(value) => setFilterDay(value)}
                    style={styles.filterPicker}
                  >
                    <Picker.Item label={translate('filters.allDays') || "All Days"} value="all" />
                    {days.map((day) => (
                      <Picker.Item key={day} label={day} value={day} />
                    ))}
                  </Picker>
                </View>
              </View>

              <View style={styles.filterActions}>
                <Button
                  mode="outlined"
                  onPress={() => {
                    setFilterClassId('');
                    setFilterSectionName('');
                    setFilterTeacherId('');
                    setFilterDay('all');
                  }}
                  style={styles.filterButton}
                  icon="refresh"
                  disabled={loading}
                >
                  {translate('actions.reset') || "Reset"}
                </Button>
                <Button
                  mode="contained"
                  onPress={() => {
                    // Apply filters and refresh the view
                    fetchTimetables();
                  }}
                  style={styles.filterButton}
                  icon="magnify"
                  loading={loading}
                  disabled={loading}
                >
                  {translate('actions.apply') || "Apply"}
                </Button>
              </View>

              {error ? (
                <View style={styles.filterErrorContainer}>
                  <Text style={styles.filterErrorText}>{error}</Text>
                </View>
              ) : null}
            </Surface>
          </Animatable.View>

          {/* Weekly timetable view - Morning Session */}
          <Animatable.View animation="fadeInUp" duration={700} delay={200}>
            <View style={styles.sectionHeader}>
              <Title style={[styles.sectionTitle, { color: '#1976d2' }]}>{translate('timetable.morningSession') || 'Morning Session (8:00 - 12:30)'}</Title>
              <IconButton icon="weather-sunny" size={24} color={'#1976d2'} />
            </View>
            <Surface style={styles.timetableSurface}>
              <View style={styles.scrollableTableContainer}>
                <ScrollView horizontal>
                  <View>
                    <DataTable style={styles.timetableTable}>
                      <DataTable.Header style={styles.tableHeader}>
                        <DataTable.Title style={styles.periodColumn}>Period</DataTable.Title>
                        {days.map((day) => (
                          <DataTable.Title key={day} style={styles.dayColumn}>{day}</DataTable.Title>
                        ))}
                      </DataTable.Header>
                    </DataTable>

                    <ScrollView style={styles.timetableScrollView} nestedScrollEnabled={true}>
                      <DataTable style={styles.timetableTable}>
                        {/* Render morning periods as rows */}
                        {morningTimeSlots.map((timeSlot) => (
                          <DataTable.Row key={timeSlot.period + '-morning'} style={styles.tableRow}>
                            <DataTable.Cell style={styles.periodColumn}>
                              <Text style={styles.periodText}>Period {timeSlot.period}</Text>
                              <Text style={styles.timeText}>{timeSlot.start} - {timeSlot.end}</Text>
                            </DataTable.Cell>

                            {/* Render cells for each day */}
                            {days.map((day) => {
                              // Find timetable entry for this day and period - optimized with filtering
                              const entry = getFilteredTimetables().find(e =>
                                e.day === day &&
                                e.startTime === timeSlot.start &&
                                e.endTime === timeSlot.end &&
                                e.session === 'morning'
                              );

                              return (
                                <DataTable.Cell key={day} style={styles.dayColumn}>
                                  {entry ? (
                                    <View style={[styles.scheduleCell, { borderLeftColor: '#2196F3' }]}>
                                      <Text style={styles.classText}>
                                        {getClassName(entry.classId)} - {entry.sectionName}
                                      </Text>
                                      <Text style={styles.subjectText}>
                                        {getSubjectName(entry.subjectId)}
                                      </Text>
                                      <Text style={styles.teacherText}>
                                        {getTeacherName(entry.teacherId)}
                                      </Text>
                                      <Text style={styles.roomText}>Room: {entry.roomNumber}</Text>
                                      <View style={styles.cellActions}>
                                        <IconButton
                                          icon="pencil"
                                          size={16}
                                          color={'#1976d2'}
                                          style={styles.cellActionButton}
                                          onPress={() => {
                                            setEditingTimetable(entry);
                                            setFormData(entry);
                                            setModalVisible(true);
                                          }}
                                        />
                                        <IconButton
                                          icon="delete"
                                          size={16}
                                          color="#F44336"
                                          style={styles.cellActionButton}
                                          onPress={() => handleDelete(entry.id)}
                                        />
                                      </View>
                                    </View>
                                  ) : (
                                    <CustomButton
                                      mode="text"
                                      onPress={() => {
                                        resetFormData();
                                        handleTimeSlotSelect(timeSlot);
                                        setFormData(prev => ({ ...prev, day }));
                                        setModalVisible(true);
                                      }}
                                      style={styles.addPeriodButton}
                                    >
                                      + Add
                                    </CustomButton>
                                  )}
                                </DataTable.Cell>
                              );
                            })}
                          </DataTable.Row>
                        ))}
                      </DataTable>
                    </ScrollView>
                  </View>
                </ScrollView>
              </View>
            </Surface>
          </Animatable.View>

          {/* Weekly timetable view - Afternoon Session */}
          <Animatable.View animation="fadeInUp" duration={700} delay={300}>
            <View style={styles.sectionHeader}>
              <Title style={[styles.sectionTitle, { color: '#1976d2' }]}>{translate('timetable.afternoonSession') || 'Afternoon Session (12:30 - 5:00)'}</Title>
              <IconButton icon="weather-night" size={24} color={'#1976d2'} />
            </View>
            <Surface style={styles.timetableSurface}>
              <View style={styles.scrollableTableContainer}>
                <ScrollView horizontal>
                  <View>
                    <DataTable style={styles.timetableTable}>
                      <DataTable.Header style={styles.tableHeader}>
                        <DataTable.Title style={styles.periodColumn}>Period</DataTable.Title>
                        {days.map((day) => (
                          <DataTable.Title key={day} style={styles.dayColumn}>{day}</DataTable.Title>
                        ))}
                      </DataTable.Header>
                    </DataTable>

                    <ScrollView style={styles.timetableScrollView} nestedScrollEnabled={true}>
                      <DataTable style={styles.timetableTable}>
                        {/* Render afternoon periods as rows */}
                        {afternoonTimeSlots.map((timeSlot) => (
                          <DataTable.Row key={timeSlot.period + '-afternoon'} style={styles.tableRow}>
                            <DataTable.Cell style={styles.periodColumn}>
                              <Text style={styles.periodText}>Period {timeSlot.period}</Text>
                              <Text style={styles.timeText}>{timeSlot.start} - {timeSlot.end}</Text>
                            </DataTable.Cell>

                            {/* Render cells for each day */}
                            {days.map((day) => {
                              // Find timetable entry for this day and period - optimized with filtering
                              const entry = getFilteredTimetables().find(e =>
                                e.day === day &&
                                e.startTime === timeSlot.start &&
                                e.endTime === timeSlot.end &&
                                e.session === 'afternoon'
                              );

                              return (
                                <DataTable.Cell key={day} style={styles.dayColumn}>
                                  {entry ? (
                                    <View style={[styles.scheduleCell, { borderLeftColor: '#673AB7' }]}>
                                      <Text style={styles.classText}>
                                        {getClassName(entry.classId)} - {entry.sectionName}
                                      </Text>
                                      <Text style={styles.subjectText}>
                                        {getSubjectName(entry.subjectId)}
                                      </Text>
                                      <Text style={styles.teacherText}>
                                        {getTeacherName(entry.teacherId)}
                                      </Text>
                                      <Text style={styles.roomText}>Room: {entry.roomNumber}</Text>
                                      <View style={styles.cellActions}>
                                        <IconButton
                                          icon="pencil"
                                          size={16}
                                          color={'#1976d2'}
                                          style={styles.cellActionButton}
                                          onPress={() => {
                                            setEditingTimetable(entry);
                                            setFormData(entry);
                                            setModalVisible(true);
                                          }}
                                        />
                                        <IconButton
                                          icon="delete"
                                          size={16}
                                          color="#F44336"
                                          style={styles.cellActionButton}
                                          onPress={() => handleDelete(entry.id)}
                                        />
                                      </View>
                                    </View>
                                  ) : (
                                    <CustomButton
                                      mode="text"
                                      onPress={() => {
                                        resetFormData();
                                        handleTimeSlotSelect(timeSlot);
                                        setFormData(prev => ({ ...prev, day }));
                                        setModalVisible(true);
                                      }}
                                      style={styles.addPeriodButton}
                                    >
                                      + Add
                                    </CustomButton>
                                  )}
                                </DataTable.Cell>
                              );
                            })}
                          </DataTable.Row>
                        ))}
                      </DataTable>
                    </ScrollView>
                  </View>
                </ScrollView>
              </View>
            </Surface>
          </Animatable.View>

          {/* List view of timetable entries */}
          <Animatable.View animation="fadeInUp" duration={700} delay={300}>
            <View style={styles.sectionHeader}>
              <Title style={[styles.sectionTitle, { color: '#1976d2' }]}>{translate('timetable.allEntries') || 'All Schedule Entries'}</Title>
              <IconButton icon="format-list-bulleted" size={24} color={'#1976d2'} />
            </View>
            <Surface style={styles.timetableSurface}>
              <ScrollView horizontal>
                <View style={styles.scrollableTableContainer}>
                  <DataTable>
                    <DataTable.Header style={styles.tableHeader}>
                      <DataTable.Title style={styles.tableCell}>Day</DataTable.Title>
                      <DataTable.Title style={styles.tableCell}>Session</DataTable.Title>
                      <DataTable.Title style={styles.tableCell}>Time</DataTable.Title>
                      <DataTable.Title style={styles.tableCell}>Class</DataTable.Title>
                      <DataTable.Title style={styles.tableCell}>Subject</DataTable.Title>
                      <DataTable.Title style={styles.tableCell}>Teacher</DataTable.Title>
                      <DataTable.Title style={styles.tableCell}>Room</DataTable.Title>
                      <DataTable.Title style={styles.tableCell}>Actions</DataTable.Title>
                    </DataTable.Header>
                  </DataTable>

                  <ScrollView style={styles.tableScrollView} nestedScrollEnabled={true}>
                    <DataTable>
                      {getFilteredTimetables().map((entry) => (
                        <DataTable.Row key={entry.id} style={styles.tableRow}>
                          <DataTable.Cell style={styles.tableCell}>{entry.day}</DataTable.Cell>
                          <DataTable.Cell style={styles.tableCell}>
                            <Chip
                              mode="outlined"
                              style={{
                                backgroundColor: entry.session === 'morning' ? '#E3F2FD' : '#EDE7F6',
                                borderColor: entry.session === 'morning' ? '#2196F3' : '#673AB7',
                              }}
                              textStyle={{
                                color: entry.session === 'morning' ? '#2196F3' : '#673AB7',
                                fontSize: 12
                              }}
                              icon={entry.session === 'morning' ? 'weather-sunny' : 'weather-night'}
                            >
                              {entry.session === 'morning' ? 'Morning' : 'Afternoon'}
                            </Chip>
                          </DataTable.Cell>
                          <DataTable.Cell style={styles.tableCell}>{entry.startTime} - {entry.endTime}</DataTable.Cell>
                          <DataTable.Cell style={styles.tableCell}>
                            <TouchableRipple onPress={() => openDetailView(entry.classId, entry.sectionName)}>
                              <View>
                                <Text style={styles.classSectionText}>
                                  {getClassName(entry.classId)} - {entry.sectionName}
                                </Text>
                                <Text style={styles.viewDetailText}>View Details</Text>
                              </View>
                            </TouchableRipple>
                          </DataTable.Cell>
                          <DataTable.Cell style={styles.tableCell}>
                            {getSubjectName(entry.subjectId)}
                          </DataTable.Cell>
                          <DataTable.Cell style={styles.tableCell}>
                            {getTeacherName(entry.teacherId)}
                          </DataTable.Cell>
                          <DataTable.Cell style={styles.tableCell}>{entry.roomNumber}</DataTable.Cell>
                          <DataTable.Cell style={styles.tableCell}>
                            <View style={styles.actionButtons}>
                              <CustomButton
                                mode="outlined"
                                onPress={() => {
                                  setEditingTimetable(entry);
                                  setFormData(entry);
                                  setModalVisible(true);
                                }}
                                style={styles.actionButton}
                              >
                                Edit
                              </CustomButton>
                              <CustomButton
                                mode="outlined"
                                onPress={() => handleDelete(entry.id)}
                                style={[styles.actionButton, styles.deleteButton]}
                              >
                                Delete
                              </CustomButton>
                            </View>
                          </DataTable.Cell>
                        </DataTable.Row>
                      ))}
                    </DataTable>
                  </ScrollView>
                </View>
              </ScrollView>
            </Surface>
          </Animatable.View>
        </Card.Content>
      </Card>

      {/* Add/Edit Timetable Entry Modal */}
      <Portal>
        <Modal
          visible={modalVisible}
          onDismiss={() => setModalVisible(false)}
          contentContainerStyle={styles.modalContent}
        >
          <ScrollView>
            <Title>{editingTimetable ? 'Edit Schedule Entry' : 'Add New Schedule Entry'}</Title>

            {/* Day selection */}
            <View style={styles.pickerContainer}>
              <Text style={styles.pickerLabel}>Day</Text>
              <Picker
                selectedValue={formData.day}
                onValueChange={(value) => setFormData({ ...formData, day: value })}
                style={styles.picker}
              >
                <Picker.Item label="Select Day" value="" />
                {days.map((day) => (
                  <Picker.Item key={day} label={day} value={day} />
                ))}
              </Picker>
            </View>

            {/* Time selection */}
            <View style={styles.timeContainer}>
              <View style={styles.timeField}>
                <Text style={styles.pickerLabel}>Start Time</Text>
                <EthiopianTimePicker
                  value={formData.startTime ? new Date(`2023-01-01T${formData.startTime}:00`) : new Date()}
                  onChange={(selectedTime) => handleTimeChange('startTime', selectedTime)}
                  label="Select Start Time"
                  is24Hour={true}
                />
              </View>

              <View style={styles.timeField}>
                <Text style={styles.pickerLabel}>End Time</Text>
                <EthiopianTimePicker
                  value={formData.endTime ? new Date(`2023-01-01T${formData.endTime}:00`) : new Date()}
                  onChange={(selectedTime) => handleTimeChange('endTime', selectedTime)}
                  label="Select End Time"
                  is24Hour={true}
                />
              </View>
            </View>

            {/* Period number */}
            <TextInput
              label="Period Number"
              value={formData.periodNumber}
              onChangeText={(text) => setFormData({ ...formData, periodNumber: text })}
              keyboardType="numeric"
              style={styles.input}
            />

            {/* Session selection */}
            <View style={styles.pickerContainer}>
              <Text style={styles.pickerLabel}>Session</Text>
              <Picker
                selectedValue={formData.session}
                onValueChange={(value) => setFormData({ ...formData, session: value })}
                style={styles.picker}
              >
                <Picker.Item label="Select Session" value="" />
                <Picker.Item label="Morning (8:00 - 12:30)" value="morning" />
                <Picker.Item label="Afternoon (12:30 - 5:00)" value="afternoon" />
              </Picker>
            </View>

            {/* Predefined time slots */}
            <Text style={styles.sectionLabel}>Select Predefined Time Slot:</Text>
            <View style={styles.sessionTabsContainer}>
              <SegmentedButtons
                value={formData.session || 'morning'}
                onValueChange={(value) => setFormData({ ...formData, session: value })}
                buttons={[
                  { value: 'morning', label: 'Morning', icon: 'weather-sunny' },
                  { value: 'afternoon', label: 'Afternoon', icon: 'weather-night' },
                ]}
                style={styles.sessionTabs}
              />
            </View>
            <ScrollView horizontal style={styles.timeSlotContainer}>
              {(formData.session === 'morning' ? morningTimeSlots : afternoonTimeSlots).map((slot) => (
                <Chip
                  key={slot.period}
                  selected={formData.startTime === slot.start && formData.endTime === slot.end}
                  onPress={() => handleTimeSlotSelect(slot)}
                  style={[
                    styles.timeSlotChip,
                    {
                      backgroundColor: formData.startTime === slot.start ?
                        (formData.session === 'morning' ? '#E3F2FD' : '#EDE7F6') :
                        '#f0f0f0'
                    }
                  ]}
                >
                  Period {slot.period}: {slot.start} - {slot.end}
                </Chip>
              ))}
            </ScrollView>

            {/* Class selection */}
            <View style={styles.pickerContainer}>
              <Text style={styles.pickerLabel}>Class</Text>
              <Picker
                selectedValue={formData.classId}
                onValueChange={(value) => {
                  setFormData({ ...formData, classId: value, sectionName: '' });
                  if (value) fetchSectionsForClass(value);
                }}
                style={styles.picker}
              >
                <Picker.Item label="Select Class" value="" />
                {classes.map((cls) => (
                  <Picker.Item key={cls.id} label={cls.name} value={cls.id} />
                ))}
              </Picker>
            </View>

            {/* Section selection */}
            <View style={styles.pickerContainer}>
              <Text style={styles.pickerLabel}>Section</Text>
              <Picker
                selectedValue={formData.sectionName}
                onValueChange={(value) => setFormData({ ...formData, sectionName: value })}
                style={styles.picker}
                enabled={!!formData.classId}
              >
                <Picker.Item label="Select Section" value="" />
                {sections.map((section) => (
                  <Picker.Item key={section.name} label={section.name} value={section.name} />
                ))}
              </Picker>
            </View>

            {/* Subject selection */}
            <View style={styles.pickerContainer}>
              <Text style={styles.pickerLabel}>Subject</Text>
              <Picker
                selectedValue={formData.subjectId}
                onValueChange={(value) => setFormData({ ...formData, subjectId: value })}
                style={styles.picker}
              >
                <Picker.Item label="Select Subject" value="" />
                {subjects.map((subject) => (
                  <Picker.Item key={subject.id} label={subject.name} value={subject.id} />
                ))}
              </Picker>
            </View>

            {/* Teacher selection */}
            <View style={styles.pickerContainer}>
              <Text style={styles.pickerLabel}>Teacher</Text>
              <Picker
                selectedValue={formData.teacherId}
                onValueChange={(value) => setFormData({ ...formData, teacherId: value })}
                style={styles.picker}
              >
                <Picker.Item label="Select Teacher" value="" />
                {teachers.map((teacher) => (
                  <Picker.Item key={teacher.id} label={teacher.fullName} value={teacher.id} />
                ))}
              </Picker>
            </View>

            {/* Room selection */}
            <View style={styles.pickerContainer}>
              <Text style={styles.pickerLabel}>Room</Text>
              <Picker
                selectedValue={formData.roomNumber}
                onValueChange={(value) => setFormData({ ...formData, roomNumber: value })}
                style={styles.picker}
              >
                <Picker.Item label="Select Room" value="" />
                {rooms.map((room) => (
                  <Picker.Item key={room.number} label={room.number} value={room.number} />
                ))}
              </Picker>
            </View>

            {/* Error message */}
            {error ? <Text style={styles.errorText}>{error}</Text> : null}

            {/* Action buttons */}
            <View style={styles.buttonContainer}>
              <CustomButton
                mode="outlined"
                onPress={() => setModalVisible(false)}
                style={[styles.button, styles.cancelButton]}
              >
                Cancel
              </CustomButton>
              <CustomButton
                mode="contained"
                onPress={handleSubmit}
                style={[styles.button, styles.saveButton]}
                loading={loading}
              >
                Save
              </CustomButton>
            </View>
          </ScrollView>
        </Modal>
      </Portal>

      {/* Detailed Timetable View Modal */}
      <Portal>
        <Modal
          visible={detailViewVisible}
          onDismiss={() => setDetailViewVisible(false)}
          contentContainerStyle={styles.detailModalContent}
        >
          <ScrollView>
            <View style={styles.detailModalHeader}>
              <Title style={styles.detailModalTitle}>
                {getClassName(detailViewData.classId)} - {detailViewData.sectionName} Schedule
              </Title>
              <IconButton
                icon="close"
                size={24}
                onPress={() => setDetailViewVisible(false)}
                style={styles.detailModalCloseButton}
              />
            </View>

            <Divider style={styles.detailModalDivider} />

            {/* Morning Session */}
            <View style={styles.detailSessionHeader}>
              <Title style={styles.detailSessionTitle}>Morning Session</Title>
              <IconButton icon="weather-sunny" size={24} color={'#1976d2'} />
            </View>

            <Surface style={styles.detailTimetableSurface}>
              <View style={styles.scrollableTableContainer}>
                <ScrollView horizontal>
                  <View>
                    <DataTable style={styles.detailTimetableTable}>
                      <DataTable.Header style={styles.tableHeader}>
                        <DataTable.Title style={styles.periodColumn}>Period</DataTable.Title>
                        {days.map((day) => (
                          <DataTable.Title key={day} style={styles.dayColumn}>{day}</DataTable.Title>
                        ))}
                      </DataTable.Header>
                    </DataTable>

                    <ScrollView style={styles.timetableScrollView} nestedScrollEnabled={true}>
                      <DataTable style={styles.detailTimetableTable}>
                        {detailViewData.classId && morningTimeSlots.map((timeSlot, index) => {
                          const weeklySchedule = getWeeklyScheduleForClassSection(
                            detailViewData.classId,
                            detailViewData.sectionName
                          );

                          return (
                            <DataTable.Row key={`morning-${index}`} style={styles.tableRow}>
                              <DataTable.Cell style={styles.periodColumn}>
                                <Text style={styles.periodText}>Period {timeSlot.period}</Text>
                                <Text style={styles.timeText}>{timeSlot.start} - {timeSlot.end}</Text>
                              </DataTable.Cell>

                              {days.map((day) => {
                                const entry = weeklySchedule.morning[day][index];

                                return (
                                  <DataTable.Cell key={day} style={styles.dayColumn}>
                                    {entry ? (
                                      <View style={[styles.scheduleCell, { borderLeftColor: '#2196F3' }]}>
                                        <Text style={styles.subjectText}>
                                          {getSubjectName(entry.subjectId)}
                                        </Text>
                                        <Text style={styles.teacherText}>
                                          {getTeacherName(entry.teacherId)}
                                        </Text>
                                        <Text style={styles.roomText}>Room: {entry.roomNumber}</Text>
                                      </View>
                                    ) : (
                                      <View style={styles.emptyCell}>
                                        <Text style={styles.emptyCellText}>Free</Text>
                                      </View>
                                    )}
                                  </DataTable.Cell>
                                );
                              })}
                            </DataTable.Row>
                          );
                        })}
                      </DataTable>
                    </ScrollView>
                  </View>
                </ScrollView>
              </View>
            </Surface>

            {/* Afternoon Session */}
            <View style={styles.detailSessionHeader}>
              <Title style={styles.detailSessionTitle}>Afternoon Session</Title>
              <IconButton icon="weather-night" size={24} color={'#673AB7'} />
            </View>

            <Surface style={styles.detailTimetableSurface}>
              <View style={styles.scrollableTableContainer}>
                <ScrollView horizontal>
                  <View>
                    <DataTable style={styles.detailTimetableTable}>
                      <DataTable.Header style={styles.tableHeader}>
                        <DataTable.Title style={styles.periodColumn}>Period</DataTable.Title>
                        {days.map((day) => (
                          <DataTable.Title key={day} style={styles.dayColumn}>{day}</DataTable.Title>
                        ))}
                      </DataTable.Header>
                    </DataTable>

                    <ScrollView style={styles.timetableScrollView} nestedScrollEnabled={true}>
                      <DataTable style={styles.detailTimetableTable}>
                        {detailViewData.classId && afternoonTimeSlots.map((timeSlot, index) => {
                          const weeklySchedule = getWeeklyScheduleForClassSection(
                            detailViewData.classId,
                            detailViewData.sectionName
                          );

                          return (
                            <DataTable.Row key={`afternoon-${index}`} style={styles.tableRow}>
                              <DataTable.Cell style={styles.periodColumn}>
                                <Text style={styles.periodText}>Period {timeSlot.period}</Text>
                                <Text style={styles.timeText}>{timeSlot.start} - {timeSlot.end}</Text>
                              </DataTable.Cell>

                              {days.map((day) => {
                                const entry = weeklySchedule.afternoon[day][index];

                                return (
                                  <DataTable.Cell key={day} style={styles.dayColumn}>
                                    {entry ? (
                                      <View style={[styles.scheduleCell, { borderLeftColor: '#673AB7' }]}>
                                        <Text style={styles.subjectText}>
                                          {getSubjectName(entry.subjectId)}
                                        </Text>
                                        <Text style={styles.teacherText}>
                                          {getTeacherName(entry.teacherId)}
                                        </Text>
                                        <Text style={styles.roomText}>Room: {entry.roomNumber}</Text>
                                      </View>
                                    ) : (
                                      <View style={styles.emptyCell}>
                                        <Text style={styles.emptyCellText}>Free</Text>
                                      </View>
                                    )}
                                  </DataTable.Cell>
                                );
                              })}
                            </DataTable.Row>
                          );
                        })}
                      </DataTable>
                    </ScrollView>
                  </View>
                </ScrollView>
              </View>
            </Surface>

            {/* Subject Summary */}
            <View style={styles.detailSummaryHeader}>
              <Title style={styles.detailSummaryTitle}>Subject Summary</Title>
              <IconButton icon="book-open-variant" size={24} color={'#1976d2'} />
            </View>

            <Surface style={styles.detailSummarySurface}>
              <View style={styles.scrollableTableContainer}>
                <ScrollView horizontal>
                  <View>
                    <DataTable>
                      <DataTable.Header style={styles.tableHeader}>
                        <DataTable.Title style={styles.summaryTableCell}>Subject</DataTable.Title>
                        <DataTable.Title style={styles.summaryTableCell}>Teacher</DataTable.Title>
                        <DataTable.Title style={styles.summaryTableCell} numeric>Periods/Week</DataTable.Title>
                      </DataTable.Header>
                    </DataTable>

                    <ScrollView style={styles.summaryScrollView} nestedScrollEnabled={true}>
                      <DataTable>
                        {detailViewData.classId && (() => {
                          const entries = getTimetableForClassSection(
                            detailViewData.classId,
                            detailViewData.sectionName
                          );

                          // Group by subject
                          const subjectSummary = {};
                          entries.forEach(entry => {
                            if (!subjectSummary[entry.subjectId]) {
                              subjectSummary[entry.subjectId] = {
                                subjectId: entry.subjectId,
                                teacherId: entry.teacherId,
                                count: 0
                              };
                            }
                            subjectSummary[entry.subjectId].count++;
                          });

                          return Object.values(subjectSummary).map(summary => (
                            <DataTable.Row key={summary.subjectId} style={styles.tableRow}>
                              <DataTable.Cell style={styles.summaryTableCell}>{getSubjectName(summary.subjectId)}</DataTable.Cell>
                              <DataTable.Cell style={styles.summaryTableCell}>{getTeacherName(summary.teacherId)}</DataTable.Cell>
                              <DataTable.Cell style={styles.summaryTableCell} numeric>{summary.count}</DataTable.Cell>
                            </DataTable.Row>
                          ));
                        })()}
                      </DataTable>
                    </ScrollView>
                  </View>
                </ScrollView>
              </View>
            </Surface>

            <View style={styles.detailModalActions}>
              <Button
                mode="contained"
                onPress={() => setDetailViewVisible(false)}
                style={styles.detailModalButton}
              >
                Close
              </Button>
            </View>
          </ScrollView>
        </Modal>
      </Portal>

      {/* Ethiopian Time Picker is used instead of DateTimePicker */}

      {/* Snackbar for notifications */}
      <Snackbar
        visible={snackbarVisible}
        onDismiss={() => setSnackbarVisible(false)}
        duration={3000}
        action={{
          label: 'Dismiss',
          onPress: () => setSnackbarVisible(false),
        }}
      >
        {snackbarMessage}
      </Snackbar>

      {/* Logout Dialog */}
      <Portal>
        <Dialog
          visible={logoutDialogVisible}
          onDismiss={() => setLogoutDialogVisible(false)}
          style={styles.dialog}
        >
          <Dialog.Title style={styles.dialogTitle}>{translate('logout') || 'Logout'}</Dialog.Title>
          <Dialog.Content>
            <Paragraph style={styles.dialogText}>{translate('logoutConfirmation') || 'Are you sure you want to logout?'}</Paragraph>
          </Dialog.Content>
          <Dialog.Actions>
            <Button
              mode="outlined"
              onPress={() => setLogoutDialogVisible(false)}
              style={styles.dialogButton}
            >
              {translate('cancel') || 'Cancel'}
            </Button>
            <Button
              mode="contained"
              onPress={() => navigation.navigate('Login')}
              style={[styles.dialogButton, { backgroundColor: '#1976d2' }]}
            >
              {translate('logout') || 'Logout'}
            </Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>
    </ScrollView>
  </SafeAreaView>
</PaperProvider>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  container: {
    flex: 1,
    padding: 16,
    paddingTop: 0,
  },
  scrollView: {
    flex: 1,
  },
  appbar: {
    backgroundColor: '#2196F3',
    elevation: 4,
  },
  appbarTitle: {
    color: 'white',
    fontWeight: 'bold',
  },
  appbarAction: {
    marginRight: 8,
  },
  dialog: {
    borderRadius: 8,
  },
  dialogTitle: {
    color: '#2196F3',
  },
  dialogText: {
    fontSize: 16,
    marginVertical: 8,
  },
  dialogButton: {
    marginHorizontal: 8,
  },


  card: {
    margin: 16,
    elevation: 4,
    borderRadius: 8,
  },
  pageTitle: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  headerButtons: {
    flexDirection: 'row',
    gap: 10,
  },
  addButton: {
    backgroundColor: '#2196F3',
    marginRight: 8,
  },
  publishButton: {
    backgroundColor: '#4CAF50',
  },
  filterContainer: {
    marginBottom: 20,
  },
  filterSurface: {
    padding: 16,
    borderRadius: 8,
    elevation: 4,
  },
  filterHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  filterTitle: {
    fontSize: 18,
  },
  filterRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  filterItem: {
    flex: 1,
    marginHorizontal: 5,
  },
  filterLabel: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
  },
  filterPicker: {
    backgroundColor: '#f5f5f5',
    borderRadius: 4,
    height: 40,
  },
  filterActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 16,
    gap: 8,
  },
  filterButton: {
    minWidth: 100,
  },
  pickerContainer: {
    position: 'relative',
    width: '100%',
  },
  pickerLoader: {
    position: 'absolute',
    right: 10,
    top: 10,
  },
  filterErrorContainer: {
    marginTop: 8,
    padding: 8,
    backgroundColor: '#ffebee',
    borderRadius: 4,
  },
  filterErrorText: {
    color: '#d32f2f',
    fontSize: 12,
  },
  timetableContainer: {
    marginBottom: 20,
  },
  timetableTable: {
    backgroundColor: '#fff',
    borderRadius: 8,
    elevation: 2,
  },
  periodColumn: {
    width: 100,
    backgroundColor: '#f0f0f0',
  },
  dayColumn: {
    width: 180,
    padding: 5,
  },
  periodText: {
    fontWeight: 'bold',
    fontSize: 14,
  },
  timeText: {
    fontSize: 12,
    color: '#666',
  },
  scheduleCell: {
    padding: 10,
    backgroundColor: '#e3f2fd',
    borderRadius: 8,
    minHeight: 100,
    elevation: 2,
    borderLeftWidth: 4,
    borderLeftColor: '#2196F3',
  },
  classText: {
    fontWeight: 'bold',
    fontSize: 14,
    marginBottom: 2,
  },
  subjectText: {
    fontSize: 14,
    color: '#333',
    marginBottom: 2,
  },
  teacherText: {
    fontSize: 12,
    color: '#666',
    marginBottom: 2,
  },
  roomText: {
    fontSize: 12,
    color: '#666',
    marginBottom: 2,
  },
  cellActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  cellActionButton: {
    margin: 0,
    padding: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.5)',
    borderRadius: 4,
  },
  addPeriodButton: {
    margin: 0,
    padding: 0,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 20,
    marginBottom: 10,
  },
  sectionTitle: {
    marginTop: 20,
    marginBottom: 10,
    fontSize: 18,
  },
  timetableSurface: {
    borderRadius: 8,
    elevation: 4,
    marginBottom: 20,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    marginHorizontal: 4,
  },
  deleteButton: {
    borderColor: '#ff5252',
  },
  modalContent: {
    backgroundColor: 'white',
    padding: 20,
    margin: 20,
    borderRadius: 8,
    maxHeight: '80%',
  },
  input: {
    marginBottom: 16,
  },
  pickerContainer: {
    marginBottom: 16,
  },
  pickerLabel: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
  },
  picker: {
    backgroundColor: '#f5f5f5',
    borderRadius: 4,
  },
  timeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  timeField: {
    flex: 1,
    marginHorizontal: 5,
  },
  sectionLabel: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  timeSlotContainer: {
    marginBottom: 16,
  },
  timeSlotChip: {
    marginRight: 8,
    marginBottom: 8,
  },
  errorText: {
    color: '#ff5252',
    marginBottom: 16,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: 8,
    marginTop: 16,
  },
  button: {
    minWidth: 100,
  },
  cancelButton: {
    borderColor: '#666',
  },
  saveButton: {
    backgroundColor: '#2196F3',
  },
  sessionTabsContainer: {
    marginBottom: 16,
  },
  sessionTabs: {
    marginBottom: 8,
  },
  // Detail view modal styles
  detailModalContent: {
    backgroundColor: 'white',
    padding: 20,
    margin: 20,
    borderRadius: 8,
    maxHeight: '90%',
    width: '90%',
    alignSelf: 'center',
  },
  detailModalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  detailModalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1976d2',
  },
  detailModalCloseButton: {
    margin: 0,
  },
  detailModalDivider: {
    marginBottom: 16,
  },
  detailSessionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 16,
    marginBottom: 8,
  },
  detailSessionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  detailTimetableSurface: {
    borderRadius: 8,
    elevation: 4,
    marginBottom: 20,
  },
  detailTimetableTable: {
    backgroundColor: '#fff',
    borderRadius: 8,
  },
  detailSummaryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 16,
    marginBottom: 8,
  },
  detailSummaryTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  detailSummarySurface: {
    borderRadius: 8,
    elevation: 4,
    marginBottom: 20,
  },
  detailModalActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 16,
    marginBottom: 8,
  },
  detailModalButton: {
    minWidth: 100,
    backgroundColor: '#1976d2',
  },
  emptyCell: {
    padding: 10,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    minHeight: 100,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyCellText: {
    color: '#9e9e9e',
    fontStyle: 'italic',
  },
  classSectionText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#1976d2',
  },
  viewDetailText: {
    fontSize: 12,
    color: '#4CAF50',
    marginTop: 4,
  },
  // Scrollable table styles
  scrollableTableContainer: {
    flexDirection: 'column',
    width: '100%',
  },
  tableHeader: {
    backgroundColor: '#f5f5f5',
  },
  tableScrollView: {
    maxHeight: 400,
  },
  timetableScrollView: {
    maxHeight: 300,
  },
  tableRow: {
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  tableCell: {
    minWidth: 120,
    paddingHorizontal: 10,
  },
  summaryTableCell: {
    minWidth: 150,
    paddingHorizontal: 10,
  },
  summaryScrollView: {
    maxHeight: 200,
  },
});

export default ClassSchedule;
