import React, { useState, useEffect } from 'react';
import { View, StyleSheet, FlatList, SafeAreaView, StatusBar, ScrollView } from 'react-native';
import {
  Appbar,
  Text,
  Button,
  ActivityIndicator,
  Chip,
  Divider,
  FAB,
  useTheme,
  Menu,
  IconButton,
  Searchbar,
  Portal,
  Dialog
} from 'react-native-paper';
import { useNotifications } from '../../context/NotificationContext';
import { useAuth } from '../../context/AuthContext';
import { useLanguage } from '../../context/LanguageContext';
import NotificationCard from '../../components/common/NotificationCard';
import * as Animatable from 'react-native-animatable';

const NotificationCenter = ({ navigation }) => {
  // No theme needed
  const { translate } = useLanguage();
  const { user } = useAuth();
  const {
    notifications,
    fetchUserNotifications,
    markAsRead,
    markAllAsRead,
    unreadCount
  } = useNotifications();

  // State variables
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [filterMenuVisible, setFilterMenuVisible] = useState(false);
  const [selectedFilter, setSelectedFilter] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredNotifications, setFilteredNotifications] = useState([]);
  const [confirmClearVisible, setConfirmClearVisible] = useState(false);

  // Fetch notifications on mount
  useEffect(() => {
    loadNotifications();
  }, []);

  // Apply filters when notifications, filter, or search changes
  useEffect(() => {
    applyFilters();
  }, [notifications, selectedFilter, searchQuery]);

  // Load notifications
  const loadNotifications = async () => {
    setLoading(true);
    await fetchUserNotifications();
    setLoading(false);
  };

  // Refresh notifications
  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchUserNotifications();
    setRefreshing(false);
  };

  // Apply filters and search
  const applyFilters = () => {
    let filtered = [...notifications];

    // Apply type filter
    if (selectedFilter !== 'all') {
      if (selectedFilter === 'unread') {
        filtered = filtered.filter(notification => !notification.read);
      } else if (selectedFilter === 'read') {
        filtered = filtered.filter(notification => notification.read);
      } else {
        filtered = filtered.filter(notification =>
          notification.type === selectedFilter ||
          notification.type?.startsWith(selectedFilter)
        );
      }
    }

    // Apply search
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(notification =>
        notification.title?.toLowerCase().includes(query) ||
        notification.body?.toLowerCase().includes(query)
      );
    }

    setFilteredNotifications(filtered);
  };

  // Handle notification press
  const handleNotificationPress = async (notification) => {
    // Mark as read if not already read
    if (!notification.read) {
      await markAsRead(notification.id);
    }

    // Handle navigation based on notification type
    if (notification.data && notification.data.viewPath) {
      navigation.navigate(notification.data.viewPath, notification.data.viewParams || {});
    } else {
      // Default navigation based on notification type
      switch (notification.type) {
        case 'grade_published':
          if (user.role === 'student') {
            navigation.navigate('StudentGrades');
          } else if (user.role === 'parent') {
            navigation.navigate('ParentChildGrades', {
              childId: notification.data?.studentId
            });
          }
          break;

        case 'grade_approval':
          if (user.role === 'teacher') {
            navigation.navigate('GradeManagement', {
              classId: notification.data?.classId,
              className: notification.data?.className,
              sectionName: notification.data?.sectionName,
              subject: notification.data?.subject,
              viewMode: 'results'
            });
          }
          break;

        case 'grade_rejection':
          if (user.role === 'teacher') {
            navigation.navigate('GradeManagement', {
              classId: notification.data?.classId,
              className: notification.data?.className,
              sectionName: notification.data?.sectionName,
              subject: notification.data?.subject,
              viewMode: 'submission'
            });
          }
          break;

        case 'grade_submission':
          if (user.role === 'admin') {
            navigation.navigate('AdminGradeApproval', {
              submissionId: notification.data?.submissionId
            });
          }
          break;

        case 'message':
          navigation.navigate('Messages');
          break;

        default:
          // No specific navigation for other types
          break;
      }
    }
  };

  // Get filter chip color
  const getFilterChipColor = (filter) => {
    return selectedFilter === filter ? '#1976d2' : '#ffffff';
  };

  // Get filter chip text color
  const getFilterChipTextColor = (filter) => {
    return selectedFilter === filter ? 'white' : '#333333';
  };

  // Render empty state
  const renderEmptyState = () => (
    <Animatable.View
      style={styles.emptyContainer}
      animation="fadeIn"
      duration={500}
    >
      <IconButton
        icon="bell-off"
        size={64}
        color={'#9e9e9e'}
      />
      <Text style={styles.emptyText}>
        {translate('notificationCenter.noNotifications') || 'No notifications found'}
      </Text>
      <Button
        mode="contained"
        onPress={handleRefresh}
        style={styles.refreshButton}
      >
        {translate('common.refresh') || 'Refresh'}
      </Button>
    </Animatable.View>
  );

  // Render header
  const renderHeader = () => (
    <View style={styles.headerContainer}>
      <Searchbar
        placeholder={translate('notificationCenter.search') || "Search notifications"}
        onChangeText={setSearchQuery}
        value={searchQuery}
        style={styles.searchBar}
      />

      <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.filtersContainer}>
        <Chip
          mode="flat"
          selected={selectedFilter === 'all'}
          onPress={() => setSelectedFilter('all')}
          style={[styles.filterChip, { backgroundColor: getFilterChipColor('all') }]}
          textStyle={{ color: getFilterChipTextColor('all') }}
        >
          {translate('notificationCenter.all') || 'All'}
        </Chip>

        <Chip
          mode="flat"
          selected={selectedFilter === 'unread'}
          onPress={() => setSelectedFilter('unread')}
          style={[styles.filterChip, { backgroundColor: getFilterChipColor('unread') }]}
          textStyle={{ color: getFilterChipTextColor('unread') }}
        >
          {translate('notificationCenter.unread') || 'Unread'}
          {unreadCount > 0 && ` (${unreadCount})`}
        </Chip>

        <Chip
          mode="flat"
          selected={selectedFilter === 'grade'}
          onPress={() => setSelectedFilter('grade')}
          style={[styles.filterChip, { backgroundColor: getFilterChipColor('grade') }]}
          textStyle={{ color: getFilterChipTextColor('grade') }}
        >
          {translate('notificationCenter.grades') || 'Grades'}
        </Chip>

        <Chip
          mode="flat"
          selected={selectedFilter === 'message'}
          onPress={() => setSelectedFilter('message')}
          style={[styles.filterChip, { backgroundColor: getFilterChipColor('message') }]}
          textStyle={{ color: getFilterChipTextColor('message') }}
        >
          {translate('notificationCenter.messages') || 'Messages'}
        </Chip>

        <Chip
          mode="flat"
          selected={selectedFilter === 'event'}
          onPress={() => setSelectedFilter('event')}
          style={[styles.filterChip, { backgroundColor: getFilterChipColor('event') }]}
          textStyle={{ color: getFilterChipTextColor('event') }}
        >
          {translate('notificationCenter.events') || 'Events'}
        </Chip>
      </ScrollView>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar backgroundColor={'#1976d2'} barStyle="light-content" />

      <Appbar.Header>
        <Appbar.BackAction onPress={() => navigation.goBack()} />
        <Appbar.Content
          title={translate('notificationCenter.title') || "Notifications"}
          subtitle={`${filteredNotifications.length} ${translate('notificationCenter.items') || 'items'}`}
        />
        <Menu
          visible={filterMenuVisible}
          onDismiss={() => setFilterMenuVisible(false)}
          anchor={
            <Appbar.Action
              icon="filter-variant"
              onPress={() => setFilterMenuVisible(true)}
            />
          }
        >
          <Menu.Item
            onPress={() => {
              setSelectedFilter('all');
              setFilterMenuVisible(false);
            }}
            title={translate('notificationCenter.all') || "All"}
          />
          <Menu.Item
            onPress={() => {
              setSelectedFilter('unread');
              setFilterMenuVisible(false);
            }}
            title={translate('notificationCenter.unread') || "Unread"}
          />
          <Menu.Item
            onPress={() => {
              setSelectedFilter('read');
              setFilterMenuVisible(false);
            }}
            title={translate('notificationCenter.read') || "Read"}
          />
          <Divider />
          <Menu.Item
            onPress={() => {
              setConfirmClearVisible(true);
              setFilterMenuVisible(false);
            }}
            title={translate('notificationCenter.markAllRead') || "Mark all as read"}
          />
        </Menu>
      </Appbar.Header>

      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={'#1976d2'} />
          <Text style={styles.loadingText}>
            {translate('common.loading') || 'Loading...'}
          </Text>
        </View>
      ) : (
        <>
          <FlatList
            data={filteredNotifications}
            keyExtractor={(item) => item.id}
            renderItem={({ item, index }) => (
              <NotificationCard
                notification={item}
                onPress={handleNotificationPress}
                animation="fadeIn"
                delay={index * 50}
                duration={300}
              />
            )}
            ListHeaderComponent={renderHeader}
            ListEmptyComponent={renderEmptyState}
            refreshing={refreshing}
            onRefresh={handleRefresh}
            contentContainerStyle={
              filteredNotifications.length === 0 ? { flex: 1 } : styles.listContent
            }
          />

          {unreadCount > 0 && (
            <FAB
              style={[styles.fab, { backgroundColor: '#1976d2' }]}
              icon="check-all"
              label={translate('notificationCenter.markAllRead') || "Mark all as read"}
              onPress={() => setConfirmClearVisible(true)}
            />
          )}
        </>
      )}

      {/* Confirmation Dialog */}
      <Portal>
        <Dialog
          visible={confirmClearVisible}
          onDismiss={() => setConfirmClearVisible(false)}
        >
          <Dialog.Title>
            {translate('notificationCenter.confirmMarkAllRead') || "Mark all as read?"}
          </Dialog.Title>
          <Dialog.Content>
            <Text>
              {translate('notificationCenter.confirmMarkAllReadMessage') ||
                "Are you sure you want to mark all notifications as read?"}
            </Text>
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={() => setConfirmClearVisible(false)}>
              {translate('common.cancel') || "Cancel"}
            </Button>
            <Button
              onPress={async () => {
                await markAllAsRead();
                setConfirmClearVisible(false);
              }}
            >
              {translate('common.confirm') || "Confirm"}
            </Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5'
  },
  headerContainer: {
    padding: 16,
    backgroundColor: '#ffffff'
  },
  searchBar: {
    marginBottom: 16,
    elevation: 2
  },
  filtersContainer: {
    flexDirection: 'row',
    marginBottom: 8
  },
  filterChip: {
    marginRight: 8
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center'
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16
  },
  emptyText: {
    fontSize: 16,
    color: '#666666',
    marginTop: 16,
    marginBottom: 24,
    textAlign: 'center'
  },
  refreshButton: {
    marginTop: 16
  },
  listContent: {
    paddingBottom: 80
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0
  }
});

export default NotificationCenter;
