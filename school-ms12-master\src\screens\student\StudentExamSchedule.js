import React, { useState, useEffect, useRef } from 'react';
import { View, ScrollView, StyleSheet, TouchableOpacity, Animated, RefreshControl } from 'react-native';
import { Card, Title, Text, Chip, Divider, useTheme, IconButton, ActivityIndicator, Searchbar, SegmentedButtons, Surface, Badge } from 'react-native-paper';
import { db, auth } from '../../config/firebase';
import { collection, query, getDocs, where, doc, getDoc, orderBy } from 'firebase/firestore';
import { useNavigation } from '@react-navigation/native';
import { useLanguage } from '../../context/LanguageContext';
import { useAuth } from '../../context/AuthContext';
import * as Animatable from 'react-native-animatable';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { Calendar } from 'react-native-calendars';
import { SafeAreaView } from 'react-native-safe-area-context';
import StudentAppHeader from '../../components/common/StudentAppHeader';

const StudentExamSchedule = () => {
  const navigation = useNavigation();
  // No theme needed
  const { translate, language, isRTL } = useLanguage();
  const { user } = useAuth();

  // State variables
  const [exams, setExams] = useState([]);
  const [subjects, setSubjects] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [viewMode, setViewMode] = useState('list'); // 'list', 'calendar', 'grid'
  const [calendarMarkedDates, setCalendarMarkedDates] = useState({});
  const [selectedDate, setSelectedDate] = useState(null);
  const [examTypeFilter, setExamTypeFilter] = useState('all');
  const [dateFilter, setDateFilter] = useState('all'); // 'all', 'upcoming', 'past'
  const [showFilters, setShowFilters] = useState(true);
  const [studentClass, setStudentClass] = useState(null);
  const [studentSection, setStudentSection] = useState('');

  // Animation
  const fadeAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Hide header in this screen as we're using our own
    navigation.setOptions({
      headerShown: false,
    });

    // Set today's date for calendar view
    try {
      const today = new Date().toISOString().split('T')[0];
      setSelectedDate(today);
    } catch (error) {
      console.error('Error setting today\'s date:', error);
      setSelectedDate('');
    }

    // Start fade-in animation
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 1000,
      useNativeDriver: true,
    }).start();

    // Fetch student information and exams
    fetchStudentInfo();
  }, [navigation]);

  // Fetch student information
  const fetchStudentInfo = async () => {
    try {
      setLoading(true);

      // Get student data
      const studentDoc = await getDoc(doc(db, 'users', auth.currentUser.uid));
      if (studentDoc.exists()) {
        const studentData = studentDoc.data();
        setStudentClass(studentData.classId || null);
        setStudentSection(studentData.section || '');

        // Fetch exams for this student's class and section
        if (studentData.classId && studentData.section) {
          console.log('Fetching exams for student:', studentData.firstName, 'Class:', studentData.classId, 'Section:', studentData.section);
          await fetchExams(studentData.classId, studentData.section);
        } else {
          console.log('Student missing classId or section:', studentData);
          setExams([]);
          setCalendarMarkedDates({});
        }
      } else {
        console.log('Student document does not exist');
        setExams([]);
        setCalendarMarkedDates({});
      }

      // Fetch subjects for reference
      await fetchSubjects();
    } catch (error) {
      console.error('Error fetching student info:', error);
      setExams([]);
      setCalendarMarkedDates({});
    } finally {
      setLoading(false);
    }
  };

  // Fetch exams for the student's class and section
  const fetchExams = async (classId, section) => {
    try {
      if (!classId || !section) {
        console.log('Missing classId or section:', { classId, section });
        setExams([]);
        setCalendarMarkedDates({});
        setLoading(false);
        setRefreshing(false);
        return;
      }

      setLoading(true);

      // Query exams for the student's class and section
      const examsRef = collection(db, 'exams');

      // First, get all published exams
      const publishedExamsQuery = query(
        examsRef,
        where('published', '==', true)
      );

      const publishedExamsSnapshot = await getDocs(publishedExamsQuery);

      // Filter locally for class and section to avoid compound query issues
      const examsData = [];
      publishedExamsSnapshot.forEach((doc) => {
        const examData = doc.data();
        if (examData.classId === classId && examData.section === section) {
          examsData.push({ id: doc.id, ...examData });
        }
      });

      // Sort by date
      examsData.sort((a, b) => {
        if (a.date < b.date) return -1;
        if (a.date > b.date) return 1;
        return 0;
      });

      setExams(examsData);

      // Update calendar marked dates
      updateCalendarMarkedDates(examsData);
    } catch (error) {
      console.error('Error fetching exams:', error);
      setExams([]);
      setCalendarMarkedDates({});
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Fetch subjects
  const fetchSubjects = async () => {
    try {
      const subjectsRef = collection(db, 'subjects');
      const querySnapshot = await getDocs(subjectsRef);

      const subjectsData = [];
      querySnapshot.forEach((doc) => {
        subjectsData.push({ id: doc.id, ...doc.data() });
      });

      setSubjects(subjectsData);
    } catch (error) {
      console.error('Error fetching subjects:', error);
    }
  };

  // Update calendar marked dates
  const updateCalendarMarkedDates = (examsList) => {
    const markedDates = {};

    try {
      // Get today's date safely
      let today = '';
      try {
        today = new Date().toISOString().split('T')[0];
      } catch (error) {
        console.error('Error getting today\'s date:', error);
      }

      examsList.forEach(exam => {
        if (!exam.date) return;

        const dateStr = exam.date;
        const isPast = today && dateStr < today;

        // Determine dot color based on exam status
        let dotColor = isPast ? '#4CAF50' : '#2196F3';

        // Add or update the marked date
        if (markedDates[dateStr]) {
          // Add another dot if the date already exists
          markedDates[dateStr].dots.push({
            key: exam.id,
            color: dotColor,
            selectedDotColor: 'white'
          });
        } else {
          // Create a new marked date
          markedDates[dateStr] = {
            dots: [{
              key: exam.id,
              color: dotColor,
              selectedDotColor: 'white'
            }],
            selected: dateStr === selectedDate,
            selectedColor: 'rgba(33, 150, 243, 0.1)'
          };
        }
      });
    } catch (error) {
      console.error('Error updating calendar marked dates:', error);
    }

    setCalendarMarkedDates(markedDates);
  };

  // Handle refresh
  const onRefresh = async () => {
    setRefreshing(true);
    if (studentClass) {
      await fetchExams(studentClass, studentSection);
    } else {
      await fetchStudentInfo();
    }
  };

  // Toggle view mode
  const toggleViewMode = (mode) => {
    setViewMode(mode);
  };

  // Toggle filters
  const toggleFilters = () => {
    setShowFilters(!showFilters);
  };

  // Filter exams
  const filteredExams = exams.filter(exam => {
    try {
      // Check if exam has required properties
      if (!exam || !exam.title || !exam.examType) {
        return false;
      }

      // Search query matching
      const matchesSearch =
        exam.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        exam.examType.toLowerCase().includes(searchQuery.toLowerCase()) ||
        getSubjectName(exam.subjectId).toLowerCase().includes(searchQuery.toLowerCase());

      // Exam type filter
      const matchesExamType = examTypeFilter === 'all' || exam.examType === examTypeFilter;

      // Date filter
      let matchesDate = true;
      if (dateFilter !== 'all' && exam.date) {
        let today = '';
        try {
          today = new Date().toISOString().split('T')[0];
        } catch (error) {
          console.error('Error getting today\'s date:', error);
        }

        if (today) {
          const examDate = exam.date;

          if (dateFilter === 'upcoming') {
            matchesDate = examDate >= today;
          } else if (dateFilter === 'past') {
            matchesDate = examDate < today;
          }
        }
      }

      return matchesSearch && matchesExamType && matchesDate;
    } catch (error) {
      console.error('Error filtering exam:', error, exam);
      return false;
    }
  });

  // Helper functions
  const getSubjectName = (subjectId) => {
    const subject = subjects.find(s => s.id === subjectId);
    return subject ? subject.name : 'N/A';
  };

  // Render exam card for calendar view
  const renderExamCard = (exam) => {
    const today = new Date().toISOString().split('T')[0];
    const isPast = exam.date < today;

    return (
      <Surface key={exam.id} style={styles.examCard} elevation={1}>
        <View style={styles.examCardContent}>
          <View style={styles.examCardHeader}>
            <Text style={styles.examCardTitle}>{exam.title}</Text>
            <Chip
              mode="flat"
              style={{
                backgroundColor: isPast ? '#E8F5E9' : '#E3F2FD',
                height: 24
              }}
              textStyle={{ fontSize: 10 }}
              icon={isPast ? 'check-circle' : 'clock-outline'}
            >
              {isPast
                ? (translate('examSchedule.completed') || "Completed")
                : (translate('examSchedule.scheduled') || "Scheduled")}
            </Chip>
          </View>

          <View style={styles.examCardDetails}>
            <View style={styles.examCardDetail}>
              <MaterialCommunityIcons name="book-open-variant" size={16} color="#666" />
              <Text style={styles.examCardDetailText}>{getSubjectName(exam.subjectId)}</Text>
            </View>

            <View style={styles.examCardDetail}>
              <MaterialCommunityIcons name="clock-outline" size={16} color="#666" />
              <Text style={styles.examCardDetailText}>{exam.startTime} ({exam.duration || '45'} min)</Text>
            </View>

            {exam.room && (
              <View style={styles.examCardDetail}>
                <MaterialCommunityIcons name="door" size={16} color="#666" />
                <Text style={styles.examCardDetailText}>{translate('examSchedule.room') || "Room"}: {exam.room}</Text>
              </View>
            )}

            {exam.totalMarks && (
              <View style={styles.examCardDetail}>
                <MaterialCommunityIcons name="star" size={16} color="#666" />
                <Text style={styles.examCardDetailText}>{translate('examSchedule.totalMarks') || "Total Marks"}: {exam.totalMarks}</Text>
              </View>
            )}

            {exam.passingMarks && (
              <View style={styles.examCardDetail}>
                <MaterialCommunityIcons name="check-circle" size={16} color="#666" />
                <Text style={styles.examCardDetailText}>{translate('examSchedule.passingMarks') || "Passing Marks"}: {exam.passingMarks}</Text>
              </View>
            )}
          </View>

          {exam.instructions && (
            <View style={styles.examCardInstructions}>
              <Text style={styles.examCardInstructionsTitle}>{translate('examSchedule.instructions') || "Instructions"}:</Text>
              <Text style={styles.examCardInstructionsText}>{exam.instructions}</Text>
            </View>
          )}
        </View>
      </Surface>
    );
  };

  // Render grid card
  const renderGridCard = (exam) => {
    const today = new Date().toISOString().split('T')[0];
    const isPast = exam.date < today;

    return (
      <Surface key={exam.id} style={styles.gridCard} elevation={2}>
        <View style={styles.gridCardContent}>
          <View style={styles.gridCardHeader}>
            <View style={styles.gridCardBadge}>
              <Badge
                size={24}
                style={{
                  backgroundColor: isPast ? '#4CAF50' : '#2196F3'
                }}
              >{exam.examType.charAt(0)}</Badge>
            </View>
            <Text style={styles.gridCardTitle} numberOfLines={1}>{exam.title}</Text>
          </View>

          <Divider style={styles.gridCardDivider} />

          <View style={styles.gridCardDetails}>
            <View style={styles.gridCardDetail}>
              <MaterialCommunityIcons name="book-open-variant" size={14} color="#666" />
              <Text style={styles.gridCardDetailText} numberOfLines={1}>{getSubjectName(exam.subjectId)}</Text>
            </View>

            <View style={styles.gridCardDetail}>
              <MaterialCommunityIcons name="calendar" size={14} color="#666" />
              <Text style={styles.gridCardDetailText}>{exam.date}</Text>
            </View>

            <View style={styles.gridCardDetail}>
              <MaterialCommunityIcons name="clock-outline" size={14} color="#666" />
              <Text style={styles.gridCardDetailText}>{exam.startTime}</Text>
            </View>
          </View>

          <View style={styles.gridCardFooter}>
            <Chip
              mode="flat"
              style={{
                backgroundColor: isPast ? '#E8F5E9' : '#E3F2FD',
                height: 24
              }}
              textStyle={{ fontSize: 10 }}
              icon={isPast ? 'check-circle' : 'clock-outline'}
            >
              {isPast
                ? (translate('examSchedule.completed') || "Completed")
                : (translate('examSchedule.scheduled') || "Scheduled")}
            </Chip>
          </View>
        </View>
      </Surface>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Student App Header */}
      <StudentAppHeader
        title={translate('examSchedule.title') || "Exam Schedule"}
      />

      {/* Main Content */}
      <Animated.View
        style={[
          styles.content,
          { opacity: fadeAnim }
        ]}
      >
        {/* Search Bar with Filter Toggle */}
        <Animatable.View animation="fadeIn" duration={500}>
          <View style={styles.searchContainer}>
            <Searchbar
              placeholder={translate('examSchedule.searchPlaceholder') || "Search exams..."}
              onChangeText={query => setSearchQuery(query)}
              value={searchQuery}
              style={styles.searchBar}
              icon="magnify"
              iconColor={'#1976d2'}
            />
            <IconButton
              icon={showFilters ? "filter-variant" : "filter-variant-plus"}
              size={24}
              style={styles.filterToggleButton}
              onPress={toggleFilters}
              color={'#1976d2'}
            />
          </View>
        </Animatable.View>

        {/* Filters */}
        {showFilters && (
          <Animatable.View
            animation={showFilters ? "fadeIn" : "fadeOut"}
            duration={300}
            style={styles.filterContainer}
          >
            <View style={styles.filterHeader}>
              <Text style={styles.filterTitle}>{translate('examSchedule.filters') || "Filters"}</Text>
              <IconButton
                icon="chevron-up"
                size={24}
                onPress={toggleFilters}
                color={'#1976d2'}
              />
            </View>

            {/* Exam Type Filter */}
            <Text style={styles.filterLabel}>{translate('examSchedule.examType') || "Exam Type"}</Text>
            <SegmentedButtons
              value={examTypeFilter}
              onValueChange={setExamTypeFilter}
              buttons={[
                { value: 'all', label: translate('common.all') || 'All' },
                { value: 'Midterm', label: translate('examSchedule.midterm') || 'Midterm' },
                { value: 'Final', label: translate('examSchedule.final') || 'Final' },
                { value: 'Quiz', label: translate('examSchedule.quiz') || 'Quiz' },
              ]}
              style={styles.segmentedButtons}
            />

            {/* Date Filter */}
            <Text style={styles.filterLabel}>{translate('examSchedule.date') || "Date"}</Text>
            <SegmentedButtons
              value={dateFilter}
              onValueChange={setDateFilter}
              buttons={[
                { value: 'all', label: translate('common.all') || 'All' },
                { value: 'upcoming', label: translate('examSchedule.upcoming') || 'Upcoming' },
                { value: 'past', label: translate('examSchedule.past') || 'Past' },
              ]}
              style={styles.segmentedButtons}
            />
          </Animatable.View>
        )}

        <ScrollView
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={['#1976d2']}
              tintColor={'#1976d2'}
            />
          }
        >
          <Animatable.View animation="fadeIn" duration={500} delay={200}>
            <Card style={styles.card}>
              <Card.Content>
                <View style={styles.cardHeader}>
                  <Title style={styles.cardTitle}>{translate('examSchedule.title') || "Exam Schedule"}</Title>
                  <View style={styles.cardActions}>
                    <SegmentedButtons
                      value={viewMode}
                      onValueChange={toggleViewMode}
                      buttons={[
                        { value: 'list', icon: 'format-list-bulleted', label: '' },
                        { value: 'calendar', icon: 'calendar-month', label: '' },
                        { value: 'grid', icon: 'grid', label: '' },
                      ]}
                      style={styles.viewToggle}
                    />
                    <Chip
                      icon="filter-variant"
                      mode="outlined"
                      style={styles.countChip}
                    >
                      {filteredExams.length} {translate('examSchedule.exams') || "exams"}
                    </Chip>
                  </View>
                </View>

                {loading ? (
                  <View style={styles.loadingContainer}>
                    <ActivityIndicator size="large" color={'#1976d2'} />
                    <Text style={styles.loadingText}>{translate('common.loading') || "Loading..."}</Text>
                  </View>
                ) : filteredExams.length === 0 ? (
                  <View style={styles.emptyContainer}>
                    <IconButton icon="calendar-alert" size={50} color={'#9e9e9e'} />
                    <Text style={styles.emptyText}>{translate('examSchedule.noExams') || "No exams found"}</Text>
                    <Text style={styles.emptySubtext}>{translate('examSchedule.tryDifferentFilters') || "Try different filters"}</Text>
                  </View>
                ) : viewMode === 'calendar' ? (
                  // Calendar View
                  <View style={styles.calendarContainer}>
                    <Calendar
                      markingType={'multi-dot'}
                      markedDates={calendarMarkedDates}
                      onDayPress={(day) => {
                        setSelectedDate(day.dateString);
                        // Update marked dates to highlight the selected date
                        updateCalendarMarkedDates(exams);
                      }}
                      theme={{
                        selectedDayBackgroundColor: '#1976d2',
                        todayTextColor: '#1976d2',
                        arrowColor: '#1976d2',
                      }}
                    />

                    {selectedDate && (
                      <View style={styles.selectedDateExams}>
                        <View style={styles.selectedDateHeader}>
                          <Text style={styles.selectedDateTitle}>
                            {translate('examSchedule.examsOn') || "Exams on"} {selectedDate}
                          </Text>
                        </View>

                        {filteredExams.filter(exam => exam.date === selectedDate).length > 0 ? (
                          filteredExams
                            .filter(exam => exam.date === selectedDate)
                            .map(exam => renderExamCard(exam))
                        ) : (
                          <View style={styles.noExamsOnDate}>
                            <Text style={styles.noExamsText}>
                              {translate('examSchedule.noExamsOnDate') || "No exams scheduled on this date"}
                            </Text>
                          </View>
                        )}
                      </View>
                    )}
                  </View>
                ) : viewMode === 'grid' ? (
                  // Grid View
                  <View style={styles.gridContainer}>
                    {filteredExams.map(exam => renderGridCard(exam))}
                  </View>
                ) : (
                  // List View (Default)
                  <View style={styles.listContainer}>
                    {filteredExams.map(exam => {
                      const today = new Date().toISOString().split('T')[0];
                      const isPast = exam.date < today;

                      return (
                        <Surface key={exam.id} style={styles.listItem} elevation={1}>
                          <View style={styles.listItemContent}>
                            <View style={styles.listItemHeader}>
                              <Text style={styles.listItemTitle}>{exam.title}</Text>
                              <Chip
                                mode="flat"
                                style={{
                                  backgroundColor: isPast ? '#E8F5E9' : '#E3F2FD',
                                  height: 24
                                }}
                                textStyle={{ fontSize: 10 }}
                                icon={isPast ? 'check-circle' : 'clock-outline'}
                              >
                                {isPast
                                  ? (translate('examSchedule.completed') || "Completed")
                                  : (translate('examSchedule.scheduled') || "Scheduled")}
                              </Chip>
                            </View>

                            <View style={styles.listItemDetails}>
                              <View style={styles.listItemDetail}>
                                <MaterialCommunityIcons name="book-open-variant" size={16} color="#666" />
                                <Text style={styles.listItemDetailText}>{getSubjectName(exam.subjectId)}</Text>
                              </View>

                              <View style={styles.listItemDetail}>
                                <MaterialCommunityIcons name="calendar" size={16} color="#666" />
                                <Text style={styles.listItemDetailText}>{exam.date}</Text>
                              </View>

                              <View style={styles.listItemDetail}>
                                <MaterialCommunityIcons name="clock-outline" size={16} color="#666" />
                                <Text style={styles.listItemDetailText}>{exam.startTime} ({exam.duration || '45'} min)</Text>
                              </View>

                              {exam.room && (
                                <View style={styles.listItemDetail}>
                                  <MaterialCommunityIcons name="door" size={16} color="#666" />
                                  <Text style={styles.listItemDetailText}>{translate('examSchedule.room') || "Room"}: {exam.room}</Text>
                                </View>
                              )}

                              {exam.totalMarks && (
                                <View style={styles.listItemDetail}>
                                  <MaterialCommunityIcons name="star" size={16} color="#666" />
                                  <Text style={styles.listItemDetailText}>{translate('examSchedule.totalMarks') || "Total Marks"}: {exam.totalMarks}</Text>
                                </View>
                              )}

                              {exam.passingMarks && (
                                <View style={styles.listItemDetail}>
                                  <MaterialCommunityIcons name="check-circle" size={16} color="#666" />
                                  <Text style={styles.listItemDetailText}>{translate('examSchedule.passingMarks') || "Passing Marks"}: {exam.passingMarks}</Text>
                                </View>
                              )}
                            </View>

                            {exam.instructions && (
                              <View style={styles.listItemInstructions}>
                                <Text style={styles.listItemInstructionsTitle}>{translate('examSchedule.instructions') || "Instructions"}:</Text>
                                <Text style={styles.listItemInstructionsText}>{exam.instructions}</Text>
                              </View>
                            )}
                          </View>
                        </Surface>
                      );
                    })}
                  </View>
                )}
              </Card.Content>
            </Card>
          </Animatable.View>
        </ScrollView>
      </Animated.View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  searchBar: {
    flex: 1,
    marginRight: 8,
    borderRadius: 8,
    elevation: 2,
  },
  filterToggleButton: {
    backgroundColor: 'white',
    borderRadius: 8,
    elevation: 2,
  },
  filterContainer: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    elevation: 2,
  },
  filterHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  filterTitle: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  filterLabel: {
    fontSize: 14,
    marginTop: 8,
    marginBottom: 4,
  },
  segmentedButtons: {
    marginBottom: 8,
  },
  card: {
    borderRadius: 8,
    marginBottom: 16,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  cardTitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  cardActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  viewToggle: {
    marginRight: 8,
  },
  countChip: {
    height: 30,
  },
  loadingContainer: {
    padding: 32,
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 8,
    color: '#666',
  },
  emptyContainer: {
    padding: 32,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 16,
    fontWeight: 'bold',
    marginTop: 8,
  },
  emptySubtext: {
    color: '#666',
    textAlign: 'center',
    marginTop: 4,
  },
  calendarContainer: {
    backgroundColor: 'white',
    borderRadius: 8,
    overflow: 'hidden',
  },
  selectedDateExams: {
    marginTop: 16,
  },
  selectedDateHeader: {
    marginBottom: 8,
  },
  selectedDateTitle: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  examCard: {
    marginBottom: 8,
    borderRadius: 8,
    overflow: 'hidden',
  },
  examCardContent: {
    padding: 16,
  },
  examCardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  examCardTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    flex: 1,
  },
  examCardDetails: {
    marginTop: 8,
  },
  examCardDetail: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  examCardDetailText: {
    marginLeft: 8,
    color: '#666',
  },
  examCardInstructions: {
    marginTop: 8,
    padding: 8,
    backgroundColor: '#f5f5f5',
    borderRadius: 4,
  },
  examCardInstructionsTitle: {
    fontWeight: 'bold',
    marginBottom: 4,
  },
  examCardInstructionsText: {
    color: '#666',
    fontSize: 12,
  },
  noExamsOnDate: {
    padding: 16,
    alignItems: 'center',
  },
  noExamsText: {
    color: '#666',
  },
  gridContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  gridCard: {
    width: '48%',
    marginBottom: 16,
    borderRadius: 8,
    overflow: 'hidden',
  },
  gridCardContent: {
    padding: 12,
  },
  gridCardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  gridCardBadge: {
    marginRight: 8,
  },
  gridCardTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    flex: 1,
  },
  gridCardDivider: {
    marginBottom: 8,
  },
  gridCardDetails: {
    marginBottom: 8,
  },
  gridCardDetail: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  gridCardDetailText: {
    marginLeft: 4,
    fontSize: 12,
    color: '#666',
    flex: 1,
  },
  gridCardFooter: {
    alignItems: 'flex-start',
  },
  listContainer: {
    marginTop: 8,
  },
  listItem: {
    marginBottom: 8,
    borderRadius: 8,
    overflow: 'hidden',
  },
  listItemContent: {
    padding: 16,
  },
  listItemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  listItemTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    flex: 1,
  },
  listItemDetails: {
    marginTop: 8,
  },
  listItemDetail: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  listItemDetailText: {
    marginLeft: 8,
    color: '#666',
  },
  listItemInstructions: {
    marginTop: 8,
    padding: 8,
    backgroundColor: '#f5f5f5',
    borderRadius: 4,
  },
  listItemInstructionsTitle: {
    fontWeight: 'bold',
    marginBottom: 4,
  },
  listItemInstructionsText: {
    color: '#666',
    fontSize: 12,
  },
});

export default StudentExamSchedule;