import * as Notifications from 'expo-notifications';
import { Platform } from 'react-native';

// Test function to check if Expo Notifications is working
export const testNotifications = async () => {
  try {
    // Check if we have permission
    const { status } = await Notifications.getPermissionsAsync();
    console.log('Notification permission status:', status);
    
    // Set notification handler
    Notifications.setNotificationHandler({
      handleNotification: async () => ({
        shouldShowAlert: true,
        shouldPlaySound: true,
        shouldSetBadge: true,
      }),
    });
    
    // Schedule a test notification
    const notificationId = await Notifications.scheduleNotificationAsync({
      content: {
        title: 'Test Notification',
        body: 'This is a test notification',
        data: { test: 'data' },
      },
      trigger: null, // null means send immediately
    });
    
    console.log('Test notification scheduled with ID:', notificationId);
    return true;
  } catch (error) {
    console.error('Error testing notifications:', error);
    return false;
  }
};

// Function to check if device is physical
export const isPhysicalDevice = async () => {
  try {
    const Device = await import('expo-device');
    return Device.isDevice;
  } catch (error) {
    console.error('Error checking device type:', error);
    return false;
  }
};

// Function to check if we can get a push token
export const testPushToken = async () => {
  try {
    const Constants = await import('expo-constants');
    const Device = await import('expo-device');
    
    // Check if device is physical
    if (!Device.isDevice) {
      console.log('Must use physical device for Push Notifications');
      return false;
    }
    
    // Try to get a push token
    const { status } = await Notifications.getPermissionsAsync();
    if (status !== 'granted') {
      console.log('Permission not granted for notifications');
      return false;
    }
    
    // Get push token
    const projectId = Constants.expoConfig?.extra?.eas?.projectId;
    if (!projectId) {
      console.log('No project ID found');
      return false;
    }
    
    const token = await Notifications.getExpoPushTokenAsync({
      projectId,
    });
    
    console.log('Push token:', token);
    return true;
  } catch (error) {
    console.error('Error testing push token:', error);
    return false;
  }
};
