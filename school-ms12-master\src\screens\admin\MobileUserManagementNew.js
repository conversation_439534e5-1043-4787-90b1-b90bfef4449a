import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  ActivityIndicator
} from 'react-native';
import {
  Text,
  Surface,
  Avatar,
  Chip,
  Searchbar,
  IconButton,
  Menu,
  Divider,
  Button,
  Portal,
  Modal,
  Title,
  Paragraph,
  List,
  Badge,
  ProgressBar,
  Snackbar,
  Dialog,
  FAB
} from 'react-native-paper';
import { db, auth } from '../../config/firebase';
import {
  collection,
  query,
  getDocs,
  doc,
  getDoc,
  updateDoc,
  deleteDoc,
  addDoc,
  orderBy,
  where,
  limit,
  writeBatch
} from 'firebase/firestore';
import { sendPasswordResetEmail } from 'firebase/auth';
import { useLanguage } from '../../context/LanguageContext';
import { useNotifications } from '../../context/NotificationContext';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import * as Animatable from 'react-native-animatable';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import MobileScreenWrapper from '../../components/common/MobileScreenWrapper';
import NetInfo from '@react-native-community/netinfo';
import mobileTheme from '../../theme/mobileTheme';
import { format, formatDistance } from 'date-fns';
import { SwipeListView } from 'react-native-swipe-list-view';

const { width } = Dimensions.get('window');

const MobileUserManagement = () => {
  const navigation = useNavigation();
  const { translate, isRTL } = useLanguage();
  const { showToast } = useNotifications();
  
  // Data state
  const [users, setUsers] = useState([]);
  const [filteredUsers, setFilteredUsers] = useState([]);
  const [userStats, setUserStats] = useState({
    total: 0,
    active: 0,
    inactive: 0,
    new: 0
  });
  
  // UI state
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [showUserDetailsModal, setShowUserDetailsModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [showFilterMenu, setShowFilterMenu] = useState(false);
  const [showSortMenu, setShowSortMenu] = useState(false);
  const [filterRole, setFilterRole] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');
  const [sortBy, setSortBy] = useState('name');
  const [sortDirection, setSortDirection] = useState('asc');
  
  // Confirmation dialogs
  const [showDeleteConfirmDialog, setShowDeleteConfirmDialog] = useState(false);
  const [showActivateConfirmDialog, setShowActivateConfirmDialog] = useState(false);
  const [showDeactivateConfirmDialog, setShowDeactivateConfirmDialog] = useState(false);
  const [showPasswordResetDialog, setShowPasswordResetDialog] = useState(false);
  const [passwordResetLoading, setPasswordResetLoading] = useState(false);
  
  // Network and error state
  const [isConnected, setIsConnected] = useState(true);
  const [errorMessage, setErrorMessage] = useState('');
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [lastUpdated, setLastUpdated] = useState(null);
  const [loadingProgress, setLoadingProgress] = useState(0);
  
  // Bottom navigation items
  const bottomNavItems = [
    { key: 'dashboard', icon: 'view-dashboard', label: 'common.dashboard', route: 'AdminDashboard' },
    { key: 'users', icon: 'account-group', label: 'userManagement.title', route: 'UserManagement' },
    { key: 'teachers', icon: 'teach', label: 'teacher.management.title', route: 'TeacherManagement' },
    { key: 'students', icon: 'account-school', label: 'student.title', route: 'StudentManagement' },
    { key: 'more', icon: 'dots-horizontal', label: 'common.more', route: 'AdminMore' }
  ];
  
  // FAB actions - optimized for mobile
  const fabActions = [
    {
      icon: 'account-plus',
      label: translate('userManagement.addUser'),
      onPress: () => navigation.navigate('UserRegistration'),
      style: { backgroundColor: mobileTheme.colors.admin }
    },
    {
      icon: 'filter-variant',
      label: translate('common.filter'),
      onPress: () => setShowFilterMenu(true),
      style: { backgroundColor: mobileTheme.colors.secondary }
    },
    {
      icon: 'sort',
      label: translate('common.sort'),
      onPress: () => setShowSortMenu(true),
      style: { backgroundColor: mobileTheme.colors.info }
    },
    {
      icon: 'refresh',
      label: translate('common.refresh'),
      onPress: onRefresh,
      style: { backgroundColor: mobileTheme.colors.success }
    }
  ];
