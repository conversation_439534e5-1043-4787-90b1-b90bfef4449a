import React from 'react';
import { StyleSheet } from 'react-native';
import { TextInput } from 'react-native-paper';

/**
 * A simple TextInput component that directly uses React Native Paper's TextInput
 * without any custom render functions
 */
const SimpleTextInput = ({ error, style, left, ...props }) => {
  return (
    <TextInput
      mode="outlined"
      style={[styles.input, style]}
      error={error}
      left={left ? <TextInput.Icon icon={() => left} /> : null}
      {...props}
    />
  );
};

const styles = StyleSheet.create({
  input: {
    marginVertical: 8,
    backgroundColor: '#fff',
  },
});

export default SimpleTextInput;
