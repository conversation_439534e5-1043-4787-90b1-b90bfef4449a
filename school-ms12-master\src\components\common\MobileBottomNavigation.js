import React, { useState } from 'react';
import { View, StyleSheet, TouchableOpacity, Dimensions } from 'react-native';
import { Surface, Text, Badge } from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { useLanguage } from '../../context/LanguageContext';
import * as Animatable from 'react-native-animatable';
import mobileTheme from '../../theme/mobileTheme';

/**
 * Mobile-optimized bottom navigation component
 * @param {Object} props - Component props
 * @param {Array} props.items - Navigation items with icon, label, route, and badge properties
 * @param {string} props.activeRoute - Currently active route
 * @param {Function} props.onChangeRoute - Function to call when route changes
 * @param {string} props.userRole - User role (admin, teacher, student, parent)
 */
const MobileBottomNavigation = ({ 
  items = [], 
  activeRoute, 
  onChangeRoute,
  userRole = 'admin'
}) => {
  const navigation = useNavigation();
  const { translate, getTextStyle, isRTL } = useLanguage();
  const [pressedItem, setPressedItem] = useState(null);
  
  // Get role-specific color
  const getRoleColor = () => {
    switch(userRole) {
      case 'admin': return mobileTheme.colors.admin;
      case 'teacher': return mobileTheme.colors.teacher;
      case 'student': return mobileTheme.colors.student;
      case 'parent': return mobileTheme.colors.parent;
      default: return mobileTheme.colors.primary;
    }
  };
  
  const handlePress = (item) => {
    setPressedItem(item.key);
    
    // Add a small delay for the animation
    setTimeout(() => {
      setPressedItem(null);
      
      if (item.route) {
        navigation.navigate(item.route);
      }
      
      if (onChangeRoute) {
        onChangeRoute(item.key);
      }
    }, 150);
  };
  
  return (
    <Surface style={[styles.container, isRTL && styles.containerRTL]}>
      {items.map((item, index) => {
        const isActive = activeRoute === item.key;
        const isPressed = pressedItem === item.key;
        
        return (
          <TouchableOpacity
            key={item.key}
            style={[
              styles.navItem,
              isActive && styles.activeNavItem
            ]}
            onPress={() => handlePress(item)}
            activeOpacity={0.7}
          >
            <Animatable.View
              animation={isPressed ? 'pulse' : undefined}
              style={styles.iconContainer}
            >
              <MaterialCommunityIcons
                name={item.icon}
                size={24}
                color={isActive ? getRoleColor() : mobileTheme.colors.textSecondary}
              />
              
              {item.badge > 0 && (
                <Badge
                  size={16}
                  style={styles.badge}
                >
                  {item.badge > 99 ? '99+' : item.badge}
                </Badge>
              )}
            </Animatable.View>
            
            <Text
              style={[
                styles.label,
                isActive && { color: getRoleColor() },
                getTextStyle({ fontSize: 12 })
              ]}
              numberOfLines={1}
            >
              {translate(item.label) || item.label}
            </Text>
          </TouchableOpacity>
        );
      })}
    </Surface>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    height: mobileTheme.mobile.bottomNavHeight,
    backgroundColor: mobileTheme.colors.surface,
    borderTopWidth: 1,
    borderTopColor: mobileTheme.colors.divider,
    elevation: 8,
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    zIndex: 1000,
  },
  containerRTL: {
    flexDirection: 'row-reverse',
  },
  navItem: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 4,
  },
  activeNavItem: {
    backgroundColor: 'rgba(25, 118, 210, 0.05)',
  },
  iconContainer: {
    position: 'relative',
    marginBottom: 2,
  },
  badge: {
    position: 'absolute',
    top: -4,
    right: -8,
    backgroundColor: mobileTheme.colors.notification,
  },
  label: {
    fontSize: 12,
    textAlign: 'center',
    color: mobileTheme.colors.textSecondary,
    marginTop: 2,
  },
});

export default MobileBottomNavigation;
