import React, { useEffect, useState } from 'react';
import { StyleSheet } from 'react-native';
import { Badge } from 'react-native-paper';
import { useNotifications } from '../../context/NotificationContext';
import mobileTheme from '../../theme/mobileTheme';

/**
 * Mobile-optimized notification badge component that displays the number of unread notifications
 * @param {Object} props - Component props
 * @param {Object} props.style - Additional styles
 */
const MobileNotificationBadge = ({ style }) => {
  const { notifications } = useNotifications();
  const [unreadCount, setUnreadCount] = useState(0);
  
  useEffect(() => {
    if (notifications && notifications.length > 0) {
      const count = notifications.filter(notification => !notification.read).length;
      setUnreadCount(count);
    } else {
      setUnreadCount(0);
    }
  }, [notifications]);
  
  if (unreadCount === 0) {
    return null;
  }
  
  return (
    <Badge
      size={16}
      style={[styles.badge, style]}
    >
      {unreadCount > 99 ? '99+' : unreadCount}
    </Badge>
  );
};

const styles = StyleSheet.create({
  badge: {
    backgroundColor: mobileTheme.colors.notification,
  },
});

export default MobileNotificationBadge;
