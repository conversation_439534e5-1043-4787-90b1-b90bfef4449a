import React, { useState, useEffect } from 'react';
import { View, StyleSheet } from 'react-native';
import { Card, Title, Text, Button, ActivityIndicator, Surface, useTheme } from 'react-native-paper';
import { useNavigation, useRoute } from '@react-navigation/native';
import { LinearGradient } from 'expo-linear-gradient';
import * as Animatable from 'react-native-animatable';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useLanguage } from '../../context/LanguageContext';
import EmailVerificationService from '../../services/EmailVerificationService';
import UserRegistrationService from '../../services/UserRegistrationService';

const EmailVerification = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { translate } = useLanguage();
  // No theme needed

  const [loading, setLoading] = useState(true);
  const [verified, setVerified] = useState(false);
  const [error, setError] = useState(null);
  const [message, setMessage] = useState('');
  const [verificationMethod, setVerificationMethod] = useState(null); // 'firebase' or 'custom'

  useEffect(() => {
    const verifyEmail = async () => {
      try {
        setLoading(true);

        // Get parameters from route params or query params
        const params = route.params || {};
        const queryParams = new URLSearchParams(window.location.search);

        // Check for Firebase oobCode (mode=verifyEmail)
        const oobCode = params.oobCode || queryParams.get('oobCode');
        const mode = params.mode || queryParams.get('mode');

        // Check for custom verification token
        const email = params.email || queryParams.get('email');
        const token = params.token || queryParams.get('token');
        const type = params.type || queryParams.get('type') || 'user';

        // Determine verification method
        if (oobCode && mode === 'verifyEmail') {
          // Firebase verification
          setVerificationMethod('firebase');

          try {
            // Verify with Firebase
            const result = await UserRegistrationService.verifyEmail(oobCode);

            if (result.valid) {
              setVerified(true);
              setMessage(result.message || translate('verification.success') || 'Email verified successfully');
            } else {
              setError(result.message || translate('verification.failed') || 'Email verification failed');
            }
          } catch (firebaseError) {
            console.error('Firebase verification error:', firebaseError);
            setError(translate('verification.failed') || 'Email verification failed');
          }
        } else if (email && token) {
          // Custom verification
          setVerificationMethod('custom');

          if (!email || !token) {
            setError(translate('verification.invalidLink') || 'Invalid verification link. Missing email or token.');
            return;
          }

          // Verify the token
          const result = await EmailVerificationService.verifyToken(email, token);

          if (result.valid) {
            setVerified(true);
            setMessage(result.message || translate('verification.success') || 'Email verified successfully');
          } else {
            setError(result.message || translate('verification.failed') || 'Email verification failed');
          }
        } else {
          // Invalid parameters
          setError(translate('verification.invalidLink') || 'Invalid verification link');
        }
      } catch (error) {
        console.error('Error verifying email:', error);
        setError(translate('verification.error') || 'An error occurred while verifying your email. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    verifyEmail();
  }, [route.params]);

  const handleContinue = () => {
    navigation.navigate('Login');
  };

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={['#1976d2', '#9c27b0']}
        style={styles.gradient}
      >
        <Animatable.View
          animation="fadeIn"
          duration={1000}
          style={styles.cardContainer}
        >
          <Surface style={styles.surface}>
            <Card style={styles.card}>
              <Card.Content style={styles.cardContent}>
                {loading ? (
                  <View style={styles.loadingContainer}>
                    <ActivityIndicator size="large" color="#1976d2" />
                    <Text style={styles.loadingText}>
                      {translate('verification.verifying') || 'Verifying your email...'}
                    </Text>
                  </View>
                ) : verified ? (
                  <Animatable.View
                    animation="bounceIn"
                    duration={1000}
                    style={styles.resultContainer}
                  >
                    <MaterialCommunityIcons
                      name="check-circle"
                      size={80}
                      color="#1976d2"
                    />
                    <Title style={[styles.title, { color: '#1976d2' }]}>
                      {translate('verification.success') || 'Email Verified!'}
                    </Title>
                    <Text style={styles.message}>
                      {message || translate('verification.successMessage') || 'Your email has been successfully verified.'}
                    </Text>
                    <Text style={styles.infoMessage}>
                      {verificationMethod === 'firebase'
                        ? (translate('verification.firebaseSuccessMessage') || 'Your account is now verified. You can now log in and access all features.')
                        : (translate('verification.customSuccessMessage') || 'Your email is now verified. You can proceed with your account setup.')}
                    </Text>
                    <Button
                      mode="contained"
                      onPress={handleContinue}
                      style={[styles.button, { backgroundColor: '#1976d2' }]}
                    >
                      {translate('verification.continue') || 'Continue to Login'}
                    </Button>
                  </Animatable.View>
                ) : (
                  <Animatable.View
                    animation="bounceIn"
                    duration={1000}
                    style={styles.resultContainer}
                  >
                    <MaterialCommunityIcons
                      name="close-circle"
                      size={80}
                      color="#B00020"
                    />
                    <Title style={[styles.title, { color: '#B00020' }]}>
                      {translate('verification.failed') || 'Verification Failed'}
                    </Title>
                    <Text style={[styles.errorMessage, { color: '#B00020' }]}>
                      {error || translate('verification.failedMessage') || 'We could not verify your email. The link may be invalid or expired.'}
                    </Text>
                    <Text style={styles.infoMessage}>
                      {verificationMethod === 'firebase'
                        ? (translate('verification.firebaseFailedMessage') || 'Please try clicking the link in your email again or request a new verification email.')
                        : (translate('verification.customFailedMessage') || 'Please check your email and try again or contact support if the problem persists.')}
                    </Text>
                    <Button
                      mode="contained"
                      onPress={handleContinue}
                      style={[styles.button, { backgroundColor: '#B00020' }]}
                    >
                      {translate('verification.tryAgain') || 'Back to Login'}
                    </Button>
                  </Animatable.View>
                )}
              </Card.Content>
            </Card>
          </Surface>
        </Animatable.View>
      </LinearGradient>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gradient: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cardContainer: {
    width: '90%',
    maxWidth: 500,
  },
  surface: {
    elevation: 8,
    borderRadius: 16,
  },
  card: {
    borderRadius: 16,
  },
  cardContent: {
    padding: 24,
    alignItems: 'center',
  },
  loadingContainer: {
    alignItems: 'center',
    padding: 24,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  resultContainer: {
    alignItems: 'center',
    padding: 16,
  },
  title: {
    fontSize: 24,
    marginTop: 16,
    fontWeight: 'bold',
  },
  message: {
    fontSize: 16,
    textAlign: 'center',
    marginVertical: 16,
  },
  infoMessage: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 16,
    fontStyle: 'italic',
    opacity: 0.8,
  },
  errorMessage: {
    fontSize: 16,
    textAlign: 'center',
    marginVertical: 16,
  },
  button: {
    marginTop: 24,
    paddingHorizontal: 24,
    borderRadius: 8,
  },
});

export default EmailVerification;
