import { useState, useEffect } from 'react';
import { collection, query, where, getDocs } from 'firebase/firestore';
import { db, auth } from '../config/firebase';
import { useAuth } from '../context/AuthContext';

/**
 * Custom hook to check user permissions
 * @returns {Object} Object containing permission check functions and states
 */
const usePermissions = () => {
  const { user } = useAuth();
  const [permissions, setPermissions] = useState({
    registration: false,
    grades: false,
    attendance: false,
    library: false,
    announcements: false,
    events: false,
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (user) {
      checkAllPermissions();
    }
  }, [user]);

  /**
   * Check all permissions for the current user
   */
  const checkAllPermissions = async () => {
    try {
      setLoading(true);
      setError(null);

      if (!user || !auth.currentUser) {
        setLoading(false);
        return;
      }

      const permissionsRef = collection(db, 'permissions');
      const q = query(
        permissionsRef,
        where('teacherId', '==', auth.currentUser.uid),
        where('status', '==', 'approved')
      );
      
      const querySnapshot = await getDocs(q);
      
      const userPermissions = {
        registration: false,
        grades: false,
        attendance: false,
        library: false,
        announcements: false,
        events: false,
      };

      querySnapshot.forEach((doc) => {
        const permissionData = doc.data();
        if (permissionData.type && userPermissions.hasOwnProperty(permissionData.type)) {
          userPermissions[permissionData.type] = true;
        }
      });

      setPermissions(userPermissions);
    } catch (error) {
      console.error('Error checking permissions:', error);
      setError('Failed to check permissions');
    } finally {
      setLoading(false);
    }
  };

  /**
   * Check if user has a specific permission
   * @param {string} permissionType - The type of permission to check
   * @returns {Promise<boolean>} Whether the user has the permission
   */
  const checkPermission = async (permissionType) => {
    try {
      if (!user || !auth.currentUser) {
        return false;
      }

      const permissionsRef = collection(db, 'permissions');
      const q = query(
        permissionsRef,
        where('teacherId', '==', auth.currentUser.uid),
        where('type', '==', permissionType),
        where('status', '==', 'approved')
      );
      
      const querySnapshot = await getDocs(q);
      return !querySnapshot.empty;
    } catch (error) {
      console.error(`Error checking ${permissionType} permission:`, error);
      return false;
    }
  };

  return {
    permissions,
    loading,
    error,
    checkPermission,
    checkAllPermissions,
    hasRegistrationPermission: permissions.registration,
    hasGradesPermission: permissions.grades,
    hasAttendancePermission: permissions.attendance,
    hasLibraryPermission: permissions.library,
    hasAnnouncementsPermission: permissions.announcements,
    hasEventsPermission: permissions.events,
  };
};

export default usePermissions;
