@echo off
echo Setting up Android environment...

echo Checking if scripts directory exists...
if not exist scripts\NUL (
  echo Creating scripts directory...
  mkdir scripts
)

echo Creating autolinking.gradle...
echo // This is a simple placeholder for autolinking.gradle > scripts\autolinking.gradle
echo // It's referenced in android/app/build.gradle >> scripts\autolinking.gradle
echo. >> scripts\autolinking.gradle
echo // This file is intentionally minimal to avoid build errors >> scripts\autolinking.gradle
echo // The actual autolinking is handled by Expo's built-in mechanisms >> scripts\autolinking.gradle
echo. >> scripts\autolinking.gradle
echo println "Using simplified autolinking.gradle" >> scripts\autolinking.gradle

echo Cleaning Android build...
cd android
call gradlew.bat clean --stacktrace
cd ..

echo Setup complete! You can now run the app with run-android.bat
