@echo off
echo ===== Running Android App Directly with React Native CLI =====

echo Installing required packages...
call npm install --save-dev @react-native-community/cli @react-native-community/cli-platform-android

echo Building and running Android app...
call npx react-native run-android --no-packager

if %ERRORLEVEL% NEQ 0 (
  echo Error building/running Android app
  exit /b %ERRORLEVEL%
)

echo Starting Metro bundler...
call npx react-native start

echo ===== Process completed =====
