import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import {
  Card,
  Title,
  Text,
  Button,
  DataTable,
  Chip,
  ActivityIndicator,
  Divider,
  ProgressBar,
  List,
  Surface,
  useTheme,
  Portal,
  Modal,
  IconButton
} from 'react-native-paper';
import { db, auth } from '../../config/firebase';
import {
  collection,
  query,
  where,
  getDocs,
  doc,
  getDoc
} from 'firebase/firestore';
import { useLanguage } from '../../context/LanguageContext';
import { LinearGradient } from 'expo-linear-gradient';
import * as Animatable from 'react-native-animatable';

const StudentResultsView = ({ classId: initialClassId, subject: initialSubject, sectionName: initialSectionName }) => {
  // No theme needed
  const { translate } = useLanguage();

  // State variables
  const [loading, setLoading] = useState(true);
  const [studentData, setStudentData] = useState(null);
  const [classData, setClassData] = useState(null);
  const [subjectScores, setSubjectScores] = useState({});
  const [rankings, setRankings] = useState({
    schoolRank: 0,
    classRank: 0,
    sectionRank: 0
  });
  const [error, setError] = useState(null);
  const [selectedAssessment, setSelectedAssessment] = useState(null);
  const [selectedSubject, setSelectedSubject] = useState(initialSubject || null);
  const [focusedClassId, setFocusedClassId] = useState(initialClassId || null);
  const [focusedSectionName, setFocusedSectionName] = useState(initialSectionName || null);
  const [assessmentModalVisible, setAssessmentModalVisible] = useState(false);

  // Fetch data on component mount
  useEffect(() => {
    fetchStudentResults();
  }, []);

  // Fetch student results
  const fetchStudentResults = async () => {
    try {
      setLoading(true);
      setError(null);

      if (!auth.currentUser) {
        setError('User not authenticated');
        setLoading(false);
        return;
      }

      const studentId = auth.currentUser.uid;

      // Fetch student data
      const studentRef = doc(db, 'users', studentId);
      const studentDoc = await getDoc(studentRef);

      if (!studentDoc.exists()) {
        setError('Student data not found');
        setLoading(false);
        return;
      }

      const student = studentDoc.data();
      setStudentData(student);

      // Fetch class data
      const targetClassId = focusedClassId || student.classId;

      if (!targetClassId) {
        setError('Student not assigned to a class');
        setLoading(false);
        return;
      }

      const classRef = doc(db, 'classes', targetClassId);
      const classDoc = await getDoc(classRef);

      if (!classDoc.exists()) {
        setError('Class data not found');
        setLoading(false);
        return;
      }

      const classData = classDoc.data();
      setClassData(classData);

      // Process grades
      const grades = classData.grades || {};
      const processedScores = {};
      let totalPoints = 0;
      let totalMaxPoints = 0;

      // Process each subject one by one
      for (const subject of Object.keys(grades)) {
        const subjectGrade = grades[subject];

        // Find this student's score
        const studentScore = subjectGrade.studentScores?.find(
          score => score.studentId === studentId
        );

        if (studentScore) {
          console.log(`Found student score for ${subject}:`, studentScore);

          // First try to get assessment scores from the scores collection
          let assessmentScores = [];
          try {
            console.log(`Fetching assessment scores for student ${studentId} in subject ${subject}`);

            // First priority: Try to get from scores collection
            const scoresRef = collection(db, 'scores');
            const scoresQuery = query(
              scoresRef,
              where('studentId', '==', studentId),
              where('subject', '==', subject),
              where('status', '==', 'approved')
            );

            const scoresSnapshot = await getDocs(scoresQuery);

            if (!scoresSnapshot.empty) {
              console.log(`Found ${scoresSnapshot.size} approved score documents for ${subject}`);

              scoresSnapshot.forEach(doc => {
                const scoreData = doc.data();
                assessmentScores.push({
                  assessmentId: scoreData.assessmentId,
                  assessmentTitle: scoreData.assessmentTitle || 'Assessment',
                  assessmentType: scoreData.assessmentType || 'Test',
                  score: scoreData.points || 0,
                  maxPoints: scoreData.maxPoints || 100,
                  description: scoreData.description || '',
                  date: scoreData.timestamp || null
                });
              });
            } else {
              console.log(`No approved score documents found for ${subject}, trying gradeSubmissions collection`);

              // Second priority: Try to get from gradeSubmissions collection
              const submissionsRef = collection(db, 'gradeSubmissions');
              const submissionsQuery = query(
                submissionsRef,
                where('subject', '==', subject),
                where('status', '==', 'approved')
              );

              const submissionsSnapshot = await getDocs(submissionsQuery);

              if (!submissionsSnapshot.empty) {
                console.log(`Found ${submissionsSnapshot.size} approved grade submissions for ${subject}`);

                for (const submissionDoc of submissionsSnapshot.docs) {
                  const submissionData = submissionDoc.data();

                  // Log the entire submission data for debugging
                  console.log(`Submission data structure:`, JSON.stringify(submissionData));

                  // Find this student in the submission
                  const studentData = submissionData.students?.find(
                    s => s.uid === studentId || s.id === studentId
                  );

                  if (studentData) {
                    console.log(`Found student data in submission:`, studentData);

                    // Get assessment definitions from the submission
                    const assessmentDefs = submissionData.assessments || [];
                    console.log(`Found ${assessmentDefs.length} assessment definitions in submission`);

                    // If the student has assessmentScores, use them
                    if (studentData.assessmentScores && Array.isArray(studentData.assessmentScores) && studentData.assessmentScores.length > 0) {
                      console.log(`Found ${studentData.assessmentScores.length} assessment scores in student data`);

                      // Process each assessment score
                      studentData.assessmentScores.forEach(score => {
                        // Find the assessment definition
                        const assessmentDef = assessmentDefs.find(
                          a => a.id === score.assessmentId
                        );

                        if (assessmentDef) {
                          assessmentScores.push({
                            assessmentId: score.assessmentId,
                            assessmentTitle: assessmentDef.title || 'Assessment',
                            assessmentType: assessmentDef.type || 'Test',
                            score: score.score || 0,
                            maxPoints: assessmentDef.points || 100,
                            description: assessmentDef.description || '',
                            date: submissionData.submittedAt || null
                          });
                        }
                      });
                    } else {
                      // If the student doesn't have assessmentScores, create them from the submission data
                      console.log(`No assessmentScores found in student data, creating from submission data`);

                      // Get the scores from the submission
                      const scores = submissionData.scores || {};
                      console.log(`Scores from submission:`, scores);
                      console.log(`Submission scores data:`, scores);

                      // Process each assessment definition
                      assessmentDefs.forEach(assessmentDef => {
                        console.log(`Processing assessment definition:`, assessmentDef);

                        // Look for this student's score for this assessment
                        const studentScoreData = scores[studentData.id]?.[assessmentDef.id];
                        console.log(`Student score data for assessment ${assessmentDef.id}:`, studentScoreData);

                        if (studentScoreData) {
                          assessmentScores.push({
                            assessmentId: assessmentDef.id,
                            assessmentTitle: assessmentDef.title || 'Assessment',
                            assessmentType: assessmentDef.type || 'Test',
                            score: parseFloat(studentScoreData.points) || 0,
                            maxPoints: parseFloat(assessmentDef.points) || 100,
                            description: assessmentDef.description || '',
                            date: submissionData.submittedAt || null
                          });
                          console.log(`Added assessment score for ${assessmentDef.title}`);
                        } else {
                          // Try to find the score directly in the submission data
                          console.log(`Looking for score in alternative locations...`);

                          // Check if there's a scores array in the submission
                          if (submissionData.studentScores && Array.isArray(submissionData.studentScores)) {
                            const studentScore = submissionData.studentScores.find(s => s.studentId === studentData.id);
                            if (studentScore) {
                              const assessmentScore = studentScore.assessmentScores?.find(a => a.assessmentId === assessmentDef.id);
                              if (assessmentScore) {
                                assessmentScores.push({
                                  assessmentId: assessmentDef.id,
                                  assessmentTitle: assessmentDef.title || 'Assessment',
                                  assessmentType: assessmentDef.type || 'Test',
                                  score: parseFloat(assessmentScore.score) || 0,
                                  maxPoints: parseFloat(assessmentDef.points) || 100,
                                  description: assessmentDef.description || '',
                                  date: submissionData.submittedAt || null
                                });
                                console.log(`Added assessment score from studentScores array for ${assessmentDef.title}`);
                              }
                            }
                          }

                          // Check if there's a direct scores field in the submission
                          if (!assessmentScores.some(a => a.assessmentId === assessmentDef.id)) {
                            // Try to find the score in the direct scores field
                            if (typeof submissionData.scores === 'object' && submissionData.scores !== null) {
                              // Iterate through all student IDs
                              Object.keys(submissionData.scores).forEach(sid => {
                                // Check if this is our student
                                if (sid === studentData.id || sid === studentId) {
                                  // Check if there's a score for this assessment
                                  const scoreData = submissionData.scores[sid][assessmentDef.id];
                                  if (scoreData) {
                                    assessmentScores.push({
                                      assessmentId: assessmentDef.id,
                                      assessmentTitle: assessmentDef.title || 'Assessment',
                                      assessmentType: assessmentDef.type || 'Test',
                                      score: parseFloat(scoreData.points) || 0,
                                      maxPoints: parseFloat(assessmentDef.points) || 100,
                                      description: assessmentDef.description || '',
                                      date: submissionData.submittedAt || null
                                    });
                                    console.log(`Added assessment score from direct scores field for ${assessmentDef.title}`);
                                  }
                                }
                              });
                            }
                          }
                        }
                      });
                    }

                    // If we found assessment scores, break out of the loop
                    if (assessmentScores.length > 0) {
                      break;
                    }
                  }
                }
              } else {
                console.log(`No approved grade submissions found for ${subject}, trying class grades collection`);

                // Try to fetch from the class grades collection
                try {
                  const classRef = doc(db, 'classes', student.classId);
                  const classDoc = await getDoc(classRef);

                  if (classDoc.exists()) {
                    const classData = classDoc.data();
                    console.log(`Class data:`, classData);

                    // Check if this subject has grades
                    if (classData.grades && classData.grades[subject]) {
                      const subjectGrades = classData.grades[subject];
                      console.log(`Subject grades from class:`, JSON.stringify(subjectGrades));

                      // Find this student's scores
                      const studentScore = subjectGrades.studentScores?.find(
                        s => s.studentId === studentId || s.uid === studentId
                      );

                      console.log(`Student score from class grades:`, JSON.stringify(studentScore));

                      if (studentScore) {
                        // Get assessment definitions
                        const assessmentDefs = subjectGrades.assessments || [];
                        console.log(`Assessment definitions from class grades:`, JSON.stringify(assessmentDefs));

                        // Check if the student has assessmentScores
                        if (studentScore.assessmentScores && Array.isArray(studentScore.assessmentScores) && studentScore.assessmentScores.length > 0) {
                          console.log(`Found ${studentScore.assessmentScores.length} assessment scores in student data:`, JSON.stringify(studentScore.assessmentScores));

                          // Process each assessment score
                          studentScore.assessmentScores.forEach(score => {
                            // Find the assessment definition
                            const assessmentDef = assessmentDefs.find(
                              a => a.id === score.assessmentId
                            ) || {};

                            assessmentScores.push({
                              assessmentId: score.assessmentId,
                              assessmentTitle: assessmentDef.title || score.assessmentTitle || 'Assessment',
                              assessmentType: assessmentDef.type || score.assessmentType || 'Test',
                              score: parseFloat(score.score) || 0,
                              maxPoints: parseFloat(assessmentDef.points || score.maxPoints) || 100,
                              description: assessmentDef.description || '',
                              date: subjectGrades.lastUpdated || null
                            });
                          });

                          console.log(`Added ${assessmentScores.length} assessment scores from class grades`);
                        } else {
                          // If there are no assessmentScores, create them from the assessment definitions
                          console.log(`No assessmentScores array found in student data, creating from assessment definitions`);

                          // Calculate scores based on the total score and assessment points
                          const totalScore = parseFloat(studentScore.totalScore) || 0;
                          const totalPoints = assessmentDefs.reduce((sum, a) => sum + (parseFloat(a.points) || 0), 0);

                          // If we have assessment definitions and a total score
                          if (assessmentDefs.length > 0 && totalScore > 0 && totalPoints > 0) {
                            console.log(`Creating scores from total score ${totalScore} and total points ${totalPoints}`);

                            // Distribute the total score proportionally across assessments
                            assessmentDefs.forEach(assessmentDef => {
                              const points = parseFloat(assessmentDef.points) || 0;
                              const proportion = points / totalPoints;
                              const score = totalScore * proportion;

                              assessmentScores.push({
                                assessmentId: assessmentDef.id,
                                assessmentTitle: assessmentDef.title || 'Assessment',
                                assessmentType: assessmentDef.type || 'Test',
                                score: score,
                                maxPoints: points,
                                description: assessmentDef.description || '',
                                date: subjectGrades.lastUpdated || null
                              });

                              console.log(`Added assessment score for ${assessmentDef.title}: ${score}/${points}`);
                            });
                          }
                        }
                      }
                    }
                  }
                } catch (error) {
                  console.error('Error fetching from class grades:', error);
                }

                // Fourth priority: Try to fetch from assessments collection as fallback
                if (assessmentScores.length === 0) {
                  console.log(`No scores found in class grades, trying assessments collection`);

                  const assessmentsRef = collection(db, 'assessments');
                  const assessmentsQuery = query(
                    assessmentsRef,
                    where('className', '==', student.className),
                    where('sectionName', '==', student.sectionName),
                    where('subject', '==', subject)
                  );

                  const assessmentsSnapshot = await getDocs(assessmentsQuery);

                  if (!assessmentsSnapshot.empty) {
                    console.log(`Found ${assessmentsSnapshot.size} assessment definitions for ${subject}`);

                    // Now we need to find the scores for these assessments
                    for (const assessmentDoc of assessmentsSnapshot.docs) {
                      const assessmentData = assessmentDoc.data();

                      // Look for this assessment in the studentScore.assessmentScores array
                      const assessmentScore = studentScore.assessmentScores?.find(
                        score => score.assessmentId === assessmentDoc.id
                      );

                      if (assessmentScore) {
                        assessmentScores.push({
                          assessmentId: assessmentDoc.id,
                          assessmentTitle: assessmentData.title || 'Assessment',
                          assessmentType: assessmentData.type || 'Test',
                          score: assessmentScore.score || 0,
                          maxPoints: assessmentData.points || 100,
                          description: assessmentData.description || '',
                          date: assessmentData.dueDate || null
                        });
                      }
                    }
                  }
                }
              }
            }
          } catch (error) {
            console.error('Error fetching assessment scores:', error);
          }

          // If we still don't have assessment scores, use the ones from studentScore as last resort
          if (assessmentScores.length === 0 && studentScore.assessmentScores && Array.isArray(studentScore.assessmentScores)) {
            console.log(`Using ${studentScore.assessmentScores.length} assessment scores from studentScore for ${subject}`);
            assessmentScores = studentScore.assessmentScores.map(score => ({
              ...score,
              assessmentId: score.assessmentId || `unknown-${Date.now()}`,
              assessmentTitle: score.assessmentTitle || 'Assessment',
              assessmentType: score.assessmentType || 'Test',
              score: score.score || 0,
              maxPoints: score.maxPoints || 100
            }));
          }

          // If we still don't have assessment scores but have a total score, create assessment scores
          if (assessmentScores.length === 0 && studentScore.totalScore) {
            console.log(`No assessment scores found but have total score ${studentScore.totalScore}, creating from subject data`);

            // Try to get assessment definitions from the subject
            const subjectRef = collection(db, 'subjects');
            const subjectQuery = query(
              subjectRef,
              where('name', '==', subject)
            );

            try {
              const subjectSnapshot = await getDocs(subjectQuery);

              if (!subjectSnapshot.empty) {
                const subjectData = subjectSnapshot.docs[0].data();
                console.log(`Found subject data:`, subjectData);

                // Check if the subject has assessment definitions
                if (subjectData.assessments && Array.isArray(subjectData.assessments) && subjectData.assessments.length > 0) {
                  console.log(`Found ${subjectData.assessments.length} assessment definitions in subject`);

                  // Calculate scores based on the total score and assessment points
                  const totalScore = parseFloat(studentScore.totalScore) || 0;
                  const totalPoints = subjectData.assessments.reduce((sum, a) => sum + (parseFloat(a.points) || 0), 0);

                  // If we have assessment definitions and a total score
                  if (totalPoints > 0 && totalScore > 0) {
                    console.log(`Creating scores from total score ${totalScore} and total points ${totalPoints}`);

                    // Distribute the total score proportionally across assessments
                    subjectData.assessments.forEach(assessmentDef => {
                      const points = parseFloat(assessmentDef.points) || 0;
                      const proportion = points / totalPoints;
                      const score = totalScore * proportion;

                      assessmentScores.push({
                        assessmentId: assessmentDef.id || `subject-${Date.now()}`,
                        assessmentTitle: assessmentDef.title || 'Assessment',
                        assessmentType: assessmentDef.type || 'Test',
                        score: score,
                        maxPoints: points,
                        description: assessmentDef.description || '',
                        date: new Date().toISOString()
                      });

                      console.log(`Added assessment score for ${assessmentDef.title}: ${score}/${points}`);
                    });
                  }
                }
              }
            } catch (error) {
              console.error('Error fetching subject data:', error);
            }
          }

          // If we still don't have assessment scores, add a placeholder
          if (assessmentScores.length === 0) {
            console.log(`No assessment scores found for ${subject}, adding placeholder`);

            // Add a placeholder assessment to indicate no real data is available
            assessmentScores.push({
              assessmentId: 'no-data',
              assessmentTitle: 'No Assessment Data',
              assessmentType: 'None',
              score: 0,
              maxPoints: 100,
              description: 'No assessment data is available for this subject. Please contact your teacher or administrator.',
              date: new Date().toISOString()
            });
          }

          // Get assessment details with more information
          const enhancedAssessmentScores = assessmentScores.map(assessment => {
            // Find the assessment definition in the subject's assessments array
            const assessmentDef = subjectGrade.assessments?.find(
              a => a.id === assessment.assessmentId
            );

            return {
              ...assessment,
              assessmentTitle: assessmentDef?.title || assessment.assessmentTitle || 'Unknown Assessment',
              assessmentType: assessmentDef?.type || assessment.assessmentType || 'Unknown Type',
              maxPoints: assessmentDef?.points || assessment.maxPoints || 100,
              description: assessmentDef?.description || assessment.description || '',
              date: assessmentDef?.date || assessment.date || null
            };
          });

          // Calculate the actual total score based on the assessment scores
          let calculatedTotalScore = 0;
          let totalMaxPoints = 0;

          enhancedAssessmentScores.forEach(assessment => {
            const score = parseFloat(assessment.score) || 0;
            const maxPoints = parseFloat(assessment.maxPoints) || 0;

            if (maxPoints > 0) {
              calculatedTotalScore += score;
              totalMaxPoints += maxPoints;
            }
          });

          // Calculate the percentage
          const percentage = totalMaxPoints > 0
            ? (calculatedTotalScore / totalMaxPoints) * 100
            : parseFloat(studentScore.totalScore) || 0;

          // Use the calculated percentage or fall back to the totalScore from studentScore
          const finalScore = isNaN(percentage)
            ? studentScore.totalScore
            : percentage.toFixed(1);

          console.log(`Calculated total score for ${subject}: ${finalScore} (original: ${studentScore.totalScore})`);

          processedScores[subject] = {
            totalScore: finalScore,
            assessmentScores: enhancedAssessmentScores,
            teacherName: subjectGrade.teacherName || 'Not Available'
          };

          // Add to total points
          const score = parseFloat(finalScore) || 0;
          totalPoints += score;
          totalMaxPoints += 100; // Assuming each subject is out of 100
        }
      }

      setSubjectScores(processedScores);

      // Calculate rankings
      await calculateRankings(studentId, student.className, student.sectionName);

    } catch (error) {
      console.error('Error fetching student results:', error);
      setError('Failed to load results: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  // Calculate rankings
  const calculateRankings = async (studentId, className, sectionName) => {
    try {
      // Fetch all students
      const usersRef = collection(db, 'users');
      const studentsQuery = query(usersRef, where('role', '==', 'student'));
      const studentsSnapshot = await getDocs(studentsQuery);

      if (studentsSnapshot.empty) {
        return;
      }

      const allStudents = [];
      const classStudents = [];
      const sectionStudents = [];

      // Process each student
      for (const studentDoc of studentsSnapshot.docs) {
        const student = studentDoc.data();

        if (!student.classId) continue;

        // Fetch class data for this student
        const classRef = doc(db, 'classes', student.classId);
        const classDoc = await getDoc(classRef);

        if (!classDoc.exists()) continue;

        const classData = classDoc.data();
        const grades = classData.grades || {};

        // Calculate total score
        let totalPoints = 0;
        let totalMaxPoints = 0;

        Object.keys(grades).forEach(subject => {
          const subjectGrade = grades[subject];

          // Find this student's score
          const studentScore = subjectGrade.studentScores?.find(
            score => score.studentId === studentDoc.id
          );

          if (studentScore) {
            const score = parseFloat(studentScore.totalScore) || 0;
            totalPoints += score;
            totalMaxPoints += 100;
          }
        });

        const totalPercentage = totalMaxPoints > 0 ? (totalPoints / totalMaxPoints) * 100 : 0;

        const studentWithScore = {
          id: studentDoc.id,
          name: student.name,
          className: student.className,
          sectionName: student.sectionName,
          totalPercentage
        };

        // Add to appropriate lists
        allStudents.push(studentWithScore);

        if (student.className === className) {
          classStudents.push(studentWithScore);

          if (student.sectionName === sectionName) {
            sectionStudents.push(studentWithScore);
          }
        }
      }

      // Sort students by total percentage (descending)
      allStudents.sort((a, b) => b.totalPercentage - a.totalPercentage);
      classStudents.sort((a, b) => b.totalPercentage - a.totalPercentage);
      sectionStudents.sort((a, b) => b.totalPercentage - a.totalPercentage);

      // Find ranks
      const schoolRank = allStudents.findIndex(s => s.id === studentId) + 1;
      const classRank = classStudents.findIndex(s => s.id === studentId) + 1;
      const sectionRank = sectionStudents.findIndex(s => s.id === studentId) + 1;

      setRankings({
        schoolRank: schoolRank > 0 ? schoolRank : 0,
        classRank: classRank > 0 ? classRank : 0,
        sectionRank: sectionRank > 0 ? sectionRank : 0
      });

    } catch (error) {
      console.error('Error calculating rankings:', error);
    }
  };

  // Get performance color
  const getPerformanceColor = (percentage) => {
    if (percentage >= 80) return '#4CAF50';
    if (percentage >= 70) return '#8BC34A';
    if (percentage >= 60) return '#FFC107';
    if (percentage >= 50) return '#FF9800';
    return '#F44336';
  };

  // Get color for assessment type
  const getAssessmentTypeColor = (type) => {
    const typeToLower = (type || '').toLowerCase();

    if (typeToLower.includes('quiz')) return '#E1BEE7'; // Light Purple
    if (typeToLower.includes('test')) return '#BBDEFB'; // Light Blue
    if (typeToLower.includes('exam')) return '#C8E6C9'; // Light Green
    if (typeToLower.includes('mid')) return '#FFE0B2'; // Light Orange
    if (typeToLower.includes('final')) return '#FFCDD2'; // Light Red
    if (typeToLower.includes('assignment')) return '#B3E5FC'; // Light Cyan
    if (typeToLower.includes('project')) return '#DCEDC8'; // Light Lime
    if (typeToLower.includes('homework')) return '#F5F5F5'; // Light Grey

    return '#E0E0E0'; // Default Grey
  };

  // Show assessment details
  const showAssessmentDetails = (assessment, subject) => {
    setSelectedAssessment(assessment);
    setSelectedSubject(subject);
    setAssessmentModalVisible(true);
  };

  // Close assessment details modal
  const closeAssessmentDetails = () => {
    setAssessmentModalVisible(false);
  };

  // Calculate percentage safely
  const calculatePercentage = (score, maxPoints) => {
    const scoreNum = parseFloat(score);
    const maxPointsNum = parseFloat(maxPoints);

    if (isNaN(scoreNum) || isNaN(maxPointsNum) || maxPointsNum === 0) {
      console.error('Invalid score or maxPoints:', score, maxPoints);
      return 0;
    }

    return (scoreNum / maxPointsNum) * 100;
  };

  // Get grade letter
  const getGradeLetter = (percentage) => {
    if (percentage >= 90) return 'A+';
    if (percentage >= 85) return 'A';
    if (percentage >= 80) return 'A-';
    if (percentage >= 75) return 'B+';
    if (percentage >= 70) return 'B';
    if (percentage >= 65) return 'B-';
    if (percentage >= 60) return 'C+';
    if (percentage >= 55) return 'C';
    if (percentage >= 50) return 'C-';
    if (percentage >= 45) return 'D+';
    if (percentage >= 40) return 'D';
    return 'F';
  };

  // Calculate total percentage
  const calculateTotalPercentage = () => {
    const subjects = Object.keys(subjectScores);
    if (subjects.length === 0) return 0;

    let total = 0;
    subjects.forEach(subject => {
      total += parseFloat(subjectScores[subject].totalScore) || 0;
    });

    return total / subjects.length;
  };

  // Get rank badge style
  const getRankBadgeStyle = (rank) => {
    if (rank === 1) return styles.firstRankBadge;
    if (rank === 2) return styles.secondRankBadge;
    if (rank === 3) return styles.thirdRankBadge;
    if (rank <= 10) return styles.topTenBadge;
    return styles.normalRankBadge;
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#1976d2" />
        <Text style={styles.loadingText}>Loading your results...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>{error}</Text>
        <Button mode="contained" onPress={fetchStudentResults}>
          Retry
        </Button>
      </View>
    );
  }

  const totalPercentage = calculateTotalPercentage();
  const gradeLetter = getGradeLetter(totalPercentage);

  return (
    <ScrollView style={styles.container}>
      {/* Assessment Details Modal */}
      <Portal>
        <Modal
          visible={assessmentModalVisible}
          onDismiss={closeAssessmentDetails}
          contentContainerStyle={styles.modalContainer}
        >
          {selectedAssessment && (
            <Card>
              <Card.Title
                title={selectedAssessment.assessmentTitle}
                subtitle={`${selectedSubject} - ${selectedAssessment.assessmentType}`}
                right={(props) => (
                  <IconButton
                    {...props}
                    icon="close"
                    onPress={closeAssessmentDetails}
                  />
                )}
              />
              <Card.Content>
                <View style={styles.assessmentDetailRow}>
                  <Text style={styles.assessmentDetailLabel}>Score:</Text>
                  <Text style={[
                    styles.assessmentDetailValue,
                    { color: getPerformanceColor(calculatePercentage(selectedAssessment.score, selectedAssessment.maxPoints)) }
                  ]}>
                    {parseFloat(selectedAssessment.score).toFixed(1)} / {parseFloat(selectedAssessment.maxPoints).toFixed(1)}
                  </Text>
                </View>

                <View style={styles.assessmentDetailRow}>
                  <Text style={styles.assessmentDetailLabel}>Percentage:</Text>
                  <Text style={[
                    styles.assessmentDetailValue,
                    { color: getPerformanceColor(calculatePercentage(selectedAssessment.score, selectedAssessment.maxPoints)) }
                  ]}>
                    {calculatePercentage(selectedAssessment.score, selectedAssessment.maxPoints).toFixed(1)}%
                  </Text>
                </View>

                <View style={styles.assessmentDetailRow}>
                  <Text style={styles.assessmentDetailLabel}>Grade:</Text>
                  <Chip style={[
                    styles.gradeChip,
                    { backgroundColor: getPerformanceColor(calculatePercentage(selectedAssessment.score, selectedAssessment.maxPoints)) }
                  ]}>
                    {getGradeLetter(calculatePercentage(selectedAssessment.score, selectedAssessment.maxPoints))}
                  </Chip>
                </View>

                {selectedAssessment.date && (
                  <View style={styles.assessmentDetailRow}>
                    <Text style={styles.assessmentDetailLabel}>Date:</Text>
                    <Text style={styles.assessmentDetailValue}>
                      {new Date(selectedAssessment.date.seconds * 1000).toLocaleDateString()}
                    </Text>
                  </View>
                )}

                {selectedAssessment.description && (
                  <View style={styles.assessmentDetailSection}>
                    <Text style={styles.assessmentDetailLabel}>Description:</Text>
                    <Text style={styles.assessmentDescription}>
                      {selectedAssessment.description}
                    </Text>
                  </View>
                )}

                <ProgressBar
                  progress={parseFloat(selectedAssessment.score) / parseFloat(selectedAssessment.maxPoints)}
                  color={getPerformanceColor(calculatePercentage(selectedAssessment.score, selectedAssessment.maxPoints))}
                  style={styles.assessmentProgressBar}
                />
              </Card.Content>
              <Card.Actions>
                <Button onPress={closeAssessmentDetails}>Close</Button>
              </Card.Actions>
            </Card>
          )}
        </Modal>
      </Portal>

      <Animatable.View animation="fadeIn" duration={500}>
        <Card style={styles.summaryCard}>
          <LinearGradient
            colors={['#e3f2fd', '#bbdefb']}
            style={styles.gradientBackground}
          >
            <Card.Content>
              <Title style={styles.cardTitle}>Academic Performance Summary</Title>

              <View style={styles.performanceRow}>
                <View style={styles.performanceItem}>
                  <Text style={styles.performanceLabel}>Overall Percentage</Text>
                  <Text style={[styles.performanceValue, { color: getPerformanceColor(totalPercentage) }]}>
                    {totalPercentage.toFixed(2)}%
                  </Text>
                </View>

                <View style={styles.performanceItem}>
                  <Text style={styles.performanceLabel}>Grade</Text>
                  <Chip style={[styles.gradeChip, { backgroundColor: getPerformanceColor(totalPercentage) }]}>
                    {gradeLetter}
                  </Chip>
                </View>
              </View>

              <ProgressBar
                progress={totalPercentage / 100}
                color={getPerformanceColor(totalPercentage)}
                style={styles.progressBar}
              />
            </Card.Content>
          </LinearGradient>
        </Card>
      </Animatable.View>

      <Animatable.View animation="fadeInUp" duration={600} delay={100}>
        <Card style={styles.rankingCard}>
          <Card.Content>
            <Title style={styles.cardTitle}>Your Rankings</Title>

            <View style={styles.rankingsContainer}>
              <View style={styles.rankItem}>
                <Text style={styles.rankLabel}>School Rank</Text>
                <Chip style={getRankBadgeStyle(rankings.schoolRank)}>
                  {rankings.schoolRank || '-'}
                </Chip>
              </View>

              <View style={styles.rankItem}>
                <Text style={styles.rankLabel}>Class Rank</Text>
                <Chip style={getRankBadgeStyle(rankings.classRank)}>
                  {rankings.classRank || '-'}
                </Chip>
              </View>

              <View style={styles.rankItem}>
                <Text style={styles.rankLabel}>Section Rank</Text>
                <Chip style={getRankBadgeStyle(rankings.sectionRank)}>
                  {rankings.sectionRank || '-'}
                </Chip>
              </View>
            </View>
          </Card.Content>
        </Card>
      </Animatable.View>

      <Animatable.View animation="fadeInUp" duration={700} delay={200}>
        <Title style={styles.sectionTitle}>Subject Results</Title>

        {Object.keys(subjectScores).length === 0 ? (
          <Card style={styles.noDataCard}>
            <Card.Content>
              <Text style={styles.noDataText}>No subject results available yet.</Text>
            </Card.Content>
          </Card>
        ) : (
          Object.entries(subjectScores).map(([subject, data], index) => (
            <Animatable.View key={subject} animation="fadeIn" duration={500} delay={index * 100}>
              <Card style={styles.subjectCard}>
                <Card.Content>
                  <View style={styles.subjectHeader}>
                    <View style={styles.subjectTitleContainer}>
                      <Title style={styles.subjectTitle}>{subject}</Title>
                      <Text style={styles.subjectTeacher}>
                        {data.teacherName || 'Teacher: Not Available'}
                      </Text>
                    </View>
                    <View style={styles.subjectScoreContainer}>
                      <Chip
                        mode="outlined"
                        style={{ backgroundColor: getPerformanceColor(parseFloat(data.totalScore)) }}
                      >
                        {getGradeLetter(parseFloat(data.totalScore))}
                      </Chip>
                      <Text style={[styles.subjectScoreText, { color: getPerformanceColor(parseFloat(data.totalScore)) }]}>
                        {data.totalScore}%
                      </Text>
                    </View>
                  </View>

                  <ProgressBar
                    progress={parseFloat(data.totalScore) / 100}
                    color={getPerformanceColor(parseFloat(data.totalScore))}
                    style={styles.subjectProgress}
                  />

                  <Divider style={styles.divider} />

                  {/* Assessment Type Summary */}
                  <View style={styles.assessmentTypeSummary}>
                    <Text style={styles.assessmentsTitle}>Assessment Types</Text>
                    <View style={styles.assessmentTypeChips}>
                      {data.assessmentScores && data.assessmentScores.length > 0 ? (
                        (() => {
                          // Check if we only have the placeholder assessment
                          if (data.assessmentScores.length === 1 && data.assessmentScores[0].assessmentId === 'no-data') {
                            return (
                              <Text style={styles.noAssessmentsText}>
                                No assessment data available. Please contact your teacher.
                              </Text>
                            );
                          }

                          // Calculate assessment type statistics
                          const assessmentTypes = {};
                          let totalPoints = 0;
                          let totalMaxPoints = 0;

                          data.assessmentScores.forEach(assessment => {
                            // Skip placeholder assessments
                            if (assessment.assessmentId === 'no-data') return;

                            const type = assessment.assessmentType || 'Unknown';
                            const score = parseFloat(assessment.score) || 0;
                            const maxPoints = parseFloat(assessment.maxPoints) || 0;

                            if (!assessmentTypes[type]) {
                              assessmentTypes[type] = {
                                count: 0,
                                totalScore: 0,
                                totalMaxPoints: 0,
                                totalPercentage: 0
                              };
                            }

                            assessmentTypes[type].count++;
                            assessmentTypes[type].totalScore += score;
                            assessmentTypes[type].totalMaxPoints += maxPoints;

                            totalPoints += score;
                            totalMaxPoints += maxPoints;
                          });

                          // If no valid assessment types were found
                          if (Object.keys(assessmentTypes).length === 0) {
                            return (
                              <Text style={styles.noAssessmentsText}>
                                No assessment data available. Please contact your teacher.
                              </Text>
                            );
                          }

                          // Calculate percentages
                          Object.keys(assessmentTypes).forEach(type => {
                            const stats = assessmentTypes[type];
                            stats.totalPercentage = stats.totalMaxPoints > 0
                              ? (stats.totalScore / stats.totalMaxPoints) * 100
                              : 0;

                            // Calculate contribution to overall score
                            stats.contribution = totalMaxPoints > 0
                              ? (stats.totalMaxPoints / totalMaxPoints) * 100
                              : 0;
                          });

                          return Object.entries(assessmentTypes).map(([type, stats], idx) => {
                            return (
                              <Chip
                                key={idx}
                                mode="outlined"
                                style={[
                                  styles.assessmentSummaryChip,
                                  { borderColor: getPerformanceColor(stats.totalPercentage) }
                                ]}
                                textStyle={{ color: getPerformanceColor(stats.totalPercentage) }}
                              >
                                {type} ({stats.count}): {stats.totalPercentage.toFixed(1)}%
                                ({stats.contribution.toFixed(0)}% of total)
                              </Chip>
                            );
                          });
                        })()
                      ) : (
                        <Text style={styles.noAssessmentsText}>No assessment types available</Text>
                      )}
                    </View>
                  </View>

                  <Divider style={styles.divider} />

                  <View style={styles.assessmentHeaderRow}>
                    <Text style={styles.assessmentsTitle}>Assessment Breakdown</Text>
                    <Chip
                      icon="information-outline"
                      mode="outlined"
                      style={styles.infoChip}
                    >
                      {data.assessmentScores ?
                        data.assessmentScores.filter(a => a.assessmentId !== 'no-data').length : 0} Assessments
                    </Chip>
                  </View>

                  {data.assessmentScores && data.assessmentScores.length > 0 ? (
                    (() => {
                      // Check if we only have the placeholder assessment
                      if (data.assessmentScores.length === 1 && data.assessmentScores[0].assessmentId === 'no-data') {
                        return (
                          <Text style={styles.noAssessmentsText}>
                            No assessment data available. Please contact your teacher.
                          </Text>
                        );
                      }

                      // Filter out placeholder assessments
                      const validAssessments = data.assessmentScores.filter(
                        assessment => assessment.assessmentId !== 'no-data'
                      );

                      if (validAssessments.length === 0) {
                        return (
                          <Text style={styles.noAssessmentsText}>
                            No assessment data available. Please contact your teacher.
                          </Text>
                        );
                      }

                      return (
                        <DataTable>
                          <DataTable.Header style={styles.dataTableHeader}>
                            <DataTable.Title>Assessment</DataTable.Title>
                            <DataTable.Title>Type</DataTable.Title>
                            <DataTable.Title numeric>Score</DataTable.Title>
                            <DataTable.Title numeric>Max</DataTable.Title>
                            <DataTable.Title numeric>%</DataTable.Title>
                          </DataTable.Header>

                          {validAssessments.map((assessment, idx) => {
                            const scorePercentage = calculatePercentage(assessment.score, assessment.maxPoints);
                            return (
                              <DataTable.Row
                                key={idx}
                                onPress={() => showAssessmentDetails(assessment, subject)}
                                style={[
                                  styles.assessmentRow,
                                  idx % 2 === 0 ? styles.evenRow : styles.oddRow
                                ]}
                              >
                                <DataTable.Cell>
                                  <View style={styles.assessmentNameCell}>
                                    <Text style={styles.assessmentNameText}>{assessment.assessmentTitle}</Text>
                                  </View>
                                </DataTable.Cell>
                                <DataTable.Cell>
                                  <Chip
                                    mode="flat"
                                    style={[
                                      styles.assessmentTypeChip,
                                      { backgroundColor: getAssessmentTypeColor(assessment.assessmentType) }
                                    ]}
                                  >
                                    {assessment.assessmentType}
                                  </Chip>
                                </DataTable.Cell>
                                <DataTable.Cell numeric>
                                  <Text style={{
                                    color: getPerformanceColor(scorePercentage),
                                    fontWeight: 'bold'
                                  }}>
                                    {parseFloat(assessment.score).toFixed(1)}
                                  </Text>
                                </DataTable.Cell>
                                <DataTable.Cell numeric>
                                  <Text>
                                    {parseFloat(assessment.maxPoints).toFixed(1)}
                                  </Text>
                                </DataTable.Cell>
                                <DataTable.Cell numeric>
                                  <Text style={{
                                    color: getPerformanceColor(scorePercentage),
                                    fontWeight: 'bold'
                                  }}>
                                    {scorePercentage.toFixed(0)}%
                                  </Text>
                                </DataTable.Cell>
                              </DataTable.Row>
                            );
                          })}
                        </DataTable>
                      );
                    })()
                  ) : (
                    <Text style={styles.noAssessmentsText}>No assessment details available</Text>
                  )}
                </Card.Content>
              </Card>
            </Animatable.View>
          ))
        )}
      </Animatable.View>

      <Animatable.View animation="fadeInUp" duration={800} delay={300}>
        <Surface style={styles.tipCard}>
          <Card.Content>
            <Title style={styles.tipTitle}>Performance Tips</Title>
            <List.Item
              title="Regular Study"
              description="Consistent daily study helps retain information better than cramming."
              left={props => <List.Icon {...props} icon="book-open-page-variant" />}
            />
            <List.Item
              title="Ask Questions"
              description="Don't hesitate to ask your teachers when you don't understand something."
              left={props => <List.Icon {...props} icon="help-circle" />}
            />
            <List.Item
              title="Practice Problems"
              description="Regular practice of problems helps reinforce concepts."
              left={props => <List.Icon {...props} icon="pencil" />}
            />
          </Card.Content>
        </Surface>
      </Animatable.View>

      <View style={styles.buttonContainer}>
        <Button
          mode="contained"
          onPress={fetchStudentResults}
          icon="refresh"
          style={styles.refreshButton}
        >
          Refresh Results
        </Button>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center'
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16
  },
  errorText: {
    color: 'red',
    marginBottom: 16,
    textAlign: 'center'
  },
  summaryCard: {
    marginBottom: 16,
    overflow: 'hidden'
  },
  gradientBackground: {
    padding: 16
  },
  cardTitle: {
    marginBottom: 16,
    fontWeight: 'bold'
  },
  performanceRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16
  },
  performanceItem: {
    alignItems: 'center'
  },
  performanceLabel: {
    fontSize: 14,
    marginBottom: 8
  },
  performanceValue: {
    fontSize: 24,
    fontWeight: 'bold'
  },
  gradeChip: {
    width: 50,
    height: 50,
    borderRadius: 25,
    alignItems: 'center',
    justifyContent: 'center'
  },
  progressBar: {
    height: 8,
    borderRadius: 4
  },
  rankingCard: {
    marginBottom: 16
  },
  rankingsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8
  },
  rankItem: {
    alignItems: 'center'
  },
  rankLabel: {
    marginBottom: 8
  },
  firstRankBadge: {
    backgroundColor: '#FFD700'
  },
  secondRankBadge: {
    backgroundColor: '#C0C0C0'
  },
  thirdRankBadge: {
    backgroundColor: '#CD7F32'
  },
  topTenBadge: {
    backgroundColor: '#90CAF9'
  },
  normalRankBadge: {
    backgroundColor: '#E0E0E0'
  },
  sectionTitle: {
    marginTop: 16,
    marginBottom: 16
  },
  noDataCard: {
    padding: 16,
    alignItems: 'center',
    marginBottom: 16
  },
  noDataText: {
    fontSize: 16,
    color: '#666'
  },
  subjectCard: {
    marginBottom: 16
  },
  subjectHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16
  },
  subjectTitleContainer: {
    flex: 1
  },
  subjectTitle: {
    fontSize: 18
  },
  subjectTeacher: {
    fontSize: 12,
    color: '#757575',
    marginTop: 4
  },
  subjectScoreContainer: {
    alignItems: 'center'
  },
  subjectScoreText: {
    fontSize: 16,
    fontWeight: 'bold',
    marginTop: 4
  },
  subjectProgress: {
    height: 6,
    borderRadius: 3
  },
  divider: {
    marginVertical: 16
  },
  assessmentsTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8
  },
  noAssessmentsText: {
    fontStyle: 'italic',
    color: '#666',
    textAlign: 'center',
    marginTop: 8
  },
  tipCard: {
    marginTop: 16,
    marginBottom: 16,
    elevation: 4,
    borderRadius: 8,
    backgroundColor: '#F5F5F5'
  },
  tipTitle: {
    marginBottom: 8
  },
  buttonContainer: {
    marginBottom: 32,
    alignItems: 'center'
  },
  refreshButton: {
    width: '80%'
  },
  assessmentTypeChip: {
    height: 24,
    alignSelf: 'flex-start'
  },
  assessmentTypeSummary: {
    marginBottom: 16
  },
  assessmentTypeChips: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 8
  },
  assessmentSummaryChip: {
    margin: 4
  },
  assessmentHeaderRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8
  },
  infoChip: {
    height: 28
  },
  assessmentRow: {
    borderBottomWidth: 0.5,
    borderBottomColor: '#e0e0e0'
  },
  evenRow: {
    backgroundColor: '#f9f9f9'
  },
  oddRow: {
    backgroundColor: '#ffffff'
  },
  assessmentNameCell: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  assessmentNameText: {
    flex: 1
  },
  dataTableHeader: {
    backgroundColor: '#f5f5f5'
  },
  modalContainer: {
    backgroundColor: 'white',
    padding: 0,
    margin: 20,
    borderRadius: 8
  },
  assessmentDetailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12
  },
  assessmentDetailLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#616161'
  },
  assessmentDetailValue: {
    fontSize: 16,
    fontWeight: 'bold'
  },
  assessmentDetailSection: {
    marginTop: 8,
    marginBottom: 16
  },
  assessmentDescription: {
    marginTop: 8,
    fontSize: 14,
    lineHeight: 20,
    color: '#616161'
  },
  assessmentProgressBar: {
    marginTop: 16,
    height: 8,
    borderRadius: 4
  },
  gradeChip: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center'
  }
});

export default StudentResultsView;
