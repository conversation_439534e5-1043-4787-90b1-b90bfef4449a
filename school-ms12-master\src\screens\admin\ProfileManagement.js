import React, { useState, useEffect, useRef } from 'react';
import { View, StyleSheet, ScrollView, Animated, TouchableOpacity } from 'react-native';
import {
  TextInput,
  Avatar,
  Title,
  Text,
  Card,
  Portal,
  Modal,
  useTheme,
  IconButton,
  ActivityIndicator,
  Snackbar,
  Divider
} from 'react-native-paper';
import { auth, db } from '../../config/firebase';
import { doc, getDoc, updateDoc } from 'firebase/firestore';
import * as ImagePicker from 'expo-image-picker';
import CustomButton from '../../components/common/CustomButton';
import ChangePasswordModal from '../../components/common/ChangePasswordModal';
import { useNavigation } from '@react-navigation/native';
import { LinearGradient } from 'expo-linear-gradient';
import * as Animatable from 'react-native-animatable';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useLanguage } from '../../context/LanguageContext';
import AdminAppHeader from '../../components/common/AdminAppHeader';
import AdminSidebar from '../../components/common/AdminSidebar';
import SidebarBackdrop from '../../components/common/SidebarBackdrop';
import ActivityService from '../../services/ActivityService';
import ProfileImageService from '../../services/ProfileImageService';
import CloudinaryAvatar from '../../components/common/CloudinaryAvatar';

const ProfileManagement = () => {
  // No theme needed
  const navigation = useNavigation();
  const { translate, language, getTextStyle, isRTL } = useLanguage();

  // Profile state
  const [profile, setProfile] = useState({
    displayName: '',
    email: '',
    phoneNumber: '',
    role: 'admin',
  });
  const [imageUri, setImageUri] = useState(null);
  const [isEditing, setIsEditing] = useState(false);
  const [passwordModalVisible, setPasswordModalVisible] = useState(false);
  const [editedProfile, setEditedProfile] = useState({});
  const [loading, setLoading] = useState(true);
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');

  // Animation states
  const [fadeAnim] = useState(new Animated.Value(0));
  const [slideAnim] = useState(new Animated.Value(50));

  // Sidebar state
  const [drawerOpen, setDrawerOpen] = useState(false);
  const drawerAnim = useRef(new Animated.Value(-300)).current;
  const backdropFadeAnim = useRef(new Animated.Value(0)).current;
  const [activeSidebarItem, setActiveSidebarItem] = useState('ProfileManagement');

  // Toggle drawer function
  const toggleDrawer = () => {
    if (drawerOpen) {
      // Close drawer
      Animated.parallel([
        Animated.timing(drawerAnim, {
          toValue: -300,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(backdropFadeAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      // Open drawer
      Animated.parallel([
        Animated.timing(drawerAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(backdropFadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    }
    setDrawerOpen(!drawerOpen);
  };

  // Animation on mount
  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 500,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 500,
        useNativeDriver: true,
      }),
    ]).start();

    // Hide the default header
    navigation.setOptions({
      headerShown: false,
    });
  }, [navigation, fadeAnim, slideAnim]);

  useEffect(() => {
    fetchProfile();
  }, []);

  const fetchProfile = async () => {
    try {
      setLoading(true);
      const user = auth.currentUser;
      if (user) {
        const docRef = doc(db, 'users', user.uid);
        const docSnap = await getDoc(docRef);
        if (docSnap.exists()) {
          const data = docSnap.data();
          setProfile(data);
          setImageUri(data.photoURL);
        }
      }
    } catch (error) {
      console.error('Error fetching profile:', error);
      setSnackbarMessage(translate('messages.errors.network'));
      setSnackbarVisible(true);
    } finally {
      setLoading(false);
    }
  };

  const pickImage = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 1,
      });

      if (!result.canceled) {
        setImageUri(result.assets[0].uri);
        setEditedProfile(prev => ({ ...prev, photoURL: result.assets[0].uri }));
      }
    } catch (error) {
      console.error('Error picking image:', error);
    }
  };

  const handleSave = async () => {
    try {
      setLoading(true);
      const user = auth.currentUser;
      if (!user) return;

      const updates = { ...editedProfile };

      // Handle profile image upload if changed
      if (imageUri && imageUri !== profile.photoURL) {
        try {
          // Upload and update profile image using the ProfileImageService
          const downloadURL = await ProfileImageService.uploadAndUpdateProfileImage(
            imageUri,
            user,
            'admin'
          );

          if (downloadURL) {
            updates.photoURL = downloadURL;
          }
        } catch (imageError) {
          console.error('Error uploading profile image:', imageError);
          // Continue with other updates even if image upload fails
        }
      }

      // Update other profile fields in Firestore if there are any changes
      if (Object.keys(updates).length > 0) {
        const userRef = doc(db, 'users', user.uid);
        await updateDoc(userRef, updates);

        // Log activity
        await ActivityService.logActivity({
          type: 'PROFILE_UPDATE',
          userId: user.uid,
          details: {
            updatedFields: Object.keys(updates),
          },
          timestamp: new Date().toISOString(),
        });

        setProfile(prev => ({ ...prev, ...updates }));
      }

      setIsEditing(false);
      setEditedProfile({});

      // Show success message
      setSnackbarMessage(translate('messages.success.update'));
      setSnackbarVisible(true);
    } catch (error) {
      console.error('Error updating profile:', error);
      setSnackbarMessage(translate('messages.errors.network'));
      setSnackbarVisible(true);
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.mainContainer}>
      {/* Sidebar */}
      <AdminSidebar
        drawerAnim={drawerAnim}
        activeSidebarItem={activeSidebarItem}
        setActiveSidebarItem={setActiveSidebarItem}
        toggleDrawer={toggleDrawer}
      />

      {/* Backdrop */}
      <SidebarBackdrop
        visible={drawerOpen}
        onPress={toggleDrawer}
        fadeAnim={backdropFadeAnim}
      />

      {/* Admin App Header */}
      <AdminAppHeader
        title={translate('common.profile')}
        onMenuPress={toggleDrawer}
      />

      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={'#1976d2'} />
          <Text style={styles.loadingText}>{translate('common.loading')}</Text>
        </View>
      ) : (
        <Animated.View
          style={[
            styles.container,
            { opacity: fadeAnim, transform: [{ translateY: slideAnim }] },
            isRTL && styles.rtlContainer
          ]}
        >
          <ScrollView contentContainerStyle={styles.scrollContent}>
            <Animatable.View animation="fadeIn" duration={500} delay={300}>
              <Card style={styles.profileCard} elevation={4}>
                <LinearGradient
                  colors={['#1976d2', '#9c27b0']}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 0 }}
                  style={styles.cardHeader}
                >
                  <View style={styles.avatarContainer}>
                    <CloudinaryAvatar
                      source={imageUri}
                      label={profile.displayName?.substring(0, 2) || 'AD'}
                      size={120}
                      style={styles.avatar}
                      backgroundColor="#1976d2"
                    />
                  </View>
                </LinearGradient>

                <Card.Content style={styles.cardContent}>
                  <Title style={[styles.name, getTextStyle()]}>{profile.displayName}</Title>
                  <Text style={[styles.role, getTextStyle()]}>{translate('userManagement.admin')}</Text>

                  <View style={styles.infoSection}>
                    <View style={styles.infoRow}>
                      <MaterialCommunityIcons name="email-outline" size={24} color={'#1976d2'} />
                      <View style={styles.infoTextContainer}>
                        <Text style={[styles.label, getTextStyle()]}>{translate('userManagement.email')}</Text>
                        <Text style={[styles.value, getTextStyle()]}>{profile.email}</Text>
                      </View>
                    </View>

                    <View style={styles.infoRow}>
                      <MaterialCommunityIcons name="phone-outline" size={24} color={'#1976d2'} />
                      <View style={styles.infoTextContainer}>
                        <Text style={[styles.label, getTextStyle()]}>{translate('userManagement.phoneNumber')}</Text>
                        <Text style={[styles.value, getTextStyle()]}>{profile.phoneNumber || translate('userManagement.notSet')}</Text>
                      </View>
                    </View>

                    <View style={styles.infoRow}>
                      <MaterialCommunityIcons name="shield-account-outline" size={24} color={'#1976d2'} />
                      <View style={styles.infoTextContainer}>
                        <Text style={[styles.label, getTextStyle()]}>{translate('userManagement.role')}</Text>
                        <Text style={[styles.value, getTextStyle()]}>{translate('userManagement.admin')}</Text>
                      </View>
                    </View>
                  </View>

                  <CustomButton
                    mode="contained"
                    onPress={() => setIsEditing(true)}
                    style={styles.editButton}
                    icon="account-edit"
                    labelStyle={getTextStyle()}
                  >
                    {translate('ui.buttons.edit')}
                  </CustomButton>

                  <CustomButton
                    mode="outlined"
                    onPress={() => setPasswordModalVisible(true)}
                    style={styles.passwordButton}
                    icon="lock-reset"
                    labelStyle={getTextStyle()}
                  >
                    {translate('profileManagement.changePassword') || 'Change Password'}
                  </CustomButton>
                </Card.Content>
              </Card>
            </Animatable.View>
          </ScrollView>

          {/* Change Password Modal */}
          <ChangePasswordModal
            visible={passwordModalVisible}
            onDismiss={() => setPasswordModalVisible(false)}
            onSuccess={() => {
              setPasswordModalVisible(false);
              setSnackbarMessage(translate('profileManagement.passwordChangeSuccess') || 'Password changed successfully');
              setSnackbarVisible(true);
            }}
          />

          {/* Edit Profile Modal */}
          <Portal>
            <Modal
              visible={isEditing}
              onDismiss={() => {
                setIsEditing(false);
                setEditedProfile({});
              }}
              contentContainerStyle={styles.modalContent}
            >
              <ScrollView>
                <Title style={[styles.modalTitle, getTextStyle()]}>{translate('userManagement.editUser')}</Title>

                <View style={styles.imagePickerContainer}>
                  <CloudinaryAvatar
                    source={imageUri}
                    label={profile.displayName?.substring(0, 2) || 'AD'}
                    size={100}
                    backgroundColor="#1976d2"
                  />
                  <CustomButton
                    mode="outlined"
                    onPress={pickImage}
                    style={styles.imagePickerButton}
                    icon="camera"
                    labelStyle={getTextStyle()}
                  >
                    {translate('parentManagement.change')}
                  </CustomButton>
                </View>

                <TextInput
                  label={translate('userManagement.displayName')}
                  value={editedProfile.displayName || profile.displayName}
                  onChangeText={text => setEditedProfile(prev => ({ ...prev, displayName: text }))}
                  style={[styles.input, isRTL && styles.rtlInput]}
                  right={<TextInput.Icon icon="account" />}
                />

                <TextInput
                  label={translate('userManagement.phoneNumber')}
                  value={editedProfile.phoneNumber || profile.phoneNumber}
                  onChangeText={text => setEditedProfile(prev => ({ ...prev, phoneNumber: text }))}
                  style={[styles.input, isRTL && styles.rtlInput]}
                  keyboardType="phone-pad"
                  right={<TextInput.Icon icon="phone" />}
                />

                <View style={[styles.buttonContainer, isRTL && styles.rtlButtonContainer]}>
                  <CustomButton
                    mode="outlined"
                    onPress={() => {
                      setIsEditing(false);
                      setEditedProfile({});
                    }}
                    style={[styles.button, styles.cancelButton]}
                    icon="close"
                    labelStyle={getTextStyle()}
                  >
                    {translate('ui.buttons.cancel')}
                  </CustomButton>
                  <CustomButton
                    mode="contained"
                    onPress={handleSave}
                    style={[styles.button, styles.saveButton]}
                    icon="content-save"
                    loading={loading}
                    disabled={loading}
                    labelStyle={getTextStyle()}
                  >
                    {translate('ui.buttons.save')}
                  </CustomButton>
                </View>
              </ScrollView>
            </Modal>
          </Portal>

          {/* Snackbar for notifications */}
          <Snackbar
            visible={snackbarVisible}
            onDismiss={() => setSnackbarVisible(false)}
            duration={3000}
            style={styles.snackbar}
            action={{
              label: 'OK',
              onPress: () => setSnackbarVisible(false),
            }}
          >
            {snackbarMessage}
          </Snackbar>
        </Animated.View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
    paddingTop: 8,
  },
  rtlContainer: {
    flexDirection: 'row-reverse',
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 32,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  profileCard: {
    borderRadius: 12,
    overflow: 'hidden',
    marginBottom: 16,
  },
  cardHeader: {
    padding: 24,
    alignItems: 'center',
  },
  cardContent: {
    padding: 16,
  },
  avatarContainer: {
    alignItems: 'center',
  },
  avatar: {
    borderWidth: 4,
    borderColor: 'white',
  },
  name: {
    marginTop: 16,
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  role: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 16,
  },
  infoSection: {
    marginTop: 16,
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
    padding: 16,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    paddingBottom: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  infoTextContainer: {
    marginLeft: 16,
    flex: 1,
  },
  label: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  value: {
    fontSize: 16,
    fontWeight: '500',
  },
  editButton: {
    marginTop: 24,
    borderRadius: 8,
    paddingVertical: 8,
  },
  passwordButton: {
    marginTop: 12,
    borderRadius: 8,
    paddingVertical: 8,
  },
  modalContent: {
    backgroundColor: 'white',
    padding: 24,
    margin: 16,
    borderRadius: 12,
    maxHeight: '80%',
    elevation: 5,
  },
  modalTitle: {
    textAlign: 'center',
    marginBottom: 24,
    fontSize: 22,
    fontWeight: 'bold',
  },
  imagePickerContainer: {
    alignItems: 'center',
    marginBottom: 24,
  },
  imagePickerButton: {
    marginTop: 16,
  },
  input: {
    marginBottom: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
  },
  rtlInput: {
    textAlign: 'right',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 24,
  },
  rtlButtonContainer: {
    flexDirection: 'row-reverse',
  },
  button: {
    flex: 1,
    marginHorizontal: 8,
    borderRadius: 8,
    paddingVertical: 8,
  },
  cancelButton: {
    borderColor: '#666',
  },
  saveButton: {
    backgroundColor: '#2196F3',
  },
  snackbar: {
    bottom: 16,
  },
});

export default ProfileManagement;
