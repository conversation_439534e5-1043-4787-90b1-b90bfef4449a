const fs = require('fs');
const path = require('path');

// Function to recursively find all JS files in a directory
function findJsFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);

  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);

    if (stat.isDirectory() && file !== 'node_modules' && file !== '.git') {
      findJsFiles(filePath, fileList);
    } else if (stat.isFile() && (file.endsWith('.js') || file.endsWith('.jsx'))) {
      fileList.push(filePath);
    }
  });

  return fileList;
}

// Function to add ChartUtils import to files that use charts
function addChartUtilsImport(filePath) {
  console.log(`Processing ${filePath}...`);
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;

  // Check if the file uses react-native-chart-kit
  if ((content.includes('react-native-chart-kit') || 
      content.includes('LineChart') || 
      content.includes('BarChart') || 
      content.includes('PieChart')) && 
      !content.includes('ChartUtils')) {
    
    // Find the last import statement
    const importRegex = /^import .+ from .+;$/gm;
    const imports = [...content.matchAll(importRegex)];
    
    if (imports.length > 0) {
      const lastImport = imports[imports.length - 1][0];
      const lastImportIndex = content.indexOf(lastImport) + lastImport.length;
      
      // Calculate relative path to utils directory
      const relPath = path.relative(path.dirname(filePath), path.join(__dirname, 'src', 'utils')).replace(/\\/g, '/');
      const importPath = relPath.startsWith('.') ? relPath : './' + relPath;
      
      const importStatement = `\nimport { sanitizeChartData, sanitizeChartDatasets } from '${importPath}/ChartUtils';`;
      
      content = content.slice(0, lastImportIndex) + importStatement + content.slice(lastImportIndex);
      modified = true;
    }
  }

  // Save the file if modified
  if (modified) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`Added ChartUtils import to ${filePath}`);
    return true;
  }

  return false;
}

// Main function
function main() {
  const srcDir = path.join(__dirname, 'src');
  const jsFiles = findJsFiles(srcDir);
  let fixedCount = 0;

  jsFiles.forEach(file => {
    if (addChartUtilsImport(file)) {
      fixedCount++;
    }
  });

  console.log(`\nAdded ChartUtils import to ${fixedCount} files.`);
}

main();
