# CHAPTER SIX
## 6. I<PERSON>LEMENTATION AND TESTING

### 6.1. Implementation

The implementation phase focused on transforming the design blueprint into a fully functioning mobile application using React Native and Expo. The development process was iterative, ensuring that all modules were integrated efficiently. The system was divided into several components such as authentication, student management, teacher management, class management, grade management, and notification systems.

React Native was chosen for its ability to build cross-platform applications with a single codebase, reducing time and resource demands. The JavaScript programming language provided a reactive and expressive syntax for building UI components and state management.

Key activities during implementation included:

- Setting up React Native and Expo development environment
- Initializing the project with folder structures for components, screens, services, and utilities
- Developing authentication features using Firebase Authentication
- Creating user roles (admin, teacher, student, parent) and managing user permissions
- Structuring the UI using React Native Paper components and custom UI elements
- Implementing role-based navigation and access control
- Setting up real-time data synchronization with Firebase Firestore
- Implementing offline capabilities for improved user experience

Each feature was developed and tested individually before integration with the rest of the application to ensure modularity and scalability.

### 6.2. Implementation of the Database

After logical and structural designs were completed, we proceeded to implement the cloud-based NoSQL database using Firebase Firestore. This task was carried out by the development team based on the earlier data modeling and design specifications. Unlike traditional relational databases that rely on SQL and table schemas, Firestore organizes data in collections and documents.

Each document stores data in key-value pairs and can contain nested structures. The flexible schema allows us to adapt easily to any future changes.

Below are the major Firestore collections used in our system along with example data structures:

#### Core Collections:

**Users** – Stores user profiles with fields such as userId, email, role, and createdAt.
```
{
  uid: "user123",
  email: "<EMAIL>",
  role: "teacher",
  firstName: "John",
  lastName: "Doe",
  displayName: "John Doe",
  createdAt: timestamp,
  updatedAt: timestamp,
  status: "active",
  emailVerified: true
}
```

**Students** – Contains student-specific information including class, section, and parent references.
```
{
  userId: "student123",
  firstName: "Jane",
  lastName: "Smith",
  rollNumber: "S2023001",
  classId: "class10A",
  section: "A",
  parentId: "parent456",
  dateOfBirth: timestamp,
  address: {...},
  contactInfo: {...},
  createdAt: timestamp
}
```

**Teachers** – Stores teacher information including subjects taught and qualifications.
```
{
  userId: "teacher123",
  firstName: "Robert",
  lastName: "Johnson",
  employeeId: "T2023001",
  subjects: ["Mathematics", "Physics"],
  qualifications: [...],
  contactInfo: {...},
  createdAt: timestamp
}
```

**Classes** – Contains class information with sections, assigned teachers, and schedules.
```
{
  name: "Grade 10",
  description: "Secondary education class",
  academicYear: "2023-2024",
  teacher: "teacher123",
  sections: ["A", "B", "C"],
  groups: [...]
}
```

**Timetables** – Stores class schedules with day, time, subject, and teacher information.
```
{
  classId: "class10A",
  section: "A",
  day: "Monday",
  startTime: "08:00",
  endTime: "09:00",
  subject: "Mathematics",
  teacherId: "teacher123",
  room: "Room 101",
  published: true
}
```

Security rules were applied in Firestore to ensure that:
- Only authorized users can write or update data
- Teachers can only access their assigned classes and students
- Students can only view their own data and class information
- Parents can only access their children's data
- Admins have full access to all collections

The database is structured to support real-time updates, with listeners set up on the client side to update the UI instantly when new data is submitted or existing data changes.

### 6.3. Implementation of Class Diagram

The system's class diagram was translated into actual JavaScript/React components, following object-oriented principles. Each class corresponds to a system component or entity.

#### Example Implemented Classes:

**User**
- Attributes: id, email, role, displayName, status
- Methods: authenticate(), updateProfile(), resetPassword()

**Student**
- Attributes: id, firstName, lastName, rollNumber, classId, section
- Methods: getGrades(), getAttendance(), getSchedule()

**Teacher**
- Attributes: id, firstName, lastName, employeeId, subjects
- Methods: submitGrades(), markAttendance(), getSchedule()

**Class**
- Attributes: id, name, description, academicYear, teacher, sections
- Methods: getStudents(), getSchedule(), getSubjects()

**Grade**
- Attributes: id, studentId, classId, subjectId, assessmentId, score
- Methods: calculate(), submit(), approve()

These classes were implemented as React components and services, with the business logic separated from the UI components to maintain a clean architecture.

### 6.4. Configuration of Application Server

The application uses Firebase as its backend server infrastructure. Firebase provides a comprehensive suite of cloud services that handle authentication, database, storage, and cloud functions. This serverless architecture eliminates the need for traditional server management while providing scalability and reliability.

Key Firebase services configured for the application include:

| Service | Configuration | Usage |
|---------|--------------|-------|
| Authentication | Email/Password, Google sign-in | User login and registration |
| Firestore | NoSQL DB with security rules | Data storage and real-time updates |
| Cloud Functions | Node.js serverless functions | Backend processing and notifications |
| Storage | Cloud storage with security rules | File and media storage |
| Hosting | Static file hosting | Web version deployment |

Firebase configuration was implemented in the `src/config/firebase.js` file, which initializes all the necessary Firebase services:

```javascript
// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Auth with AsyncStorage persistence
const auth = initializeAuth(app, {
  persistence: getReactNativePersistence(AsyncStorage)
});

// Initialize Firestore
const db = getFirestore(app);

// Initialize Storage
const storage = getStorage(app);
```

For local development and testing, we used Expo's development server which provides hot reloading, debugging tools, and device simulation. The application can be run on physical devices using Expo Go or through native builds for Android and iOS.

### 6.5. Configuration of Application Security

Security in the application was implemented using a multi-layered approach:

1. **Authentication**: Firebase Authentication was used for secure sign-up and sign-in. User sessions are maintained securely using Firebase tokens with AsyncStorage persistence.

2. **Authorization**: Access to data and features is role-based (admin, teacher, student, parent). Firestore security rules were defined to enforce these permissions at the database level.

3. **Data Validation**: Client-side and server-side input validations were implemented to prevent malformed data and potential security vulnerabilities.

4. **Secure Data Transfer**: HTTPS protocol is enforced by Firebase to encrypt all data in transit.

5. **Data at Rest**: Firebase automatically encrypts stored data, and additional encryption was implemented for sensitive information.

6. **Session Management**: Automatic session timeout and secure token refresh mechanisms were implemented.

7. **Password Security**: Password complexity requirements, secure password reset flows, and password change functionality were implemented.

Firestore security rules were a critical component of our security implementation, ensuring that users can only access data they are authorized to view or modify:

```
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Helper functions
    function isAdmin() {
      return request.auth != null &&
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }

    // Allow users to read their own profile
    match /users/{userId} {
      allow read: if request.auth.uid == userId || isAdmin();
      allow write: if request.auth.uid == userId || isAdmin();
    }

    // Additional rules for other collections...
  }
}
```

These security configurations ensure that only authorized users can access or modify sensitive data while maintaining high levels of system integrity and privacy.

### 6.6. Implementation of User Interface

The user interface was designed to be simple, interactive, and responsive, with usability and accessibility in mind. The UI components were implemented using React Native Paper and custom components with Material Design standards.

#### Key UI Features:

1. **Responsive Design**: The UI adapts to different screen sizes and orientations, ensuring a consistent experience across devices.

2. **Role-Based Interfaces**: Different interfaces for admin, teacher, student, and parent roles, with tailored functionality and information.

3. **Multilingual Support**: Support for multiple languages including English, Amharic, and Oromo, with easy language switching.

4. **Ethiopian Calendar Integration**: Custom date and time pickers that support the Ethiopian calendar system.

5. **Offline Mode Indicators**: Visual indicators when the app is operating in offline mode.

6. **Accessibility Features**: Support for screen readers, adjustable font sizes, and high-contrast modes.

#### Key Screens:

**Login/Register Screens** – With email/password authentication and error validation.

**Role-Specific Dashboards** – Customized dashboards for each user role showing relevant information and quick access to frequently used features.

**Student Management** – Interfaces for adding, editing, and managing student information, with bulk import capabilities.

**Teacher Management** – Screens for managing teacher information, assignments, and schedules.

**Class Management** – Interfaces for creating and managing classes, sections, and schedules.

**Grade Management** – Systems for entering, approving, and viewing grades with detailed breakdowns and statistics.

**Attendance Tracking** – Interfaces for recording and monitoring student and teacher attendance.

A consistent color scheme, typography, and iconography were used across all screens to maintain a professional and polished look.

### 6.7. Testing

A comprehensive testing strategy was followed to validate the system's functionality, performance, and security. Different types of testing were carried out at different development phases.

#### 6.7.1. Testing Tools and Environment

**Tools Used:**
- Jest – For unit and integration tests
- React Native Testing Library – For component testing
- Firebase Emulator Suite – For testing database and authentication locally
- Expo Device – For testing on physical devices
- Detox – For end-to-end testing

**Environment:**
- Development on Expo development server
- Testing on Android and iOS simulators
- Testing on physical Android and iOS devices
- Firebase emulators for backend testing

#### 6.7.2. Unit Testing

Unit tests were written for key business logic, such as:
- Authentication functions
- Data validation utilities
- Grade calculation algorithms
- Date conversion utilities (especially for Ethiopian calendar)

Each test case followed the AAA (Arrange-Act-Assert) pattern and was structured to test expected output for specific inputs.

Example unit test for the grade calculation function:

```javascript
test('calculateFinalGrade calculates correct grade', () => {
  // Arrange
  const assessments = [
    { id: 'a1', weight: 0.3, maxScore: 100 },
    { id: 'a2', weight: 0.7, maxScore: 100 }
  ];
  const scores = [
    { assessmentId: 'a1', score: 80 },
    { assessmentId: 'a2', score: 90 }
  ];

  // Act
  const result = calculateFinalGrade(assessments, scores);

  // Assert
  expect(result).toBe(87); // 80*0.3 + 90*0.7 = 87
});
```

#### 6.7.3. Integration Testing

Integration testing involved testing complete workflows, such as:
- User registration and login flow
- Grade submission and approval process
- Student enrollment in classes
- Attendance marking and reporting

Real-time database updates were tested across multiple devices to ensure synchronization.

#### 6.7.4. System Testing

The complete system was deployed in a staging environment where all modules were tested together. The tests included:
- Concurrent user sessions
- Performance under load
- Offline functionality and data synchronization
- Cross-device compatibility
- Security and permission enforcement

#### 6.7.5. Acceptance Testing

Acceptance testing was conducted with real users from each role category (administrators, teachers, students, and parents). Their feedback was used to:
- Improve UI usability
- Refine workflows
- Address performance issues
- Enhance feature functionality

Test results confirmed that the system met its original objectives and was ready for deployment.
