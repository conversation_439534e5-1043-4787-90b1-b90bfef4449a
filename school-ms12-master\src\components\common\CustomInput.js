import React from 'react';
import { StyleSheet, View } from 'react-native';
import FixedTextInput from './FixedTextInput';

const CustomInput = ({ error, style, left, ...props }) => {
  return (
    <FixedTextInput
      mode="outlined"
      style={[styles.input, style]}
      error={error}
      left={left ? <FixedTextInput.Icon icon={() => left} /> : null}
      {...props}
    />
  );
};

const styles = StyleSheet.create({
  input: {
    marginVertical: 8,
    backgroundColor: '#fff',
  },
});

export default CustomInput;
