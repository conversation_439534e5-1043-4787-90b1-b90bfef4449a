# Instructions for Combining Mobile User Management Files

Due to size limitations, the Mobile User Management implementation has been split into multiple files. Follow these instructions to combine them into a single working component.

## Files Overview

1. **MobileUserManagementPart1.js** - Contains imports, component declaration, state variables, and FAB actions
2. **MobileUserManagementPart2.js** - Contains user interaction functions (search, status change, deletion, password reset)
3. **MobileUserManagementPart3.js** - Contains the main UI components (user list with swipe actions)
4. **MobileUserManagementPart4.js** - Contains confirmation dialogs and user details modal
5. **MobileUserManagementPart5.js** - Contains styles and export statement

## Combining Steps

1. Create a new file named `MobileUserManagement.js` in the `src/screens/admin` directory.

2. Copy the content from each file in order:
   - Copy all content from `MobileUserManagementPart1.js`
   - Add the data fetching functions from `MobileUserManagementPart2.js` after the FAB actions
   - Add the user interaction functions from `MobileUserManagementPart2.js`
   - Add the render function and UI components from `MobileUserManagementPart3.js`
   - Add the confirmation dialogs and modals from `MobileUserManagementPart4.js`
   - Add the styles and export statement from `MobileUserManagementPart5.js`

3. Make sure there are no duplicate function declarations or missing closing brackets.

4. The final structure should look like this:

```javascript
// Imports...

const MobileUserManagement = () => {
  // State variables...
  
  // Bottom navigation items...
  
  // FAB actions...
  
  // Data fetching functions...
  
  // User interaction functions...
  
  // Render function...
  return (
    <MobileScreenWrapper>
      {/* UI components... */}
      
      {/* Confirmation dialogs... */}
      
      {/* Snackbar... */}
    </MobileScreenWrapper>
  );
};

// Styles...

export default MobileUserManagement;
```

## Adding to Navigation

After combining the files, add the screen to your navigation stack in `src/navigation/AdminNavigator.js`:

```javascript
import MobileUserManagement from '../screens/admin/MobileUserManagement';

// In your navigation stack
<Stack.Screen 
  name="UserManagement" 
  component={MobileUserManagement} 
  options={{ 
    headerShown: false 
  }} 
/>
```

## Translation Keys

Make sure the following translation keys are available in your language files:

```json
{
  "userManagement": {
    "title": "User Management",
    "subtitle": "Manage all users in the system",
    "searchUsers": "Search users...",
    "addUser": "Add User",
    "addFirstUser": "Add First User",
    "clearSearch": "Clear Search",
    "noUsersFound": "No users found",
    "noSearchResults": "No users match your search",
    "totalUsers": "Total Users",
    "activeUsers": "Active Users",
    "inactiveUsers": "Inactive Users",
    "newUsers": "New Users",
    "userActivated": "User activated successfully",
    "userDeactivated": "User deactivated successfully",
    "userDeleted": "User deleted successfully",
    "errorFetching": "Error fetching users",
    "errorUpdating": "Error updating user status",
    "errorDeleting": "Error deleting user",
    "confirmDelete": "Confirm Delete",
    "deleteWarning": "Are you sure you want to delete this user? This action cannot be undone.",
    "confirmActivate": "Confirm Activate",
    "activateWarning": "Are you sure you want to activate this user?",
    "confirmDeactivate": "Confirm Deactivate",
    "deactivateWarning": "Are you sure you want to deactivate this user?",
    "activate": "Activate",
    "deactivate": "Deactivate",
    "resetPassword": "Reset Password",
    "resetPasswordWarning": "Send a password reset email to this user?",
    "sendResetLink": "Send Reset Link",
    "passwordResetSent": "Password reset email sent successfully",
    "errorPasswordReset": "Failed to send password reset email",
    "userDetails": "User Details",
    "accountInfo": "Account Information",
    "contactInfo": "Contact Information",
    "role": "Role",
    "email": "Email",
    "phone": "Phone",
    "createdAt": "Created At",
    "lastLogin": "Last Login",
    "roles": {
      "admin": "Administrator",
      "teacher": "Teacher",
      "student": "Student",
      "parent": "Parent",
      "unknown": "Unknown"
    },
    "status": {
      "status": "Status",
      "active": "Active",
      "inactive": "Inactive"
    }
  },
  "common": {
    "dashboard": "Dashboard",
    "filter": "Filter",
    "sort": "Sort",
    "refresh": "Refresh",
    "edit": "Edit",
    "delete": "Delete",
    "cancel": "Cancel",
    "dismiss": "Dismiss",
    "noInternetConnection": "No internet connection",
    "errorFetchingData": "Error fetching data",
    "notAvailable": "Not available",
    "more": "More"
  },
  "auth": {
    "errors": {
      "userNotFound": "User not found with this email",
      "invalidEmail": "Invalid email format"
    }
  }
}
```
