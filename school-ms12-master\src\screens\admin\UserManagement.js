import React, { useState, useEffect, useRef } from 'react';
import { View, StyleSheet, Animated, TouchableOpacity, RefreshControl, Dimensions, ScrollView } from 'react-native';
import {
  FAB,
  Searchbar,
  Portal,
  Modal,
  Text,
  Surface,
  Avatar,
  IconButton,
  useTheme,
  Divider,
  Chip,
  Menu,
  Snackbar,
  ActivityIndicator,
  Title,
  TextInput,
  Dialog,
  Button
} from 'react-native-paper';
import { SwipeListView } from 'react-native-swipe-list-view';
import { db, auth } from '../../config/firebase';
import { collection, query, getDocs, doc, getDoc, updateDoc, deleteDoc, addDoc, orderBy, where, limit, writeBatch } from 'firebase/firestore';
import { sendPasswordResetEmail, deleteUser } from 'firebase/auth';
import AdminService from '../../services/AdminService';
import CustomButton from '../../components/common/CustomButton';
import { useLanguage } from '../../context/LanguageContext';
import { useNotifications } from '../../context/NotificationContext';
import * as Animatable from 'react-native-animatable';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { format, formatDistance } from 'date-fns';
import { useNavigation } from '@react-navigation/native';
import AdminScreenWrapper from '../../components/common/AdminScreenWrapper';
import { useAdminSidebar } from '../../context/AdminSidebarContext';

const UserManagement = () => {
  const { translate, getTextStyle, isRTL } = useLanguage();
  const { showToast } = useNotifications();
  // No theme needed
  const navigation = useNavigation();
  const [users, setUsers] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [visible, setVisible] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [fadeAnim] = useState(new Animated.Value(0));
  const [drawerOpen, setDrawerOpen] = useState(false);
  const drawerAnim = useRef(new Animated.Value(-300)).current;
  const backdropFadeAnim = useRef(new Animated.Value(0)).current;
  const [filterRole, setFilterRole] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');
  const [sortBy, setSortBy] = useState('name');
  const [sortDirection, setSortDirection] = useState('asc');
  const [showFilterMenu, setShowFilterMenu] = useState(false);
  const [showSortMenu, setShowSortMenu] = useState(false);
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [addUserMenuVisible, setAddUserMenuVisible] = useState(false);
  const [userStats, setUserStats] = useState({
    total: 0,
    active: 0,
    inactive: 0,
    new: 0
  });
  const [error, setError] = useState(null);

  // New state for confirmation dialogs
  const [showDeleteConfirmDialog, setShowDeleteConfirmDialog] = useState(false);
  const [showActivateConfirmDialog, setShowActivateConfirmDialog] = useState(false);
  const [showDeactivateConfirmDialog, setShowDeactivateConfirmDialog] = useState(false);

  // New state for password reset
  const [showPasswordResetDialog, setShowPasswordResetDialog] = useState(false);
  const [passwordResetLoading, setPasswordResetLoading] = useState(false);

  // No need for FAB reference as we're using FAB.Group

  useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 1000,
      useNativeDriver: true,
    }).start();
    fetchUsers();
  }, []);

  useEffect(() => {
    navigation.setOptions({
      headerShown: false,
    });
  }, [navigation]);

  const toggleDrawer = () => {
    if (drawerOpen) {
      // Close drawer
      Animated.parallel([
        Animated.timing(drawerAnim, {
          toValue: -300,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(backdropFadeAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        })
      ]).start(() => setDrawerOpen(false));
    } else {
      // Open drawer
      setDrawerOpen(true);
      Animated.parallel([
        Animated.timing(drawerAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(backdropFadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        })
      ]).start();
    }
  };

  // Refresh data when filters or sort options change
  useEffect(() => {
    if (!loading) {
      fetchUsers();
    }
  }, [filterRole, filterStatus, sortBy, sortDirection]);

  const fetchUsers = async () => {
    try {
      setLoading(true);
      setError(null);
      const usersRef = collection(db, 'users');

      // Create query
      let q = query(usersRef);

      // Apply role filter if not 'all'
      if (filterRole !== 'all') {
        q = query(q, where('role', '==', filterRole));
      }

      // Apply status filter if not 'all'
      if (filterStatus !== 'all') {
        q = query(q, where('status', '==', filterStatus));
      }

      // Apply sorting if field exists
      if (sortBy === 'name') {
        // Special case for name sorting since it's a combination of firstName and lastName
        q = query(q, orderBy('firstName', sortDirection));
      } else if (sortBy) {
        q = query(q, orderBy(sortBy, sortDirection));
      }

      // Limit to 100 users for performance
      q = query(q, limit(100));

      const querySnapshot = await getDocs(q);

      const usersData = [];
      let activeCount = 0;
      let inactiveCount = 0;
      let newCount = 0;
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      querySnapshot.forEach((docSnapshot) => {
        const userData = { id: docSnapshot.id, ...docSnapshot.data() };

        // Ensure all required fields exist
        const processedUser = {
          ...userData,
          firstName: userData.firstName || '',
          lastName: userData.lastName || '',
          email: userData.email || '',
          role: userData.role || 'unknown',
          status: userData.status || 'inactive',
          createdAt: userData.createdAt || null,
          lastLogin: userData.lastLogin || null,
          phoneNumber: userData.phoneNumber || '',
        };

        // Count for stats
        if (processedUser.status === 'active') activeCount++;
        else inactiveCount++;

        if (processedUser.createdAt && new Date(processedUser.createdAt) > thirtyDaysAgo) {
          newCount++;
        }

        usersData.push(processedUser);
      });

      console.log(translate('userManagement.fetchedUsers', { count: usersData.length }));

      setUsers(usersData);
      setUserStats({
        total: usersData.length,
        active: activeCount,
        inactive: inactiveCount,
        new: newCount
      });

      setLoading(false);
      setRefreshing(false);
    } catch (error) {
      console.error('Error fetching users:', error);
      setError(translate('userManagement.errorFetching'));
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleUserPress = (user) => {
    setSelectedUser(user);
    setVisible(true);
  };

  const handleRefresh = () => {
    setRefreshing(true);
    fetchUsers();
  };

  const handleStatusChange = async (userId, newStatus) => {
    try {
      // Get user data to determine role
      const userRef = doc(db, 'users', userId);
      const userDoc = await getDoc(userRef);

      if (!userDoc.exists()) {
        throw new Error('User not found');
      }

      const userData = userDoc.data();

      // Update user document
      await updateDoc(userRef, {
        status: newStatus,
        updatedAt: new Date().toISOString()
      });

      // Update role-specific document if it exists
      if (userData.role) {
        try {
          const roleRef = doc(db, `${userData.role}s`, userId);
          const roleDoc = await getDoc(roleRef);

          if (roleDoc.exists()) {
            await updateDoc(roleRef, {
              status: newStatus,
              updatedAt: new Date().toISOString()
            });
          }
        } catch (roleError) {
          console.error('Error updating role document:', roleError);
          // Continue even if role document update fails
        }
      }

      // Update local state
      setUsers(users.map(user =>
        user.id === userId ? {...user, status: newStatus} : user
      ));

      // Update selected user if it's the one being modified
      if (selectedUser && selectedUser.id === userId) {
        setSelectedUser({...selectedUser, status: newStatus});
      }

      // Show success message
      setSnackbarMessage(newStatus === 'active'
        ? (translate('userManagement.userActivated') || 'User activated successfully')
        : (translate('userManagement.userDeactivated') || 'User deactivated successfully'));
      setSnackbarVisible(true);

      // Log the status change
      try {
        await addDoc(collection(db, 'user_status_logs'), {
          userId,
          adminId: auth.currentUser?.uid,
          oldStatus: userData.status,
          newStatus,
          timestamp: new Date().toISOString()
        });
      } catch (logError) {
        console.warn('Failed to log status change:', logError.message);
        // Continue even if logging fails
      }

    } catch (error) {
      console.error('Error updating user status:', error);
      setSnackbarMessage(translate('userManagement.errorUpdating') || 'Error updating user status');
      setSnackbarVisible(true);
    }
  };

  const handleDeleteUser = async (userId) => {
    try {
      // Get user data to determine role
      const userRef = doc(db, 'users', userId);
      const userDoc = await getDoc(userRef);

      if (!userDoc.exists()) {
        throw new Error('User not found');
      }

      const userData = userDoc.data();

      // Create a batch to delete all related documents
      const batch = writeBatch(db);

      // Delete user document
      batch.delete(userRef);

      // Delete role-specific document if it exists
      if (userData.role) {
        try {
          const roleRef = doc(db, `${userData.role}s`, userId);
          const roleDoc = await getDoc(roleRef);

          if (roleDoc.exists()) {
            batch.delete(roleRef);
          }
        } catch (roleError) {
          console.error('Error preparing role document for deletion:', roleError);
        }
      }

      // Commit the batch to delete Firestore documents
      await batch.commit();

      // Try to delete the user from Firebase Authentication
      // Note: This is a limitation in client-side Firebase Auth - we can only delete the current user
      let authDeleted = false;
      try {
        // Check if the user being deleted is the current user
        const currentUser = auth.currentUser;
        if (currentUser && currentUser.uid === userId) {
          await deleteUser(currentUser);
          console.log('Successfully deleted user from Firebase Auth');
          authDeleted = true;
        } else {
          // We can't delete other users from Auth in client-side code
          // This is a Firebase limitation - in a production app, you would use Firebase Admin SDK on a server

          // Instead of showing a warning in the console, we'll show a more user-friendly message
          console.log('User deleted from Firestore. Firebase Auth deletion requires server-side code.');

          // Log this action for future server-side cleanup
          await addDoc(collection(db, 'auth_deletion_queue'), {
            userId: userId,
            email: userData.email,
            requestedBy: auth.currentUser?.uid || 'unknown',
            timestamp: new Date().toISOString(),
            processed: false
          });
        }
      } catch (authError) {
        console.error('Error deleting user from Firebase Auth:', authError);
        // We continue even if auth deletion fails since we've already deleted from Firestore
      }

      // Update local state
      setUsers(users.filter(user => user.id !== userId));

      // Close modal if the deleted user was selected
      if (selectedUser && selectedUser.id === userId) {
        setVisible(false);
      }

      // Show success message with toast notification
      const successMessage = translate('userManagement.userDeleted') || 'User deleted successfully';
      showToast(
        translate('userManagement.success') || 'Success',
        authDeleted
          ? successMessage
          : successMessage + ' ' + (translate('userManagement.authDeletionPending') || '(Auth account will be cleaned up later)'),
        'success',
        'UserManagement'
      );

      // Also show snackbar for immediate feedback
      setSnackbarMessage(successMessage);
      setSnackbarVisible(true);

    } catch (error) {
      console.error('Error deleting user:', error);

      // Show error message with toast notification
      showToast(
        translate('common.error') || 'Error',
        error.message || translate('userManagement.errorDeleting') || 'Error deleting user',
        'error'
      );

      // Also show snackbar for immediate feedback
      setSnackbarMessage(translate('userManagement.errorDeleting') || 'Error deleting user');
      setSnackbarVisible(true);
    }
  };

  // Handle password reset for a user
  const handlePasswordReset = async (email) => {
    try {
      setPasswordResetLoading(true);

      if (!email) {
        throw new Error('Email is required');
      }

      // Use Firebase's password reset functionality
      await sendPasswordResetEmail(auth, email);

      // Close the dialog
      setShowPasswordResetDialog(false);

      // Show success message
      setSnackbarMessage(translate('userManagement.passwordResetSent') || 'Password reset email sent successfully');
      setSnackbarVisible(true);

      // Log the password reset request
      try {
        await addDoc(collection(db, 'password_reset_logs'), {
          email,
          adminId: auth.currentUser?.uid,
          timestamp: new Date().toISOString(),
          status: 'requested'
        });
      } catch (logError) {
        console.warn('Failed to log password reset request:', logError.message);
        // Continue even if logging fails
      }

    } catch (error) {
      console.error('Error sending password reset:', error);

      let errorMessage = translate('userManagement.errorPasswordReset') || 'Failed to send password reset email';

      // Provide more specific error messages
      if (error.code === 'auth/user-not-found') {
        errorMessage = translate('auth.errors.userNotFound') || 'User not found with this email';
      } else if (error.code === 'auth/invalid-email') {
        errorMessage = translate('auth.errors.invalidEmail') || 'Invalid email format';
      } else if (error.message) {
        errorMessage += ': ' + error.message;
      }

      setSnackbarMessage(errorMessage);
      setSnackbarVisible(true);
    } finally {
      setPasswordResetLoading(false);
    }
  };

  // Apply search filter only (role and status filters are applied in the query)
  const filteredUsers = users.filter(user => {
    if (!searchQuery) return true;

    const query = searchQuery.toLowerCase().trim();

    // Search in multiple fields
    return (
      (user.firstName?.toLowerCase() || '').includes(query) ||
      (user.lastName?.toLowerCase() || '').includes(query) ||
      (user.email?.toLowerCase() || '').includes(query) ||
      (user.phoneNumber?.toLowerCase() || '').includes(query) ||
      (`${user.firstName} ${user.lastName}`.toLowerCase()).includes(query) ||
      (user.role?.toLowerCase() || '').includes(query)
    );
  });

  // Sort the filtered users
  filteredUsers.sort((a, b) => {
    let valueA, valueB;

    switch (sortBy) {
      case 'name':
        valueA = `${a.firstName || ''} ${a.lastName || ''}`.toLowerCase();
        valueB = `${b.firstName || ''} ${b.lastName || ''}`.toLowerCase();
        break;
      case 'email':
        valueA = (a.email || '').toLowerCase();
        valueB = (b.email || '').toLowerCase();
        break;
      case 'role':
        valueA = (a.role || '').toLowerCase();
        valueB = (b.role || '').toLowerCase();
        break;
      case 'status':
        valueA = (a.status || '').toLowerCase();
        valueB = (b.status || '').toLowerCase();
        break;
      case 'createdAt':
        valueA = a.createdAt ? new Date(a.createdAt) : new Date(0);
        valueB = b.createdAt ? new Date(b.createdAt) : new Date(0);
        break;
      default:
        valueA = (a.firstName || '').toLowerCase();
        valueB = (b.firstName || '').toLowerCase();
    }

    if (sortDirection === 'asc') {
      return valueA > valueB ? 1 : -1;
    } else {
      return valueA < valueB ? 1 : -1;
    }
  });

  const UserDetailsModal = () => (
    <Portal>
      <Modal
        visible={visible}
        onDismiss={() => setVisible(false)}
        contentContainerStyle={[styles.modal, { backgroundColor: '#ffffff' }]}
        accessibilityLabel={translate('userManagement.userDetails')}
      >
        <LinearGradient
          colors={['#1976d2', '#005cb2']}
          style={styles.modalHeader}
        >
          <Avatar.Text
            size={64}
            label={`${selectedUser?.firstName?.charAt(0) || ''}${selectedUser?.lastName?.charAt(0) || 'U'}`}
            style={[styles.modalAvatar, {
              backgroundColor: selectedUser?.role === 'admin' ? 'rgba(92, 107, 192, 0.8)' :
                              selectedUser?.role === 'teacher' ? 'rgba(38, 166, 154, 0.8)' :
                              selectedUser?.role === 'student' ? 'rgba(239, 83, 80, 0.8)' :
                              selectedUser?.role === 'parent' ? 'rgba(255, 167, 38, 0.8)' : 'rgba(255, 255, 255, 0.2)'
            }]}
          />
          <Text style={[styles.modalTitle, { color: '#ffffff' }, getTextStyle()]}>
            {selectedUser?.firstName} {selectedUser?.lastName}
          </Text>
          <Text style={[styles.modalSubtitle, { color: '#ffffff' }, getTextStyle()]}>
            {selectedUser?.email}
          </Text>
          <IconButton
            icon="close"
            color={'#ffffff'}
            size={20}
            style={styles.closeButton}
            onPress={() => setVisible(false)}
            accessibilityLabel={translate('common.cancel')}
            tooltip={translate('common.cancel')}
          />
        </LinearGradient>

        <View style={styles.modalContent}>
          <View style={styles.modalInfo}>
            <Text style={[styles.modalLabel, { color: '#333333' }, getTextStyle()]}>
              {translate('userManagement.role')}
            </Text>
            <Chip
              mode="outlined"
              style={[styles.roleChip, { borderColor: '#1976d2' }]}
              textStyle={getTextStyle()}
            >
              {translate(`userManagement.${selectedUser?.role || 'unknown'}`)}
            </Chip>
          </View>

          <Divider style={styles.modalDivider} />

          <View style={styles.modalInfo}>
            <Text style={[styles.modalLabel, { color: '#333333' }, getTextStyle()]}>
              {translate('userManagement.status')}
            </Text>
            <Chip
              mode="outlined"
              style={[styles.statusChip, {
                backgroundColor: selectedUser?.status === 'active' ? '#e8f5e9' : '#ffebee',
                borderColor: selectedUser?.status === 'active' ? '#4caf50' : '#f44336'
              }]}
              textStyle={getTextStyle({ color: selectedUser?.status === 'active' ? '#2e7d32' : '#c62828' })}
            >
              {translate(`userManagement.${selectedUser?.status || 'unknown'}`)}
            </Chip>
          </View>

          <Divider style={styles.modalDivider} />

          <View style={styles.modalInfo}>
            <Text style={[styles.modalLabel, { color: '#333333' }, getTextStyle()]}>
              {translate('userManagement.phoneNumber')}
            </Text>
            <Text style={[styles.modalValue, { color: '#333333' }, getTextStyle()]}>
              {selectedUser?.phoneNumber || translate('userManagement.unknown')}
            </Text>
          </View>

          <Divider style={styles.modalDivider} />

          <View style={styles.modalInfo}>
            <Text style={[styles.modalLabel, { color: '#333333' }, getTextStyle()]}>
              {translate('userManagement.lastLogin')}
            </Text>
            <Text style={[styles.modalValue, { color: '#333333' }, getTextStyle()]}>
              {selectedUser?.lastLogin
                ? (selectedUser.lastLogin instanceof Date || !isNaN(new Date(selectedUser.lastLogin).getTime()))
                  ? formatDistance(new Date(selectedUser.lastLogin), new Date(), { addSuffix: true })
                  : translate('userManagement.never')
                : translate('userManagement.never')}
            </Text>
          </View>

          <Divider style={styles.modalDivider} />

          <View style={styles.modalInfo}>
            <Text style={[styles.modalLabel, { color: '#333333' }, getTextStyle()]}>
              {translate('userManagement.createdAt')}
            </Text>
            <Text style={[styles.modalValue, { color: '#333333' }, getTextStyle()]}>
              {selectedUser?.createdAt
                ? (selectedUser.createdAt instanceof Date || !isNaN(new Date(selectedUser.createdAt).getTime()))
                  ? format(new Date(selectedUser.createdAt), 'PPP')
                  : translate('userManagement.unknown')
                : translate('userManagement.unknown')}
            </Text>
          </View>
        </View>

        <View style={styles.modalActions}>
          {selectedUser?.status === 'active' ? (
            <CustomButton
              mode="outlined"
              onPress={() => setShowDeactivateConfirmDialog(true)}
              style={[styles.modalButton, styles.deactivateButton]}
              icon="account-off"
              labelStyle={getTextStyle()}
            >
              {translate('userManagement.deactivate')}
            </CustomButton>
          ) : (
            <CustomButton
              mode="outlined"
              onPress={() => setShowActivateConfirmDialog(true)}
              style={[styles.modalButton, styles.activateButton]}
              icon="account-check"
              labelStyle={getTextStyle()}
            >
              {translate('userManagement.activate')}
            </CustomButton>
          )}

          <CustomButton
            mode="outlined"
            onPress={() => setShowDeleteConfirmDialog(true)}
            style={[styles.modalButton, styles.deleteButton]}
            icon="delete"
            labelStyle={getTextStyle()}
          >
            {translate('common.delete')}
          </CustomButton>

          <CustomButton
            mode="outlined"
            onPress={() => setShowPasswordResetDialog(true)}
            style={[styles.modalButton, { borderColor: '#ff9800' }]}
            icon="key"
            labelStyle={getTextStyle()}
          >
            {translate('userManagement.resetPassword') || 'Reset Password'}
          </CustomButton>

          <CustomButton
            mode="contained"
            onPress={() => {
              setVisible(false);
              // Navigate to the appropriate management screen based on user role
              if (selectedUser?.role === 'admin') {
                navigation.navigate('AdminManagement', { userId: selectedUser.id });
              } else if (selectedUser?.role === 'teacher') {
                navigation.navigate('TeacherManagement', { userId: selectedUser.id });
              } else if (selectedUser?.role === 'student') {
                navigation.navigate('StudentManagement', { userId: selectedUser.id });
              } else if (selectedUser?.role === 'parent') {
                navigation.navigate('ParentManagement', { userId: selectedUser.id });
              }
            }}
            style={[styles.modalButton, { backgroundColor: '#1976d2' }]}
            icon="account-edit"
            labelStyle={[getTextStyle(), { color: '#ffffff' }]}
            accessibilityLabel={translate('userManagement.tooltipEdit')}
          >
            {translate('userManagement.editUser')}
          </CustomButton>
        </View>
      </Modal>
    </Portal>
  );

  // Use the admin sidebar context
  const { setActiveSidebarItem } = useAdminSidebar();

  // Set the active sidebar item when the component mounts
  useEffect(() => {
    setActiveSidebarItem('UserManagement');
  }, []);

  return (
    <AdminScreenWrapper
      title={translate('userManagement.title')}
      showNotification={true}
    >
      <Animated.View style={[styles.contentContainer, { opacity: fadeAnim }]}>
      {/* Quick Access section removed */}

      {/* Stats section removed */}

      {/* Search and filter bar */}
      <Animatable.View animation="fadeInUp" duration={800} delay={300}>
        <Surface style={[styles.searchContainer, { backgroundColor: '#ffffff' }]}>
          <View style={styles.searchRow}>
            <Searchbar
              placeholder={translate('userManagement.search')}
              onChangeText={setSearchQuery}
              value={searchQuery}
              style={[styles.searchBar, isRTL && styles.searchBarRTL]}
              iconColor={'#1976d2'}
              inputStyle={getTextStyle({ textAlign: isRTL ? 'right' : 'left' })}
              accessibilityLabel={translate('userManagement.search')}
            />

            <View style={styles.filterButtons}>
              {/* Total users count */}
              <Animatable.View animation="fadeIn" delay={300}>
                <Chip
                  mode="outlined"
                  style={styles.totalUsersChip}
                  icon="account-group"
                >
                  <Text style={[styles.totalUsersText, getTextStyle()]}>
                    {userStats.total}
                  </Text>
                </Chip>
              </Animatable.View>

              <Animatable.View animation="fadeIn" delay={400}>
                <IconButton
                  icon="filter-variant"
                  color={'#1976d2'}
                  size={24}
                  onPress={() => setShowFilterMenu(true)}
                  style={styles.filterButton}
                  accessibilityLabel={translate('userManagement.tooltipFilter')}
                  tooltip={translate('userManagement.tooltipFilter')}
                />
              </Animatable.View>

              <Animatable.View animation="fadeIn" delay={500}>
                <IconButton
                  icon="sort"
                  color={'#1976d2'}
                  size={24}
                  onPress={() => setShowSortMenu(true)}
                  style={styles.sortButton}
                  accessibilityLabel={translate('userManagement.tooltipSort')}
                  tooltip={translate('userManagement.tooltipSort')}
                />
              </Animatable.View>

              {/* View mode toggle removed - list view only for mobile */}
            </View>
          </View>

        <View style={styles.activeFilters}>
          {filterRole !== 'all' && (
            <Chip
              mode="outlined"
              onClose={() => setFilterRole('all')}
              style={styles.filterChip}
              textStyle={[getTextStyle(), isRTL && styles.rtlText]}
              closeIconAccessibilityLabel={translate('userManagement.clearFilter')}
            >
              {translate('userManagement.filterByRole')}: {translate(`userManagement.${filterRole}`)}
            </Chip>
          )}

          {filterStatus !== 'all' && (
            <Chip
              mode="outlined"
              onClose={() => setFilterStatus('all')}
              style={styles.filterChip}
              textStyle={[getTextStyle(), isRTL && styles.rtlText]}
              closeIconAccessibilityLabel={translate('userManagement.clearFilter')}
            >
              {translate('userManagement.filterByStatus')}: {translate(`userManagement.${filterStatus}`)}
            </Chip>
          )}

          {(filterRole !== 'all' || filterStatus !== 'all') && (
            <Chip
              mode="outlined"
              onPress={() => {
                setFilterRole('all');
                setFilterStatus('all');
              }}
              style={styles.clearFiltersChip}
              textStyle={[getTextStyle(), isRTL && styles.rtlText]}
              icon="filter-remove"
            >
              {translate('userManagement.clearFilters')}
            </Chip>
          )}
        </View>
      </Surface>
      </Animatable.View>

      {/* Error message */}
      {error && (
        <Animatable.View animation="fadeIn" duration={500} style={styles.errorContainer}>
          <MaterialCommunityIcons name="alert-circle" size={24} color="#d32f2f" style={styles.errorIcon} />
          <Text style={[styles.errorText, getTextStyle(), isRTL && styles.rtlText]}>{error}</Text>
        </Animatable.View>
      )}

      {/* User list with swipe actions */}
      {loading ? (
        <Animatable.View animation="pulse" iterationCount="infinite" duration={1500} style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={'#1976d2'} />
          <Text style={[styles.loadingText, getTextStyle(), isRTL && styles.rtlText]}>
            {translate('userManagement.loading')}
          </Text>
        </Animatable.View>
      ) : filteredUsers.length === 0 ? (
        <Animatable.View animation="fadeIn" duration={800} style={styles.emptyContainer}>
          <MaterialCommunityIcons name="account-search" size={64} color={'#1976d2'} />
          <Text style={[styles.emptyText, getTextStyle(), isRTL && styles.rtlText]}>
            {translate('userManagement.noUsers')}
          </Text>
        </Animatable.View>
      ) : (
        <SwipeListView
          data={filteredUsers}
          keyExtractor={(item) => item.id}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={['#1976d2']}
              tintColor={'#1976d2'}
            />
          }
          contentContainerStyle={styles.scrollViewContent}
          renderItem={({ item: user, index }) => (
            <Animatable.View
              animation="fadeInUp"
              duration={500}
              delay={index * 100}
              style={styles.rowFront}
            >
              <View
                style={[styles.userCard, {
                  borderLeftColor: user.role === 'admin' ? '#5c6bc0' :
                                  user.role === 'teacher' ? '#26a69a' :
                                  user.role === 'student' ? '#ef5350' :
                                  user.role === 'parent' ? '#ffa726' : '#1976d2'
                }]}
              >
                <View style={styles.userCardHeader}>
                  <Avatar.Text
                    size={56}
                    label={`${user.firstName?.charAt(0) || ''}${user.lastName?.charAt(0) || 'U'}`}
                    style={[styles.userAvatar, {
                      backgroundColor: user.role === 'admin' ? '#5c6bc0' :
                                      user.role === 'teacher' ? '#26a69a' :
                                      user.role === 'student' ? '#ef5350' :
                                      user.role === 'parent' ? '#ffa726' : '#1976d2'
                    }]}
                  />
                  <View style={styles.userInfo}>
                    <Text style={[styles.userName, { color: '#333333' }, getTextStyle()]}>
                      {user.firstName} {user.lastName}
                    </Text>
                    <Text style={[styles.userEmail, getTextStyle()]}>
                      {user.email}
                    </Text>
                    <View style={styles.userMetaInfo}>
                      <Chip
                        mode="outlined"
                        style={[styles.roleChip, {
                          marginRight: 8,
                          borderColor: user.role === 'admin' ? '#5c6bc0' :
                                      user.role === 'teacher' ? '#26a69a' :
                                      user.role === 'student' ? '#ef5350' :
                                      user.role === 'parent' ? '#ffa726' : '#1976d2'
                        }]}
                        textStyle={[styles.chipText, getTextStyle({
                          color: user.role === 'admin' ? '#3949ab' :
                                user.role === 'teacher' ? '#00897b' :
                                user.role === 'student' ? '#d32f2f' :
                                user.role === 'parent' ? '#f57c00' : '#1976d2'
                        })]}
                      >
                        {translate(`userManagement.${user.role || 'unknown'}`)}
                      </Chip>
                      <Chip
                        mode="outlined"
                        style={[styles.statusChip, {
                          backgroundColor: user.status === 'active' ? '#e8f5e9' : '#ffebee',
                          borderColor: user.status === 'active' ? '#4caf50' : '#f44336'
                        }]}
                        textStyle={[styles.chipText, getTextStyle({
                          color: user.status === 'active' ? '#2e7d32' : '#c62828'
                        })]}
                      >
                        {translate(`userManagement.${user.status || 'unknown'}`)}
                      </Chip>
                    </View>
                  </View>
                  <IconButton
                    icon="chevron-right"
                    size={24}
                    color="#757575"
                    onPress={() => handleUserPress(user)}
                  />
                </View>

                <View style={styles.userCardFooter}>
                  <MaterialCommunityIcons name="calendar" size={16} color="#757575" />
                  <Text style={styles.joinDate}>
                    {user.createdAt && (user.createdAt instanceof Date || !isNaN(new Date(user.createdAt).getTime()))
                      ? format(new Date(user.createdAt), 'PP')
                      : translate('userManagement.unknown')}
                  </Text>
                </View>
              </View>
            </Animatable.View>
          )}
          renderHiddenItem={({ item: user }) => (
            <View style={styles.rowBack}>
              {/* Left swipe action - Edit */}
              <TouchableOpacity
                style={[styles.backLeftBtn, styles.backLeftBtnLeft]}
                onPress={() => {
                  if (user.role === 'admin') {
                    navigation.navigate('AdminManagement', { userId: user.id });
                  } else if (user.role === 'teacher') {
                    navigation.navigate('TeacherManagement', { userId: user.id });
                  } else if (user.role === 'student') {
                    navigation.navigate('StudentManagement', { userId: user.id });
                  } else if (user.role === 'parent') {
                    navigation.navigate('ParentManagement', { userId: user.id });
                  }
                }}
              >
                <MaterialCommunityIcons name="account-edit" size={24} color="white" />
                <Text style={styles.backTextWhite}>
                  {translate('common.edit')}
                </Text>
              </TouchableOpacity>

              {/* Right swipe action - Status change */}
              <TouchableOpacity
                style={[styles.backRightBtn, styles.backRightBtnLeft]}
                onPress={() => {
                  setSelectedUser(user);
                  if (user.status === 'active') {
                    setShowDeactivateConfirmDialog(true);
                  } else {
                    setShowActivateConfirmDialog(true);
                  }
                }}
              >
                <MaterialCommunityIcons
                  name={user.status === 'active' ? 'account-off' : 'account-check'}
                  size={24}
                  color="white"
                />
                <Text style={styles.backTextWhite}>
                  {user.status === 'active'
                    ? translate('userManagement.deactivate')
                    : translate('userManagement.activate')}
                </Text>
              </TouchableOpacity>

              {/* Right swipe action - Delete */}
              <TouchableOpacity
                style={[styles.backRightBtn, styles.backRightBtnRight]}
                onPress={() => {
                  setSelectedUser(user);
                  setShowDeleteConfirmDialog(true);
                }}
              >
                <MaterialCommunityIcons name="delete" size={24} color="white" />
                <Text style={styles.backTextWhite}>
                  {translate('common.delete')}
                </Text>
              </TouchableOpacity>
            </View>
          )}
          leftOpenValue={75}
          rightOpenValue={-150}
          previewRowKey={filteredUsers[0]?.id}
          previewOpenValue={-40}
          previewOpenDelay={3000}
          onRowDidOpen={(rowKey) => console.log('Row opened:', rowKey)}
        />
      )}



      {/* Add user FAB with dropup menu for mobile */}
      <Portal>
        <Animatable.View animation="fadeIn" duration={1000} delay={300}>
          <FAB.Group
            visible={true}
            open={addUserMenuVisible}
            icon={addUserMenuVisible ? 'close' : 'plus'}
            actions={[
              {
                icon: 'account-tie',
                label: translate('userManagement.addAdmin'),
                onPress: () => navigation.navigate('AdminManagement'),
                color: '#3949ab',
                accessibilityLabel: translate('userManagement.addAdmin'),
                tooltip: translate('userManagement.addAdmin'),
                style: { backgroundColor: '#e8eaf6' }
              },
              {
                icon: 'human-male-board',
                label: translate('userManagement.addTeacher'),
                onPress: () => navigation.navigate('TeacherManagement'),
                color: '#00897b',
                accessibilityLabel: translate('userManagement.addTeacher'),
                tooltip: translate('userManagement.addTeacher'),
                style: { backgroundColor: '#e0f2f1' }
              },
              {
                icon: 'school',
                label: translate('userManagement.addStudent'),
                onPress: () => navigation.navigate('StudentManagement'),
                color: '#d32f2f',
                accessibilityLabel: translate('userManagement.addStudent'),
                tooltip: translate('userManagement.addStudent'),
                style: { backgroundColor: '#ffebee' }
              },
              {
                icon: 'account-child',
                label: translate('userManagement.addParent'),
                onPress: () => navigation.navigate('ParentManagement'),
                color: '#f57c00',
                accessibilityLabel: translate('userManagement.addParent'),
                tooltip: translate('userManagement.addParent'),
                style: { backgroundColor: '#fff3e0' }
              },
            ]}
            onStateChange={({ open }) => setAddUserMenuVisible(open)}
            onPress={() => {
              if (!addUserMenuVisible) {
                setAddUserMenuVisible(true);
              }
            }}
            fabStyle={{
              backgroundColor: '#1976d2',
              shadowColor: '#000',
              shadowOffset: { width: 0, height: 2 },
              shadowOpacity: 0.8,
              shadowRadius: 2,
              elevation: 8,
            }}
            style={styles.fabGroupMobile}
            accessibilityLabel={translate('userManagement.addUser')}
            tooltip={translate('userManagement.addUser')}
          />
        </Animatable.View>
      </Portal>

      {/* Standalone FAB as fallback */}
      <FAB
        style={styles.standaloneFab}
        icon="plus"
        color="#ffffff"
        visible={true}
        onPress={() => setAddUserMenuVisible(!addUserMenuVisible)}
      />

      {/* Filter menu */}
      <Menu
        visible={showFilterMenu}
        onDismiss={() => setShowFilterMenu(false)}
        anchor={{ x: 0, y: 0 }}
        style={[styles.menu, { backgroundColor: '#ffffff' }]}
      >
        <Menu.Item
          onPress={() => {
            setFilterRole('all');
            setShowFilterMenu(false);
          }}
          title={translate('userManagement.allRoles')}
          titleStyle={getTextStyle()}
        />
        <Menu.Item
          onPress={() => {
            setFilterRole('admin');
            setShowFilterMenu(false);
          }}
          title={translate('userManagement.admin')}
          titleStyle={getTextStyle()}
        />
        <Menu.Item
          onPress={() => {
            setFilterRole('teacher');
            setShowFilterMenu(false);
          }}
          title={translate('userManagement.teacher')}
          titleStyle={getTextStyle()}
        />
        <Menu.Item
          onPress={() => {
            setFilterRole('student');
            setShowFilterMenu(false);
          }}
          title={translate('userManagement.student')}
          titleStyle={getTextStyle()}
        />
        <Menu.Item
          onPress={() => {
            setFilterRole('parent');
            setShowFilterMenu(false);
          }}
          title={translate('userManagement.parent')}
          titleStyle={getTextStyle()}
        />
        <Divider />
        <Menu.Item
          onPress={() => {
            setFilterStatus('all');
            setShowFilterMenu(false);
          }}
          title={translate('userManagement.allStatuses')}
          titleStyle={getTextStyle()}
        />
        <Menu.Item
          onPress={() => {
            setFilterStatus('active');
            setShowFilterMenu(false);
          }}
          title={translate('userManagement.active')}
          titleStyle={getTextStyle()}
        />
        <Menu.Item
          onPress={() => {
            setFilterStatus('inactive');
            setShowFilterMenu(false);
          }}
          title={translate('userManagement.inactive')}
          titleStyle={getTextStyle()}
        />
      </Menu>

      {/* Sort menu */}
      <Menu
        visible={showSortMenu}
        onDismiss={() => setShowSortMenu(false)}
        anchor={{ x: 0, y: 0 }}
        style={[styles.menu, { backgroundColor: '#ffffff' }]}
      >
        <Menu.Item
          onPress={() => {
            setSortBy('name');
            setShowSortMenu(false);
          }}
          title={translate('userManagement.sortName')}
          titleStyle={getTextStyle()}
          leadingIcon={sortBy === 'name' ? 'check' : undefined}
        />
        <Menu.Item
          onPress={() => {
            setSortBy('email');
            setShowSortMenu(false);
          }}
          title={translate('userManagement.sortEmail')}
          titleStyle={getTextStyle()}
          leadingIcon={sortBy === 'email' ? 'check' : undefined}
        />
        <Menu.Item
          onPress={() => {
            setSortBy('role');
            setShowSortMenu(false);
          }}
          title={translate('userManagement.sortRole')}
          titleStyle={getTextStyle()}
          leadingIcon={sortBy === 'role' ? 'check' : undefined}
        />
        <Menu.Item
          onPress={() => {
            setSortBy('status');
            setShowSortMenu(false);
          }}
          title={translate('userManagement.sortStatus')}
          titleStyle={getTextStyle()}
          leadingIcon={sortBy === 'status' ? 'check' : undefined}
        />
        <Menu.Item
          onPress={() => {
            setSortBy('createdAt');
            setShowSortMenu(false);
          }}
          title={translate('userManagement.sortCreated')}
          titleStyle={getTextStyle()}
          leadingIcon={sortBy === 'createdAt' ? 'check' : undefined}
        />
        <Divider />
        <Menu.Item
          onPress={() => {
            setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
            setShowSortMenu(false);
          }}
          title={sortDirection === 'asc' ? translate('userManagement.ascending') : translate('userManagement.descending')}
          titleStyle={getTextStyle()}
        />
      </Menu>

      {/* Snackbar for notifications */}
      <Snackbar
        visible={snackbarVisible}
        onDismiss={() => setSnackbarVisible(false)}
        duration={3000}
        style={styles.snackbar}
        action={{
          label: translate('common.dismiss'),
          onPress: () => setSnackbarVisible(false),
        }}
      >
        <Text style={[getTextStyle({ color: 'white' }), isRTL && styles.rtlText]}>{snackbarMessage}</Text>
      </Snackbar>

      {/* User Details Modal */}
      <Portal>
        <Modal
          visible={visible}
          onDismiss={() => setVisible(false)}
          contentContainerStyle={{
            backgroundColor: 'white',
            padding: 20,
            margin: 20,
            borderRadius: 12,
            maxHeight: '80%',
          }}
        >
          {selectedUser && (
            <Animatable.View animation="fadeIn" duration={300}>
              <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 16 }}>
                <Avatar.Text
                  size={64}
                  label={`${selectedUser.firstName?.charAt(0) || ''}${selectedUser.lastName?.charAt(0) || 'U'}`}
                  style={{
                    backgroundColor: selectedUser.role === 'admin' ? '#5c6bc0' :
                                    selectedUser.role === 'teacher' ? '#26a69a' :
                                    selectedUser.role === 'student' ? '#ef5350' :
                                    selectedUser.role === 'parent' ? '#ffa726' : '#1976d2',
                    marginRight: 16
                  }}
                />
                <View style={{ flex: 1 }}>
                  <Text style={[{ fontSize: 20, fontWeight: 'bold', marginBottom: 4 }, getTextStyle()]}>
                    {selectedUser.firstName} {selectedUser.lastName}
                  </Text>
                  <Chip
                    mode="outlined"
                    style={{
                      alignSelf: 'flex-start',
                      borderColor: selectedUser.role === 'admin' ? '#5c6bc0' :
                                  selectedUser.role === 'teacher' ? '#26a69a' :
                                  selectedUser.role === 'student' ? '#ef5350' :
                                  selectedUser.role === 'parent' ? '#ffa726' : '#1976d2'
                    }}
                    textStyle={[getTextStyle({
                      color: selectedUser.role === 'admin' ? '#3949ab' :
                            selectedUser.role === 'teacher' ? '#00897b' :
                            selectedUser.role === 'student' ? '#d32f2f' :
                            selectedUser.role === 'parent' ? '#f57c00' : '#1976d2'
                    })]}
                  >
                    {translate(`userManagement.${selectedUser.role || 'unknown'}`)}
                  </Chip>
                </View>
                <IconButton
                  icon="close"
                  size={24}
                  onPress={() => setVisible(false)}
                />
              </View>

              <Divider style={{ marginVertical: 8 }} />

              <ScrollView style={{ maxHeight: 400 }}>
                <View style={{ marginBottom: 16 }}>
                  <Text style={[{ fontSize: 14, color: '#666', marginBottom: 4 }, getTextStyle()]}>
                    {translate('userManagement.email')}
                  </Text>
                  <Text style={[{ fontSize: 16 }, getTextStyle()]}>
                    {selectedUser.email || translate('userManagement.notProvided')}
                  </Text>
                </View>

                <View style={{ marginBottom: 16 }}>
                  <Text style={[{ fontSize: 14, color: '#666', marginBottom: 4 }, getTextStyle()]}>
                    {translate('userManagement.phoneNumber')}
                  </Text>
                  <Text style={[{ fontSize: 16 }, getTextStyle()]}>
                    {selectedUser.phoneNumber || translate('userManagement.notProvided')}
                  </Text>
                </View>

                <View style={{ marginBottom: 16 }}>
                  <Text style={[{ fontSize: 14, color: '#666', marginBottom: 4 }, getTextStyle()]}>
                    {translate('userManagement.status')}
                  </Text>
                  <Chip
                    mode="outlined"
                    style={{
                      alignSelf: 'flex-start',
                      backgroundColor: selectedUser.status === 'active' ? '#e8f5e9' : '#ffebee',
                      borderColor: selectedUser.status === 'active' ? '#4caf50' : '#f44336'
                    }}
                    textStyle={[getTextStyle({
                      color: selectedUser.status === 'active' ? '#2e7d32' : '#c62828'
                    })]}
                  >
                    {translate(`userManagement.${selectedUser.status || 'unknown'}`)}
                  </Chip>
                </View>

                <View style={{ marginBottom: 16 }}>
                  <Text style={[{ fontSize: 14, color: '#666', marginBottom: 4 }, getTextStyle()]}>
                    {translate('userManagement.createdAt')}
                  </Text>
                  <Text style={[{ fontSize: 16 }, getTextStyle()]}>
                    {selectedUser.createdAt && (selectedUser.createdAt instanceof Date || !isNaN(new Date(selectedUser.createdAt).getTime()))
                      ? format(new Date(selectedUser.createdAt), 'PPpp')
                      : translate('userManagement.unknown')}
                  </Text>
                </View>

                <View style={{ marginBottom: 16 }}>
                  <Text style={[{ fontSize: 14, color: '#666', marginBottom: 4 }, getTextStyle()]}>
                    {translate('userManagement.lastLogin')}
                  </Text>
                  <Text style={[{ fontSize: 16 }, getTextStyle()]}>
                    {selectedUser.lastLogin && (selectedUser.lastLogin instanceof Date || !isNaN(new Date(selectedUser.lastLogin).getTime()))
                      ? format(new Date(selectedUser.lastLogin), 'PPpp')
                      : translate('userManagement.never')}
                  </Text>
                </View>
              </ScrollView>

              <Divider style={{ marginVertical: 16 }} />

              <View style={{ flexDirection: 'row', justifyContent: 'space-around' }}>
                <Button
                  mode="outlined"
                  icon="account-edit"
                  onPress={() => {
                    setVisible(false);
                    if (selectedUser.role === 'admin') {
                      navigation.navigate('AdminManagement', { userId: selectedUser.id });
                    } else if (selectedUser.role === 'teacher') {
                      navigation.navigate('TeacherManagement', { userId: selectedUser.id });
                    } else if (selectedUser.role === 'student') {
                      navigation.navigate('StudentManagement', { userId: selectedUser.id });
                    } else if (selectedUser.role === 'parent') {
                      navigation.navigate('ParentManagement', { userId: selectedUser.id });
                    }
                  }}
                  style={{ marginRight: 8 }}
                >
                  {translate('userManagement.edit')}
                </Button>

                <Button
                  mode="outlined"
                  icon="key"
                  onPress={() => {
                    setShowPasswordResetDialog(true);
                  }}
                  style={{ marginRight: 8 }}
                  color="#ff9800"
                >
                  {translate('userManagement.resetPassword')}
                </Button>

                {selectedUser.status === 'active' ? (
                  <Button
                    mode="outlined"
                    icon="account-off"
                    onPress={() => {
                      setShowDeactivateConfirmDialog(true);
                    }}
                    color="#f44336"
                  >
                    {translate('userManagement.deactivate')}
                  </Button>
                ) : (
                  <Button
                    mode="outlined"
                    icon="account-check"
                    onPress={() => {
                      setShowActivateConfirmDialog(true);
                    }}
                    color="#4caf50"
                  >
                    {translate('userManagement.activate')}
                  </Button>
                )}
              </View>
            </Animatable.View>
          )}
        </Modal>
      </Portal>

      {/* Confirmation Dialogs */}
      <Portal>
        {/* Delete Confirmation Dialog */}
        <Dialog
          visible={showDeleteConfirmDialog}
          onDismiss={() => setShowDeleteConfirmDialog(false)}
          style={{ backgroundColor: '#ffffff' }}
        >
          <Dialog.Title style={getTextStyle()}>
            {translate('userManagement.confirmDelete') || 'Confirm Delete'}
          </Dialog.Title>
          <Dialog.Content>
            <Text style={getTextStyle()}>
              {translate('userManagement.confirmDeleteMessage') ||
                'Are you sure you want to delete this user? This action cannot be undone.'}
            </Text>
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={() => setShowDeleteConfirmDialog(false)}>
              {translate('common.cancel') || 'Cancel'}
            </Button>
            <Button
              onPress={() => {
                setShowDeleteConfirmDialog(false);
                handleDeleteUser(selectedUser.id);
                setVisible(false);
              }}
              color="#f44336"
            >
              {translate('common.delete') || 'Delete'}
            </Button>
          </Dialog.Actions>
        </Dialog>

        {/* Activate Confirmation Dialog */}
        <Dialog
          visible={showActivateConfirmDialog}
          onDismiss={() => setShowActivateConfirmDialog(false)}
          style={{ backgroundColor: '#ffffff' }}
        >
          <Dialog.Title style={getTextStyle()}>
            {translate('userManagement.confirmActivate') || 'Confirm Activate'}
          </Dialog.Title>
          <Dialog.Content>
            <Text style={getTextStyle()}>
              {translate('userManagement.confirmActivateMessage') ||
                'Are you sure you want to activate this user?'}
            </Text>
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={() => setShowActivateConfirmDialog(false)}>
              {translate('common.cancel') || 'Cancel'}
            </Button>
            <Button
              onPress={() => {
                setShowActivateConfirmDialog(false);
                handleStatusChange(selectedUser.id, 'active');
                setVisible(false);
              }}
              color="#4caf50"
            >
              {translate('userManagement.activate') || 'Activate'}
            </Button>
          </Dialog.Actions>
        </Dialog>

        {/* Deactivate Confirmation Dialog */}
        <Dialog
          visible={showDeactivateConfirmDialog}
          onDismiss={() => setShowDeactivateConfirmDialog(false)}
          style={{ backgroundColor: '#ffffff' }}
        >
          <Dialog.Title style={getTextStyle()}>
            {translate('userManagement.confirmDeactivate') || 'Confirm Deactivate'}
          </Dialog.Title>
          <Dialog.Content>
            <Text style={getTextStyle()}>
              {translate('userManagement.confirmDeactivateMessage') ||
                'Are you sure you want to deactivate this user?'}
            </Text>
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={() => setShowDeactivateConfirmDialog(false)}>
              {translate('common.cancel') || 'Cancel'}
            </Button>
            <Button
              onPress={() => {
                setShowDeactivateConfirmDialog(false);
                handleStatusChange(selectedUser.id, 'inactive');
                setVisible(false);
              }}
              color="#f44336"
            >
              {translate('userManagement.deactivate') || 'Deactivate'}
            </Button>
          </Dialog.Actions>
        </Dialog>

        {/* Password Reset Dialog */}
        <Dialog
          visible={showPasswordResetDialog}
          onDismiss={() => setShowPasswordResetDialog(false)}
          style={{ backgroundColor: '#ffffff' }}
        >
          <Dialog.Title style={getTextStyle()}>
            {translate('userManagement.resetPassword') || 'Reset Password'}
          </Dialog.Title>
          <Dialog.Content>
            <Text style={[getTextStyle(), { marginBottom: 16 }]}>
              {translate('userManagement.resetPasswordMessage') ||
                'This will send a password reset email to the user. The user will be able to set a new password via the link in the email.'}
            </Text>
            <Text style={[getTextStyle(), { fontWeight: 'bold' }]}>
              {selectedUser?.email}
            </Text>
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={() => setShowPasswordResetDialog(false)}>
              {translate('common.cancel') || 'Cancel'}
            </Button>
            <Button
              onPress={() => handlePasswordReset(selectedUser?.email)}
              loading={passwordResetLoading}
              disabled={passwordResetLoading}
              color="#ff9800"
            >
              {translate('userManagement.sendResetEmail') || 'Send Reset Email'}
            </Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>
    </Animated.View>
    </AdminScreenWrapper>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  contentContainer: {
    flex: 1,
  },
  // Stats section
  statsContainer: {
    elevation: 4,
    marginBottom: 16,
    borderRadius: 12,
    marginHorizontal: 16,
    marginTop: 16,
    overflow: 'hidden',
  },
  statsGradient: {
    padding: 16,
  },
  statsTitle: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
    flexWrap: 'wrap',
  },
  statItem: {
    width: '48%',
    alignItems: 'center',
    padding: 4,
    marginBottom: 8,
  },
  statItemSurface: {
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    elevation: 2,
    backgroundColor: 'white',
    width: '90%',
  },
  statIcon: {
    marginBottom: 8,
  },
  statValue: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
  },
  // Search and filter section
  searchContainer: {
    padding: 16,
    elevation: 4,
    marginBottom: 8,
    marginHorizontal: 16,
    borderRadius: 12,
  },
  searchRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  searchBar: {
    flex: 1,
    elevation: 2,
    borderRadius: 8,
    marginRight: 8,
  },
  searchBarRTL: {
    marginRight: 0,
    marginLeft: 8,
  },
  filterButtons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  filterButton: {
    margin: 0,
  },
  sortButton: {
    margin: 0,
  },
  totalUsersChip: {
    backgroundColor: '#e3f2fd',
    borderColor: '#1976d2',
    marginRight: 8,
    height: 36,
  },
  totalUsersText: {
    color: '#1976d2',
    fontWeight: 'bold',
  },
  viewModeButton: {
    margin: 0,
  },
  activeFilters: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 8,
  },
  filterChip: {
    marginRight: 8,
    marginBottom: 8,
  },
  clearFiltersChip: {
    marginRight: 8,
    marginBottom: 8,
    backgroundColor: '#ffebee',
  },
  // Error and loading
  errorContainer: {
    backgroundColor: '#ffebee',
    padding: 16,
    margin: 16,
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: '#f44336',
    flexDirection: 'row',
    alignItems: 'center',
  },
  errorIcon: {
    marginRight: 8,
  },
  errorText: {
    color: '#d32f2f',
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  rtlText: {
    textAlign: 'right',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  emptyText: {
    marginTop: 16,
    fontSize: 16,
    textAlign: 'center',
  },
  // List view
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    padding: 16,
    paddingBottom: 100, // Extra padding at the bottom for FAB
  },
  userCard: {
    flexDirection: 'column',
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
    elevation: 3,
    backgroundColor: '#ffffff',
    borderLeftWidth: 4,
    borderLeftColor: '#1976d2',
  },
  userCardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  userCardFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  userAvatar: {
    marginRight: 16,
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  userEmail: {
    fontSize: 14,
    marginBottom: 8,
    color: '#666666',
  },
  userMetaInfo: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  joinDate: {
    fontSize: 12,
    color: '#757575',
    marginLeft: 4,
  },
  chipText: {
    fontSize: 12,
  },
  userMeta: {
    flexDirection: 'row',
    marginTop: 4,
  },
  roleChipSmall: {
    height: 24,
    marginRight: 8,
    backgroundColor: '#e3f2fd',
  },
  statusChipSmall: {
    height: 24,
  },
  chipContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  roleChipGrid: {
    height: 24,
    marginRight: 4,
    backgroundColor: '#e3f2fd',
  },
  // Grid view
  gridContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    padding: 8,
  },
  gridItem: {
    width: '48%',
    marginBottom: 16,
  },
  userCardGrid: {
    padding: 16,
    borderRadius: 12,
    elevation: 2,
    alignItems: 'center',
    height: 220, // Increased height to accommodate action buttons
  },
  userAvatarGrid: {
    marginBottom: 12,
  },
  userNameGrid: {
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 4,
  },
  userEmailGrid: {
    fontSize: 12,
    textAlign: 'center',
    marginBottom: 8,
  },
  statusChipGrid: {
    height: 24,
  },
  roleChipGrid: {
    height: 24,
    marginRight: 4,
  },
  chipContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
  },
  userCardActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: 8,
    width: '100%',
  },
  userCardActionsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: 4,
    width: '100%',
  },
  actionButton: {
    margin: 0,
  },
  actionButtonGrid: {
    margin: 0,
    padding: 4,
  },
  cardDivider: {
    width: '100%',
    marginVertical: 8,
  },
  // FAB
  fabGroup: {
    paddingBottom: 16, // Add padding to avoid overlap with navigation bar
    marginBottom: 16,
  },
  // Menus
  menu: {
    elevation: 4,
    borderRadius: 8,
    marginTop: 48,
  },
  // Snackbar
  snackbar: {
    backgroundColor: '#323232',
    borderRadius: 8,
  },
  // Modal
  modal: {
    margin: 20,
    borderRadius: 16,
    overflow: 'hidden',
    maxHeight: '80%',
  },
  modalHeader: {
    padding: 20,
    alignItems: 'center',
    position: 'relative',
  },
  closeButton: {
    position: 'absolute',
    top: 8,
    right: 8,
  },
  modalAvatar: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    marginBottom: 12,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 4,
    textAlign: 'center',
  },
  modalSubtitle: {
    fontSize: 14,
    textAlign: 'center',
  },
  modalContent: {
    padding: 20,
  },
  modalInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  modalLabel: {
    fontSize: 14,
    fontWeight: '500',
  },
  modalValue: {
    fontSize: 14,
  },
  modalDivider: {
    marginVertical: 12,
  },
  modalActions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'flex-end',
    padding: 16,
  },
  modalButton: {
    marginLeft: 8,
    marginBottom: 8,
  },
  activateButton: {
    borderColor: '#4caf50',
  },
  deactivateButton: {
    borderColor: '#f44336',
  },
  deleteButton: {
    borderColor: '#f44336',
  },
  roleChip: {
    backgroundColor: '#e3f2fd',
    borderColor: '#2196f3',
  },
  statusChip: {
    minWidth: 80,
  },
  adminButton: {
    backgroundColor: '#3949ab',
    marginLeft: 8,
  },
  // Section containers
  sectionContainer: {
    marginBottom: 16,
    borderRadius: 12,
    elevation: 4,
    overflow: 'hidden',
    marginHorizontal: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333333',
  },

  // Quick access section
  quickAccessScrollContent: {
    paddingHorizontal: 8,
    paddingBottom: 8,
  },
  quickAccessCards: {
    padding: 8,
  },
  quickAccessCard: {
    width: 150,
    height: 120,
    borderRadius: 8,
    padding: 16,
    marginHorizontal: 8,
    marginVertical: 8,
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 2,
  },
  quickAccessCardText: {
    marginTop: 8,
    fontWeight: 'bold',
    textAlign: 'center',
  },

  // Stats section
  statsScrollContent: {
    paddingHorizontal: 8,
    paddingBottom: 8,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },

  // Mobile-specific styles
  fabGroupMobile: {
    position: 'absolute',
    right: 0,
    bottom: 0,
    margin: 16,
    elevation: 8,
    zIndex: 999,
  },
  standaloneFab: {
    position: 'absolute',
    right: 16,
    bottom: 16,
    backgroundColor: '#1976d2',
    elevation: 8,
    zIndex: 998,
  },

  // SwipeListView styles
  rowFront: {
    width: '100%',
  },
  rowBack: {
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingLeft: 15,
  },
  backTextWhite: {
    color: '#FFF',
    fontSize: 12,
    fontWeight: 'bold',
    marginTop: 4,
  },
  backLeftBtn: {
    alignItems: 'center',
    bottom: 0,
    justifyContent: 'center',
    position: 'absolute',
    top: 0,
    width: 75,
  },
  backRightBtn: {
    alignItems: 'center',
    bottom: 0,
    justifyContent: 'center',
    position: 'absolute',
    top: 0,
    width: 75,
  },
  backLeftBtnLeft: {
    backgroundColor: '#1976d2',
    left: 0,
  },
  backRightBtnLeft: {
    backgroundColor: '#ff9800',
    right: 75,
  },
  backRightBtnRight: {
    backgroundColor: '#f44336',
    right: 0,
  },
});

export default UserManagement;
