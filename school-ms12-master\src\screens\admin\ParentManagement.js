import React, { useState, useEffect, useRef } from 'react';
import { View, ScrollView, StyleSheet, Image, TouchableOpacity, Animated, Alert } from 'react-native';
import {
  Card,
  Title,
  FAB,
  Portal,
  Modal,
  DataTable,
  Searchbar,
  List,
  Text,
  Button,
  Chip,
  Badge,
  Menu,
  Surface,
  useTheme,
  IconButton,
} from 'react-native-paper';
import * as Animatable from 'react-native-animatable';
import { db, storage, auth } from '../../config/firebase';
import { collection, addDoc, query, getDocs, doc, updateDoc, deleteDoc, where, getDoc, setDoc, serverTimestamp } from 'firebase/firestore';
import { createUserWithEmailAndPassword, sendEmailVerification, updateProfile } from 'firebase/auth';
import { ref, uploadBytes, getDownloadURL } from 'firebase/storage';
import * as ImagePicker from 'expo-image-picker';
import CustomButton from '../../components/common/CustomButton';
import CustomInput from '../../components/common/CustomInput';
import { useLanguage } from '../../context/LanguageContext';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import AdminAppHeader from '../../components/common/AdminAppHeader';
import AdminSidebar from '../../components/common/AdminSidebar';
import SidebarBackdrop from '../../components/common/SidebarBackdrop';
import VerificationStatus from '../../components/common/VerificationStatus';
import UserRegistrationService from '../../services/UserRegistrationService';
import EmailVerificationService from '../../services/EmailVerificationService';
import EmailNotificationService from '../../services/EmailNotificationService';
import EventService, { EVENTS } from '../../services/EventService';
import CloudinaryService from '../../services/CloudinaryService';

const ParentManagement = ({ route, navigation }) => {
  const { translate, language, setLanguage, getTextStyle, isRTL, supportedLanguages } = useLanguage();
  // No theme needed
  const fadeAnim = useRef(new Animated.Value(1)).current;
  const slideAnim = useRef(new Animated.Value(0)).current;
  const [languageMenuVisible, setLanguageMenuVisible] = useState(false);

  // Sidebar state
  const [drawerOpen, setDrawerOpen] = useState(false);
  const drawerAnim = useRef(new Animated.Value(-300)).current;
  const backdropFadeAnim = useRef(new Animated.Value(0)).current;
  const [activeSidebarItem, setActiveSidebarItem] = useState('ParentManagement');

  const [parents, setParents] = useState([]);
  const [students, setStudents] = useState([]);
  const [visible, setVisible] = useState(false);
  const [selectedParent, setSelectedParent] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [profileImage, setProfileImage] = useState(null);
  const [kebeleIdImage, setKebeleIdImage] = useState(null);
  const [showChildrenModal, setShowChildrenModal] = useState(false);
  const [fabOpen, setFabOpen] = useState(false);
  const [tooltipVisible, setTooltipVisible] = useState(false);
  const [tooltipPosition, setTooltipPosition] = useState({ x: 0, y: 0 });
  const [tooltipText, setTooltipText] = useState('');

  // Email verification states
  const [emailVerified, setEmailVerified] = useState(false);
  const [verificationSent, setVerificationSent] = useState(false);
  const [verificationLoading, setVerificationLoading] = useState(false);
  const [createdUser, setCreatedUser] = useState(null);

  // Get params from navigation if they exist
  const studentData = route?.params?.studentData ? {
    ...route.params.studentData,
    // Convert ISO string dates back to Date objects if needed
    dateOfBirth: route.params.studentData.dateOfBirth ? new Date(route.params.studentData.dateOfBirth) : new Date(),
    admissionDate: route.params.studentData.admissionDate ? new Date(route.params.studentData.admissionDate) : new Date(),
    createdAt: route.params.studentData.createdAt ? new Date(route.params.studentData.createdAt) : null,
    updatedAt: route.params.studentData.updatedAt ? new Date(route.params.studentData.updatedAt) : null,
  } : null;
  const parentEmail = route?.params?.parentEmail;
  const fromStudentAdmission = route?.params?.fromStudentAdmission;

  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: parentEmail || '',
    phone: '',
    alternatePhone: '',
    address: {
      street: '',
      city: '',
      state: '',
      country: '',
    },
    occupation: '',
    workAddress: '',
    workPhone: '',
    relationship: 'father',
    emergencyContact: false,
    children: [],
    preferredContactMethod: 'email',
    notificationPreferences: {
      attendance: true,
      grades: true,
      behavior: true,
      announcements: true,
      events: true,
    },
    status: 'active',
    password: '',
    role: 'parent',
    imageUrl: '',
    kebeleIdUrl: '',
  });

  useEffect(() => {
    fetchParents();
    fetchStudents();
    if (parentEmail) {
      setVisible(true);
    }

    // Hide the default header
    navigation.setOptions({
      headerShown: false,
    });
  }, [parentEmail, navigation]);

  const toggleDrawer = () => {
    if (drawerOpen) {
      // Close drawer
      Animated.parallel([
        Animated.timing(drawerAnim, {
          toValue: -300,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(backdropFadeAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        })
      ]).start(() => setDrawerOpen(false));
    } else {
      // Open drawer
      setDrawerOpen(true);
      Animated.parallel([
        Animated.timing(drawerAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(backdropFadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        })
      ]).start();
    }
  };

  // Language transition effect
  useEffect(() => {
    // Fade out
    Animated.timing(fadeAnim, {
      toValue: 0.3,
      duration: 200,
      useNativeDriver: true,
    }).start(() => {
      // Slide out
      Animated.timing(slideAnim, {
        toValue: isRTL ? 50 : -50,
        duration: 0,
        useNativeDriver: true,
      }).start(() => {
        // Slide in
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }).start();
        // Fade in
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 200,
          useNativeDriver: true,
        }).start();
      });
    });
  }, [language]);

  const handleLanguageChange = async (langCode) => {
    try {
      await setLanguage(langCode);
      setLanguageMenuVisible(false);
    } catch (error) {
      console.error('Error changing language:', error);
    }
  };

  const showTooltip = (text, event) => {
    const { pageX, pageY } = event.nativeEvent;
    setTooltipText(text);
    setTooltipPosition({ x: pageX, y: pageY - 60 });
    setTooltipVisible(true);

    // Auto-hide tooltip after 3 seconds
    setTimeout(() => {
      setTooltipVisible(false);
    }, 3000);
  };

  const getLanguageFlag = (langCode) => {
    switch(langCode) {
      case 'en': return '🇺🇸';
      case 'am': return '🇪🇹';
      case 'or': return '🇪🇹';
      default: return '🌐';
    }
  };

  const pickImage = async (type) => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 1,
      });

      if (!result.canceled) {
        const selectedImage = result.assets[0];
        if (type === 'profile') {
          setProfileImage(selectedImage.uri);
        } else if (type === 'kebeleId') {
          setKebeleIdImage(selectedImage.uri);
        }
      }
    } catch (error) {
      console.error('Error picking image:', error);
      setError('Failed to pick image');
    }
  };

  const uploadImage = async (uri, path) => {
    if (!uri) return null;

    try {
      // Upload image to Cloudinary
      const result = await CloudinaryService.uploadFromUri(uri, {
        folder: 'parents',
        tags: ['profile', 'parent'],
        publicId: `parent_${formData.email.replace('@', '_')}`
      });

      return result.url;
    } catch (error) {
      console.error('Error uploading image:', error);
      throw new Error('Failed to upload image');
    }
  };

  const handleSubmit = async () => {
    try {
      setLoading(true);
      setError(null);

      if (!formData.firstName || !formData.lastName || !formData.email || !formData.password) {
        setError('Please fill in all required fields');
        return;
      }

      // Upload images if selected
      let imageUrl = null;
      let kebeleIdUrl = null;

      if (profileImage) {
        imageUrl = await uploadImage(
          profileImage,
          'parents/profile_images'
        );
      }

      if (kebeleIdImage) {
        kebeleIdUrl = await uploadImage(
          kebeleIdImage,
          'parents/documents'
        );
      }

      // Create user in Firebase Auth
      const userCredential = await createUserWithEmailAndPassword(
        auth,
        formData.email,
        formData.password
      );

      // Store the created user for verification
      setCreatedUser(userCredential.user);

      // Update user profile
      await updateProfile(userCredential.user, {
        displayName: `${formData.firstName} ${formData.lastName}`
      });

      // Send verification email
      try {
        await sendEmailVerification(userCredential.user);

        // Log verification email sent
        await addDoc(collection(db, 'verification_logs'), {
          userId: userCredential.user.uid,
          email: userCredential.user.email,
          type: 'parent_registration',
          timestamp: serverTimestamp(),
          status: 'sent'
        });

        // Try to send custom verification email as well
        try {
          // Generate verification token
          const { token } = await EmailVerificationService.generateVerificationToken(formData.email, 'parent');

          // Send custom verification email
          await EmailNotificationService.sendVerificationEmail(formData.email, token, 'parent');
        } catch (customEmailError) {
          console.error('Error sending custom verification email:', customEmailError);
          // Continue even if custom email fails, as Firebase will send its own
        }

        setVerificationSent(true);
      } catch (verificationError) {
        console.error('Error sending verification email:', verificationError);
        // Continue even if verification email fails
      }

      const parentDoc = {
        ...formData,
        uid: userCredential.user.uid,
        imageUrl: imageUrl || formData.imageUrl,
        kebeleIdUrl: kebeleIdUrl || formData.kebeleIdUrl,
        emailVerified: false,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      };

      delete parentDoc.password;

      // Use the UID as the document ID
      await setDoc(doc(db, 'users', userCredential.user.uid), parentDoc);
      const newParent = { id: userCredential.user.uid, ...parentDoc };

      // Don't close the modal yet, show verification status
      if (studentData && fromStudentAdmission && emailVerified) {
        // Emit event instead of calling callback directly
        EventService.emit(EVENTS.PARENT_ADDED, newParent);
        navigation.goBack();
      } else if (emailVerified) {
        setParents(prev => [...prev, newParent]);
        setVisible(false);
        resetForm();
      }

      // Show success message
      setError(null);

      // Fetch parents to update the list
      fetchParents();
    } catch (error) {
      console.error('Error adding parent:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleVerificationSent = () => {
    setVerificationSent(true);
  };

  const handleVerificationStatusChange = (status) => {
    setEmailVerified(status);

    // Update the user document if verified
    if (status && createdUser) {
      const userRef = doc(db, 'users', createdUser.uid);
      updateDoc(userRef, {
        emailVerified: true,
        updatedAt: serverTimestamp()
      }).catch(error => {
        console.error('Error updating email verification status:', error);
      });
    }
  };

  const handleFinish = () => {
    if (studentData && fromStudentAdmission && createdUser) {
      const newParent = {
        id: createdUser.uid,
        ...formData,
        emailVerified: true,
        uid: createdUser.uid
      };

      // Emit event instead of calling callback directly
      EventService.emit(EVENTS.PARENT_ADDED, newParent);

      navigation.goBack();
    } else {
      setVisible(false);
      resetForm();
      fetchParents();
    }
  };

  const resetForm = () => {
    setFormData({
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      alternatePhone: '',
      address: {
        street: '',
        city: '',
        state: '',
        country: '',
      },
      occupation: '',
      workAddress: '',
      workPhone: '',
      relationship: 'father',
      emergencyContact: false,
      children: [],
      preferredContactMethod: 'email',
      notificationPreferences: {
        attendance: true,
        grades: true,
        behavior: true,
        announcements: true,
        events: true,
      },
      status: 'active',
      password: '',
      role: 'parent',
      imageUrl: '',
      kebeleIdUrl: '',
    });
    setProfileImage(null);
    setKebeleIdImage(null);
  };

  const fetchParents = async () => {
    try {
      setLoading(true);
      const parentsRef = collection(db, 'users');
      const q = query(parentsRef, where('role', '==', 'parent'));
      const querySnapshot = await getDocs(q);

      const parentsData = [];
      const fetchPromises = [];

      querySnapshot.forEach((docSnapshot) => {
        const parentData = { id: docSnapshot.id, ...docSnapshot.data() };
        parentsData.push(parentData);

        // Prepare to fetch children details for each parent
        if (parentData.children && parentData.children.length > 0) {
          fetchPromises.push(
            Promise.all(
              parentData.children.map(async (childId) => {
                try {
                  // Make sure childId is a string
                  const childIdStr = String(childId);
                  const childDocRef = doc(db, 'users', childIdStr);
                  const childDoc = await getDoc(childDocRef);
                  if (childDoc.exists()) {
                    return { id: childIdStr, ...childDoc.data() };
                  }
                  return null;
                } catch (err) {
                  console.error(`Error fetching child ${childId}:`, err);
                  return null;
                }
              })
            )
          );
        } else {
          fetchPromises.push(Promise.resolve([]));
        }
      });

      // Wait for all child data to be fetched
      const childrenData = await Promise.all(fetchPromises);

      // Attach full children data to parents
      parentsData.forEach((parent, index) => {
        parent.childrenData = childrenData[index].filter(child => child !== null);
      });

      setParents(parentsData);
    } catch (error) {
      console.error('Error fetching parents:', error);
      setError(translate('parent.management.fetchError'));
    } finally {
      setLoading(false);
    }
  };

  const fetchStudents = async () => {
    try {
      const studentsRef = collection(db, 'users');
      const q = query(studentsRef, where('role', '==', 'student'));
      const querySnapshot = await getDocs(q);

      const studentsData = [];
      querySnapshot.forEach((doc) => {
        studentsData.push({ id: doc.id, ...doc.data() });
      });

      setStudents(studentsData);
    } catch (error) {
      console.error('Error fetching students:', error);
    }
  };

  const handleUpdateParent = async () => {
    try {
      setLoading(true);
      const parentRef = doc(db, 'users', selectedParent.id);
      await updateDoc(parentRef, {
        firstName: formData.firstName,
        lastName: formData.lastName,
        phone: formData.phone,
        alternatePhone: formData.alternatePhone,
        address: formData.address,
        occupation: formData.occupation,
        workAddress: formData.workAddress,
        workPhone: formData.workPhone,
        relationship: formData.relationship,
        emergencyContact: formData.emergencyContact,
        children: formData.children,
        preferredContactMethod: formData.preferredContactMethod,
        notificationPreferences: formData.notificationPreferences,
        status: formData.status,
        updatedAt: new Date().toISOString(),
      });

      setVisible(false);
      resetForm();
      fetchParents();
    } catch (error) {
      console.error('Error updating parent:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteParent = async (parentId) => {
    try {
      setLoading(true);
      setError(null);

      // Delete user document
      await deleteDoc(doc(db, 'users', parentId));

      // Show success message
      alert(translate('parent.management.deleteSuccess'));

      // Refresh the list
      fetchParents();
      setVisible(false);
      resetForm();
    } catch (error) {
      console.error('Error deleting parent:', error);
      setError(translate('parent.management.deleteError'));
    } finally {
      setLoading(false);
    }
  };

  const toggleChild = (studentId) => {
    const children = formData.children.includes(studentId)
      ? formData.children.filter(id => id !== studentId)
      : [...formData.children, studentId];
    setFormData({ ...formData, children });
  };

  const toggleNotificationPreference = (preference) => {
    setFormData({
      ...formData,
      notificationPreferences: {
        ...formData.notificationPreferences,
        [preference]: !formData.notificationPreferences[preference],
      },
    });
  };

  const filteredParents = parents.filter(parent =>
    `${parent.firstName} ${parent.lastName}`.toLowerCase().includes(searchQuery.toLowerCase()) ||
    parent.email.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <View style={styles.mainContainer}>
      {/* Sidebar */}
      <AdminSidebar
        drawerAnim={drawerAnim}
        activeSidebarItem={activeSidebarItem}
        setActiveSidebarItem={setActiveSidebarItem}
        toggleDrawer={toggleDrawer}
      />

      {/* Backdrop */}
      <SidebarBackdrop
        visible={drawerOpen}
        onPress={toggleDrawer}
        fadeAnim={backdropFadeAnim}
      />

      {/* Admin App Header */}
      <AdminAppHeader
        title={translate('parent.management.title')}
        onMenuPress={toggleDrawer}
      />

      <Animated.View
        style={[
          styles.container,
          { opacity: fadeAnim, transform: [{ translateX: slideAnim }] },
          isRTL && styles.rtlContainer
        ]}
      >
        <View style={styles.header}>
          <LinearGradient
            colors={['#1976d2', '#005cb2']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
            style={styles.headerGradient}
          >
            <View style={styles.headerContent}>
              <View style={styles.titleContainer}>
                <MaterialCommunityIcons name="account-child" size={24} color="white" />
              </View>

            <View style={styles.headerActions}>
              <Menu
                visible={languageMenuVisible}
                onDismiss={() => setLanguageMenuVisible(false)}
                anchor={
                  <TouchableOpacity
                    style={styles.languageButton}
                    onPress={() => setLanguageMenuVisible(true)}
                  >
                    <Text style={styles.languageButtonText}>
                      {getLanguageFlag(language)} {language.toUpperCase()}
                    </Text>
                    <MaterialCommunityIcons name="chevron-down" size={16} color="white" />
                  </TouchableOpacity>
                }
              >
                {Object.keys(supportedLanguages).map((langCode) => (
                  <Menu.Item
                    key={langCode}
                    title={`${getLanguageFlag(langCode)} ${supportedLanguages[langCode]}`}
                    onPress={() => handleLanguageChange(langCode)}
                    style={langCode === language ? styles.selectedLanguageItem : null}
                    titleStyle={langCode === language ? styles.selectedLanguageText : null}
                    leadingIcon={langCode === language ? "check" : undefined}
                  />
                ))}
              </Menu>

              <Badge style={styles.parentCountBadge}>{parents.length}</Badge>
            </View>
          </View>
        </LinearGradient>
      </View>

      <Searchbar
        placeholder={translate('parent.management.searchParents')}
        onChangeText={setSearchQuery}
        value={searchQuery}
        style={[styles.searchBar, isRTL && styles.rtlSearchBar]}
        iconColor={'#1976d2'}
        inputStyle={getTextStyle({ textAlign: isRTL ? 'right' : 'left' })}
      />

      {error && (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
        </View>
      )}

      {loading && (
        <View style={styles.loadingContainer}>
          <Text style={getTextStyle()}>{translate('common.loading')}</Text>
        </View>
      )}

      <ScrollView style={[styles.content, isRTL && styles.rtlContent]}>
        <Surface style={styles.tableContainer}>
          <DataTable>
            <DataTable.Header style={styles.tableHeader}>
              <DataTable.Title
                style={[styles.tableCell, isRTL && styles.rtlTableCell]}
                textStyle={getTextStyle(styles.tableHeaderText)}
              >
                {translate('parent.management.firstName')}
              </DataTable.Title>
              <DataTable.Title
                style={[styles.tableCell, isRTL && styles.rtlTableCell]}
                textStyle={getTextStyle(styles.tableHeaderText)}
              >
                {translate('parent.management.email')}
              </DataTable.Title>
              <DataTable.Title
                style={[styles.tableCell, isRTL && styles.rtlTableCell]}
                textStyle={getTextStyle(styles.tableHeaderText)}
              >
                {translate('parent.management.phone')}
              </DataTable.Title>
              <DataTable.Title
                style={[styles.tableCell, isRTL && styles.rtlTableCell]}
                textStyle={getTextStyle(styles.tableHeaderText)}
              >
                {translate('parent.management.children')}
              </DataTable.Title>
            </DataTable.Header>

          {filteredParents.map((parent) => (
            <DataTable.Row
              key={parent.id}
              style={[styles.tableRow, isRTL && styles.rtlTableRow]}
              onPress={() => {
                setSelectedParent(parent);
                setFormData({
                  firstName: parent.firstName,
                  lastName: parent.lastName,
                  email: parent.email,
                  phone: parent.phone,
                  alternatePhone: parent.alternatePhone,
                  address: parent.address || {
                    street: '',
                    city: '',
                    state: '',
                    country: '',
                  },
                  occupation: parent.occupation,
                  workAddress: parent.workAddress,
                  workPhone: parent.workPhone,
                  relationship: parent.relationship || 'father',
                  emergencyContact: parent.emergencyContact || false,
                  children: parent.children || [],
                  preferredContactMethod: parent.preferredContactMethod || 'email',
                  notificationPreferences: parent.notificationPreferences || {
                    attendance: true,
                    grades: true,
                    behavior: true,
                    announcements: true,
                    events: true,
                  },
                  status: parent.status || 'active',
                });
                setVisible(true);
              }}
            >
              <DataTable.Cell
                style={[styles.tableCell, isRTL && styles.rtlTableCell]}
                textStyle={getTextStyle(styles.tableCellText)}
              >
                <Text style={getTextStyle(styles.parentName)}>
                  {`${parent.firstName} ${parent.lastName}`}
                </Text>
              </DataTable.Cell>
              <DataTable.Cell
                style={[styles.tableCell, isRTL && styles.rtlTableCell]}
                textStyle={getTextStyle(styles.tableCellText)}
              >
                {parent.email}
              </DataTable.Cell>
              <DataTable.Cell
                style={[styles.tableCell, isRTL && styles.rtlTableCell]}
                textStyle={getTextStyle(styles.tableCellText)}
              >
                {parent.phone}
              </DataTable.Cell>
              <DataTable.Cell
                style={[styles.tableCell, isRTL && styles.rtlTableCell]}
              >
                <Chip
                  mode="outlined"
                  style={[styles.childrenChip, { backgroundColor: parent.children?.length > 0 ? '#e3f2fd' : '#f5f5f5' }]}
                  textStyle={getTextStyle(styles.chipText)}
                  icon="account-child"
                  onPress={(e) => {
                    e.stopPropagation();
                    setSelectedParent(parent);
                    setShowChildrenModal(true);
                  }}
                  onLongPress={(e) => showTooltip(translate('parent.management.assignChildren'), e)}
                >
                  {parent.children?.length || 0} {translate('parent.management.childrenCount')}
                  {parent.childrenData && parent.childrenData.length > 0 && (
                    <Text style={styles.childNamePreview}>
                      {parent.childrenData.map(child => `${child.firstName}`).join(', ')}
                    </Text>
                  )}
                </Chip>
              </DataTable.Cell>
            </DataTable.Row>
          ))}
        </DataTable>
        </Surface>
      </ScrollView>

      <Portal>
        {/* Parent Edit/Add Modal */}
        <Modal
          visible={visible}
          onDismiss={() => {
            setVisible(false);
            resetForm();
          }}
          contentContainerStyle={[styles.modalContent, isRTL && styles.rtlModalContent]}
        >
          <LinearGradient
            colors={['#1976d2', '#005cb2']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
            style={styles.modalHeader}
          >
            <View style={styles.modalHeaderContent}>
              <MaterialCommunityIcons
                name={selectedParent ? "account-edit" : "account-plus"}
                size={24}
                color="white"
              />
              <Title style={styles.modalHeaderTitle}>
                {selectedParent ? translate('parent.management.editParent') : translate('parent.management.addParent')}
              </Title>
              <IconButton
                icon="close"
                color="white"
                size={20}
                onPress={() => {
                  setVisible(false);
                  resetForm();
                }}
              />
            </View>
          </LinearGradient>

          <ScrollView style={[styles.formScrollView, isRTL && styles.rtlFormScrollView]}>
            <Card>
              <Card.Content>
                <View style={[styles.imageSection, isRTL && styles.rtlImageSection]}>
                  <View style={styles.imageContainer}>
                    <Title style={[styles.imageTitle, getTextStyle()]}>{translate('parent.management.profileImage')}</Title>
                    {profileImage ? (
                      <Image source={{ uri: profileImage }} style={styles.image} />
                    ) : formData.imageUrl ? (
                      <Image source={{ uri: formData.imageUrl }} style={styles.image} />
                    ) : (
                      <View style={styles.placeholderImage}>
                        <MaterialCommunityIcons name="account" size={50} color="#bdbdbd" />
                      </View>
                    )}
                    <Button
                      mode="contained"
                      onPress={() => pickImage('profile')}
                      style={styles.imageButton}
                      labelStyle={getTextStyle()}
                      icon="camera"
                    >
                      {formData.imageUrl || profileImage ? translate('parent.management.change') : translate('parent.management.upload')}
                    </Button>
                  </View>
                </View>

                <View style={styles.formSection}>
                  <CustomInput
                    label={translate('parent.management.firstName')}
                    value={formData.firstName}
                    onChangeText={(text) => setFormData({ ...formData, firstName: text })}
                    style={[styles.formInput, isRTL && styles.rtlFormInput]}
                    textInputStyle={getTextStyle({ textAlign: isRTL ? 'right' : 'left' })}
                    left={<MaterialCommunityIcons name="account" size={24} color={'#1976d2'} />}
                  />
                  <CustomInput
                    label={translate('parent.management.lastName')}
                    value={formData.lastName}
                    onChangeText={(text) => setFormData({ ...formData, lastName: text })}
                    style={[styles.formInput, isRTL && styles.rtlFormInput]}
                    textInputStyle={getTextStyle({ textAlign: isRTL ? 'right' : 'left' })}
                    left={<MaterialCommunityIcons name="account-outline" size={24} color={'#1976d2'} />}
                  />
                  <View style={styles.emailVerificationContainer}>
                    <CustomInput
                      label={translate('parent.management.email')}
                      value={formData.email}
                      onChangeText={(text) => {
                        setFormData({ ...formData, email: text });
                        setEmailVerified(false);
                        setVerificationSent(false);
                      }}
                      keyboardType="email-address"
                      style={[styles.formInput, isRTL && styles.rtlFormInput]}
                      textInputStyle={getTextStyle({ textAlign: isRTL ? 'right' : 'left' })}
                      left={<MaterialCommunityIcons name="email" size={24} color={'#1976d2'} />}
                      disabled={selectedParent || emailVerified}
                    />
                  </View>

                  {!selectedParent && createdUser && (
                    <Surface style={styles.verificationContainer}>
                      <VerificationStatus
                        email={formData.email}
                        isVerified={emailVerified}
                        verificationSent={verificationSent}
                        onVerificationSent={handleVerificationSent}
                        onVerificationStatusChange={handleVerificationStatusChange}
                        userType="parent"
                        user={createdUser}
                      />

                      {verificationSent && !emailVerified && (
                        <Text style={styles.verificationMessage}>
                          {translate('verification.checkEmail') || 'Please check your email and verify your account before continuing.'}
                        </Text>
                      )}

                      {emailVerified && (
                        <Text style={styles.verificationSuccess}>
                          {translate('verification.success') || 'Email verified successfully!'}
                        </Text>
                      )}
                    </Surface>
                  )}

                  {!selectedParent && (
                    <CustomInput
                      label={translate('parent.management.password')}
                      value={formData.password}
                      onChangeText={(text) => setFormData({ ...formData, password: text })}
                      secureTextEntry
                      style={[styles.formInput, isRTL && styles.rtlFormInput]}
                      textInputStyle={getTextStyle({ textAlign: isRTL ? 'right' : 'left' })}
                      left={<MaterialCommunityIcons name="lock" size={24} color={'#1976d2'} />}
                    />
                  )}

                  <CustomInput
                    label={translate('parent.management.phone')}
                    value={formData.phone}
                    onChangeText={(text) => setFormData({ ...formData, phone: text })}
                    keyboardType="phone-pad"
                    style={[styles.formInput, isRTL && styles.rtlFormInput]}
                    textInputStyle={getTextStyle({ textAlign: isRTL ? 'right' : 'left' })}
                    left={<MaterialCommunityIcons name="phone" size={24} color={'#1976d2'} />}
                  />
                </View>

                <View style={styles.sectionContainer}>
                  <View style={styles.sectionHeader}>
                    <MaterialCommunityIcons name="map-marker" size={24} color={'#1976d2'} />
                    <Title style={[styles.sectionTitle, getTextStyle()]}>{translate('parent.management.address')}</Title>
                  </View>

                  <View style={styles.formSection}>
                    <CustomInput
                      label={translate('address.street')}
                      value={formData.address.street}
                      onChangeText={(text) => setFormData({
                        ...formData,
                        address: { ...formData.address, street: text },
                      })}
                      style={[styles.formInput, isRTL && styles.rtlFormInput]}
                      textInputStyle={getTextStyle({ textAlign: isRTL ? 'right' : 'left' })}
                      left={<MaterialCommunityIcons name="road-variant" size={24} color={'#1976d2'} />}
                    />

                    <CustomInput
                      label={translate('address.city')}
                      value={formData.address.city}
                      onChangeText={(text) => setFormData({
                        ...formData,
                        address: { ...formData.address, city: text },
                      })}
                      style={[styles.formInput, isRTL && styles.rtlFormInput]}
                      textInputStyle={getTextStyle({ textAlign: isRTL ? 'right' : 'left' })}
                      left={<MaterialCommunityIcons name="city" size={24} color={'#1976d2'} />}
                    />

                    <CustomInput
                      label={translate('address.state')}
                      value={formData.address.state}
                      onChangeText={(text) => setFormData({
                        ...formData,
                        address: { ...formData.address, state: text },
                      })}
                      style={[styles.formInput, isRTL && styles.rtlFormInput]}
                      textInputStyle={getTextStyle({ textAlign: isRTL ? 'right' : 'left' })}
                      left={<MaterialCommunityIcons name="map" size={24} color={'#1976d2'} />}
                    />

                    <CustomInput
                      label={translate('address.country')}
                      value={formData.address.country}
                      onChangeText={(text) => setFormData({
                        ...formData,
                        address: { ...formData.address, country: text },
                      })}
                      style={[styles.formInput, isRTL && styles.rtlFormInput]}
                      textInputStyle={getTextStyle({ textAlign: isRTL ? 'right' : 'left' })}
                      left={<MaterialCommunityIcons name="earth" size={24} color={'#1976d2'} />}
                    />
                  </View>
                </View>

                <View style={styles.sectionContainer}>
                  <View style={styles.sectionHeader}>
                    <MaterialCommunityIcons name="briefcase" size={24} color={'#1976d2'} />
                    <Title style={[styles.sectionTitle, getTextStyle()]}>{translate('parent.management.workInfo')}</Title>
                  </View>

                  <View style={styles.formSection}>
                    <CustomInput
                      label={translate('parent.management.occupation')}
                      value={formData.occupation}
                      onChangeText={(text) => setFormData({ ...formData, occupation: text })}
                      style={[styles.formInput, isRTL && styles.rtlFormInput]}
                      textInputStyle={getTextStyle({ textAlign: isRTL ? 'right' : 'left' })}
                      left={<MaterialCommunityIcons name="account-tie" size={24} color={'#1976d2'} />}
                    />

                    <CustomInput
                      label={translate('parent.management.workAddress')}
                      value={formData.workAddress}
                      onChangeText={(text) => setFormData({ ...formData, workAddress: text })}
                      multiline
                      numberOfLines={2}
                      style={[styles.formInput, isRTL && styles.rtlFormInput]}
                      textInputStyle={getTextStyle({ textAlign: isRTL ? 'right' : 'left' })}
                      left={<MaterialCommunityIcons name="office-building" size={24} color={'#1976d2'} />}
                    />

                    <CustomInput
                      label={translate('parent.management.workPhone')}
                      value={formData.workPhone}
                      onChangeText={(text) => setFormData({ ...formData, workPhone: text })}
                      keyboardType="phone-pad"
                      style={[styles.formInput, isRTL && styles.rtlFormInput]}
                      textInputStyle={getTextStyle({ textAlign: isRTL ? 'right' : 'left' })}
                      left={<MaterialCommunityIcons name="phone-in-talk" size={24} color={'#1976d2'} />}
                    />
                  </View>
                </View>

                <View style={styles.sectionContainer}>
                  <View style={styles.sectionHeader}>
                    <MaterialCommunityIcons name="account-child" size={24} color={'#1976d2'} />
                    <Title style={[styles.sectionTitle, getTextStyle()]}>{translate('parent.management.relationship')}</Title>
                  </View>

                  <List.Section style={[styles.listSection, isRTL && styles.rtlListSection]}>
                    {[
                      { value: 'father', icon: 'human-male', label: translate('parent.management.father') },
                      { value: 'mother', icon: 'human-female', label: translate('parent.management.mother') },
                      { value: 'guardian', icon: 'account-supervisor', label: translate('parent.management.guardian') }
                    ].map((item) => (
                      <List.Item
                        key={item.value}
                        title={item.label}
                        titleStyle={[getTextStyle(), formData.relationship === item.value && styles.selectedItemText]}
                        onPress={() => setFormData({ ...formData, relationship: item.value })}
                        style={[styles.listItem, formData.relationship === item.value ? styles.selectedItem : null, isRTL && styles.rtlListItem]}
                        left={props => <List.Icon {...props} icon={item.icon} color={formData.relationship === item.value ? '#1976d2' : '#757575'} />}
                        right={props => formData.relationship === item.value ? <List.Icon {...props} icon="check" color={'#1976d2'} /> : null}
                      />
                    ))}
                  </List.Section>

                  <List.Item
                    title={translate('parent.management.emergencyContact')}
                    titleStyle={getTextStyle()}
                    onPress={() => setFormData({ ...formData, emergencyContact: !formData.emergencyContact })}
                    style={[styles.listItem, isRTL && styles.rtlListItem]}
                    left={props => (
                      <List.Icon
                        {...props}
                        icon={formData.emergencyContact ? 'checkbox-marked' : 'checkbox-blank-outline'}
                        color={formData.emergencyContact ? '#1976d2' : '#757575'}
                      />
                    )}
                  />
                </View>

                <View style={styles.sectionContainer}>
                  <View style={styles.sectionHeader}>
                    <MaterialCommunityIcons name="account-group" size={24} color={'#1976d2'} />
                    <Title style={[styles.sectionTitle, getTextStyle()]}>{translate('parent.management.children')}</Title>
                  </View>

                  <ScrollView horizontal style={[styles.chipScrollView, isRTL && styles.rtlChipScrollView]}>
                    <View style={styles.chipContainer}>
                      {students.map((student) => (
                        <Chip
                          key={student.id}
                          selected={formData.children.includes(student.id)}
                          onPress={() => toggleChild(student.id)}
                          style={[styles.chip, formData.children.includes(student.id) && styles.selectedChip]}
                          mode="outlined"
                          icon={formData.children.includes(student.id) ? "check" : "school"}
                          textStyle={getTextStyle(styles.chipText)}
                        >
                          {`${student.firstName} ${student.lastName}`}
                        </Chip>
                      ))}
                    </View>
                  </ScrollView>
                </View>

                <View style={styles.sectionContainer}>
                  <View style={styles.sectionHeader}>
                    <MaterialCommunityIcons name="message-settings" size={24} color={'#1976d2'} />
                    <Title style={[styles.sectionTitle, getTextStyle()]}>{translate('parent.management.contactPreferences')}</Title>
                  </View>

                  <List.Section style={[styles.listSection, isRTL && styles.rtlListSection]}>
                    <Title style={[styles.subSectionTitle, getTextStyle()]}>{translate('parent.management.preferredContactMethod')}</Title>
                    {[
                      { value: 'email', icon: 'email', label: translate('contact.email') },
                      { value: 'phone', icon: 'phone', label: translate('contact.phone') },
                      { value: 'sms', icon: 'message', label: translate('contact.sms') }
                    ].map((item) => (
                      <List.Item
                        key={item.value}
                        title={item.label}
                        titleStyle={[getTextStyle(), formData.preferredContactMethod === item.value && styles.selectedItemText]}
                        onPress={() => setFormData({ ...formData, preferredContactMethod: item.value })}
                        style={[styles.listItem, formData.preferredContactMethod === item.value ? styles.selectedItem : null, isRTL && styles.rtlListItem]}
                        left={props => <List.Icon {...props} icon={item.icon} color={formData.preferredContactMethod === item.value ? '#1976d2' : '#757575'} />}
                        right={props => formData.preferredContactMethod === item.value ? <List.Icon {...props} icon="check" color={'#1976d2'} /> : null}
                      />
                    ))}
                  </List.Section>

                  <List.Section style={[styles.listSection, isRTL && styles.rtlListSection]}>
                    <Title style={[styles.subSectionTitle, getTextStyle()]}>{translate('parent.management.notificationPreferences')}</Title>
                    {Object.entries(formData.notificationPreferences).map(([key, value]) => (
                      <List.Item
                        key={key}
                        title={translate(`notifications.${key}`)}
                        titleStyle={getTextStyle()}
                        onPress={() => toggleNotificationPreference(key)}
                        style={[styles.listItem, isRTL && styles.rtlListItem]}
                        left={props => (
                          <List.Icon
                            {...props}
                            icon={value ? 'checkbox-marked' : 'checkbox-blank-outline'}
                            color={value ? '#1976d2' : '#757575'}
                          />
                        )}
                      />
                    ))}
                  </List.Section>
                </View>

                <View style={styles.sectionContainer}>
                  <View style={styles.sectionHeader}>
                    <MaterialCommunityIcons name="account-check" size={24} color={'#1976d2'} />
                    <Title style={[styles.sectionTitle, getTextStyle()]}>{translate('parent.management.status')}</Title>
                  </View>

                  <List.Section style={[styles.listSection, isRTL && styles.rtlListSection]}>
                    {[
                      { value: 'active', icon: 'check-circle', label: translate('status.active') },
                      { value: 'inactive', icon: 'close-circle', label: translate('status.inactive') }
                    ].map((item) => (
                      <List.Item
                        key={item.value}
                        title={item.label}
                        titleStyle={[getTextStyle(), formData.status === item.value && styles.selectedItemText]}
                        onPress={() => setFormData({ ...formData, status: item.value })}
                        style={[styles.listItem, formData.status === item.value ? styles.selectedItem : null, isRTL && styles.rtlListItem]}
                        left={props => <List.Icon {...props} icon={item.icon} color={formData.status === item.value ? '#1976d2' : '#757575'} />}
                        right={props => formData.status === item.value ? <List.Icon {...props} icon="check" color={'#1976d2'} /> : null}
                      />
                    ))}
                  </List.Section>
                </View>

                <View style={[styles.sectionContainer, styles.kebeleContainer]}>
                  <View style={styles.sectionHeader}>
                    <MaterialCommunityIcons name="card-account-details" size={24} color={'#1976d2'} />
                    <Title style={[styles.sectionTitle, getTextStyle()]}>{translate('parent.management.kebeleId')}</Title>
                  </View>

                  <View style={styles.imageContainer}>
                    {kebeleIdImage ? (
                      <Image source={{ uri: kebeleIdImage }} style={styles.kebeleImage} />
                    ) : formData.kebeleIdUrl ? (
                      <Image source={{ uri: formData.kebeleIdUrl }} style={styles.kebeleImage} />
                    ) : (
                      <View style={styles.placeholderKebeleImage}>
                        <MaterialCommunityIcons name="card-account-details-outline" size={50} color="#bdbdbd" />
                      </View>
                    )}
                    <Button
                      mode="contained"
                      onPress={() => pickImage('kebeleId')}
                      style={styles.imageButton}
                      labelStyle={getTextStyle()}
                      icon="camera"
                    >
                      {formData.kebeleIdUrl || kebeleIdImage ? translate('parent.management.change') : translate('parent.management.upload')}
                    </Button>
                  </View>
                </View>

                <View style={[styles.modalButtons, isRTL && styles.rtlModalButtons]}>
                  {selectedParent ? (
                    <CustomButton
                      mode="contained"
                      onPress={handleUpdateParent}
                      loading={loading}
                      labelStyle={getTextStyle(styles.buttonText)}
                      style={styles.saveButton}
                      icon="content-save"
                    >
                      {translate('actions.update')}
                    </CustomButton>
                  ) : createdUser && emailVerified ? (
                    <CustomButton
                      mode="contained"
                      onPress={handleFinish}
                      labelStyle={getTextStyle(styles.buttonText)}
                      style={styles.saveButton}
                      icon="check-circle"
                    >
                      {translate('common.finish') || 'Finish'}
                    </CustomButton>
                  ) : (
                    <CustomButton
                      mode="contained"
                      onPress={handleSubmit}
                      loading={loading}
                      labelStyle={getTextStyle(styles.buttonText)}
                      style={styles.saveButton}
                      icon="plus"
                      disabled={createdUser && !emailVerified}
                    >
                      {translate('actions.add')}
                    </CustomButton>
                  )}

                  {selectedParent && (
                    <CustomButton
                      mode="outlined"
                      onPress={() => {
                        if (confirm(translate('parent.management.confirmDelete'))) {
                          handleDeleteParent(selectedParent.id);
                        }
                      }}
                      style={styles.deleteButton}
                      labelStyle={getTextStyle(styles.deleteButtonText)}
                      icon="delete"
                    >
                      {translate('actions.delete')}
                    </CustomButton>
                  )}

                  <CustomButton
                    mode="outlined"
                    onPress={() => {
                      setVisible(false);
                      resetForm();
                      setCreatedUser(null);
                      setEmailVerified(false);
                      setVerificationSent(false);
                    }}
                    labelStyle={getTextStyle(styles.buttonText)}
                    style={styles.cancelButton}
                    icon="close"
                  >
                    {translate('actions.cancel')}
                  </CustomButton>
                </View>
              </Card.Content>
            </Card>
          </ScrollView>
        </Modal>
      </Portal>

      {/* Children Modal */}
      <Portal>
        <Modal
          visible={showChildrenModal}
          onDismiss={() => setShowChildrenModal(false)}
          contentContainerStyle={[styles.modalContent, isRTL && styles.rtlModalContent]}
        >
          <LinearGradient
            colors={['#1976d2', '#005cb2']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
            style={styles.modalHeader}
          >
            <View style={styles.modalHeaderContent}>
              <MaterialCommunityIcons
                name="account-child"
                size={24}
                color="white"
              />
              <Title style={styles.modalHeaderTitle}>
                {translate('parent.management.childrenDetails')}
              </Title>
              <IconButton
                icon="close"
                color="white"
                size={20}
                onPress={() => setShowChildrenModal(false)}
              />
            </View>
          </LinearGradient>

          <ScrollView style={styles.formScrollView}>
            {selectedParent && (
              <Card>
                <Card.Content>
                  <Title style={getTextStyle()}>
                    {translate('parent.management.parentName')}: {selectedParent.firstName} {selectedParent.lastName}
                  </Title>

                  {selectedParent.children && selectedParent.children.length > 0 ? (
                    <>
                      <Title style={[styles.subSectionTitle, getTextStyle()]}>
                        {translate('parent.management.assignedChildren')}
                      </Title>
                      {selectedParent.childrenData && selectedParent.childrenData.length > 0 ? (
                        selectedParent.childrenData.map((child) => (
                          <List.Item
                            key={child.id}
                            title={`${child.firstName} ${child.lastName}`}
                            titleStyle={getTextStyle()}
                            description={child.grade ? translate('parent.management.grade') + ': ' + child.grade : ''}
                            descriptionStyle={getTextStyle()}
                            left={props => <List.Icon {...props} icon="school" color={'#1976d2'} />}
                          />
                        ))
                      ) : (
                        <Text style={getTextStyle()}>{translate('parent.management.loadingChildren')}</Text>
                      )}
                    </>
                  ) : (
                    <Text style={getTextStyle()}>{translate('parent.management.noChildren')}</Text>
                  )}

                  <CustomButton
                    mode="contained"
                    onPress={() => {
                      setShowChildrenModal(false);
                      setVisible(true);
                    }}
                    style={styles.saveButton}
                    labelStyle={getTextStyle()}
                    icon="pencil"
                  >
                    {translate('parent.management.editParentChildren')}
                  </CustomButton>
                </Card.Content>
              </Card>
            )}
          </ScrollView>
        </Modal>
      </Portal>

      {/* Tooltip */}
      {tooltipVisible && (
        <View
          style={[
            styles.tooltip,
            { top: tooltipPosition.y, left: tooltipPosition.x },
            isRTL && styles.rtlTooltip
          ]}
        >
          <Text style={styles.tooltipText}>{tooltipText}</Text>
          <View style={styles.tooltipArrow} />
        </View>
      )}

      {/* Main FAB button for adding a parent */}
      <Animatable.View animation="fadeIn" duration={1000} delay={800}>
        <FAB
          style={styles.fab}
          icon="account-plus"
          label={translate('admin.parentManagement.addParent') || 'Add New Parent'}
          onPress={() => setVisible(true)}
          color="white"
        />
      </Animatable.View>

      {/* FAB Group for additional actions */}
      <Animatable.View animation="fadeIn" duration={1000} delay={800} style={styles.fabGroupContainer}>
        <FAB.Group
          open={fabOpen}
          icon={fabOpen ? 'close' : 'dots-vertical'}
          actions={[
            {
              icon: 'account-multiple-plus',
              label: translate('admin.parentManagement.bulkImport') || 'Bulk Import Parents',
              onPress: () => {
                // Future implementation for bulk import
                Alert.alert(
                  translate('common.comingSoon') || 'Coming Soon',
                  translate('admin.parentManagement.bulkImportMessage') || 'Bulk import feature will be available soon.'
                );
              },
              color: 'white',
              style: { backgroundColor: '#FF9800' }
            },
            {
              icon: 'refresh',
              label: translate('common.refresh') || 'Refresh List',
              onPress: fetchParents,
              color: 'white',
              style: { backgroundColor: '#2196F3' }
            }
          ]}
          onStateChange={({ open }) => setFabOpen(open)}
          fabStyle={{
            backgroundColor: '#1976d2',
            borderRadius: 28,
            shadowColor: "#000",
            shadowOffset: {
              width: 0,
              height: 4,
            },
            shadowOpacity: 0.30,
            shadowRadius: 4.65,
            elevation: 8,
          }}
          color="white"
        />
      </Animatable.View>
    </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  errorContainer: {
    backgroundColor: '#ffebee',
    padding: 10,
    margin: 10,
    borderRadius: 5,
    borderLeftWidth: 4,
    borderLeftColor: '#f44336',
  },
  errorText: {
    color: '#d32f2f',
  },
  loadingContainer: {
    padding: 20,
    alignItems: 'center',
  },
  childNamePreview: {
    fontSize: 10,
    color: '#555',
    marginTop: 2,
  },
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  rtlContainer: {
    flexDirection: 'row-reverse',
  },
  header: {
    width: '100%',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    zIndex: 10,
  },
  headerGradient: {
    paddingVertical: 16,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerTitle: {
    color: 'white',
    marginLeft: 12,
    fontSize: 20,
    fontWeight: 'bold',
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  languageButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    marginRight: 12,
  },
  languageButtonText: {
    color: 'white',
    marginRight: 4,
    fontWeight: 'bold',
  },
  selectedLanguageItem: {
    backgroundColor: '#e3f2fd',
  },
  selectedLanguageText: {
    fontWeight: 'bold',
  },
  parentCountBadge: {
    backgroundColor: '#ff6f00',
    color: 'white',
  },
  rtlContainer: {
    flexDirection: 'row-reverse',
  },
  header: {
    width: '100%',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    zIndex: 10,
  },
  headerGradient: {
    paddingVertical: 16,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerTitle: {
    color: 'white',
    marginLeft: 12,
    fontSize: 20,
    fontWeight: 'bold',
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  languageButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    marginRight: 12,
  },
  languageButtonText: {
    color: 'white',
    marginRight: 4,
    fontWeight: 'bold',
  },
  selectedLanguageItem: {
    backgroundColor: '#e3f2fd',
  },
  selectedLanguageText: {
    fontWeight: 'bold',
  },
  parentCountBadge: {
    backgroundColor: '#ff6f00',
    color: 'white',
  },
  searchBar: {
    margin: 10,
    elevation: 2,
    borderRadius: 8,
  },
  rtlSearchBar: {
    flexDirection: 'row-reverse',
  },
  content: {
    flex: 1,
    padding: 10,
  },
  rtlContent: {
    flexDirection: 'row-reverse',
  },
  tableContainer: {
    borderRadius: 8,
    overflow: 'hidden',
    elevation: 2,
    marginBottom: 16,
  },
  tableHeader: {
    backgroundColor: '#f0f0f0',
  },
  tableRow: {
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  rtlTableRow: {
    flexDirection: 'row-reverse',
  },
  tableCell: {
    justifyContent: 'center',
  },
  rtlTableCell: {
    justifyContent: 'flex-end',
  },
  tableHeaderText: {
    fontWeight: 'bold',
    color: '#333',
  },
  tableCellText: {
    color: '#555',
  },
  parentName: {
    fontWeight: 'bold',
    color: '#333',
  },
  childrenChip: {
    backgroundColor: '#e3f2fd',
  },
  chipText: {
    fontSize: 12,
  },
  modalContainer: {
    backgroundColor: 'white',
    padding: 20,
    margin: 20,
    maxHeight: '90%',
  },
  modalContent: {
    backgroundColor: 'white',
    padding: 0,
    margin: 20,
    borderRadius: 10,
    maxHeight: '80%',
    overflow: 'hidden',
  },
  rtlModalContent: {
    flexDirection: 'row-reverse',
  },
  modalHeader: {
    paddingVertical: 16,
    paddingHorizontal: 20,
    width: '100%',
  },
  modalHeaderContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  modalHeaderTitle: {
    color: 'white',
    flex: 1,
    marginLeft: 12,
    fontSize: 18,
    fontWeight: 'bold',
  },
  formScrollView: {
    padding: 20,
  },
  rtlFormScrollView: {
    flexDirection: 'row-reverse',
  },
  tooltip: {
    position: 'absolute',
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    padding: 8,
    borderRadius: 4,
    zIndex: 1000,
    maxWidth: 200,
    transform: [{ translateX: -100 }],
  },
  rtlTooltip: {
    transform: [{ translateX: 0 }],
  },
  tooltipText: {
    color: 'white',
    fontSize: 12,
    textAlign: 'center',
  },
  tooltipArrow: {
    position: 'absolute',
    bottom: -8,
    left: '50%',
    marginLeft: -8,
    borderTopWidth: 8,
    borderLeftWidth: 8,
    borderRightWidth: 8,
    borderTopColor: 'rgba(0, 0, 0, 0.8)',
    borderLeftColor: 'transparent',
    borderRightColor: 'transparent',
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
    backgroundColor: '#4CAF50',
    borderRadius: 28,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.30,
    shadowRadius: 4.65,
    elevation: 8,
  },
  fabGroupContainer: {
    position: 'absolute',
    right: 0,
    bottom: 80,
  },
  imageSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  imageContainer: {
    flex: 1,
    alignItems: 'center',
    marginHorizontal: 10,
  },
  imageTitle: {
    fontSize: 16,
    marginBottom: 10,
  },
  image: {
    width: 150,
    height: 150,
    borderRadius: 75,
    marginBottom: 10,
  },
  image1: {
    width: 200,
    height: 150,
    marginBottom: 10,
  },
  placeholderImage: {
    width: 150,
    height: 150,
    borderRadius: 75,
    backgroundColor: '#e0e0e0',
    marginBottom: 10,
  },
  placeholderImage1: {
    width: 200,
    height: 150,
    backgroundColor: '#e0e0e0',
    marginBottom: 10,
  },
  imageButton: {
    marginTop: 10,
  },
  buttonContainer: {
    marginTop: 20,
  },
  submitButton: {
    marginTop: 10,
  },
  selectedItem: {
    backgroundColor: '#e8f4f8',
  },
  chipScrollView: {
    maxHeight: 100,
  },
  chipContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    padding: 5,
  },
  chip: {
    margin: 4,
  },
  sectionTitle: {
    fontSize: 18,
    marginTop: 20,
    marginBottom: 10,
  },
  modalButtons: {
    marginTop: 20,
  },
  deleteButton: {
    marginVertical: 10,
    borderColor: '#f44336',
  },
  deleteButtonText: {
    color: '#f44336',
  },
  buttonText: {
    fontWeight: 'bold',
  },
  saveButton: {
    marginHorizontal: 4,
    backgroundColor: '#2196F3',
  },
  cancelButton: {
    marginHorizontal: 4,
    borderColor: '#757575',
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
    backgroundColor: '#1976d2',
    borderRadius: 28,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.30,
    shadowRadius: 4.65,
    elevation: 8,
  },
  rtlFab: {
    right: undefined,
    left: 0,
  },
  rtlImageSection: {
    flexDirection: 'row-reverse',
  },
  selectedItemText: {
    fontWeight: 'bold',
    color: '#2196F3',
  },
  rtlChipScrollView: {
    flexDirection: 'row-reverse',
  },
  subSectionTitle: {
    fontSize: 16,
    marginLeft: 16,
    marginTop: 8,
    marginBottom: 8,
    color: '#555',
  },
  rtlModalButtons: {
    flexDirection: 'row-reverse',
  },
  kebeleContainer: {
    marginTop: 16,
  },
  kebeleImage: {
    width: 200,
    height: 120,
    resizeMode: 'cover',
    borderRadius: 8,
    marginBottom: 10,
  },
  placeholderKebeleImage: {
    width: 200,
    height: 120,
    backgroundColor: '#f0f0f0',
    borderRadius: 8,
    marginBottom: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emailVerificationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  verificationContainer: {
    marginTop: 8,
    marginBottom: 16,
    padding: 12,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: '#2196F3',
  },
  verificationMessage: {
    marginTop: 8,
    fontSize: 14,
    color: '#666',
    fontStyle: 'italic',
  },
  verificationSuccess: {
    marginTop: 8,
    fontSize: 14,
    color: '#4CAF50',
    fontWeight: 'bold',
  },
});

export default ParentManagement;
