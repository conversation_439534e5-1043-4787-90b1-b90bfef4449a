# Adding MobileUserManagement to Navigation

After combining the MobileUserManagement files into a single component, you need to add it to your navigation stack. Here's how to do it:

## 1. Update AdminNavigator.js

Open `src/navigation/AdminNavigator.js` and make the following changes:

1. Import the MobileUserManagement component at the top of the file:

```javascript
import MobileUserManagement from '../screens/admin/MobileUserManagement';
```

2. Add a new screen for mobile user management:

```javascript
<Stack.Screen
  name="MobileUserManagement"
  component={MobileUserManagement}
  options={{
    title: translate('userManagement.title'),
    headerShown: false
  }}
/>
```

You can place this right after the existing UserManagement screen.

## 2. Update Navigation Logic

To ensure that mobile users are directed to the mobile version of the screen, you need to add conditional navigation logic. There are two approaches:

### Option 1: Device Detection in AdminNavigator

Add device detection logic in the AdminNavigator to automatically route to the appropriate screen:

```javascript
import { Platform, Dimensions } from 'react-native';

// At the top of your component
const isMobile = Platform.OS === 'ios' || Platform.OS === 'android';
const { width } = Dimensions.get('window');
const isSmallScreen = width < 768;

// Then in your navigation logic
<Stack.Screen
  name="UserManagement"
  component={isMobile || isSmallScreen ? MobileUserManagement : UserManagement}
  options={{ title: translate('screens.userManagement') }}
/>
```

### Option 2: Create a Router Component

Create a UserManagementRouter component that decides which version to show:

```javascript
// src/screens/admin/UserManagementRouter.js
import React from 'react';
import { Platform, Dimensions } from 'react-native';
import UserManagement from './UserManagement';
import MobileUserManagement from './MobileUserManagement';

const UserManagementRouter = (props) => {
  const isMobile = Platform.OS === 'ios' || Platform.OS === 'android';
  const { width } = Dimensions.get('window');
  const isSmallScreen = width < 768;
  
  if (isMobile || isSmallScreen) {
    return <MobileUserManagement {...props} />;
  } else {
    return <UserManagement {...props} />;
  }
};

export default UserManagementRouter;
```

Then update AdminNavigator.js:

```javascript
import UserManagementRouter from '../screens/admin/UserManagementRouter';

// In your Stack.Navigator
<Stack.Screen
  name="UserManagement"
  component={UserManagementRouter}
  options={{ title: translate('screens.userManagement') }}
/>
```

## 3. Update Bottom Navigation Links

Make sure any bottom navigation links that point to "UserManagement" will work with your new routing logic. The current implementation already has this set up:

```javascript
const bottomNavItems = [
  { key: 'dashboard', icon: 'view-dashboard', label: 'common.dashboard', route: 'AdminDashboard' },
  { key: 'users', icon: 'account-group', label: 'userManagement.title', route: 'UserManagement' },
  { key: 'teachers', icon: 'teach', label: 'teacher.management.title', route: 'TeacherManagement' },
  { key: 'students', icon: 'account-school', label: 'student.title', route: 'StudentManagement' },
  { key: 'more', icon: 'dots-horizontal', label: 'common.more', route: 'AdminMore' }
];
```

This will work with either approach above, as the route name "UserManagement" remains the same.

## 4. Testing the Navigation

To test the navigation:

1. Make sure the MobileUserManagement component is properly combined and working
2. Update the navigation as described above
3. Run the app on a mobile device or simulator
4. Navigate to the admin section
5. Tap on the "Users" icon in the bottom navigation
6. Verify that the mobile-optimized user management screen appears
