import { useState, useEffect, useContext, createContext } from 'react';
import LanguageService from '../services/LanguageService';
import { useAuth } from '../context/AuthContext';
import { I18nManager } from 'react-native';
import { useLanguage } from '../context/LanguageContext';

const LanguageContext = createContext();

export const LanguageProvider = ({ children }) => {
    const [language, setLanguage] = useState(LanguageService.defaultLanguage);
    const [translations, setTranslations] = useState({});
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState(null);
    const { user } = useAuth();

    const supportedLanguages = {
        en: { name: 'English', isRTL: false, fontScale: 1 },
        am: { name: 'አማርኛ', isRTL: false, fontScale: 1.1 },
        or: { name: '<PERSON><PERSON><PERSON>', isRTL: false, fontScale: 1 }
    };

    useEffect(() => {
        loadUserLanguage();
    }, [user, language]);

    const loadUserLanguage = async () => {
        try {
            setIsLoading(true);
            setError(null);
            
            if (user) {
                // User is authenticated, try to get their preferred language
                const userLanguage = await LanguageService.getUserLanguage(user.uid);
                const allTranslations = await LanguageService.getAllTranslations(userLanguage);
                
                await updateLanguageSettings(userLanguage);
                setTranslations(allTranslations);
                
                console.log(`Loaded language: ${userLanguage} for user: ${user.uid}`);
            } else {
                // No authenticated user, use default language
                await updateLanguageSettings(LanguageService.defaultLanguage);
                const allTranslations = await LanguageService.getAllTranslations(LanguageService.defaultLanguage);
                setTranslations(allTranslations);
                
                console.log(`Loaded default language: ${LanguageService.defaultLanguage}`);
            }
        } catch (err) {
            console.error('Error loading language:', err);
            setError(err.message);
            // Fallback to default language
            await updateLanguageSettings(LanguageService.defaultLanguage);
        } finally {
            setIsLoading(false);
        }
    };

    const updateLanguageSettings = async (newLanguage) => {
        const config = supportedLanguages[newLanguage];
        if (!config) {
            throw new Error(`Unsupported language: ${newLanguage}`);
        }

        // Update RTL setting if needed
        if (I18nManager.isRTL !== config.isRTL) {
            await I18nManager.forceRTL(config.isRTL);
        }

        setLanguage(newLanguage);
    };

    const changeLanguage = async (newLanguage) => {
        try {
            setIsLoading(true);
            setError(null);

            if (user) {
                await LanguageService.setUserLanguage(user.uid, newLanguage);
            }

            await updateLanguageSettings(newLanguage);
            const newTranslations = await LanguageService.getAllTranslations(newLanguage);
            setTranslations(newTranslations);
        } catch (err) {
            console.error('Error changing language:', err);
            setError(err.message);
            throw err;
        } finally {
            setIsLoading(false);
        }
    };

    const getTextStyle = (customStyle = {}) => {
        const config = supportedLanguages[language];
        return {
            ...customStyle,
            writingDirection: config.isRTL ? 'rtl' : 'ltr',
            fontSize: (customStyle.fontSize || 14) * config.fontScale,
        };
    };

    return (
        <LanguageContext.Provider 
            value={{ 
                language, 
                changeLanguage, 
                translations,
                isLoading,
                error,
                supportedLanguages,
                getTextStyle,
                isRTL: supportedLanguages[language]?.isRTL || false
            }}
        >
            {children}
        </LanguageContext.Provider>
    );
};

/**
 * Custom hook for accessing translations throughout the app
 * @returns {Object} - Object containing translation functions and language state
 */
export const useTranslation = () => {
  const languageContext = useLanguage();
  
  return {
    // Original language context properties
    ...languageContext,
    
    // Shorthand for translate function
    t: languageContext.translate,
    
    // Helper functions
    formatNumber: (number, options = {}) => {
      return new Intl.NumberFormat(
        languageContext.language === 'en' ? 'en-US' : 
        languageContext.language === 'am' ? 'am-ET' : 'om-ET', 
        options
      ).format(number);
    },
    
    formatDate: (date, options = {}) => {
      const dateObj = date instanceof Date ? date : new Date(date);
      return dateObj.toLocaleDateString(
        languageContext.language === 'en' ? 'en-US' : 
        languageContext.language === 'am' ? 'am-ET' : 'om-ET',
        options
      );
    },
    
    // Check if a translation exists for a key
    hasTranslation: (key) => {
      return !!languageContext.translate(key);
    }
  };
};

export default useTranslation;

