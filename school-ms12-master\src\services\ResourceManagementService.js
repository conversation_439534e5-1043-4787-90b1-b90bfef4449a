import { db, storage } from '../config/firebase';
import { collection, addDoc, updateDoc, deleteDoc, getDocs, query, where, doc } from 'firebase/firestore';
import { ref, uploadBytes, getDownloadURL, deleteObject } from 'firebase/storage';
import { v4 as uuidv4 } from 'uuid';
import '../utils/uuidPolyfill'; // Import the UUID polyfill
import { generateSimpleUUID } from '../utils/uuidPolyfill';

class ResourceManagementService {
    static instance = null;

    constructor() {
        if (ResourceManagementService.instance) {
            return ResourceManagementService.instance;
        }
        ResourceManagementService.instance = this;

        this.resourceTypes = {
            PHYSICAL: 'physical',
            DIGITAL: 'digital',
            BOOK: 'book',
            EQUIPMENT: 'equipment',
            CLASSROOM: 'classroom',
            LAB: 'laboratory'
        };

        this.resourceStatus = {
            AVAILABLE: 'available',
            IN_USE: 'in_use',
            MAINTENANCE: 'maintenance',
            DAMAGED: 'damaged',
            LOST: 'lost'
        };
    }

    // Add new resource
    async addResource(resourceData) {
        try {
            // Generate ID with fallback
            let resourceId;
            try {
                resourceId = uuidv4();
            } catch (uuidError) {
                console.error('UUID generation failed, using fallback:', uuidError);
                resourceId = generateSimpleUUID();
            }
            const timestamp = Date.now();

            const resource = {
                ...resourceData,
                id: resourceId,
                status: this.resourceStatus.AVAILABLE,
                createdAt: timestamp,
                updatedAt: timestamp,
                maintenanceHistory: [],
                usageHistory: []
            };

            // Handle digital resource upload if file is present
            if (resourceData.file) {
                const fileUrl = await this.uploadDigitalResource(resourceData.file, resourceId);
                resource.fileUrl = fileUrl;
            }

            // Add to Firestore
            await addDoc(collection(db, 'resources'), resource);

            // Add to inventory tracking
            await this.updateInventory(resource.type, 1);

            return resourceId;
        } catch (error) {
            console.error('Failed to add resource:', error);
            throw error;
        }
    }

    // Upload digital resource
    async uploadDigitalResource(file, resourceId) {
        const fileRef = ref(storage, `resources/${resourceId}/${file.name}`);
        await uploadBytes(fileRef, file);
        return await getDownloadURL(fileRef);
    }

    // Update resource
    async updateResource(resourceId, updates) {
        try {
            const resourceRef = doc(db, 'resources', resourceId);

            const updateData = {
                ...updates,
                updatedAt: Date.now()
            };

            await updateDoc(resourceRef, updateData);

            // Handle status change tracking
            if (updates.status) {
                await this.trackStatusChange(resourceId, updates.status);
            }

            return true;
        } catch (error) {
            console.error('Failed to update resource:', error);
            throw error;
        }
    }

    // Delete resource
    async deleteResource(resourceId) {
        try {
            const resourceRef = doc(db, 'resources', resourceId);
            const resource = await this.getResource(resourceId);

            // Delete digital file if exists
            if (resource.fileUrl) {
                const fileRef = ref(storage, resource.fileUrl);
                await deleteObject(fileRef);
            }

            await deleteDoc(resourceRef);

            // Update inventory
            await this.updateInventory(resource.type, -1);

            return true;
        } catch (error) {
            console.error('Failed to delete resource:', error);
            throw error;
        }
    }

    // Get resource by ID
    async getResource(resourceId) {
        const resourceRef = doc(db, 'resources', resourceId);
        const resourceDoc = await getDocs(resourceRef);
        return resourceDoc.exists() ? resourceDoc.data() : null;
    }

    // Search resources
    async searchResources(filters = {}) {
        try {
            let queryRef = collection(db, 'resources');

            // Apply filters
            Object.entries(filters).forEach(([field, value]) => {
                queryRef = query(queryRef, where(field, '==', value));
            });

            const snapshot = await getDocs(queryRef);
            return snapshot.docs.map(doc => ({
                id: doc.id,
                ...doc.data()
            }));
        } catch (error) {
            console.error('Failed to search resources:', error);
            throw error;
        }
    }

    // Track resource usage
    async trackResourceUsage(resourceId, userId, action) {
        try {
            const resourceRef = doc(db, 'resources', resourceId);
            const timestamp = Date.now();

            const usageData = {
                userId,
                action,
                timestamp
            };

            await updateDoc(resourceRef, {
                usageHistory: arrayUnion(usageData),
                lastUsed: timestamp,
                status: action === 'checkout' ? this.resourceStatus.IN_USE : this.resourceStatus.AVAILABLE
            });

            return true;
        } catch (error) {
            console.error('Failed to track resource usage:', error);
            throw error;
        }
    }

    // Schedule maintenance
    async scheduleMaintenance(resourceId, maintenanceData) {
        try {
            const resourceRef = doc(db, 'resources', resourceId);
            const timestamp = Date.now();

            const maintenance = {
                ...maintenanceData,
                scheduledDate: timestamp,
                status: 'scheduled'
            };

            await updateDoc(resourceRef, {
                maintenanceHistory: arrayUnion(maintenance),
                status: this.resourceStatus.MAINTENANCE
            });

            return true;
        } catch (error) {
            console.error('Failed to schedule maintenance:', error);
            throw error;
        }
    }

    // Update inventory
    async updateInventory(resourceType, change) {
        try {
            const inventoryRef = doc(db, 'inventory', resourceType);
            const inventoryDoc = await getDocs(inventoryRef);

            if (inventoryDoc.exists()) {
                await updateDoc(inventoryRef, {
                    quantity: inventoryDoc.data().quantity + change,
                    lastUpdated: Date.now()
                });
            } else {
                await addDoc(collection(db, 'inventory'), {
                    type: resourceType,
                    quantity: change,
                    lastUpdated: Date.now()
                });
            }
        } catch (error) {
            console.error('Failed to update inventory:', error);
            throw error;
        }
    }

    // Track status change
    async trackStatusChange(resourceId, newStatus) {
        try {
            const statusChange = {
                status: newStatus,
                timestamp: Date.now()
            };

            await updateDoc(doc(db, 'resources', resourceId), {
                statusHistory: arrayUnion(statusChange)
            });
        } catch (error) {
            console.error('Failed to track status change:', error);
            throw error;
        }
    }

    // Generate resource report
    async generateResourceReport(type = 'all') {
        try {
            let resources;
            if (type === 'all') {
                resources = await this.searchResources();
            } else {
                resources = await this.searchResources({ type });
            }

            const report = {
                totalCount: resources.length,
                available: resources.filter(r => r.status === this.resourceStatus.AVAILABLE).length,
                inUse: resources.filter(r => r.status === this.resourceStatus.IN_USE).length,
                maintenance: resources.filter(r => r.status === this.resourceStatus.MAINTENANCE).length,
                damaged: resources.filter(r => r.status === this.resourceStatus.DAMAGED).length,
                lost: resources.filter(r => r.status === this.resourceStatus.LOST).length,
                utilization: this.calculateUtilization(resources),
                maintenanceCosts: this.calculateMaintenanceCosts(resources),
                resourcesByType: this.groupResourcesByType(resources)
            };

            return report;
        } catch (error) {
            console.error('Failed to generate resource report:', error);
            throw error;
        }
    }

    // Calculate resource utilization
    calculateUtilization(resources) {
        const now = Date.now();
        const thirtyDaysAgo = now - (30 * 24 * 60 * 60 * 1000);

        return resources.map(resource => ({
            id: resource.id,
            name: resource.name,
            utilizationRate: resource.usageHistory
                .filter(usage => usage.timestamp > thirtyDaysAgo)
                .length / 30
        }));
    }

    // Calculate maintenance costs
    calculateMaintenanceCosts(resources) {
        return resources.reduce((total, resource) => {
            const maintenanceCost = resource.maintenanceHistory
                .reduce((cost, maintenance) => cost + (maintenance.cost || 0), 0);
            return total + maintenanceCost;
        }, 0);
    }

    // Group resources by type
    groupResourcesByType(resources) {
        return resources.reduce((groups, resource) => {
            const type = resource.type;
            if (!groups[type]) {
                groups[type] = [];
            }
            groups[type].push(resource);
            return groups;
        }, {});
    }
}

export default new ResourceManagementService();
