import { Alert } from 'react-native';
import * as FileSystem from 'expo-file-system';
import { Platform } from 'react-native';
import TranslationDebugger from './TranslationDebugger';

/**
 * Updates translation files with missing translation keys
 * This is for development only
 */
export const updateTranslationFiles = async (language) => {
  if (!__DEV__) {
    console.warn('Translation files can only be updated in development mode');
    return;
  }

  if (Platform.OS === 'web') {
    console.warn('Translation file updates are not supported on web platform');
    return;
  }

  try {
    // Get missing translations template JSON
    const missingKeysTemplate = TranslationDebugger.exportMissingTranslationsTemplate(language);
    if (missingKeysTemplate === '{}') {
      Alert.alert('No missing translations', 'All translations are present for this language.');
      return;
    }

    // Create updated JSON file in the app's document directory
    const fileName = `missing_translations_${language}_${Date.now()}.json`;
    const filePath = `${FileSystem.documentDirectory}${fileName}`;

    // Write the file
    await FileSystem.writeAsStringAsync(filePath, missingKeysTemplate);

    // Alert the user about the file location
    Alert.alert(
      'Missing Translations Exported',
      `Missing translations have been exported to your app's document directory as ${fileName}. ` +
      'You can copy these to your translation files.',
      [
        { 
          text: 'OK' 
        }
      ]
    );

    return filePath;
  } catch (error) {
    console.error('Error exporting translation template:', error);
    Alert.alert('Error', 'Failed to export missing translations.');
    return null;
  }
};

/**
 * Development helper to display translation keys in UI
 * Wraps all translated text elements with their key in development mode
 * @param {string} key - Translation key
 * @param {string} text - Translated text
 * @returns {string} - Text with debug info in development mode, or just text in production
 */
export const withDebugInfo = (key, text) => {
  if (!__DEV__ || !global.showTranslationKeys) {
    return text;
  }
  
  return `[${key}] ${text}`;
};

/**
 * Toggle showing translation keys in the UI
 * @param {boolean} show - Whether to show translation keys
 */
export const toggleTranslationKeys = (show = !global.showTranslationKeys) => {
  global.showTranslationKeys = show;
  Alert.alert(
    'Translation Debug Mode',
    `Translation keys are now ${show ? 'visible' : 'hidden'} in the UI.`
  );
  return show;
};

export default {
  updateTranslationFiles,
  withDebugInfo,
  toggleTranslationKeys
}; 