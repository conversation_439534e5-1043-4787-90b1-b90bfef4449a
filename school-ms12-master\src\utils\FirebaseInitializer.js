// Import our platform polyfill to ensure Platform is available
import '../utils/platformPolyfill';
import { firebaseApp } from '../config/firebase';

// Try to get Platform from react-native, but use our polyfill as fallback
let Platform;
try {
  Platform = require('react-native').Platform;
} catch (error) {
  console.log('Using Platform polyfill in FirebaseInitializer');
  Platform = global.Platform;
}

// Attempt to get UIManager and other React Native modules
let UIManager;
try {
  UIManager = require('react-native').UIManager;
} catch (error) {
  console.log('UIManager import failed');
}

/**
 * Utility function to ensure Firebase is properly initialized
 * This is especially important for Android push notifications
 */
export const ensureFirebaseInitialized = async () => {
  try {
    if (!firebaseApp) {
      console.error('Firebase app is not initialized');
      return false;
    }

    console.log('Firebase app name:', firebaseApp.name);

    // Add a delay to ensure Firebase is fully initialized
    // This helps with the "Default FirebaseApp is not initialized" error
    let delay = 1000; // Default delay

    try {
      if (Platform && Platform.OS === 'android') {
        delay = 1500; // Longer delay for Android
        console.log('Using Android-specific delay');
        
        // Make sure UIManager is ready on Android
        if (UIManager) {
          console.log('UIManager is available');
        } else {
          console.warn('UIManager is not available, this may cause rendering issues');
          delay = 2000; // Even longer delay if UIManager isn't detected
        }
      } else if (Platform && Platform.OS === 'ios') {
        delay = 800; // Shorter delay for iOS
        console.log('Using iOS-specific delay');
      }
    } catch (platformError) {
      console.log('Platform detection failed, using default delay');
    }

    // Apply the delay
    await new Promise(resolve => setTimeout(resolve, delay));

    return true;
  } catch (error) {
    console.error('Error ensuring Firebase is initialized:', error);
    return false;
  }
};

/**
 * Get Firebase app instance
 */
export const getFirebaseApp = () => {
  return firebaseApp;
};
