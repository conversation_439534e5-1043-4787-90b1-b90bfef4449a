// This file is a workaround for the expo-modules-core issue
// It creates a symlink to the correct module

const fs = require('fs');
const path = require('path');

// Create the directory structure if it doesn't exist
const modulePath = path.join(__dirname, 'node_modules', 'metro', 'src', 'ModuleGraph', 'worker');
if (!fs.existsSync(modulePath)) {
  fs.mkdirSync(modulePath, { recursive: true });
}

// Create a simple importLocationsPlugin.js file
const pluginContent = `
// This is a placeholder for the importLocationsPlugin
module.exports = function() {
  return {
    visitor: {
      ImportDeclaration(path) {
        // Do nothing, this is just a placeholder
      }
    }
  };
};
`;

// Write the file
fs.writeFileSync(path.join(modulePath, 'importLocationsPlugin.js'), pluginContent);

console.log('Created importLocationsPlugin.js successfully!');
