import { useLanguage } from '../context/LanguageContext';
import EthiopianCalendar from '../utils/EthiopianCalendar';

export const useFormatting = () => {
  const { language } = useLanguage();

  const getLocale = () => {
    switch (language) {
      case 'am':
        return 'am-ET';
      case 'or':
        return 'om-ET';
      default:
        return 'en-US';
    }
  };

  const formatDate = (date) => {
    try {
      // Use Ethiopian calendar for date formatting
      return EthiopianCalendar.formatDate(new Date(date), language);
    } catch (error) {
      console.error('Date formatting error:', error);
      return date.toString();
    }
  };

  const formatShortDate = (date) => {
    try {
      // Use Ethiopian calendar for short date formatting
      return EthiopianCalendar.formatShortDate(new Date(date));
    } catch (error) {
      console.error('Short date formatting error:', error);
      return date.toString();
    }
  };

  const formatTime = (time) => {
    try {
      return new Date(time).toLocaleTimeString(getLocale(), {
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch (error) {
      console.error('Time formatting error:', error);
      return time.toString();
    }
  };

  const formatNumber = (number) => {
    try {
      return new Intl.NumberFormat(getLocale()).format(number);
    } catch (error) {
      console.error('Number formatting error:', error);
      return number.toString();
    }
  };

  return {
    formatDate,
    formatShortDate,
    formatTime,
    formatNumber,
    getLocale
  };
};
