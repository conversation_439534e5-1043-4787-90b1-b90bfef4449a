import React, { useState, useEffect, useRef } from 'react';
import { View, ScrollView, StyleSheet, TouchableOpacity, Animated, Alert } from 'react-native';
import {
  Card,
  Title,
  Text,
  Portal,
  Modal,
  ActivityIndicator,
  DataTable,
  IconButton,
  Chip,
  Avatar,
  Searchbar,
  Menu,
  Divider,
  Button,
  useTheme,
  ProgressBar,
} from 'react-native-paper';
import { db } from '../../config/firebase';
import {
  collection,
  addDoc,
  query,
  getDocs,
  getDoc,
  doc,
  updateDoc,
  deleteDoc,
  where,
  orderBy,
  limit,
} from 'firebase/firestore';
import CustomButton from '../../components/common/CustomButton';
import CustomInput from '../../components/common/CustomInput';
import { useLanguage } from '../../context/LanguageContext';
import { MaterialIcons, MaterialCommunityIcons } from '@expo/vector-icons';
import EthiopianDatePicker from '../../components/common/EthiopianDatePicker';
import EthiopianTimePicker from '../../components/common/EthiopianTimePicker';
import { LinearGradient } from 'expo-linear-gradient';
import { PieChart } from 'react-native-chart-kit';
import AdminAppHeader from '../../components/common/AdminAppHeader';
import AdminSidebar from '../../components/common/AdminSidebar';
import SidebarBackdrop from '../../components/common/SidebarBackdrop';
import { sanitizeChartData, sanitizeChartDatasets } from '../../utils/ChartUtils';

const TeacherAttendance = ({ route, navigation }) => {
  const { translate, language } = useLanguage();
  // No theme needed
  const fadeAnim = useRef(new Animated.Value(0)).current;

  // Sidebar state
  const [drawerOpen, setDrawerOpen] = useState(false);
  const drawerAnim = useRef(new Animated.Value(-300)).current;
  const backdropFadeAnim = useRef(new Animated.Value(0)).current;
  const [activeSidebarItem, setActiveSidebarItem] = useState('TeacherAttendance');

  const [attendance, setAttendance] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedAttendance, setSelectedAttendance] = useState(null);
  const [stats, setStats] = useState({
    present: 0,
    absent: 0,
    late: 0,
    leave: 0,
  });
  const [teachers, setTeachers] = useState([]);
  const [selectedTeacher, setSelectedTeacher] = useState(null);
  const [teacherMenuVisible, setTeacherMenuVisible] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [datePickerMode, setDatePickerMode] = useState('date');
  const [dateField, setDateField] = useState(null);
  const [timePickerField, setTimePickerField] = useState(null);

  const [formData, setFormData] = useState({
    date: new Date().toISOString().split('T')[0],
    status: 'present',
    checkIn: '',
    checkOut: '',
    reason: '',
    notes: '',
  });

  useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 500,
      useNativeDriver: true,
    }).start();

    fetchTeachers();

    // If route has teacherId param, set the selected teacher
    if (route.params?.teacherId) {
      fetchTeacherById(route.params.teacherId);
    }

    // Hide the default header
    navigation.setOptions({
      headerShown: false,
    });
  }, [navigation]);

  const toggleDrawer = () => {
    if (drawerOpen) {
      // Close drawer
      Animated.parallel([
        Animated.timing(drawerAnim, {
          toValue: -300,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(backdropFadeAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        })
      ]).start(() => setDrawerOpen(false));
    } else {
      // Open drawer
      setDrawerOpen(true);
      Animated.parallel([
        Animated.timing(drawerAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(backdropFadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        })
      ]).start();
    }
  };

  useEffect(() => {
    if (selectedTeacher) {
      fetchAttendance();
    }
  }, [selectedTeacher]);

  const fetchTeacherById = async (teacherId) => {
    try {
      const teacherDoc = doc(db, 'users', teacherId);
      const docSnap = await getDoc(teacherDoc);

      if (docSnap.exists()) {
        setSelectedTeacher({ id: docSnap.id, ...docSnap.data() });
      }
    } catch (error) {
      console.error('Error fetching teacher:', error);
    }
  };

  const fetchTeachers = async () => {
    try {
      setLoading(true);
      const teachersRef = collection(db, 'users');
      const q = query(teachersRef, where('role', '==', 'teacher'));
      const querySnapshot = await getDocs(q);

      const teachersData = [];
      querySnapshot.forEach((doc) => {
        teachersData.push({
          id: doc.id,
          ...doc.data(),
          fullName: `${doc.data().firstName || ''} ${doc.data().lastName || ''}`.trim() || doc.data().email
        });
      });

      setTeachers(teachersData);

      // If there's only one teacher, select it automatically
      if (teachersData.length === 1 && !selectedTeacher) {
        setSelectedTeacher(teachersData[0]);
      }
    } catch (error) {
      console.error('Error fetching teachers:', error);
      Alert.alert(
        translate('common.error'),
        translate('common.error')
      );
    } finally {
      setLoading(false);
    }
  };

  const fetchAttendance = async () => {
    if (!selectedTeacher) return;

    try {
      setLoading(true);
      const attendanceRef = collection(db, 'teacherAttendance');
      const q = query(
        attendanceRef,
        where('teacherId', '==', selectedTeacher.id),
        orderBy('date', 'desc')
      );
      const querySnapshot = await getDocs(q);

      const attendanceData = [];
      querySnapshot.forEach((doc) => {
        attendanceData.push({ id: doc.id, ...doc.data() });
      });

      setAttendance(attendanceData);
      calculateStats(attendanceData);
    } catch (error) {
      console.error('Error fetching attendance:', error);
      Alert.alert(
        translate('common.error'),
        translate('common.error')
      );
    } finally {
      setLoading(false);
    }
  };

  const calculateStats = (data) => {
    const stats = data.reduce((acc, curr) => {
      acc[curr.status] = (acc[curr.status] || 0) + 1;
      return acc;
    }, {});

    setStats({
      present: stats.present || 0,
      absent: stats.absent || 0,
      late: stats.late || 0,
      leave: stats.leave || 0,
    });
  };

  const handleAddAttendance = async () => {
    if (!selectedTeacher) {
      Alert.alert(
        translate('common.error'),
        translate('teacherAttendance.selectTeacherFirst')
      );
      return;
    }

    try {
      setLoading(true);
      const attendanceRef = collection(db, 'teacherAttendance');
      await addDoc(attendanceRef, {
        ...formData,
        teacherId: selectedTeacher.id,
        teacherName: selectedTeacher.fullName || selectedTeacher.email,
        createdAt: new Date().toISOString(),
      });

      setModalVisible(false);
      resetForm();
      fetchAttendance();
    } catch (error) {
      console.error('Error adding attendance:', error);
      Alert.alert(
        translate('common.error'),
        translate('common.error')
      );
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateAttendance = async () => {
    try {
      setLoading(true);
      const attendanceRef = doc(db, 'teacherAttendance', selectedAttendance.id);
      await updateDoc(attendanceRef, {
        ...formData,
        updatedAt: new Date().toISOString(),
      });

      setModalVisible(false);
      resetForm();
      fetchAttendance();
    } catch (error) {
      console.error('Error updating attendance:', error);
      Alert.alert(
        translate('common.error'),
        translate('common.error')
      );
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteAttendance = async (attendanceId) => {
    try {
      await deleteDoc(doc(db, 'teacherAttendance', attendanceId));
      fetchAttendance();
    } catch (error) {
      console.error('Error deleting attendance:', error);
      Alert.alert(
        translate('common.error'),
        translate('common.error')
      );
    }
  };

  const resetForm = () => {
    setSelectedAttendance(null);
    setFormData({
      date: new Date().toISOString().split('T')[0],
      status: 'present',
      checkIn: '',
      checkOut: '',
      reason: '',
      notes: '',
    });
  };

  const getStatusColor = (status) => {
    const colors = {
      present: '#4CAF50',
      absent: '#F44336',
      late: '#FFC107',
      leave: '#2196F3',
    };
    return colors[status] || '#757575';
  };

  const handleDateChange = (selectedDate) => {
    const formattedDate = selectedDate.toISOString().split('T')[0];
    setFormData({ ...formData, date: formattedDate });
  };

  const handleTimeChange = (field, selectedTime) => {
    const hours = selectedTime.getHours().toString().padStart(2, '0');
    const minutes = selectedTime.getMinutes().toString().padStart(2, '0');
    const formattedTime = `${hours}:${minutes}`;

    setFormData({ ...formData, [field]: formattedTime });
  };

  const showDatePickerModal = (field) => {
    setDateField(field);
    setDatePickerMode('date');
    setShowDatePicker(true);
  };

  const showTimePickerModal = (field) => {
    setTimePickerField(field);
    setDatePickerMode('time');
    setShowDatePicker(true);
  };

  const renderTeacherSelector = () => (
    <Card style={styles.selectorCard}>
      <Card.Content>
        <View style={styles.selectorHeader}>
          <Title>{translate('teacherAttendance.buttons.selectTeacher')}</Title>
          <TouchableOpacity
            onPress={() => setTeacherMenuVisible(true)}
            style={styles.teacherSelector}
          >
            <Text style={styles.selectedTeacherText}>
              {selectedTeacher ? selectedTeacher.fullName : translate('teacherAttendance.buttons.selectTeacher')}
            </Text>
            <MaterialIcons name="arrow-drop-down" size={24} color="#666" />
          </TouchableOpacity>
        </View>

        <Menu
          visible={teacherMenuVisible}
          onDismiss={() => setTeacherMenuVisible(false)}
          anchor={<View />}
          style={styles.teacherMenu}
        >
          <Searchbar
            placeholder={translate('actions.search')}
            onChangeText={setSearchQuery}
            value={searchQuery}
            style={styles.searchBar}
          />
          <ScrollView style={styles.teacherList}>
            {teachers
              .filter(teacher =>
                teacher.fullName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                (teacher.email && teacher.email.toLowerCase().includes(searchQuery.toLowerCase()))
              )
              .map(teacher => (
                <Menu.Item
                  key={teacher.id}
                  title={teacher.fullName}
                  onPress={() => {
                    setSelectedTeacher(teacher);
                    setTeacherMenuVisible(false);
                    setSearchQuery('');
                  }}
                  style={selectedTeacher?.id === teacher.id ? styles.selectedTeacherItem : null}
                />
              ))}
          </ScrollView>
        </Menu>
      </Card.Content>
    </Card>
  );

  const renderStats = () => {
    // Calculate percentages for pie chart
    // const total = stats.present + stats.absent + stats.late + stats.leave;
    // const chartData = [
    //   {
    //     name: translate('teacherAttendance.stats.present'),
    //     population: stats.present,
    //     color: '#4CAF50',
    //     legendFontColor: '#7F7F7F',
    //   },
    //   {
    //     name: translate('teacherAttendance.stats.absent'),
    //     population: stats.absent,
    //     color: '#F44336',
    //     legendFontColor: '#7F7F7F',
    //   },
    //   {
    //     name: translate('teacherAttendance.stats.late'),
    //     population: stats.late,
    //     color: '#FFC107',
    //     legendFontColor: '#7F7F7F',
    //   },
    //   {
    //     name: translate('teacherAttendance.stats.leave'),
    //     population: stats.leave,
    //     color: '#2196F3',
    //     legendFontColor: '#7F7F7F',
    //   },
    // ];

    return (
      <View style={styles.statsContainer}>
        <Card style={[styles.statCard, { borderColor: '#4CAF50' }]}>
          <Card.Content>
            <Title style={styles.statNumber}>{stats.present}</Title>
            <Text>{translate('teacherAttendance.stats.present')}</Text>
          </Card.Content>
        </Card>

        <Card style={[styles.statCard, { borderColor: '#F44336' }]}>
          <Card.Content>
            <Title style={styles.statNumber}>{stats.absent}</Title>
            <Text>{translate('teacherAttendance.stats.absent')}</Text>
          </Card.Content>
        </Card>

        <Card style={[styles.statCard, { borderColor: '#FFC107' }]}>
          <Card.Content>
            <Title style={styles.statNumber}>{stats.late}</Title>
            <Text>{translate('teacherAttendance.stats.late')}</Text>
          </Card.Content>
        </Card>

        <Card style={[styles.statCard, { borderColor: '#2196F3' }]}>
          <Card.Content>
            <Title style={styles.statNumber}>{stats.leave}</Title>
            <Text>{translate('teacherAttendance.stats.leave')}</Text>
          </Card.Content>
        </Card>
      </View>
    );
  };

  return (
    <View style={styles.mainContainer}>
      {/* Sidebar */}
      <AdminSidebar
        drawerAnim={drawerAnim}
        activeSidebarItem={activeSidebarItem}
        setActiveSidebarItem={setActiveSidebarItem}
        toggleDrawer={toggleDrawer}
      />

      {/* Backdrop */}
      <SidebarBackdrop
        visible={drawerOpen}
        onPress={toggleDrawer}
        fadeAnim={backdropFadeAnim}
      />

      {/* Admin App Header */}
      <AdminAppHeader
        title={translate('teacherAttendance.title')}
        onMenuPress={toggleDrawer}
      />

      <Animated.View style={[styles.container, { opacity: fadeAnim }]}>
        <LinearGradient
          colors={['#6200ee', '#3700b3']}
          style={styles.headerGradient}
        >
          <Card style={styles.headerCard}>
            <Card.Content>
              <Text style={styles.headerSubtitle}>{translate('teacherAttendance.subtitle')}</Text>
            </Card.Content>
          </Card>
        </LinearGradient>

      {renderTeacherSelector()}

      {selectedTeacher && (
        <>
          {renderStats()}

          {loading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color="#6200ee" />
              <Text style={styles.loadingText}>{translate('common.loading')}</Text>
            </View>
          ) : attendance.length > 0 ? (
            <ScrollView style={styles.tableContainer}>
              <DataTable>
                <DataTable.Header style={styles.tableHeader}>
                  <DataTable.Title>{translate('teacherAttendance.table.date')}</DataTable.Title>
                  <DataTable.Title>{translate('teacherAttendance.table.status')}</DataTable.Title>
                  <DataTable.Title>{translate('teacherAttendance.table.checkIn')}</DataTable.Title>
                  <DataTable.Title>{translate('teacherAttendance.table.checkOut')}</DataTable.Title>
                  <DataTable.Title>{translate('teacherAttendance.table.actions')}</DataTable.Title>
                </DataTable.Header>

                {attendance.map((record) => (
                  <DataTable.Row key={record.id} style={styles.tableRow}>
                    <DataTable.Cell>{record.date}</DataTable.Cell>
                    <DataTable.Cell>
                      <Chip
                        mode="outlined"
                        style={[styles.statusChip, { backgroundColor: getStatusColor(record.status) + '20' }]}
                        textStyle={{ color: getStatusColor(record.status) }}
                      >
                        {translate(`teacherAttendance.status.${record.status}`)}
                      </Chip>
                    </DataTable.Cell>
                    <DataTable.Cell>{record.checkIn || '-'}</DataTable.Cell>
                    <DataTable.Cell>{record.checkOut || '-'}</DataTable.Cell>
                    <DataTable.Cell>
                      <View style={styles.rowActions}>
                        <IconButton
                          icon="pencil"
                          size={20}
                          color="#6200ee"
                          onPress={() => {
                            setSelectedAttendance(record);
                            setFormData({
                              date: record.date,
                              status: record.status,
                              checkIn: record.checkIn || '',
                              checkOut: record.checkOut || '',
                              reason: record.reason || '',
                              notes: record.notes || '',
                            });
                            setModalVisible(true);
                          }}
                        />
                        <IconButton
                          icon="delete"
                          size={20}
                          color="#F44336"
                          onPress={() => handleDeleteAttendance(record.id)}
                        />
                      </View>
                    </DataTable.Cell>
                  </DataTable.Row>
                ))}
              </DataTable>
            </ScrollView>
          ) : (
            <View style={styles.emptyContainer}>
              <MaterialCommunityIcons name="calendar-blank" size={64} color="#ccc" />
              <Text style={styles.emptyText}>No attendance records found</Text>
            </View>
          )}
        </>
      )}

      {/* Ethiopian Date and Time Pickers are used instead of DateTimePicker */}

      <Portal>
        <Modal
          visible={modalVisible}
          onDismiss={() => {
            setModalVisible(false);
            resetForm();
          }}
          contentContainerStyle={styles.modalContent}
        >
          <ScrollView>
            <Title style={styles.modalTitle}>
              {selectedAttendance
                ? translate('teacherAttendance.modal.edit')
                : translate('teacherAttendance.modal.mark')}
            </Title>

            <View style={styles.dateInput}>
              <Text style={styles.dateInputLabel}>{translate('teacherAttendance.modal.date')}</Text>
              <EthiopianDatePicker
                value={formData.date ? new Date(formData.date) : new Date()}
                onChange={handleDateChange}
                label={translate('teacherAttendance.modal.selectDate')}
                language={language}
                display="compact"
                themeType="light"
                buttonMode="contained"
                showIcon={true}
                iconPosition="right"
                theme={{
                  primaryColor: '#6200ee',
                  textColor: '#333',
                  borderColor: '#ddd',
                  backgroundColor: '#fff',
                  weekendColor: '#f44336',
                  todayColor: '#03A9F4'
                }}
              />
            </View>

            <View style={styles.statusContainer}>
              {['present', 'absent', 'late', 'leave'].map((status) => (
                <Chip
                  key={status}
                  selected={formData.status === status}
                  onPress={() => setFormData({ ...formData, status })}
                  style={[
                    styles.statusChip,
                    formData.status === status && {
                      backgroundColor: getStatusColor(status) + '20',
                    },
                  ]}
                  textStyle={
                    formData.status === status && {
                      color: getStatusColor(status),
                    }
                  }
                >
                  {translate(`teacherAttendance.status.${status}`)}
                </Chip>
              ))}
            </View>

            <View style={styles.dateInput}>
              <Text style={styles.dateInputLabel}>{translate('teacherAttendance.modal.checkIn')}</Text>
              <EthiopianTimePicker
                value={formData.checkIn ? new Date(`2023-01-01T${formData.checkIn}:00`) : new Date()}
                onChange={(time) => handleTimeChange('checkIn', time)}
                label={translate('teacherAttendance.modal.selectCheckInTime')}
                is24Hour={true}
                theme={{
                  primaryColor: '#6200ee',
                  textColor: '#333',
                  borderColor: '#ddd',
                  backgroundColor: '#fff'
                }}
              />
            </View>

            <View style={styles.dateInput}>
              <Text style={styles.dateInputLabel}>{translate('teacherAttendance.modal.checkOut')}</Text>
              <EthiopianTimePicker
                value={formData.checkOut ? new Date(`2023-01-01T${formData.checkOut}:00`) : new Date()}
                onChange={(time) => handleTimeChange('checkOut', time)}
                label={translate('teacherAttendance.modal.selectCheckOutTime')}
                is24Hour={true}
                theme={{
                  primaryColor: '#6200ee',
                  textColor: '#333',
                  borderColor: '#ddd',
                  backgroundColor: '#fff'
                }}
              />
            </View>

            <CustomInput
              label={translate('teacherAttendance.modal.reason')}
              value={formData.reason}
              onChangeText={(text) => setFormData({ ...formData, reason: text })}
              multiline
              numberOfLines={2}
              style={styles.modalInput}
            />

            <CustomInput
              label={translate('teacherAttendance.modal.notes')}
              value={formData.notes}
              onChangeText={(text) => setFormData({ ...formData, notes: text })}
              multiline
              numberOfLines={2}
              style={styles.modalInput}
            />

            <View style={styles.modalButtons}>
              <CustomButton
                mode="contained"
                onPress={selectedAttendance ? handleUpdateAttendance : handleAddAttendance}
                loading={loading}
                style={styles.modalButton}
              >
                {selectedAttendance
                  ? translate('teacherAttendance.modal.update')
                  : translate('teacherAttendance.modal.mark')}
              </CustomButton>

              <CustomButton
                mode="outlined"
                onPress={() => {
                  setModalVisible(false);
                  resetForm();
                }}
                style={styles.modalButton}
              >
                {translate('teacherAttendance.modal.cancel')}
              </CustomButton>
            </View>
          </ScrollView>
        </Modal>
      </Portal>

      {selectedTeacher && (
        <CustomButton
          mode="contained"
          icon="plus"
          onPress={() => setModalVisible(true)}
          style={styles.addButton}
        >
          {translate('teacherAttendance.buttons.markAttendance')}
        </CustomButton>
      )}
    </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  headerGradient: {
    paddingTop: 20,
    paddingBottom: 10,
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
  },
  headerCard: {
    margin: 10,
    elevation: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 10,
  },
  headerTitle: {
    color: '#3700b3',
    fontWeight: 'bold',
    fontSize: 22,
  },
  headerSubtitle: {
    color: '#666',
    marginTop: 5,
  },
  selectorCard: {
    margin: 10,
    elevation: 3,
    borderRadius: 10,
  },
  selectorHeader: {
    marginBottom: 10,
  },
  teacherSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 10,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 5,
    marginTop: 10,
  },
  selectedTeacherText: {
    fontSize: 16,
    color: '#333',
  },
  teacherMenu: {
    width: '80%',
    maxHeight: 300,
  },
  searchBar: {
    margin: 8,
    borderRadius: 5,
  },
  teacherList: {
    maxHeight: 200,
  },
  selectedTeacherItem: {
    backgroundColor: 'rgba(98, 0, 238, 0.1)',
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 10,
    flexWrap: 'wrap',
  },
  statCard: {
    flex: 1,
    margin: 5,
    borderWidth: 1,
    borderRadius: 10,
    minWidth: 80,
    elevation: 2,
  },
  statNumber: {
    fontSize: 24,
    textAlign: 'center',
    fontWeight: 'bold',
  },
  tableContainer: {
    flex: 1,
    margin: 10,
  },
  tableHeader: {
    backgroundColor: '#f0f0f0',
  },
  tableRow: {
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  rowActions: {
    flexDirection: 'row',
  },
  statusChip: {
    margin: 4,
    borderRadius: 15,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  modalContent: {
    backgroundColor: 'white',
    padding: 20,
    margin: 20,
    borderRadius: 10,
    maxHeight: '80%',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 15,
    color: '#3700b3',
  },
  dateInput: {
    marginBottom: 15,
  },
  dateInputLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 5,
  },
  dateInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 10,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 5,
  },
  dateInputText: {
    fontSize: 16,
  },
  statusContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginVertical: 15,
  },
  modalInput: {
    marginBottom: 10,
  },
  modalButtons: {
    marginTop: 20,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  modalButton: {
    flex: 1,
    marginHorizontal: 5,
  },
  addButton: {
    margin: 16,
    borderRadius: 30,
    elevation: 4,
  },
});

export default TeacherAttendance;
