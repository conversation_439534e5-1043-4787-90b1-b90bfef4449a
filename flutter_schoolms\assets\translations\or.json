{"common": {"appName": "<PERSON><PERSON>", "loading": "Fe'aa jira...", "error": "Dogoggora", "success": "<PERSON><PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON>", "delete": "<PERSON><PERSON><PERSON>", "edit": "<PERSON><PERSON><PERSON>", "view": "<PERSON><PERSON><PERSON>", "submit": "<PERSON><PERSON><PERSON>", "back": "<PERSON><PERSON><PERSON>", "next": "<PERSON><PERSON>", "previous": "<PERSON><PERSON>", "search": "Barbaadi", "clearSearch": "<PERSON><PERSON><PERSON><PERSON>", "filter": "<PERSON><PERSON><PERSON>", "sort": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "refresh": "<PERSON><PERSON><PERSON><PERSON>", "noData": "<PERSON><PERSON><PERSON> hin jiru", "yes": "<PERSON><PERSON><PERSON>", "no": "<PERSON><PERSON><PERSON>", "confirm": "<PERSON><PERSON><PERSON><PERSON>", "reject": "<PERSON><PERSON><PERSON>", "approve": "<PERSON><PERSON><PERSON><PERSON>", "logout": "<PERSON>'i", "login": "<PERSON><PERSON>", "register": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "today": "Har'a", "yesterday": "<PERSON><PERSON><PERSON>", "daysAgo": "Guyyaa {{count}} dura", "date": "<PERSON><PERSON><PERSON>", "time": "Sa'<PERSON><PERSON><PERSON>", "status": "Ha<PERSON>", "active": "<PERSON><PERSON><PERSON><PERSON><PERSON> jira", "inactive": "<PERSON><PERSON><PERSON>", "pending": "<PERSON><PERSON><PERSON><PERSON> jira", "completed": "<PERSON><PERSON><PERSON><PERSON>", "rejected": "<PERSON><PERSON><PERSON><PERSON>", "approved": "<PERSON><PERSON><PERSON><PERSON>", "all": "<PERSON><PERSON>", "indexNeeded": "Tuulamni barbaachi<PERSON>", "createIndexMessage": "<PERSON><PERSON><PERSON><PERSON> kana hojjechuuf tuulama uumi", "createIndex": "<PERSON><PERSON><PERSON>", "dashboard": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "photo": "<PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "remarks": "<PERSON><PERSON>", "roll": "Lakkoofsa", "dismiss": "<PERSON><PERSON><PERSON>", "notes": "<PERSON><PERSON><PERSON><PERSON>", "unnamed": "<PERSON><PERSON>a hin qabu", "notAssigned": "<PERSON><PERSON>", "addRemarks": "<PERSON><PERSON> itti da<PERSON>...", "invalidDate": "<PERSON><PERSON><PERSON>", "selectLanguage": "<PERSON><PERSON><PERSON>", "details": "<PERSON><PERSON>a bal'aa", "confirmLogout": "<PERSON>huu akka barbaaddu mirkanee<PERSON><PERSON>ha?", "viewAll": "<PERSON><PERSON>", "confirmLogoutMessage": "<PERSON><PERSON><PERSON><PERSON> ba'uu barba<PERSON>daa?", "errorLoadingData": "Daataa fe'uun hin danda'amne", "navigatingTo": "Gara itti jijji<PERSON>a jira", "dateFormat": "DD/MM/YYYY"}, "auth": {"login": "<PERSON><PERSON>", "loginTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> keetti <PERSON>i", "loginSubtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> keetti seeenu<PERSON>ha<PERSON> ragaa kee galchi", "email": "<PERSON><PERSON><PERSON><PERSON>", "password": "<PERSON><PERSON>", "role": "<PERSON><PERSON><PERSON>", "teacher": "<PERSON><PERSON><PERSON><PERSON>", "student": "Barataa", "parent": "<PERSON><PERSON><PERSON>"}, "teacher": {"dashboard": {"title": "Daash<PERSON><PERSON><PERSON>", "welcome": "<PERSON>ga nagaan dhuftan", "overview": "Il<PERSON><PERSON>", "quickActions": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recentActivity": "Gochaalee <PERSON>", "statistics": "Is<PERSON>ati<PERSON>ii", "totalStudents": "<PERSON><PERSON><PERSON><PERSON>", "totalClasses": "Kutaale<PERSON>", "upcomingEvents": "<PERSON><PERSON><PERSON><PERSON>", "attendance": "<PERSON><PERSON><PERSON><PERSON>", "assignmentsOverview": "Il<PERSON><PERSON>", "pendingGrading": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yourClasses": "<PERSON><PERSON><PERSON><PERSON>", "todaySchedule": "Sagantaa <PERSON>a", "classPerformance": "<PERSON><PERSON><PERSON><PERSON>", "recentSubmissions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "notifications": "<PERSON><PERSON><PERSON><PERSON>", "markAllAsRead": "<PERSON><PERSON>", "viewAll": "<PERSON><PERSON> il<PERSON>", "settings": "<PERSON><PERSON><PERSON><PERSON><PERSON>a", "profile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "help": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "goToClass": "<PERSON><PERSON><PERSON><PERSON>", "viewDetails": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dashboard": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "activeStudents": "<PERSON><PERSON><PERSON><PERSON>", "pendingTasks": "<PERSON><PERSON><PERSON><PERSON>", "progressOverview": "<PERSON><PERSON><PERSON>", "attendanceOverview": "<PERSON><PERSON><PERSON>", "gradingProgress": "Raaw<PERSON><PERSON>ab<PERSON>", "fullTodaySchedule": "<PERSON><PERSON><PERSON><PERSON>", "viewFullSchedule": "<PERSON><PERSON><PERSON> guutuu ilaali", "viewFullWeekSchedule": "Sagantaa Torbana<PERSON>", "noClassesToday": "Kutaaleen <PERSON><PERSON>", "period": "Yeroo", "room": "<PERSON><PERSON><PERSON>", "class": "<PERSON><PERSON><PERSON>", "in": "<PERSON><PERSON><PERSON><PERSON>", "minutes": "<PERSON><PERSON><PERSON><PERSON>", "minutesLeft": "<PERSON><PERSON><PERSON><PERSON> hafe", "viewClass": "<PERSON><PERSON><PERSON>", "prepare": "Qopha<PERSON>'<PERSON>", "next": "<PERSON><PERSON>", "ongoing": "<PERSON><PERSON><PERSON>", "now": "<PERSON><PERSON>", "goodMorning": "<PERSON><PERSON><PERSON>,", "goodAfternoon": "<PERSON><PERSON><PERSON>,", "goodEvening": "<PERSON><PERSON><PERSON>,", "gradeBook": "Kitaaba Qabxee", "assignments": "<PERSON><PERSON><PERSON>", "calendar": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "messaging": "<PERSON><PERSON><PERSON>", "logout": "<PERSON>'i", "academic": "Barnootummaa", "classes": "<PERSON><PERSON><PERSON><PERSON>", "assessments": "Mad<PERSON>liiwwan", "grades": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "homework": "<PERSON><PERSON><PERSON>", "manageClasses": "<PERSON><PERSON><PERSON><PERSON> bulchi", "createAssessments": "<PERSON><PERSON><PERSON><PERSON> uumi fi to'<PERSON>hu", "recordGrades": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> galmeessi fi to'<PERSON>hu", "assignHomework": "<PERSON><PERSON>i manaa ramadi fi hordoffi", "attendanceMonitoring": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "realTimeAttendance": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "examAttendance": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "examSchedule": "<PERSON><PERSON><PERSON>", "behavior": "Amala", "takeAttendance": "<PERSON><PERSON><PERSON><PERSON><PERSON> gal<PERSON> fi to'<PERSON>hu", "monitorAttendance": "<PERSON><PERSON><PERSON><PERSON><PERSON> yeroo dhu<PERSON>a ho<PERSON>i", "trackExamAttendance": "<PERSON><PERSON><PERSON><PERSON><PERSON> q<PERSON>i", "viewExamSchedules": "Sagantaalee qormaataa ilaali", "monitorBehavior": "Amala barataa ho<PERSON>i", "reportsAnalytics": "Gabaasawwan fi <PERSON>", "reports": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "classAnalytics": "Xinxala Daree", "markSheets": "<PERSON><PERSON><PERSON><PERSON>", "studentPortfolios": "Foliowwan <PERSON>", "viewReports": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "analyzePerformance": "Raawwii xinxali", "submitMarkSheets": "<PERSON><PERSON><PERSON><PERSON> qab<PERSON>i galchi", "reviewPortfolios": "<PERSON><PERSON><PERSON><PERSON>", "communicationResources": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> fi Qabeenya", "library": "<PERSON><PERSON> kit<PERSON>a", "resources": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "communicate": "Barattootaa fi maati<PERSON><PERSON>wan waliin quunnamuun", "manageSchedules": "Sagantaalee bulchuu", "accessLibrary": "<PERSON>a kitaabaa da<PERSON>u", "requestResources": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> gaa<PERSON>u", "quickStats": "Istaatiks<PERSON>", "students": "<PERSON><PERSON><PERSON><PERSON>", "studentPerformance": "Raawwii Bar<PERSON>", "performanceSubtitle": "Qab<PERSON>i g<PERSON>ii", "averagePerformance": "<PERSON><PERSON><PERSON><PERSON>", "viewDetailedPerformance": "<PERSON><PERSON><PERSON><PERSON> bal'aa ilaali", "attendanceStats": "Istaatiksii Dhiyeennaa", "present": "<PERSON><PERSON><PERSON><PERSON>", "absent": "<PERSON>n dhi<PERSON>", "late": "<PERSON><PERSON><PERSON>", "excused": "<PERSON><PERSON><PERSON>", "completed": "<PERSON><PERSON><PERSON>", "noSchedule": "<PERSON><PERSON><PERSON> har'a<PERSON> hin jiru", "recentActivities": "<PERSON><PERSON><PERSON><PERSON>", "noActivities": "<PERSON><PERSON><PERSON><PERSON> dhiyoo hin argamne", "statsRefreshed": "Istaatiksiin ha<PERSON>", "activityDetails": "<PERSON><PERSON><PERSON> go<PERSON>a", "activitySelected": "<PERSON><PERSON><PERSON> filatame: {{title}}", "newExamSchedule": "<PERSON><PERSON><PERSON>", "examScheduleNotification": "<PERSON><PERSON><PERSON> qormaata ha<PERSON>a jira", "currentClass": "<PERSON><PERSON>", "current": "<PERSON><PERSON><PERSON>", "preparingDashboard": "<PERSON><PERSON><PERSON><PERSON><PERSON> qophee<PERSON>a jira", "viewClassPerformance": "<PERSON><PERSON><PERSON><PERSON>", "viewStudentPerformance": "Raaw<PERSON><PERSON>", "exportData": "<PERSON><PERSON><PERSON> baasi"}, "drawerMenu": {"academic": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "communication": {"newMessage": "<PERSON><PERSON><PERSON>"}, "attendance": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "grades": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "homework": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "exams": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "classes": {"title": "<PERSON><PERSON><PERSON><PERSON>"}, "calendar": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "attendanceManagement": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "markAttendance": "<PERSON><PERSON><PERSON><PERSON>", "class": "<PERSON><PERSON><PERSON>", "section": "<PERSON><PERSON><PERSON>", "date": "<PERSON><PERSON><PERSON>", "saveAttendance": "<PERSON><PERSON><PERSON><PERSON>", "submitAttendance": "<PERSON><PERSON><PERSON><PERSON>", "present": "<PERSON><PERSON><PERSON>", "absent": "<PERSON><PERSON>", "late": "<PERSON><PERSON><PERSON>", "excused": "<PERSON><PERSON><PERSON><PERSON>", "remarks": "Yaadannoolee", "addRemarks": "<PERSON><PERSON><PERSON><PERSON> (filannoodha)", "studentList": "<PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "rollNumber": "Lakkoofsa G<PERSON>mee", "status": "Ha<PERSON>", "action": "Tarka<PERSON><PERSON><PERSON>", "markAll": "<PERSON><PERSON>", "filter": "<PERSON><PERSON><PERSON>", "loadingData": "Ragaa barataa fe'aa jira...", "noStudentsInSection": "<PERSON><PERSON><PERSON><PERSON> kutaa kana keessatti hin argamne", "classNotFound": "<PERSON><PERSON><PERSON> hin argamne", "attendanceSaved": "<PERSON><PERSON><PERSON><PERSON><PERSON>'<PERSON><PERSON> o<PERSON>'ameera", "attendanceSubmitted": "<PERSON><PERSON><PERSON><PERSON>'<PERSON>aan dhi<PERSON>", "errorSaving": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>u k<PERSON>", "errorSubmitting": "<PERSON><PERSON><PERSON><PERSON> gal<PERSON>u k<PERSON>a", "confirmSubmit": "<PERSON><PERSON><PERSON>a kana galchuu akka barbaaddu mirkaneffadha? Ye<PERSON> tokko erga dhiyaatee booda, hayyama malee jijjiiruu hin dandaa'amu.", "cancel": "<PERSON><PERSON>", "confirm": "<PERSON><PERSON><PERSON><PERSON>", "submissionNote": "<PERSON><PERSON><PERSON><PERSON> (filannoodha)", "submissionStatus": "<PERSON><PERSON>", "pending": "Eegama", "approved": "<PERSON><PERSON>", "rejected": "<PERSON><PERSON>", "viewHistory": "<PERSON><PERSON><PERSON>", "attendanceHistory": "<PERSON><PERSON><PERSON>", "summary": "Cuunfaa", "totalPresent": "<PERSON><PERSON><PERSON>", "totalAbsent": "<PERSON><PERSON> <PERSON><PERSON>", "totalLate": "<PERSON><PERSON>", "totalExcused": "<PERSON><PERSON><PERSON>", "presentPercentage": "D<PERSON><PERSON><PERSON><PERSON>", "absentPercentage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "markAllAsPresent": "<PERSON>nda akka a<PERSON><PERSON><PERSON> mallatt<PERSON>si", "markAllAsAbsent": "<PERSON>nda akka hin a<PERSON><PERSON><PERSON><PERSON> mallatt<PERSON>si", "markAllAsLate": "<PERSON>nda akka tura<PERSON><PERSON> mallatteessi", "markAllAsExcused": "<PERSON>nda akka hay<PERSON><PERSON><PERSON> mallatteessi"}, "classSchedule": {"title": "<PERSON><PERSON><PERSON>", "weekView": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dayView": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "listView": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "today": "Har'a", "noSchedule": "<PERSON><PERSON><PERSON><PERSON> guyyaa kanaaf hin saganta<PERSON>'an", "morning": "Ganama", "afternoon": "<PERSON><PERSON><PERSON>", "evening": "G<PERSON><PERSON><PERSON>", "period": "Yeroo", "time": "Sa'<PERSON><PERSON><PERSON>", "subject": "<PERSON><PERSON>", "class": "<PERSON><PERSON><PERSON>", "section": "<PERSON><PERSON><PERSON>", "room": "<PERSON><PERSON><PERSON>", "day": "<PERSON><PERSON><PERSON>", "monday": "Wiixata", "tuesday": "<PERSON><PERSON><PERSON><PERSON>", "wednesday": "<PERSON><PERSON><PERSON><PERSON>", "thursday": "<PERSON><PERSON><PERSON>", "friday": "<PERSON><PERSON><PERSON>", "saturday": "Sanbata", "sunday": "Di<PERSON>bat<PERSON>", "allDays": "Guyyoota Mara", "viewDetails": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "startTime": "<PERSON><PERSON>", "endTime": "<PERSON><PERSON>", "currentClass": "<PERSON><PERSON><PERSON>", "upcomingClass": "<PERSON><PERSON><PERSON>", "filterByDay": "<PERSON><PERSON><PERSON>", "showFilters": "Calleessi<PERSON><PERSON>", "hideFilters": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "refreshing": "<PERSON><PERSON><PERSON> haaro<PERSON>u...", "errorLoading": "Sagantaa fe'uu k<PERSON><PERSON> dog<PERSON>a", "tryAgain": "<PERSON><PERSON>", "noClasses": "<PERSON><PERSON><PERSON><PERSON> siif hin ramada<PERSON>ne", "sessionDetails": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "classStartsIn": "<PERSON><PERSON><PERSON> {{minutes}} k<PERSON><PERSON><PERSON> jal<PERSON>ba", "classEndsIn": "<PERSON><PERSON><PERSON> {{minutes}} k<PERSON><PERSON><PERSON> xumura", "downloadSchedule": "Saganta<PERSON>", "printSchedule": "Sagantaa <PERSON>"}, "gradeManagement": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "createAssessment": "<PERSON><PERSON><PERSON><PERSON>", "recordGrades": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "viewGrades": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "viewAssessments": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "assessmentDetails": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "assessmentName": "<PERSON><PERSON><PERSON>", "assessmentType": "<PERSON><PERSON>", "assessmentDate": "<PERSON><PERSON><PERSON>", "totalPoints": "Qabxiiwwan <PERSON>", "class": "<PERSON><PERSON><PERSON>", "section": "<PERSON><PERSON><PERSON>", "subject": "<PERSON><PERSON>", "description": "<PERSON><PERSON>a", "instructions": "Qajeelfamoota", "publishToStudents": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "saveAssessment": "<PERSON><PERSON><PERSON><PERSON>", "enterGrades": "Qab<PERSON>iw<PERSON>", "studentName": "<PERSON><PERSON><PERSON>", "rollNumber": "Lakkoofsa G<PERSON>mee", "score": "<PERSON><PERSON><PERSON><PERSON>", "comments": "Ya<PERSON><PERSON>", "status": "Ha<PERSON>", "graded": "Qabxeef<PERSON>meera", "notGraded": "<PERSON><PERSON>", "saveGrades": "<PERSON>ab<PERSON><PERSON><PERSON><PERSON>", "submitForApproval": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "performance": "Raaw<PERSON><PERSON>", "classAverage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "highestScore": "<PERSON>ab<PERSON><PERSON>", "lowestScore": "Qabxii Gadi <PERSON>", "passRate": "<PERSON><PERSON><PERSON><PERSON>", "failRate": "<PERSON><PERSON><PERSON><PERSON>", "excellent": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "good": "<PERSON><PERSON><PERSON>", "average": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "needsImprovement": "<PERSON><PERSON><PERSON>'insa <PERSON>", "poor": "<PERSON><PERSON><PERSON>", "filter": "<PERSON><PERSON><PERSON>", "filterByClass": "<PERSON><PERSON><PERSON>", "filterBySubject": "<PERSON><PERSON>", "filterByType": "<PERSON><PERSON><PERSON>", "quiz": "Gaaffii Gabaabaa", "test": "<PERSON><PERSON><PERSON><PERSON>", "exam": "<PERSON><PERSON><PERSON><PERSON>", "assignment": "<PERSON><PERSON><PERSON>", "project": "Pirojeektii", "midterm": "<PERSON><PERSON><PERSON><PERSON>", "final": "<PERSON><PERSON><PERSON><PERSON>", "loadingAssessments": "Madaalliiwwan fe'aa jira...", "loadingGrades": "Qabxiiwwan fe'aa jira...", "noAssessments": "<PERSON><PERSON><PERSON><PERSON><PERSON> hin argamne", "noGrades": "Qab<PERSON><PERSON><PERSON><PERSON> hin galmoofne", "confirmSubmit": "Qab<PERSON>iw<PERSON> kana mirkanaa'uuf dhiyeessuu akka barbaaddu mirkaneffadha? <PERSON><PERSON> tokko erga dhiya<PERSON>i booda, <PERSON><PERSON> bulchi<PERSON>ota malee jijjiiruu hin dandaa'amu.", "gradesSaved": "Qabxiiwwan milk<PERSON>'inaan olkaa'amaniiru", "gradesSubmitted": "Qab<PERSON><PERSON><PERSON><PERSON> mirkana<PERSON>'<PERSON><PERSON>'inaan dhiya<PERSON>iru", "errorSaving": "Qab<PERSON><PERSON><PERSON><PERSON> o<PERSON><PERSON><PERSON><PERSON>u k<PERSON><PERSON>", "errorSubmitting": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dhi<PERSON><PERSON><PERSON>u k<PERSON>a", "weightage": "<PERSON><PERSON><PERSON>", "assessmentCreated": "<PERSON><PERSON><PERSON><PERSON> milk<PERSON>'<PERSON><PERSON> u<PERSON>ra", "assessmentUpdated": "<PERSON><PERSON><PERSON><PERSON> milk<PERSON>'<PERSON><PERSON> ha<PERSON>", "editAssessment": "<PERSON><PERSON><PERSON><PERSON>", "deleteAssessment": "<PERSON><PERSON><PERSON><PERSON>", "confirmDelete": "<PERSON><PERSON><PERSON>i kana haquu akka barbaaddu mirkane<PERSON>adha?", "gradeReport": "Gabaasa Qabxee", "downloadReport": "Gabaasa <PERSON>", "printReport": "Gabaasa Maxxa<PERSON>i", "generateReport": "<PERSON><PERSON><PERSON>", "exportAsCSV": "Akka CSV Baasi", "exportAsPDF": "Akka PDF Baasi"}, "profileManagement": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "profileDetails": "<PERSON><PERSON><PERSON>", "personalInfo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "contactInfo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "professionalInfo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "role": "<PERSON><PERSON><PERSON>", "teacher": "<PERSON><PERSON><PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON><PERSON>", "phone": "<PERSON><PERSON><PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON>", "qualification": "<PERSON><PERSON><PERSON><PERSON>", "specialization": "<PERSON><PERSON><PERSON><PERSON>", "experience": "<PERSON><PERSON><PERSON><PERSON>", "experienceYears": "Waggaa {{years}}", "bio": "<PERSON><PERSON><PERSON>", "editProfile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "changePhoto": "<PERSON><PERSON><PERSON>", "uploadPhoto": "<PERSON><PERSON><PERSON>", "takePhoto": "<PERSON><PERSON><PERSON>", "chooseFromGallery": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON>", "save": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "saving": "<PERSON><PERSON><PERSON><PERSON>aa jira...", "profileUpdated": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ha<PERSON>", "updateError": "<PERSON><PERSON><PERSON><PERSON><PERSON> dog<PERSON>a", "loadingProfile": "Profaayilii fe'aa jira...", "noProfileData": "Odeeffannoon profaayilii hin argamne", "createProfile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "personalDetails": "<PERSON><PERSON><PERSON>", "displayName": "<PERSON><PERSON><PERSON>", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "gender": "Saala", "male": "<PERSON><PERSON><PERSON>", "female": "<PERSON><PERSON>", "other": "<PERSON><PERSON>", "dateOfBirth": "<PERSON><PERSON><PERSON>", "nationality": "<PERSON><PERSON><PERSON><PERSON>", "streetAddress": "<PERSON><PERSON><PERSON>", "city": "<PERSON><PERSON><PERSON><PERSON>", "state": "Naannoo/Godinaa", "postalCode": "Ko<PERSON>ii Poostaa", "country": "B<PERSON>yya", "mobilePhone": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "alternatePhone": "<PERSON><PERSON><PERSON><PERSON>", "emergencyContact": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "emergencyPhone": "<PERSON><PERSON><PERSON><PERSON>", "degree": "Digiri<PERSON>", "institution": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "graduationYear": "<PERSON><PERSON>", "certifications": "<PERSON><PERSON><PERSON>", "skills": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "languages": "Afaanota", "yearsOfExperience": "<PERSON><PERSON><PERSON><PERSON>", "previousEmployment": "<PERSON><PERSON><PERSON>", "addEmployment": "<PERSON><PERSON><PERSON>", "position": "<PERSON><PERSON><PERSON>", "company": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "startDate": "<PERSON><PERSON><PERSON>", "endDate": "<PERSON><PERSON><PERSON>", "current": "<PERSON><PERSON>", "description": "<PERSON><PERSON>a", "bioPlaceholder": "Waa'ee kee, fals<PERSON>aa barsiisummaa fi fedhii kee ibsa gabaabaa barreessi...", "requiredField": "<PERSON><PERSON><PERSON> kun dirqama guutamuu qaba", "invalidEmail": "<PERSON><PERSON><PERSON> tee<PERSON>o iimeelii sirrii galchi", "invalidPhone": "Ma<PERSON><PERSON> lakkoofsa bilbilaa sirrii galchi", "photoUpdated": "<PERSON><PERSON><PERSON> pro<PERSON><PERSON><PERSON><PERSON>", "photoUpdateError": "<PERSON><PERSON><PERSON> profa<PERSON><PERSON>i ha<PERSON> dogoggora"}}, "notifications": {"title": "Beeksisaalee", "noNotifications": "<PERSON><PERSON><PERSON><PERSON><PERSON> hin jiran", "viewAll": "<PERSON><PERSON> il<PERSON>", "searchPlaceholder": "Beeksisaalee bar<PERSON>adi...", "all": "<PERSON><PERSON><PERSON><PERSON>", "unread": "Kan hin dub<PERSON><PERSON>min", "read": "Kan dub<PERSON><PERSON>me", "academic": "Barnootaa", "attendance": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "behavioral": "Amala", "events": "Gochaalee", "messages": "Erga<PERSON><PERSON>", "total": "<PERSON><PERSON><PERSON>", "checkBackLater": "<PERSON><PERSON><PERSON><PERSON><PERSON> ha<PERSON>a a<PERSON> bood<PERSON> ilaalaa", "markAsRead": "<PERSON><PERSON><PERSON> mallatt<PERSON>", "viewDetails": "<PERSON><PERSON><PERSON>"}}