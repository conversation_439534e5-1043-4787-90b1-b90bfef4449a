const { initializeApp } = require('firebase/app');
const { getAuth, createUserWithEmailAndPassword, signInWithEmailAndPassword, deleteUser } = require('firebase/auth');
const { getFirestore, doc, setDoc, deleteDoc, collection, query, where, getDocs } = require('firebase/firestore');

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyBHkGAhm80xBIECyaHbZ_DbeNzu3jnVuDg",
  authDomain: "schoolmn-16cbc.firebaseapp.com",
  projectId: "schoolmn-16cbc",
  storageBucket: "schoolmn-16cbc.appspot.com",
  messagingSenderId: "999485613068",
  appId: "1:999485613068:web:e9c0c3e0a4c7f4e4c8b8b8"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const auth = getAuth(app);
const db = getFirestore(app);

const recreateAdminUser = async () => {
  try {
    console.log('Starting admin user recreation process...');

    // Try to find existing admin user
    const usersRef = collection(db, 'users');
    const q = query(usersRef, where('email', '==', '<EMAIL>'));
    const querySnapshot = await getDocs(q);

    // Delete existing admin user if found
    if (!querySnapshot.empty) {
      console.log('Found existing admin user, deleting...');
      for (const docSnapshot of querySnapshot.docs) {
        await deleteDoc(doc(db, 'users', docSnapshot.id));
      }
      console.log('Deleted admin document from Firestore');
      
      try {
        // Try to sign in and delete the auth user
        const userCredential = await signInWithEmailAndPassword(auth, '<EMAIL>', '123qwe');
        await deleteUser(userCredential.user);
        console.log('Deleted admin auth user');
      } catch (error) {
        console.log('Could not delete auth user:', error.message);
      }
    }

    // Create new admin user
    console.log('Creating new admin user...');
    const userCredential = await createUserWithEmailAndPassword(auth, '<EMAIL>', '123qwe');
    console.log('Created auth user successfully');

    // Create admin document
    const userDocRef = doc(db, 'users', userCredential.user.uid);
    await setDoc(userDocRef, {
      uid: userCredential.user.uid,
      email: '<EMAIL>',
      firstName: 'Admin',
      lastName: 'User',
      role: 'admin',
      status: 'active',
      createdAt: new Date().toISOString(),
    });

    console.log('Created admin document successfully');
    console.log('Admin user recreated with UID:', userCredential.user.uid);
    process.exit(0);
  } catch (error) {
    console.error('Error recreating admin user:', error);
    process.exit(1);
  }
};

recreateAdminUser();
