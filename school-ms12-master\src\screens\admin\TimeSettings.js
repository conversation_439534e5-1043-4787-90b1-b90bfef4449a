import React, { useState, useEffect } from 'react';
import { View, ScrollView, StyleSheet } from 'react-native';
import { Card, Title, Text, Switch, Portal, Modal, TextInput } from 'react-native-paper';
import { db } from '../../config/firebase';
import { doc, getDoc, setDoc } from 'firebase/firestore';
import CustomButton from '../../components/common/CustomButton';

const TimeSettings = () => {
  const [settings, setSettings] = useState({
    timeZone: 'Africa/Addis_Ababa',
    weekStartDay: 'Monday',
    workingDays: {
      Monday: true,
      Tuesday: true,
      Wednesday: true,
      Thursday: true,
      Friday: true,
      Saturday: false,
      Sunday: false,
    },
    schoolHours: {
      start: '08:00',
      end: '16:00',
    },
    periodDuration: 45, // minutes
    breakDuration: 10, // minutes
    lunchBreakDuration: 45, // minutes
  });

  const [isEditing, setIsEditing] = useState(false);
  const [tempSettings, setTempSettings] = useState({});

  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    try {
      const settingsRef = doc(db, 'settings', 'timeSettings');
      const docSnap = await getDoc(settingsRef);
      
      if (docSnap.exists()) {
        setSettings(docSnap.data());
      } else {
        // If no settings exist, create default settings
        await setDoc(settingsRef, settings);
      }
    } catch (error) {
      console.error('Error fetching time settings:', error);
    }
  };

  const handleSave = async () => {
    try {
      const settingsRef = doc(db, 'settings', 'timeSettings');
      await setDoc(settingsRef, tempSettings);
      setSettings(tempSettings);
      setIsEditing(false);
    } catch (error) {
      console.error('Error saving time settings:', error);
    }
  };

  const weekDays = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];

  return (
    <ScrollView style={styles.container}>
      <Card style={styles.card}>
        <Card.Content>
          <View style={styles.headerContainer}>
            <Title>Time Settings</Title>
            <CustomButton
              mode="contained"
              onPress={() => {
                setTempSettings(settings);
                setIsEditing(true);
              }}
            >
              Edit Settings
            </CustomButton>
          </View>

          <View style={styles.section}>
            <Title style={styles.sectionTitle}>Time Zone</Title>
            <Text>{settings.timeZone}</Text>
          </View>

          <View style={styles.section}>
            <Title style={styles.sectionTitle}>Working Days</Title>
            {weekDays.map((day) => (
              <View key={day} style={styles.dayItem}>
                <Text>{day}</Text>
                <Switch
                  value={settings.workingDays[day]}
                  disabled={!isEditing}
                />
              </View>
            ))}
          </View>

          <View style={styles.section}>
            <Title style={styles.sectionTitle}>School Hours</Title>
            <Text>Start: {settings.schoolHours.start}</Text>
            <Text>End: {settings.schoolHours.end}</Text>
          </View>

          <View style={styles.section}>
            <Title style={styles.sectionTitle}>Period Settings</Title>
            <Text>Period Duration: {settings.periodDuration} minutes</Text>
            <Text>Break Duration: {settings.breakDuration} minutes</Text>
            <Text>Lunch Break Duration: {settings.lunchBreakDuration} minutes</Text>
          </View>
        </Card.Content>
      </Card>

      <Portal>
        <Modal
          visible={isEditing}
          onDismiss={() => setIsEditing(false)}
          contentContainerStyle={styles.modalContent}
        >
          <ScrollView>
            <Title>Edit Time Settings</Title>

            <TextInput
              label="Time Zone"
              value={tempSettings.timeZone}
              onChangeText={(text) => setTempSettings({ ...tempSettings, timeZone: text })}
              style={styles.input}
            />

            <Title style={styles.modalSectionTitle}>Working Days</Title>
            {weekDays.map((day) => (
              <View key={day} style={styles.dayItem}>
                <Text>{day}</Text>
                <Switch
                  value={tempSettings.workingDays?.[day]}
                  onValueChange={(value) => 
                    setTempSettings({
                      ...tempSettings,
                      workingDays: {
                        ...tempSettings.workingDays,
                        [day]: value,
                      },
                    })
                  }
                />
              </View>
            ))}

            <Title style={styles.modalSectionTitle}>School Hours</Title>
            <TextInput
              label="Start Time"
              value={tempSettings.schoolHours?.start}
              onChangeText={(text) => 
                setTempSettings({
                  ...tempSettings,
                  schoolHours: {
                    ...tempSettings.schoolHours,
                    start: text,
                  },
                })
              }
              style={styles.input}
            />
            <TextInput
              label="End Time"
              value={tempSettings.schoolHours?.end}
              onChangeText={(text) => 
                setTempSettings({
                  ...tempSettings,
                  schoolHours: {
                    ...tempSettings.schoolHours,
                    end: text,
                  },
                })
              }
              style={styles.input}
            />

            <Title style={styles.modalSectionTitle}>Period Settings</Title>
            <TextInput
              label="Period Duration (minutes)"
              value={String(tempSettings.periodDuration)}
              onChangeText={(text) => 
                setTempSettings({
                  ...tempSettings,
                  periodDuration: parseInt(text) || 0,
                })
              }
              keyboardType="numeric"
              style={styles.input}
            />
            <TextInput
              label="Break Duration (minutes)"
              value={String(tempSettings.breakDuration)}
              onChangeText={(text) => 
                setTempSettings({
                  ...tempSettings,
                  breakDuration: parseInt(text) || 0,
                })
              }
              keyboardType="numeric"
              style={styles.input}
            />
            <TextInput
              label="Lunch Break Duration (minutes)"
              value={String(tempSettings.lunchBreakDuration)}
              onChangeText={(text) => 
                setTempSettings({
                  ...tempSettings,
                  lunchBreakDuration: parseInt(text) || 0,
                })
              }
              keyboardType="numeric"
              style={styles.input}
            />

            <View style={styles.buttonContainer}>
              <CustomButton
                mode="outlined"
                onPress={() => setIsEditing(false)}
                style={[styles.button, styles.cancelButton]}
              >
                Cancel
              </CustomButton>
              <CustomButton
                mode="contained"
                onPress={handleSave}
                style={[styles.button, styles.saveButton]}
              >
                Save
              </CustomButton>
            </View>
          </ScrollView>
        </Modal>
      </Portal>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  card: {
    margin: 16,
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  section: {
    marginVertical: 16,
  },
  sectionTitle: {
    fontSize: 18,
    marginBottom: 8,
  },
  dayItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  modalContent: {
    backgroundColor: 'white',
    padding: 20,
    margin: 20,
    borderRadius: 8,
    maxHeight: '80%',
  },
  modalSectionTitle: {
    fontSize: 16,
    marginTop: 16,
    marginBottom: 8,
  },
  input: {
    marginBottom: 16,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 20,
    gap: 8,
  },
  button: {
    minWidth: 100,
  },
  cancelButton: {
    borderColor: '#666',
  },
  saveButton: {
    backgroundColor: '#2196F3',
  },
});

export default TimeSettings;
