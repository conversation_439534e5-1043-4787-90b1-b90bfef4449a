import React, { useState, useRef, useEffect } from 'react';
import { Animated } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import AdminSidebar from './AdminSidebar';
import SidebarBackdrop from './SidebarBackdrop';

/**
 * A wrapper component for AdminSidebar that provides consistent behavior
 * across all admin screens.
 * 
 * @param {Object} props - Component props
 * @param {string} props.activeItem - The currently active sidebar item
 * @param {Function} props.setActiveItem - Function to set the active sidebar item
 * @param {boolean} props.initiallyOpen - Whether the sidebar should be initially open
 * @returns {React.ReactElement} The AdminSidebarWrapper component
 */
const AdminSidebarWrapper = ({ 
  activeItem, 
  setActiveItem,
  initiallyOpen = false
}) => {
  const navigation = useNavigation();
  const [drawerOpen, setDrawerOpen] = useState(initiallyOpen);
  const drawerAnim = useRef(new Animated.Value(initiallyOpen ? 0 : -280)).current;
  const backdropFadeAnim = useRef(new Animated.Value(initiallyOpen ? 1 : 0)).current;

  // Toggle drawer function
  const toggleDrawer = () => {
    if (drawerOpen) {
      // Close drawer
      Animated.parallel([
        Animated.timing(drawerAnim, {
          toValue: -280,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(backdropFadeAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        })
      ]).start(() => setDrawerOpen(false));
    } else {
      // Open drawer
      setDrawerOpen(true);
      Animated.parallel([
        Animated.timing(drawerAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(backdropFadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        })
      ]).start();
    }
  };

  return (
    <>
      <AdminSidebar
        drawerAnim={drawerAnim}
        toggleDrawer={toggleDrawer}
        activeSidebarItem={activeItem}
        setActiveSidebarItem={setActiveItem}
        navigation={navigation}
        visible={drawerOpen}
      />
      
      <SidebarBackdrop
        visible={drawerOpen}
        onPress={toggleDrawer}
        fadeAnim={backdropFadeAnim}
      />
    </>
  );
};

export default AdminSidebarWrapper;
