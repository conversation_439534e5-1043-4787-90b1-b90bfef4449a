import { db } from '../config/firebase';
import {
  collection,
  query,
  where,
  getDocs,
  addDoc,
  doc,
  getDoc,
  updateDoc,
  Timestamp
} from 'firebase/firestore';
import * as Notifications from 'expo-notifications';

class ExamNotificationService {
  constructor() {
    this.db = db;
  }

  /**
   * Notify admins about a new exam schedule
   * @param {Object} notification - The notification object
   */
  async notifyAdmins(notification) {
    try {
      // Get all admin users
      const usersRef = collection(this.db, 'users');
      const q = query(usersRef, where('role', '==', 'admin'));
      const querySnapshot = await getDocs(q);

      if (querySnapshot.empty) return;

      // Send notification to each admin
      querySnapshot.forEach(async (userDoc) => {
        const userId = userDoc.id;
        await this.sendNotification(userId, notification);
      });

      console.log('Exam notification sent to admins');
    } catch (error) {
      console.error('Error notifying admins:', error);
    }
  }

  /**
   * Notify teachers about a new exam schedule
   * @param {Object} exam - The exam object
   */
  async notifyTeachers(exam) {
    try {
      // Get teachers for the subject and class
      const usersRef = collection(this.db, 'users');
      const q = query(
        usersRef,
        where('role', '==', 'teacher'),
        where('subjects', 'array-contains', exam.subjectId)
      );
      const querySnapshot = await getDocs(q);

      if (querySnapshot.empty) return;

      // Send notification to each teacher
      querySnapshot.forEach(async (userDoc) => {
        const userId = userDoc.id;
        const teacher = userDoc.data();

        await this.sendNotification(userId, {
          title: 'New Exam Scheduled',
          body: `${exam.title} for ${exam.className} - ${exam.section} has been scheduled on ${exam.date} at ${exam.startTime}`,
          data: {
            type: 'exam_schedule',
            examId: exam.id,
            subjectId: exam.subjectId,
            classId: exam.classId,
            section: exam.section,
            date: exam.date,
            startTime: exam.startTime,
            duration: exam.duration,
            room: exam.room,
            examType: exam.examType,
            title: exam.title,
            subjectName: exam.subjectName,
            className: exam.className,
            viewPath: 'TeacherExamSchedule'
          }
        });
      });

      console.log('Exam notification sent to teachers');
    } catch (error) {
      console.error('Error notifying teachers:', error);
    }
  }

  /**
   * Notify students about a new exam schedule
   * @param {Object} exam - The exam object
   */
  async notifyStudents(exam) {
    try {
      // Get students for the class and section
      const usersRef = collection(this.db, 'users');
      const q = query(
        usersRef,
        where('role', '==', 'student'),
        where('classId', '==', exam.classId),
        where('section', '==', exam.section)
      );
      const querySnapshot = await getDocs(q);

      if (querySnapshot.empty) return;

      // Send notification to each student
      querySnapshot.forEach(async (userDoc) => {
        const userId = userDoc.id;
        const student = userDoc.data();

        await this.sendNotification(userId, {
          title: 'New Exam Scheduled',
          body: `${exam.title} for ${exam.subjectName} has been scheduled on ${exam.date} at ${exam.startTime}`,
          data: {
            type: 'exam_schedule',
            examId: exam.id,
            subjectId: exam.subjectId,
            classId: exam.classId,
            section: exam.section,
            date: exam.date,
            startTime: exam.startTime,
            duration: exam.duration,
            room: exam.room,
            examType: exam.examType,
            title: exam.title,
            subjectName: exam.subjectName,
            className: exam.className,
            totalMarks: exam.totalMarks,
            passingMarks: exam.passingMarks,
            instructions: exam.instructions,
            viewPath: 'StudentExamSchedule'
          }
        });
      });

      console.log('Exam notification sent to students');
    } catch (error) {
      console.error('Error notifying students:', error);
    }
  }

  /**
   * Notify parents about a new exam schedule
   * @param {Object} exam - The exam object
   */
  async notifyParents(exam) {
    try {
      // Get students for the class and section
      const usersRef = collection(this.db, 'users');
      const studentsQuery = query(
        usersRef,
        where('role', '==', 'student'),
        where('classId', '==', exam.classId),
        where('section', '==', exam.section)
      );
      const studentsSnapshot = await getDocs(studentsQuery);

      if (studentsSnapshot.empty) return;

      // Get parent IDs for each student
      const parentIds = [];
      studentsSnapshot.forEach((studentDoc) => {
        const student = studentDoc.data();
        if (student.parent && student.parent.id) {
          parentIds.push(student.parent.id);
        }
      });

      if (parentIds.length === 0) return;

      // Get parent users
      const parentsQuery = query(
        usersRef,
        where('role', '==', 'parent'),
        where('__name__', 'in', parentIds)
      );
      const parentsSnapshot = await getDocs(parentsQuery);

      if (parentsSnapshot.empty) return;

      // Send notification to each parent
      parentsSnapshot.forEach(async (userDoc) => {
        const userId = userDoc.id;
        const parent = userDoc.data();

        // Get children of this parent
        const childrenQuery = query(
          usersRef,
          where('role', '==', 'student'),
          where('parent.id', '==', userId),
          where('classId', '==', exam.classId),
          where('section', '==', exam.section)
        );
        const childrenSnapshot = await getDocs(childrenQuery);

        if (childrenSnapshot.empty) return;

        // Get child names for notification
        const childNames = [];
        const childIds = [];
        childrenSnapshot.forEach((childDoc) => {
          const child = childDoc.data();
          childNames.push(`${child.firstName} ${child.lastName}`);
          childIds.push(childDoc.id);
        });

        const childrenText = childNames.join(', ');

        await this.sendNotification(userId, {
          title: 'New Exam Scheduled',
          body: `${exam.title} for ${childrenText} has been scheduled on ${exam.date} at ${exam.startTime}`,
          data: {
            type: 'exam_schedule',
            examId: exam.id,
            subjectId: exam.subjectId,
            classId: exam.classId,
            section: exam.section,
            date: exam.date,
            startTime: exam.startTime,
            duration: exam.duration,
            room: exam.room,
            examType: exam.examType,
            title: exam.title,
            subjectName: exam.subjectName,
            className: exam.className,
            totalMarks: exam.totalMarks,
            passingMarks: exam.passingMarks,
            instructions: exam.instructions,
            childIds: childIds,
            childNames: childNames,
            viewPath: 'ParentExamSchedule'
          }
        });
      });

      console.log('Exam notification sent to parents');
    } catch (error) {
      console.error('Error notifying parents:', error);
    }
  }

  /**
   * Notify all relevant users about a new exam schedule
   * @param {Object} exam - The exam object
   */
  async notifyAllUsers(exam) {
    try {
      // Notify teachers
      await this.notifyTeachers(exam);

      // Notify students
      await this.notifyStudents(exam);

      // Notify parents
      await this.notifyParents(exam);

      console.log('Exam notifications sent to all relevant users');

      // Update exam as published
      const examRef = doc(this.db, 'exams', exam.id);
      await updateDoc(examRef, {
        published: true,
        publishedAt: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error notifying users about exam:', error);
    }
  }

  /**
   * Notify all relevant users about multiple exams
   * @param {Array} exams - Array of exam objects
   */
  async notifyAllUsersForExams(exams) {
    try {
      for (const exam of exams) {
        await this.notifyAllUsers(exam);
      }
      console.log(`Notifications sent for ${exams.length} exams`);
    } catch (error) {
      console.error('Error notifying users about exams:', error);
    }
  }

  /**
   * Send a notification to a specific user
   * @param {string} userId - The user ID
   * @param {Object} notification - The notification object
   */
  async sendNotification(userId, notification) {
    try {
      console.log(`Attempting to send notification to user ${userId}`, notification);

      // Add notification to database
      const notificationRef = collection(this.db, 'notifications');
      const now = new Date().toISOString();
      const notificationData = {
        userId,
        title: notification.title,
        body: notification.body,
        data: notification.data || {},
        type: notification.data?.type || 'exam',
        read: false,
        status: 'unread',
        createdAt: now,
        timestamp: now
      };

      console.log('Saving notification to database:', notificationData);
      const docRef = await addDoc(notificationRef, notificationData);
      console.log('Notification saved with ID:', docRef.id);

      // Get user's push token
      const userDoc = await getDoc(doc(this.db, 'users', userId));
      if (!userDoc.exists()) {
        console.log(`User ${userId} does not exist, skipping push notification`);
        return;
      }

      const user = userDoc.data();
      const expoPushToken = user.expoPushToken;

      if (!expoPushToken) {
        console.log(`User ${userId} does not have a push token, skipping push notification`);
        // Even without a push token, the notification is saved in the database
        // and will appear in the app's notification center
        return;
      }

      console.log(`Sending push notification to token: ${expoPushToken}`);

      // Send push notification
      await Notifications.scheduleNotificationAsync({
        content: {
          title: notification.title,
          body: notification.body,
          data: {
            ...notification.data,
            notificationId: docRef.id
          }
        },
        trigger: null // Send immediately
      });

      console.log(`Push notification sent to user ${userId}`);
      return docRef.id;
    } catch (error) {
      console.error('Error sending notification:', error);
      return null;
    }
  }
}

export default new ExamNotificationService();
