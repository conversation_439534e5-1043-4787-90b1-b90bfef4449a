import 'package:flutter/foundation.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/user_model.dart';
import '../services/firebase_auth_service.dart';
import '../utils/error_handler.dart';

class AuthContext extends ChangeNotifier {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuthService _authService = FirebaseAuthService();

  User? _user;
  UserModel? _userData;
  String? _userRole;
  bool _isLoading = false;
  String? _error;

  // Getters
  User? get user => _user;
  UserModel? get userData => _userData;
  String? get userRole => _userRole;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isAuthenticated => _user != null;

  AuthContext() {
    _initializeAuth();
  }

  void _initializeAuth() {
    _auth.authStateChanges().listen((User? user) {
      _user = user;
      if (user != null) {
        _fetchUserData();
      } else {
        _userData = null;
        _userRole = null;
      }
      notifyListeners();
    });
  }

  Future<void> _fetchUserData() async {
    if (_user == null) return;

    try {
      _setLoading(true);
      
      // Get user data from Firestore
      final userDoc = await _firestore.collection('users').doc(_user!.uid).get();
      
      if (userDoc.exists) {
        _userData = UserModel.fromMap(userDoc.data()!);
        _userRole = _userData!.role;
      }
      
      _setError(null);
    } catch (e) {
      _setError('Failed to fetch user data: $e');
      ErrorHandler.reportError(e, StackTrace.current);
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> signIn(String email, String password) async {
    try {
      _setLoading(true);
      _setError(null);

      final result = await _authService.signIn(email, password);
      
      if (result['success']) {
        return true;
      } else {
        _setError(result['error']);
        return false;
      }
    } catch (e) {
      _setError('Sign in failed: $e');
      ErrorHandler.reportError(e, StackTrace.current);
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> signUp({
    required String email,
    required String password,
    required String displayName,
    required String role,
    Map<String, dynamic>? additionalData,
  }) async {
    try {
      _setLoading(true);
      _setError(null);

      final result = await _authService.signUp(
        email: email,
        password: password,
        displayName: displayName,
        role: role,
        additionalData: additionalData,
      );

      if (result['success']) {
        return true;
      } else {
        _setError(result['error']);
        return false;
      }
    } catch (e) {
      _setError('Sign up failed: $e');
      ErrorHandler.reportError(e, StackTrace.current);
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<void> signOut() async {
    try {
      _setLoading(true);
      await _authService.signOut();
      _user = null;
      _userData = null;
      _userRole = null;
      _setError(null);
    } catch (e) {
      _setError('Sign out failed: $e');
      ErrorHandler.reportError(e, StackTrace.current);
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> resetPassword(String email) async {
    try {
      _setLoading(true);
      _setError(null);

      await _authService.resetPassword(email);
      return true;
    } catch (e) {
      _setError('Password reset failed: $e');
      ErrorHandler.reportError(e, StackTrace.current);
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> updateProfile({
    String? displayName,
    String? photoURL,
    Map<String, dynamic>? additionalData,
  }) async {
    try {
      _setLoading(true);
      _setError(null);

      final result = await _authService.updateProfile(
        displayName: displayName,
        photoURL: photoURL,
        additionalData: additionalData,
      );

      if (result['success']) {
        await _fetchUserData(); // Refresh user data
        return true;
      } else {
        _setError(result['error']);
        return false;
      }
    } catch (e) {
      _setError('Profile update failed: $e');
      ErrorHandler.reportError(e, StackTrace.current);
      return false;
    } finally {
      _setLoading(false);
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  void clearError() {
    _error = null;
    notifyListeners();
  }
}
