const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Colors for terminal output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

// Helper functions
function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function exec(command, options = {}) {
  try {
    log(`Running: ${command}`, colors.cyan);
    return execSync(command, {
      stdio: 'inherit',
      ...options,
    });
  } catch (error) {
    log(`Error executing command: ${command}`, colors.red);
    throw error;
  }
}

// Main build process
async function build() {
  try {
    // Step 1: Clean previous build files
    log('\n== CLEANING PREVIOUS BUILD FILES ==', colors.magenta);
    try {
      exec('cd android && gradlew.bat clean');
    } catch (error) {
      log('Warning: Clean failed, continuing anyway...', colors.yellow);
    }

    // Step 2: Make gradlew executable (already done in fix-gradlew-permissions.js)
    log('\n== PREPARING GRADLE WRAPPER ==', colors.magenta);
    try {
      require('./fix-gradlew-permissions');
    } catch (error) {
      log('Warning: Could not fix gradlew permissions, continuing anyway...', colors.yellow);
    }

    // Step 3: Build debug APK first to test build process
    log('\n== BUILDING DEBUG APK ==', colors.magenta);
    exec('cd android && gradlew.bat assembleDebug');

    // Check if debug build succeeded
    const debugApkPath = path.join(__dirname, 'android', 'app', 'build', 'outputs', 'apk', 'debug', 'app-debug.apk');
    if (fs.existsSync(debugApkPath)) {
      log('Debug APK built successfully!', colors.green);
    } else {
      throw new Error('Debug APK was not created');
    }

    // Step 4: Build release APK
    log('\n== BUILDING RELEASE APK ==', colors.magenta);
    exec('cd android && gradlew.bat assembleRelease');

    // Check if release build succeeded
    const releaseApkPath = path.join(__dirname, 'android', 'app', 'build', 'outputs', 'apk', 'release', 'app-release.apk');
    if (fs.existsSync(releaseApkPath)) {
      log('Release APK built successfully!', colors.green);
      
      // Copy APK to project root for easy access
      const destPath = path.join(__dirname, 'app-release.apk');
      fs.copyFileSync(releaseApkPath, destPath);
      log(`APK copied to: ${destPath}`, colors.green);
      
      log('\n== BUILD COMPLETED SUCCESSFULLY ==', colors.green);
      log(`You can find your release APK at:\n- ${releaseApkPath}\n- ${destPath}`, colors.green);
    } else {
      throw new Error('Release APK was not created');
    }
  } catch (error) {
    log(`\n== BUILD FAILED ==\n${error.message || error}`, colors.red);
    process.exit(1);
  }
}

// Run the build process
build(); 