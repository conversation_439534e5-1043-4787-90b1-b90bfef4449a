import React, { useState, useEffect, useRef } from 'react';
import { View, ScrollView, StyleSheet, Alert, Animated, Text } from 'react-native';
import {
  TextInput,
  Card,
  Title,
  Paragraph,
  Button,
  Snackbar,
  ActivityIndicator,
  HelperText,
  Divider,
  Avatar,
  Chip,
  useTheme,
  Surface,
  IconButton,
} from 'react-native-paper';
import { db, auth } from '../../config/firebase';
import { collection, doc, setDoc, getDoc, updateDoc, addDoc, serverTimestamp, writeBatch } from 'firebase/firestore';
import {
  createUserWithEmailAndPassword,
  updateProfile,
  sendEmailVerification
} from 'firebase/auth';
import CustomButton from '../../components/common/CustomButton';
import { useLanguage } from '../../context/LanguageContext';
import { useNotifications } from '../../context/NotificationContext';
import { LinearGradient } from 'expo-linear-gradient';
import * as Animatable from 'react-native-animatable';
import AdminAppHeader from '../../components/common/AdminAppHeader';
import AdminSidebar from '../../components/common/AdminSidebar';
import SidebarBackdrop from '../../components/common/SidebarBackdrop';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import VerificationStatus from '../../components/common/VerificationStatus';
import UserRegistrationService from '../../services/UserRegistrationService';
import EmailVerificationService from '../../services/EmailVerificationService';
import EmailNotificationService from '../../services/EmailNotificationService';

const AdminManagement = ({ route, navigation }) => {
  // No theme needed
  const { translate, getTextStyle, isRTL } = useLanguage();
  const { showToast } = useNotifications();
  const [loading, setLoading] = useState(false);
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [isEditing, setIsEditing] = useState(false);
  const [errors, setErrors] = useState({});
  const [drawerOpen, setDrawerOpen] = useState(false);
  const drawerAnim = useRef(new Animated.Value(-300)).current;
  const backdropFadeAnim = useRef(new Animated.Value(0)).current;
  const [activeSidebarItem, setActiveSidebarItem] = useState('AdminManagement');

  // Email verification states
  const [emailVerified, setEmailVerified] = useState(false);
  const [verificationSent, setVerificationSent] = useState(false);
  const [verificationLoading, setVerificationLoading] = useState(false);
  const [createdUser, setCreatedUser] = useState(null);

  // Form data state
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    password: '',
    confirmPassword: '',
    address: {
      street: '',
      city: '',
      state: '',
      zipCode: '',
      country: '',
    },
    role: 'admin',
    status: 'active',
    permissions: {
      manageUsers: true,
      manageTeachers: true,
      manageStudents: true,
      manageClasses: true,
      manageSubjects: true,
      viewReports: true,
      manageSettings: true,
    },
  });

  useEffect(() => {
    // If userId is provided in route params, we're editing an existing admin
    if (route.params?.userId) {
      setIsEditing(true);
      fetchAdminData(route.params.userId);
    }
  }, [route.params]);

  useEffect(() => {
    navigation.setOptions({
      headerShown: false,
    });
  }, [navigation]);

  const toggleDrawer = () => {
    if (drawerOpen) {
      // Close drawer
      Animated.parallel([
        Animated.timing(drawerAnim, {
          toValue: -300,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(backdropFadeAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        })
      ]).start(() => setDrawerOpen(false));
    } else {
      // Open drawer
      setDrawerOpen(true);
      Animated.parallel([
        Animated.timing(drawerAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(backdropFadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        })
      ]).start();
    }
  };

  const fetchAdminData = async (userId) => {
    try {
      setLoading(true);
      const userDoc = await getDoc(doc(db, 'users', userId));

      if (userDoc.exists()) {
        const userData = userDoc.data();
        // Don't include password fields when editing
        setFormData({
          ...userData,
          password: '',
          confirmPassword: '',
          // Ensure address object exists
          address: userData.address || {
            street: '',
            city: '',
            state: '',
            zipCode: '',
            country: '',
          },
          // Ensure permissions object exists
          permissions: userData.permissions || {
            manageUsers: true,
            manageTeachers: true,
            manageStudents: true,
            manageClasses: true,
            manageSubjects: true,
            viewReports: true,
            manageSettings: true,
          },
        });
      } else {
        showSnackbar(translate('adminManagement.userNotFound'));
        navigation.goBack();
      }
    } catch (error) {
      console.error('Error fetching admin data:', error);
      showSnackbar(translate('adminManagement.errorFetchingData'));
    } finally {
      setLoading(false);
    }
  };

  const validateForm = () => {
    const newErrors = {};

    // Validate required fields
    if (!formData.firstName.trim()) {
      newErrors.firstName = translate('validation.required');
    }

    if (!formData.lastName.trim()) {
      newErrors.lastName = translate('validation.required');
    }

    if (!formData.email.trim()) {
      newErrors.email = translate('validation.required');
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = translate('validation.invalidEmail');
    }

    if (!formData.phone.trim()) {
      newErrors.phone = translate('validation.required');
    }

    // Only validate password fields if creating a new admin
    if (!isEditing) {
      if (!formData.password) {
        newErrors.password = translate('validation.required');
      } else if (formData.password.length < 6) {
        newErrors.password = translate('validation.passwordTooShort');
      }

      if (!formData.confirmPassword) {
        newErrors.confirmPassword = translate('validation.required');
      } else if (formData.password !== formData.confirmPassword) {
        newErrors.confirmPassword = translate('validation.passwordsDoNotMatch');
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      showSnackbar(translate('validation.pleaseFixErrors'));
      return;
    }

    try {
      setLoading(true);

      if (isEditing) {
        // Update existing admin
        await updateAdmin();

        // Show success message with toast notification
        showToast(
          translate('adminManagement.success') || 'Success',
          translate('adminManagement.adminUpdated') || 'Admin updated successfully',
          'success',
          'UserManagement'
        );

        // Navigate back after a short delay to show the snackbar
        setTimeout(() => {
          navigation.goBack();
        }, 1500);
      } else {
        // Create new admin
        const newUser = await createAdmin();

        // Show toast notification
        if (newUser) {
          showToast(
            translate('adminManagement.success') || 'Success',
            translate('adminManagement.adminCreated') || 'Admin created successfully',
            'success',
            'UserManagement'
          );
        }

        // Don't navigate back yet, show verification status
        setLoading(false);
      }
    } catch (error) {
      console.error('Error saving admin:', error);
      showSnackbar(translate('adminManagement.errorSavingAdmin'));
      setLoading(false);
    }
  };

  const handleVerificationSent = () => {
    setVerificationSent(true);
  };

  const handleVerificationStatusChange = (status) => {
    setEmailVerified(status);

    // Update the user document if verified
    if (status && createdUser) {
      const userRef = doc(db, 'users', createdUser.uid);
      updateDoc(userRef, {
        emailVerified: true,
        updatedAt: serverTimestamp()
      }).catch(error => {
        console.error('Error updating email verification status:', error);
      });
    }
  };

  const handleFinish = () => {
    // Show success message with toast notification
    showToast(
      translate('adminManagement.success') || 'Success',
      translate('adminManagement.adminCreatedAndVerified') || 'Admin created and verified successfully.',
      'success',
      'UserManagement'
    );

    // Navigate back after a short delay
    setTimeout(() => {
      navigation.goBack();
    }, 1000);
  };

  const createAdmin = async () => {
    // Prepare user data for registration
    const userData = {
      firstName: formData.firstName,
      lastName: formData.lastName,
      displayName: `${formData.firstName} ${formData.lastName}`,
      phone: formData.phone || '',
      address: formData.address || {
        street: '',
        city: '',
        state: '',
        zipCode: '',
        country: '',
      },
      permissions: formData.permissions || {},
      status: 'active',
      emailVerified: false,
    };

    // Create auth user using the admin method to avoid signing out the current admin
    const result = await UserRegistrationService.adminCreateUser(
      formData.email,
      formData.password,
      'admin',
      userData
    );

    if (!result.success) {
      throw new Error(result.error || 'Failed to register admin');
    }

    // Store the created user for verification
    setCreatedUser(result.user);

    // Store the password in verification_credentials collection for later use
    try {
      await addDoc(collection(db, 'verification_credentials'), {
        userId: result.user.uid,
        email: formData.email,
        password: formData.password,
        role: 'admin',
        timestamp: serverTimestamp(),
        used: false
      });
    } catch (credentialsError) {
      console.error('Error storing credentials:', credentialsError);
      // Continue even if storing credentials fails
    }

    // For development mode, auto-verify the email
    setEmailVerified(true);

    // Update the user document to mark email as verified
    try {
      const userRef = doc(db, 'users', result.user.uid);
      await updateDoc(userRef, {
        emailVerified: true,
        updatedAt: serverTimestamp()
      });
      console.log('Admin user document updated with verified email status');
    } catch (updateError) {
      console.error('Error updating admin email verification status:', updateError);
    }

    // Send credentials email to the new admin
    try {
      await EmailNotificationService.sendCredentialsEmail(
        formData.email,
        formData.password,
        userData,
        'admin'
      );
      console.log(`Credentials email sent to admin (${formData.email})`);
    } catch (emailError) {
      console.error(`Error sending credentials email to admin:`, emailError);
      // Continue even if email sending fails
    }

    // Show success message
    showSnackbar(translate('adminManagement.adminCreated') || 'Admin created successfully. Login credentials have been sent to the admin.');

    // Set verification sent flag
    setVerificationSent(true);

    return result.user;
  };

  const updateAdmin = async () => {
    const userId = route.params.userId;

    // Update user document in Firestore
    const userData = {
      firstName: formData.firstName,
      lastName: formData.lastName,
      phone: formData.phone,
      address: formData.address,
      permissions: formData.permissions,
      updatedAt: serverTimestamp(),
    };

    // Create a batch to update both documents
    const batch = writeBatch(db);

    // Update user document in the users collection
    const userDocRef = doc(db, 'users', userId);
    batch.update(userDocRef, userData);

    // Check if admin-specific document exists and update it
    try {
      const adminDocRef = doc(db, 'admins', userId);
      const adminDoc = await getDoc(adminDocRef);

      if (adminDoc.exists()) {
        // Update the admin document
        batch.update(adminDocRef, userData);
      } else {
        // Create the admin document if it doesn't exist
        batch.set(adminDocRef, {
          ...userData,
          userId: userId,
          role: 'admin',
          email: formData.email,
          status: 'active',
          createdAt: serverTimestamp()
        });
      }
    } catch (error) {
      console.error('Error checking admin document:', error);
      // Continue with the user document update even if admin document check fails
    }

    // Commit the batch to update both documents
    await batch.commit();
  };

  const showSnackbar = (message) => {
    setSnackbarMessage(message);
    setSnackbarVisible(true);
  };

  const handleInputChange = (field, value) => {
    setFormData({
      ...formData,
      [field]: value,
    });

    // Clear error for this field if it exists
    if (errors[field]) {
      setErrors({
        ...errors,
        [field]: null,
      });
    }
  };

  const handleAddressChange = (field, value) => {
    setFormData({
      ...formData,
      address: {
        ...formData.address,
        [field]: value,
      },
    });
  };

  const handlePermissionChange = (permission) => {
    setFormData({
      ...formData,
      permissions: {
        ...formData.permissions,
        [permission]: !formData.permissions[permission],
      },
    });
  };

  if (loading && isEditing) {
    return (
      <View style={styles.container}>
        <AdminAppHeader
          title={translate('adminManagement.editAdmin')}
          showBack={true}
        />
        <View style={styles.loadingContainer}>
          <Animatable.View animation="pulse" iterationCount="infinite" duration={1500}>
            <ActivityIndicator size="large" color={'#1976d2'} />
          </Animatable.View>
          <Animatable.Text
            animation="fadeIn"
            duration={1000}
            style={[styles.loadingText, getTextStyle(), isRTL && styles.rtlText]}
          >
            {translate('common.loading')}
          </Animatable.Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Sidebar */}
      <AdminSidebar
        drawerAnim={drawerAnim}
        activeSidebarItem={activeSidebarItem}
        setActiveSidebarItem={setActiveSidebarItem}
        toggleDrawer={toggleDrawer}
      />

      {/* Backdrop */}
      <SidebarBackdrop
        visible={drawerOpen}
        onPress={toggleDrawer}
        fadeAnim={backdropFadeAnim}
      />

      {/* Admin App Header */}
      <AdminAppHeader
        title={isEditing ? translate('adminManagement.editAdmin') : translate('adminManagement.addAdmin')}
        showBack={true}
        onMenuPress={toggleDrawer}
      />

      <ScrollView style={styles.scrollContainer}>
        <Animatable.View
          animation="fadeInUp"
          duration={800}
          style={styles.content}
        >
          <Card style={styles.card}>
          <LinearGradient
            colors={['#1976d2', '#005cb2']}
            style={styles.cardHeader}
          >
            <View style={styles.cardHeaderContent}>
              <MaterialCommunityIcons
                name="shield-account"
                size={32}
                color="white"
                style={styles.headerIcon}
              />
              <Title style={[styles.cardTitle, { color: '#ffffff' }, getTextStyle(), isRTL && styles.rtlText]}>
                {isEditing
                  ? translate('adminManagement.editAdmin')
                  : translate('adminManagement.addAdmin')}
              </Title>
            </View>
            {isEditing && (
              <Animatable.View animation="pulse" iterationCount="infinite" iterationDelay={1500} duration={2000}>
                <Avatar.Text
                  size={60}
                  label={`${formData.firstName.charAt(0)}${formData.lastName.charAt(0)}`}
                  style={styles.avatar}
                  labelStyle={getTextStyle()}
                />
              </Animatable.View>
            )}
          </LinearGradient>

          <Card.Content style={styles.cardContent}>
            <View style={styles.formSection}>
              <View style={styles.sectionHeader}>
                <MaterialCommunityIcons name="account-details" size={24} color={'#1976d2'} />
                <Title style={[styles.sectionTitle, getTextStyle(), isRTL && styles.rtlText]}>
                  {translate('adminManagement.personalInfo')}
                </Title>
              </View>
              <Divider style={styles.sectionDivider} />

              <View style={styles.row}>
                <View style={styles.column}>
                  <TextInput
                    label={translate('adminManagement.firstName')}
                    value={formData.firstName}
                    onChangeText={(text) => handleInputChange('firstName', text)}
                    style={styles.input}
                    error={!!errors.firstName}
                    disabled={loading}
                  />
                  {errors.firstName && (
                    <HelperText type="error" visible={!!errors.firstName}>
                      {errors.firstName}
                    </HelperText>
                  )}
                </View>

                <View style={styles.column}>
                  <TextInput
                    label={translate('adminManagement.lastName')}
                    value={formData.lastName}
                    onChangeText={(text) => handleInputChange('lastName', text)}
                    style={styles.input}
                    error={!!errors.lastName}
                    disabled={loading}
                  />
                  {errors.lastName && (
                    <HelperText type="error" visible={!!errors.lastName}>
                      {errors.lastName}
                    </HelperText>
                  )}
                </View>
              </View>

              <TextInput
                label={translate('adminManagement.email')}
                value={formData.email}
                onChangeText={(text) => handleInputChange('email', text)}
                style={styles.input}
                keyboardType="email-address"
                error={!!errors.email}
                disabled={loading || isEditing} // Email can't be changed when editing
              />
              {errors.email && (
                <HelperText type="error" visible={!!errors.email}>
                  {errors.email}
                </HelperText>
              )}

              <TextInput
                label={translate('adminManagement.phone')}
                value={formData.phone}
                onChangeText={(text) => handleInputChange('phone', text)}
                style={styles.input}
                keyboardType="phone-pad"
                error={!!errors.phone}
                disabled={loading}
              />
              {errors.phone && (
                <HelperText type="error" visible={!!errors.phone}>
                  {errors.phone}
                </HelperText>
              )}
            </View>

            <Divider style={styles.divider} />

            <View style={styles.formSection}>
              <View style={styles.sectionHeader}>
                <MaterialCommunityIcons name="map-marker" size={24} color={'#1976d2'} />
                <Title style={[styles.sectionTitle, getTextStyle(), isRTL && styles.rtlText]}>
                  {translate('adminManagement.address')}
                </Title>
              </View>
              <Divider style={styles.sectionDivider} />

              <TextInput
                label={translate('adminManagement.street')}
                value={formData.address.street}
                onChangeText={(text) => handleAddressChange('street', text)}
                style={styles.input}
                disabled={loading}
              />

              <View style={styles.row}>
                <View style={styles.column}>
                  <TextInput
                    label={translate('adminManagement.city')}
                    value={formData.address.city}
                    onChangeText={(text) => handleAddressChange('city', text)}
                    style={styles.input}
                    disabled={loading}
                  />
                </View>

                <View style={styles.column}>
                  <TextInput
                    label={translate('adminManagement.state')}
                    value={formData.address.state}
                    onChangeText={(text) => handleAddressChange('state', text)}
                    style={styles.input}
                    disabled={loading}
                  />
                </View>
              </View>

              <View style={styles.row}>
                <View style={styles.column}>
                  <TextInput
                    label={translate('adminManagement.zipCode')}
                    value={formData.address.zipCode}
                    onChangeText={(text) => handleAddressChange('zipCode', text)}
                    style={styles.input}
                    disabled={loading}
                  />
                </View>

                <View style={styles.column}>
                  <TextInput
                    label={translate('adminManagement.country')}
                    value={formData.address.country}
                    onChangeText={(text) => handleAddressChange('country', text)}
                    style={styles.input}
                    disabled={loading}
                  />
                </View>
              </View>
            </View>

            {!isEditing && (
              <>
                <Divider style={styles.divider} />

                <View style={styles.formSection}>
                  <View style={styles.sectionHeader}>
                    <MaterialCommunityIcons name="lock" size={24} color={'#1976d2'} />
                    <Title style={[styles.sectionTitle, getTextStyle(), isRTL && styles.rtlText]}>
                      {translate('adminManagement.accountCredentials')}
                    </Title>
                  </View>
                  <Divider style={styles.sectionDivider} />

                  <TextInput
                    label={translate('adminManagement.password')}
                    value={formData.password}
                    onChangeText={(text) => handleInputChange('password', text)}
                    secureTextEntry
                    style={styles.input}
                    error={!!errors.password}
                    disabled={loading}
                  />
                  {errors.password && (
                    <HelperText type="error" visible={!!errors.password}>
                      {errors.password}
                    </HelperText>
                  )}

                  <TextInput
                    label={translate('adminManagement.confirmPassword')}
                    value={formData.confirmPassword}
                    onChangeText={(text) => handleInputChange('confirmPassword', text)}
                    secureTextEntry
                    style={styles.input}
                    error={!!errors.confirmPassword}
                    disabled={loading}
                  />
                  {errors.confirmPassword && (
                    <HelperText type="error" visible={!!errors.confirmPassword}>
                      {errors.confirmPassword}
                    </HelperText>
                  )}

                  {createdUser && (
                    <Surface style={styles.verificationContainer}>
                      <Title style={[styles.sectionTitle, getTextStyle()]}>
                        {translate('verification.title') || 'Email Verification'}
                      </Title>
                      <VerificationStatus
                        email={formData.email}
                        isVerified={emailVerified}
                        verificationSent={verificationSent}
                        onVerificationSent={handleVerificationSent}
                        onVerificationStatusChange={handleVerificationStatusChange}
                        userType="admin"
                        user={createdUser}
                      />

                      {verificationSent && !emailVerified && (
                        <Text style={[styles.verificationMessage, getTextStyle()]}>
                          {translate('verification.checkEmail') || 'Please check your email and verify your account before continuing.'}
                        </Text>
                      )}

                      {emailVerified && (
                        <Text style={[styles.verificationSuccess, getTextStyle()]}>
                          {translate('verification.success') || 'Email verified successfully!'}
                        </Text>
                      )}
                    </Surface>
                  )}
                </View>
              </>
            )}

            <Divider style={styles.divider} />

            <View style={styles.formSection}>
              <View style={styles.sectionHeader}>
                <MaterialCommunityIcons name="shield-key" size={24} color={'#1976d2'} />
                <Title style={[styles.sectionTitle, getTextStyle(), isRTL && styles.rtlText]}>
                  {translate('adminManagement.permissions')}
                </Title>
              </View>
              <Divider style={styles.sectionDivider} />
              <Paragraph style={[styles.permissionsDescription, getTextStyle(), isRTL && styles.rtlText]}>
                {translate('adminManagement.permissionsDescription')}
              </Paragraph>

              <View style={styles.permissionsContainer}>
                {Object.keys(formData.permissions).map((permission) => (
                  <Chip
                    key={permission}
                    selected={formData.permissions[permission]}
                    onPress={() => handlePermissionChange(permission)}
                    style={[
                      styles.permissionChip,
                      formData.permissions[permission] && styles.selectedPermissionChip,
                    ]}
                    textStyle={[
                      getTextStyle(),
                      formData.permissions[permission] && styles.selectedPermissionText,
                    ]}
                    disabled={loading}
                    mode="outlined"
                    selectedColor={'#1976d2'}
                  >
                    {translate(`adminManagement.${permission}`)}
                  </Chip>
                ))}
              </View>
            </View>
          </Card.Content>

          <Card.Actions style={styles.cardActions}>
            <Animatable.View animation="fadeIn" duration={1200} delay={500}>
              <CustomButton
                mode="outlined"
                onPress={() => navigation.goBack()}
                style={styles.button}
                labelStyle={getTextStyle()}
                disabled={loading}
                icon="close"
              >
                {translate('common.cancel')}
              </CustomButton>
            </Animatable.View>

            {!createdUser ? (
              // Show Save/Update button when no user has been created yet
              <Animatable.View animation="fadeIn" duration={1200} delay={700}>
                <CustomButton
                  mode="contained"
                  onPress={handleSubmit}
                  style={styles.button}
                  labelStyle={[getTextStyle(), { color: '#ffffff' }]}
                  loading={loading}
                  disabled={loading}
                  icon={isEditing ? "content-save-edit" : "content-save"}
                >
                  {isEditing
                    ? translate('common.update')
                    : translate('common.save')}
                </CustomButton>
              </Animatable.View>
            ) : (
              // Show Finish button when user has been created and verification is complete
              <Animatable.View animation="fadeIn" duration={1200} delay={700}>
                <CustomButton
                  mode="contained"
                  onPress={handleFinish}
                  style={styles.button}
                  labelStyle={[getTextStyle(), { color: '#ffffff' }]}
                  disabled={!emailVerified}
                  icon="check-circle"
                >
                  {translate('common.finish') || 'Finish'}
                </CustomButton>
              </Animatable.View>
            )}
          </Card.Actions>
        </Card>
      </Animatable.View>

      <Snackbar
        visible={snackbarVisible}
        onDismiss={() => setSnackbarVisible(false)}
        duration={3000}
        style={styles.snackbar}
        action={{
          label: translate('common.dismiss'),
          onPress: () => setSnackbarVisible(false),
        }}
      >
        {snackbarMessage}
      </Snackbar>
    </ScrollView>
  </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollContainer: {
    flex: 1,
  },
  content: {
    padding: 16,
  },
  card: {
    borderRadius: 12,
    overflow: 'hidden',
    elevation: 4,
  },
  cardHeader: {
    padding: 20,
    alignItems: 'center',
  },
  cardHeaderContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerIcon: {
    marginRight: 12,
  },
  cardTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 8,
  },
  avatar: {
    marginTop: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  cardContent: {
    paddingVertical: 16,
  },
  formSection: {
    marginBottom: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  sectionTitle: {
    fontSize: 18,
    marginLeft: 8,
  },
  sectionDivider: {
    marginBottom: 16,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  column: {
    flex: 1,
    marginHorizontal: 4,
  },
  input: {
    marginBottom: 8,
    backgroundColor: 'white',
  },
  divider: {
    marginVertical: 16,
  },
  permissionsDescription: {
    marginBottom: 16,
    fontSize: 14,
    color: '#666',
  },
  permissionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  permissionChip: {
    margin: 4,
    paddingHorizontal: 8,
  },
  selectedPermissionChip: {
    backgroundColor: '#e3f2fd',
  },
  selectedPermissionText: {
    color: '#2196F3',
  },
  cardActions: {
    justifyContent: 'flex-end',
    padding: 16,
  },
  button: {
    marginLeft: 8,
  },
  snackbar: {
    marginBottom: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#2196F3', // Primary color
  },
  rtlText: {
    textAlign: 'right',
  },
  verificationContainer: {
    marginTop: 16,
    padding: 16,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: '#2196F3',
  },
  verificationMessage: {
    marginTop: 8,
    fontSize: 14,
    color: '#666',
    fontStyle: 'italic',
  },
  verificationSuccess: {
    marginTop: 8,
    fontSize: 14,
    color: '#4CAF50',
    fontWeight: 'bold',
  },
});

export default AdminManagement;
