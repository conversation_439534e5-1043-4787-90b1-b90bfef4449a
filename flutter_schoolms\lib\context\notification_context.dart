import 'package:flutter/foundation.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/notification_model.dart';
import '../services/notification_service.dart';
import '../utils/error_handler.dart';

class NotificationContext extends ChangeNotifier {
  final NotificationService _notificationService = NotificationService();
  final FlutterLocalNotificationsPlugin _localNotifications = FlutterLocalNotificationsPlugin();
  
  List<NotificationModel> _notifications = [];
  bool _isLoading = false;
  String? _error;
  bool _notificationsEnabled = true;
  String? _fcmToken;

  // Getters
  List<NotificationModel> get notifications => _notifications;
  List<NotificationModel> get unreadNotifications => 
      _notifications.where((n) => !n.isRead).toList();
  int get unreadCount => unreadNotifications.length;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get notificationsEnabled => _notificationsEnabled;
  String? get fcmToken => _fcmToken;

  NotificationContext() {
    _initializeNotifications();
  }

  Future<void> _initializeNotifications() async {
    try {
      _setLoading(true);
      
      // Initialize local notifications
      await _initializeLocalNotifications();
      
      // Initialize Firebase messaging
      await _initializeFirebaseMessaging();
      
      // Load notification preferences
      await _loadNotificationPreferences();
      
      // Load existing notifications
      await loadNotifications();
      
    } catch (e) {
      _setError('Failed to initialize notifications: $e');
      ErrorHandler.reportError(e, StackTrace.current);
    } finally {
      _setLoading(false);
    }
  }

  Future<void> _initializeLocalNotifications() async {
    const androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );
    
    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _localNotifications.initialize(
      initSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );
  }

  Future<void> _initializeFirebaseMessaging() async {
    // Request permission for iOS
    final messaging = FirebaseMessaging.instance;
    
    final settings = await messaging.requestPermission(
      alert: true,
      badge: true,
      sound: true,
      provisional: false,
    );

    if (settings.authorizationStatus == AuthorizationStatus.authorized) {
      debugPrint('User granted permission');
      
      // Get FCM token
      _fcmToken = await messaging.getToken();
      debugPrint('FCM Token: $_fcmToken');
      
      // Listen to token refresh
      messaging.onTokenRefresh.listen((token) {
        _fcmToken = token;
        notifyListeners();
      });
      
      // Handle foreground messages
      FirebaseMessaging.onMessage.listen(_handleForegroundMessage);
      
      // Handle background messages
      FirebaseMessaging.onMessageOpenedApp.listen(_handleBackgroundMessage);
      
    } else {
      debugPrint('User declined or has not accepted permission');
    }
  }

  Future<void> _loadNotificationPreferences() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _notificationsEnabled = prefs.getBool('notifications_enabled') ?? true;
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading notification preferences: $e');
    }
  }

  Future<void> loadNotifications() async {
    try {
      _setLoading(true);
      _setError(null);
      
      final notifications = await _notificationService.getUserNotifications();
      _notifications = notifications;
      
    } catch (e) {
      _setError('Failed to load notifications: $e');
      ErrorHandler.reportError(e, StackTrace.current);
    } finally {
      _setLoading(false);
    }
  }

  Future<void> markAsRead(String notificationId) async {
    try {
      await _notificationService.markAsRead(notificationId);
      
      final index = _notifications.indexWhere((n) => n.id == notificationId);
      if (index != -1) {
        _notifications[index] = _notifications[index].copyWith(isRead: true);
        notifyListeners();
      }
    } catch (e) {
      _setError('Failed to mark notification as read: $e');
      ErrorHandler.reportError(e, StackTrace.current);
    }
  }

  Future<void> markAllAsRead() async {
    try {
      await _notificationService.markAllAsRead();
      
      _notifications = _notifications.map((n) => n.copyWith(isRead: true)).toList();
      notifyListeners();
    } catch (e) {
      _setError('Failed to mark all notifications as read: $e');
      ErrorHandler.reportError(e, StackTrace.current);
    }
  }

  Future<void> deleteNotification(String notificationId) async {
    try {
      await _notificationService.deleteNotification(notificationId);
      
      _notifications.removeWhere((n) => n.id == notificationId);
      notifyListeners();
    } catch (e) {
      _setError('Failed to delete notification: $e');
      ErrorHandler.reportError(e, StackTrace.current);
    }
  }

  Future<void> sendNotification({
    required String title,
    required String body,
    required String recipientId,
    String? type,
    Map<String, dynamic>? data,
  }) async {
    try {
      await _notificationService.sendNotification(
        title: title,
        body: body,
        recipientId: recipientId,
        type: type,
        data: data,
      );
    } catch (e) {
      _setError('Failed to send notification: $e');
      ErrorHandler.reportError(e, StackTrace.current);
    }
  }

  Future<void> toggleNotifications(bool enabled) async {
    try {
      _notificationsEnabled = enabled;
      
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('notifications_enabled', enabled);
      
      notifyListeners();
    } catch (e) {
      _setError('Failed to update notification settings: $e');
      ErrorHandler.reportError(e, StackTrace.current);
    }
  }

  void _handleForegroundMessage(RemoteMessage message) {
    if (!_notificationsEnabled) return;
    
    _showLocalNotification(
      title: message.notification?.title ?? 'New Notification',
      body: message.notification?.body ?? '',
      data: message.data,
    );
    
    // Add to local notifications list
    final notification = NotificationModel.fromFirebaseMessage(message);
    _notifications.insert(0, notification);
    notifyListeners();
  }

  void _handleBackgroundMessage(RemoteMessage message) {
    // Handle notification tap when app is in background
    debugPrint('Background message: ${message.messageId}');
  }

  Future<void> _showLocalNotification({
    required String title,
    required String body,
    Map<String, dynamic>? data,
  }) async {
    const androidDetails = AndroidNotificationDetails(
      'school_management_channel',
      'School Management Notifications',
      channelDescription: 'Notifications for school management system',
      importance: Importance.high,
      priority: Priority.high,
    );
    
    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );
    
    const details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _localNotifications.show(
      DateTime.now().millisecondsSinceEpoch.remainder(100000),
      title,
      body,
      details,
      payload: data?.toString(),
    );
  }

  void _onNotificationTapped(NotificationResponse response) {
    // Handle notification tap
    debugPrint('Notification tapped: ${response.payload}');
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  void clearError() {
    _error = null;
    notifyListeners();
  }
}
