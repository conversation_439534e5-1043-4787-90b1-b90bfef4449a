import React, { useState } from 'react';
import { StyleSheet, StatusBar } from 'react-native';
// Theme import removed
import { useAuth } from '../../context/AuthContext';
import { useLanguage } from '../../context/LanguageContext';
import MessagingComponent from '../../components/messaging/MessagingComponent';
import ParentAppHeader from '../../components/common/ParentAppHeader';
import ParentSidebar from '../../components/common/ParentSidebar';

const ParentMessaging = ({ navigation }) => {
  // No theme needed
  const { user } = useAuth();
  const { translate } = useLanguage();
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [activeSidebarItem, setActiveSidebarItem] = useState('messaging');

  const toggleDrawer = () => {
    setDrawerOpen(!drawerOpen);
  };

  // Custom header for parent dashboard
  const customHeader = (
    <ParentAppHeader
      title={translate('communication.parentTitle') || 'Parent Communication Center'}
      onMenuPress={toggleDrawer}
    />
  );

  // Custom sidebar for parent dashboard
  const customSidebar = (
    <ParentSidebar
      visible={drawerOpen}
      onClose={toggleDrawer}
      navigation={navigation}
      activeSidebarItem={activeSidebarItem}
      setActiveSidebarItem={setActiveSidebarItem}
    />
  );

  return (
    <MessagingComponent
      customHeader={customHeader}
      customSidebar={customSidebar}
      userRole="parent"
    />
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

export default ParentMessaging;
