/**
 * This file provides compatibility for MUI components in React Native
 * It's a temporary solution to allow the app to run on mobile platforms
 * while we transition away from MUI (which is designed for web)
 */

// Mock styled function from MUI
export const styled = (Component) => {
  return (props) => {
    // Return a function that renders the component with the props
    return (styleProps) => {
      // Merge the props and styleProps
      return Component({ ...props, ...styleProps });
    };
  };
};

// Mock ThemeProvider from MUI
export const ThemeProvider = ({ children }) => {
  return children;
};

// Mock createTheme from MUI
export const createTheme = (themeOptions) => {
  return themeOptions;
};

// Export a default theme object
export const defaultTheme = {
  spacing: (factor) => factor * 8,
  palette: {
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#9c27b0',
    },
    error: {
      main: '#f44336',
    },
    warning: {
      main: '#ff9800',
    },
    info: {
      main: '#2196f3',
    },
    success: {
      main: '#4caf50',
    },
    common: {
      white: '#ffffff',
      black: '#000000',
    },
  },
};
