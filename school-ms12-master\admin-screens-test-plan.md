# Admin Screens Test Plan

This document outlines a systematic approach to test all admin screens in the School Management System application.

## 1. Admin Dashboard

- [ ] Verify the dashboard loads correctly
- [ ] Check if all menu items are displayed properly
- [ ] Verify quick stats (students, teachers, classes, active users) show correct data
- [ ] Test sidebar navigation
- [ ] Test search functionality
- [ ] Test FAB (Floating Action Button) menu

## 2. Academic Management Screens

### 2.1 Academic Calendar
- [ ] Verify calendar loads correctly
- [ ] Test adding new terms
- [ ] Test adding holidays
- [ ] Verify Ethiopian date picker works correctly
- [ ] Test date range selection

### 2.2 Academic Year Management
- [ ] Verify academic years list loads correctly
- [ ] Test creating new academic year
- [ ] Test editing existing academic year
- [ ] Test activating/deactivating academic year

### 2.3 Semester Management
- [ ] Verify semesters list loads correctly
- [ ] Test creating new semester
- [ ] Test editing existing semester
- [ ] Test date range selection with Ethiopian calendar

### 2.4 Course Management
- [ ] Verify courses list loads correctly
- [ ] Test adding new course
- [ ] Test editing existing course
- [ ] Test assigning teachers to courses

### 2.5 Subject Management
- [ ] Verify subjects list loads correctly
- [ ] Test adding new subject
- [ ] Test editing existing subject
- [ ] Test assigning subjects to classes

### 2.6 Grade Settings
- [ ] Verify grade scales list loads correctly
- [ ] Test adding new grade scale
- [ ] Test editing existing grade scale
- [ ] Test grade calculation rules

## 3. User Management Screens

### 3.1 Student Management
- [ ] Verify students list loads correctly
- [ ] Test student search functionality
- [ ] Test adding new student
- [ ] Test editing student details
- [ ] Test viewing student profile
- [ ] Test assigning student to class/section

### 3.2 Teacher Management
- [ ] Verify teachers list loads correctly
- [ ] Test teacher search functionality
- [ ] Test adding new teacher
- [ ] Test editing teacher details
- [ ] Test assigning subjects to teachers
- [ ] Test teacher permissions

### 3.3 Parent Management
- [ ] Verify parents list loads correctly
- [ ] Test parent search functionality
- [ ] Test adding new parent
- [ ] Test editing parent details
- [ ] Test linking parents to students

### 3.4 User Management
- [ ] Verify users list loads correctly
- [ ] Test user search functionality
- [ ] Test adding new user
- [ ] Test editing user details
- [ ] Test user role assignment
- [ ] Test user activation/deactivation

### 3.5 Admin Management
- [ ] Verify admins list loads correctly
- [ ] Test adding new admin
- [ ] Test editing admin details
- [ ] Test admin permissions

## 4. Class and Exam Management Screens

### 4.1 Class Management
- [ ] Verify classes list loads correctly
- [ ] Test adding new class
- [ ] Test editing class details
- [ ] Test assigning teachers to classes
- [ ] Test class section management

### 4.2 Exam Management
- [ ] Verify exams list loads correctly
- [ ] Test creating new exam
- [ ] Test editing exam details
- [ ] Test exam scheduling with Ethiopian calendar
- [ ] Test assigning subjects to exams

### 4.3 Exam Routine Manager
- [ ] Verify exam routines load correctly
- [ ] Test creating new exam routine
- [ ] Test editing exam routine
- [ ] Test time selection with Ethiopian time picker

### 4.4 Results Management
- [ ] Verify results list loads correctly
- [ ] Test entering new results
- [ ] Test editing existing results
- [ ] Test result calculation
- [ ] Test result publishing

### 4.5 Result Approval
- [ ] Verify pending approvals list loads correctly
- [ ] Test approving results
- [ ] Test rejecting results
- [ ] Test adding comments to results

## 5. Settings and Reports Screens

### 5.1 School Settings
- [ ] Verify school settings load correctly
- [ ] Test updating school information
- [ ] Test logo upload
- [ ] Test contact information update

### 5.2 System Reports
- [ ] Verify reports dashboard loads correctly
- [ ] Test generating different types of reports
- [ ] Test exporting reports
- [ ] Test date range selection with Ethiopian calendar

### 5.3 Library Management
- [ ] Verify books list loads correctly
- [ ] Test adding new book
- [ ] Test editing book details
- [ ] Test book checkout/return

### 5.4 Time Settings
- [ ] Verify time settings load correctly
- [ ] Test updating time format
- [ ] Test updating academic periods
- [ ] Test Ethiopian time format settings

## 6. Ethiopian Calendar Implementation

- [ ] Verify EthiopianDatePicker works correctly in all screens
- [ ] Verify EthiopianTimePicker works correctly in all screens
- [ ] Check date formatting and display in Ethiopian calendar
- [ ] Test date range selection
- [ ] Test date validation
- [ ] Test different display modes (calendar, spinner, compact)

## 7. Error Handling

- [ ] Test network error handling
- [ ] Test form validation errors
- [ ] Test database operation errors
- [ ] Verify error messages are displayed correctly
- [ ] Test retry mechanisms

## 8. Performance and UI

- [ ] Check loading states and indicators
- [ ] Test responsiveness on different screen sizes
- [ ] Check animations and transitions
- [ ] Verify consistent styling across all screens
- [ ] Test multilingual support (Amharic, Oromo, English)

## Notes on Testing Approach

1. Start with the Admin Dashboard and test navigation to all screens
2. For each screen, test:
   - Initial loading
   - Data retrieval from Firebase
   - Form inputs and validation
   - CRUD operations
   - Error handling
   - Ethiopian calendar integration
3. Document any issues found with screenshots and detailed steps to reproduce
4. Verify all screens work with all supported languages
