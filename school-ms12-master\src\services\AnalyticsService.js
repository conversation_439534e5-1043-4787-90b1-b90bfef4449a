import { db } from '../config/firebase';
import { collection, addDoc, query, where, getDocs, Timestamp } from 'firebase/firestore';
import { MonitoringService } from './MonitoringService';

class AnalyticsService {
    static instance = null;

    constructor() {
        if (AnalyticsService.instance) {
            return AnalyticsService.instance;
        }
        AnalyticsService.instance = this;
        
        this.eventCollection = collection(db, 'analytics_events');
        this.monitoringService = new MonitoringService();
    }

    // Track general events
    async logEvent(eventName, eventData = {}) {
        try {
            const event = {
                name: eventName,
                data: eventData,
                timestamp: Timestamp.now(),
                userId: this.getCurrentUserId()
            };

            await addDoc(this.eventCollection, event);
            this.monitoringService.trackEvent(eventName);
        } catch (error) {
            console.error('Error logging event:', error);
            this.monitoringService.logError('analytics_event_logging', error);
        }
    }

    // Track resource usage
    async trackResourceUsage(resourceType, action, metadata = {}) {
        try {
            const event = {
                type: 'resource_usage',
                resourceType,
                action,
                metadata,
                timestamp: Timestamp.now(),
                userId: this.getCurrentUserId()
            };

            await addDoc(this.eventCollection, event);
            this.monitoringService.trackResourceUsage(resourceType, action);
        } catch (error) {
            console.error('Error tracking resource usage:', error);
            this.monitoringService.logError('resource_tracking', error);
        }
    }

    // Track report generation
    async trackReportGeneration(reportType, format, metadata = {}) {
        try {
            const event = {
                type: 'report_generation',
                reportType,
                format,
                metadata,
                timestamp: Timestamp.now(),
                userId: this.getCurrentUserId()
            };

            await addDoc(this.eventCollection, event);
            this.monitoringService.trackReportGeneration(reportType);
        } catch (error) {
            console.error('Error tracking report generation:', error);
            this.monitoringService.logError('report_tracking', error);
        }
    }

    // Track user interactions
    async trackUserInteraction(component, action, metadata = {}) {
        try {
            const event = {
                type: 'user_interaction',
                component,
                action,
                metadata,
                timestamp: Timestamp.now(),
                userId: this.getCurrentUserId()
            };

            await addDoc(this.eventCollection, event);
            this.monitoringService.trackUserInteraction(component);
        } catch (error) {
            console.error('Error tracking user interaction:', error);
            this.monitoringService.logError('interaction_tracking', error);
        }
    }

    // Get analytics data for a specific event type
    async getAnalytics(eventType, startDate, endDate) {
        try {
            const q = query(
                this.eventCollection,
                where('type', '==', eventType),
                where('timestamp', '>=', startDate),
                where('timestamp', '<=', endDate)
            );

            const querySnapshot = await getDocs(q);
            return querySnapshot.docs.map(doc => ({
                id: doc.id,
                ...doc.data()
            }));
        } catch (error) {
            console.error('Error fetching analytics:', error);
            this.monitoringService.logError('analytics_fetch', error);
            return [];
        }
    }

    // Get resource usage analytics
    async getResourceAnalytics(resourceType, startDate, endDate) {
        try {
            const q = query(
                this.eventCollection,
                where('type', '==', 'resource_usage'),
                where('resourceType', '==', resourceType),
                where('timestamp', '>=', startDate),
                where('timestamp', '<=', endDate)
            );

            const querySnapshot = await getDocs(q);
            return querySnapshot.docs.map(doc => ({
                id: doc.id,
                ...doc.data()
            }));
        } catch (error) {
            console.error('Error fetching resource analytics:', error);
            this.monitoringService.logError('resource_analytics_fetch', error);
            return [];
        }
    }

    // Get report generation analytics
    async getReportAnalytics(reportType, startDate, endDate) {
        try {
            const q = query(
                this.eventCollection,
                where('type', '==', 'report_generation'),
                where('reportType', '==', reportType),
                where('timestamp', '>=', startDate),
                where('timestamp', '<=', endDate)
            );

            const querySnapshot = await getDocs(q);
            return querySnapshot.docs.map(doc => ({
                id: doc.id,
                ...doc.data()
            }));
        } catch (error) {
            console.error('Error fetching report analytics:', error);
            this.monitoringService.logError('report_analytics_fetch', error);
            return [];
        }
    }

    // Get current user ID from auth context
    getCurrentUserId() {
        // Implementation depends on your auth system
        return 'current_user_id';
    }

    // Aggregate analytics data
    async aggregateAnalytics(eventType, groupBy = 'day') {
        try {
            const events = await this.getAnalytics(
                eventType,
                Timestamp.fromDate(new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)), // Last 30 days
                Timestamp.now()
            );

            const aggregated = events.reduce((acc, event) => {
                const date = event.timestamp.toDate();
                const key = this.getGroupKey(date, groupBy);
                
                if (!acc[key]) {
                    acc[key] = {
                        count: 0,
                        data: []
                    };
                }

                acc[key].count++;
                acc[key].data.push(event);

                return acc;
            }, {});

            return Object.entries(aggregated).map(([key, value]) => ({
                period: key,
                count: value.count,
                data: value.data
            }));
        } catch (error) {
            console.error('Error aggregating analytics:', error);
            this.monitoringService.logError('analytics_aggregation', error);
            return [];
        }
    }

    // Helper function to get group key based on grouping period
    getGroupKey(date, groupBy) {
        switch (groupBy) {
            case 'hour':
                return `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()} ${date.getHours()}:00`;
            case 'day':
                return `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`;
            case 'week':
                const weekNumber = Math.ceil((date.getDate() + 1) / 7);
                return `${date.getFullYear()}-${date.getMonth() + 1}-W${weekNumber}`;
            case 'month':
                return `${date.getFullYear()}-${date.getMonth() + 1}`;
            default:
                return `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`;
        }
    }
}

export default new AnalyticsService();
