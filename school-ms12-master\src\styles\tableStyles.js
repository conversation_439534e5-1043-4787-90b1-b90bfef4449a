import { StyleSheet } from 'react-native';

/**
 * Common table styles for use across the application
 */
export const tableStyles = StyleSheet.create({
  // Table container styles
  tableWrapper: {
    marginVertical: 16,
    borderRadius: 8,
    overflow: 'hidden',
    elevation: 2,
    backgroundColor: '#ffffff',
  },
  tableContainer: {
    minWidth: '100%',
  },
  dataTable: {
    backgroundColor: '#ffffff',
  },
  tableHeader: {
    backgroundColor: '#f0f0f0',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    elevation: 2,
  },
  tableBody: {
    maxHeight: 400, // Default max height
  },
  
  // Common column styles
  idColumn: {
    width: 80,
    justifyContent: 'center',
  },
  photoColumn: {
    width: 70,
    justifyContent: 'center',
  },
  rollColumn: {
    width: 100,
    justifyContent: 'center',
  },
  nameColumn: {
    width: 180,
    justifyContent: 'center',
  },
  subjectColumn: {
    width: 150,
    justifyContent: 'center',
  },
  classColumn: {
    width: 120,
    justifyContent: 'center',
  },
  sectionColumn: {
    width: 120,
    justifyContent: 'center',
  },
  dateColumn: {
    width: 120,
    justifyContent: 'center',
  },
  timeColumn: {
    width: 120,
    justifyContent: 'center',
  },
  statusColumn: {
    width: 120,
    justifyContent: 'center',
  },
  gradeColumn: {
    width: 100,
    justifyContent: 'center',
  },
  remarksColumn: {
    width: 150,
    justifyContent: 'center',
  },
  actionsColumn: {
    width: 120,
    justifyContent: 'center',
  },
  
  // Row styles
  row: {
    minHeight: 60,
  },
  alternateRow: {
    backgroundColor: '#f5f5f5',
  },
  
  // Cell content styles
  cellText: {
    fontSize: 14,
  },
  cellTextBold: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  cellTextSmall: {
    fontSize: 12,
    color: '#666',
  },
});

/**
 * Function to get column styles based on column type
 * @param {string} columnType - The type of column
 * @returns {Object} The style object for the column
 */
export const getColumnStyle = (columnType) => {
  return tableStyles[`${columnType}Column`] || {};
};

export default tableStyles;
