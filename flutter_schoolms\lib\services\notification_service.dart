import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import '../models/notification_model.dart';
import '../utils/error_handler.dart';

class NotificationService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseMessaging _messaging = FirebaseMessaging.instance;

  // Get user notifications
  Future<List<NotificationModel>> getUserNotifications({
    int limit = 50,
    String? lastNotificationId,
  }) async {
    try {
      final userId = _auth.currentUser?.uid;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      Query query = _firestore
          .collection('notifications')
          .where('recipientId', isEqualTo: userId)
          .orderBy('createdAt', descending: true)
          .limit(limit);

      if (lastNotificationId != null) {
        final lastDoc = await _firestore
            .collection('notifications')
            .doc(lastNotificationId)
            .get();
        if (lastDoc.exists) {
          query = query.startAfterDocument(lastDoc);
        }
      }

      final snapshot = await query.get();
      return snapshot.docs
          .map((doc) => NotificationModel.fromMap({
                'id': doc.id,
                ...doc.data() as Map<String, dynamic>,
              }))
          .toList();
    } catch (e) {
      ErrorHandler.reportError(e, StackTrace.current);
      rethrow;
    }
  }

  // Send notification
  Future<void> sendNotification({
    required String title,
    required String body,
    required String recipientId,
    String? type,
    Map<String, dynamic>? data,
    String? imageUrl,
    String? actionUrl,
    NotificationPriority priority = NotificationPriority.normal,
  }) async {
    try {
      final senderId = _auth.currentUser?.uid;
      if (senderId == null) {
        throw Exception('User not authenticated');
      }

      final notification = NotificationModel(
        id: '', // Will be set by Firestore
        title: title,
        body: body,
        type: type ?? 'general',
        senderId: senderId,
        recipientId: recipientId,
        isRead: false,
        createdAt: DateTime.now(),
        data: data ?? {},
        imageUrl: imageUrl,
        actionUrl: actionUrl,
        priority: priority,
      );

      // Save to Firestore
      final docRef = await _firestore
          .collection('notifications')
          .add(notification.toMap());

      // Send push notification
      await _sendPushNotification(
        recipientId: recipientId,
        title: title,
        body: body,
        data: {
          'notificationId': docRef.id,
          'type': type ?? 'general',
          ...?data,
        },
      );
    } catch (e) {
      ErrorHandler.reportError(e, StackTrace.current);
      rethrow;
    }
  }

  // Send bulk notifications
  Future<void> sendBulkNotifications({
    required String title,
    required String body,
    required List<String> recipientIds,
    String? type,
    Map<String, dynamic>? data,
    String? imageUrl,
    String? actionUrl,
    NotificationPriority priority = NotificationPriority.normal,
  }) async {
    try {
      final senderId = _auth.currentUser?.uid;
      if (senderId == null) {
        throw Exception('User not authenticated');
      }

      final batch = _firestore.batch();
      final notifications = <String, Map<String, dynamic>>{};

      for (final recipientId in recipientIds) {
        final notification = NotificationModel(
          id: '', // Will be set by Firestore
          title: title,
          body: body,
          type: type ?? 'general',
          senderId: senderId,
          recipientId: recipientId,
          isRead: false,
          createdAt: DateTime.now(),
          data: data ?? {},
          imageUrl: imageUrl,
          actionUrl: actionUrl,
          priority: priority,
        );

        final docRef = _firestore.collection('notifications').doc();
        batch.set(docRef, notification.toMap());
        notifications[recipientId] = {
          'notificationId': docRef.id,
          'type': type ?? 'general',
          ...?data,
        };
      }

      // Commit batch
      await batch.commit();

      // Send push notifications
      for (final entry in notifications.entries) {
        await _sendPushNotification(
          recipientId: entry.key,
          title: title,
          body: body,
          data: entry.value,
        );
      }
    } catch (e) {
      ErrorHandler.reportError(e, StackTrace.current);
      rethrow;
    }
  }

  // Mark notification as read
  Future<void> markAsRead(String notificationId) async {
    try {
      await _firestore
          .collection('notifications')
          .doc(notificationId)
          .update({
        'isRead': true,
        'readAt': Timestamp.fromDate(DateTime.now()),
      });
    } catch (e) {
      ErrorHandler.reportError(e, StackTrace.current);
      rethrow;
    }
  }

  // Mark all notifications as read
  Future<void> markAllAsRead() async {
    try {
      final userId = _auth.currentUser?.uid;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      final snapshot = await _firestore
          .collection('notifications')
          .where('recipientId', isEqualTo: userId)
          .where('isRead', isEqualTo: false)
          .get();

      final batch = _firestore.batch();
      for (final doc in snapshot.docs) {
        batch.update(doc.reference, {
          'isRead': true,
          'readAt': Timestamp.fromDate(DateTime.now()),
        });
      }

      await batch.commit();
    } catch (e) {
      ErrorHandler.reportError(e, StackTrace.current);
      rethrow;
    }
  }

  // Delete notification
  Future<void> deleteNotification(String notificationId) async {
    try {
      await _firestore
          .collection('notifications')
          .doc(notificationId)
          .delete();
    } catch (e) {
      ErrorHandler.reportError(e, StackTrace.current);
      rethrow;
    }
  }

  // Get unread count
  Future<int> getUnreadCount() async {
    try {
      final userId = _auth.currentUser?.uid;
      if (userId == null) {
        return 0;
      }

      final snapshot = await _firestore
          .collection('notifications')
          .where('recipientId', isEqualTo: userId)
          .where('isRead', isEqualTo: false)
          .count()
          .get();

      return snapshot.count ?? 0;
    } catch (e) {
      ErrorHandler.reportError(e, StackTrace.current);
      return 0;
    }
  }

  // Subscribe to notification updates
  Stream<List<NotificationModel>> getNotificationStream({int limit = 20}) {
    final userId = _auth.currentUser?.uid;
    if (userId == null) {
      return Stream.value([]);
    }

    return _firestore
        .collection('notifications')
        .where('recipientId', isEqualTo: userId)
        .orderBy('createdAt', descending: true)
        .limit(limit)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => NotificationModel.fromMap({
                  'id': doc.id,
                  ...doc.data(),
                }))
            .toList());
  }

  // Send push notification
  Future<void> _sendPushNotification({
    required String recipientId,
    required String title,
    required String body,
    Map<String, dynamic>? data,
  }) async {
    try {
      // Get recipient's FCM token
      final userDoc = await _firestore
          .collection('users')
          .doc(recipientId)
          .get();

      if (!userDoc.exists) return;

      final userData = userDoc.data()!;
      final fcmToken = userData['fcmToken'] as String?;

      if (fcmToken == null) return;

      // Send notification via FCM (this would typically be done via Cloud Functions)
      // For now, we'll just log it
      print('Sending push notification to $recipientId: $title - $body');
    } catch (e) {
      ErrorHandler.reportError(e, StackTrace.current);
      // Don't rethrow as push notification failure shouldn't break the flow
    }
  }

  // Update FCM token
  Future<void> updateFCMToken() async {
    try {
      final userId = _auth.currentUser?.uid;
      if (userId == null) return;

      final token = await _messaging.getToken();
      if (token != null) {
        await _firestore.collection('users').doc(userId).update({
          'fcmToken': token,
          'tokenUpdatedAt': Timestamp.fromDate(DateTime.now()),
        });
      }
    } catch (e) {
      ErrorHandler.reportError(e, StackTrace.current);
    }
  }
}
