import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import { DataTable, Text, Card, Title, ActivityIndicator, Button, IconButton, Chip, Badge, Searchbar, Menu, Divider, Surface } from 'react-native-paper';
import { db } from '../config/firebase';
import { collection, query, where, getDocs } from 'firebase/firestore';
import { useNavigation } from '@react-navigation/native';
import { useLanguage } from '../context/LanguageContext';
import * as Animatable from 'react-native-animatable';
import { MaterialCommunityIcons } from '@expo/vector-icons';

const SectionStudentsList = ({ classId, sectionName, onClose }) => {
  const navigation = useNavigation();
  const { translate, getTextStyle, isRTL } = useLanguage();
  const [students, setStudents] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState('name');
  const [sortOrder, setSortOrder] = useState('asc');
  const [menuVisible, setMenuVisible] = useState(false);
  const [selectedStudent, setSelectedStudent] = useState(null);
  const [quickViewVisible, setQuickViewVisible] = useState(false);

  // Translate function for this component
  const t = (key, params = {}) => {
    try {
      return translate(`classManagement.${key}`, params);
    } catch (error) {
      console.error('Translation error:', error);
      return key;
    }
  };

  useEffect(() => {
    fetchStudents();
  }, [classId, sectionName]);

  const fetchStudents = async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('Fetching students for section:', { classId, sectionName });

      const studentsRef = collection(db, 'users');
      const q = query(
        studentsRef,
        where('role', '==', 'student'),
        where('classId', '==', classId),
        where('sectionName', '==', sectionName)
      );

      const querySnapshot = await getDocs(q);
      console.log('Query snapshot size:', querySnapshot.size);

      querySnapshot.forEach(doc => {
        console.log('Student data:', doc.data());
      });

      const studentsList = [];

      for (const doc of querySnapshot.docs) {
        const studentData = doc.data();

        // Fetch student's results
        const resultsRef = collection(db, 'results');
        const resultsQuery = query(
          resultsRef,
          where('studentId', '==', doc.id),
          where('classId', '==', classId)
        );
        const resultsSnapshot = await getDocs(resultsQuery);
        const results = resultsSnapshot.docs.map(resultDoc => resultDoc.data());

        studentsList.push({
          id: doc.id,
          ...studentData,
          results: results
        });
      }

      setStudents(studentsList);
    } catch (err) {
      console.error('Error fetching students:', err);
      setError('Failed to fetch students');
    } finally {
      setLoading(false);
    }
  };

  const calculateAverage = (results) => {
    if (!results || results.length === 0) return 'N/A';
    const total = results.reduce((sum, result) => sum + (result.score || 0), 0);
    return (total / results.length).toFixed(2);
  };

  const getScoreColor = (score) => {
    if (!score || score === 'N/A') return '#666';
    const numScore = parseFloat(score);
    if (numScore >= 90) return '#4CAF50';
    if (numScore >= 70) return '#FFC107';
    if (numScore >= 50) return '#FF9800';
    return '#F44336';
  };

  const getScoreLabel = (score) => {
    if (!score || score === 'N/A') return t('notAvailable');
    const numScore = parseFloat(score);
    if (numScore >= 90) return t('excellent');
    if (numScore >= 70) return t('veryGood');
    if (numScore >= 50) return t('good');
    return t('needsImprovement');
  };



  const sortStudents = (students) => {
    return [...students].sort((a, b) => {
      if (sortBy === 'name') {
        const nameA = `${a.firstName} ${a.lastName}`.toLowerCase();
        const nameB = `${b.firstName} ${b.lastName}`.toLowerCase();
        return sortOrder === 'asc' ? nameA.localeCompare(nameB) : nameB.localeCompare(nameA);
      } else if (sortBy === 'rollNumber') {
        const rollA = a.rollNumber || '';
        const rollB = b.rollNumber || '';
        return sortOrder === 'asc' ? rollA.localeCompare(rollB) : rollB.localeCompare(rollA);
      } else if (sortBy === 'average') {
        const avgA = calculateAverage(a.results);
        const avgB = calculateAverage(b.results);
        const numA = avgA === 'N/A' ? -1 : parseFloat(avgA);
        const numB = avgB === 'N/A' ? -1 : parseFloat(avgB);
        return sortOrder === 'asc' ? numA - numB : numB - numA;
      }
      return 0;
    });
  };

  const toggleSortOrder = (field) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('asc');
    }
  };

  const filteredStudents = students.filter(student => {
    const searchLower = searchQuery.toLowerCase();
    const fullName = `${student.firstName} ${student.lastName}`.toLowerCase();
    const email = (student.email || '').toLowerCase();
    const roll = (student.rollNumber || '').toLowerCase();

    return fullName.includes(searchLower) ||
           email.includes(searchLower) ||
           roll.includes(searchLower);
  });

  const renderQuickView = () => {
    if (!selectedStudent) return null;

    const average = calculateAverage(selectedStudent.results);
    const scoreColor = getScoreColor(average);

    return (
      <Animatable.View animation="fadeIn" duration={300} style={styles.quickViewContainer}>
        <Surface style={styles.quickViewContent}>
          <View style={styles.quickViewHeader}>
            <View style={styles.studentAvatar}>
              <Text style={styles.avatarText}>
                {selectedStudent.firstName?.[0]}{selectedStudent.lastName?.[0]}
              </Text>
            </View>
            <View style={styles.quickViewHeaderText}>
              <Title style={[styles.quickViewName, getTextStyle()]}>
                {`${selectedStudent.firstName} ${selectedStudent.lastName}`}
              </Title>
              <Text style={[styles.quickViewSubtitle, getTextStyle()]}>
                {t('rollNumber')}: {selectedStudent.rollNumber || 'N/A'}
              </Text>
            </View>
            <IconButton
              icon="close"
              size={20}
              onPress={() => setQuickViewVisible(false)}
              style={styles.closeQuickView}
            />
          </View>

          <Divider style={styles.quickViewDivider} />

          <View style={styles.quickViewStats}>
            <View style={styles.quickViewStatItem}>
              <Text style={styles.quickViewStatLabel}>{t('averageScore')}</Text>
              <Text style={[styles.quickViewStatValue, { color: scoreColor }]}>{average}</Text>
              <Chip style={[styles.scoreChip, { backgroundColor: 'rgba(240, 240, 240, 0.8)' }]}>
                <Text style={{ color: scoreColor }}>{getScoreLabel(average)}</Text>
              </Chip>
            </View>
          </View>

          <View style={styles.quickViewActions}>
            <Button
              mode="contained"
              icon="account-details"
              onPress={() => {
                setQuickViewVisible(false);
                navigation.navigate('StudentDetails', { studentId: selectedStudent.id });
              }}
              style={styles.viewDetailsButton}
            >
              {t('viewDetails')}
            </Button>
          </View>
        </Surface>
      </Animatable.View>
    );
  };

  if (loading) {
    return (
      <View style={styles.centered}>
        <ActivityIndicator size="large" color="#4CAF50" />
        <Text style={[styles.loadingText, getTextStyle()]}>{t('loadingStudents')}</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.centered}>
        <MaterialCommunityIcons name="alert-circle-outline" size={48} color="#F44336" />
        <Text style={[styles.error, getTextStyle()]}>{error}</Text>
        <Button mode="contained" onPress={fetchStudents} style={styles.retryButton}>
          {t('retry')}
        </Button>
      </View>
    );
  }

  const sortedAndFilteredStudents = sortStudents(filteredStudents);

  return (
    <Card style={styles.container}>
      <Card.Content>
        <Animatable.View animation="fadeIn" duration={500}>
          <View style={[styles.headerContainer, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
            <View>
              <Title style={[styles.title, getTextStyle()]}>{t('studentsInSection')} {sectionName}</Title>
              <Text style={[styles.hintText, getTextStyle()]}>{t('tapForDetails')}</Text>
            </View>
            <Badge style={styles.studentCountBadge}>{students.length}</Badge>
          </View>

          <Searchbar
            placeholder={t('searchStudents')}
            onChangeText={setSearchQuery}
            value={searchQuery}
            style={styles.searchBar}
            iconColor="#4CAF50"
          />

          <View style={[styles.sortControls, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
            <Text style={[styles.sortByText, getTextStyle()]}>{t('sortBy')}:</Text>
            <Chip
              selected={sortBy === 'name'}
              onPress={() => toggleSortOrder('name')}
              style={styles.sortChip}
              icon={sortBy === 'name' ? (sortOrder === 'asc' ? 'sort-alphabetical-ascending' : 'sort-alphabetical-descending') : 'account'}
            >
              {t('name')}
            </Chip>
            <Chip
              selected={sortBy === 'rollNumber'}
              onPress={() => toggleSortOrder('rollNumber')}
              style={styles.sortChip}
              icon={sortBy === 'rollNumber' ? (sortOrder === 'asc' ? 'sort-numeric-ascending' : 'sort-numeric-descending') : 'identifier'}
            >
              {t('rollNumber')}
            </Chip>
            <Chip
              selected={sortBy === 'average'}
              onPress={() => toggleSortOrder('average')}
              style={styles.sortChip}
              icon={sortBy === 'average' ? (sortOrder === 'asc' ? 'sort-numeric-ascending' : 'sort-numeric-descending') : 'chart-bar'}
            >
              {t('averageScore')}
            </Chip>
          </View>
        </Animatable.View>

        <ScrollView style={styles.tableContainer}>
          <DataTable>
            <DataTable.Header style={styles.tableHeader}>
              <DataTable.Title
                style={{ flexDirection: isRTL ? 'row-reverse' : 'row' }}
                sortDirection={sortBy === 'rollNumber' ? (sortOrder === 'asc' ? 'ascending' : 'descending') : 'none'}
              >
                <Text style={getTextStyle()}>{t('rollNumber')}</Text>
              </DataTable.Title>
              <DataTable.Title
                style={{ flexDirection: isRTL ? 'row-reverse' : 'row' }}
                sortDirection={sortBy === 'name' ? (sortOrder === 'asc' ? 'ascending' : 'descending') : 'none'}
              >
                <Text style={getTextStyle()}>{t('name')}</Text>
              </DataTable.Title>
              <DataTable.Title
                numeric
                style={{ flexDirection: isRTL ? 'row-reverse' : 'row' }}
                sortDirection={sortBy === 'average' ? (sortOrder === 'asc' ? 'ascending' : 'descending') : 'none'}
              >
                <Text style={getTextStyle()}>{t('averageScore')}</Text>
              </DataTable.Title>
              <DataTable.Title style={{ width: 80 }}>
                <Text style={getTextStyle()}>{t('actions')}</Text>
              </DataTable.Title>
            </DataTable.Header>

            {sortedAndFilteredStudents.map((student, index) => (
              <Animatable.View
                key={student.id}
                animation="fadeInUp"
                duration={300}
                delay={index * 50}
              >
                <DataTable.Row
                  onPress={() => {
                    setSelectedStudent(student);
                    setQuickViewVisible(true);
                  }}
                  style={[styles.clickableRow, quickViewVisible && selectedStudent?.id === student.id && styles.selectedRow]}
                >
                  <DataTable.Cell>
                    <Text style={getTextStyle()}>{student.rollNumber || (index + 1).toString().padStart(2, '0')}</Text>
                  </DataTable.Cell>
                  <DataTable.Cell>
                    <View style={[styles.nameCell, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
                      <View style={styles.studentAvatar}>
                        <Text style={styles.avatarText}>
                          {student.firstName?.[0]}{student.lastName?.[0]}
                        </Text>
                      </View>
                      <Text style={[styles.studentName, getTextStyle()]}>
                        {`${student.firstName} ${student.lastName}`}
                      </Text>
                    </View>
                  </DataTable.Cell>
                  <DataTable.Cell numeric>
                    <View style={styles.scoreContainer}>
                      <Text style={[styles.scoreText, { color: getScoreColor(calculateAverage(student.results)) }, getTextStyle()]}>
                        {calculateAverage(student.results)}
                      </Text>
                    </View>
                  </DataTable.Cell>
                  <DataTable.Cell style={{ width: 80 }}>
                    <View style={styles.actionButtons}>
                      <IconButton
                        icon="eye"
                        size={20}
                        color="#4CAF50"
                        onPress={() => {
                          setSelectedStudent(student);
                          setQuickViewVisible(true);
                        }}
                        style={styles.actionButton}
                      />
                      <IconButton
                        icon="account-details"
                        size={20}
                        color="#2196F3"
                        onPress={() => navigation.navigate('StudentDetails', { studentId: student.id })}
                        style={styles.actionButton}
                      />
                    </View>
                  </DataTable.Cell>
                </DataTable.Row>
              </Animatable.View>
            ))}

            {sortedAndFilteredStudents.length === 0 && (
              <DataTable.Row style={styles.emptyRow}>
                <DataTable.Cell style={styles.emptyCell}>
                  <View style={styles.noDataContainer}>
                    <MaterialCommunityIcons name="account-search" size={48} color="#9e9e9e" />
                    <Text style={[styles.noStudents, getTextStyle()]}>{t('noStudentsFound')}</Text>
                  </View>
                </DataTable.Cell>
              </DataTable.Row>
            )}
          </DataTable>
        </ScrollView>

        <Animatable.View animation="fadeInUp" duration={500}>
          <Button
            mode="outlined"
            onPress={onClose}
            style={styles.closeButton}
            icon="close"
          >
            {t('close')}
          </Button>
        </Animatable.View>
      </Card.Content>

      {quickViewVisible && renderQuickView()}

      <Menu
        visible={menuVisible}
        onDismiss={() => setMenuVisible(false)}
        anchor={{ x: 0, y: 0 }}
      >
        <Menu.Item
          onPress={() => {
            setMenuVisible(false);
            navigation.navigate('StudentDetails', { studentId: selectedStudent?.id });
          }}
          title={t('viewDetails')}
          icon="account-details"
        />
      </Menu>
    </Card>
  );
};

const styles = StyleSheet.create({
  container: {
    margin: 10,
    elevation: 4,
    borderRadius: 12,
    overflow: 'hidden',
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  studentCountBadge: {
    backgroundColor: '#4CAF50',
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold',
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  error: {
    color: '#F44336',
    textAlign: 'center',
    margin: 16,
    fontSize: 16,
  },
  retryButton: {
    marginTop: 16,
    backgroundColor: '#4CAF50',
  },
  searchBar: {
    marginBottom: 16,
    elevation: 2,
    borderRadius: 8,
    backgroundColor: '#f5f5f5',
  },
  sortControls: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    flexWrap: 'wrap',
  },
  sortByText: {
    marginRight: 8,
    fontSize: 14,
    color: '#666',
  },
  sortChip: {
    marginRight: 8,
    marginBottom: 8,
  },
  tableContainer: {
    maxHeight: 400,
  },
  tableHeader: {
    backgroundColor: '#f5f5f5',
  },
  noStudents: {
    textAlign: 'center',
    margin: 20,
    fontStyle: 'italic',
    color: '#9e9e9e',
    fontSize: 16,
  },
  closeButton: {
    marginTop: 20,
  },
  clickableRow: {
    cursor: 'pointer',
    backgroundColor: 'rgba(0, 0, 0, 0.02)',
    borderRadius: 4,
    marginVertical: 2,
    borderLeftWidth: 3,
    borderLeftColor: 'transparent',
  },
  selectedRow: {
    backgroundColor: 'rgba(76, 175, 80, 0.1)',
    borderLeftColor: '#4CAF50',
  },
  hintText: {
    fontSize: 12,
    fontStyle: 'italic',
    color: '#666',
    marginBottom: 10,
  },
  nameCell: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  studentAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#4CAF50',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  avatarText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 12,
  },
  studentName: {
    fontSize: 14,
    fontWeight: '500',
  },
  scoreContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
  },
  scoreText: {
    fontWeight: 'bold',
    fontSize: 14,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  actionButton: {
    margin: 0,
    padding: 0,
  },
  emptyRow: {
    height: 200,
  },
  emptyCell: {
    flex: 1,
  },
  noDataContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  quickViewContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  quickViewContent: {
    width: '90%',
    maxWidth: 500,
    borderRadius: 12,
    padding: 16,
    backgroundColor: 'white',
    elevation: 5,
  },
  quickViewHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  quickViewHeaderText: {
    flex: 1,
    marginLeft: 16,
  },
  quickViewName: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  quickViewSubtitle: {
    fontSize: 14,
    color: '#666',
  },
  closeQuickView: {
    margin: 0,
  },
  quickViewDivider: {
    marginBottom: 16,
  },
  quickViewStats: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  quickViewStatItem: {
    flex: 1,
    alignItems: 'center',
  },
  quickViewStatLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  quickViewStatValue: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  scoreChip: {
    marginTop: 4,
  },
  quickViewActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 16,
  },
  viewDetailsButton: {
    backgroundColor: '#4CAF50',
  }
});

export default SectionStudentsList;
