import React, { useState, useEffect, useRef, useCallback } from 'react';
import { View, ScrollView, StyleSheet, Text, Animated, TouchableOpacity, RefreshControl, Platform } from 'react-native';
import { Card, Title, Portal, Modal, DataTable, List, Provider, IconButton, Surface, Divider, Avatar, Chip, Button, Snackbar, ProgressBar, Menu, Searchbar, ActivityIndicator, Badge, useTheme, Switch, TextInput } from 'react-native-paper';
import { db } from '../../config/firebase';
import { collection, addDoc, query, getDocs, doc, updateDoc, deleteDoc, orderBy, where, Timestamp } from 'firebase/firestore';
import CustomButton from '../../components/common/CustomButton';
import CustomInput from '../../components/common/CustomInput';
import { useLanguage } from '../../context/LanguageContext';
import { LinearGradient } from 'expo-linear-gradient';
import * as Animatable from 'react-native-animatable';
import { useNavigation } from '@react-navigation/native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import AdminAppHeader from '../../components/common/AdminAppHeader';
import AdminSidebar from '../../components/common/AdminSidebar';
import SidebarBackdrop from '../../components/common/SidebarBackdrop';
import ActivityService from '../../services/ActivityService';

const LibrarySettings = () => {
  const { translate, language } = useLanguage();
  // No theme needed
  const navigation = useNavigation();

  // Data state
  const [settings, setSettings] = useState(null);
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [categoryModalVisible, setCategoryModalVisible] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState(null);

  // UI state
  const [activeTab, setActiveTab] = useState('general'); // 'general', 'hours', 'categories'
  const [refreshing, setRefreshing] = useState(false);
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');

  // Sidebar state
  const [sidebarVisible, setSidebarVisible] = useState(false);
  const [activeSidebarItem, setActiveSidebarItem] = useState('LibrarySettings');
  const drawerAnim = useRef(new Animated.Value(-300)).current;

  // Confirmation dialog state
  const [confirmDialogVisible, setConfirmDialogVisible] = useState(false);
  const [confirmDialogAction, setConfirmDialogAction] = useState(null);
  const [confirmDialogParams, setConfirmDialogParams] = useState(null);
  const [confirmDialogTitle, setConfirmDialogTitle] = useState('');
  const [confirmDialogMessage, setConfirmDialogMessage] = useState('');

  const defaultFormData = {
    workingHours: {
      monday: { start: '08:00', end: '16:00', closed: false },
      tuesday: { start: '08:00', end: '16:00', closed: false },
      wednesday: { start: '08:00', end: '16:00', closed: false },
      thursday: { start: '08:00', end: '16:00', closed: false },
      friday: { start: '08:00', end: '16:00', closed: false },
      saturday: { start: '09:00', end: '13:00', closed: false },
      sunday: { start: '00:00', end: '00:00', closed: true },
    },
    location: {
      building: '',
      floor: '',
      room: '',
    },
    maxBorrowDays: '14',
    maxBooksPerStudent: '3',
    maxBooksPerTeacher: '5',
    finePerDay: '1',
    categories: [],
  };

  const [formData, setFormData] = useState(defaultFormData);

  const defaultCategoryForm = {
    name: '',
    code: '',
    description: '',
    shelfLocation: '',
    ageGroup: '',
    maxBorrowDays: '14',
  };

  const [categoryForm, setCategoryForm] = useState(defaultCategoryForm);

  // Toggle sidebar
  const toggleSidebar = () => {
    const toValue = sidebarVisible ? -300 : 0;
    Animated.timing(drawerAnim, {
      toValue,
      duration: 250,
      useNativeDriver: true,
    }).start();
    setSidebarVisible(!sidebarVisible);
  };

  // Handle tab change
  const handleTabChange = (tab) => {
    setActiveTab(tab);
  };

  // Show confirmation dialog
  const showConfirmDialog = (action, params, title, message) => {
    setConfirmDialogAction(() => action);
    setConfirmDialogParams(params);
    setConfirmDialogTitle(title);
    setConfirmDialogMessage(message);
    setConfirmDialogVisible(true);
  };

  // Handle confirmation dialog confirm action
  const handleConfirmDialogConfirm = () => {
    if (confirmDialogAction) {
      confirmDialogAction(confirmDialogParams);
    }
    setConfirmDialogVisible(false);
  };

  // Handle confirmation dialog dismiss
  const handleConfirmDialogDismiss = () => {
    setConfirmDialogVisible(false);
  };

  // Refresh function for pull-to-refresh
  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      await fetchSettings();
      setSnackbarMessage(translate('library.refreshSuccess') || 'Library settings refreshed');
      setSnackbarVisible(true);
    } catch (error) {
      console.error('Error refreshing library settings:', error);
      setSnackbarMessage(translate('library.refreshError') || 'Failed to refresh library settings');
      setSnackbarVisible(true);
    } finally {
      setRefreshing(false);
    }
  }, []);

  useEffect(() => {
    // Hide header in this screen as we're using our own
    navigation.setOptions({
      headerShown: false,
    });

    // Set active sidebar item
    setActiveSidebarItem('LibrarySettings');

    // Fetch initial data
    fetchSettings();
  }, [navigation]);

  const fetchSettings = async () => {
    try {
      setLoading(true);

      // Fetch library settings
      const settingsRef = collection(db, 'librarySettings');
      const querySnapshot = await getDocs(settingsRef);

      // Fetch categories
      const categoriesRef = collection(db, 'bookCategories');
      const categoriesQuery = query(categoriesRef, orderBy('name', 'asc'));
      const categoriesSnapshot = await getDocs(categoriesQuery);
      const categoriesData = [];
      categoriesSnapshot.forEach(doc => categoriesData.push({ id: doc.id, ...doc.data() }));

      if (!querySnapshot.empty) {
        const settingsDoc = querySnapshot.docs[0];
        const settingsData = settingsDoc.data();

        // Update settings with categories
        const updatedSettings = {
          id: settingsDoc.id,
          ...settingsData,
          categories: categoriesData
        };

        setSettings(updatedSettings);
        setFormData({
          workingHours: settingsData.workingHours || defaultFormData.workingHours,
          location: settingsData.location || defaultFormData.location,
          maxBorrowDays: settingsData.maxBorrowDays || defaultFormData.maxBorrowDays,
          maxBooksPerStudent: settingsData.maxBooksPerStudent || defaultFormData.maxBooksPerStudent,
          maxBooksPerTeacher: settingsData.maxBooksPerTeacher || defaultFormData.maxBooksPerTeacher,
          finePerDay: settingsData.finePerDay || defaultFormData.finePerDay,
          categories: categoriesData,
        });
      } else {
        // If no settings exist, create default settings with categories
        const defaultSettings = {
          ...defaultFormData,
          categories: categoriesData,
        };
        setSettings(defaultSettings);
        setFormData(defaultSettings);
      }

      return true;
    } catch (error) {
      console.error('Error fetching library settings:', error);
      setSnackbarMessage(translate('library.fetchError') || 'Failed to load library settings');
      setSnackbarVisible(true);
      return false;
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateSettings = async () => {
    try {
      setLoading(true);

      // Validate form data
      if (!formData.maxBorrowDays || !formData.maxBooksPerStudent || !formData.maxBooksPerTeacher || !formData.finePerDay) {
        setSnackbarMessage(translate('library.missingFields') || 'Please fill in all required fields');
        setSnackbarVisible(true);
        return;
      }

      // Convert numeric fields to numbers
      const numericFormData = {
        ...formData,
        maxBorrowDays: parseInt(formData.maxBorrowDays),
        maxBooksPerStudent: parseInt(formData.maxBooksPerStudent),
        maxBooksPerTeacher: parseInt(formData.maxBooksPerTeacher),
        finePerDay: parseFloat(formData.finePerDay),
      };

      if (settings && settings.id) {
        // Update existing settings
        const settingsRef = doc(db, 'librarySettings', settings.id);
        const updateData = {
          workingHours: numericFormData.workingHours || defaultFormData.workingHours,
          location: numericFormData.location || defaultFormData.location,
          maxBorrowDays: numericFormData.maxBorrowDays,
          maxBooksPerStudent: numericFormData.maxBooksPerStudent,
          maxBooksPerTeacher: numericFormData.maxBooksPerTeacher,
          finePerDay: numericFormData.finePerDay,
          updatedAt: new Date().toISOString(),
        };

        await updateDoc(settingsRef, updateData);

        // Log activity
        await ActivityService.logActivity(
          'admin',
          'library',
          translate('activities.updateLibrarySettings') || 'Updated library settings',
          translate('activities.updateLibrarySettingsDesc') || 'Updated library settings configuration'
        );
      } else {
        // Create new settings
        const settingsRef = collection(db, 'librarySettings');
        await addDoc(settingsRef, {
          ...numericFormData,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        });

        // Log activity
        await ActivityService.logActivity(
          'admin',
          'library',
          translate('activities.createLibrarySettings') || 'Created library settings',
          translate('activities.createLibrarySettingsDesc') || 'Created initial library settings configuration'
        );
      }

      setVisible(false);
      await fetchSettings();
      setSnackbarMessage(translate('library.settingsUpdated') || 'Library settings updated successfully');
      setSnackbarVisible(true);
    } catch (error) {
      console.error('Error updating library settings:', error);
      setSnackbarMessage(translate('library.updateError') || 'Failed to update library settings');
      setSnackbarVisible(true);
    } finally {
      setLoading(false);
    }
  };

  const handleAddCategory = async () => {
    try {
      setLoading(true);

      // Validate form data
      if (!categoryForm.name || !categoryForm.code) {
        setSnackbarMessage(translate('library.missingCategoryFields') || 'Please fill in all required fields');
        setSnackbarVisible(true);
        return;
      }

      const categoriesRef = collection(db, 'bookCategories');
      await addDoc(categoriesRef, {
        ...categoryForm,
        maxBorrowDays: parseInt(categoryForm.maxBorrowDays),
        createdAt: new Date().toISOString(),
      });

      // Log activity
      await ActivityService.logActivity(
        'admin',
        'library',
        translate('activities.addCategory') || 'Added new category',
        translate('activities.addCategoryDesc', { name: categoryForm.name }) || `Added category: ${categoryForm.name}`
      );

      setCategoryModalVisible(false);
      resetCategoryForm();
      await fetchSettings();
      setSnackbarMessage(translate('library.categoryAdded') || 'Category added successfully');
      setSnackbarVisible(true);
    } catch (error) {
      console.error('Error adding category:', error);
      setSnackbarMessage(translate('library.addCategoryError') || 'Failed to add category');
      setSnackbarVisible(true);
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateCategory = async () => {
    try {
      setLoading(true);

      // Validate form data
      if (!categoryForm.name || !categoryForm.code) {
        setSnackbarMessage(translate('library.missingCategoryFields') || 'Please fill in all required fields');
        setSnackbarVisible(true);
        return;
      }

      const categoryRef = doc(db, 'bookCategories', selectedCategory.id);
      await updateDoc(categoryRef, {
        ...categoryForm,
        maxBorrowDays: parseInt(categoryForm.maxBorrowDays),
        updatedAt: new Date().toISOString(),
      });

      // Log activity
      await ActivityService.logActivity(
        'admin',
        'library',
        translate('activities.updateCategory') || 'Updated category',
        translate('activities.updateCategoryDesc', { name: categoryForm.name }) || `Updated category: ${categoryForm.name}`
      );

      setCategoryModalVisible(false);
      resetCategoryForm();
      await fetchSettings();
      setSnackbarMessage(translate('library.categoryUpdated') || 'Category updated successfully');
      setSnackbarVisible(true);
    } catch (error) {
      console.error('Error updating category:', error);
      setSnackbarMessage(translate('library.updateCategoryError') || 'Failed to update category');
      setSnackbarVisible(true);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteCategory = async (categoryId) => {
    try {
      // Get category name for activity log
      const categoryToDelete = settings.categories.find(cat => cat.id === categoryId);
      if (!categoryToDelete) return;

      showConfirmDialog(
        async () => {
          try {
            setLoading(true);
            await deleteDoc(doc(db, 'bookCategories', categoryId));

            // Log activity
            await ActivityService.logActivity(
              'admin',
              'library',
              translate('activities.deleteCategory') || 'Deleted category',
              translate('activities.deleteCategoryDesc', { name: categoryToDelete.name }) || `Deleted category: ${categoryToDelete.name}`
            );

            await fetchSettings();
            setCategoryModalVisible(false);
            resetCategoryForm();
            setSnackbarMessage(translate('library.categoryDeleted') || 'Category deleted successfully');
            setSnackbarVisible(true);
          } catch (error) {
            console.error('Error deleting category:', error);
            setSnackbarMessage(translate('library.deleteCategoryError') || 'Failed to delete category');
            setSnackbarVisible(true);
          } finally {
            setLoading(false);
          }
        },
        categoryId,
        translate('library.confirmDeleteCategory') || 'Confirm Delete Category',
        translate('library.confirmDeleteCategoryMessage', { name: categoryToDelete.name }) || `Are you sure you want to delete the category "${categoryToDelete.name}"? This action cannot be undone.`
      );
    } catch (error) {
      console.error('Error preparing to delete category:', error);
      setSnackbarMessage(translate('library.deleteCategoryError') || 'Failed to delete category');
      setSnackbarVisible(true);
    }
  };

  const resetCategoryForm = () => {
    setCategoryForm({
      name: '',
      code: '',
      description: '',
      shelfLocation: '',
      ageGroup: '',
      maxBorrowDays: '14',
    });
    setSelectedCategory(null);
  };

  const toggleDayClosed = (day) => {
    setFormData({
      ...formData,
      workingHours: {
        ...formData.workingHours,
        [day]: {
          ...formData.workingHours[day],
          closed: !formData.workingHours[day].closed,
        },
      },
    });
  };

  return (
    <Provider>
      <SafeAreaView style={styles.container}>
        {/* Admin App Header */}
        <AdminAppHeader
          title={translate('library.settings') || "Library Settings"}
          onMenuPress={toggleSidebar}
        />

        {/* Sidebar Backdrop - only visible when sidebar is open */}
        {sidebarVisible && (
          <SidebarBackdrop onPress={toggleSidebar} />
        )}

        {/* Admin Sidebar */}
        <AdminSidebar
          drawerAnim={drawerAnim}
          activeSidebarItem={activeSidebarItem}
          setActiveSidebarItem={setActiveSidebarItem}
          toggleDrawer={toggleSidebar}
        />

        {/* Main Content */}
        <ScrollView
          style={styles.content}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={['#1976d2']}
              tintColor={'#1976d2'}
            />
          }
        >
          <Card style={styles.card}>
            <Card.Content>
              <View style={styles.headerContainer}>
                <Title style={styles.headerTitle}>{translate('library.settings') || "Library Settings"}</Title>
                {loading && <ActivityIndicator style={styles.loader} />}
              </View>

            {settings ? (
              <>
                <View style={styles.section}>
                  <Title style={styles.sectionTitle}>Working Hours</Title>
                  {Object.entries(settings.workingHours).map(([day, hours]) => (
                    <View key={day} style={styles.workingHoursRow}>
                      <Text style={styles.dayText}>
                        {day.charAt(0).toUpperCase() + day.slice(1)}:
                      </Text>
                      <Text>
                        {hours.closed ? 'Closed' : `${hours.start} - ${hours.end}`}
                      </Text>
                    </View>
                  ))}
                </View>

                <View style={styles.section}>
                  <Title style={styles.sectionTitle}>Location</Title>
                  <Text>Building: {settings.location.building}</Text>
                  <Text>Floor: {settings.location.floor}</Text>
                  <Text>Room: {settings.location.room}</Text>
                </View>

                <View style={styles.section}>
                  <Title style={styles.sectionTitle}>Borrowing Rules</Title>
                  <Text>Maximum Borrow Days: {settings.maxBorrowDays}</Text>
                  <Text>Books per Student: {settings.maxBooksPerStudent}</Text>
                  <Text>Books per Teacher: {settings.maxBooksPerTeacher}</Text>
                  <Text>Fine per Day: ${settings.finePerDay}</Text>
                </View>

                <View style={styles.section}>
                  <Title style={styles.sectionTitle}>Categories</Title>
                  <DataTable>
                    <DataTable.Header>
                      <DataTable.Title>Name</DataTable.Title>
                      <DataTable.Title>Code</DataTable.Title>
                      <DataTable.Title>Location</DataTable.Title>
                    </DataTable.Header>

                    {settings.categories.map((category) => (
                      <DataTable.Row
                        key={category.id}
                        onPress={() => {
                          setSelectedCategory(category);
                          setCategoryForm({
                            name: category.name,
                            code: category.code,
                            description: category.description,
                            shelfLocation: category.shelfLocation,
                            ageGroup: category.ageGroup,
                            maxBorrowDays: category.maxBorrowDays.toString(),
                          });
                          setCategoryModalVisible(true);
                        }}
                      >
                        <DataTable.Cell>{category.name}</DataTable.Cell>
                        <DataTable.Cell>{category.code}</DataTable.Cell>
                        <DataTable.Cell>{category.shelfLocation}</DataTable.Cell>
                      </DataTable.Row>
                    ))}
                  </DataTable>

                  <CustomButton
                    mode="outlined"
                    onPress={() => setCategoryModalVisible(true)}
                    style={styles.addButton}
                  >
                    Add Category
                  </CustomButton>
                </View>
              </>
            ) : (
              <Text>No settings configured</Text>
            )}

            <CustomButton
              mode="contained"
              onPress={() => setVisible(true)}
              style={styles.editButton}
            >
              Edit Settings
            </CustomButton>
          </Card.Content>
        </Card>
        </ScrollView>

        <Portal>
          <Modal
            visible={visible}
            onDismiss={() => setVisible(false)}
            contentContainerStyle={styles.modalContent}
          >
            <ScrollView>
              <Title>Library Settings</Title>

              <Title style={styles.sectionTitle}>Working Hours</Title>
              {Object.entries(formData.workingHours).map(([day, hours]) => (
                <View key={day} style={styles.workingHoursInput}>
                  <Text style={styles.dayText}>
                    {day.charAt(0).toUpperCase() + day.slice(1)}
                  </Text>

                  <CustomButton
                    mode={hours.closed ? 'contained' : 'outlined'}
                    onPress={() => toggleDayClosed(day)}
                    style={styles.closedButton}
                  >
                    {hours.closed ? 'Closed' : 'Open'}
                  </CustomButton>

                  {!hours.closed && (
                    <>
                      <CustomInput
                        label="Start Time"
                        value={hours.start}
                        onChangeText={(text) => setFormData({
                          ...formData,
                          workingHours: {
                            ...formData.workingHours,
                            [day]: { ...hours, start: text },
                          },
                        })}
                      />

                      <CustomInput
                        label="End Time"
                        value={hours.end}
                        onChangeText={(text) => setFormData({
                          ...formData,
                          workingHours: {
                            ...formData.workingHours,
                            [day]: { ...hours, end: text },
                          },
                        })}
                      />
                    </>
                  )}
                </View>
              ))}

              <Title style={styles.sectionTitle}>Location</Title>
              <CustomInput
                label="Building"
                value={formData.location.building}
                onChangeText={(text) => setFormData({
                  ...formData,
                  location: { ...formData.location, building: text },
                })}
              />

              <CustomInput
                label="Floor"
                value={formData.location.floor}
                onChangeText={(text) => setFormData({
                  ...formData,
                  location: { ...formData.location, floor: text },
                })}
              />

              <CustomInput
                label="Room"
                value={formData.location.room}
                onChangeText={(text) => setFormData({
                  ...formData,
                  location: { ...formData.location, room: text },
                })}
              />

              <Title style={styles.sectionTitle}>Borrowing Rules</Title>
              <CustomInput
                label="Maximum Borrow Days"
                value={formData.maxBorrowDays}
                onChangeText={(text) => setFormData({ ...formData, maxBorrowDays: text })}
                keyboardType="numeric"
              />

              <CustomInput
                label="Maximum Books per Student"
                value={formData.maxBooksPerStudent}
                onChangeText={(text) => setFormData({ ...formData, maxBooksPerStudent: text })}
                keyboardType="numeric"
              />

              <CustomInput
                label="Maximum Books per Teacher"
                value={formData.maxBooksPerTeacher}
                onChangeText={(text) => setFormData({ ...formData, maxBooksPerTeacher: text })}
                keyboardType="numeric"
              />

              <CustomInput
                label="Fine per Day (ETB)"
                value={formData.finePerDay}
                onChangeText={(text) => setFormData({ ...formData, finePerDay: text })}
                keyboardType="numeric"
              />

              <View style={styles.modalButtons}>
                <CustomButton
                  mode="contained"
                  onPress={handleUpdateSettings}
                  loading={loading}
                >
                  Save Settings
                </CustomButton>

                <CustomButton
                  mode="outlined"
                  onPress={() => setVisible(false)}
                >
                  Cancel
                </CustomButton>
              </View>
            </ScrollView>
          </Modal>

          <Modal
            visible={categoryModalVisible}
            onDismiss={() => {
              setCategoryModalVisible(false);
              resetCategoryForm();
            }}
            contentContainerStyle={styles.modalContent}
          >
            <ScrollView>
              <Title>{selectedCategory ? 'Edit Category' : 'Add New Category'}</Title>

              <CustomInput
                label="Category Name"
                value={categoryForm.name}
                onChangeText={(text) => setCategoryForm({ ...categoryForm, name: text })}
              />

              <CustomInput
                label="Category Code"
                value={categoryForm.code}
                onChangeText={(text) => setCategoryForm({ ...categoryForm, code: text })}
              />

              <CustomInput
                label="Description"
                value={categoryForm.description}
                onChangeText={(text) => setCategoryForm({ ...categoryForm, description: text })}
                multiline
                numberOfLines={3}
              />

              <CustomInput
                label="Shelf Location"
                value={categoryForm.shelfLocation}
                onChangeText={(text) => setCategoryForm({ ...categoryForm, shelfLocation: text })}
              />

              <CustomInput
                label="Age Group"
                value={categoryForm.ageGroup}
                onChangeText={(text) => setCategoryForm({ ...categoryForm, ageGroup: text })}
              />

              <CustomInput
                label="Maximum Borrow Days"
                value={categoryForm.maxBorrowDays}
                onChangeText={(text) => setCategoryForm({ ...categoryForm, maxBorrowDays: text })}
                keyboardType="numeric"
              />

              <View style={styles.modalButtons}>
                <CustomButton
                  mode="contained"
                  onPress={selectedCategory ? handleUpdateCategory : handleAddCategory}
                  loading={loading}
                >
                  {selectedCategory ? 'Update' : 'Add'}
                </CustomButton>

                {selectedCategory && (
                  <CustomButton
                    mode="outlined"
                    onPress={() => handleDeleteCategory(selectedCategory.id)}
                    style={styles.deleteButton}
                  >
                    Delete
                  </CustomButton>
                )}

                <CustomButton
                  mode="outlined"
                  onPress={() => {
                    setCategoryModalVisible(false);
                    resetCategoryForm();
                  }}
                >
                  Cancel
                </CustomButton>
              </View>
            </ScrollView>
          </Modal>
        </Portal>

        {/* Confirmation Dialog */}
        <Portal>
          <Modal
            visible={confirmDialogVisible}
            onDismiss={handleConfirmDialogDismiss}
            contentContainerStyle={styles.confirmDialog}
          >
            <View style={styles.confirmDialogContent}>
              <Title style={styles.confirmDialogTitle}>{confirmDialogTitle}</Title>
              <Text style={styles.confirmDialogMessage}>{confirmDialogMessage}</Text>

              <View style={styles.confirmDialogActions}>
                <Button
                  mode="outlined"
                  onPress={handleConfirmDialogDismiss}
                  style={styles.confirmDialogButton}
                >
                  {translate('common.cancel') || 'Cancel'}
                </Button>
                <Button
                  mode="contained"
                  onPress={handleConfirmDialogConfirm}
                  style={[styles.confirmDialogButton, { marginLeft: 8 }]}
                >
                  {translate('common.confirm') || 'Confirm'}
                </Button>
              </View>
            </View>
          </Modal>
        </Portal>

        {/* Snackbar for notifications */}
        <Snackbar
          visible={snackbarVisible}
          onDismiss={() => setSnackbarVisible(false)}
          duration={3000}
          action={{
            label: translate('common.dismiss') || 'Dismiss',
            onPress: () => setSnackbarVisible(false),
          }}
          style={styles.snackbar}
        >
          {snackbarMessage}
        </Snackbar>
      </SafeAreaView>
    </Provider>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
    paddingTop: 8,
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  loader: {
    marginLeft: 16,
  },
  card: {
    margin: 16,
    borderRadius: 8,
    elevation: 4,
    overflow: 'hidden',
    backgroundColor: '#ffffff',
  },
  section: {
    marginVertical: 15,
    padding: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333',
  },
  workingHoursRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: 5,
    padding: 8,
    backgroundColor: '#f9f9f9',
    borderRadius: 4,
  },
  workingHoursInput: {
    marginVertical: 10,
    padding: 8,
    backgroundColor: '#f9f9f9',
    borderRadius: 4,
  },
  dayText: {
    fontWeight: 'bold',
    marginBottom: 5,
    color: '#555',
  },
  closedButton: {
    marginVertical: 5,
  },
  modalContent: {
    backgroundColor: '#ffffff',
    padding: 20,
    margin: 20,
    borderRadius: 10,
    maxHeight: '80%',
  },
  modalButtons: {
    marginTop: 20,
    flexDirection: 'row',
    justifyContent: 'space-between',
    flexWrap: 'wrap',
  },
  addButton: {
    marginTop: 10,
  },
  editButton: {
    marginTop: 20,
  },
  deleteButton: {
    marginVertical: 10,
    backgroundColor: '#FFEBEE',
  },
  confirmDialog: {
    backgroundColor: '#ffffff',
    borderRadius: 8,
    padding: 20,
    margin: 20,
    maxWidth: 500,
    alignSelf: 'center',
  },
  confirmDialogContent: {
    alignItems: 'center',
  },
  confirmDialogTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
    textAlign: 'center',
  },
  confirmDialogMessage: {
    fontSize: 14,
    marginBottom: 20,
    textAlign: 'center',
    color: '#666',
  },
  confirmDialogActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    width: '100%',
    marginTop: 16,
  },
  confirmDialogButton: {
    minWidth: 100,
  },
  snackbar: {
    bottom: 16,
  },
});

export default LibrarySettings;
