# Firebase Permissions Fix

This document provides instructions for fixing the "Missing or insufficient permissions" error that occurs when trying to access the timetables collection in Firestore.

## Problem

The error occurs because the Firestore security rules are not properly configured to allow access to the timetables collection for different user roles (teachers, students, parents).

## Solution

We've implemented several fixes to address this issue:

1. Created Firestore security rules that properly allow access to the timetables collection
2. Added error handling to the OngoingClassTracker and DashboardClassWidget components
3. Created a script to update timetable entries to ensure they have the required fields

## Implementation Steps

### 1. Deploy Firestore Security Rules

The `firestore.rules` file contains the security rules that need to be deployed to Firebase. To deploy these rules:

```bash
# Install Firebase CLI if you haven't already
npm install -g firebase-tools

# Login to Firebase
firebase login

# Deploy the rules
firebase deploy --only firestore:rules
```

### 2. Run the Timetable Update Script

The `src/scripts/updateTimetables.js` script updates all timetable entries to ensure they have the required fields, including the `published` field which is needed for the security rules to work properly.

```bash
# Run the script
node src/scripts/updateTimetables.js
```

### 3. Verify the Changes

After deploying the security rules and running the update script, the "Missing or insufficient permissions" error should be resolved. You can verify this by:

1. Logging in as a teacher, student, or parent
2. Navigating to the dashboard to see if the class schedule widget loads properly
3. Checking the class schedule screen to see if it displays the schedule correctly

## Security Rules Explanation

The security rules in `firestore.rules` implement the following permissions:

- **Teachers** can access timetable entries where they are the assigned teacher
- **Students** can access timetable entries for their class and section
- **Parents** can access timetable entries for their children's classes
- **Admins** have full access to all collections

## Troubleshooting

If you still encounter permission errors after following these steps:

1. Check the Firebase console to ensure the security rules were deployed correctly
2. Verify that the timetable entries have the required fields (published, teacherId, classId, sectionName)
3. Make sure the user is authenticated and has the correct role assigned
4. Check the browser console for specific error messages that might provide more details

## Additional Notes

- The error handling in the components will now display more specific error messages to help diagnose issues
- The OngoingClassTracker component has been updated to handle errors gracefully and continue functioning even if there are permission issues
- The DashboardClassWidget component now performs additional validation to ensure all required parameters are provided
