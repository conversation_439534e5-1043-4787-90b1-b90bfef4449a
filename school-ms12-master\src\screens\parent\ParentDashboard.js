import React, { useState, useEffect, useRef } from 'react';
import { View, ScrollView, StyleSheet, Dimensions, TouchableOpacity, Text, Alert, ImageBackground, RefreshControl, Image, StatusBar } from 'react-native';
import { Card, Title, Paragraph, IconButton, Portal, Dialog, Button, Avatar, Badge, Divider, List, Chip, ActivityIndicator, Searchbar, FAB, Surface, useTheme } from 'react-native-paper';
import { useNavigation } from '@react-navigation/native';
import { signOut } from 'firebase/auth';
import { auth, db } from '../../config/firebase';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, ProgressChart } from 'react-native-chart-kit';
import { doc, getDoc, collection, query, where, getDocs, orderBy, limit, updateDoc } from 'firebase/firestore';
import { LinearGradient } from 'expo-linear-gradient';
import * as Animatable from 'react-native-animatable';
import { useLanguage } from '../../context/LanguageContext';
import NotificationService from '../../services/NotificationService';
import MessagingFAB from '../../components/common/MessagingFAB';
import DashboardClassWidget from '../../components/common/DashboardClassWidget';
import { sanitizeChartData, sanitizeChartDatasets } from '../../utils/ChartUtils';
import ParentAppHeader from '../../components/common/ParentAppHeader';
import ParentSidebar from '../../components/common/ParentSidebar';

// Child's Today Schedule Widget Component
const ChildTodayScheduleWidget = ({ childId }) => {
  const [loading, setLoading] = useState(true);
  const [todaySchedule, setTodaySchedule] = useState([]);
  const [error, setError] = useState('');
  const [childInfo, setChildInfo] = useState(null);
  const [subjectNames, setSubjectNames] = useState({});
  const [teacherNames, setTeacherNames] = useState({});
  const navigation = useNavigation();
  const { translate } = useLanguage();
  const theme = useTheme();

  // Get today's day name
  const getDayName = () => {
    const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    return days[new Date().getDay()];
  };

  useEffect(() => {
    if (childId) {
      fetchChildInfo();
    } else {
      setError(translate('parent.classSchedule.childIdMissing') || 'Child ID is missing');
      setLoading(false);
    }
  }, [childId]);

  const fetchChildInfo = async () => {
    try {
      if (!childId) {
        setError(translate('parent.classSchedule.childIdMissing') || 'Child ID is missing');
        setLoading(false);
        return;
      }

      // Get child's information
      const childRef = doc(db, 'users', childId);
      const childDoc = await getDoc(childRef);

      if (!childDoc.exists()) {
        setError(translate('parent.classSchedule.childInfoNotFound') || 'Child information not found');
        setLoading(false);
        return;
      }

      const childData = childDoc.data();
      setChildInfo(childData);

      // Fetch schedule using child's class and section
      if (childData.classId && childData.section) {
        fetchTodaySchedule(childData.classId, childData.section);
      } else {
        setError(translate('parent.classSchedule.noClassAssigned') || 'Child has no class or section assigned');
        setLoading(false);
      }
    } catch (error) {
      console.error('Error fetching child info:', error);
      setError(translate('parent.classSchedule.failedToLoadInfo') || 'Failed to load child information. Please try again.');
      setLoading(false);
    }
  };

  const fetchTodaySchedule = async (classId, section) => {
    try {
      setLoading(true);

      if (!classId || !section) {
        setError(translate('parent.classSchedule.classInfoMissing') || 'Class or section information missing');
        setLoading(false);
        return;
      }

      const today = getDayName();

      const timetableRef = collection(db, 'timetables');
      const timetableQuery = query(
        timetableRef,
        where('classId', '==', classId),
        where('sectionName', '==', section),
        where('day', '==', today),
        where('published', '==', true)
      );

      const querySnapshot = await getDocs(timetableQuery);

      const scheduleData = [];
      const teacherIds = new Set();
      const subjectIds = new Set();

      querySnapshot.forEach((doc) => {
        const data = doc.data();
        scheduleData.push({ id: doc.id, ...data });

        if (data.teacherId) teacherIds.add(data.teacherId);
        if (data.subjectId) subjectIds.add(data.subjectId);
      });

      // Sort by start time
      scheduleData.sort((a, b) => {
        const timeA = a.startTime.split(':').map(Number);
        const timeB = b.startTime.split(':').map(Number);

        if (timeA[0] !== timeB[0]) {
          return timeA[0] - timeB[0];
        }
        return timeA[1] - timeB[1];
      });

      setTodaySchedule(scheduleData);

      // Fetch subject and teacher names in parallel
      await Promise.all([
        fetchSubjectNames(Array.from(subjectIds)),
        fetchTeacherNames(Array.from(teacherIds))
      ]);

      setLoading(false);
    } catch (error) {
      console.error('Error fetching today\'s schedule:', error);
      setError(translate('parent.classSchedule.failedToLoadSchedule') || 'Failed to load schedule. Please try again.');
      setLoading(false);
    }
  };

  // Fetch subject names from Firestore
  const fetchSubjectNames = async (subjectIds) => {
    if (!subjectIds.length) return;

    try {
      const subjectData = {};

      // Fetch subjects in batches to avoid large queries
      for (let i = 0; i < subjectIds.length; i += 10) {
        const batch = subjectIds.slice(i, i + 10);
        const subjectsRef = collection(db, 'subjects');
        const subjectsQuery = query(subjectsRef, where('id', 'in', batch));
        const subjectsSnapshot = await getDocs(subjectsQuery);

        subjectsSnapshot.forEach(doc => {
          const data = doc.data();
          subjectData[data.id] = data.name || data.subjectName || data.id;
        });
      }

      setSubjectNames(subjectData);
    } catch (error) {
      console.error('Error fetching subject names:', error);
    }
  };

  // Fetch teacher names from Firestore
  const fetchTeacherNames = async (teacherIds) => {
    if (!teacherIds.length) return;

    try {
      const teacherData = {};

      // Fetch teachers in batches to avoid large queries
      for (let i = 0; i < teacherIds.length; i += 10) {
        const batch = teacherIds.slice(i, i + 10);
        const usersRef = collection(db, 'users');
        const teachersQuery = query(usersRef, where('uid', 'in', batch));
        const teachersSnapshot = await getDocs(teachersQuery);

        teachersSnapshot.forEach(doc => {
          const data = doc.data();
          teacherData[data.uid] = `${data.firstName || ''} ${data.lastName || ''}`.trim() || data.uid;
        });
      }

      setTeacherNames(teacherData);
    } catch (error) {
      console.error('Error fetching teacher names:', error);
    }
  };

  // Get subject name by ID
  const getSubjectName = (subjectId) => {
    return subjectNames[subjectId] || subjectId || translate('common.unknownSubject') || 'Unknown Subject';
  };

  // Get teacher name by ID
  const getTeacherName = (teacherId) => {
    return teacherNames[teacherId] || teacherId || translate('common.unknownTeacher') || 'Unknown Teacher';
  };

  // Format time for display (12-hour format)
  const formatTime = (timeString) => {
    if (!timeString) return '';

    const [hours, minutes] = timeString.split(':').map(Number);
    const period = hours >= 12 ? 'PM' : 'AM';
    const displayHours = hours % 12 || 12; // Convert 0 to 12 for 12 AM

    return `${displayHours}:${minutes.toString().padStart(2, '0')} ${period}`;
  };

  if (loading) {
    return (
      <Animatable.View
        animation="pulse"
        iterationCount="infinite"
        duration={1500}
        style={styles.scheduleLoadingContainer}
      >
        <ActivityIndicator size="small" color={theme.colors.primary} />
        <Text style={styles.scheduleLoadingText}>
          {translate('parent.classSchedule.loadingSchedule') || "Loading today's schedule..."}
        </Text>
      </Animatable.View>
    );
  }

  if (error) {
    return (
      <Animatable.View
        animation="fadeIn"
        duration={500}
        style={styles.scheduleErrorContainer}
      >
        <IconButton icon="alert-circle-outline" size={32} color={theme.colors.error} />
        <Text style={styles.scheduleErrorText}>{error}</Text>
        <Button
          mode="contained"
          onPress={fetchChildInfo}
          style={styles.retryButton}
          icon="refresh"
        >
          {translate('common.retry') || 'Retry'}
        </Button>
      </Animatable.View>
    );
  }

  if (todaySchedule.length === 0) {
    return (
      <Animatable.View
        animation="fadeIn"
        duration={500}
        style={styles.scheduleEmptyContainer}
      >
        <IconButton icon="calendar-blank" size={40} color="#9e9e9e" />
        <Text style={styles.scheduleEmptyText}>
          {translate('parent.classSchedule.noClassesToday') || 'No classes scheduled for today'}
        </Text>
        <Button
          mode="contained"
          onPress={() => navigation.navigate('ParentClassSchedule')}
          style={styles.scheduleEmptyButton}
          icon="calendar-month"
        >
          {translate('parent.classSchedule.viewFullSchedule') || 'View Full Schedule'}
        </Button>
      </Animatable.View>
    );
  }

  return (
    <Animatable.View
      animation="fadeIn"
      duration={800}
      style={styles.todayScheduleContainer}
    >
      <View style={styles.todayHeaderContainer}>
        <View style={styles.todayHeaderLeft}>
          <IconButton icon="calendar-today" size={24} color={theme.colors.primary} style={styles.todayIcon} />
          <Text style={[styles.todayText, { color: theme.colors.primary }]}>
            {translate('parent.classSchedule.today') || 'Today'}: {getDayName()}
          </Text>
        </View>
        <Chip
          mode="outlined"
          style={styles.totalClassesChip}
        >
          {todaySchedule.length} {translate('parent.classSchedule.classes') || 'Classes'}
        </Chip>
      </View>

      {todaySchedule.map((entry, index) => {
        const now = new Date();
        const currentHour = now.getHours();
        const currentMinute = now.getMinutes();

        const [startHour, startMinute] = entry.startTime.split(':').map(Number);
        const [endHour, endMinute] = entry.endTime.split(':').map(Number);

        const isCurrentClass =
          (currentHour > startHour || (currentHour === startHour && currentMinute >= startMinute)) &&
          (currentHour < endHour || (currentHour === endHour && currentMinute < endMinute));

        const isPastClass =
          currentHour > endHour || (currentHour === endHour && currentMinute >= endMinute);

        const isUpcomingClass = !isCurrentClass && !isPastClass;

        // Calculate progress percentage for current class
        let progressPercent = 0;
        if (isCurrentClass) {
          const totalClassMinutes = (endHour - startHour) * 60 + (endMinute - startMinute);
          const elapsedMinutes = (currentHour - startHour) * 60 + (currentMinute - startMinute);
          progressPercent = Math.min(100, Math.max(0, (elapsedMinutes / totalClassMinutes) * 100));
        }

        return (
          <Animatable.View
            key={entry.id}
            animation="fadeInUp"
            duration={500}
            delay={index * 100}
          >
            <Surface
              style={[
                styles.scheduleItemCard,
                isCurrentClass && styles.currentClassCard,
                isPastClass && styles.pastClassCard,
                isUpcomingClass && styles.upcomingClassCard
              ]}
            >
              <View style={styles.scheduleItemContent}>
                <View style={styles.scheduleTimeContainer}>
                  <View style={styles.scheduleTimeWrapper}>
                    <IconButton
                      icon={isCurrentClass ? "clock" : isPastClass ? "clock-check" : "clock-time-four"}
                      size={20}
                      color={isCurrentClass ? "#1976D2" : isPastClass ? "#757575" : "#4CAF50"}
                      style={styles.scheduleTimeIcon}
                    />
                    <Text style={[
                      styles.scheduleTimeText,
                      isCurrentClass && styles.currentTimeText,
                      isPastClass && styles.pastTimeText
                    ]}>
                      {formatTime(entry.startTime)} - {formatTime(entry.endTime)}
                    </Text>
                  </View>
                  <Chip
                    mode="flat"
                    style={[
                      styles.scheduleStatusChip,
                      isCurrentClass && styles.currentClassChip,
                      isPastClass && styles.pastClassChip,
                      isUpcomingClass && styles.upcomingClassChip
                    ]}
                    textStyle={styles.scheduleStatusText}
                  >
                    {isCurrentClass
                      ? (translate('parent.classSchedule.current') || 'Current')
                      : isPastClass
                        ? (translate('parent.classSchedule.completed') || 'Completed')
                        : (translate('parent.classSchedule.upcoming') || 'Upcoming')}
                  </Chip>
                </View>

                {isCurrentClass && (
                  <View style={styles.progressBarContainer}>
                    <View style={styles.progressBar}>
                      <View style={[styles.progressFill, { width: `${progressPercent}%` }]} />
                    </View>
                    <Text style={styles.progressText}>{Math.round(progressPercent)}% {translate('parent.classSchedule.complete') || 'complete'}</Text>
                  </View>
                )}

                <View style={styles.scheduleDetailsContainer}>
                  <Text style={[
                    styles.scheduleSubjectText,
                    isPastClass && styles.pastSubjectText
                  ]}>
                    {getSubjectName(entry.subjectId)}
                  </Text>

                  <View style={styles.scheduleDetailRow}>
                    <Avatar.Icon
                      size={24}
                      icon="account"
                      style={styles.detailIcon}
                      color="#fff"
                    />
                    <Text style={[
                      styles.scheduleTeacherText,
                      isPastClass && styles.pastDetailText
                    ]}>
                      {translate('parent.classSchedule.teacher') || 'Teacher'}: {getTeacherName(entry.teacherId)}
                    </Text>
                  </View>

                  <View style={styles.scheduleDetailRow}>
                    <Avatar.Icon
                      size={24}
                      icon="door"
                      style={styles.detailIcon}
                      color="#fff"
                    />
                    <Text style={[
                      styles.scheduleRoomText,
                      isPastClass && styles.pastDetailText
                    ]}>
                      {translate('parent.classSchedule.room') || 'Room'}: {entry.roomNumber || 'N/A'}
                    </Text>
                  </View>
                </View>

                <View style={styles.scheduleActionsContainer}>
                  <Button
                    mode="outlined"
                    icon="message-text"
                    onPress={() => navigation.navigate('ParentMessaging', {
                      teacherId: entry.teacherId,
                      teacherName: getTeacherName(entry.teacherId),
                      subjectName: getSubjectName(entry.subjectId),
                      childId: childId,
                      childName: `${childInfo?.firstName || ''} ${childInfo?.lastName || ''}`.trim()
                    })}
                    style={[
                      styles.contactTeacherButton,
                      isPastClass && styles.pastActionButton
                    ]}
                    labelStyle={isPastClass ? styles.pastActionButtonLabel : null}
                  >
                    {translate('parent.classSchedule.contactTeacher') || 'Contact Teacher'}
                  </Button>
                </View>
              </View>
            </Surface>
          </Animatable.View>
        );
      })}
    </Animatable.View>
  );
};

const screenWidth = Dimensions.get('window').width;

// Enhanced chart configuration with better colors and styling
const chartConfig = {
  backgroundColor: 'transparent',
  backgroundGradientFrom: '#ffffff',
  backgroundGradientTo: '#ffffff',
  decimalPlaces: 0,
  color: (opacity = 1) => `rgba(33, 150, 243, ${opacity})`,
  labelColor: (opacity = 1) => `rgba(51, 51, 51, ${opacity})`,
  style: {
    borderRadius: 16
  },
  propsForDots: {
    r: '6',
    strokeWidth: '2',
    stroke: '#ffa726'
  },
  propsForLabels: {
    fontSize: 12,
    fontWeight: '500'
  }
};

// Enhanced academic progress data with more subjects and better visualization
const createAcademicProgressData = (childId) => {
  // In a real implementation, this would fetch data from Firestore
  // For now, we'll use sample data with some randomization based on childId

  // Use the last character of childId to create some variation
  const variationSeed = childId ? parseInt(childId.slice(-1), 16) % 10 : 5;

  return {
    labels: ['Math', 'Science', 'English', 'History', 'Art', 'PE'],
    datasets: [
      {
        data: sanitizeChartData([
          85 + variationSeed % 5,
          78 + variationSeed % 7,
          92 - variationSeed % 4,
          88 + variationSeed % 3,
          90 - variationSeed % 6,
          95 - variationSeed % 5
        ]),
        color: (opacity = 1) => `rgba(33, 150, 243, ${opacity})`,
        strokeWidth: 2
      },
      {
        data: sanitizeChartData([
          75 + variationSeed % 3,
          72 + variationSeed % 4,
          80 + variationSeed % 2,
          78 + variationSeed % 5,
          85 - variationSeed % 3,
          90 - variationSeed % 4
        ]),
        color: (opacity = 1) => `rgba(156, 39, 176, ${opacity})`,
        strokeWidth: 2
      }
    ],
    legend: ['Current Score', 'Class Average']
  };
};

// Enhanced attendance data with better visualization
const createAttendanceData = (childId) => {
  // In a real implementation, this would fetch data from Firestore
  // For now, we'll use sample data with some randomization based on childId

  // Use the last character of childId to create some variation
  const variationSeed = childId ? parseInt(childId.slice(-1), 16) % 10 : 5;

  const currentMonth = new Date().getMonth();
  const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

  // Get the last 5 months including current month
  const labels = [];
  for (let i = 4; i >= 0; i--) {
    const monthIndex = (currentMonth - i + 12) % 12;
    labels.push(monthNames[monthIndex]);
  }

  return {
    labels,
    datasets: [{
      data: sanitizeChartData([
        95 - variationSeed % 5,
        92 + variationSeed % 3,
        96 - variationSeed % 2,
        94 + variationSeed % 4,
        98 - variationSeed % 3
      ]),
      color: (opacity = 1) => `rgba(76, 175, 80, ${opacity})`,
      strokeWidth: 2
    }],
    legend: ['Monthly Attendance %']
  };
};

// Enhanced behavior data with better visualization
const createBehaviorData = (childId) => {
  // In a real implementation, this would fetch data from Firestore
  // For now, we'll use sample data with some randomization based on childId

  // Use the last character of childId to create some variation
  const variationSeed = childId ? parseInt(childId.slice(-1), 16) % 10 : 5;

  return [
    {
      name: 'Excellent',
      value: 65 + variationSeed % 10,
      color: '#4CAF50',
      legendFontColor: '#333333',
      legendFontSize: 12
    },
    {
      name: 'Good',
      value: 20 - variationSeed % 5,
      color: '#8BC34A',
      legendFontColor: '#333333',
      legendFontSize: 12
    },
    {
      name: 'Average',
      value: 10 + variationSeed % 5,
      color: '#FFC107',
      legendFontColor: '#333333',
      legendFontSize: 12
    },
    {
      name: 'Needs Improvement',
      value: 5 + variationSeed % 3,
      color: '#FF5722',
      legendFontColor: '#333333',
      legendFontSize: 12
    }
  ];
};

// Progress chart data for overall performance
const createProgressData = (childId) => {
  // In a real implementation, this would fetch data from Firestore
  // For now, we'll use sample data with some randomization based on childId

  // Use the last character of childId to create some variation
  const variationSeed = childId ? parseInt(childId.slice(-1), 16) % 10 : 5;

  return {
    labels: ["Academics", "Attendance", "Behavior", "Activities"],
    data: [
      (85 + variationSeed % 10) / 100,
      (95 - variationSeed % 5) / 100,
      (90 + variationSeed % 5) / 100,
      (80 - variationSeed % 10) / 100
    ]
  };
};

// Sample upcoming events data
const createUpcomingEvents = (childId) => {
  // In a real implementation, this would fetch data from Firestore
  // For now, we'll use sample data

  const today = new Date();
  const tomorrow = new Date(today);
  tomorrow.setDate(tomorrow.getDate() + 1);

  const nextWeek = new Date(today);
  nextWeek.setDate(nextWeek.getDate() + 7);

  const twoWeeks = new Date(today);
  twoWeeks.setDate(twoWeeks.getDate() + 14);

  return [
    {
      id: '1',
      title: 'Math Quiz',
      date: tomorrow,
      type: 'exam',
      description: 'Chapter 5: Algebra Fundamentals'
    },
    {
      id: '2',
      title: 'Parent-Teacher Meeting',
      date: nextWeek,
      type: 'meeting',
      description: 'End of term progress discussion'
    },
    {
      id: '3',
      title: 'Science Project Due',
      date: twoWeeks,
      type: 'assignment',
      description: 'Ecosystem model presentation'
    },
    {
      id: '4',
      title: 'School Sports Day',
      date: new Date(today.getFullYear(), today.getMonth(), today.getDate() + 10),
      type: 'event',
      description: 'Annual athletics competition'
    }
  ];
};

// Parent menu items with comprehensive categories and items
// Function to get parent menu items with translations
const getParentMenuItems = (translate) => {
  console.log('Generating menu items with current translations');

  return [
    {
      category: translate('parent.menu.academicMonitoring') || 'Academic Monitoring',
      items: [
        { title: translate('parent.menu.academicProgress') || 'Academic Progress', icon: 'chart-line', route: 'StudentProgressReport' },
        { title: translate('parent.menu.viewGrades') || 'View Grades', icon: 'clipboard-check', route: 'ParentChildGrades' },
        { title: translate('parent.menu.attendanceRecords') || 'Attendance Records', icon: 'calendar-check', route: 'ParentAttendanceView' },
        { title: translate('parent.menu.examSchedule') || 'Exam Schedule', icon: 'calendar-text', route: 'ParentExamSchedule' },
        { title: translate('parent.menu.classSchedule') || 'Class Schedule', icon: 'calendar-week', route: 'ParentClassSchedule' },
        { title: translate('parent.menu.homeworkStatus') || 'Homework Status', icon: 'book-open-page-variant', route: 'ChildProgress' },
        { title: translate('parent.menu.subjectPerformance') || 'Subject Performance', icon: 'chart-bar', route: 'StudentProgressReport' },
        { title: translate('parent.menu.behaviorRecords') || 'Behavior Records', icon: 'clipboard-account', route: 'BehaviorView' },
      ]
    },
    {
      category: translate('parent.menu.communication') || 'Communication',
      items: [
        { title: translate('parent.menu.messageCenter') || 'Message Center', icon: 'message-text', route: 'ChatList' },
        { title: translate('parent.menu.schoolAnnouncements') || 'School Announcements', icon: 'bullhorn', route: 'ParentAnnouncements' },
        { title: translate('parent.menu.parentTeacherMeetings') || 'Parent-Teacher Meetings', icon: 'account-group', route: 'MeetingScheduler' },
        { title: translate('parent.menu.eventCalendar') || 'Event Calendar', icon: 'calendar-month', route: 'SchoolEventCalendar' },
        { title: translate('parent.menu.notifications') || 'Notifications', icon: 'bell', route: 'Notifications' },
      ]
    },
    {
      category: translate('parent.menu.settings') || 'Settings',
      items: [
        { title: translate('parent.menu.profileManagement') || 'Profile Management', icon: 'account-cog', route: 'ProfileManagement' },
      ]
    }
  ];
};

const ParentDashboard = () => {
  // Theme and language hooks
  const theme = useTheme();
  const { translate, isRTL, language } = useLanguage();
  const navigation = useNavigation();

  // Debug log for translation
  useEffect(() => {
    console.log('Current language:', language);
    console.log('Translation test:', translate('parent.dashboard.title'));
    console.log('Translation test menu:', translate('parent.menu.academicMonitoring'));
  }, [language, translate]);

  // UI state
  const [logoutDialogVisible, setLogoutDialogVisible] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [showAllItems, setShowAllItems] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');
  const [menuItems, setMenuItems] = useState(getParentMenuItems(translate));
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [activeSidebarItem, setActiveSidebarItem] = useState('ParentDashboard');

  // User data state
  const [userData, setUserData] = useState(null);
  const [notifications, setNotifications] = useState([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [isLoading, setIsLoading] = useState(false);

  // Children data state
  const [children, setChildren] = useState([]);
  const [selectedChildId, setSelectedChildId] = useState(null);
  const [selectedChildData, setSelectedChildData] = useState(null);
  const [childrenProfiles, setChildrenProfiles] = useState({});

  // Academic data state
  const [academicData, setAcademicData] = useState(null);
  const [attendanceData, setAttendanceData] = useState(null);
  const [behaviorData, setBehaviorData] = useState(null);
  const [progressData, setProgressData] = useState(null);
  const [upcomingEvents, setUpcomingEvents] = useState([]);

  // Animation refs
  const headerAnimRef = useRef(null);
  const childSelectorRef = useRef(null);
  const contentRef = useRef(null);

  // Initial data loading
  useEffect(() => {
    loadInitialData();

    // Set up notification listeners
    const notificationListener = NotificationService.addNotificationListener(
      notification => {
        handleNotification(notification);
      }
    );

    const responseListener = NotificationService.addNotificationResponseListener(
      response => {
        handleNotificationResponse(response);
      }
    );

    // Clean up listeners
    return () => {
      NotificationService.removeNotificationListener(notificationListener);
      NotificationService.removeNotificationListener(responseListener);
    };
  }, []);

  // Effect to update data when selected child changes
  useEffect(() => {
    if (selectedChildId) {
      fetchChildData(selectedChildId);
    }
  }, [selectedChildId]);

  // Effect to update menu items when language changes
  useEffect(() => {
    console.log('Language changed, updating menu items');

    // Update menu items when language changes
    const updatedMenuItems = getParentMenuItems(translate);
    setMenuItems(updatedMenuItems);
  }, [translate, language]);

  // Toggle drawer function
  const toggleDrawer = () => {
    setDrawerVisible(!drawerVisible);
    console.log('Drawer toggled:', !drawerVisible);
  };

  // Load all initial data
  const loadInitialData = async () => {
    try {
      setIsLoading(true);
      await Promise.all([
        fetchUserData(),
        fetchNotifications()
      ]);
    } catch (error) {
      console.error('Error loading initial data:', error);
      Alert.alert(
        translate('common.error') || 'Error',
        translate('dashboard.errorLoadingData') || 'Failed to load dashboard data. Please try again.'
      );
    } finally {
      setIsLoading(false);
    }
  };

  // Handle pull-to-refresh
  const onRefresh = async () => {
    try {
      setRefreshing(true);
      await Promise.all([
        fetchUserData(),
        fetchNotifications(),
        selectedChildId ? fetchChildData(selectedChildId) : Promise.resolve()
      ]);
    } catch (error) {
      console.error('Error refreshing data:', error);
    } finally {
      setRefreshing(false);
    }
  };

  // Fetch user data and children
  const fetchUserData = async () => {
    try {
      const user = auth.currentUser;
      if (!user) {
        console.log('No authenticated user found');
        return;
      }

      // Get user data
      const userDoc = await getDoc(doc(db, 'users', user.uid));
      if (userDoc.exists()) {
        const userData = userDoc.data();
        setUserData(userData);

        // Get children IDs from the parent document
        const childrenIds = userData.children || [];

        if (childrenIds.length === 0) {
          console.log('No children found for this parent');
          setChildren([]);
          setChildrenProfiles({});

          // Show message to user
          Alert.alert(
            translate('parent.dashboard.noChildrenTitle') || 'No Children Found',
            translate('parent.dashboard.noChildrenMessage') || 'No children are associated with your account. Please contact the administrator.',
            [{ text: translate('common.ok') || 'OK' }]
          );
          return;
        }

        console.log(`Found ${childrenIds.length} children for parent:`, childrenIds);

        // Fetch each child's data
        const childrenData = [];
        const profiles = {};
        let fetchErrors = 0;

        for (const childId of childrenIds) {
          try {
            console.log(`Attempting to fetch data for child: ${childId}`);
            const childRef = doc(db, 'users', childId);
            const childDoc = await getDoc(childRef);

            if (childDoc.exists()) {
              const childData = { id: childId, ...childDoc.data() };
              childrenData.push(childData);
              profiles[childId] = childData;
              console.log(`Successfully fetched child data for ${childId}:`, childData.firstName);
            } else {
              console.log(`Child document not found for ID: ${childId}`);
              fetchErrors++;

              // Create placeholder data for missing child
              const placeholderData = {
                id: childId,
                firstName: 'Unknown',
                lastName: 'Child',
                displayName: 'Unknown Child',
                role: 'student'
              };
              childrenData.push(placeholderData);
              profiles[childId] = placeholderData;
            }
          } catch (childError) {
            console.error(`Error fetching child ${childId}:`, childError);
            fetchErrors++;

            // Create placeholder data for child with error
            const placeholderData = {
              id: childId,
              firstName: 'Unknown',
              lastName: 'Child',
              displayName: 'Unknown Child',
              role: 'student'
            };
            childrenData.push(placeholderData);
            profiles[childId] = placeholderData;
          }
        }

        // Show warning if there were errors
        if (fetchErrors > 0 && childrenIds.length > 0) {
          console.warn(`Encountered ${fetchErrors} errors while fetching children data`);

          // Only show alert if all children failed to load
          if (fetchErrors === childrenIds.length) {
            Alert.alert(
              translate('parent.dashboard.errorTitle') || 'Error',
              translate('parent.dashboard.childDataError') || 'There was a problem loading your children\'s data. Some information may be unavailable.',
              [{ text: translate('common.ok') || 'OK' }]
            );
          }
        }

        // Also check for students that might have this parent linked but not in the parent's children array
        // This is for backward compatibility
        try {
          console.log('Checking for backward compatibility with parent.id field...');
          const usersRef = collection(db, 'users');
          const studentsQuery = query(
            usersRef,
            where('role', '==', 'student'),
            where('parent.id', '==', user.uid)
          );
          const studentsSnapshot = await getDocs(studentsQuery);

          if (!studentsSnapshot.empty) {
            console.log(`Found ${studentsSnapshot.size} additional children via parent.id reference`);

            studentsSnapshot.forEach((doc) => {
              const childData = { id: doc.id, ...doc.data() };

              // Only add if not already in the array
              if (!childrenData.some(child => child.id === doc.id)) {
                childrenData.push(childData);
                profiles[doc.id] = childData;
                console.log(`Added additional child via parent.id reference: ${doc.id}`);

                // Update parent's children array to include this child
                updateParentChildrenArray(user.uid, doc.id);
              }
            });
          } else {
            console.log('No additional children found via parent.id reference');
          }
        } catch (backwardCompatError) {
          console.error('Error in backward compatibility check:', backwardCompatError);
          // Don't show an error to the user for this, as it's just a supplementary check
        }

        // Log the final results
        console.log(`Final children count: ${childrenData.length}`);

        // Update state with the children data
        setChildren(childrenData);
        setChildrenProfiles(profiles);

        // Select the first child by default if none is selected
        if (childrenData.length > 0 && !selectedChildId) {
          console.log(`Selecting first child by default: ${childrenData[0].id}`);
          setSelectedChildId(childrenData[0].id);
        } else if (selectedChildId && profiles[selectedChildId]) {
          console.log(`Using currently selected child: ${selectedChildId}`);
        } else {
          console.log('No children to select or display');
        }
      } else {
        console.error('Parent document not found');
        Alert.alert(
          translate('parent.dashboard.errorTitle') || 'Error',
          translate('parent.dashboard.parentDataError') || 'Your parent profile could not be found. Please contact the administrator.',
          [{ text: translate('common.ok') || 'OK' }]
        );
      }
    } catch (error) {
      console.error('Error fetching user data:', error);

      // Show error message to user
      Alert.alert(
        translate('parent.dashboard.errorTitle') || 'Error',
        translate('parent.dashboard.dataLoadError') || 'There was a problem loading your data. Please try again later.',
        [{ text: translate('common.ok') || 'OK' }]
      );

      // Set empty data to prevent crashes
      setChildren([]);
      setChildrenProfiles({});
    } finally {
      setIsLoading(false);
    }
  };

  // Helper function to update parent's children array
  const updateParentChildrenArray = async (parentId, childId) => {
    try {
      const parentRef = doc(db, 'users', parentId);
      const parentDoc = await getDoc(parentRef);

      if (parentDoc.exists()) {
        const parentData = parentDoc.data();
        const currentChildren = parentData.children || [];

        // Only add if not already in the array
        if (!currentChildren.includes(childId)) {
          const updatedChildren = [...currentChildren, childId];

          await updateDoc(parentRef, {
            children: updatedChildren,
            updatedAt: new Date().toISOString()
          });

          console.log(`Updated parent ${parentId} with missing child ${childId}`);
        }
      }
    } catch (error) {
      console.error('Error updating parent children array:', error);
    }
  };

  // Fetch notifications
  const fetchNotifications = async () => {
    try {
      const notificationData = await NotificationService.getNotifications(20);
      setNotifications(notificationData);
      const count = await NotificationService.getUnreadCount();
      setUnreadCount(count);
    } catch (error) {
      console.error('Error fetching notifications:', error);
      // Show error message to user
      setNotifications([]);
      setUnreadCount(0);
      throw error;
    }
  };

  // Fetch selected child's data
  const fetchChildData = async (childId) => {
    try {
      if (!childId) {
        console.log('No child ID provided');
        return;
      }

      console.log('Fetching data for child ID:', childId);

      // Get child's detailed information
      const childRef = doc(db, 'users', childId);
      const childDoc = await getDoc(childRef);

      if (childDoc.exists()) {
        const childData = { id: childId, ...childDoc.data() };

        // Ensure child has name property
        if (!childData.name) {
          childData.name = `${childData.firstName || ''} ${childData.lastName || ''}`.trim() || 'Unknown';
        }

        // Ensure child has classId and section properties
        if (!childData.classId && (childData.class || childData.className)) {
          childData.classId = childData.class || childData.className;
        }

        if (!childData.section && childData.sectionName) {
          childData.section = childData.sectionName;
        }

        setSelectedChildData(childData);
        console.log('Selected child data:', childData);

        // Fetch real academic data
        await fetchRealAcademicData(childId);

        // Fetch real attendance data
        await fetchRealAttendanceData(childId);

        // Fetch real behavior data
        await fetchRealBehaviorData(childId);

        // Generate progress data based on real data
        updateProgressData();

        // Fetch real upcoming events
        await fetchRealEvents(childId);
      } else {
        console.error('Child information not found for ID:', childId);
        // Create a placeholder child data object with minimal information
        const placeholderData = {
          id: childId,
          name: 'Unknown Child',
          firstName: 'Unknown',
          lastName: 'Child',
          classId: null,
          section: null
        };
        setSelectedChildData(placeholderData);

        // Show error message to user
        Alert.alert(
          translate('parent.dashboard.errorTitle') || 'Error',
          translate('parent.dashboard.childNotFound') || 'Child information not found. Please contact the administrator.',
          [{ text: translate('common.ok') || 'OK' }]
        );
      }
    } catch (error) {
      console.error('Error fetching child data:', error);

      // Create a placeholder child data object with minimal information
      const placeholderData = {
        id: childId,
        name: 'Unknown Child',
        firstName: 'Unknown',
        lastName: 'Child',
        classId: null,
        section: null
      };
      setSelectedChildData(placeholderData);

      // Show error message to user
      Alert.alert(
        translate('parent.dashboard.errorTitle') || 'Error',
        translate('parent.dashboard.errorFetchingChild') || 'Error fetching child data. Please try again later.',
        [{ text: translate('common.ok') || 'OK' }]
      );
    }
  };

  // Fetch real academic data from Firestore
  const fetchRealAcademicData = async (childId) => {
    try {
      // Get grades for the student
      const gradesRef = collection(db, 'grades');
      const gradesQuery = query(
        gradesRef,
        where('studentId', '==', childId),
        where('status', '==', 'published')
      );

      const gradesSnapshot = await getDocs(gradesQuery);

      if (gradesSnapshot.empty) {
        // If no real data, use sample data
        setAcademicData(createAcademicProgressData(childId));
        return;
      }

      // Process real grade data
      const subjects = {};
      const classAverage = {};

      gradesSnapshot.forEach(doc => {
        const gradeData = doc.data();
        const subject = gradeData.subject || 'Unknown';
        const score = parseFloat(gradeData.points) || 0;
        const maxScore = parseFloat(gradeData.maxPoints) || 100;
        const percentage = maxScore > 0 ? (score / maxScore) * 100 : 0;

        if (!subjects[subject]) {
          subjects[subject] = [];
        }

        subjects[subject].push(percentage);

        if (!classAverage[subject]) {
          classAverage[subject] = gradeData.classAverage || (percentage - 5);
        }
      });

      // Format data for chart
      const labels = Object.keys(subjects);
      const datasets = [
        {
          data: labels.map(subject => {
            const scores = subjects[subject];
            return scores.reduce((sum, score) => sum + score, 0) / scores.length;
          }),
          color: (opacity = 1) => `rgba(33, 150, 243, ${opacity})`,
          strokeWidth: 2
        },
        {
          data: labels.map(subject => classAverage[subject] || 0),
          color: (opacity = 1) => `rgba(156, 39, 176, ${opacity})`,
          strokeWidth: 2
        }
      ];

      setAcademicData({
        labels,
        datasets,
        legend: ['Current Score', 'Class Average']
      });
    } catch (error) {
      console.error('Error fetching academic data:', error);
      // Fallback to sample data
      setAcademicData(createAcademicProgressData(childId));
    }
  };

  // Fetch real attendance data from Firestore
  const fetchRealAttendanceData = async (childId) => {
    try {
      const attendanceRef = collection(db, 'attendance');
      const attendanceQuery = query(
        attendanceRef,
        where('studentId', '==', childId),
        orderBy('date', 'desc')
      );

      const attendanceSnapshot = await getDocs(attendanceQuery);

      if (attendanceSnapshot.empty) {
        // If no real data, use sample data
        setAttendanceData(createAttendanceData(childId));
        return;
      }

      // Process attendance data by month
      const attendanceByMonth = {};
      const currentMonth = new Date().getMonth();
      const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

      // Initialize last 5 months
      const labels = [];
      for (let i = 4; i >= 0; i--) {
        const monthIndex = (currentMonth - i + 12) % 12;
        labels.push(monthNames[monthIndex]);
        attendanceByMonth[monthNames[monthIndex]] = {
          present: 0,
          total: 0
        };
      }

      // Count attendance
      attendanceSnapshot.forEach(doc => {
        const data = doc.data();
        const date = data.date ?
          (typeof data.date === 'string' ? new Date(data.date) : new Date(data.date.seconds * 1000)) :
          new Date();

        const monthName = monthNames[date.getMonth()];

        // Only count months we're displaying
        if (labels.includes(monthName)) {
          attendanceByMonth[monthName].total++;

          if (data.status === 'present') {
            attendanceByMonth[monthName].present++;
          }
        }
      });

      // Calculate percentages
      const data = labels.map(month => {
        const monthData = attendanceByMonth[month];
        return monthData.total > 0 ? (monthData.present / monthData.total) * 100 : 0;
      });

      setAttendanceData({
        labels,
        datasets: [{
          data: sanitizeChartData(data),
          color: (opacity = 1) => `rgba(76, 175, 80, ${opacity})`,
          strokeWidth: 2
        }],
        legend: ['Monthly Attendance %']
      });
    } catch (error) {
      console.error('Error fetching attendance data:', error);
      // Fallback to sample data
      setAttendanceData(createAttendanceData(childId));
    }
  };

  // Fetch real behavior data from Firestore
  const fetchRealBehaviorData = async (childId) => {
    try {
      const behaviorRef = collection(db, 'behavior');
      const behaviorQuery = query(
        behaviorRef,
        where('studentId', '==', childId)
      );

      const behaviorSnapshot = await getDocs(behaviorQuery);

      if (behaviorSnapshot.empty) {
        // If no real data, use sample data
        setBehaviorData(createBehaviorData(childId));
        return;
      }

      // Count behavior types
      const behaviorCounts = {
        'Excellent': 0,
        'Good': 0,
        'Average': 0,
        'Needs Improvement': 0
      };

      behaviorSnapshot.forEach(doc => {
        const data = doc.data();

        if (data.type === 'positive') {
          const points = parseInt(data.points) || 0;
          if (points >= 8) {
            behaviorCounts['Excellent']++;
          } else {
            behaviorCounts['Good']++;
          }
        } else if (data.type === 'negative') {
          const points = parseInt(data.points) || 0;
          if (points >= 5) {
            behaviorCounts['Needs Improvement']++;
          } else {
            behaviorCounts['Average']++;
          }
        } else {
          behaviorCounts['Average']++;
        }
      });

      // Convert to pie chart data
      const behaviorData = [
        {
          name: 'Excellent',
          value: behaviorCounts['Excellent'],
          color: '#4CAF50',
          legendFontColor: '#333333',
          legendFontSize: 12
        },
        {
          name: 'Good',
          value: behaviorCounts['Good'],
          color: '#8BC34A',
          legendFontColor: '#333333',
          legendFontSize: 12
        },
        {
          name: 'Average',
          value: behaviorCounts['Average'],
          color: '#FFC107',
          legendFontColor: '#333333',
          legendFontSize: 12
        },
        {
          name: 'Needs Improvement',
          value: behaviorCounts['Needs Improvement'],
          color: '#FF5722',
          legendFontColor: '#333333',
          legendFontSize: 12
        }
      ];

      // Filter out zero values
      const filteredData = behaviorData.filter(item => item.value > 0);

      // If all values are zero, use sample data
      if (filteredData.length === 0) {
        setBehaviorData(createBehaviorData(childId));
      } else {
        setBehaviorData(filteredData);
      }
    } catch (error) {
      console.error('Error fetching behavior data:', error);
      // Fallback to sample data
      setBehaviorData(createBehaviorData(childId));
    }
  };

  // Update progress data based on real data
  const updateProgressData = () => {
    try {
      // Calculate overall progress based on real data
      const academicScore = academicData?.datasets?.[0]?.data?.reduce((sum, val) => sum + val, 0) /
                           (academicData?.datasets?.[0]?.data?.length || 1);

      const attendanceScore = attendanceData?.datasets?.[0]?.data?.reduce((sum, val) => sum + val, 0) /
                             (attendanceData?.datasets?.[0]?.data?.length || 1);

      // Calculate behavior score (percentage of positive behaviors)
      let behaviorScore = 0;
      const totalBehavior = behaviorData?.reduce((sum, item) => sum + item.value, 0) || 1;

      const excellentCount = behaviorData?.find(item => item.name === 'Excellent')?.value || 0;
      const goodCount = behaviorData?.find(item => item.name === 'Good')?.value || 0;

      behaviorScore = ((excellentCount + goodCount) / totalBehavior) * 100;

      // Activities score (placeholder)
      const activitiesScore = 80;

      setProgressData({
        labels: ["Academics", "Attendance", "Behavior", "Activities"],
        data: [
          academicScore / 100 || 0.85,
          attendanceScore / 100 || 0.95,
          behaviorScore / 100 || 0.9,
          activitiesScore / 100 || 0.8
        ]
      });
    } catch (error) {
      console.error('Error updating progress data:', error);
      // Fallback to sample data
      setProgressData(createProgressData(selectedChildId));
    }
  };

  // Fetch real upcoming events
  const fetchRealEvents = async (childId) => {
    try {
      // Try to get real events from exams, homework, etc.
      const events = [];

      // Check if we have valid child data
      if (!selectedChildData) {
        console.log('No selected child data available for events');
        setUpcomingEvents(createUpcomingEvents(childId));
        return;
      }

      // Get class and section from different possible properties
      const classId = selectedChildData.classId || selectedChildData.class || selectedChildData.className;
      const section = selectedChildData.section || selectedChildData.sectionName;

      if (!classId || !section) {
        console.log('Missing classId or section for events:', { classId, section });
        setUpcomingEvents(createUpcomingEvents(childId));
        return;
      }

      console.log('Fetching events for class:', classId, 'section:', section);

      // Get upcoming exams
      const examsRef = collection(db, 'exams');
      const examsQuery = query(
        examsRef,
        where('published', '==', true)
      );

      const examsSnapshot = await getDocs(examsQuery);

      // Filter exams for this student's class
      examsSnapshot.forEach(doc => {
        const examData = doc.data();
        if (examData.classId === classId && examData.section === section) {
          let examDate = null;

          // Handle different date formats
          if (examData.date) {
            if (typeof examData.date === 'string') {
              examDate = new Date(examData.date);
            } else if (examData.date.seconds) {
              examDate = new Date(examData.date.seconds * 1000);
            } else if (examData.date.toDate) {
              examDate = examData.date.toDate();
            }
          }

          if (examDate && examDate > new Date()) {
            events.push({
              id: doc.id,
              title: examData.title || 'Exam',
              description: `${examData.subject || 'Subject'} exam`,
              date: examDate,
              type: 'exam'
            });
          }
        }
      });

      // Get homework
      const homeworkRef = collection(db, 'homework');
      const homeworkQuery = query(
        homeworkRef,
        where('status', '==', 'active')
      );

      const homeworkSnapshot = await getDocs(homeworkQuery);

      // Filter homework for this student's class
      homeworkSnapshot.forEach(doc => {
        const homeworkData = doc.data();

        // Check if the homework is for this class and section
        // Note: homework might use sectionName instead of section
        if (homeworkData.classId === classId &&
            (homeworkData.section === section || homeworkData.sectionName === section)) {

          let dueDate = null;

          // Handle different date formats
          if (homeworkData.dueDate) {
            if (homeworkData.dueDate.toDate) {
              dueDate = homeworkData.dueDate.toDate();
            } else if (typeof homeworkData.dueDate === 'string') {
              dueDate = new Date(homeworkData.dueDate);
            } else if (homeworkData.dueDate.seconds) {
              dueDate = new Date(homeworkData.dueDate.seconds * 1000);
            }
          }

          if (dueDate && dueDate > new Date()) {
            events.push({
              id: doc.id,
              title: homeworkData.title || 'Homework',
              description: homeworkData.description || `${homeworkData.subject || 'Subject'} homework`,
              date: dueDate,
              type: 'homework'
            });
          }
        }
      });

      console.log(`Found ${events.length} upcoming events for child`);

      // Sort events by date
      events.sort((a, b) => a.date - b.date);

      // If we have real events, use them
      if (events.length > 0) {
        setUpcomingEvents(events);
      } else {
        // Otherwise use sample data
        setUpcomingEvents(createUpcomingEvents(childId));
      }
    } catch (error) {
      console.error('Error fetching events:', error);
      // Fallback to sample data
      setUpcomingEvents(createUpcomingEvents(childId));
    }
  };

  // Handle logout
  const handleLogout = async () => {
    try {
      await signOut(auth);
      navigation.reset({
        index: 0,
        routes: [{ name: 'Login' }],
      });
    } catch (error) {
      console.error('Error logging out:', error);
      Alert.alert(
        translate('common.error') || 'Error',
        translate('auth.logoutError') || 'Failed to logout. Please try again.'
      );
    }
  };

  // Handle menu item press with child ID
  const handleMenuItemPress = (route, childId) => {
    if (!route) return;

    // Routes that need childId
    const routesNeedingChildId = [
      'ChildProgress',
      'ParentChildGrades',
      'ParentAttendanceView',
      'BehaviorView',
      'StudentProgressReport'
    ];

    try {
      if (routesNeedingChildId.includes(route)) {
        if (childId) {
          navigation.navigate(route, { childId });
        } else {
          // If no child is selected but we have children, use the first one
          if (children.length > 0) {
            navigation.navigate(route, { childId: children[0].id });
          } else {
            Alert.alert(
              translate('common.error') || 'Error',
              translate('dashboard.noChildSelected') || 'No child selected. Please select a child first.'
            );
          }
        }
      } else {
        navigation.navigate(route);
      }
    } catch (error) {
      console.error('Error navigating to route:', error);
      Alert.alert(
        translate('common.error') || 'Error',
        translate('dashboard.navigationError') || 'Failed to navigate to the selected screen.'
      );
    }
  };

  // Handle incoming notification while app is in foreground
  const handleNotification = (notification) => {
    try {
      console.log('Received notification:', notification);

      // Handle specific notification types
      if (notification.request?.content?.data?.type === 'exam_schedule') {
        // Show an alert for exam schedule notification
        Alert.alert(
          notification.request.content.title || translate('notifications.newExamSchedule') || 'New Exam Schedule',
          notification.request.content.body || translate('notifications.examScheduledMessage') || 'A new exam has been scheduled for your child.',
          [
            {
              text: translate('notifications.viewDetails') || 'View Details',
              onPress: async () => {
                const navAction = await NotificationService.handleExamScheduleNotification(notification.request.content);
                if (navAction) {
                  navigation.navigate(navAction.screen, navAction.params);
                }
              },
            },
            {
              text: translate('common.dismiss') || 'Dismiss',
              style: 'cancel',
            },
          ]
        );
      } else if (notification.request?.content?.data?.type === 'grade_published') {
        // Show an alert for grade notification
        Alert.alert(
          notification.request.content.title || translate('notifications.gradePublished') || 'Grade Published',
          notification.request.content.body || translate('notifications.gradePublishedMessage') || 'New grades have been published for your child.',
          [
            {
              text: translate('notifications.viewGrades') || 'View Grades',
              onPress: () => {
                const data = notification.request?.content?.data;
                if (data?.childId) {
                  navigation.navigate('ParentChildGrades', { childId: data.childId });
                } else if (selectedChildId) {
                  navigation.navigate('ParentChildGrades', { childId: selectedChildId });
                } else if (children.length > 0) {
                  navigation.navigate('ParentChildGrades', { childId: children[0].id });
                }
              },
            },
            {
              text: translate('common.dismiss') || 'Dismiss',
              style: 'cancel',
            },
          ]
        );
      }
    } catch (error) {
      console.error('Error handling notification:', error);
    }
  };

  // Handle notification response when user taps on a notification
  const handleNotificationResponse = async (response) => {
    try {
      console.log('Notification response:', response);

      const { notification } = response;
      const data = notification.request.content.data;

      console.log('Notification data:', data);

      // Mark the notification as read
      if (data.notificationId) {
        console.log('Marking notification as read:', data.notificationId);
        await NotificationService.markAsRead(data.notificationId);
      }

      // Handle specific notification types
      if (data.type === 'exam_schedule') {
        console.log('Handling exam schedule notification');
        const navAction = await NotificationService.handleExamScheduleNotification(notification.request.content);
        console.log('Navigation action:', navAction);

        if (navAction) {
          console.log(`Navigating to ${navAction.screen}`, navAction.params);
          navigation.navigate(navAction.screen, navAction.params);
        } else {
          console.log('No navigation action returned, navigating to ParentExamSchedule');
          navigation.navigate('ParentExamSchedule');
        }
      } else if (data.type === 'grade_published') {
        console.log('Handling grade published notification');
        if (data.childId) {
          navigation.navigate('ParentChildGrades', { childId: data.childId });
        } else if (selectedChildId) {
          navigation.navigate('ParentChildGrades', { childId: selectedChildId });
        } else if (children.length > 0) {
          navigation.navigate('ParentChildGrades', { childId: children[0].id });
        }
      } else if (data.type === 'attendance') {
        console.log('Handling attendance notification');
        if (data.childId) {
          navigation.navigate('ParentAttendanceView', { childId: data.childId });
        } else if (selectedChildId) {
          navigation.navigate('ParentAttendanceView', { childId: selectedChildId });
        } else if (children.length > 0) {
          navigation.navigate('ParentAttendanceView', { childId: children[0].id });
        }
      } else if (data.viewPath) {
        // Generic navigation based on viewPath
        console.log(`Navigating to ${data.viewPath} with params:`, data.viewParams);
        navigation.navigate(data.viewPath, data.viewParams);
      }

      // Refresh notifications
      fetchNotifications();
    } catch (error) {
      console.error('Error handling notification response:', error);
      // Fallback navigation
      try {
        if (selectedChildId) {
          navigation.navigate('ParentDashboard');
        } else {
          navigation.navigate('ParentDashboard');
        }
      } catch (navError) {
        console.error('Error navigating to fallback screen:', navError);
      }
    }
  };

  // Filter menu items based on search query
  const filteredMenuItems = menuItems.reduce((acc, category) => {
    const filteredItems = category.items.filter(item =>
      item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      category.category.toLowerCase().includes(searchQuery.toLowerCase())
    );
    if (filteredItems.length > 0) {
      acc.push({ ...category, items: filteredItems });
    }
    return acc;
  }, []);

  // Get random color for menu icons
  const getRandomColor = (index) => {
    const colors = [
      '#2196F3', // Blue
      '#4CAF50', // Green
      '#FF9800', // Orange
      '#E91E63', // Pink
      '#9C27B0', // Purple
      '#00BCD4', // Cyan
      '#009688', // Teal
      '#673AB7', // Deep Purple
      '#3F51B5', // Indigo
      '#F44336'  // Red
    ];
    return colors[index % colors.length];
  };

  // Format date for display
  const formatDate = (date) => {
    if (!date) return '';
    const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
    return date.toLocaleDateString(undefined, options);
  };

  // Get child's full name
  const getChildFullName = (child) => {
    if (!child) return '';
    return `${child.firstName || ''} ${child.lastName || ''}`.trim();
  };

  // Get child's class and section
  const getChildClassInfo = (child) => {
    if (!child) return '';
    const className = child.className || `Class ${child.classId || ''}`;
    const section = child.section || '';
    return `${className}${section ? ` - ${section}` : ''}`;
  };

  // Get child's avatar
  const getChildAvatar = (child) => {
    if (!child) return null;

    if (child.photoURL) {
      return { uri: child.photoURL };
    }

    // Return first letter of first and last name
    const initials = `${(child.firstName || '').charAt(0)}${(child.lastName || '').charAt(0)}`.toUpperCase();
    return initials || '?';
  };

  // Format current date for display
  const getCurrentDate = () => {
    return formatDate(new Date());
  };

  // Render child selector
  const renderChildSelector = () => {
    if (!children || children.length === 0) {
      return (
        <Card style={styles.noChildrenCard}>
          <Card.Content>
            <Text style={styles.noChildrenText}>
              {translate('parent.dashboard.noChildrenRegistered') || 'No children registered in the system.'}
            </Text>
            <Button
              mode="contained"
              icon="account-plus"
              onPress={() => navigation.navigate('ProfileManagement')}
              style={styles.updateProfileButton}
            >
              {translate('parent.dashboard.updateProfile') || 'Update Profile'}
            </Button>
          </Card.Content>
        </Card>
      );
    }

    if (children.length === 1) {
      // If only one child, just show the child info card
      return null;
    }

    return (
      <Animatable.View
        ref={childSelectorRef}
        animation="fadeInDown"
        duration={800}
        delay={300}
        style={styles.childSelectorContainer}
      >
        <Text style={styles.childSelectorTitle}>
          {translate('parent.selectChild') || 'Select Child'}
        </Text>

        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.childSelectorScrollContent}
        >
          {children.map((child) => {
            const isSelected = selectedChildId === child.id;
            const avatar = getChildAvatar(child);

            return (
              <TouchableOpacity
                key={child.id}
                style={[
                  styles.childSelectorItem,
                  isSelected && styles.selectedChildItem
                ]}
                onPress={() => setSelectedChildId(child.id)}
              >
                {typeof avatar === 'string' ? (
                  <Avatar.Text
                    size={50}
                    label={avatar}
                    style={[
                      styles.childAvatar,
                      isSelected && styles.selectedChildAvatar
                    ]}
                  />
                ) : (
                  <Avatar.Image
                    size={50}
                    source={avatar}
                    style={[
                      styles.childAvatar,
                      isSelected && styles.selectedChildAvatar
                    ]}
                  />
                )}

                <Text style={[
                  styles.childNameText,
                  isSelected && styles.selectedChildText
                ]}>
                  {getChildFullName(child)}
                </Text>

                <Text style={[
                  styles.childClassText,
                  isSelected && styles.selectedChildText
                ]}>
                  {getChildClassInfo(child)}
                </Text>

                {isSelected && (
                  <View style={styles.selectedIndicator} />
                )}
              </TouchableOpacity>
            );
          })}
        </ScrollView>
      </Animatable.View>
    );
  };

  return (
    <View style={styles.container}>
      {/* Status Bar */}
      <StatusBar backgroundColor="#1976d2" barStyle="light-content" />

      {/* Parent App Header */}
      <ParentAppHeader
        title={translate('parent.dashboard.title') || "Parent Dashboard"}
        showBackButton={false}
        onMenuPress={toggleDrawer}
      />

      {/* Parent Sidebar */}
      <ParentSidebar
        visible={drawerVisible}
        onClose={toggleDrawer}
        navigation={navigation}
        activeSidebarItem={activeSidebarItem}
        setActiveSidebarItem={setActiveSidebarItem}
        selectedChild={selectedChildData}
        children={children}
        onChildSelect={(childId) => setSelectedChildId(childId)}
      />

      {/* Main Content */}
      <ScrollView
        style={styles.content}
        contentContainerStyle={styles.contentContainer}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[theme.colors.primary]}
            tintColor={theme.colors.primary}
          />
        }
      >
        {/* Child Selector */}
        {renderChildSelector()}

        {/* Search Bar */}
        {!searchQuery && (
          <Animatable.View
            animation="fadeIn"
            duration={500}
            delay={400}
          >
            <Searchbar
              placeholder={translate('parent.dashboard.searchMenuItems') || "Search menu items..."}
              onChangeText={query => setSearchQuery(query)}
              value={searchQuery}
              style={styles.searchBar}
              iconColor={theme.colors.primary}
            />
          </Animatable.View>
        )}

        {/* Dashboard Content */}
        {!searchQuery && (
          <Animatable.View
            ref={contentRef}
            animation="fadeIn"
            duration={800}
            delay={500}
          >
            {/* Child Status Card */}
            {selectedChildId && selectedChildData && (
              <Card style={styles.childStatusCard}>
                <Card.Content>
                  <View style={styles.childStatusHeader}>
                    <View style={styles.childStatusLeft}>
                      {selectedChildData.photoURL ? (
                        <Avatar.Image
                          size={60}
                          source={{ uri: selectedChildData.photoURL }}
                          style={styles.childStatusAvatar}
                        />
                      ) : (
                        <Avatar.Text
                          size={60}
                          label={`${(selectedChildData.firstName || '').charAt(0)}${(selectedChildData.lastName || '').charAt(0)}`}
                          style={styles.childStatusAvatar}
                        />
                      )}

                      <View style={styles.childStatusInfo}>
                        <Title style={styles.childStatusName}>
                          {selectedChildData.firstName} {selectedChildData.lastName}
                        </Title>
                        <Text style={styles.childStatusClass}>
                          {getChildClassInfo(selectedChildData)}
                        </Text>
                        <Chip
                          mode="outlined"
                          style={styles.childStatusChip}
                          icon="check-circle"
                        >
                          {translate('parent.dashboard.active') || 'Active'}
                        </Chip>
                      </View>
                    </View>

                    <View style={styles.childStatusRight}>
                      {progressData && (
                        <ProgressChart
                          data={progressData}
                          width={100}
                          height={100}
                          strokeWidth={10}
                          radius={40}
                          chartConfig={{
                            ...chartConfig,
                            backgroundGradientFrom: '#ffffff',
                            backgroundGradientTo: '#ffffff',
                            color: (opacity = 1) => `rgba(81, 45, 168, ${opacity})`,
                          }}
                          hideLegend={true}
                          style={styles.overallProgressChart}
                        />
                      )}
                    </View>
                  </View>

                  <View style={styles.childQuickStats}>
                    <View style={styles.quickStatItem}>
                      <Avatar.Icon
                        size={40}
                        icon="book-open-variant"
                        style={[styles.quickStatIcon, { backgroundColor: '#2196F3' }]}
                      />
                      <View style={styles.quickStatContent}>
                        <Text style={styles.quickStatLabel}>
                          {translate('parent.dashboard.gpa') || 'GPA'}
                        </Text>
                        <Text style={styles.quickStatValue}>3.8/4.0</Text>
                      </View>
                    </View>

                    <View style={styles.quickStatItem}>
                      <Avatar.Icon
                        size={40}
                        icon="calendar-check"
                        style={[styles.quickStatIcon, { backgroundColor: '#4CAF50' }]}
                      />
                      <View style={styles.quickStatContent}>
                        <Text style={styles.quickStatLabel}>
                          {translate('parent.dashboard.attendance') || 'Attendance'}
                        </Text>
                        <Text style={styles.quickStatValue}>96%</Text>
                      </View>
                    </View>

                    <View style={styles.quickStatItem}>
                      <Avatar.Icon
                        size={40}
                        icon="star"
                        style={[styles.quickStatIcon, { backgroundColor: '#FFC107' }]}
                      />
                      <View style={styles.quickStatContent}>
                        <Text style={styles.quickStatLabel}>
                          {translate('parent.dashboard.behavior') || 'Behavior'}
                        </Text>
                        <Text style={styles.quickStatValue}>Excellent</Text>
                      </View>
                    </View>
                  </View>
                </Card.Content>
              </Card>
            )}

            {/* Child's Today Schedule Widget */}
            {selectedChildId && (
              <Card style={styles.scheduleCard}>
                <Card.Content>
                  <View style={styles.scheduleHeader}>
                    <Title style={styles.sectionTitle}>
                      {translate('schedule.todayClassSchedule') || "Today's Class Schedule"}
                    </Title>
                    <Button
                      mode="contained"
                      onPress={() => navigation.navigate('ParentClassSchedule')}
                      style={styles.viewScheduleButton}
                      icon="calendar-month"
                    >
                      {translate('schedule.fullSchedule') || "Full Schedule"}
                    </Button>
                  </View>
                  <ScrollView
                    horizontal
                    showsHorizontalScrollIndicator={false}
                    style={styles.scheduleScrollView}
                  >
                    <ChildTodayScheduleWidget
                      childId={selectedChildId}
                    />
                  </ScrollView>
                </Card.Content>
              </Card>
            )}

            {/* Class Status Widget */}
            {selectedChildId && (
              <Card style={styles.classStatusCard}>
                <Card.Content>
                  <Title style={styles.sectionTitle}>
                    {translate('parent.dashboard.classStatus') || "Class Status"}
                  </Title>
                  <DashboardClassWidget
                    role="parent"
                    userId={auth.currentUser?.uid}
                    childId={selectedChildId}
                    onPressViewSchedule={() => navigation.navigate('ParentClassSchedule')}
                  />
                </Card.Content>
              </Card>
            )}

            {/* Academic Progress Section */}
            {academicData && (
              <Card style={styles.academicCard}>
                <Card.Content>
                  <Title style={styles.sectionTitle}>
                    {translate('parent.dashboard.academicProgress') || "Academic Progress"}
                  </Title>
                  <LineChart
                    data={sanitizeChartDatasets(academicData)}
                    width={screenWidth - 48}
                    height={220}
                    chartConfig={chartConfig}
                    style={styles.chart}
                    bezier
                    fromZero
                    yAxisSuffix="%"
                  />
                  <View style={styles.academicSummary}>
                    <Text style={styles.academicSummaryText}>
                      {translate('parent.dashboard.academicSummary')}
                    </Text>
                    <Button
                      mode="outlined"
                      icon="chart-line"
                      onPress={() => navigation.navigate('ParentChildGrades', { childId: selectedChildId })}
                      style={styles.viewDetailsButton}
                    >
                      {translate('parent.dashboard.viewGrades')}
                    </Button>
                  </View>
                </Card.Content>
              </Card>
            )}

            {/* Attendance Overview */}
            {attendanceData && (
              <Card style={styles.attendanceCard}>
                <Card.Content>
                  <Title style={styles.sectionTitle}>
                    {translate('parent.dashboard.attendanceOverview')}
                  </Title>
                  <LineChart
                    data={sanitizeChartDatasets(attendanceData)}
                    width={screenWidth - 48}
                    height={220}
                    chartConfig={{
                      ...chartConfig,
                      color: (opacity = 1) => `rgba(76, 175, 80, ${opacity})`,
                    }}
                    style={styles.chart}
                    bezier
                    fromZero
                    yAxisSuffix="%"
                  />
                  <View style={styles.attendanceSummary}>
                    <View style={styles.attendanceStats}>
                      <View style={styles.attendanceStatItem}>
                        <Text style={styles.attendanceStatLabel}>
                          {translate('parent.dashboard.present')}
                        </Text>
                        <Text style={[styles.attendanceStatValue, { color: '#4CAF50' }]}>
                          96%
                        </Text>
                      </View>
                      <View style={styles.attendanceStatItem}>
                        <Text style={styles.attendanceStatLabel}>
                          {translate('parent.dashboard.absent')}
                        </Text>
                        <Text style={[styles.attendanceStatValue, { color: '#F44336' }]}>
                          2%
                        </Text>
                      </View>
                      <View style={styles.attendanceStatItem}>
                        <Text style={styles.attendanceStatLabel}>
                          {translate('parent.dashboard.late')}
                        </Text>
                        <Text style={[styles.attendanceStatValue, { color: '#FFC107' }]}>
                          2%
                        </Text>
                      </View>
                    </View>
                    <Button
                      mode="outlined"
                      icon="calendar-check"
                      onPress={() => navigation.navigate('ParentAttendanceView', { childId: selectedChildId })}
                      style={styles.viewDetailsButton}
                    >
                      {translate('parent.dashboard.viewAttendance')}
                    </Button>
                  </View>
                </Card.Content>
              </Card>
            )}

            {/* Behavior Analysis */}
            {behaviorData && (
              <Card style={styles.behaviorCard}>
                <Card.Content>
                  <Title style={styles.sectionTitle}>
                    {translate('parent.dashboard.behaviorAnalysis')}
                  </Title>
                  <View style={styles.behaviorChartContainer}>
                    <PieChart
                      data={behaviorData.map(item => ({
                        ...item,
                        value: isNaN(item.value) || item.value === undefined ||
                              item.value === null || item.value === Infinity ||
                              item.value === -Infinity ? 0 : item.value
                      }))}
                      width={screenWidth - 48}
                      height={220}
                      chartConfig={chartConfig}
                      accessor="value"
                      backgroundColor="transparent"
                      paddingLeft="15"
                      style={styles.chart}
                    />
                  </View>
                  <View style={styles.behaviorSummary}>
                    <Text style={styles.behaviorSummaryText}>
                      {translate('parent.dashboard.behaviorSummary')}
                    </Text>
                    <Button
                      mode="outlined"
                      icon="clipboard-text"
                      onPress={() => navigation.navigate('BehaviorView', { childId: selectedChildId })}
                      style={styles.viewDetailsButton}
                    >
                      {translate('parent.dashboard.viewBehaviorRecords')}
                    </Button>
                  </View>
                </Card.Content>
              </Card>
            )}

            {/* Upcoming Events */}
            {upcomingEvents.length > 0 && (
              <Card style={styles.eventsCard}>
                <Card.Content>
                  <Title style={styles.sectionTitle}>
                    {translate('parent.dashboard.upcomingEvents')}
                  </Title>
                  {upcomingEvents.map((event, index) => (
                    <Animatable.View
                      key={event.id}
                      animation="fadeInRight"
                      duration={500}
                      delay={index * 100}
                    >
                      <Surface style={styles.eventItem}>
                        <View style={styles.eventIconContainer}>
                          <Avatar.Icon
                            size={40}
                            icon={
                              event.type === 'exam' ? 'file-document' :
                              event.type === 'meeting' ? 'account-group' :
                              event.type === 'assignment' ? 'book-open-page-variant' :
                              'calendar-star'
                            }
                            style={[
                              styles.eventIcon,
                              event.type === 'exam' ? { backgroundColor: '#F44336' } :
                              event.type === 'meeting' ? { backgroundColor: '#2196F3' } :
                              event.type === 'assignment' ? { backgroundColor: '#4CAF50' } :
                              { backgroundColor: '#9C27B0' }
                            ]}
                          />
                        </View>
                        <View style={styles.eventContent}>
                          <Text style={styles.eventTitle}>{event.title}</Text>
                          <Text style={styles.eventDate}>{formatDate(event.date)}</Text>
                          <Text style={styles.eventDescription}>{event.description}</Text>
                        </View>
                      </Surface>
                    </Animatable.View>
                  ))}
                  <Button
                    mode="outlined"
                    icon="calendar-month"
                    onPress={() => navigation.navigate('SchoolEventCalendar')}
                    style={[styles.viewDetailsButton, { marginTop: 16 }]}
                  >
                    {translate('parent.dashboard.viewCalendar')}
                  </Button>
                </Card.Content>
              </Card>
            )}
          </Animatable.View>
        )}

        {/* Quick Access Menu */}
        <View style={styles.menuHeader}>
          <Title style={styles.menuTitle}>
            {translate('parent.dashboard.quickAccess')}
          </Title>
          <Button
            mode="text"
            icon={showAllItems ? "chevron-up" : "chevron-down"}
            onPress={() => setShowAllItems(!showAllItems)}
          >
            {showAllItems
              ? translate('parent.dashboard.showLess')
              : translate('parent.dashboard.showAll')}
          </Button>
        </View>

        {/* Filter menu items based on search query */}
        {searchQuery ? (
          // Show search results
          <Animatable.View
            key={`search-results-${language}`}
            animation="fadeIn"
            duration={500}
          >
            {filteredMenuItems.length > 0 ? (
              filteredMenuItems.map((category, index) => (
                <Card key={`${category.category}-${language}-${index}`} style={styles.menuCategoryCard}>
                  <Card.Content>
                    <Title style={styles.menuCategoryTitle}>{category.category}</Title>
                    <View style={styles.menuGrid}>
                      {category.items.map((item, index) => (
                        <TouchableOpacity
                          key={`${item.title}-${language}-${index}`}
                          style={styles.menuItem}
                          onPress={() => handleMenuItemPress(item.route, selectedChildId)}
                        >
                          <Avatar.Icon
                            size={48}
                            icon={item.icon}
                            style={[styles.menuIcon, { backgroundColor: getRandomColor(index) }]}
                            color="#fff"
                          />
                          <Text style={styles.menuText}>{item.title}</Text>
                        </TouchableOpacity>
                      ))}
                    </View>
                  </Card.Content>
                </Card>
              ))
            ) : (
              <Card style={styles.noResultsCard}>
                <Card.Content style={styles.noResultsContent}>
                  <IconButton icon="magnify-close" size={48} color="#9e9e9e" />
                  <Text style={styles.noResultsText}>
                    {translate('parent.dashboard.noSearchResults')}
                  </Text>
                  <Button
                    mode="contained"
                    onPress={() => setSearchQuery('')}
                    style={styles.clearSearchButton}
                    icon="close-circle"
                  >
                    {translate('parent.dashboard.clearSearch')}
                  </Button>
                </Card.Content>
              </Card>
            )}
          </Animatable.View>
        ) : (
          // Show regular menu categories
          <Animatable.View
            key={`menu-items-${language}`}
            animation="fadeIn"
            duration={500}
          >
            {menuItems
              .slice(0, showAllItems ? menuItems.length : 3)
              .map((category, categoryIndex) => (
                <Animatable.View
                  key={`${category.category}-${language}-${categoryIndex}`}
                  animation="fadeInUp"
                  duration={500}
                  delay={categoryIndex * 100}
                >
                  <Card style={styles.menuCategoryCard}>
                    <Card.Content>
                      <Title style={styles.menuCategoryTitle}>{category.category}</Title>
                      <View style={styles.menuGrid}>
                        {category.items.map((item, index) => (
                          <TouchableOpacity
                            key={`${item.title}-${language}-${index}`}
                            style={styles.menuItem}
                            onPress={() => handleMenuItemPress(item.route, selectedChildId)}
                          >
                            <LinearGradient
                              colors={[getRandomColor(index) || '#6200ee', getRandomColor(index + 2) || '#3700b3']}
                              style={styles.menuIconGradient}
                              start={{ x: 0, y: 0 }}
                              end={{ x: 1, y: 1 }}
                            >
                              <Avatar.Icon
                                size={48}
                                icon={item.icon}
                                style={styles.menuIcon}
                                color="#fff"
                              />
                            </LinearGradient>
                            <Text style={styles.menuText}>{item.title}</Text>
                          </TouchableOpacity>
                        ))}
                      </View>
                    </Card.Content>
                  </Card>
                </Animatable.View>
              ))}

            {!showAllItems && menuItems.length > 3 && (
              <Button
                mode="outlined"
                onPress={() => setShowAllItems(true)}
                style={styles.showMoreButton}
                icon="chevron-down"
              >
                {translate('parent.dashboard.showMoreCategories')}
              </Button>
            )}
          </Animatable.View>
        )}
      </ScrollView>

      {/* FAB Group */}
      <FAB.Group
        open={false}
        icon="plus"
        actions={[
          {
            icon: 'calendar-clock',
            label: translate('parent.dashboard.examSchedule'),
            onPress: () => navigation.navigate('ParentExamSchedule'),
            color: '#673AB7',
            style: { backgroundColor: '#EDE7F6' }
          },
          {
            icon: 'calendar-week',
            label: translate('parent.dashboard.classSchedule'),
            onPress: () => navigation.navigate('ParentClassSchedule'),
            color: '#2196F3',
            style: { backgroundColor: '#E3F2FD' }
          },
          {
            icon: 'message-text',
            label: translate('parent.dashboard.messages'),
            onPress: () => navigation.navigate('ChatList'),
            color: '#4CAF50',
            style: { backgroundColor: '#E8F5E9' }
          }
        ]}
        fabStyle={{ backgroundColor: theme.colors.primary }}
        color="white"
      />

      {/* Messaging FAB */}
      <MessagingFAB />

      {/* Logout Dialog */}
      <Portal>
        <Dialog
          visible={logoutDialogVisible}
          onDismiss={() => setLogoutDialogVisible(false)}
          style={styles.logoutDialog}
        >
          <Dialog.Title style={styles.logoutDialogTitle}>
            {translate('auth.confirmLogout')}
          </Dialog.Title>
          <Dialog.Content>
            <Paragraph style={styles.logoutDialogText}>
              {translate('auth.logoutConfirmMessage')}
            </Paragraph>
          </Dialog.Content>
          <Dialog.Actions>
            <Button
              onPress={() => setLogoutDialogVisible(false)}
              mode="text"
              textColor={theme.colors.primary}
            >
              {translate('common.cancel')}
            </Button>
            <Button
              onPress={handleLogout}
              mode="contained"
              style={styles.logoutButton}
            >
              {translate('auth.logout')}
            </Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>

      {/* Loading Indicator */}
      {isLoading && (
        <View style={styles.loadingIndicator}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={styles.loadingText}>
            {translate('common.loading')}
          </Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  // Main Container
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
    paddingTop: 8, // Add padding to account for header
  },
  contentContainer: {
    paddingBottom: 100, // Extra padding for FABs
  },

  // Date display style
  currentDateText: {
    color: '#1976d2',
    fontSize: 14,
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
    fontWeight: '500',
  },

  // Child Selector Styles
  childSelectorContainer: {
    marginHorizontal: 16,
    marginTop: 16,
    marginBottom: 8,
  },
  childSelectorTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 12,
    color: '#424242',
  },
  childSelectorScrollContent: {
    paddingBottom: 8,
  },
  childSelectorItem: {
    alignItems: 'center',
    marginRight: 16,
    padding: 12,
    borderRadius: 12,
    backgroundColor: '#fff',
    width: 120,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    position: 'relative',
  },
  selectedChildItem: {
    backgroundColor: '#E3F2FD',
    borderColor: '#2196F3',
    borderWidth: 1,
    elevation: 4,
  },
  childAvatar: {
    marginBottom: 8,
    backgroundColor: '#9E9E9E',
  },
  selectedChildAvatar: {
    backgroundColor: '#2196F3',
  },
  childNameText: {
    fontSize: 14,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 4,
    color: '#424242',
  },
  childClassText: {
    fontSize: 12,
    textAlign: 'center',
    color: '#757575',
  },
  selectedChildText: {
    color: '#1976D2',
  },
  selectedIndicator: {
    position: 'absolute',
    bottom: -2,
    width: 40,
    height: 4,
    backgroundColor: '#2196F3',
    borderRadius: 2,
  },
  noChildrenCard: {
    margin: 16,
    borderRadius: 12,
    elevation: 3,
  },
  noChildrenText: {
    textAlign: 'center',
    marginBottom: 16,
    color: '#757575',
  },
  updateProfileButton: {
    marginTop: 8,
  },

  // Search Bar
  searchBar: {
    margin: 16,
    elevation: 3,
    borderRadius: 12,
    backgroundColor: '#fff',
  },

  // Child Status Card
  childStatusCard: {
    margin: 16,
    borderRadius: 12,
    elevation: 3,
  },
  childStatusHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  childStatusLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  childStatusAvatar: {
    backgroundColor: '#2196F3',
  },
  childStatusInfo: {
    marginLeft: 12,
    flex: 1,
  },
  childStatusName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#212121',
  },
  childStatusClass: {
    fontSize: 14,
    color: '#757575',
    marginBottom: 8,
  },
  childStatusChip: {
    alignSelf: 'flex-start',
    backgroundColor: '#E8F5E9',
    borderColor: '#4CAF50',
  },
  childStatusRight: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  overallProgressChart: {
    marginLeft: 16,
  },
  childQuickStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
  },
  quickStatItem: {
    flexDirection: 'column',
    alignItems: 'center',
    flex: 1,
    padding: 8,
    borderRadius: 8,
    backgroundColor: '#F5F5F5',
    marginHorizontal: 4,
  },
  quickStatIcon: {
    marginRight: 8,
  },
  quickStatContent: {
    flex: 1,
  },
  quickStatLabel: {
    fontSize: 12,
    color: '#757575',
  },
  quickStatValue: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#212121',
  },

  // Schedule Card
  scheduleCard: {
    margin: 16,
    borderRadius: 12,
    elevation: 3,
  },
  scheduleHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  viewScheduleButton: {
    borderRadius: 20,
    height: 36,
  },
  scheduleScrollView: {
    marginTop: 8,
  },

  // Class Status Card
  classStatusCard: {
    margin: 16,
    borderRadius: 12,
    elevation: 3,
  },

  // Academic Card
  academicCard: {
    margin: 16,
    borderRadius: 12,
    elevation: 3,
  },
  chart: {
    marginVertical: 16,
    borderRadius: 16,
  },
  academicSummary: {
    marginTop: 16,
  },
  academicSummaryText: {
    fontSize: 14,
    color: '#616161',
    marginBottom: 16,
    lineHeight: 20,
  },
  viewDetailsButton: {
    alignSelf: 'flex-end',
  },

  // Attendance Card
  attendanceCard: {
    margin: 16,
    borderRadius: 12,
    elevation: 3,
  },
  attendanceSummary: {
    marginTop: 16,
  },
  attendanceStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 16,
  },
  attendanceStatItem: {
    alignItems: 'center',
  },
  attendanceStatLabel: {
    fontSize: 14,
    color: '#757575',
    marginBottom: 4,
  },
  attendanceStatValue: {
    fontSize: 18,
    fontWeight: 'bold',
  },

  // Behavior Card
  behaviorCard: {
    margin: 16,
    borderRadius: 12,
    elevation: 3,
  },
  behaviorChartContainer: {
    alignItems: 'center',
  },
  behaviorSummary: {
    marginTop: 16,
  },
  behaviorSummaryText: {
    fontSize: 14,
    color: '#616161',
    marginBottom: 16,
    lineHeight: 20,
  },

  // Events Card
  eventsCard: {
    margin: 16,
    borderRadius: 12,
    elevation: 3,
  },
  eventItem: {
    flexDirection: 'row',
    padding: 12,
    borderRadius: 8,
    marginBottom: 12,
    backgroundColor: '#F5F5F5',
    elevation: 1,
  },
  eventIconContainer: {
    marginRight: 12,
  },
  eventIcon: {
    margin: 0,
  },
  eventContent: {
    flex: 1,
  },
  eventTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#212121',
    marginBottom: 4,
  },
  eventDate: {
    fontSize: 12,
    color: '#757575',
    marginBottom: 4,
  },
  eventDescription: {
    fontSize: 14,
    color: '#616161',
  },

  // Menu Section
  menuHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    marginTop: 16,
  },
  menuTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#212121',
  },
  menuCategoryCard: {
    margin: 16,
    borderRadius: 12,
    elevation: 3,
  },
  menuCategoryTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 16,
    color: '#424242',
  },
  menuGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -8,
  },
  menuItem: {
    width: '33.33%',
    padding: 8,
    alignItems: 'center',
  },
  menuIconGradient: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  menuIcon: {
    backgroundColor: 'transparent',
    elevation: 0,
  },
  menuText: {
    fontSize: 12,
    textAlign: 'center',
    color: '#424242',
  },
  showMoreButton: {
    margin: 16,
  },

  // No Results
  noResultsCard: {
    margin: 16,
    borderRadius: 12,
    elevation: 3,
  },
  noResultsContent: {
    alignItems: 'center',
    padding: 16,
  },
  noResultsText: {
    fontSize: 16,
    color: '#757575',
    textAlign: 'center',
    marginVertical: 16,
  },
  clearSearchButton: {
    marginTop: 8,
  },

  // Loading Indicator
  loadingIndicator: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.7)',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#616161',
  },

  // Logout Dialog
  logoutDialog: {
    borderRadius: 12,
  },
  logoutDialogTitle: {
    textAlign: 'center',
  },
  logoutDialogText: {
    textAlign: 'center',
  },
  logoutButton: {
    marginLeft: 8,
  },

  // Section Titles
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#212121',
  },

  // Today Schedule Widget Styles
  todayScheduleContainer: {
    minWidth: screenWidth - 64,
    paddingVertical: 8,
  },
  todayHeaderContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  todayHeaderLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  todayIcon: {
    margin: 0,
  },
  todayText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  totalClassesChip: {
    backgroundColor: '#E3F2FD',
  },
  scheduleItemCard: {
    marginBottom: 12,
    borderRadius: 8,
    elevation: 2,
    backgroundColor: '#f5f5f5',
    borderLeftWidth: 4,
    borderLeftColor: '#2196F3',
  },
  currentClassCard: {
    backgroundColor: '#E3F2FD',
    borderLeftColor: '#1976D2',
  },
  pastClassCard: {
    backgroundColor: '#F5F5F5',
    borderLeftColor: '#9E9E9E',
    opacity: 0.8,
  },
  upcomingClassCard: {
    backgroundColor: '#E8F5E9',
    borderLeftColor: '#4CAF50',
  },
  scheduleItemContent: {
    padding: 12,
  },
  scheduleTimeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  scheduleTimeWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  scheduleTimeIcon: {
    margin: 0,
    marginRight: 4,
  },
  scheduleTimeText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
  },
  currentTimeText: {
    color: '#1976D2',
  },
  pastTimeText: {
    color: '#757575',
  },
  scheduleStatusChip: {
    height: 24,
    backgroundColor: '#E3F2FD',
  },
  currentClassChip: {
    backgroundColor: '#1976D2',
  },
  pastClassChip: {
    backgroundColor: '#9E9E9E',
  },
  upcomingClassChip: {
    backgroundColor: '#4CAF50',
  },
  scheduleStatusText: {
    fontSize: 10,
    color: '#fff',
  },
  progressBarContainer: {
    marginBottom: 12,
  },
  progressBar: {
    height: 6,
    backgroundColor: '#E0E0E0',
    borderRadius: 3,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#1976D2',
    borderRadius: 3,
  },
  progressText: {
    fontSize: 12,
    color: '#757575',
    marginTop: 4,
    textAlign: 'right',
  },
  scheduleDetailsContainer: {
    marginBottom: 12,
  },
  scheduleDetailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  detailIcon: {
    marginRight: 8,
    backgroundColor: '#9E9E9E',
    width: 24,
    height: 24,
  },
  scheduleSubjectText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  pastSubjectText: {
    color: '#757575',
  },
  scheduleTeacherText: {
    fontSize: 14,
    color: '#666',
  },
  pastDetailText: {
    color: '#9E9E9E',
  },
  scheduleRoomText: {
    fontSize: 14,
    color: '#666',
  },
  scheduleActionsContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  contactTeacherButton: {
    marginTop: 4,
  },
  pastActionButton: {
    borderColor: '#9E9E9E',
  },
  pastActionButtonLabel: {
    color: '#9E9E9E',
  },
  scheduleLoadingContainer: {
    minHeight: 150,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  scheduleLoadingText: {
    marginTop: 8,
    color: '#666',
    fontSize: 14,
  },
  scheduleErrorContainer: {
    minHeight: 150,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#FFEBEE',
    borderRadius: 8,
  },
  scheduleErrorText: {
    color: '#D32F2F',
    fontSize: 14,
    textAlign: 'center',
    marginVertical: 8,
  },
  retryButton: {
    marginTop: 8,
    backgroundColor: '#F44336',
  },
  scheduleEmptyContainer: {
    minHeight: 150,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  scheduleEmptyText: {
    color: '#9E9E9E',
    fontSize: 16,
    marginVertical: 8,
    textAlign: 'center',
  },
  scheduleEmptyButton: {
    marginTop: 8,
  },
});

export default ParentDashboard;
