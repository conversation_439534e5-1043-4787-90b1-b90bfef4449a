import React, { useState, useEffect } from 'react';
import { View, ScrollView, StyleSheet } from 'react-native';
import {
  Card,
  Title,
  Text,
  Portal,
  Modal,
  ActivityIndicator,
  List,
  IconButton,
  Chip,
} from 'react-native-paper';
import { db, storage } from '../../config/firebase';
import {
  collection,
  addDoc,
  query,
  getDocs,
  doc,
  updateDoc,
  deleteDoc,
  where,
  orderBy,
} from 'firebase/firestore';
import {
  ref,
  uploadBytes,
  getDownloadURL,
  deleteObject,
} from 'firebase/storage';
import * as DocumentPicker from 'expo-document-picker';
import CustomButton from '../../components/common/CustomButton';
import CustomInput from '../../components/common/CustomInput';

const TeacherDocuments = ({ route, navigation }) => {
  const [documents, setDocuments] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedDocument, setSelectedDocument] = useState(null);
  const [selectedFile, setSelectedFile] = useState(null);
  const [error, setError] = useState(null);

  // Validate route params
  const teacherId = route?.params?.teacherId;

  const [formData, setFormData] = useState({
    title: '',
    type: '',
    description: '',
    status: 'pending',
    expiryDate: '',
  });

  const documentTypes = [
    'Certification',
    'Qualification',
    'Training',
    'ID Proof',
    'Contract',
    'Other',
  ];

  // Redirect if teacherId is missing
  useEffect(() => {
    if (!teacherId) {
      console.error('TeacherDocuments: teacherId is missing in route params');
      setError('Teacher ID is missing. Please select a teacher first.');
      // Optionally navigate back to teacher list
      // navigation.goBack();
      return;
    }

    fetchDocuments();
  }, [teacherId]);

  const fetchDocuments = async () => {
    if (!teacherId) return;

    try {
      setLoading(true);
      setError(null);

      const documentsRef = collection(db, 'teacherDocuments');
      const q = query(
        documentsRef,
        where('teacherId', '==', teacherId),
        orderBy('createdAt', 'desc')
      );
      const querySnapshot = await getDocs(q);

      const documentsData = [];
      querySnapshot.forEach((doc) => {
        documentsData.push({ id: doc.id, ...doc.data() });
      });

      setDocuments(documentsData);
    } catch (error) {
      console.error('Error fetching documents:', error);
      setError('Failed to load documents. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const pickDocument = async () => {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: '*/*',
        copyToCacheDirectory: true,
      });

      if (!result.canceled) {
        setSelectedFile(result.assets[0]);
      }
    } catch (error) {
      console.error('Error picking document:', error);
    }
  };

  const uploadDocument = async () => {
    try {
      if (!selectedFile) return null;

      const response = await fetch(selectedFile.uri);
      const blob = await response.blob();
      const filename = `teacher_documents/${Date.now()}_${selectedFile.name}`;
      const fileRef = ref(storage, filename);

      await uploadBytes(fileRef, blob);
      const downloadURL = await getDownloadURL(fileRef);
      return { url: downloadURL, path: filename };
    } catch (error) {
      console.error('Error uploading document:', error);
      return null;
    }
  };

  const handleAddDocument = async () => {
    if (!teacherId) {
      setError('Cannot add document: Teacher ID is missing');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const fileData = await uploadDocument();

      if (!fileData) {
        throw new Error('Failed to upload document');
      }

      const documentsRef = collection(db, 'teacherDocuments');
      await addDoc(documentsRef, {
        ...formData,
        teacherId: teacherId,
        fileUrl: fileData.url,
        filePath: fileData.path,
        fileName: selectedFile.name,
        fileSize: selectedFile.size,
        createdAt: new Date().toISOString(),
      });

      setModalVisible(false);
      resetForm();
      fetchDocuments();
    } catch (error) {
      console.error('Error adding document:', error);
      setError('Failed to add document. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateDocument = async () => {
    try {
      setLoading(true);
      let fileData = null;

      if (selectedFile) {
        // Delete old file if exists
        if (selectedDocument.filePath) {
          const oldFileRef = ref(storage, selectedDocument.filePath);
          await deleteObject(oldFileRef);
        }

        fileData = await uploadDocument();
        if (!fileData) {
          throw new Error('Failed to upload document');
        }
      }

      const documentRef = doc(db, 'teacherDocuments', selectedDocument.id);
      await updateDoc(documentRef, {
        ...formData,
        ...(fileData && {
          fileUrl: fileData.url,
          filePath: fileData.path,
          fileName: selectedFile.name,
          fileSize: selectedFile.size,
        }),
        updatedAt: new Date().toISOString(),
      });

      setModalVisible(false);
      resetForm();
      fetchDocuments();
    } catch (error) {
      console.error('Error updating document:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteDocument = async (documentId, filePath) => {
    try {
      // Delete file from storage
      if (filePath) {
        const fileRef = ref(storage, filePath);
        await deleteObject(fileRef);
      }

      // Delete document record
      await deleteDoc(doc(db, 'teacherDocuments', documentId));
      fetchDocuments();
    } catch (error) {
      console.error('Error deleting document:', error);
    }
  };

  const resetForm = () => {
    setSelectedDocument(null);
    setSelectedFile(null);
    setFormData({
      title: '',
      type: '',
      description: '',
      status: 'pending',
      expiryDate: '',
    });
  };

  const renderDocumentCard = (document) => (
    <Card key={document.id} style={styles.documentCard}>
      <Card.Content>
        <View style={styles.cardHeader}>
          <View>
            <Title>{document.title}</Title>
            <Text>Type: {document.type}</Text>
          </View>
          <Chip mode="outlined">{document.status}</Chip>
        </View>

        <Text style={styles.description}>{document.description}</Text>

        {document.expiryDate && (
          <Text style={styles.expiryDate}>
            Expires: {document.expiryDate}
          </Text>
        )}

        <View style={styles.fileInfo}>
          <Text>File: {document.fileName}</Text>
          <Text>Size: {(document.fileSize / 1024 / 1024).toFixed(2)} MB</Text>
        </View>

        <View style={styles.cardActions}>
          <CustomButton
            mode="outlined"
            onPress={() => window.open(document.fileUrl, '_blank')}
          >
            View Document
          </CustomButton>

          <IconButton
            icon="pencil"
            onPress={() => {
              setSelectedDocument(document);
              setFormData({
                title: document.title,
                type: document.type,
                description: document.description,
                status: document.status,
                expiryDate: document.expiryDate || '',
              });
              setModalVisible(true);
            }}
          />

          <IconButton
            icon="delete"
            onPress={() => handleDeleteDocument(document.id, document.filePath)}
          />
        </View>
      </Card.Content>
    </Card>
  );

  return (
    <View style={styles.container}>
      <Card style={styles.headerCard}>
        <Card.Content>
          <Title>Teacher Documents</Title>
          <Text>Manage teacher's documents and certifications</Text>
        </Card.Content>
      </Card>

      <ScrollView>
        {loading ? (
          <ActivityIndicator style={styles.loader} />
        ) : error ? (
          <Card style={styles.errorCard}>
            <Card.Content>
              <Text style={styles.errorText}>{error}</Text>
              {teacherId && (
                <CustomButton
                  mode="contained"
                  onPress={fetchDocuments}
                  style={styles.retryButton}
                >
                  Retry
                </CustomButton>
              )}
            </Card.Content>
          </Card>
        ) : documents.length === 0 ? (
          <Card style={styles.emptyCard}>
            <Card.Content>
              <Text style={styles.emptyText}>No documents found for this teacher.</Text>
            </Card.Content>
          </Card>
        ) : (
          documents.map(renderDocumentCard)
        )}
      </ScrollView>

      <Portal>
        <Modal
          visible={modalVisible}
          onDismiss={() => {
            setModalVisible(false);
            resetForm();
          }}
          contentContainerStyle={styles.modalContent}
        >
          <ScrollView>
            <Title>{selectedDocument ? 'Edit Document' : 'Upload Document'}</Title>

            <CustomInput
              label="Title"
              value={formData.title}
              onChangeText={(text) => setFormData({ ...formData, title: text })}
            />

            <List.Section title="Document Type">
              {documentTypes.map((type) => (
                <List.Item
                  key={type}
                  title={type}
                  onPress={() => setFormData({ ...formData, type })}
                  style={formData.type === type ? styles.selectedItem : null}
                  left={props => <List.Icon {...props} icon="file-document" />}
                />
              ))}
            </List.Section>

            <CustomInput
              label="Description"
              value={formData.description}
              onChangeText={(text) => setFormData({ ...formData, description: text })}
              multiline
              numberOfLines={3}
            />

            <CustomInput
              label="Expiry Date (if applicable)"
              value={formData.expiryDate}
              onChangeText={(text) => setFormData({ ...formData, expiryDate: text })}
            />

            <List.Section title="Status">
              {['pending', 'approved', 'expired'].map((status) => (
                <List.Item
                  key={status}
                  title={status.charAt(0).toUpperCase() + status.slice(1)}
                  onPress={() => setFormData({ ...formData, status })}
                  style={formData.status === status ? styles.selectedItem : null}
                  left={props => <List.Icon {...props} icon="checkbox-marked-circle" />}
                />
              ))}
            </List.Section>

            <CustomButton
              mode="outlined"
              onPress={pickDocument}
              style={styles.uploadButton}
            >
              {selectedFile ? 'Change Document' : 'Select Document'}
            </CustomButton>

            {selectedFile && (
              <Text style={styles.selectedFileName}>
                Selected: {selectedFile.name}
              </Text>
            )}

            <View style={styles.modalButtons}>
              <CustomButton
                mode="contained"
                onPress={selectedDocument ? handleUpdateDocument : handleAddDocument}
                loading={loading}
                disabled={!selectedFile && !selectedDocument}
              >
                {selectedDocument ? 'Update' : 'Upload'}
              </CustomButton>

              <CustomButton
                mode="outlined"
                onPress={() => {
                  setModalVisible(false);
                  resetForm();
                }}
              >
                Cancel
              </CustomButton>
            </View>
          </ScrollView>
        </Modal>
      </Portal>

      <CustomButton
        mode="contained"
        icon="plus"
        onPress={() => setModalVisible(true)}
        style={styles.addButton}
      >
        Upload Document
      </CustomButton>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  headerCard: {
    margin: 10,
    elevation: 4,
  },
  documentCard: {
    margin: 10,
    elevation: 2,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  description: {
    marginVertical: 10,
    color: '#666',
  },
  expiryDate: {
    color: '#e91e63',
    marginVertical: 5,
  },
  fileInfo: {
    backgroundColor: '#f0f0f0',
    padding: 10,
    borderRadius: 5,
    marginVertical: 10,
  },
  cardActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
    marginTop: 10,
  },
  loader: {
    marginTop: 20,
  },
  modalContent: {
    backgroundColor: 'white',
    padding: 20,
    margin: 20,
    borderRadius: 10,
    maxHeight: '80%',
  },
  selectedItem: {
    backgroundColor: '#e8f4f8',
  },
  uploadButton: {
    marginVertical: 10,
  },
  selectedFileName: {
    marginVertical: 10,
    color: '#666',
  },
  modalButtons: {
    marginTop: 20,
  },
  addButton: {
    margin: 16,
  },
  errorCard: {
    margin: 10,
    backgroundColor: '#ffebee',
  },
  errorText: {
    color: '#d32f2f',
    textAlign: 'center',
    marginBottom: 10,
  },
  retryButton: {
    marginTop: 10,
  },
  emptyCard: {
    margin: 10,
    padding: 20,
  },
  emptyText: {
    textAlign: 'center',
    color: '#757575',
  },
});

export default TeacherDocuments;
