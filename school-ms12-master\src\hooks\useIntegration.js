import { useContext } from 'react';
import { useAuth } from '../context/AuthContext';
import { useData } from '../context/DataContext';
import IntegrationService from '../services/IntegrationService';

export const useIntegration = () => {
  const { user } = useAuth();
  const { userRole, userData } = useData();

  // Role-specific integration functions
  const adminIntegration = {
    manageUsers: async (userData, role) => {
      return await IntegrationService.adminServices.createUser(userData, role);
    },
    manageClass: async (classData, action) => {
      return await IntegrationService.adminServices.manageClass(classData, action);
    },
    manageResults: async (results, action) => {
      return await IntegrationService.adminServices.manageResults(results, action);
    },
  };

  const teacherIntegration = {
    submitGrades: async (grades) => {
      return await IntegrationService.teacherServices.submitGrades(grades);
    },
    recordAttendance: async (attendanceData) => {
      return await IntegrationService.teacherServices.recordAttendance(attendanceData);
    },
    sendNotification: async (notification) => {
      return await IntegrationService.teacherServices.sendNotification(notification);
    },
  };

  const studentIntegration = {
    submitAssignment: async (assignmentData) => {
      return await IntegrationService.studentServices.submitAssignment(assignmentData);
    },
    viewGrades: async () => {
      return await IntegrationService.studentServices.viewGrades(user.uid);
    },
  };

  const parentIntegration = {
    trackProgress: async () => {
      return await IntegrationService.parentServices.trackProgress(userData.studentId);
    },
    communicateWithTeacher: async (messageData) => {
      return await IntegrationService.parentServices.communicateWithTeacher(messageData);
    },
  };

  // Common integration functions available to all roles
  const commonIntegration = {
    sendMessage: async (messageData) => {
      return await IntegrationService.commonServices.sendMessage({
        ...messageData,
        senderId: user.uid,
        senderRole: userRole,
      });
    },
    getNotifications: async () => {
      return await IntegrationService.commonServices.getNotifications(user.uid, userRole);
    },
    manageDocument: async (documentData, action) => {
      return await IntegrationService.commonServices.manageDocument(documentData, action);
    },
  };

  // Get role-specific integration functions
  const getRoleIntegration = () => {
    switch (userRole) {
      case 'admins':
        return adminIntegration;
      case 'teachers':
        return teacherIntegration;
      case 'students':
        return studentIntegration;
      case 'parents':
        return parentIntegration;
      default:
        return {};
    }
  };

  return {
    ...getRoleIntegration(),
    ...commonIntegration,
    userRole,
    userData,
  };
};

export default useIntegration;
