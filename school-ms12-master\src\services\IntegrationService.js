import FirebaseAuthService from './FirebaseAuthService';
import FirebaseDataService from './FirebaseDataService';
import { auth } from '../config/firebase';

class IntegrationService {
  // Authentication
  static async signIn(email, password) {
    return FirebaseAuthService.signIn(email, password);
  }

  static async signUp(email, password, userData, role) {
    return FirebaseAuthService.signUp(email, password, userData, role);
  }

  static async signOut() {
    return FirebaseAuthService.signOut();
  }

  static async resetPassword(email) {
    return FirebaseAuthService.resetPassword(email);
  }

  static async updatePassword(newPassword) {
    return FirebaseAuthService.updateUserPassword(newPassword);
  }

  static async updateProfile(profileData) {
    return FirebaseAuthService.updateUserProfile(profileData);
  }

  // User Management
  static async getUserData(userId) {
    return FirebaseAuthService.getUserData(userId);
  }

  static async getRoleData(userId, role) {
    return FirebaseAuthService.getRoleData(userId, role);
  }

  // Data Operations
  static async addData(collection, data) {
    return FirebaseDataService.addDocument(collection, data);
  }

  static async getData(collection, id) {
    return FirebaseDataService.getDocument(collection, id);
  }

  static async updateData(collection, id, data) {
    return FirebaseDataService.updateDocument(collection, id, data);
  }

  static async deleteData(collection, id) {
    return FirebaseDataService.deleteDocument(collection, id);
  }

  static async queryData(collection, constraints, orderBy, direction, limit, startAfter) {
    return FirebaseDataService.queryDocuments(collection, constraints, orderBy, direction, limit, startAfter);
  }

  // File Operations
  static async uploadFile(path, file) {
    return FirebaseDataService.uploadFile(path, file);
  }

  static async deleteFile(path) {
    return FirebaseDataService.deleteFile(path);
  }

  // Role-specific Operations
  static async getTeacherClasses(teacherId) {
    return FirebaseDataService.getTeacherClasses(teacherId);
  }

  static async getStudentsByClass(classId) {
    return FirebaseDataService.getStudentsByClass(classId);
  }

  static async getParentsByClass(classId) {
    return FirebaseDataService.getParentsByClass(classId);
  }

  static async getStudentGrades(studentId) {
    return FirebaseDataService.getStudentGrades(studentId);
  }

  static async getStudentAttendance(studentId) {
    return FirebaseDataService.getStudentAttendance(studentId);
  }

  // Communication
  static async sendMessage(message) {
    return FirebaseDataService.sendMessage(message);
  }

  static async getMessages(userId, role) {
    return FirebaseDataService.getMessages(userId, role);
  }

  static async createAnnouncement(announcement) {
    return FirebaseDataService.createAnnouncement(announcement);
  }

  static async getAnnouncements(filters = []) {
    return FirebaseDataService.getAnnouncements(filters);
  }

  // Utility Methods
  static getCurrentUser() {
    return auth.currentUser;
  }

  static onAuthStateChanged(callback) {
    return auth.onAuthStateChanged(callback);
  }
}

export default IntegrationService;
