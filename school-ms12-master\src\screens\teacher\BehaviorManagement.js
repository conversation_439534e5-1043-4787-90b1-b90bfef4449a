import React, { useState, useEffect } from 'react';
import { View, ScrollView, StyleSheet } from 'react-native';
import { Card, Title, Text, DataTable, Portal, Modal, TextInput, ActivityIndicator, Chip, IconButton } from 'react-native-paper';
import { db, auth } from '../../config/firebase';
import { collection, query, where, getDocs, addDoc, doc } from 'firebase/firestore';
import CustomButton from '../../components/common/CustomButton';
import { Picker } from '@react-native-picker/picker';
import EthiopianCalendar from '../../utils/EthiopianCalendar';

const BehaviorManagement = () => {
  const [teacherData, setTeacherData] = useState(null);
  const [selectedClass, setSelectedClass] = useState('');
  const [selectedSection, setSelectedSection] = useState('');
  const [selectedSubject, setSelectedSubject] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [classes, setClasses] = useState([]);
  const [students, setStudents] = useState([]);
  const [behaviorRecords, setBehaviorRecords] = useState({});
  const [modalVisible, setModalVisible] = useState(false);
  const [behaviorForm, setBehaviorForm] = useState({
    type: 'positive', // positive, negative, neutral
    category: '', // academic, disciplinary, social
    description: '',
    points: '0',
    actionTaken: '',
    parentNotified: false,
  });

  const behaviorCategories = {
    academic: [
      'Class Participation',
      'Assignment Completion',
      'Academic Honesty',
      'Study Habits',
    ],
    disciplinary: [
      'Classroom Behavior',
      'School Rules Compliance',
      'Attendance',
      'Punctuality',
    ],
    social: [
      'Peer Interaction',
      'Communication Skills',
      'Leadership',
      'Teamwork',
    ],
  };

  useEffect(() => {
    fetchTeacherData();
  }, []);

  const fetchTeacherData = async () => {
    try {
      setLoading(true);
      setError(null);

      const userDocRef = doc(db, 'users', auth.currentUser.uid);
      const userDoc = await getDoc(userDocRef);

      if (userDoc.exists()) {
        const userData = userDoc.data();
        console.log('User document found:', userData);
        setTeacherData(userData);

        // Group sections by class
        const sectionsByClass = {};
        userData.sections?.forEach(section => {
          if (!sectionsByClass[section.classId]) {
            sectionsByClass[section.classId] = {
              id: section.classId,
              name: section.className,
              sections: new Set(),
              subjects: new Map()
            };
          }
          sectionsByClass[section.classId].sections.add(section.sectionName);
          
          // Store subjects by section
          const sectionKey = `${section.sectionName}`;
          if (!sectionsByClass[section.classId].subjects.has(sectionKey)) {
            sectionsByClass[section.classId].subjects.set(sectionKey, []);
          }
          sectionsByClass[section.classId].subjects.get(sectionKey).push(section.subject);
        });

        // Convert to array format
        const classesArray = Object.values(sectionsByClass).map(cls => ({
          ...cls,
          sections: Array.from(cls.sections),
          subjects: Object.fromEntries(cls.subjects)
        }));

        setClasses(classesArray);
      } else {
        setError('Teacher profile not found');
      }
    } catch (err) {
      console.error('Error fetching teacher data:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const getAvailableSubjects = () => {
    if (!selectedClass || !selectedSection || !teacherData?.sections) return [];
    return teacherData.sections
      .filter(s => s.classId === selectedClass && s.sectionName === selectedSection)
      .map(s => s.subject);
  };

  const renderClassPicker = () => (
    <View style={styles.pickerContainer}>
      <Text>Select Class</Text>
      <Picker
        selectedValue={selectedClass}
        onValueChange={(value) => {
          setSelectedClass(value);
          setSelectedSection('');
          setSelectedSubject('');
        }}
        style={styles.picker}
      >
        <Picker.Item label="Select a class" value="" />
        {classes.map((cls) => (
          <Picker.Item key={cls.id} label={`Class ${cls.name}`} value={cls.id} />
        ))}
      </Picker>
    </View>
  );

  const renderSectionPicker = () => {
    const currentClass = classes.find(c => c.id === selectedClass);
    return (
      <View style={styles.pickerContainer}>
        <Text>Select Section</Text>
        <Picker
          selectedValue={selectedSection}
          onValueChange={(value) => {
            setSelectedSection(value);
            setSelectedSubject('');
            fetchSectionStudents();
          }}
          style={styles.picker}
        >
          <Picker.Item label="Select a section" value="" />
          {currentClass?.sections.map((section) => (
            <Picker.Item key={section} label={`Section ${section}`} value={section} />
          ))}
        </Picker>
      </View>
    );
  };

  const renderSubjectPicker = () => (
    <View style={styles.pickerContainer}>
      <Text>Select Subject</Text>
      <Picker
        selectedValue={selectedSubject}
        onValueChange={setSelectedSubject}
        style={styles.picker}
      >
        <Picker.Item label="Select a subject" value="" />
        {getAvailableSubjects().map((subject) => (
          <Picker.Item key={subject} label={subject} value={subject} />
        ))}
      </Picker>
    </View>
  );

  const fetchSectionStudents = async () => {
    try {
      setLoading(true);
      setError(null);

      const studentsRef = collection(db, 'users');
      const q = query(
        studentsRef,
        where('role', '==', 'student'),
        where('classId', '==', selectedClass),
        where('sectionName', '==', selectedSection)
      );

      const querySnapshot = await getDocs(q);
      const students = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })).sort((a, b) => {
        if (a.rollNumber && b.rollNumber) {
          return parseInt(a.rollNumber) - parseInt(b.rollNumber);
        }
        return `${a.firstName} ${a.lastName}`.localeCompare(`${b.firstName} ${b.lastName}`);
      });

      console.log('Found students:', students.length);
      setStudents(students);

      // Fetch existing behavior records
      const behaviorRef = collection(db, 'behavior');
      const behaviorQuery = query(
        behaviorRef,
        where('classId', '==', selectedClass),
        where('sectionName', '==', selectedSection)
      );
      const behaviorSnapshot = await getDocs(behaviorQuery);
      
      const behaviorData = {};
      behaviorSnapshot.forEach(doc => {
        const data = doc.data();
        if (!behaviorData[data.studentId]) {
          behaviorData[data.studentId] = [];
        }
        behaviorData[data.studentId].push({
          id: doc.id,
          ...data
        });
      });

      setBehaviorRecords(behaviorData);
    } catch (error) {
      console.error('Error fetching students:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleAddBehavior = async () => {
    try {
      setLoading(true);
      const behaviorRef = collection(db, 'behavior');
      await addDoc(behaviorRef, {
        ...behaviorForm,
        studentId: students[0].id,
        classId: selectedClass,
        sectionName: selectedSection,
        teacherId: auth.currentUser.uid,
        timestamp: new Date().toISOString(),
      });
      
      setModalVisible(false);
      setBehaviorForm({
        type: 'positive',
        category: '',
        description: '',
        points: '0',
        actionTaken: '',
        parentNotified: false,
      });
      
      fetchSectionStudents();
    } catch (error) {
      console.error('Error adding behavior record:', error);
    } finally {
      setLoading(false);
    }
  };

  const renderStudentList = () => (
    <DataTable>
      <DataTable.Header>
        <DataTable.Title>Roll No</DataTable.Title>
        <DataTable.Title>Name</DataTable.Title>
        <DataTable.Title numeric>Records</DataTable.Title>
        <DataTable.Title>Actions</DataTable.Title>
      </DataTable.Header>

      {students.map((student) => (
        <DataTable.Row key={student.id}>
          <DataTable.Cell>{student.rollNumber || 'N/A'}</DataTable.Cell>
          <DataTable.Cell>{`${student.firstName} ${student.lastName}`}</DataTable.Cell>
          <DataTable.Cell numeric>
            {behaviorRecords[student.id]?.length || 0}
          </DataTable.Cell>
          <DataTable.Cell>
            <IconButton
              icon="plus"
              size={20}
              onPress={() => handleAddBehaviorRecord(student)}
            />
            {behaviorRecords[student.id]?.length > 0 && (
              <IconButton
                icon="eye"
                size={20}
                onPress={() => handleViewRecords(student)}
              />
            )}
          </DataTable.Cell>
        </DataTable.Row>
      ))}
    </DataTable>
  );

  if (loading) {
    return (
      <View style={[styles.container, styles.centerContent]}>
        <ActivityIndicator size="large" />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <ScrollView>
        <Card style={styles.card}>
          <Card.Content>
            <Title>Behavior Management</Title>
            
            {renderClassPicker()}
            {selectedClass && renderSectionPicker()}
            {selectedClass && selectedSection && renderSubjectPicker()}

            {selectedClass && selectedSection && (
              <>
                <CustomButton
                  mode="contained"
                  onPress={() => setModalVisible(true)}
                  style={styles.addButton}
                >
                  Add Behavior Record
                </CustomButton>

                {renderStudentList()}
              </>
            )}
          </Card.Content>
        </Card>
      </ScrollView>

      <Portal>
        <Modal
          visible={modalVisible}
          onDismiss={() => setModalVisible(false)}
          contentContainerStyle={styles.modalContent}
        >
          <Title>Add Behavior Record</Title>
          
          <View style={styles.formField}>
            <Text>Type</Text>
            <Picker
              selectedValue={behaviorForm.type}
              onValueChange={(value) =>
                setBehaviorForm({ ...behaviorForm, type: value })
              }
              style={styles.picker}
            >
              <Picker.Item label="Positive" value="positive" />
              <Picker.Item label="Negative" value="negative" />
              <Picker.Item label="Neutral" value="neutral" />
            </Picker>
          </View>

          <View style={styles.formField}>
            <Text>Category</Text>
            <Picker
              selectedValue={behaviorForm.category}
              onValueChange={(value) =>
                setBehaviorForm({ ...behaviorForm, category: value })
              }
              style={styles.picker}
            >
              <Picker.Item label="Select category" value="" />
              {Object.entries(behaviorCategories).map(([key, categories]) => (
                categories.map((category) => (
                  <Picker.Item key={category} label={category} value={category} />
                ))
              ))}
            </Picker>
          </View>

          <TextInput
            label="Description"
            value={behaviorForm.description}
            onChangeText={(text) =>
              setBehaviorForm({ ...behaviorForm, description: text })
            }
            style={styles.input}
            multiline
          />

          <TextInput
            label="Points"
            value={behaviorForm.points}
            onChangeText={(text) =>
              setBehaviorForm({ ...behaviorForm, points: text })
            }
            keyboardType="numeric"
            style={styles.input}
          />

          <TextInput
            label="Action Taken"
            value={behaviorForm.actionTaken}
            onChangeText={(text) =>
              setBehaviorForm({ ...behaviorForm, actionTaken: text })
            }
            style={styles.input}
            multiline
          />

          <View style={styles.modalButtons}>
            <CustomButton
              mode="outlined"
              onPress={() => setModalVisible(false)}
              style={styles.modalButton}
            >
              Cancel
            </CustomButton>
            <CustomButton
              mode="contained"
              onPress={handleAddBehavior}
              style={styles.modalButton}
              loading={loading}
            >
              Save
            </CustomButton>
          </View>
        </Modal>
      </Portal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  card: {
    margin: 16,
    elevation: 4,
  },
  pickerContainer: {
    marginVertical: 10,
  },
  picker: {
    marginTop: 5,
    backgroundColor: '#fff',
    borderRadius: 4,
  },
  addButton: {
    marginVertical: 16,
  },
  modalContent: {
    backgroundColor: 'white',
    padding: 20,
    margin: 20,
    borderRadius: 8,
  },
  formField: {
    marginVertical: 10,
  },
  input: {
    marginVertical: 8,
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 16,
  },
  modalButton: {
    marginLeft: 8,
  },
});

export default BehaviorManagement;
