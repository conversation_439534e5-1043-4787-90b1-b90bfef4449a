{"cli": {"version": ">= 5.9.1", "appVersionSource": "local"}, "build": {"development": {"developmentClient": true, "distribution": "internal", "android": {"buildType": "apk", "gradleCommand": ":app:assembleDebug", "withoutCredentials": true}}, "preview": {"distribution": "internal", "android": {"buildType": "apk", "withoutCredentials": true}}, "production": {"autoIncrement": true}, "apk": {"android": {"buildType": "apk", "gradleCommand": ":app:assembleRelease", "withoutCredentials": true, "image": "latest"}}}, "submit": {"production": {}}}