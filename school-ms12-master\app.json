{"expo": {"name": "School Management System", "slug": "school-ms", "version": "1.0.0", "jsEngine": "hermes", "orientation": "portrait", "icon": "./src/assets/logo.png", "userInterfaceStyle": "light", "scheme": "school-ms", "splash": {"image": "./src/assets/logo.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.schoolmanagementsystem", "infoPlist": {"UIBackgroundModes": ["remote-notification"]}}, "android": {"adaptiveIcon": {"foregroundImage": "./src/assets/logo.png", "backgroundColor": "#ffffff"}, "package": "com.schoolmanagementsystem", "googleServicesFile": "./google-services.json", "permissions": ["NOTIFICATIONS", "VIBRATE", "RECEIVE_BOOT_COMPLETED", "SCHEDULE_EXACT_ALARM"]}, "web": {"favicon": "./src/assets/default-avatar.png"}, "plugins": [["expo-notifications", {"icon": "./src/assets/default-avatar.png", "color": "#ffffff", "sounds": []}], "expo-dev-client", "expo-system-ui"], "extra": {"eas": {"projectId": "e895b937-c30e-4e3d-867b-b10a90d4185a"}}, "notification": {"icon": "./src/assets/default-avatar.png", "color": "#000000", "androidMode": "default", "androidCollapsedTitle": "#{unread_count} new notifications", "iosDisplayInForeground": true}}}