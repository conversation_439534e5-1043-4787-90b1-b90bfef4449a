const { initializeApp } = require('firebase/app');
const { getFirestore, collection, addDoc, getDocs, query, where } = require('firebase/firestore');

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyBHkGAhm80xBIECyaHbZ_DbeNzu3jnVuDg",
  authDomain: "schoolmn-16cbc.firebaseapp.com",
  projectId: "schoolmn-16cbc",
  storageBucket: "schoolmn-16cbc.appspot.com",
  messagingSenderId: "999485613068",
  appId: "1:999485613068:web:e9c0c3e0a4c7f4e4c8b8b8"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

// Define the morning and afternoon time slots
const morningTimeSlots = [
  { period: 1, start: '08:00', end: '08:40', session: 'morning' },
  { period: 2, start: '08:45', end: '09:25', session: 'morning' },
  { period: 3, start: '09:30', end: '10:10', session: 'morning' },
  { period: 4, start: '10:15', end: '10:55', session: 'morning' },
  { period: 5, start: '11:00', end: '11:40', session: 'morning' },
  { period: 6, start: '11:45', end: '12:25', session: 'morning' },
];

const afternoonTimeSlots = [
  { period: 1, start: '12:30', end: '13:10', session: 'afternoon' },
  { period: 2, start: '13:15', end: '13:55', session: 'afternoon' },
  { period: 3, start: '14:00', end: '14:40', session: 'afternoon' },
  { period: 4, start: '14:45', end: '15:25', session: 'afternoon' },
  { period: 5, start: '15:30', end: '16:10', session: 'afternoon' },
  { period: 6, start: '16:15', end: '16:55', session: 'afternoon' },
];

// Days of the week
const days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'];

// Sample subjects
const subjects = [
  { id: 'math', name: 'Mathematics' },
  { id: 'science', name: 'Science' },
  { id: 'english', name: 'English' },
  { id: 'history', name: 'History' },
  { id: 'geography', name: 'Geography' },
  { id: 'physics', name: 'Physics' },
  { id: 'chemistry', name: 'Chemistry' },
  { id: 'biology', name: 'Biology' },
  { id: 'computer', name: 'Computer Science' },
  { id: 'art', name: 'Art' },
  { id: 'music', name: 'Music' },
  { id: 'pe', name: 'Physical Education' },
];

// Sample room numbers
const rooms = ['101', '102', '103', '104', '105', '201', '202', '203', '204', '205', '301', '302'];

// Function to generate a random timetable entry
const generateTimetableEntry = (classId, sectionName, teacherId, day, timeSlot) => {
  const randomSubject = subjects[Math.floor(Math.random() * subjects.length)];
  const randomRoom = rooms[Math.floor(Math.random() * rooms.length)];
  
  return {
    classId,
    sectionName,
    day,
    periodNumber: timeSlot.period,
    startTime: timeSlot.start,
    endTime: timeSlot.end,
    session: timeSlot.session,
    subjectId: randomSubject.id,
    subjectName: randomSubject.name,
    teacherId,
    teacherName: 'Teacher ' + teacherId.substring(0, 5),
    roomNumber: randomRoom,
    published: true,
    createdAt: new Date().toISOString()
  };
};

// Function to generate timetable for a class and section
const generateClassTimetable = async (classId, sectionName, teacherIds) => {
  const timetableEntries = [];
  
  // For each day of the week
  days.forEach(day => {
    // Randomly decide if this class has morning or afternoon session
    const sessionType = Math.random() > 0.5 ? 'morning' : 'afternoon';
    const timeSlots = sessionType === 'morning' ? morningTimeSlots : afternoonTimeSlots;
    
    // For each period in the session
    timeSlots.forEach(timeSlot => {
      // Randomly select a teacher
      const randomTeacherId = teacherIds[Math.floor(Math.random() * teacherIds.length)];
      
      // Generate timetable entry
      const entry = generateTimetableEntry(classId, sectionName, randomTeacherId, day, timeSlot);
      timetableEntries.push(entry);
    });
  });
  
  return timetableEntries;
};

// Main function to generate sample timetables
const generateSampleTimetables = async () => {
  try {
    console.log('Generating sample timetable data...');
    
    // Get all classes
    const classesRef = collection(db, 'classes');
    const classesSnapshot = await getDocs(classesRef);
    
    if (classesSnapshot.empty) {
      console.log('No classes found. Please create classes first.');
      return;
    }
    
    // Get all teachers
    const teachersRef = collection(db, 'teachers');
    const teachersSnapshot = await getDocs(teachersRef);
    
    if (teachersSnapshot.empty) {
      console.log('No teachers found. Please create teachers first.');
      return;
    }
    
    // Extract teacher IDs
    const teacherIds = [];
    teachersSnapshot.forEach(doc => {
      teacherIds.push(doc.id);
    });
    
    console.log(`Found ${classesSnapshot.size} classes and ${teacherIds.length} teachers`);
    
    // Generate timetables for each class and section
    let totalEntries = 0;
    const timetablesRef = collection(db, 'timetables');
    
    for (const classDoc of classesSnapshot.docs) {
      const classData = classDoc.data();
      const classId = classDoc.id;
      
      // Get sections for this class
      const sections = classData.sections || ['A', 'B'];
      
      for (const section of sections) {
        console.log(`Generating timetable for class ${classData.name || classId}, section ${section}`);
        
        // Check if timetable already exists for this class and section
        const existingQuery = query(
          timetablesRef,
          where('classId', '==', classId),
          where('sectionName', '==', section)
        );
        const existingSnapshot = await getDocs(existingQuery);
        
        if (!existingSnapshot.empty) {
          console.log(`Timetable already exists for class ${classData.name || classId}, section ${section}. Skipping.`);
          continue;
        }
        
        // Generate timetable entries
        const entries = await generateClassTimetable(classId, section, teacherIds);
        
        // Add entries to Firestore
        for (const entry of entries) {
          await addDoc(timetablesRef, entry);
          totalEntries++;
        }
        
        console.log(`Added ${entries.length} timetable entries for class ${classData.name || classId}, section ${section}`);
      }
    }
    
    console.log(`Successfully generated ${totalEntries} timetable entries`);
  } catch (error) {
    console.error('Error generating sample timetables:', error);
  }
};

// Run the generator
generateSampleTimetables()
  .then(() => {
    console.log('Script completed');
    process.exit(0);
  })
  .catch(error => {
    console.error('Script failed:', error);
    process.exit(1);
  });
