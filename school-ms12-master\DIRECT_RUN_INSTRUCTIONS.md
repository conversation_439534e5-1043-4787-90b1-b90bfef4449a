# Running the School Management System App Directly

This document provides instructions for running the School Management System app directly on Android and iOS devices/emulators without using EAS Build.

## Prerequisites

- Node.js and npm installed
- For Android:
  - Android Studio installed
  - Android SDK installed and configured
  - Android device or emulator set up
- For iOS (macOS only):
  - Xcode installed
  - iOS device or simulator set up
  - CocoaPods installed

## Android Setup and Running

### Option 1: Using Batch Scripts (Windows)

1. Clean the Android build (if needed):
   ```
   .\clean-android.bat
   ```

2. Build and run the Android app:
   ```
   .\run-android.bat
   ```

### Option 2: Using npm Scripts

1. Clean the Android build (if needed):
   ```
   npm run android-clean
   ```

2. Build the Android app:
   ```
   npm run android-build
   ```

3. Install and run the Android app:
   ```
   npm run android-run
   ```

4. For creating a release bundle:
   ```
   npm run android-bundle
   ```

## iOS Setup and Running (macOS Only)

Since iOS project files can only be generated on macOS, follow these steps if you're using a Mac:

1. Generate iOS project files:
   ```
   npx expo prebuild --platform ios
   ```

2. Install CocoaPods dependencies:
   ```
   cd ios
   pod install
   cd ..
   ```

3. Run the iOS app:
   ```
   npm run ios
   ```

### Manual iOS Build (Advanced)

If you need to build the iOS app manually:

1. Open the Xcode project:
   ```
   open ios/SchoolManagementSystem.xcworkspace
   ```

2. In Xcode, select your target device/simulator and click the Run button.

## Troubleshooting

### Android Issues

- If you encounter build errors, try cleaning the project:
  ```
  npm run android-clean
  ```

- Make sure your Android SDK is properly configured in your environment variables.

- Check that your device is properly connected and USB debugging is enabled.

### iOS Issues

- If CocoaPods installation fails, try updating CocoaPods:
  ```
  sudo gem install cocoapods
  ```

- Make sure your Apple Developer account is properly set up in Xcode.

- If you encounter signing issues, check the signing configuration in Xcode project settings.

## Notes

- The Android app can be built and run directly on Windows, Linux, or macOS.
- The iOS app can only be built and run on macOS.
- Make sure you have the necessary development certificates and provisioning profiles for iOS distribution.
