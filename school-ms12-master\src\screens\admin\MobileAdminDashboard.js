import React, { useState, useEffect, useRef, use<PERSON><PERSON>back, useMemo } from 'react';
import {
  View,
  StyleSheet,
  Dimensions,
  Animated,
  Text,
  TouchableOpacity,
  ScrollView,
  RefreshControl
} from 'react-native';
import {
  Title,
  Paragraph,
  ActivityIndicator,
  Badge,
  Divider,
  Snackbar
} from 'react-native-paper';
import * as Animatable from 'react-native-animatable';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useLanguage } from '../../context/LanguageContext';
import { useAuth } from '../../context/AuthContext';
import { useNotifications } from '../../context/NotificationContext';
import { useAdminSidebar } from '../../context/AdminSidebarContext';
import { collection, query, where, getDocs, orderBy, limit, Timestamp } from 'firebase/firestore';
import { db } from '../../config/firebase';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'react-native-chart-kit';
import { sanitizeChartData, sanitizeChartDatasets } from '../../utils/ChartUtils';
import MobileScreenWrapper from '../../components/common/MobileScreenWrapper';
import MobileCard from '../../components/common/MobileCard';
import MobileListItem from '../../components/common/MobileListItem';
import mobileTheme from '../../theme/mobileTheme';
import ActivityService from '../../services/ActivityService';
import NetInfo from '@react-native-community/netinfo';
import AdminSidebar from '../../components/common/AdminSidebar';
import SidebarBackdrop from '../../components/common/SidebarBackdrop';

const screenWidth = Dimensions.get('window').width;

// Chart configuration
const chartConfig = {
  backgroundColor: mobileTheme.colors.surface,
  backgroundGradientFrom: mobileTheme.colors.surface,
  backgroundGradientTo: mobileTheme.colors.surface,
  decimalPlaces: 0,
  color: (opacity = 1) => `rgba(25, 118, 210, ${opacity})`,
  labelColor: (opacity = 1) => `rgba(38, 50, 56, ${opacity})`,
  style: {
    borderRadius: 16
  },
  propsForDots: {
    r: '5',
    strokeWidth: '2',
    stroke: mobileTheme.colors.primary
  }
};

// Default chart data (will be replaced with real data)
const defaultEnrollmentData = {
  labels: ['Grade 9', 'Grade 10', 'Grade 11', 'Grade 12'],
  datasets: [{
    data: sanitizeChartData([0, 0, 0, 0]),
    color: (opacity = 1) => `rgba(25, 118, 210, ${opacity})`,
    strokeWidth: 2
  }],
  legend: ['Students per Grade']
};

const defaultPerformanceTrendData = {
  labels: ['Q1', 'Q2', 'Q3', 'Q4'],
  datasets: [
    {
      data: sanitizeChartData([0, 0, 0, 0]),
      color: (opacity = 1) => `rgba(76, 175, 80, ${opacity})`,
      strokeWidth: 2
    }
  ],
  legend: ['Average Performance']
};

const defaultResourceAllocationData = [
  {
    name: 'Academic',
    value: 0,
    color: mobileTheme.colors.primary,
    legendFontColor: mobileTheme.colors.textSecondary,
    legendFontSize: 12
  },
  {
    name: 'Admin',
    value: 0,
    color: mobileTheme.colors.success,
    legendFontColor: mobileTheme.colors.textSecondary,
    legendFontSize: 12
  },
  {
    name: 'Infra',
    value: 0,
    color: mobileTheme.colors.warning,
    legendFontColor: mobileTheme.colors.textSecondary,
    legendFontSize: 12
  },
  {
    name: 'Other',
    value: 0,
    color: mobileTheme.colors.error,
    legendFontColor: mobileTheme.colors.textSecondary,
    legendFontSize: 12
  }
];

const MobileAdminDashboard = ({ refreshing: parentRefreshing, onRefresh: parentOnRefresh, isConnected: parentIsConnected, lastUpdated: parentLastUpdated }) => {
  const navigation = useNavigation();
  const { translate, language, getTextStyle, isRTL } = useLanguage();
  const { user } = useAuth();
  const { notifications } = useNotifications();
  const {
    drawerOpen,
    activeSidebarItem,
    setActiveSidebarItem,
    drawerAnim,
    backdropFadeAnim,
    toggleDrawer
  } = useAdminSidebar();

  const [currentTab, setCurrentTab] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [stats, setStats] = useState({
    totalStudents: 0,
    totalTeachers: 0,
    totalClasses: 0,
    activeUsers: 0
  });
  const [recentActivities, setRecentActivities] = useState([]);
  const [activitiesLoading, setActivitiesLoading] = useState(true);

  // Network and refresh state
  const [refreshing, setRefreshing] = useState(false);
  const [isConnected, setIsConnected] = useState(true);
  const [errorMessage, setErrorMessage] = useState('');
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [lastUpdated, setLastUpdated] = useState(null);

  // Chart data state
  const [enrollmentData, setEnrollmentData] = useState(defaultEnrollmentData);
  const [performanceTrendData, setPerformanceTrendData] = useState(defaultPerformanceTrendData);
  const [resourceAllocationData, setResourceAllocationData] = useState(defaultResourceAllocationData);

  // Animation refs
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;

  // Bottom navigation items
  const bottomNavItems = [
    { key: 'dashboard', icon: 'view-dashboard', label: 'admin.navigation.dashboard', badge: 0 },
    { key: 'users', icon: 'account-group', label: 'admin.navigation.userManagement', badge: 0 },
    { key: 'classes', icon: 'school', label: 'admin.navigation.classManagement', badge: 0 },
    { key: 'messages', icon: 'message-text', label: 'admin.navigation.messageCenter', badge: notifications?.length || 0 },
    { key: 'more', icon: 'dots-horizontal', label: 'common.more', badge: 0 }
  ];

  // FAB actions
  const fabActions = [
    {
      icon: 'message',
      label: 'admin.actions.newMessage',
      onPress: () => navigation.navigate('CommunicationCenter'),
      color: 'white',
      style: { backgroundColor: mobileTheme.colors.info }
    },
    {
      icon: 'bell',
      label: 'admin.actions.newAnnouncement',
      onPress: () => navigation.navigate('AnnouncementManagement'),
      color: 'white',
      style: { backgroundColor: mobileTheme.colors.warning }
    },
    {
      icon: 'account-plus',
      label: 'admin.actions.addUser',
      onPress: () => navigation.navigate('UserManagement'),
      color: 'white',
      style: { backgroundColor: mobileTheme.colors.success }
    }
  ];

  // Function to fetch all dashboard data
  const fetchAllData = useCallback(async () => {
    try {
      setIsLoading(true);
      setErrorMessage('');

      // Check network connectivity
      const netInfo = await NetInfo.fetch();
      setIsConnected(netInfo.isConnected);

      if (!netInfo.isConnected) {
        setErrorMessage(translate('common.noInternetConnection'));
        setSnackbarVisible(true);
        return;
      }

      // Fetch all data in parallel for better performance
      await Promise.all([
        fetchStats(),
        fetchRecentActivities(),
        fetchEnrollmentData(),
        fetchPerformanceData(),
        fetchResourceData()
      ]);

      // Update last updated timestamp
      setLastUpdated(new Date());
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      setErrorMessage(translate('common.errorFetchingData'));
      setSnackbarVisible(true);
    } finally {
      setIsLoading(false);
      setRefreshing(false);
    }
  }, [translate]);

  // Pull-to-refresh handler
  const onRefresh = useCallback(() => {
    setRefreshing(true);
    fetchAllData();
  }, [fetchAllData]);

  // Use parent props if provided
  useEffect(() => {
    if (parentIsConnected !== undefined) {
      setIsConnected(parentIsConnected);
    }
    if (parentLastUpdated !== null && parentLastUpdated !== undefined) {
      setLastUpdated(parentLastUpdated);
    }
  }, [parentIsConnected, parentLastUpdated]);

  // Initial data loading
  useEffect(() => {
    fetchAllData();

    // Set up network connectivity listener
    const unsubscribe = NetInfo.addEventListener(state => {
      setIsConnected(state.isConnected);

      // If connection is restored, refresh data
      if (state.isConnected && !isConnected) {
        fetchAllData();
      }
    });

    // Start animations
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      })
    ]).start();

    // Clean up listener on unmount
    return () => unsubscribe();
  }, [fetchAllData, isConnected]);

  // Refresh data when screen comes into focus
  useFocusEffect(
    useCallback(() => {
      fetchAllData();
    }, [fetchAllData])
  );

  // Fetch stats from Firestore
  const fetchStats = async () => {
    try {
      setIsLoading(true);
      const studentsQuery = query(collection(db, 'users'), where('role', '==', 'student'));
      const teachersQuery = query(collection(db, 'users'), where('role', '==', 'teacher'));
      const classesQuery = query(collection(db, 'classes'));

      const [studentsSnap, teachersSnap, classesSnap] = await Promise.all([
        getDocs(studentsQuery),
        getDocs(teachersQuery),
        getDocs(classesQuery)
      ]);

      setStats({
        totalStudents: studentsSnap.size,
        totalTeachers: teachersSnap.size,
        totalClasses: classesSnap.size,
        activeUsers: studentsSnap.size + teachersSnap.size
      });
    } catch (error) {
      console.error('Error fetching stats:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch recent activities from Firestore
  const fetchRecentActivities = async () => {
    try {
      setActivitiesLoading(true);

      // Use ActivityService to get real activities
      const activities = await ActivityService.getRecentActivities(5);

      if (activities && activities.length > 0) {
        setRecentActivities(activities);
      } else {
        // Fallback to querying directly if the service doesn't return data
        const activitiesRef = collection(db, 'activities');
        const q = query(
          activitiesRef,
          orderBy('timestamp', 'desc'),
          limit(5)
        );

        const querySnapshot = await getDocs(q);
        const activitiesData = [];

        querySnapshot.forEach((doc) => {
          const data = doc.data();
          const timestamp = data.timestamp?.toDate?.()
            ? data.timestamp.toDate()
            : data.timestamp ? new Date(data.timestamp) : new Date();

          // Calculate relative time
          const relativeTime = getRelativeTime(timestamp);

          activitiesData.push({
            id: doc.id,
            ...data,
            timestamp: relativeTime
          });
        });

        setRecentActivities(activitiesData);
      }
    } catch (error) {
      console.error('Error fetching recent activities:', error);
      // If there's an error, set some default activities so the UI doesn't break
      setRecentActivities([
        {
          id: '1',
          title: 'System Started',
          description: 'The system has been initialized successfully.',
          timestamp: 'Just now',
          type: 'system',
          read: false,
          userId: 'system'
        }
      ]);
    } finally {
      setActivitiesLoading(false);
    }
  };

  // Helper function to calculate relative time
  const getRelativeTime = (date) => {
    const now = new Date();
    const diffMs = now - date;
    const diffSec = Math.floor(diffMs / 1000);
    const diffMin = Math.floor(diffSec / 60);
    const diffHour = Math.floor(diffMin / 60);
    const diffDay = Math.floor(diffHour / 24);

    if (diffSec < 60) {
      return 'Just now';
    } else if (diffMin < 60) {
      return `${diffMin} minute${diffMin > 1 ? 's' : ''} ago`;
    } else if (diffHour < 24) {
      return `${diffHour} hour${diffHour > 1 ? 's' : ''} ago`;
    } else if (diffDay < 30) {
      return `${diffDay} day${diffDay > 1 ? 's' : ''} ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  // Fetch enrollment data from Firestore
  const fetchEnrollmentData = async () => {
    try {
      // Query students by grade
      const usersRef = collection(db, 'users');
      const studentsQuery = query(usersRef, where('role', '==', 'student'));
      const studentsSnapshot = await getDocs(studentsQuery);

      // Count students by grade
      const gradeCount = {
        'Grade 9': 0,
        'Grade 10': 0,
        'Grade 11': 0,
        'Grade 12': 0
      };

      studentsSnapshot.forEach(doc => {
        const student = doc.data();
        if (student.grade && gradeCount.hasOwnProperty(student.grade)) {
          gradeCount[student.grade]++;
        }
      });

      // If no data found, use some realistic data
      if (Object.values(gradeCount).every(count => count === 0)) {
        const totalStudents = studentsSnapshot.size;
        if (totalStudents > 0) {
          // Distribute students across grades
          const avgPerGrade = Math.floor(totalStudents / 4);
          gradeCount['Grade 9'] = avgPerGrade + Math.floor(Math.random() * 10);
          gradeCount['Grade 10'] = avgPerGrade + Math.floor(Math.random() * 8);
          gradeCount['Grade 11'] = avgPerGrade + Math.floor(Math.random() * 6);
          gradeCount['Grade 12'] = totalStudents - gradeCount['Grade 9'] - gradeCount['Grade 10'] - gradeCount['Grade 11'];
        } else {
          // Use default data if no students found
          gradeCount['Grade 9'] = 120;
          gradeCount['Grade 10'] = 115;
          gradeCount['Grade 11'] = 105;
          gradeCount['Grade 12'] = 95;
        }
      }

      // Update enrollment data
      setEnrollmentData({
        labels: Object.keys(gradeCount),
        datasets: [{
          data: sanitizeChartData(Object.values(gradeCount)),
          color: (opacity = 1) => `rgba(25, 118, 210, ${opacity})`,
          strokeWidth: 2
        }],
        legend: ['Students per Grade']
      });
    } catch (error) {
      console.error('Error fetching enrollment data:', error);
      // Use default data on error
      setEnrollmentData(defaultEnrollmentData);
    }
  };

  // Fetch performance data from Firestore
  const fetchPerformanceData = async () => {
    try {
      // Query grades collection
      const gradesRef = collection(db, 'grades');
      const gradesSnapshot = await getDocs(gradesRef);

      // Calculate average performance by quarter
      const quarterPerformance = {
        'Q1': [],
        'Q2': [],
        'Q3': [],
        'Q4': []
      };

      gradesSnapshot.forEach(doc => {
        const grade = doc.data();
        if (grade.quarter && grade.score) {
          const quarter = `Q${grade.quarter}`;
          if (quarterPerformance.hasOwnProperty(quarter)) {
            quarterPerformance[quarter].push(grade.score);
          }
        }
      });

      // Calculate averages
      const averages = Object.keys(quarterPerformance).map(quarter => {
        const scores = quarterPerformance[quarter];
        if (scores.length > 0) {
          return scores.reduce((sum, score) => sum + score, 0) / scores.length;
        }
        return 0;
      });

      // If no data found, use some realistic data
      if (averages.every(avg => avg === 0)) {
        // Use default data
        setPerformanceTrendData(defaultPerformanceTrendData);
        return;
      }

      // Update performance data
      setPerformanceTrendData({
        labels: Object.keys(quarterPerformance),
        datasets: [{
          data: sanitizeChartData(averages),
          color: (opacity = 1) => `rgba(76, 175, 80, ${opacity})`,
          strokeWidth: 2
        }],
        legend: ['Average Performance']
      });
    } catch (error) {
      console.error('Error fetching performance data:', error);
      // Use default data on error
      setPerformanceTrendData(defaultPerformanceTrendData);
    }
  };

  // Fetch resource allocation data from Firestore
  const fetchResourceData = async () => {
    try {
      // Query resources collection
      const resourcesRef = collection(db, 'resources');
      const resourcesSnapshot = await getDocs(resourcesRef);

      // Calculate resource allocation by category
      const categoryAllocation = {
        'Academic': 0,
        'Admin': 0,
        'Infra': 0,
        'Other': 0
      };

      resourcesSnapshot.forEach(doc => {
        const resource = doc.data();
        if (resource.category && resource.allocation) {
          if (categoryAllocation.hasOwnProperty(resource.category)) {
            categoryAllocation[resource.category] += resource.allocation;
          } else {
            categoryAllocation['Other'] += resource.allocation;
          }
        }
      });

      // If no data found, use some realistic data
      if (Object.values(categoryAllocation).every(allocation => allocation === 0)) {
        // Use default data
        setResourceAllocationData(defaultResourceAllocationData);
        return;
      }

      // Update resource allocation data
      const newResourceData = Object.keys(categoryAllocation).map((category, index) => {
        const colors = [
          mobileTheme.colors.primary,
          mobileTheme.colors.success,
          mobileTheme.colors.warning,
          mobileTheme.colors.error
        ];

        return {
          name: category,
          value: categoryAllocation[category],
          color: colors[index % colors.length],
          legendFontColor: mobileTheme.colors.textSecondary,
          legendFontSize: 12
        };
      });

      setResourceAllocationData(newResourceData);
    } catch (error) {
      console.error('Error fetching resource data:', error);
      // Use default data on error
      setResourceAllocationData(defaultResourceAllocationData);
    }
  };

  // Handle bottom navigation changes
  const handleRouteChange = (route) => {
    switch(route) {
      case 'users':
        navigation.navigate('UserManagement');
        break;
      case 'classes':
        navigation.navigate('ClassManagement');
        break;
      case 'messages':
        navigation.navigate('CommunicationCenter');
        break;
      case 'more':
        // Show more menu or navigate to CloudinaryTest
        navigation.navigate('CloudinaryTest');
        break;
      case 'cloudinary':
        navigation.navigate('CloudinaryTest');
        break;
      default:
        // If it's a valid route, navigate to it
        if (route && typeof route === 'string') {
          navigation.navigate(route);
        }
        // Otherwise, dashboard is already active
        break;
    }
  };

  // Get activity icon based on type
  const getActivityIcon = (type) => {
    switch (type) {
      case 'user': return 'account';
      case 'staff': return 'account-tie';
      case 'class': return 'school';
      case 'exam': return 'file-document-edit';
      case 'academic': return 'calendar';
      case 'attendance': return 'calendar-check';
      case 'grade': return 'chart-box';
      case 'announcement': return 'bullhorn';
      case 'system': return 'cog';
      case 'finance': return 'currency-usd';
      default: return 'information';
    }
  };

  // Get activity color based on type
  const getActivityColor = (type) => {
    switch (type) {
      case 'user': return mobileTheme.colors.primary;
      case 'staff': return mobileTheme.colors.primary;
      case 'class': return mobileTheme.colors.success;
      case 'exam': return mobileTheme.colors.warning;
      case 'academic': return mobileTheme.colors.accent;
      case 'attendance': return mobileTheme.colors.info;
      case 'grade': return mobileTheme.colors.error;
      case 'announcement': return mobileTheme.colors.notification;
      case 'system': return mobileTheme.colors.textSecondary;
      case 'finance': return mobileTheme.colors.warning;
      default: return mobileTheme.colors.disabled;
    }
  };

  // Handle activity press
  const handleActivityPress = (activity) => {
    navigation.navigate('ActivityDetailScreen', { activityId: activity.id });
  };

  // Render tab selector
  const renderTabSelector = () => (
    <View style={styles.tabContainer}>
      {['enrollment', 'performance', 'resources'].map((tab, index) => (
        <TouchableOpacity
          key={index}
          style={[
            styles.tabButton,
            currentTab === index && styles.activeTabButton
          ]}
          onPress={() => setCurrentTab(index)}
        >
          <Text
            style={[
              styles.tabButtonText,
              currentTab === index && styles.activeTabButtonText,
              getTextStyle({ fontSize: 14 })
            ]}
          >
            {translate(`admin.dashboard.${tab}`)}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );

  // Render quick stats section with mobile cards
  const renderQuickStats = () => (
    <ScrollView
      horizontal
      showsHorizontalScrollIndicator={false}
      contentContainerStyle={styles.statsContainer}
    >
      <MobileCard
        title="admin.dashboard.totalStudents"
        value={stats.totalStudents.toString()}
        icon="account-group"
        colors={['#1976d2', '#42a5f5']}
        size="medium"
        delay={100}
        onPress={() => navigation.navigate('StudentManagement')}
      />

      <MobileCard
        title="admin.dashboard.totalTeachers"
        value={stats.totalTeachers.toString()}
        icon="account-tie"
        colors={['#4caf50', '#8bc34a']}
        size="medium"
        delay={200}
        onPress={() => navigation.navigate('TeacherManagement')}
      />

      <MobileCard
        title="admin.dashboard.totalClasses"
        value={stats.totalClasses.toString()}
        icon="school"
        colors={['#ff9800', '#ffb74d']}
        size="medium"
        delay={300}
        onPress={() => navigation.navigate('ClassManagement')}
      />

      <MobileCard
        title="admin.dashboard.activeUsers"
        value={stats.activeUsers.toString()}
        icon="account-check"
        colors={['#f44336', '#e57373']}
        size="medium"
        delay={400}
        onPress={() => navigation.navigate('UserManagement')}
      />
    </ScrollView>
  );

  // Render chart based on current tab
  const renderChart = () => {
    if (isLoading) {
      return (
        <View style={styles.loaderContainer}>
          <ActivityIndicator size="large" color={mobileTheme.colors.primary} />
        </View>
      );
    }

    switch(currentTab) {
      case 0: // Enrollment
        return (
          <View style={styles.chartContainer}>
            <Title style={styles.chartTitle}>{translate('admin.dashboard.enrollment')}</Title>
            <BarChart
              data={sanitizeChartDatasets(enrollmentData)}
              width={screenWidth - 32}
              height={220}
              chartConfig={chartConfig}
              style={styles.chart}
              showValuesOnTopOfBars
              fromZero
              withInnerLines={false}
            />
          </View>
        );
      case 1: // Performance
        return (
          <View style={styles.chartContainer}>
            <Title style={styles.chartTitle}>{translate('admin.dashboard.performance')}</Title>
            <LineChart
              data={sanitizeChartDatasets(performanceTrendData)}
              width={screenWidth - 32}
              height={220}
              chartConfig={chartConfig}
              style={styles.chart}
              bezier
              withInnerLines={false}
              withDots={true}
            />
          </View>
        );
      case 2: // Resources
        return (
          <View style={styles.chartContainer}>
            <Title style={styles.chartTitle}>{translate('admin.dashboard.resources')}</Title>
            <PieChart
              data={resourceAllocationData.map(item => ({
                ...item,
                value: isNaN(item.value) || item.value === undefined ||
                       item.value === null || item.value === Infinity ||
                       item.value === -Infinity ? 0 : item.value
              }))}
              width={screenWidth - 32}
              height={220}
              chartConfig={chartConfig}
              accessor="value"
              backgroundColor="transparent"
              paddingLeft="15"
              style={styles.chart}
              hasLegend={true}
            />
          </View>
        );
      default:
        return null;
    }
  };

  // Render recent activities
  const renderActivities = () => {
    if (activitiesLoading) {
      return (
        <View style={styles.loaderContainer}>
          <ActivityIndicator size="small" color={mobileTheme.colors.primary} />
          <Text style={styles.loaderText}>{translate('common.loading')}</Text>
        </View>
      );
    }

    if (recentActivities.length === 0) {
      return (
        <View style={styles.emptyContainer}>
          <MaterialCommunityIcons
            name="bell-off-outline"
            size={48}
            color={mobileTheme.colors.disabled}
          />
          <Text style={styles.emptyText}>
            {translate('admin.dashboard.noActivities')}
          </Text>
        </View>
      );
    }

    return (
      <View style={styles.activitiesContainer}>
        {recentActivities.map((activity, index) => (
          <MobileListItem
            key={activity.id}
            title={activity.title}
            subtitle={activity.relativeTime || (typeof activity.timestamp === 'string' ? activity.timestamp : getRelativeTime(activity.timestamp))}
            description={activity.description}
            icon={getActivityIcon(activity.type)}
            avatarColor={getActivityColor(activity.type)}
            onPress={() => handleActivityPress(activity)}
            showDivider={index < recentActivities.length - 1}
            status={!activity.read ? 'active' : undefined}
          />
        ))}
      </View>
    );
  };

  // Render quick access buttons
  const renderQuickAccess = () => {
    const quickAccessItems = [
      { icon: 'account-multiple', title: 'admin.navigation.students', route: 'StudentManagement', color: mobileTheme.colors.student },
      { icon: 'account-tie', title: 'admin.navigation.teachers', route: 'TeacherManagement', color: mobileTheme.colors.teacher },
      { icon: 'school', title: 'admin.navigation.classManagement', route: 'ClassManagement', color: mobileTheme.colors.primary },
      { icon: 'file-document-edit', title: 'admin.navigation.examTimetable', route: 'ExamManagement', color: mobileTheme.colors.warning },
      { icon: 'chart-box', title: 'admin.navigation.results', route: 'ResultsManagement', color: mobileTheme.colors.success },
      { icon: 'calendar', title: 'admin.navigation.academicCalendar', route: 'AcademicCalendar', color: mobileTheme.colors.info },
      { icon: 'calendar-check',title: translate('admin.navigation.classSchedule'), route: 'ExamRoutineManager' },
      { icon: 'clipboard-check', title: translate('admin.navigation.gradeApproval'), route: 'AdminGradeApproval' },
    ];

    return (
      <View style={styles.quickAccessGrid}>
        {quickAccessItems.map((item, index) => (
          <Animatable.View
            key={index}
            animation="fadeIn"
            duration={500}
            delay={300 + (index * 100)}
            style={styles.quickAccessItem}
          >
            <TouchableOpacity
              style={styles.quickAccessButton}
              onPress={() => navigation.navigate(item.route)}
            >
              <View style={[styles.quickAccessIconContainer, { backgroundColor: item.color + '15' }]}>
                <MaterialCommunityIcons
                  name={item.icon}
                  size={28}
                  color={item.color}
                />
              </View>
              <Text
                style={[styles.quickAccessText, getTextStyle({ fontSize: 12 })]}
                numberOfLines={2}
              >
                {translate(item.title)}
              </Text>
            </TouchableOpacity>
          </Animatable.View>
        ))}
      </View>
    );
  };

  // Memoize the main content to prevent unnecessary re-renders
  const renderContent = useMemo(() => (
    <>
      {/* Welcome Section */}
      <Animatable.View
        animation="fadeInDown"
        duration={800}
        style={styles.welcomeContainer}
      >
        <Title style={[styles.welcomeTitle, getTextStyle({ fontSize: 20 })]}>
          {translate('admin.dashboard.welcome', { name: user?.displayName || translate('admin.dashboard.administrator') })}
        </Title>
        <Paragraph style={[styles.welcomeDate, getTextStyle({ fontSize: 14 })]}>
          {new Date().toLocaleDateString(language === 'en' ? 'en-US' : language === 'am' ? 'am-ET' : 'om-ET', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
          })}
        </Paragraph>

        {lastUpdated && (
          <Text style={styles.lastUpdatedText}>
            <MaterialCommunityIcons name="clock-outline" size={12} color={mobileTheme.colors.textSecondary} />
            {' '}{translate('admin.dashboard.lastUpdated')}: {getRelativeTime(lastUpdated)}
          </Text>
        )}
      </Animatable.View>

      {!isConnected && (
        <View style={styles.offlineWarning}>
          <MaterialCommunityIcons name="wifi-off" size={16} color={mobileTheme.colors.error} />
          <Text style={styles.offlineWarningText}>{translate('common.offlineMode')}</Text>
        </View>
      )}

      {/* Quick Stats */}
      {renderQuickStats()}

      {/* Quick Access */}
      <View style={styles.sectionContainer}>
        <Title style={[styles.sectionTitle, getTextStyle({ fontSize: 18 })]}>
          {translate('admin.dashboard.quickAccess')}
        </Title>
        {renderQuickAccess()}
      </View>

      {/* Charts */}
      <View style={styles.sectionContainer}>
        <Title style={[styles.sectionTitle, getTextStyle({ fontSize: 18 })]}>
          {translate('admin.dashboard.analytics')}
        </Title>
        {renderTabSelector()}
        {renderChart()}
      </View>

      {/* Recent Activities */}
      <View style={styles.sectionContainer}>
        <View style={styles.sectionHeader}>
          <Title style={[styles.sectionTitle, getTextStyle({ fontSize: 18 })]}>
            {translate('admin.dashboard.recentActivities')}
          </Title>
          <TouchableOpacity
            onPress={() => navigation.navigate('ActivityManagement')}
            style={styles.viewAllButton}
          >
            <Text style={styles.viewAllText}>
              {translate('common.viewAll')}
            </Text>
            <MaterialCommunityIcons
              name={isRTL ? "chevron-left" : "chevron-right"}
              size={16}
              color={mobileTheme.colors.primary}
            />
          </TouchableOpacity>
        </View>
        {renderActivities()}
      </View>

      {/* Footer with version info */}
      <View style={styles.footerContainer}>
        <Text style={styles.footerText}>
          {translate('common.version')}: 1.0.0
        </Text>
      </View>
    </>
  ), [
    user,
    language,
    translate,
    getTextStyle,
    isRTL,
    renderQuickStats,
    renderQuickAccess,
    renderTabSelector,
    renderChart,
    renderActivities,
    isConnected,
    lastUpdated
  ]);

  return (
    <View style={{ flex: 1 }}>
      {/* Admin Sidebar */}
      <AdminSidebar
        visible={drawerOpen}
        drawerAnim={drawerAnim}
        toggleDrawer={toggleDrawer}
        navigation={navigation}
        activeSidebarItem={activeSidebarItem}
        setActiveSidebarItem={setActiveSidebarItem}
      />

      {/* Sidebar Backdrop */}
      <SidebarBackdrop
        visible={drawerOpen}
        onPress={toggleDrawer}
        fadeAnim={backdropFadeAnim}
      />

      <MobileScreenWrapper
        title={translate('admin.navigation.adminDashboard')}
        showBottomNav={true}
        bottomNavItems={bottomNavItems}
        activeRoute="dashboard"
        onChangeRoute={handleRouteChange}
        fabActions={fabActions}
        userRole="admin"
        onMenuPress={toggleDrawer}
        refreshControl={
          <RefreshControl
            refreshing={refreshing || (parentRefreshing || false)}
            onRefresh={parentOnRefresh || onRefresh}
            colors={[mobileTheme.colors.primary]}
            tintColor={mobileTheme.colors.primary}
          />
        }
      >
        {isLoading && (
          <View style={styles.loadingOverlay}>
            <ActivityIndicator size="large" color={mobileTheme.colors.primary} />
            <Text style={styles.loadingText}>{translate('common.loading')}</Text>
          </View>
        )}

        {renderContent}

        {/* Error message snackbar */}
        <Snackbar
          visible={snackbarVisible}
          onDismiss={() => setSnackbarVisible(false)}
          action={{
            label: translate('common.dismiss'),
            onPress: () => setSnackbarVisible(false),
          }}
          duration={3000}
          style={styles.snackbar}
        >
          {errorMessage}
        </Snackbar>
      </MobileScreenWrapper>
    </View>
  );
};

const styles = StyleSheet.create({
  // Loading overlay
  loadingOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: mobileTheme.colors.primary,
  },

  // Snackbar
  snackbar: {
    backgroundColor: '#323232',
  },

  // Offline warning
  offlineWarning: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#ffebee',
    padding: 8,
    borderRadius: 4,
    marginBottom: 16,
  },
  offlineWarningText: {
    color: mobileTheme.colors.error,
    marginLeft: 8,
    fontSize: 12,
  },

  // Footer
  footerContainer: {
    padding: 16,
    alignItems: 'center',
    marginTop: 16,
    marginBottom: 24,
  },
  footerText: {
    fontSize: 12,
    color: mobileTheme.colors.textSecondary,
  },

  // Welcome section
  welcomeContainer: {
    marginBottom: 16,
  },
  welcomeTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: mobileTheme.colors.text,
  },
  welcomeDate: {
    fontSize: 14,
    color: mobileTheme.colors.textSecondary,
    marginTop: 4,
  },
  lastUpdatedText: {
    fontSize: 12,
    color: mobileTheme.colors.textSecondary,
    marginTop: 4,
  },
  statsContainer: {
    paddingRight: 16,
    marginBottom: 24,
  },
  sectionContainer: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: mobileTheme.colors.text,
    marginBottom: 12,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  viewAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  viewAllText: {
    fontSize: 14,
    color: mobileTheme.colors.primary,
    marginRight: 4,
  },
  quickAccessGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -8,
  },
  quickAccessItem: {
    width: '33.33%',
    padding: 8,
  },
  quickAccessButton: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
  },
  quickAccessIconContainer: {
    width: 56,
    height: 56,
    borderRadius: 28,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  quickAccessText: {
    fontSize: 12,
    color: mobileTheme.colors.text,
    textAlign: 'center',
  },
  tabContainer: {
    flexDirection: 'row',
    marginBottom: 16,
    backgroundColor: mobileTheme.colors.surfaceVariant,
    borderRadius: 8,
    padding: 4,
  },
  tabButton: {
    flex: 1,
    paddingVertical: 8,
    alignItems: 'center',
    borderRadius: 6,
  },
  activeTabButton: {
    backgroundColor: mobileTheme.colors.surface,
    elevation: 2,
  },
  tabButtonText: {
    fontSize: 14,
    color: mobileTheme.colors.textSecondary,
  },
  activeTabButtonText: {
    color: mobileTheme.colors.primary,
    fontWeight: '500',
  },
  chartContainer: {
    backgroundColor: mobileTheme.colors.surface,
    borderRadius: 12,
    padding: 16,
    elevation: 2,
  },
  chartTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 12,
  },
  chart: {
    marginVertical: 8,
    borderRadius: 16,
  },
  activitiesContainer: {
    backgroundColor: mobileTheme.colors.surface,
    borderRadius: 12,
    overflow: 'hidden',
    elevation: 2,
  },
  loaderContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 32,
    backgroundColor: mobileTheme.colors.surface,
    borderRadius: 12,
  },
  loaderText: {
    marginTop: 8,
    color: mobileTheme.colors.textSecondary,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 32,
    backgroundColor: mobileTheme.colors.surface,
    borderRadius: 12,
  },
  emptyText: {
    marginTop: 8,
    color: mobileTheme.colors.textSecondary,
    textAlign: 'center',
  },
});

export default MobileAdminDashboard;
