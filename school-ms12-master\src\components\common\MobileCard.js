import React from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import { Surface, Text } from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import * as Animatable from 'react-native-animatable';
import { LinearGradient } from 'expo-linear-gradient';
import mobileTheme from '../../theme/mobileTheme';
import { useLanguage } from '../../context/LanguageContext';

/**
 * Mobile-optimized card component
 * @param {Object} props - Component props
 * @param {string} props.title - Card title
 * @param {string} props.subtitle - Card subtitle
 * @param {string} props.icon - Icon name
 * @param {string} props.value - Main value to display
 * @param {string} props.unit - Unit for the value
 * @param {Array} props.colors - Gradient colors
 * @param {Function} props.onPress - Function to call when card is pressed
 * @param {string} props.size - Card size (small, medium, large)
 * @param {number} props.delay - Animation delay
 * @param {string} props.animation - Animation type
 * @param {Object} props.style - Additional styles
 */
const MobileCard = ({
  title,
  subtitle,
  icon,
  value,
  unit,
  colors = ['#1976d2', '#42a5f5'],
  onPress,
  size = 'medium',
  delay = 0,
  animation = 'fadeInUp',
  style,
}) => {
  const { translate, getTextStyle, isRTL } = useLanguage();
  
  // Determine card dimensions based on size
  const getCardDimensions = () => {
    switch(size) {
      case 'small': return { width: 100, height: 100 };
      case 'large': return { width: 180, height: 180 };
      case 'medium':
      default: return { width: 140, height: 140 };
    }
  };
  
  // Determine font sizes based on size
  const getFontSizes = () => {
    switch(size) {
      case 'small': return { title: 12, value: 20, unit: 10, subtitle: 10 };
      case 'large': return { title: 16, value: 32, unit: 14, subtitle: 14 };
      case 'medium':
      default: return { title: 14, value: 24, unit: 12, subtitle: 12 };
    }
  };
  
  const dimensions = getCardDimensions();
  const fontSizes = getFontSizes();
  
  return (
    <Animatable.View
      animation={animation}
      duration={800}
      delay={delay}
      style={[
        styles.container,
        { width: dimensions.width, height: dimensions.height },
        style
      ]}
    >
      <TouchableOpacity
        onPress={onPress}
        activeOpacity={0.8}
        style={styles.touchable}
      >
        <Surface style={styles.card}>
          <LinearGradient
            colors={colors}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
            style={styles.gradient}
          >
            <View style={[styles.content, isRTL && styles.contentRTL]}>
              {/* Title */}
              <Text
                style={[
                  styles.title,
                  { fontSize: fontSizes.title },
                  getTextStyle({ fontSize: fontSizes.title })
                ]}
                numberOfLines={1}
              >
                {translate(title) || title}
              </Text>
              
              {/* Icon */}
              {icon && (
                <MaterialCommunityIcons
                  name={icon}
                  size={size === 'small' ? 24 : size === 'large' ? 40 : 32}
                  color="rgba(255, 255, 255, 0.9)"
                  style={styles.icon}
                />
              )}
              
              {/* Value */}
              <View style={[styles.valueContainer, isRTL && styles.valueContainerRTL]}>
                <Text
                  style={[
                    styles.value,
                    { fontSize: fontSizes.value }
                  ]}
                  numberOfLines={1}
                >
                  {value}
                </Text>
                
                {unit && (
                  <Text
                    style={[
                      styles.unit,
                      { fontSize: fontSizes.unit }
                    ]}
                  >
                    {unit}
                  </Text>
                )}
              </View>
              
              {/* Subtitle */}
              {subtitle && (
                <Text
                  style={[
                    styles.subtitle,
                    { fontSize: fontSizes.subtitle },
                    getTextStyle({ fontSize: fontSizes.subtitle })
                  ]}
                  numberOfLines={1}
                >
                  {translate(subtitle) || subtitle}
                </Text>
              )}
            </View>
          </LinearGradient>
        </Surface>
      </TouchableOpacity>
    </Animatable.View>
  );
};

const styles = StyleSheet.create({
  container: {
    margin: 8,
  },
  touchable: {
    flex: 1,
  },
  card: {
    flex: 1,
    borderRadius: 16,
    overflow: 'hidden',
    elevation: 4,
  },
  gradient: {
    flex: 1,
    padding: 12,
  },
  content: {
    flex: 1,
    alignItems: 'flex-start',
    justifyContent: 'space-between',
  },
  contentRTL: {
    alignItems: 'flex-end',
  },
  title: {
    color: 'rgba(255, 255, 255, 0.9)',
    fontWeight: '500',
  },
  icon: {
    alignSelf: 'center',
    marginVertical: 4,
  },
  valueContainer: {
    flexDirection: 'row',
    alignItems: 'baseline',
  },
  valueContainerRTL: {
    flexDirection: 'row-reverse',
  },
  value: {
    color: 'white',
    fontWeight: 'bold',
  },
  unit: {
    color: 'rgba(255, 255, 255, 0.8)',
    marginLeft: 4,
  },
  subtitle: {
    color: 'rgba(255, 255, 255, 0.8)',
    alignSelf: 'stretch',
  },
});

export default MobileCard;
