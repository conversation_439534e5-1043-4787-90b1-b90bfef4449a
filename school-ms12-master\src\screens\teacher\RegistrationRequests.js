import React, { useState, useEffect } from 'react';
import { View, ScrollView, StyleSheet } from 'react-native';
import { Title, Card, Paragraph, Chip, ActivityIndicator, Divider } from 'react-native-paper';
import { auth, db } from '../../config/firebase';
import { collection, query, where, getDocs, orderBy } from 'firebase/firestore';

const RegistrationRequests = () => {
  const [loading, setLoading] = useState(true);
  const [registrations, setRegistrations] = useState([]);

  useEffect(() => {
    fetchRegistrations();
  }, []);

  const fetchRegistrations = async () => {
    try {
      const registrationsRef = collection(db, 'registrations');
      const q = query(
        registrationsRef,
        where('teacherId', '==', auth.currentUser.uid),
        orderBy('timestamp', 'desc')
      );
      
      const querySnapshot = await getDocs(q);
      const registrationsData = [];
      
      for (const doc of querySnapshot.docs) {
        const registration = doc.data();
        let userData = null;

        // Fetch user data based on registration type
        if (registration.type === 'student' && registration.studentId) {
          const studentDoc = await getDoc(doc(db, 'students', registration.studentId));
          if (studentDoc.exists()) {
            userData = studentDoc.data();
          }
        } else if (registration.type === 'parent' && registration.parentId) {
          const parentDoc = await getDoc(doc(db, 'parents', registration.parentId));
          if (parentDoc.exists()) {
            userData = parentDoc.data();
          }
        }

        registrationsData.push({
          id: doc.id,
          ...registration,
          userData,
        });
      }

      setRegistrations(registrationsData);
    } catch (error) {
      console.error('Error fetching registrations:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return '#4CAF50';
      case 'pending':
        return '#FFC107';
      case 'rejected':
        return '#f44336';
      default:
        return '#9E9E9E';
    }
  };

  if (loading) {
    return (
      <View style={[styles.container, styles.centerContent]}>
        <ActivityIndicator size="large" color="#2196F3" />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <Title style={styles.title}>Registration Requests</Title>

        {registrations.length === 0 ? (
          <Card style={styles.emptyCard}>
            <Card.Content>
              <Paragraph>No registration requests found</Paragraph>
            </Card.Content>
          </Card>
        ) : (
          registrations.map((registration, index) => (
            <Card key={registration.id} style={styles.card}>
              <Card.Content>
                <View style={styles.cardHeader}>
                  <Title>{registration.type === 'student' ? 'Student' : 'Parent'} Registration</Title>
                  <Chip
                    mode="outlined"
                    selectedColor={getStatusColor(registration.status)}
                    style={[styles.statusChip, { borderColor: getStatusColor(registration.status) }]}
                  >
                    {registration.status}
                  </Chip>
                </View>
                
                <Divider style={styles.divider} />

                {registration.userData && (
                  <>
                    <Paragraph>Name: {registration.userData.firstName} {registration.userData.lastName}</Paragraph>
                    <Paragraph>Email: {registration.userData.email}</Paragraph>
                    {registration.type === 'student' && (
                      <>
                        <Paragraph>Grade: {registration.userData.grade}</Paragraph>
                        <Paragraph>Admission Number: {registration.userData.admissionNumber}</Paragraph>
                      </>
                    )}
                    {registration.type === 'parent' && (
                      <>
                        <Paragraph>Phone: {registration.userData.phone}</Paragraph>
                        <Paragraph>Student's Email: {registration.userData.studentEmail}</Paragraph>
                      </>
                    )}
                  </>
                )}

                <Paragraph style={styles.timestamp}>
                  Registered on: {new Date(registration.timestamp).toLocaleDateString()}
                </Paragraph>
              </Card.Content>
            </Card>
          ))
        )}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  scrollContent: {
    padding: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  card: {
    marginBottom: 16,
  },
  emptyCard: {
    alignItems: 'center',
    padding: 20,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  statusChip: {
    height: 28,
  },
  divider: {
    marginVertical: 12,
  },
  timestamp: {
    marginTop: 12,
    fontSize: 12,
    color: '#666',
  },
});

export default RegistrationRequests;
