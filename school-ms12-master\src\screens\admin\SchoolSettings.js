import React, { useState, useEffect, useRef } from 'react';
import { View, ScrollView, StyleSheet, SafeAreaView, StatusBar, TouchableOpacity, Animated } from 'react-native';
import { Card, Title, TextInput, Text, Button, Divider, List, Switch, IconButton, Snackbar, SegmentedButtons, Avatar, Surface, ProgressBar, RadioButton, Menu, Portal, Dialog, Modal } from 'react-native-paper';
import { db, auth } from '../../config/firebase';
import { doc, getDoc, updateDoc, setDoc } from 'firebase/firestore';
import { handleAdminError } from '../../utils/errorHandler';
import CustomButton from '../../components/common/CustomButton';
import { useNavigation } from '@react-navigation/native';
import { useLanguage } from '../../context/LanguageContext';
import { useAuth } from '../../context/AuthContext';
import * as Animatable from 'react-native-animatable';
import { LinearGradient } from 'expo-linear-gradient';
import AdminAppHeader from '../../components/common/AdminAppHeader';
import AdminSidebar from '../../components/common/AdminSidebar';
import SidebarBackdrop from '../../components/common/SidebarBackdrop';
import LanguageSelector from '../../components/common/LanguageSelector';

const SchoolSettings = () => {
  const navigation = useNavigation();
  // No theme needed
  const { translate, language, setLanguage, isRTL } = useLanguage();
  const { user } = useAuth();

  // Animation refs
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const backdropFadeAnim = useRef(new Animated.Value(0)).current;
  const drawerAnim = useRef(new Animated.Value(-300)).current;

  // State variables
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [activeSidebarItem, setActiveSidebarItem] = useState('SchoolSettings');
  const [activeTab, setActiveTab] = useState('general'); // 'general', 'appearance', 'security', 'language', 'performance'
  const [loading, setLoading] = useState(false);
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [confirmDialogVisible, setConfirmDialogVisible] = useState(false);
  const [confirmDialogAction, setConfirmDialogAction] = useState(null);
  const [languageMenuVisible, setLanguageMenuVisible] = useState(false);

  // Settings state
  const [settings, setSettings] = useState({
    // General settings
    schoolName: '',
    email: '',
    phone: '',
    website: '',
    address: '',
    city: '',
    state: '',
    postalCode: '',
    country: '',
    timeZone: '',
    description: '',
    mission: '',
    vision: '',
    logo: '',

    // Appearance settings
    primaryColor: '#2196F3',
    accentColor: '#FF9800',
    fontFamily: 'Roboto',
    fontSize: 'medium', // 'small', 'medium', 'large'

    // Security settings
    requirePasswordChange: true,
    passwordExpiryDays: 90,
    loginAttempts: 5,
    sessionTimeout: 30, // minutes
    twoFactorAuth: false,

    // Performance settings
    cacheEnabled: true,
    analyticsEnabled: true,
    notificationsEnabled: true,
    autoRefresh: true,
    refreshInterval: 5, // minutes
  });

  // Start animations when component mounts
  useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 1000,
      useNativeDriver: true,
    }).start();

    // Hide the default header
    navigation.setOptions({
      headerShown: false,
    });

    fetchSchoolSettings();
  }, []);

  // Toggle drawer
  const toggleDrawer = () => {
    if (drawerOpen) {
      // Close drawer
      Animated.timing(backdropFadeAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }).start();

      Animated.timing(drawerAnim, {
        toValue: -300,
        duration: 300,
        useNativeDriver: true,
      }).start(() => setDrawerOpen(false));
    } else {
      // Open drawer
      setDrawerOpen(true);

      Animated.timing(drawerAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }).start();

      Animated.timing(backdropFadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }).start();
    }
  };

  const fetchSchoolSettings = async () => {
    try {
      setLoading(true);
      const settingsDoc = await getDoc(doc(db, 'schoolConfig', 'settings'));
      if (settingsDoc.exists()) {
        // Merge default settings with stored settings
        setSettings({
          ...settings,
          ...settingsDoc.data()
        });
      } else {
        // If no settings exist, create default settings
        await setDoc(doc(db, 'schoolConfig', 'settings'), settings);
      }
    } catch (error) {
      handleAdminError(error, 'fetchSchoolSettings');
      setSnackbarMessage(translate('schoolSettings.messages.fetchError') || 'Failed to fetch school settings');
      setSnackbarVisible(true);
    } finally {
      setLoading(false);
    }
  };

  const updateSchoolSettings = async () => {
    try {
      setLoading(true);
      await updateDoc(doc(db, 'schoolConfig', 'settings'), settings);
      setSnackbarMessage(translate('schoolSettings.messages.updateSuccess') || 'School settings updated successfully');
      setSnackbarVisible(true);
    } catch (error) {
      handleAdminError(error, 'updateSchoolSettings');
      setSnackbarMessage(translate('schoolSettings.messages.updateError') || 'Failed to update school settings');
      setSnackbarVisible(true);
    } finally {
      setLoading(false);
    }
  };

  const handleLanguageChange = (newLanguage) => {
    setLanguage(newLanguage);
    setLanguageMenuVisible(false);
    setSnackbarMessage(translate('settings.languageChanged') || 'Language changed successfully');
    setSnackbarVisible(true);
  };

  const handleResetSettings = () => {
    setConfirmDialogAction('resetSettings');
    setConfirmDialogVisible(true);
  };

  const confirmResetSettings = async () => {
    try {
      setLoading(true);
      // Reset to default settings
      const defaultSettings = {
        // General settings
        schoolName: '',
        email: '',
        phone: '',
        website: '',
        address: '',
        city: '',
        state: '',
        postalCode: '',
        country: '',
        timeZone: '',
        description: '',
        mission: '',
        vision: '',
        logo: '',

        // Appearance settings
        primaryColor: '#2196F3',
        accentColor: '#FF9800',
        fontFamily: 'Roboto',
        fontSize: 'medium',

        // Security settings
        requirePasswordChange: true,
        passwordExpiryDays: 90,
        loginAttempts: 5,
        sessionTimeout: 30,
        twoFactorAuth: false,

        // Performance settings
        cacheEnabled: true,
        analyticsEnabled: true,
        notificationsEnabled: true,
        autoRefresh: true,
        refreshInterval: 5,
      };

      await updateDoc(doc(db, 'schoolConfig', 'settings'), defaultSettings);
      setSettings(defaultSettings);
      setSnackbarMessage(translate('settings.resetSuccess') || 'Settings reset to default');
      setSnackbarVisible(true);
    } catch (error) {
      handleAdminError(error, 'resetSettings');
      setSnackbarMessage(translate('settings.resetError') || 'Failed to reset settings');
      setSnackbarVisible(true);
    } finally {
      setLoading(false);
      setConfirmDialogVisible(false);
    }
  };

  const handleConfirmDialog = () => {
    if (confirmDialogAction === 'resetSettings') {
      confirmResetSettings();
    }
    setConfirmDialogVisible(false);
  };

  // Render general settings tab
  const renderGeneralSettings = () => (
    <Card style={styles.card}>
      <Card.Content>
        <Title style={styles.cardTitle}>{translate('schoolSettings.form.generalSettings') || 'General Settings'}</Title>

        <TextInput
          label={translate('schoolSettings.form.schoolName') || 'School Name'}
          value={settings.schoolName}
          onChangeText={(text) => setSettings({ ...settings, schoolName: text })}
          style={styles.input}
          mode="outlined"
        />

        <TextInput
          label={translate('schoolSettings.form.email') || 'Email'}
          value={settings.email}
          onChangeText={(text) => setSettings({ ...settings, email: text })}
          style={styles.input}
          mode="outlined"
          keyboardType="email-address"
        />

        <TextInput
          label={translate('schoolSettings.form.phone') || 'Phone'}
          value={settings.phone}
          onChangeText={(text) => setSettings({ ...settings, phone: text })}
          style={styles.input}
          mode="outlined"
          keyboardType="phone-pad"
        />

        <TextInput
          label={translate('schoolSettings.form.website') || 'Website'}
          value={settings.website}
          onChangeText={(text) => setSettings({ ...settings, website: text })}
          style={styles.input}
          mode="outlined"
          keyboardType="url"
        />

        <TextInput
          label={translate('schoolSettings.form.address') || 'Address'}
          value={settings.address}
          onChangeText={(text) => setSettings({ ...settings, address: text })}
          style={styles.input}
          mode="outlined"
          multiline
        />

        <View style={styles.row}>
          <TextInput
            label={translate('schoolSettings.form.city') || 'City'}
            value={settings.city}
            onChangeText={(text) => setSettings({ ...settings, city: text })}
            style={[styles.input, styles.halfInput]}
            mode="outlined"
          />

          <TextInput
            label={translate('schoolSettings.form.state') || 'State/Region'}
            value={settings.state}
            onChangeText={(text) => setSettings({ ...settings, state: text })}
            style={[styles.input, styles.halfInput]}
            mode="outlined"
          />
        </View>

        <View style={styles.row}>
          <TextInput
            label={translate('schoolSettings.form.postalCode') || 'Postal Code'}
            value={settings.postalCode}
            onChangeText={(text) => setSettings({ ...settings, postalCode: text })}
            style={[styles.input, styles.halfInput]}
            mode="outlined"
          />

          <TextInput
            label={translate('schoolSettings.form.country') || 'Country'}
            value={settings.country}
            onChangeText={(text) => setSettings({ ...settings, country: text })}
            style={[styles.input, styles.halfInput]}
            mode="outlined"
          />
        </View>

        <TextInput
          label={translate('schoolSettings.form.description') || 'Description'}
          value={settings.description}
          onChangeText={(text) => setSettings({ ...settings, description: text })}
          style={styles.input}
          mode="outlined"
          multiline
          numberOfLines={3}
        />

        <TextInput
          label={translate('schoolSettings.form.mission') || 'Mission'}
          value={settings.mission}
          onChangeText={(text) => setSettings({ ...settings, mission: text })}
          style={styles.input}
          mode="outlined"
          multiline
          numberOfLines={3}
        />

        <TextInput
          label={translate('schoolSettings.form.vision') || 'Vision'}
          value={settings.vision}
          onChangeText={(text) => setSettings({ ...settings, vision: text })}
          style={styles.input}
          mode="outlined"
          multiline
          numberOfLines={3}
        />
      </Card.Content>
    </Card>
  );

  // Render appearance settings tab
  const renderAppearanceSettings = () => (
    <Card style={styles.card}>
      <Card.Content>
        <Title style={styles.cardTitle}>{translate('settings.appearance') || 'Appearance Settings'}</Title>

        <List.Item
          title={translate('settings.primaryColor') || 'Primary Color'}
          description={translate('settings.primaryColorDescription') || 'Main color used throughout the app'}
          left={props => <List.Icon {...props} icon="palette" color={settings.primaryColor} />}
        />
        <Divider />

        <List.Item
          title={translate('settings.accentColor') || 'Accent Color'}
          description={translate('settings.accentColorDescription') || 'Secondary color used for highlights'}
          left={props => <List.Icon {...props} icon="palette-swatch" color={settings.accentColor} />}
        />
        <Divider />

        <List.Item
          title={translate('settings.fontSize') || 'Font Size'}
          description={translate(`settings.${settings.fontSize}Size`) || settings.fontSize.charAt(0).toUpperCase() + settings.fontSize.slice(1)}
          left={props => <List.Icon {...props} icon="format-size" />}
          right={props => (
            <SegmentedButtons
              value={settings.fontSize}
              onValueChange={(value) => setSettings({ ...settings, fontSize: value })}
              buttons={[
                { value: 'small', label: translate('settings.small') || 'S' },
                { value: 'medium', label: translate('settings.medium') || 'M' },
                { value: 'large', label: translate('settings.large') || 'L' },
              ]}
              style={styles.segmentedButtons}
            />
          )}
        />
      </Card.Content>
    </Card>
  );

  // Render security settings tab
  const renderSecuritySettings = () => (
    <Card style={styles.card}>
      <Card.Content>
        <Title style={styles.cardTitle}>{translate('settings.security') || 'Security Settings'}</Title>

        <List.Item
          title={translate('settings.requirePasswordChange') || 'Require Password Change'}
          description={translate('settings.requirePasswordChangeDescription') || 'Force users to change password on first login'}
          left={props => <List.Icon {...props} icon="lock-reset" />}
          right={props => (
            <Switch
              value={settings.requirePasswordChange}
              onValueChange={(value) => setSettings({ ...settings, requirePasswordChange: value })}
              color={'#1976d2'}
            />
          )}
        />
        <Divider />

        <List.Item
          title={translate('settings.passwordExpiryDays') || 'Password Expiry (Days)'}
          description={translate('settings.passwordExpiryDescription') || 'Number of days before password expires'}
          left={props => <List.Icon {...props} icon="timer-sand" />}
          right={props => (
            <TextInput
              value={settings.passwordExpiryDays.toString()}
              onChangeText={(text) => setSettings({ ...settings, passwordExpiryDays: parseInt(text) || 90 })}
              keyboardType="numeric"
              style={styles.smallInput}
              mode="outlined"
            />
          )}
        />
        <Divider />

        <List.Item
          title={translate('settings.loginAttempts') || 'Login Attempts'}
          description={translate('settings.loginAttemptsDescription') || 'Maximum number of failed login attempts'}
          left={props => <List.Icon {...props} icon="login" />}
          right={props => (
            <TextInput
              value={settings.loginAttempts.toString()}
              onChangeText={(text) => setSettings({ ...settings, loginAttempts: parseInt(text) || 5 })}
              keyboardType="numeric"
              style={styles.smallInput}
              mode="outlined"
            />
          )}
        />
        <Divider />

        <List.Item
          title={translate('settings.sessionTimeout') || 'Session Timeout (Minutes)'}
          description={translate('settings.sessionTimeoutDescription') || 'Time before user is automatically logged out'}
          left={props => <List.Icon {...props} icon="clock-outline" />}
          right={props => (
            <TextInput
              value={settings.sessionTimeout.toString()}
              onChangeText={(text) => setSettings({ ...settings, sessionTimeout: parseInt(text) || 30 })}
              keyboardType="numeric"
              style={styles.smallInput}
              mode="outlined"
            />
          )}
        />
        <Divider />

        <List.Item
          title={translate('settings.twoFactorAuth') || 'Two-Factor Authentication'}
          description={translate('settings.twoFactorAuthDescription') || 'Require additional verification when logging in'}
          left={props => <List.Icon {...props} icon="two-factor-authentication" />}
          right={props => (
            <Switch
              value={settings.twoFactorAuth}
              onValueChange={(value) => setSettings({ ...settings, twoFactorAuth: value })}
              color={'#1976d2'}
            />
          )}
        />
      </Card.Content>
    </Card>
  );

  // Render language settings tab
  const renderLanguageSettings = () => (
    <Card style={styles.card}>
      <Card.Content>
        <Title style={styles.cardTitle}>{translate('settings.language') || 'Language Settings'}</Title>

        <List.Item
          title={translate('settings.appLanguage') || 'Application Language'}
          description={translate('settings.appLanguageDescription') || 'Change the language of the application interface'}
          left={props => <List.Icon {...props} icon="translate" />}
          right={props => (
            <LanguageSelector color={'#1976d2'} />
          )}
        />
        <Divider />

        <List.Item
          title={translate('settings.rtlSupport') || 'Right-to-Left Support'}
          description={translate('settings.rtlSupportDescription') || 'Enable support for right-to-left languages'}
          left={props => <List.Icon {...props} icon="format-textdirection-r-to-l" />}
          right={props => (
            <Switch
              value={isRTL}
              disabled={true}
              color={'#1976d2'}
            />
          )}
        />
        <Divider />

        <View style={styles.languageInfo}>
          <Text style={styles.languageInfoText}>
            {translate('settings.languageInfo') || 'Language settings are applied immediately and saved to your user profile.'}
          </Text>
        </View>
      </Card.Content>
    </Card>
  );

  // Render performance settings tab
  const renderPerformanceSettings = () => (
    <Card style={styles.card}>
      <Card.Content>
        <Title style={styles.cardTitle}>{translate('settings.performance') || 'Performance Settings'}</Title>

        <List.Item
          title={translate('settings.cacheEnabled') || 'Enable Caching'}
          description={translate('settings.cacheEnabledDescription') || 'Store data locally for faster loading'}
          left={props => <List.Icon {...props} icon="cached" />}
          right={props => (
            <Switch
              value={settings.cacheEnabled}
              onValueChange={(value) => setSettings({ ...settings, cacheEnabled: value })}
              color={'#1976d2'}
            />
          )}
        />
        <Divider />

        <List.Item
          title={translate('settings.analyticsEnabled') || 'Enable Analytics'}
          description={translate('settings.analyticsEnabledDescription') || 'Collect usage data to improve the app'}
          left={props => <List.Icon {...props} icon="chart-bar" />}
          right={props => (
            <Switch
              value={settings.analyticsEnabled}
              onValueChange={(value) => setSettings({ ...settings, analyticsEnabled: value })}
              color={'#1976d2'}
            />
          )}
        />
        <Divider />

        <List.Item
          title={translate('settings.notificationsEnabled') || 'Enable Notifications'}
          description={translate('settings.notificationsEnabledDescription') || 'Receive push notifications'}
          left={props => <List.Icon {...props} icon="bell" />}
          right={props => (
            <Switch
              value={settings.notificationsEnabled}
              onValueChange={(value) => setSettings({ ...settings, notificationsEnabled: value })}
              color={'#1976d2'}
            />
          )}
        />
        <Divider />

        <List.Item
          title={translate('settings.autoRefresh') || 'Auto Refresh'}
          description={translate('settings.autoRefreshDescription') || 'Automatically refresh data'}
          left={props => <List.Icon {...props} icon="refresh" />}
          right={props => (
            <Switch
              value={settings.autoRefresh}
              onValueChange={(value) => setSettings({ ...settings, autoRefresh: value })}
              color={'#1976d2'}
            />
          )}
        />
        <Divider />

        <List.Item
          title={translate('settings.refreshInterval') || 'Refresh Interval (Minutes)'}
          description={translate('settings.refreshIntervalDescription') || 'How often to refresh data automatically'}
          left={props => <List.Icon {...props} icon="timer" />}
          right={props => (
            <TextInput
              value={settings.refreshInterval.toString()}
              onChangeText={(text) => setSettings({ ...settings, refreshInterval: parseInt(text) || 5 })}
              keyboardType="numeric"
              style={styles.smallInput}
              mode="outlined"
              disabled={!settings.autoRefresh}
            />
          )}
        />
      </Card.Content>
    </Card>
  );

  return (
    <SafeAreaView style={styles.safeArea}>
      <StatusBar backgroundColor={'#1976d2'} barStyle="light-content" />

      {/* Admin App Header */}
      <AdminAppHeader
        title={translate('schoolSettings.title') || "School Settings"}
        onMenuPress={toggleDrawer}
        showNotification={true}
      />

      {/* Admin Sidebar */}
      <AdminSidebar
        visible={drawerOpen}
        onClose={toggleDrawer}
        navigation={navigation}
        activeSidebarItem={activeSidebarItem}
        setActiveSidebarItem={setActiveSidebarItem}
      />

      {/* Sidebar Backdrop */}
      <SidebarBackdrop
        visible={drawerOpen}
        onPress={toggleDrawer}
        fadeAnim={backdropFadeAnim}
      />

      {/* Main Content */}
      <Animated.View
        style={[
          styles.content,
          { opacity: fadeAnim }
        ]}
      >
        <ScrollView style={styles.scrollView}>
          {/* Settings Tabs */}
          <SegmentedButtons
            value={activeTab}
            onValueChange={setActiveTab}
            buttons={[
              { value: 'general', label: translate('settings.general') || 'General', icon: 'information' },
              { value: 'appearance', label: translate('settings.appearance') || 'Appearance', icon: 'palette' },
              { value: 'security', label: translate('settings.security') || 'Security', icon: 'shield-lock' },
              { value: 'language', label: translate('settings.language') || 'Language', icon: 'translate' },
              { value: 'performance', label: translate('settings.performance') || 'Performance', icon: 'speedometer' },
            ]}
            style={styles.segmentedButtons}
            multiline
          />

          {/* Settings Content */}
          {activeTab === 'general' && renderGeneralSettings()}
          {activeTab === 'appearance' && renderAppearanceSettings()}
          {activeTab === 'security' && renderSecuritySettings()}
          {activeTab === 'language' && renderLanguageSettings()}
          {activeTab === 'performance' && renderPerformanceSettings()}

          {/* Action Buttons */}
          <View style={styles.buttonContainer}>
            <Button
              mode="contained"
              onPress={updateSchoolSettings}
              loading={loading}
              style={styles.saveButton}
              icon="content-save"
            >
              {translate('common.save') || 'Save Settings'}
            </Button>

            <Button
              mode="outlined"
              onPress={handleResetSettings}
              style={styles.resetButton}
              icon="refresh"
            >
              {translate('settings.resetToDefault') || 'Reset to Default'}
            </Button>
          </View>
        </ScrollView>
      </Animated.View>

      {/* Confirmation Dialog */}
      <Portal>
        <Dialog
          visible={confirmDialogVisible}
          onDismiss={() => setConfirmDialogVisible(false)}
        >
          <Dialog.Title>{translate('settings.confirmReset') || 'Reset Settings?'}</Dialog.Title>
          <Dialog.Content>
            <Text>{translate('settings.confirmResetMessage') || 'Are you sure you want to reset all settings to default? This action cannot be undone.'}</Text>
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={() => setConfirmDialogVisible(false)}>
              {translate('common.cancel') || 'Cancel'}
            </Button>
            <Button onPress={handleConfirmDialog}>
              {translate('common.confirm') || 'Confirm'}
            </Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>

      {/* Snackbar for notifications */}
      <Snackbar
        visible={snackbarVisible}
        onDismiss={() => setSnackbarVisible(false)}
        duration={3000}
        style={styles.snackbar}
      >
        {snackbarMessage}
      </Snackbar>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  scrollView: {
    flex: 1,
  },
  card: {
    marginBottom: 16,
    borderRadius: 8,
  },
  cardTitle: {
    fontSize: 18,
    marginBottom: 16,
  },
  input: {
    marginBottom: 16,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  halfInput: {
    width: '48%',
  },
  smallInput: {
    width: 80,
    height: 40,
    marginRight: 8,
  },
  segmentedButtons: {
    marginBottom: 16,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 24,
  },
  saveButton: {
    flex: 1,
    marginRight: 8,
  },
  resetButton: {
    flex: 1,
    marginLeft: 8,
  },
  menuButton: {
    marginVertical: 4,
  },
  languageInfo: {
    marginTop: 16,
    padding: 16,
    backgroundColor: '#E3F2FD',
    borderRadius: 8,
  },
  languageInfoText: {
    color: '#0D47A1',
  },
  snackbar: {
    bottom: 16,
  },
});

export default SchoolSettings;
