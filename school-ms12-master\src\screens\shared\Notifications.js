import React, { useState, useEffect } from 'react';
import { View, ScrollView, StyleSheet } from 'react-native';
import { Card, Title, Text, Divider, ActivityIndicator, useTheme } from 'react-native-paper';
import { db } from '../../config/firebase';
import { collection, query, orderBy, limit, getDocs } from '../../config/firebase';
import { formatDistanceToNow } from 'date-fns';

const Notifications = () => {
  const [announcements, setAnnouncements] = useState([]);
  const [loading, setLoading] = useState(true);
  // No theme needed

  useEffect(() => {
    fetchAnnouncements();
  }, []);

  const fetchAnnouncements = async () => {
    try {
      setLoading(true);
      const announcementsRef = collection(db, 'announcements');
      const q = query(
        announcementsRef,
        orderBy('createdAt', 'desc'),
        limit(50)
      );
      
      const querySnapshot = await getDocs(q);
      const announcementsList = [];
      
      querySnapshot.forEach((doc) => {
        announcementsList.push({
          id: doc.id,
          ...doc.data(),
        });
      });
      
      setAnnouncements(announcementsList);
    } catch (error) {
      console.error('Error fetching announcements:', error);
    } finally {
      setLoading(false);
    }
  };

  const renderAnnouncementCard = (announcement) => (
    <Card key={announcement.id} style={styles.card}>
      <Card.Content>
        <Title>{announcement.title}</Title>
        <Text style={styles.category}>{announcement.category}</Text>
        <Divider style={styles.divider} />
        <Text style={styles.content}>{announcement.content}</Text>
        <Text style={styles.timestamp}>
          {announcement.createdAt?.toDate
            ? formatDistanceToNow(announcement.createdAt.toDate(), { addSuffix: true })
            : 'Recently'}
        </Text>
      </Card.Content>
    </Card>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={'#1976d2'} />
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <View style={styles.content}>
        {announcements.length > 0 ? (
          announcements.map(renderAnnouncementCard)
        ) : (
          <Card style={styles.emptyCard}>
            <Card.Content>
              <Text style={styles.emptyText}>No announcements available</Text>
            </Card.Content>
          </Card>
        )}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    padding: 16,
  },
  card: {
    marginBottom: 16,
    elevation: 2,
  },
  category: {
    color: '#666',
    fontSize: 14,
    marginTop: 4,
  },
  divider: {
    marginVertical: 8,
  },
  content: {
    fontSize: 16,
    lineHeight: 24,
    marginVertical: 8,
  },
  timestamp: {
    color: '#666',
    fontSize: 12,
    marginTop: 8,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyCard: {
    marginTop: 16,
  },
  emptyText: {
    textAlign: 'center',
    color: '#666',
  },
});

export default Notifications;
