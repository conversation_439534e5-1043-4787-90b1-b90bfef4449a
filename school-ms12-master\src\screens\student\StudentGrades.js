import React, { useState } from 'react';
import { View, StyleSheet, SafeAreaView, StatusBar } from 'react-native';
import { SegmentedButtons } from 'react-native-paper';
import { useLanguage } from '../../context/LanguageContext';
import StudentAppHeader from '../../components/common/StudentAppHeader';
import StudentResultsView from '../../components/student/StudentResultsView';
import DetailedResultView from '../../components/student/DetailedResultView';

const StudentGrades = ({ route, navigation }) => {
  const { translate } = useLanguage();
  const [viewMode, setViewMode] = useState('detailed');

  // Extract parameters from route
  const { classId, subject, sectionName } = route.params || {};

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar backgroundColor={'#1976d2'} barStyle="light-content" />

      <StudentAppHeader
        title={translate('studentGrades.title') || "My Grades"}
        navigation={navigation}
        showNotification={true}
      />

      <View style={styles.segmentContainer}>
        <SegmentedButtons
          value={viewMode}
          onValueChange={setViewMode}
          buttons={[
            {
              value: 'detailed',
              label: translate('studentGrades.detailedView') || 'Detailed View',
            },
            {
              value: 'summary',
              label: translate('studentGrades.summaryView') || 'Summary View',
            },
          ]}
        />
      </View>

      <View style={styles.content}>
        {viewMode === 'detailed' ? (
          <DetailedResultView />
        ) : (
          <StudentResultsView
            classId={classId}
            subject={subject}
            sectionName={sectionName}
          />
        )}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5'
  },
  segmentContainer: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: '#fff',
    elevation: 2,
  },
  content: {
    flex: 1
  }
});

export default StudentGrades;
