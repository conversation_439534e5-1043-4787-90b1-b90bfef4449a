import React, { useState } from 'react';
import { View, StyleSheet } from 'react-native';
import { SegmentedButtons } from 'react-native-paper';
import { useLanguage } from '../../context/LanguageContext';
import ParentScreenWrapper from '../../components/common/ParentScreenWrapper';
import ParentChildResultsView from '../../components/parent/ParentChildResultsView';
import DetailedChildResultView from '../../components/parent/DetailedChildResultView';

const ParentChildGrades = ({ route, navigation }) => {
  const { translate } = useLanguage();
  const [viewMode, setViewMode] = useState('detailed');

  // Extract parameters from route
  const { childId, classId, subject, sectionName } = route.params || {};

  return (
    <ParentScreenWrapper
      title={translate('parent.childGrades.title') || "Child's Grades"}
      showBack={true}
      showNotification={true}
    >
      <View style={styles.segmentContainer}>
        <SegmentedButtons
          value={viewMode}
          onValueChange={setViewMode}
          buttons={[
            {
              value: 'detailed',
              label: translate('parent.childGrades.detailedView') || 'Detailed View',
            },
            {
              value: 'summary',
              label: translate('parent.childGrades.summaryView') || 'Summary View',
            },
          ]}
        />
      </View>

      <View style={styles.content}>
        {viewMode === 'detailed' ? (
          <DetailedChildResultView />
        ) : (
          <ParentChildResultsView
            childId={childId}
            classId={classId}
            subject={subject}
            sectionName={sectionName}
          />
        )}
      </View>
    </ParentScreenWrapper>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5'
  },
  segmentContainer: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: '#fff',
    elevation: 2,
  },
  content: {
    flex: 1
  }
});

export default ParentChildGrades;
