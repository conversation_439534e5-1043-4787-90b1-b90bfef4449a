import React, { useState, useEffect } from 'react';
import { View, ScrollView, StyleSheet, Linking } from 'react-native';
import { Card, Title, Text, Searchbar, List, Avatar, Portal, Modal } from 'react-native-paper';
import { db } from '../../config/firebase';
import { collection, query, getDocs, where } from 'firebase/firestore';
import { useAuth } from '../../context/AuthContext';
import { useLanguage } from '../../context/LanguageContext';
import CustomButton from '../../components/common/CustomButton';

const ParentList = () => {
  const { user } = useAuth();
  const { translate } = useLanguage();
  const [parents, setParents] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedParent, setSelectedParent] = useState(null);
  const [visible, setVisible] = useState(false);
  const [student, setStudent] = useState(null);

  useEffect(() => {
    fetchStudentInfo();
  }, []);

  useEffect(() => {
    if (student) {
      fetchParents();
    }
  }, [student]);

  const fetchStudentInfo = async () => {
    try {
      const parentsRef = collection(db, 'parents');
      const parentDoc = await getDocs(query(parentsRef, where('userId', '==', user.uid)));

      if (!parentDoc.empty) {
        const parentData = parentDoc.docs[0].data();
        const studentsRef = collection(db, 'students');
        const studentDoc = await getDocs(query(studentsRef, where('id', '==', parentData.studentId)));

        if (!studentDoc.empty) {
          setStudent(studentDoc.docs[0].data());
        }
      }
    } catch (error) {
      console.error('Error fetching student info:', error);
    }
  };

  const fetchParents = async () => {
    try {
      // First get all students in the same class
      const studentsRef = collection(db, 'students');
      const studentsQuery = query(studentsRef, where('classId', '==', student.classId));
      const studentsSnapshot = await getDocs(studentsQuery);

      const studentIds = [];
      studentsSnapshot.forEach((doc) => {
        studentIds.push(doc.id);
      });

      // Then get all parents of those students
      const parentsRef = collection(db, 'parents');
      const parentsQuery = query(parentsRef, where('studentId', 'in', studentIds));
      const parentsSnapshot = await getDocs(parentsQuery);

      const parentsData = [];
      parentsSnapshot.forEach((doc) => {
        // Don't include the current parent
        if (doc.data().userId !== user.uid) {
          parentsData.push({ id: doc.id, ...doc.data() });
        }
      });

      setParents(parentsData);
    } catch (error) {
      console.error('Error fetching parents:', error);
    }
  };

  const handleCall = (phone) => {
    Linking.openURL(`tel:${phone}`);
  };

  const handleEmail = (email) => {
    Linking.openURL(`mailto:${email}`);
  };

  const filteredParents = parents.filter(parent =>
    parent.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    parent.studentName.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <View style={styles.container}>
      <Card style={styles.searchCard}>
        <Card.Content>
          <Searchbar
            placeholder={translate('parent.list.searchPlaceholder') || "Search parents..."}
            onChangeText={setSearchQuery}
            value={searchQuery}
          />
        </Card.Content>
      </Card>

      <ScrollView>
        <Card style={styles.listCard}>
          <Card.Content>
            <Title>{translate('parent.list.parentsInClass', { className: student?.className, section: student?.section }) || `Parents in ${student?.className} - Section ${student?.section}`}</Title>

            {filteredParents.map((parent) => (
              <List.Item
                key={parent.id}
                title={parent.name}
                description={translate('parent.list.parentOf', { studentName: parent.studentName }) || `Parent of ${parent.studentName}`}
                left={props => (
                  <Avatar.Text
                    {...props}
                    size={40}
                    label={parent.name.substring(0, 2).toUpperCase()}
                  />
                )}
                onPress={() => {
                  setSelectedParent(parent);
                  setVisible(true);
                }}
              />
            ))}

            {filteredParents.length === 0 && (
              <Text style={styles.noResults}>{translate('parent.list.noParentsFound') || "No parents found"}</Text>
            )}
          </Card.Content>
        </Card>
      </ScrollView>

      <Portal>
        <Modal
          visible={visible}
          onDismiss={() => setVisible(false)}
          contentContainerStyle={styles.modalContent}
        >
          {selectedParent && (
            <View>
              <Avatar.Text
                size={80}
                label={selectedParent.name.substring(0, 2).toUpperCase()}
                style={styles.modalAvatar}
              />

              <Title style={styles.modalTitle}>{selectedParent.name}</Title>
              <Text style={styles.modalSubtitle}>
                {translate('parent.list.parentOf', { studentName: selectedParent.studentName }) || `Parent of ${selectedParent.studentName}`}
              </Text>

              <List.Section>
                <List.Item
                  title={translate('parent.list.phone') || "Phone"}
                  description={selectedParent.phone}
                  left={props => <List.Icon {...props} icon="phone" />}
                  onPress={() => handleCall(selectedParent.phone)}
                />

                <List.Item
                  title={translate('parent.list.email') || "Email"}
                  description={selectedParent.email}
                  left={props => <List.Icon {...props} icon="email" />}
                  onPress={() => handleEmail(selectedParent.email)}
                />

                <List.Item
                  title={translate('parent.list.address') || "Address"}
                  description={selectedParent.address}
                  left={props => <List.Icon {...props} icon="map-marker" />}
                />
              </List.Section>

              <CustomButton
                mode="outlined"
                onPress={() => setVisible(false)}
                style={styles.closeButton}
              >
                {translate('common.close') || "Close"}
              </CustomButton>
            </View>
          )}
        </Modal>
      </Portal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  searchCard: {
    margin: 10,
  },
  listCard: {
    margin: 10,
  },
  noResults: {
    textAlign: 'center',
    marginTop: 20,
    color: '#666',
  },
  modalContent: {
    backgroundColor: 'white',
    padding: 20,
    margin: 20,
    borderRadius: 10,
  },
  modalAvatar: {
    alignSelf: 'center',
    marginBottom: 20,
  },
  modalTitle: {
    textAlign: 'center',
  },
  modalSubtitle: {
    textAlign: 'center',
    color: '#666',
    marginBottom: 20,
  },
  closeButton: {
    marginTop: 20,
  },
});

export default ParentList;
