import React from 'react';
import { View, ScrollView, StyleSheet } from 'react-native';
import { DataTable } from 'react-native-paper';

/**
 * A reusable scrollable table component that supports both horizontal and vertical scrolling
 * 
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.header - The table header component (DataTable.Header)
 * @param {React.ReactNode} props.children - The table rows (DataTable.Row components)
 * @param {Object} props.style - Additional styles for the table
 * @param {number} props.maxHeight - Maximum height for the table body (default: 400)
 * @param {boolean} props.showHorizontalScrollIndicator - Whether to show horizontal scroll indicator (default: true)
 * @param {boolean} props.showVerticalScrollIndicator - Whether to show vertical scroll indicator (default: true)
 */
const ScrollableTable = ({
  header,
  children,
  style,
  maxHeight = 400,
  showHorizontalScrollIndicator = true,
  showVerticalScrollIndicator = true,
}) => {
  return (
    <View style={[styles.tableWrapper, style]}>
      <ScrollView horizontal showsHorizontalScrollIndicator={showHorizontalScrollIndicator}>
        <View style={styles.tableContainer}>
          <DataTable style={styles.dataTable}>
            {header}
            <ScrollView 
              style={[styles.tableBody, { maxHeight }]}
              showsVerticalScrollIndicator={showVerticalScrollIndicator}
            >
              {children}
            </ScrollView>
          </DataTable>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  tableWrapper: {
    marginVertical: 16,
    borderRadius: 8,
    overflow: 'hidden',
    elevation: 2,
    backgroundColor: '#ffffff',
  },
  tableContainer: {
    minWidth: '100%',
  },
  dataTable: {
    backgroundColor: '#ffffff',
  },
  tableBody: {
    maxHeight: 400, // Default max height, can be overridden by props
  },
});

export default ScrollableTable;
