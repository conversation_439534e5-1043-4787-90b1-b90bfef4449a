import React, { useState, useEffect } from 'react';
import { StyleSheet, View } from 'react-native';
import { Appbar, useTheme, Portal, Drawer, Modal, IconButton } from 'react-native-paper';
import { useNavigation } from '@react-navigation/native';
import { useLanguage } from '../../context/LanguageContext';
import { useAuth } from '../../context/AuthContext';
import NotificationBadge from './NotificationBadge';
import ParentNavigationMenu from '../parent/ParentNavigationMenu';
import LanguageSelector from './LanguageSelector';
import { db, auth } from '../../config/firebase';
import { collection, query, where, getDocs, doc, getDoc } from 'firebase/firestore';

const ParentAppHeader = ({ title, showBackButton = false, onMenuPress }) => {
  const theme = useTheme();
  const navigation = useNavigation();
  const { isRTL, translate } = useLanguage();
  const { user } = useAuth();

  const [drawerVisible, setDrawerVisible] = useState(false);
  const [children, setChildren] = useState([]);
  const [selectedChild, setSelectedChild] = useState(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    fetchChildren();
  }, []);

  const fetchChildren = async () => {
    try {
      setLoading(true);

      // Get parent data to check for children array
      const parentRef = doc(db, 'users', auth.currentUser.uid);
      const parentDoc = await getDoc(parentRef);

      if (!parentDoc.exists()) {
        console.error(translate('parent.dashboard.errorLoadingChildData'));
        setLoading(false);
        return;
      }

      const parentData = parentDoc.data();
      const childrenIds = parentData.children || [];

      if (childrenIds.length === 0) {
        console.log(translate('parent.dashboard.noChildrenRegistered'));
        setChildren([]);
        setLoading(false);
        return;
      }

      console.log(`Found ${childrenIds.length} children for parent:`, childrenIds);

      // Fetch each child's data
      const childrenData = [];

      for (const childId of childrenIds) {
        try {
          const childRef = doc(db, 'users', childId);
          const childDoc = await getDoc(childRef);

          if (childDoc.exists()) {
            const childData = { id: childId, ...childDoc.data() };

            // Ensure child has a name property for display
            if (!childData.name) {
              childData.name = `${childData.firstName || ''} ${childData.lastName || ''}`.trim() || translate('common.unnamed');
            }

            console.log(`Fetched child data for ${childId}:`, childData.name);
            childrenData.push(childData);
          }
        } catch (childError) {
          console.error(`Error fetching child ${childId}:`, childError);
        }
      }

      setChildren(childrenData);

      // Select first child by default
      if (childrenData.length > 0 && !selectedChild) {
        setSelectedChild(childrenData[0]);
      }
    } catch (error) {
      console.error('Error fetching children:', error);
    } finally {
      setLoading(false);
    }
  };

  const toggleDrawer = () => {
    setDrawerVisible(!drawerVisible);
  };

  const handleChildSelect = (child) => {
    setSelectedChild(child);
    // Close drawer after selection on small screens
    if (window.innerWidth < 768) {
      setDrawerVisible(false);
    }
  };

  return (
    <>
      <Appbar.Header style={[styles.header, { backgroundColor: '#1976d2' }]}>
        {showBackButton ? (
          <Appbar.BackAction
            onPress={() => navigation.goBack()}
            color="white"
            style={isRTL ? styles.rightIcon : {}}
          />
        ) : (
          <Appbar.Action
            icon="menu"
            color="white"
            onPress={onMenuPress || toggleDrawer}
            style={isRTL ? styles.rightIcon : {}}
          />
        )}
        <Appbar.Content
          title={title || translate('parent.dashboard.title')}
          titleStyle={[styles.title, isRTL && styles.rtlTitle]}
        />
        <NotificationBadge
          size={24}
          color="white"
          onPress={() => navigation.navigate('NotificationCenter')}
          containerStyle={styles.notificationContainer}
        />
        <LanguageSelector />
      </Appbar.Header>

      <Portal>
        <Modal
          visible={drawerVisible}
          onDismiss={toggleDrawer}
          contentContainerStyle={styles.modalContainer}
          dismissable={true}
        >
          <View style={styles.drawerContainer}>
            <View style={styles.drawerHeader}>
              <IconButton
                icon="close"
                size={24}
                onPress={toggleDrawer}
                style={styles.closeButton}
              />
            </View>
            <ParentNavigationMenu
              selectedChild={selectedChild}
              children={children}
              onChildSelect={handleChildSelect}
            />
          </View>
        </Modal>
      </Portal>
    </>
  );
};

const styles = StyleSheet.create({
  header: {
    elevation: 4,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  rtlTitle: {
    textAlign: 'right',
  },
  rightIcon: {
    marginLeft: 'auto',
  },
  notificationContainer: {
    marginRight: 8,
  },
  modalContainer: {
    margin: 0,
    backgroundColor: 'white',
    height: '100%',
    width: '80%',
    maxWidth: 320,
    position: 'absolute',
    left: 0,
    top: 0,
  },
  drawerContainer: {
    flex: 1,
    backgroundColor: 'white',
  },
  drawerHeader: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    padding: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  closeButton: {
    margin: 0,
  },
});

export default ParentAppHeader;
