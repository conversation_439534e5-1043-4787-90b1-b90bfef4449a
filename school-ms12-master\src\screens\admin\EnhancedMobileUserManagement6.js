  // Main render function
  return (
    <MobileScreenWrapper
      title={translate('userManagement.title')}
      subtitle={translate('userManagement.subtitle')}
      showBackArrow={true}
      bottomNavItems={bottomNavItems}
      fabActions={fabActions}
      isRTL={isRTL}
    >
      <View style={styles.container}>
        {/* Search bar */}
        <Animatable.View 
          animation="fadeInDown" 
          duration={500}
          style={styles.searchContainer}
        >
          <Searchbar
            placeholder={translate('userManagement.searchUsers')}
            onChangeText={setSearchQuery}
            value={searchQuery}
            style={styles.searchBar}
            icon="magnify"
            clearIcon="close"
            onClearIconPress={() => setSearchQuery('')}
          />
        </Animatable.View>
        
        {/* Last updated info */}
        {lastUpdated && (
          <Animatable.View 
            animation="fadeIn" 
            duration={500}
            style={styles.lastUpdatedContainer}
          >
            <Text style={styles.lastUpdatedText}>
              {translate('common.lastUpdated')}: {formatDistance(lastUpdated, new Date(), { addSuffix: true })}
            </Text>
          </Animatable.View>
        )}
        
        {/* User list */}
        {renderUserList()}
        
        {/* User details modal */}
        {renderUserDetailsModal()}
        
        {/* Filter and sort menus */}
        {renderFilterMenu()}
        {renderSortMenu()}
        
        {/* Confirmation dialogs */}
        {renderConfirmationDialogs()}
        
        {/* Snackbar for messages */}
        <Snackbar
          visible={snackbarVisible}
          onDismiss={() => setSnackbarVisible(false)}
          duration={3000}
          style={styles.snackbar}
          action={{
            label: translate('common.dismiss'),
            onPress: () => setSnackbarVisible(false),
          }}
        >
          {errorMessage}
        </Snackbar>
      </View>
    </MobileScreenWrapper>
  );
};

// Styles
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: mobileTheme.colors.background,
  },
  searchContainer: {
    padding: 16,
    backgroundColor: mobileTheme.colors.surface,
    elevation: 2,
  },
  searchBar: {
    elevation: 0,
    borderRadius: 8,
  },
  lastUpdatedContainer: {
    paddingHorizontal: 16,
    paddingBottom: 8,
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  lastUpdatedText: {
    fontSize: 12,
    color: mobileTheme.colors.placeholder,
  },
  statsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    padding: 8,
  },
  statCardContainer: {
    width: '48%',
    marginBottom: 16,
  },
  statCard: {
    padding: 16,
    borderRadius: 8,
    elevation: 2,
    alignItems: 'center',
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    marginVertical: 8,
  },
  statLabel: {
    fontSize: 12,
    color: mobileTheme.colors.placeholder,
  },
  userCard: {
    marginHorizontal: 16,
    marginVertical: 8,
    borderRadius: 8,
    elevation: 2,
  },
  userCardContent: {
    padding: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  avatar: {
    marginRight: 16,
  },
  userDetails: {
    flex: 1,
  },
  userName: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  userEmail: {
    fontSize: 14,
    color: mobileTheme.colors.placeholder,
    marginBottom: 8,
  },
  userChips: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  roleChip: {
    marginRight: 8,
    marginBottom: 4,
  },
  statusChip: {
    marginBottom: 4,
  },
  chipText: {
    fontSize: 12,
  },
  userActions: {
    flexDirection: 'row',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  loadingText: {
    marginTop: 16,
    marginBottom: 8,
    fontSize: 16,
  },
  progressBar: {
    width: '80%',
    height: 6,
    borderRadius: 3,
  },
  noResultsContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  noResultsText: {
    marginTop: 16,
    marginBottom: 24,
    fontSize: 16,
    textAlign: 'center',
  },
  addUserButton: {
    marginTop: 16,
  },
  clearSearchButton: {
    marginTop: 16,
  },
  listContent: {
    paddingBottom: 80, // Space for FAB
  },
  // SwipeListView styles
  rowBack: {
    alignItems: 'center',
    backgroundColor: mobileTheme.colors.background,
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingLeft: 15,
    marginHorizontal: 16,
    marginVertical: 8,
    borderRadius: 8,
  },
  backLeftBtn: {
    alignItems: 'center',
    bottom: 0,
    justifyContent: 'center',
    position: 'absolute',
    top: 0,
    width: 75,
    borderTopLeftRadius: 8,
    borderBottomLeftRadius: 8,
  },
  backLeftBtnLeft: {
    backgroundColor: mobileTheme.colors.primary,
    left: 0,
  },
  backRightBtn: {
    alignItems: 'center',
    bottom: 0,
    justifyContent: 'center',
    position: 'absolute',
    top: 0,
    width: 75,
  },
  backRightBtnLeft: {
    backgroundColor: mobileTheme.colors.warning,
    right: 75,
  },
  backRightBtnRight: {
    backgroundColor: mobileTheme.colors.error,
    right: 0,
    borderTopRightRadius: 8,
    borderBottomRightRadius: 8,
  },
  backTextWhite: {
    color: '#FFF',
    fontSize: 12,
    textAlign: 'center',
  },
  // Modal styles
  modalContainer: {
    margin: 20,
  },
  modalSurface: {
    borderRadius: 8,
    elevation: 4,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  modalTitle: {
    fontSize: 20,
  },
  modalContent: {
    padding: 16,
    maxHeight: 400,
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    padding: 16,
  },
  // User details modal
  userDetailHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  detailAvatar: {
    marginRight: 16,
  },
  userDetailInfo: {
    flex: 1,
  },
  detailName: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  detailEmail: {
    fontSize: 14,
    color: mobileTheme.colors.placeholder,
    marginBottom: 8,
  },
  detailChips: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  divider: {
    marginVertical: 8,
  },
  // Dialog styles
  dialog: {
    borderRadius: 8,
  },
  warningText: {
    color: mobileTheme.colors.error,
    marginTop: 8,
  },
  userSummary: {
    padding: 16,
    borderRadius: 8,
    marginTop: 16,
    backgroundColor: mobileTheme.colors.surface,
    elevation: 1,
  },
  userSummaryName: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  userSummaryEmail: {
    fontSize: 14,
    color: mobileTheme.colors.placeholder,
  },
  userSummaryRole: {
    fontSize: 14,
    marginTop: 4,
  },
  // Button styles
  cancelButton: {
    marginRight: 8,
  },
  deleteButton: {
    backgroundColor: mobileTheme.colors.error,
  },
  activateButton: {
    backgroundColor: mobileTheme.colors.success,
  },
  deactivateButton: {
    backgroundColor: mobileTheme.colors.warning,
  },
  resetButton: {
    marginRight: 8,
  },
  editButton: {
    backgroundColor: mobileTheme.colors.primary,
  },
  applyButton: {
    backgroundColor: mobileTheme.colors.primary,
  },
  // Filter styles
  filterLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  filterChips: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  filterChip: {
    margin: 4,
  },
  // Snackbar
  snackbar: {
    bottom: 70, // Above bottom navigation
  },
});

export default EnhancedMobileUserManagement;
