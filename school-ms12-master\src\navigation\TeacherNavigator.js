import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { IconButton } from 'react-native-paper';
import { signOut } from 'firebase/auth';
import { auth } from '../config/firebase';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import TeacherDashboard from '../screens/teacher/TeacherDashboard';
import ClassManagementWrapper from '../screens/teacher/ClassManagementWrapper';
import AssessmentManagement from '../screens/teacher/AssessmentManagement';
import AttendanceManagement from '../screens/teacher/AttendanceManagement';
import GradeManagement from '../screens/teacher/GradeManagement';
import TeacherMessaging from '../screens/teacher/TeacherMessaging';
import ExamAttendance from '../screens/teacher/ExamAttendance';
import CalendarManagement from '../screens/teacher/CalendarManagement';
import LibraryManagement from '../screens/teacher/LibraryManagement';
import ProfileManagement from '../screens/teacher/ProfileManagement';
import MarkSheetSubmission from '../screens/teacher/MarkSheetSubmission';
import HomeworkManagement from '../screens/teacher/HomeworkManagement';
import BehaviorManagement from '../screens/teacher/BehaviorManagement';
import ResourceRequest from '../screens/teacher/ResourceRequest';
import ClassPerformance from '../screens/teacher/ClassPerformance';
import Communication from '../screens/shared/Communication';
import NotificationCenter from '../screens/shared/NotificationCenter';
import NotificationSettings from '../screens/shared/NotificationSettings';
import ReportCenter from '../screens/teacher/ReportCenter';
import StudentRegistration from '../screens/teacher/StudentRegistration';
import ParentRegistration from '../screens/teacher/ParentRegistration';
import RegistrationRequests from '../screens/teacher/RegistrationRequests';
import ActivityHistory from '../screens/teacher/ActivityHistory';
import TeacherExamSchedule from '../screens/teacher/TeacherExamSchedule';
import TeacherClassSchedule from '../screens/teacher/TeacherClassSchedule';
import TeacherDirectory from '../screens/teacher/TeacherDirectory';
import { useTranslation } from '../hooks/useTranslation';
import TranslationDebugger from '../utils/TranslationDebugger';
import { TeacherSidebarProvider } from '../context/TeacherSidebarContext';

const Stack = createStackNavigator();

const TeacherNavigator = () => {
  const { language } = useTranslation();

  const handleLogout = async () => {
    try {
      await signOut(auth);
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  return (
    <TeacherSidebarProvider>
      <Stack.Navigator
        screenOptions={{
          headerShown: false,
        }}
      >
      <Stack.Screen
        name="TeacherDashboard"
        component={TeacherDashboard}
        options={({ navigation }) => ({
          title: 'Teacher Dashboard',
          headerRight: () => (
            <>
              {__DEV__ && (
                <IconButton
                  icon={props => <MaterialCommunityIcons name="translate" {...props} color="#ff9800" />}
                  size={24}
                  onPress={() => {
                    // Show translation debugging tools
                    TranslationDebugger.displayMissingTranslations(language);
                    TranslationDebugger.showTranslationExportMenu(language);
                  }}
                  style={{ marginRight: 5 }}
                />
              )}
              <IconButton
                icon="exit-to-app"
                size={24}
                onPress={handleLogout}
                style={{ marginRight: 10 }}
              />
            </>
          ),
        })}
      />
      <Stack.Screen
        name="ClassManagement"
        component={ClassManagementWrapper}
        options={{ title: 'Class Management' }}
      />
      <Stack.Screen
        name="ClassPerformance"
        component={ClassPerformance}
        options={{ title: 'Class Performance' }}
      />
      <Stack.Screen
        name="AssessmentManagement"
        component={AssessmentManagement}
        options={{ title: 'Assessments' }}
      />
      <Stack.Screen
        name="AttendanceManagement"
        component={AttendanceManagement}
        options={{ title: 'Attendance' }}
      />
      <Stack.Screen
        name="GradeManagement"
        component={GradeManagement}
        options={{ title: 'Grades' }}
      />
      <Stack.Screen
        name="TeacherMessaging"
        component={TeacherMessaging}
        options={{ title: 'Messaging', headerShown: false }}
      />
      <Stack.Screen
        name="ExamAttendance"
        component={ExamAttendance}
        options={{ title: 'Exam Attendance' }}
      />
      <Stack.Screen
        name="TeacherExamSchedule"
        component={TeacherExamSchedule}
        options={{ title: 'Exam Schedule', headerShown: false }}
      />
      <Stack.Screen
        name="TeacherClassSchedule"
        component={TeacherClassSchedule}
        options={{ title: 'Class Schedule', headerShown: false }}
      />
      <Stack.Screen
        name="TeacherDirectory"
        component={TeacherDirectory}
        options={{ title: 'Teacher Directory', headerShown: false }}
      />

      <Stack.Screen
        name="CalendarManagement"
        component={CalendarManagement}
        options={{ title: 'Calendar Management' }}
      />
      <Stack.Screen
        name="LibraryManagement"
        component={LibraryManagement}
        options={{ title: 'Library Management' }}
      />
      <Stack.Screen
        name="ProfileManagement"
        component={ProfileManagement}
        options={{ title: 'Profile Management' }}
      />
      <Stack.Screen
        name="HomeworkManagement"
        component={HomeworkManagement}
        options={{ title: 'Homework Management' }}
      />
      <Stack.Screen
        name="MarkSheetSubmission"
        component={MarkSheetSubmission}
        options={{ title: 'Mark Sheet Submission' }}
      />
      <Stack.Screen
        name="BehaviorManagement"
        component={BehaviorManagement}
        options={{ title: 'Behavior Management' }}
      />
      <Stack.Screen
        name="ResourceRequest"
        component={ResourceRequest}
        options={{ title: 'Resource Request' }}
      />
      <Stack.Screen
        name="Communication"
        component={Communication}
        options={{ title: 'Communication' }}
      />
      <Stack.Screen
        name="NotificationCenter"
        component={NotificationCenter}
        options={{ title: 'Notifications', headerShown: false }}
      />
      <Stack.Screen
        name="NotificationSettings"
        component={NotificationSettings}
        options={{ title: 'Notification Settings', headerShown: false }}
      />
      <Stack.Screen
        name="ReportCenter"
        component={ReportCenter}
        options={{ title: 'Report Center' }}
      />
      <Stack.Screen name="StudentRegistration" component={StudentRegistration} />
      <Stack.Screen name="ParentRegistration" component={ParentRegistration} />
      <Stack.Screen name="RegistrationRequests" component={RegistrationRequests} />
      <Stack.Screen
        name="ActivityHistory"
        component={ActivityHistory}
        options={{ title: 'Activity History' }}
      />
    </Stack.Navigator>
    </TeacherSidebarProvider>
  );
};

export default TeacherNavigator;