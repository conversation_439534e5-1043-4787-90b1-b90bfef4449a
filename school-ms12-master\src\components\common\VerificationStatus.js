import React, { useState, useContext } from 'react';
import { View, StyleSheet } from 'react-native';
import { Text, Button, ActivityIndicator, Chip, useTheme } from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useLanguage, LanguageProvider, LanguageContext } from '../../context/LanguageContext';
import UserRegistrationService from '../../services/UserRegistrationService';
import { auth } from '../../config/firebase';
import EmailVerificationHandler from './EmailVerificationHandler';

/**
 * A reusable component to display email verification status and actions
 *
 * @param {Object} props - Component props
 * @param {string} props.email - The email address to verify
 * @param {boolean} props.isVerified - Whether the email is verified
 * @param {boolean} props.verificationSent - Whether a verification email has been sent
 * @param {Function} props.onVerificationSent - Callback when verification email is sent
 * @param {Function} props.onVerificationStatusChange - Callback when verification status changes
 * @param {string} props.userType - The type of user (student, parent, teacher, admin)
 * @param {Object} props.user - The Firebase user object (optional)
 */

// Inner component that uses the language context directly
function VerificationStatusContent({
  email,
  isVerified,
  verificationSent,
  onVerificationSent,
  onVerificationStatusChange,
  userType = 'user',
  user = null
}) {
  const { translate } = useLanguage();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Development mode flag - set to false to disable auto-verification
  const isDevelopmentMode = false; // Changed to false to disable auto-verification

  // We've removed the auto-verification useEffect to ensure verification only happens when the button is clicked

  const handleSendVerification = async () => {
    if (!email) {
      setError(translate('verification.emailRequired') || 'Email is required');
      return;
    }

    // Validate email format only when the button is clicked
    const EmailVerificationService = require('../../services/EmailVerificationService').default;
    if (!EmailVerificationService.isValidEmail(email)) {
      setError(translate('verification.invalidEmail') || 'Please enter a valid email address');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      if (isDevelopmentMode) {
        // In development mode, simulate sending verification email without auto-verifying
        console.log(`DEVELOPMENT MODE: Simulating verification email sent to ${email}`);

        // Call the callbacks to indicate email was sent
        if (onVerificationSent) {
          onVerificationSent();
        }

        // No auto-verification - user must click the verify button
      } else {
        // Production mode - actually send verification emails
        // If we have a Firebase user object, use it to send verification
        if (user) {
          await UserRegistrationService.resendVerificationEmail(user, userType);
        } else {
          // Otherwise use the custom verification service
          await UserRegistrationService.sendVerificationEmail(email, userType);
        }

        // Call the callback
        if (onVerificationSent) {
          onVerificationSent();
        }
      }
    } catch (error) {
      console.error('Error sending verification email:', error);
      setError(translate('verification.sendError') || 'Failed to send verification email');

      // No auto-verification on error - user must click the verify button
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = () => {
    if (isVerified) {
      return '#4CAF50';
    } else if (verificationSent) {
      return '#FF9800';
    } else {
      return '#F44336';
    }
  };

  const getStatusIcon = () => {
    if (isVerified) {
      return 'check-circle';
    } else if (verificationSent) {
      return 'email-send';
    } else {
      return 'email-alert';
    }
  };

  const getStatusText = () => {
    if (isVerified) {
      return translate('verification.verified') || 'Verified';
    } else if (verificationSent) {
      return translate('verification.sent') || 'Verification Sent';
    } else {
      return translate('verification.notVerified') || 'Not Verified';
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.statusContainer}>
        <Chip
          icon={() => (
            <MaterialCommunityIcons
              name={getStatusIcon()}
              size={16}
              color={getStatusColor()}
            />
          )}
          style={[styles.statusChip, { borderColor: getStatusColor() }]}
          textStyle={{ color: getStatusColor() }}
        >
          {getStatusText()}
        </Chip>

        <Text style={styles.emailText}>{email}</Text>
      </View>

      {!isVerified && (
        <View style={styles.actionContainer}>
          <Button
            mode="contained"
            onPress={handleSendVerification}
            loading={loading}
            disabled={loading}
            style={styles.button}
            labelStyle={styles.buttonLabel}
          >
            {verificationSent
              ? translate('verification.resend') || 'Resend Verification'
              : translate('verification.send') || 'Send Verification'}
          </Button>

          {/* Manual verification button (only shown after sending verification) */}
          {verificationSent && (
            <Button
              mode="outlined"
              onPress={() => {
                console.log(`Manual verification for ${userType} email: ${email}`);
                if (onVerificationStatusChange) {
                  onVerificationStatusChange(true);
                }
              }}
              style={[styles.button, { marginLeft: 8 }]}
              labelStyle={styles.buttonLabel}
              icon="check-circle"
            >
              {translate('verification.verify') || 'Verify'}
            </Button>
          )}
        </View>
      )}

      {error && (
        <Text style={[styles.errorText, { color: '#F44336' }]}>
          {error}
        </Text>
      )}

      {/* In-app verification handler for when emails aren't being received */}
      {verificationSent && !isVerified && (
        <View style={styles.inAppVerificationContainer}>
          <Text style={styles.inAppVerificationTitle}>
            {translate('verification.notReceived') || "Didn't receive the email?"}
          </Text>
          <EmailVerificationHandler
            email={email}
            onVerificationComplete={(status) => {
              if (status && onVerificationStatusChange) {
                onVerificationStatusChange(true);
              }
            }}
          />
        </View>
      )}
    </View>
  );
}

// Simple fallback component that doesn't use any context
function VerificationStatusFallback({ email, isVerified, verificationSent, onVerificationSent, onVerificationStatusChange, userType = 'user' }) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Handle sending verification email
  const handleSendVerification = async () => {
    if (!email) {
      setError('Email is required');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Store verification info directly in Firestore for in-app verification
      const { addDoc, collection, serverTimestamp } = require('firebase/firestore');
      const { db } = require('../../config/firebase');

      // Generate a simple token
      const token = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);

      // Store verification info
      await addDoc(collection(db, 'app_verifications'), {
        email,
        token,
        type: userType,
        createdAt: new Date(),
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
        verified: false
      });

      console.log(`Verification info stored for ${email}`);

      // Call the callback to indicate verification was sent
      if (onVerificationSent) {
        onVerificationSent();
      }
    } catch (error) {
      console.error('Error sending verification:', error);
      setError('Failed to send verification');
    } finally {
      setLoading(false);
    }
  };

  // Handle direct verification
  const handleDirectVerify = async () => {
    try {
      setLoading(true);
      setError(null);

      console.log(`Direct verification for ${userType} email: ${email}`);

      // Store verification info directly in Firestore for in-app verification
      const { addDoc, collection, updateDoc, doc, query, where, getDocs, serverTimestamp } = require('firebase/firestore');
      const { db } = require('../../config/firebase');

      // Generate a simple token
      const token = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);

      // Store verification info as already verified
      await addDoc(collection(db, 'app_verifications'), {
        email,
        token,
        type: userType,
        createdAt: new Date(),
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
        verified: true, // Already verified
        verifiedAt: new Date()
      });

      // Update user document if it exists
      const usersQuery = query(
        collection(db, 'users'),
        where('email', '==', email)
      );

      const userSnapshot = await getDocs(usersQuery);

      if (!userSnapshot.empty) {
        const userDoc = userSnapshot.docs[0];
        await updateDoc(doc(db, 'users', userDoc.id), {
          emailVerified: true,
          updatedAt: serverTimestamp()
        });
      }

      console.log(`Direct verification completed for ${email}`);

      // Call the callback
      if (onVerificationStatusChange) {
        onVerificationStatusChange(true);
      }
    } catch (error) {
      console.error('Error with direct verification:', error);
      setError('Failed to verify email directly');
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.statusContainer}>
        <Chip
          icon={() => (
            <MaterialCommunityIcons
              name={isVerified ? "check-circle" : "email-alert"}
              size={16}
              color={isVerified ? "#4CAF50" : "#F44336"}
            />
          )}
          style={[styles.statusChip, { borderColor: isVerified ? "#4CAF50" : "#F44336" }]}
        >
          {isVerified ? "Verified" : "Not Verified"}
        </Chip>
        <Text style={styles.emailText}>{email}</Text>
      </View>

      {/* Verification buttons */}
      {!isVerified && (
        <View style={styles.actionContainer}>
          <Button
            mode="contained"
            onPress={handleSendVerification}
            loading={loading}
            disabled={loading}
            style={styles.button}
            labelStyle={styles.buttonLabel}
          >
            {verificationSent ? "Resend Verification" : "Send Verification"}
          </Button>

          {/* Direct verification button - always show it */}
          <Button
            mode="contained"
            onPress={handleDirectVerify}
            loading={loading}
            disabled={loading}
            style={[styles.button, { marginLeft: 8, backgroundColor: '#4CAF50' }]}
            labelStyle={styles.buttonLabel}
            icon="check-circle"
          >
            {"Verify Now"}
          </Button>
        </View>
      )}

      {error && (
        <Text style={[styles.errorText, { color: '#F44336' }]}>
          {error}
        </Text>
      )}

      {/* In-app verification handler for when emails aren't being received */}
      {verificationSent && !isVerified && (
        <View style={styles.inAppVerificationContainer}>
          <Text style={styles.inAppVerificationTitle}>
            {"Didn't receive the email?"}
          </Text>
          <EmailVerificationHandler
            email={email}
            onVerificationComplete={(status) => {
              if (status && onVerificationStatusChange) {
                onVerificationStatusChange(true);
              }
            }}
          />
        </View>
      )}
    </View>
  );
}

// Wrapper component that provides the language context if needed
function VerificationStatus(props) {
  // Use a simpler approach to avoid context errors
  // Always use the fallback component which doesn't rely on context
  return <VerificationStatusFallback {...props} />;
}

const styles = StyleSheet.create({
  container: {
    marginVertical: 8,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  statusChip: {
    borderWidth: 1,
    backgroundColor: 'transparent',
    marginRight: 8,
  },
  emailText: {
    flex: 1,
    fontSize: 14,
  },
  actionContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  button: {
    marginTop: 4,
  },
  buttonLabel: {
    fontSize: 12,
    paddingHorizontal: 8,
  },
  errorText: {
    fontSize: 12,
    marginTop: 4,
  },
  inAppVerificationContainer: {
    marginTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
    paddingTop: 16,
  },
  inAppVerificationTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 8,
  },
});

export default VerificationStatus;
