/**
 * UUID Polyfill for environments without crypto.getRandomValues()
 * This provides a fallback implementation for generating UUIDs when the standard
 * crypto.getRandomValues() method is not available.
 */

// Check if crypto.getRandomValues is available
const hasCrypto = typeof crypto !== 'undefined' && 
                 typeof crypto.getRandomValues === 'function';

// If crypto.getRandomValues is not available, create a polyfill
if (!hasCrypto) {
  console.log('Applying UUID polyfill for crypto.getRandomValues');
  
  // Create a global crypto object if it doesn't exist
  if (typeof crypto === 'undefined') {
    global.crypto = {};
  }
  
  // Implement a simple polyfill for getRandomValues
  crypto.getRandomValues = function(array) {
    if (!(array instanceof Uint8Array)) {
      throw new Error('Expected Uint8Array');
    }
    
    // Fill the array with random values
    for (let i = 0; i < array.length; i++) {
      array[i] = Math.floor(Math.random() * 256);
    }
    
    return array;
  };
}

// Export a simple function to generate a random UUID-like string
// This is a fallback in case the uuid library still fails
export const generateSimpleUUID = () => {
  const timestamp = new Date().getTime();
  const randomPart = Math.random().toString(36).substring(2, 15);
  return `${timestamp}-${randomPart}`;
};

export default {
  generateSimpleUUID
};
