import React from 'react';
import { View, StyleSheet, SafeAreaView, StatusBar } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import StudentAppHeader from './StudentAppHeader';
import StudentSidebar from './StudentSidebar';
import SidebarBackdrop from './SidebarBackdrop';
import { useStudentSidebar } from '../../context/StudentSidebarContext';

/**
 * A wrapper component for student screens that includes the header and sidebar
 * 
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - The screen content
 * @param {string} props.title - The screen title
 * @param {boolean} props.showBack - Whether to show the back button
 * @param {boolean} props.showNotification - Whether to show the notification icon
 * @returns {React.ReactElement} The StudentScreenWrapper component
 */
const StudentScreenWrapper = ({ 
  children, 
  title, 
  showBack = false,
  showNotification = true
}) => {
  const navigation = useNavigation();
  const { 
    drawerOpen, 
    activeSidebarItem, 
    setActiveSidebarItem, 
    drawerAnim, 
    backdropFadeAnim, 
    toggleDrawer 
  } = useStudentSidebar();

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar backgroundColor={'#1976d2'} barStyle="light-content" />
      
      {/* Student App Header */}
      <StudentAppHeader
        title={title}
        onMenuPress={toggleDrawer}
        showBackButton={showBack}
      />
      
      {/* Student Sidebar */}
      <StudentSidebar
        visible={drawerOpen}
        drawerAnim={drawerAnim}
        toggleDrawer={toggleDrawer}
        navigation={navigation}
        activeSidebarItem={activeSidebarItem}
        setActiveSidebarItem={setActiveSidebarItem}
      />
      
      {/* Sidebar Backdrop */}
      <SidebarBackdrop
        visible={drawerOpen}
        onPress={toggleDrawer}
        fadeAnim={backdropFadeAnim}
      />
      
      {/* Screen Content */}
      <View style={styles.content}>
        {children}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
    padding: 16,
  },
});

export default StudentScreenWrapper;
