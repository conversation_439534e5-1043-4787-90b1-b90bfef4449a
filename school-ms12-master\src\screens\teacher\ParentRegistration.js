import React, { useState, useEffect } from 'react';
import { View, ScrollView, StyleSheet, Image, Platform } from 'react-native';
import {
  Card,
  Title,
  FAB,
  Portal,
  Modal,
  DataTable,
  Searchbar,
  List,
  Text,
  Button,
  ActivityIndicator,
  Chip,
} from 'react-native-paper';
import { db, storage } from '../../config/firebase';
import { collection, addDoc, query, getDocs, doc, updateDoc, deleteDoc, where, setDoc } from 'firebase/firestore';
import { createUserWithEmailAndPassword } from 'firebase/auth';
import { ref, uploadBytes, getDownloadURL } from 'firebase/storage';
import * as ImagePicker from 'expo-image-picker';
import CustomButton from '../../components/common/CustomButton';
import CustomInput from '../../components/common/CustomInput';

const ParentManagement = ({ route, navigation }) => {
  const [parents, setParents] = useState([]);
  const [students, setStudents] = useState([]);
  const [visible, setVisible] = useState(false);
  const [selectedParent, setSelectedParent] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [profileImage, setProfileImage] = useState(null);
  const [kebeleIdImage, setKebeleIdImage] = useState(null);
  
  // Get params from navigation if they exist
  const studentData = route?.params?.studentData ? {
    ...route.params.studentData,
    dateOfBirth: route.params.studentData.dateOfBirth ? new Date(route.params.studentData.dateOfBirth) : new Date(),
    createdAt: route.params.studentData.createdAt ? new Date(route.params.studentData.createdAt) : null,
    updatedAt: route.params.studentData.updatedAt ? new Date(route.params.studentData.updatedAt) : null,
  } : null;
  const parentEmail = route?.params?.parentEmail;
  const onParentAdded = route?.params?.onParentAdded;
  
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: parentEmail || '',
    phone: '',
    alternatePhone: '',
    address: {
      street: '',
      city: 'Asella',
      state: 'Oromia',
      country: 'Ethiopia',
    },
    occupation: '',
    workAddress: '',
    workPhone: '',
    relationship: 'father',
    emergencyContact: false,
    children: [],
    preferredContactMethod: 'email',
    notificationPreferences: {
      attendance: true,
      grades: true,
      behavior: true,
      announcements: true,
      events: true,
    },
    status: 'active',
    password: '1234qwer',
    role: 'parent',
    imageUrl: '',
    kebeleIdUrl: '',
  });

  useEffect(() => {
    fetchParents();
    if (parentEmail) {
      setVisible(true);
    }
  }, [parentEmail]);

  const pickImage = async (type) => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 1,
      });

      if (!result.canceled) {
        const selectedImage = result.assets[0];
        if (type === 'profile') {
          setProfileImage(selectedImage.uri);
        } else if (type === 'kebeleId') {
          setKebeleIdImage(selectedImage.uri);
        }
      }
    } catch (error) {
      console.error('Error picking image:', error);
      setError('Failed to pick image');
    }
  };

  const uploadImage = async (uri, path) => {
    if (!uri) return null;

    try {
      const response = await fetch(uri);
      const blob = await response.blob();
      const storageRef = ref(storage, path);
      await uploadBytes(storageRef, blob);
      return await getDownloadURL(storageRef);
    } catch (error) {
      console.error('Error uploading image:', error);
      throw new Error('Failed to upload image');
    }
  };

  const handleSubmit = async () => {
    try {
      setLoading(true);
      setError(null);

      if (!formData.firstName || !formData.lastName || !formData.email) {
        setError('Please fill in all required fields');
        return;
      }

      // Upload images if selected
      let imageUrl = null;
      let kebeleIdUrl = null;

      if (profileImage) {
        imageUrl = await uploadImage(
          profileImage,
          `parents/${formData.email}/profile.jpg`
        );
      }

      if (kebeleIdImage) {
        kebeleIdUrl = await uploadImage(
          kebeleIdImage,
          `parents/${formData.email}/kebeleId.jpg`
        );
      }

      const parentDoc = {
        ...formData,
        imageUrl: imageUrl || formData.imageUrl,
        kebeleIdUrl: kebeleIdUrl || formData.kebeleIdUrl,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      delete parentDoc.password;

      const docRef = await addDoc(collection(db, 'users'), parentDoc);
      const newParent = { id: docRef.id, ...parentDoc };

      if (studentData && onParentAdded) {
        onParentAdded(newParent);
        navigation.goBack();
      } else {
        setParents(prev => [...prev, newParent]);
        setVisible(false);
        resetForm();
      }
    } catch (error) {
      console.error('Error adding parent:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setFormData({
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      alternatePhone: '',
      address: {
        street: '',
        city: 'Asella',
        state: 'Oromia',
        country: 'Ethiopia',
      },
      occupation: '',
      workAddress: '',
      workPhone: '',
      relationship: 'father',
      emergencyContact: false,
      children: [],
      preferredContactMethod: 'email',
      notificationPreferences: {
        attendance: true,
        grades: true,
        behavior: true,
        announcements: true,
        events: true,
      },
      status: 'active',
      password: '1234qwer',
      role: 'parent',
      imageUrl: '',
      kebeleIdUrl: '',
    });
    setProfileImage(null);
    setKebeleIdImage(null);
  };

  const fetchParents = async () => {
    try {
      const parentsRef = collection(db, 'users');
      const q = query(parentsRef, where('role', '==', 'parent'));
      const querySnapshot = await getDocs(q);
      
      const parentsData = [];
      querySnapshot.forEach((doc) => {
        parentsData.push({ id: doc.id, ...doc.data() });
      });
      
      setParents(parentsData);
    } catch (error) {
      console.error('Error fetching parents:', error);
    }
  };

  const fetchStudents = async () => {
    try {
      const studentsRef = collection(db, 'users');
      const q = query(studentsRef, where('role', '==', 'student'));
      const querySnapshot = await getDocs(q);
      
      const studentsData = [];
      querySnapshot.forEach((doc) => {
        studentsData.push({ id: doc.id, ...doc.data() });
      });
      
      setStudents(studentsData);
    } catch (error) {
      console.error('Error fetching students:', error);
    }
  };

  const handleUpdateParent = async () => {
    try {
      setLoading(true);
      const parentRef = doc(db, 'users', selectedParent.id);
      await updateDoc(parentRef, {
        firstName: formData.firstName,
        lastName: formData.lastName,
        phone: formData.phone,
        alternatePhone: formData.alternatePhone,
        address: formData.address,
        occupation: formData.occupation,
        workAddress: formData.workAddress,
        workPhone: formData.workPhone,
        relationship: formData.relationship,
        emergencyContact: formData.emergencyContact,
        children: formData.children,
        preferredContactMethod: formData.preferredContactMethod,
        notificationPreferences: formData.notificationPreferences,
        status: formData.status,
        updatedAt: new Date().toISOString(),
      });
      
      setVisible(false);
      resetForm();
      fetchParents();
    } catch (error) {
      console.error('Error updating parent:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteParent = async (parentId, uid) => {
    try {
      // Delete user auth account
      await deleteUser(auth.currentUser);
      
      // Delete user document
      await deleteDoc(doc(db, 'users', parentId));
      
      fetchParents();
    } catch (error) {
      console.error('Error deleting parent:', error);
    }
  };

  const toggleChild = (studentId) => {
    const children = formData.children.includes(studentId)
      ? formData.children.filter(id => id !== studentId)
      : [...formData.children, studentId];
    setFormData({ ...formData, children });
  };

  const toggleNotificationPreference = (preference) => {
    setFormData({
      ...formData,
      notificationPreferences: {
        ...formData.notificationPreferences,
        [preference]: !formData.notificationPreferences[preference],
      },
    });
  };

  const filteredParents = parents.filter(parent =>
    `${parent.firstName} ${parent.lastName}`.toLowerCase().includes(searchQuery.toLowerCase()) ||
    parent.email.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <View style={styles.container}>
      <Searchbar
        placeholder="Search parents..."
        onChangeText={setSearchQuery}
        value={searchQuery}
        style={styles.searchBar}
      />

      <ScrollView>
        <DataTable>
          <DataTable.Header>
            <DataTable.Title>Name</DataTable.Title>
            <DataTable.Title>Email</DataTable.Title>
            <DataTable.Title>Phone</DataTable.Title>
            <DataTable.Title>Status</DataTable.Title>
          </DataTable.Header>

          {filteredParents.map((parent) => (
            <DataTable.Row
              key={parent.id}
              onPress={() => {
                setSelectedParent(parent);
                setFormData({
                  firstName: parent.firstName,
                  lastName: parent.lastName,
                  email: parent.email,
                  phone: parent.phone,
                  alternatePhone: parent.alternatePhone,
                  address: parent.address || {
                    street: '',
                    city: '',
                    state: '',
                    country: '',
                  },
                  occupation: parent.occupation,
                  workAddress: parent.workAddress,
                  workPhone: parent.workPhone,
                  relationship: parent.relationship || 'father',
                  emergencyContact: parent.emergencyContact || false,
                  children: parent.children || [],
                  preferredContactMethod: parent.preferredContactMethod || 'email',
                  notificationPreferences: parent.notificationPreferences || {
                    attendance: true,
                    grades: true,
                    behavior: true,
                    announcements: true,
                    events: true,
                  },
                  status: parent.status || 'active',
                });
                setVisible(true);
              }}
            >
              <DataTable.Cell>{`${parent.firstName} ${parent.lastName}`}</DataTable.Cell>
              <DataTable.Cell>{parent.email}</DataTable.Cell>
              <DataTable.Cell>{parent.phone}</DataTable.Cell>
              <DataTable.Cell>
                <Chip mode="outlined">{parent.status}</Chip>
              </DataTable.Cell>
            </DataTable.Row>
          ))}
        </DataTable>
      </ScrollView>

      <Portal>
        <Modal
          visible={visible}
          onDismiss={() => {
            setVisible(false);
            resetForm();
          }}
          contentContainerStyle={styles.modalContainer}
        >
          <ScrollView>
            <Card>
              <Card.Content>
                <Title>Parent Information</Title>

                <View style={styles.imageSection}>
                  <View style={styles.imageContainer}>
                    <Title style={styles.imageTitle}>Profile Image</Title>
                    {profileImage ? (
                      <Image source={{ uri: profileImage }} style={styles.image} />
                    ) : formData.imageUrl ? (
                      <Image source={{ uri: formData.imageUrl }} style={styles.image} />
                    ) : (
                      <View style={styles.placeholderImage} />
                    )}
                    <Button
                      mode="contained"
                      onPress={() => pickImage('profile')}
                      style={styles.imageButton}
                    >
                      Select Profile Image
                    </Button>
                  </View>

                  </View>

                <CustomInput
                  label="First Name"
                  value={formData.firstName}
                  onChangeText={(text) => setFormData({ ...formData, firstName: text })}
                />
                <CustomInput
                  label="Last Name"
                  value={formData.lastName}
                  onChangeText={(text) => setFormData({ ...formData, lastName: text })}
                />
                <CustomInput
                  label="Email"
                  value={formData.email}
                  onChangeText={(text) => setFormData({ ...formData, email: text })}
                  keyboardType="email-address"
                />
                <CustomInput
                  label="Phone"
                  value={formData.phone}
                  onChangeText={(text) => setFormData({ ...formData, phone: text })}
                  keyboardType="phone-pad"
                />

                <Title style={styles.sectionTitle}>Address</Title>
                <CustomInput
                  label="Street"
                  value={formData.address.street}
                  onChangeText={(text) => setFormData({
                    ...formData,
                    address: { ...formData.address, street: text },
                  })}
                />
                
                <CustomInput
                  label="City"
                  value={formData.address.city}
                  onChangeText={(text) => setFormData({
                    ...formData,
                    address: { ...formData.address, city: text },
                  })}
                />
                
                <CustomInput
                  label="State"
                  value={formData.address.state}
                  onChangeText={(text) => setFormData({
                    ...formData,
                    address: { ...formData.address, state: text },
                  })}
                />
                
                <CustomInput
                  label="Country"
                  value={formData.address.country}
                  onChangeText={(text) => setFormData({
                    ...formData,
                    address: { ...formData.address, country: text },
                  })}
                />

                <CustomInput
                  label="Occupation"
                  value={formData.occupation}
                  onChangeText={(text) => setFormData({ ...formData, occupation: text })}
                />
                
                <CustomInput
                  label="Work Address"
                  value={formData.workAddress}
                  onChangeText={(text) => setFormData({ ...formData, workAddress: text })}
                  multiline
                  numberOfLines={2}
                />
                
                <CustomInput
                  label="Work Phone"
                  value={formData.workPhone}
                  onChangeText={(text) => setFormData({ ...formData, workPhone: text })}
                  keyboardType="phone-pad"
                />

                <List.Section title="Relationship">
                  {['father', 'mother', 'guardian'].map((relationship) => (
                    <List.Item
                      key={relationship}
                      title={relationship.charAt(0).toUpperCase() + relationship.slice(1)}
                      onPress={() => setFormData({ ...formData, relationship })}
                      style={formData.relationship === relationship ? styles.selectedItem : null}
                      left={props => <List.Icon {...props} icon="account" />}
                    />
                  ))}
                </List.Section>

                <List.Item
                  title="Emergency Contact"
                  onPress={() => setFormData({ ...formData, emergencyContact: !formData.emergencyContact })}
                  left={props => (
                    <List.Icon
                      {...props}
                      icon={formData.emergencyContact ? 'checkbox-marked' : 'checkbox-blank-outline'}
                    />
                  )}
                />

                <Title style={styles.sectionTitle}>Children</Title>
                <ScrollView horizontal style={styles.chipScrollView}>
                  <View style={styles.chipContainer}>
                    {students.map((student) => (
                      <Chip
                        key={student.id}
                        selected={formData.children.includes(student.id)}
                        onPress={() => toggleChild(student.id)}
                        style={styles.chip}
                        mode="outlined"
                      >
                        {`${student.firstName} ${student.lastName}`}
                      </Chip>
                    ))}
                  </View>
                </ScrollView>

                <List.Section title="Preferred Contact Method">
                  {['email', 'phone', 'sms'].map((method) => (
                    <List.Item
                      key={method}
                      title={method.charAt(0).toUpperCase() + method.slice(1)}
                      onPress={() => setFormData({ ...formData, preferredContactMethod: method })}
                      style={formData.preferredContactMethod === method ? styles.selectedItem : null}
                      left={props => <List.Icon {...props} icon={
                        method === 'email' ? 'email' :
                        method === 'phone' ? 'phone' : 'message'
                      } />}
                    />
                  ))}
                </List.Section>

                <Title style={styles.sectionTitle}>Notification Preferences</Title>
                {Object.entries(formData.notificationPreferences).map(([key, value]) => (
                  <List.Item
                    key={key}
                    title={key.charAt(0).toUpperCase() + key.slice(1)}
                    onPress={() => toggleNotificationPreference(key)}
                    left={props => (
                      <List.Icon
                        {...props}
                        icon={value ? 'checkbox-marked' : 'checkbox-blank-outline'}
                      />
                    )}
                  />
                ))}

                <List.Section title="Status">
                  {['active', 'inactive'].map((status) => (
                    <List.Item
                      key={status}
                      title={status.charAt(0).toUpperCase() + status.slice(1)}
                      onPress={() => setFormData({ ...formData, status })}
                      style={formData.status === status ? styles.selectedItem : null}
                      left={props => <List.Icon {...props} icon="account-check" />}
                    />
                  ))}
                </List.Section>

                <View style={styles.imageContainer}>
                    <Title style={styles.imageTitle}>Kebele ID</Title>
                    {kebeleIdImage ? (
                      <Image source={{ uri: kebeleIdImage }} style={styles.image1} />
                    ) : formData.kebeleIdUrl ? (
                      <Image source={{ uri: formData.kebeleIdUrl }} style={styles.image1} />
                    ) : (
                      <View style={styles.placeholderImage1} />
                    )}
                    <Button
                      mode="contained"
                      onPress={() => pickImage('kebeleId')}
                      style={styles.imageButton}
                    >
                      Select Kebele ID
                    </Button>
                  </View>
                
                <View style={styles.modalButtons}>
                  {selectedParent ? (
                    <CustomButton
                      mode="contained"
                      onPress={handleUpdateParent}
                      loading={loading}
                    >
                      Update
                    </CustomButton>
                  ) : (
                    <CustomButton
                      mode="contained"
                      onPress={handleSubmit}
                      loading={loading}
                    >
                      Add
                    </CustomButton>
                  )}
                  
                  {selectedParent && (
                    <CustomButton
                      mode="outlined"
                      onPress={() => handleDeleteParent(selectedParent.id, selectedParent.uid)}
                      style={styles.deleteButton}
                    >
                      Delete
                    </CustomButton>
                  )}
                  
                  <CustomButton
                    mode="outlined"
                    onPress={() => {
                      setVisible(false);
                      resetForm();
                    }}
                  >
                    Cancel
                  </CustomButton>
                </View>
              </Card.Content>
            </Card>
          </ScrollView>
        </Modal>
      </Portal>

      <FAB
        style={styles.fab}
        icon="plus"
        onPress={() => setVisible(true)}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  searchBar: {
    margin: 10,
    elevation: 2,
  },
  modalContainer: {
    backgroundColor: 'white',
    padding: 20,
    margin: 20,
    maxHeight: '90%',
  },
  imageSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  imageContainer: {
    flex: 1,
    alignItems: 'center',
    marginHorizontal: 10,
  },
  imageTitle: {
    fontSize: 16,
    marginBottom: 10,
  },
  image: {
    width: 150,
    height: 150,
    borderRadius: 75,
    marginBottom: 10,
  },
  image1: {
    width: 200,
    height: 150,
    marginBottom: 10,
  },
  placeholderImage: {
    width: 150,
    height: 150,
    borderRadius: 75,
    backgroundColor: '#e0e0e0',
    marginBottom: 10,
  },
  placeholderImage1: {
    width: 200,
    height: 150,
    backgroundColor: '#e0e0e0',
    marginBottom: 10,
  },
  imageButton: {
    marginTop: 10,
  },
  buttonContainer: {
    marginTop: 20,
  },
  submitButton: {
    marginTop: 10,
  },
  selectedItem: {
    backgroundColor: '#e8f4f8',
  },
  chipScrollView: {
    maxHeight: 100,
  },
  chipContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    padding: 5,
  },
  chip: {
    margin: 4,
  },
  sectionTitle: {
    fontSize: 18,
    marginTop: 20,
    marginBottom: 10,
  },
  modalButtons: {
    marginTop: 20,
  },
  deleteButton: {
    marginVertical: 10,
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
  },
});

export default ParentManagement;
