# Customization Guide for Mobile User Management

This guide will help you customize the appearance of the Mobile User Management screen to match your application's design system.

## Theme Customization

The Mobile User Management screen uses the `mobileTheme` object for styling. You can customize this theme to change the overall appearance of the screen.

### Updating the Mobile Theme

Locate your theme file (typically `src/theme/mobileTheme.js`) and update the colors and other properties:

```javascript
// Example mobileTheme.js
import { DefaultTheme } from 'react-native-paper';

const mobileTheme = {
  ...DefaultTheme,
  colors: {
    ...DefaultTheme.colors,
    primary: '#1976D2',        // Main primary color
    accent: '#FF4081',         // Accent color
    background: '#F5F5F5',     // Background color
    surface: '#FFFFFF',        // Surface color for cards
    text: '#212121',           // Main text color
    placeholder: '#9E9E9E',    // Placeholder text color
    
    // Role-specific colors
    admin: '#673AB7',          // Admin color (purple)
    teacher: '#2196F3',        // Teacher color (blue)
    student: '#4CAF50',        // Student color (green)
    parent: '#FF9800',         // Parent color (orange)
    
    // Status colors
    success: '#4CAF50',        // Success color (green)
    warning: '#FF9800',        // Warning color (orange)
    error: '#F44336',          // Error color (red)
    info: '#2196F3',           // Info color (blue)
    
    // Additional colors
    divider: '#BDBDBD',        // Divider color
    disabled: '#E0E0E0',       // Disabled state color
  },
  
  // Typography
  fonts: {
    ...DefaultTheme.fonts,
    regular: {
      fontFamily: 'sans-serif',
      fontWeight: 'normal',
    },
    medium: {
      fontFamily: 'sans-serif-medium',
      fontWeight: 'normal',
    },
    light: {
      fontFamily: 'sans-serif-light',
      fontWeight: 'normal',
    },
    thin: {
      fontFamily: 'sans-serif-thin',
      fontWeight: 'normal',
    },
  },
  
  // Animation
  animation: {
    scale: 1.0,                // Animation scale factor
  },
  
  // Roundness
  roundness: 8,                // Border radius for components
};

export default mobileTheme;
```

## Component-Specific Style Customization

You can also customize specific components by modifying the styles in the MobileUserManagement component.

### User Card Customization

To customize the appearance of user cards:

```javascript
// In MobileUserManagement.js
const styles = StyleSheet.create({
  // ...existing styles
  
  userCard: {
    marginHorizontal: 16,
    marginVertical: 8,
    borderRadius: 12,         // Increased border radius
    elevation: 3,             // Increased elevation for more shadow
    borderLeftWidth: 4,       // Add a colored border on the left
    borderLeftColor: '#673AB7', // Border color
  },
  
  userCardContent: {
    padding: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  
  // ...other styles
});
```

### Stat Cards Customization

To customize the statistics cards:

```javascript
// In MobileUserManagement.js
const styles = StyleSheet.create({
  // ...existing styles
  
  statCard: {
    padding: 16,
    borderRadius: 12,         // Increased border radius
    elevation: 3,             // Increased elevation
    alignItems: 'center',
    backgroundColor: '#FFFFFF', // Explicit background color
    borderTopWidth: 4,        // Add a colored border on top
  },
  
  statValue: {
    fontSize: 28,             // Larger font size
    fontWeight: 'bold',
    marginVertical: 8,
    color: '#212121',         // Explicit text color
  },
  
  statLabel: {
    fontSize: 12,
    color: '#757575',         // Darker color for better contrast
    textTransform: 'uppercase', // Make labels uppercase
    letterSpacing: 1,         // Add letter spacing
  },
  
  // ...other styles
});
```

### Swipe Actions Customization

To customize the swipe actions:

```javascript
// In MobileUserManagement.js
const styles = StyleSheet.create({
  // ...existing styles
  
  backLeftBtn: {
    alignItems: 'center',
    bottom: 0,
    justifyContent: 'center',
    position: 'absolute',
    top: 0,
    width: 75,
    borderTopLeftRadius: 12,    // Match card border radius
    borderBottomLeftRadius: 12, // Match card border radius
  },
  
  backLeftBtnLeft: {
    backgroundColor: '#2196F3', // Different color
    left: 0,
  },
  
  backRightBtn: {
    alignItems: 'center',
    bottom: 0,
    justifyContent: 'center',
    position: 'absolute',
    top: 0,
    width: 75,
  },
  
  backRightBtnLeft: {
    backgroundColor: '#FF9800', // Different color
    right: 75,
  },
  
  backRightBtnRight: {
    backgroundColor: '#F44336', // Different color
    right: 0,
    borderTopRightRadius: 12,    // Match card border radius
    borderBottomRightRadius: 12, // Match card border radius
  },
  
  backTextWhite: {
    color: '#FFFFFF',
    fontSize: 13,              // Slightly larger
    fontWeight: 'bold',        // Make text bold
    textAlign: 'center',
  },
  
  // ...other styles
});
```

## Animation Customization

You can customize the animations used in the component:

```javascript
// In MobileUserManagement.js
<Animatable.View 
  animation="fadeInDown"      // Change animation type
  duration={600}              // Increase duration
  easing="ease-in-out"        // Add easing
  style={styles.searchContainer}
>
  {/* Component content */}
</Animatable.View>
```

Available animations include:
- `fadeIn`, `fadeInDown`, `fadeInUp`, `fadeInLeft`, `fadeInRight`
- `slideInUp`, `slideInDown`, `slideInLeft`, `slideInRight`
- `zoomIn`, `zoomOut`
- `bounceIn`, `bounceInUp`, `bounceInDown`
- And many more from the react-native-animatable library

## RTL Support Customization

To improve RTL language support:

```javascript
// In MobileUserManagement.js
<View style={[
  styles.userInfo, 
  isRTL && { flexDirection: 'row-reverse' }
]}>
  {/* Component content */}
</View>
```

## Custom Icons

You can customize the icons used in the component:

```javascript
// In MobileUserManagement.js
<MaterialCommunityIcons 
  name="account-circle"       // Change icon
  size={28}                   // Change size
  color={mobileTheme.colors.primary} // Change color
/>
```

## Custom FAB Actions

You can customize the FAB actions:

```javascript
// In MobileUserManagement.js
const fabActions = [
  {
    icon: 'account-plus-outline', // Change to outline version
    label: translate('userManagement.addUser'),
    onPress: () => navigation.navigate('UserRegistration'),
    style: { 
      backgroundColor: mobileTheme.colors.admin,
      borderWidth: 1,          // Add border
      borderColor: '#FFFFFF'   // White border
    }
  },
  // ...other actions
];
```

## Applying a Custom Font

To use a custom font throughout the application:

1. Add the font files to your project (typically in an `assets/fonts` directory)
2. Load the fonts using Expo's font loading mechanism:

```javascript
// In App.js or a similar entry point
import * as Font from 'expo-font';

// Load fonts in your app initialization
await Font.loadAsync({
  'custom-font': require('./assets/fonts/CustomFont-Regular.ttf'),
  'custom-font-bold': require('./assets/fonts/CustomFont-Bold.ttf'),
});

// Then update your theme
const mobileTheme = {
  // ...other theme properties
  fonts: {
    regular: {
      fontFamily: 'custom-font',
      fontWeight: 'normal',
    },
    medium: {
      fontFamily: 'custom-font-bold',
      fontWeight: 'normal',
    },
    // ...other font variants
  },
};
```

## Testing Your Customizations

After making customizations:

1. Run the app on a mobile device or simulator
2. Navigate to the Mobile User Management screen
3. Verify that your customizations are applied correctly
4. Test on different screen sizes to ensure responsive design
5. Test with both LTR and RTL languages if your app supports them
