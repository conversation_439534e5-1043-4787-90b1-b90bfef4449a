import React, { useState, useEffect } from 'react';
import { View, ScrollView, StyleSheet } from 'react-native';
import { Card, Title, Text, DataTable, Portal, Modal, ActivityIndicator, Chip } from 'react-native-paper';
import { db, auth } from '../../config/firebase';
import { collection, query, where, getDocs, onSnapshot, doc, getDoc } from 'firebase/firestore';
import CustomButton from '../../components/common/CustomButton';
import { Picker } from '@react-native-picker/picker';
import { LineChart } from 'react-native-chart-kit';
import EthiopianCalendar from '../../utils/EthiopianCalendar';
import { sanitizeChartData, sanitizeChartDatasets } from '../../utils/ChartUtils';

const BehaviorView = () => {
  const [userRole, setUserRole] = useState('');
  const [students, setStudents] = useState([]);
  const [selectedStudent, setSelectedStudent] = useState('');
  const [behaviorRecords, setBehaviorRecords] = useState([]);
  const [selectedRecord, setSelectedRecord] = useState(null);
  const [modalVisible, setModalVisible] = useState(false);
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('month'); // week, month, semester, year

  useEffect(() => {
    fetchUserRole();
  }, []);

  useEffect(() => {
    if (userRole === 'parent') {
      fetchChildren();
    } else if (userRole === 'student') {
      setSelectedStudent(auth.currentUser.uid);
      setLoading(false);
    }
  }, [userRole]);

  useEffect(() => {
    if (selectedStudent) {
      setupBehaviorListener();
    }
  }, [selectedStudent]);

  const fetchUserRole = async () => {
    try {
      const userRef = doc(db, 'users', auth.currentUser.uid);
      const userDoc = await getDoc(userRef);
      if (userDoc.exists()) {
        setUserRole(userDoc.data().role);
      }
    } catch (error) {
      console.error('Error fetching user role:', error);
    }
  };

  const fetchChildren = async () => {
    try {
      const studentsRef = collection(db, 'users');
      const q = query(
        studentsRef,
        where('role', '==', 'student'),
        where('parentId', '==', auth.currentUser.uid)
      );
      const querySnapshot = await getDocs(q);
      
      const studentData = [];
      querySnapshot.forEach((doc) => {
        studentData.push({ id: doc.id, ...doc.data() });
      });
      
      setStudents(studentData);
      if (studentData.length > 0) {
        setSelectedStudent(studentData[0].id);
      }
    } catch (error) {
      console.error('Error fetching children:', error);
    } finally {
      setLoading(false);
    }
  };

  const setupBehaviorListener = () => {
    const behaviorRef = collection(db, 'behavior');
    const q = query(behaviorRef, where('studentId', '==', selectedStudent));
    
    const unsubscribe = onSnapshot(q, (snapshot) => {
      const behaviorData = [];
      snapshot.forEach((doc) => {
        behaviorData.push({ id: doc.id, ...doc.data() });
      });
      setBehaviorRecords(behaviorData.sort((a, b) => 
        new Date(b.timestamp) - new Date(a.timestamp)
      ));
    });

    return unsubscribe;
  };

  const formatDate = (date) => {
    return EthiopianCalendar.formatDate(new Date(date));
  };

  const getBehaviorTypeColor = (type) => {
    switch (type) {
      case 'positive': return '#4CAF50';
      case 'negative': return '#f44336';
      default: return '#FF9800';
    }
  };

  const calculateBehaviorScore = () => {
    return behaviorRecords.reduce((total, record) => {
      const points = parseInt(record.points) || 0;
      return total + (record.type === 'positive' ? points : -points);
    }, 0);
  };

  const getChartData = () => {
    const now = new Date();
    let timeLimit;
    
    switch (timeRange) {
      case 'week':
        timeLimit = new Date(now.setDate(now.getDate() - 7));
        break;
      case 'month':
        timeLimit = new Date(now.setMonth(now.getMonth() - 1));
        break;
      case 'semester':
        timeLimit = new Date(now.setMonth(now.getMonth() - 6));
        break;
      case 'year':
        timeLimit = new Date(now.setFullYear(now.getFullYear() - 1));
        break;
    }

    const filteredRecords = behaviorRecords.filter(record => 
      new Date(record.timestamp) >= timeLimit
    );

    const groupedData = filteredRecords.reduce((acc, record) => {
      const date = new Date(record.timestamp);
      const key = formatDate(date);
      if (!acc[key]) {
        acc[key] = { positive: 0, negative: 0 };
      }
      const points = parseInt(record.points) || 0;
      if (record.type === 'positive') {
        acc[key].positive += points;
      } else if (record.type === 'negative') {
        acc[key].negative += points;
      }
      return acc;
    }, {});

    return {
      labels: Object.keys(groupedData).slice(-6),
      datasets: [
        {
          data: sanitizeChartData(Object.values(groupedData).slice(-6).map(d => d.positive)),
          color: (opacity = 1) => `rgba(76, 175, 80, ${opacity})`,
          strokeWidth: 2,
        },
        {
          data: sanitizeChartData(Object.values(groupedData).slice(-6).map(d => -d.negative)),
          color: (opacity = 1) => `rgba(244, 67, 54, ${opacity})`,
          strokeWidth: 2,
        },
      ],
      legend: ['Positive', 'Negative'],
    };
  };

  return (
    <ScrollView style={styles.container}>
      <Card style={styles.card}>
        <Card.Content>
          <Title>Behavior Records</Title>

          {userRole === 'parent' && (
            <View style={styles.pickerContainer}>
              <Text style={styles.pickerLabel}>Select Child</Text>
              <Picker
                selectedValue={selectedStudent}
                onValueChange={setSelectedStudent}
                style={styles.picker}
              >
                <Picker.Item label="Select..." value="" />
                {students.map((student) => (
                  <Picker.Item 
                    key={student.id} 
                    label={student.displayName} 
                    value={student.id} 
                  />
                ))}
              </Picker>
            </View>
          )}

          {loading ? (
            <ActivityIndicator style={styles.loader} />
          ) : selectedStudent ? (
            <View>
              <Card style={styles.scoreCard}>
                <Card.Content>
                  <Title>Overall Behavior Score</Title>
                  <Text style={[
                    styles.scoreText,
                    { color: calculateBehaviorScore() >= 0 ? '#4CAF50' : '#f44336' }
                  ]}>
                    {calculateBehaviorScore()}
                  </Text>
                </Card.Content>
              </Card>

              <View style={styles.chartContainer}>
                <View style={styles.chartHeader}>
                  <Title>Behavior Trends</Title>
                  <View style={styles.timeRangeContainer}>
                    {['week', 'month', 'semester', 'year'].map((range) => (
                      <CustomButton
                        key={range}
                        mode={timeRange === range ? 'contained' : 'outlined'}
                        onPress={() => setTimeRange(range)}
                        style={styles.timeRangeButton}
                      >
                        {range.charAt(0).toUpperCase() + range.slice(1)}
                      </CustomButton>
                    ))}
                  </View>
                </View>

                <LineChart
                  data={sanitizeChartDatasets(getChartData())}
                  width={350}
                  height={220}
                  chartConfig={{
                    backgroundColor: '#ffffff',
                    backgroundGradientFrom: '#ffffff',
                    backgroundGradientTo: '#ffffff',
                    decimalPlaces: 0,
                    color: (opacity = 1) => `rgba(0, 0, 0, ${opacity})`,
                    style: {
                      borderRadius: 16,
                    },
                  }}
                  bezier
                  style={styles.chart}
                />
              </View>

              <DataTable>
                <DataTable.Header>
                  <DataTable.Title>Date</DataTable.Title>
                  <DataTable.Title>Type</DataTable.Title>
                  <DataTable.Title>Category</DataTable.Title>
                  <DataTable.Title numeric>Points</DataTable.Title>
                </DataTable.Header>

                {behaviorRecords.map((record) => (
                  <DataTable.Row 
                    key={record.id}
                    onPress={() => {
                      setSelectedRecord(record);
                      setModalVisible(true);
                    }}
                  >
                    <DataTable.Cell>{formatDate(record.timestamp)}</DataTable.Cell>
                    <DataTable.Cell>
                      <Chip
                        style={[
                          styles.typeChip,
                          { backgroundColor: getBehaviorTypeColor(record.type) }
                        ]}
                      >
                        {record.type}
                      </Chip>
                    </DataTable.Cell>
                    <DataTable.Cell>{record.category}</DataTable.Cell>
                    <DataTable.Cell numeric>{record.points}</DataTable.Cell>
                  </DataTable.Row>
                ))}
              </DataTable>
            </View>
          ) : (
            <Text style={styles.selectPrompt}>
              Please select a student to view behavior records.
            </Text>
          )}
        </Card.Content>
      </Card>

      <Portal>
        <Modal
          visible={modalVisible}
          onDismiss={() => setModalVisible(false)}
          contentContainerStyle={styles.modalContent}
        >
          {selectedRecord && (
            <ScrollView>
              <Title>Behavior Record Details</Title>
              
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Date:</Text>
                <Text>{formatDate(selectedRecord.timestamp)}</Text>
              </View>

              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Type:</Text>
                <Chip
                  style={[
                    styles.typeChip,
                    { backgroundColor: getBehaviorTypeColor(selectedRecord.type) }
                  ]}
                >
                  {selectedRecord.type}
                </Chip>
              </View>

              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Category:</Text>
                <Text>{selectedRecord.category}</Text>
              </View>

              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Points:</Text>
                <Text>{selectedRecord.points}</Text>
              </View>

              <View style={styles.detailSection}>
                <Text style={styles.detailLabel}>Description:</Text>
                <Text style={styles.description}>{selectedRecord.description}</Text>
              </View>

              {selectedRecord.actionTaken && (
                <View style={styles.detailSection}>
                  <Text style={styles.detailLabel}>Action Taken:</Text>
                  <Text style={styles.description}>{selectedRecord.actionTaken}</Text>
                </View>
              )}

              <CustomButton
                mode="contained"
                onPress={() => setModalVisible(false)}
                style={styles.closeButton}
              >
                Close
              </CustomButton>
            </ScrollView>
          )}
        </Modal>
      </Portal>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  card: {
    margin: 16,
  },
  pickerContainer: {
    marginBottom: 20,
  },
  pickerLabel: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
  },
  picker: {
    backgroundColor: '#f5f5f5',
    borderRadius: 4,
  },
  loader: {
    marginVertical: 20,
  },
  scoreCard: {
    marginBottom: 20,
    backgroundColor: '#E3F2FD',
  },
  scoreText: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  chartContainer: {
    marginBottom: 20,
  },
  chartHeader: {
    marginBottom: 16,
  },
  timeRangeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 8,
  },
  timeRangeButton: {
    flex: 1,
    marginHorizontal: 4,
  },
  chart: {
    marginVertical: 8,
    borderRadius: 16,
  },
  typeChip: {
    alignSelf: 'flex-start',
  },
  selectPrompt: {
    textAlign: 'center',
    marginTop: 20,
    color: '#666',
  },
  modalContent: {
    backgroundColor: 'white',
    padding: 20,
    margin: 20,
    borderRadius: 8,
    maxHeight: '80%',
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  detailSection: {
    marginBottom: 16,
  },
  detailLabel: {
    fontWeight: 'bold',
    color: '#666',
    marginBottom: 4,
  },
  description: {
    lineHeight: 20,
  },
  closeButton: {
    marginTop: 16,
  },
});

export default BehaviorView;
