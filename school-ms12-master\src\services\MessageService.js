import { db } from '../config/firebase';
import { 
  collection, 
  addDoc, 
  query, 
  where, 
  getDocs, 
  updateDoc, 
  doc, 
  orderBy, 
  serverTimestamp,
  limit,
  deleteDoc
} from 'firebase/firestore';

class MessageService {
  static async sendMessage(messageData) {
    try {
      const { senderId, recipientId, subject, content, attachments = [] } = messageData;
      
      // Create conversation if it doesn't exist
      const conversationId = await this.getOrCreateConversation([senderId, recipientId]);
      
      // Add message
      const messageRef = collection(db, 'messages');
      const message = {
        conversationId,
        senderId,
        content,
        subject,
        attachments,
        status: 'sent',
        createdAt: serverTimestamp(),
        readBy: [senderId]
      };
      
      await addDoc(messageRef, message);
      
      // Update conversation's last message
      await updateDoc(doc(db, 'conversations', conversationId), {
        lastMessageAt: serverTimestamp(),
        lastMessage: content.substring(0, 100),
        unreadCount: {
          [recipientId]: (await this.getUnreadCount(conversationId, recipientId)) + 1
        }
      });
      
      return true;
    } catch (error) {
      console.error('Error sending message:', error);
      throw error;
    }
  }

  static async getOrCreateConversation(participants) {
    try {
      // Check if conversation exists
      const conversationsRef = collection(db, 'conversations');
      const q = query(
        conversationsRef,
        where('participants', 'array-contains', participants[0])
      );
      const querySnapshot = await getDocs(q);
      
      let conversation = null;
      querySnapshot.forEach(doc => {
        const data = doc.data();
        if (data.participants.includes(participants[1])) {
          conversation = { id: doc.id, ...data };
        }
      });
      
      if (conversation) {
        return conversation.id;
      }
      
      // Create new conversation
      const newConversation = {
        participants,
        createdAt: serverTimestamp(),
        lastMessageAt: serverTimestamp(),
        lastMessage: '',
        unreadCount: {
          [participants[0]]: 0,
          [participants[1]]: 0
        }
      };
      
      const docRef = await addDoc(conversationsRef, newConversation);
      return docRef.id;
    } catch (error) {
      console.error('Error with conversation:', error);
      throw error;
    }
  }

  static async getConversations(userId) {
    try {
      const conversationsRef = collection(db, 'conversations');
      const q = query(
        conversationsRef,
        where('participants', 'array-contains', userId),
        orderBy('lastMessageAt', 'desc')
      );
      
      const querySnapshot = await getDocs(q);
      const conversations = [];
      
      querySnapshot.forEach(doc => {
        conversations.push({ id: doc.id, ...doc.data() });
      });
      
      return conversations;
    } catch (error) {
      console.error('Error getting conversations:', error);
      throw error;
    }
  }

  static async getMessages(conversationId, limitCount = 50) {
    try {
      const messagesRef = collection(db, 'messages');
      const q = query(
        messagesRef,
        where('conversationId', '==', conversationId),
        orderBy('createdAt', 'desc'),
        limit(limitCount)
      );
      
      const querySnapshot = await getDocs(q);
      const messages = [];
      
      querySnapshot.forEach(doc => {
        messages.push({ id: doc.id, ...doc.data() });
      });
      
      return messages.reverse();
    } catch (error) {
      console.error('Error getting messages:', error);
      throw error;
    }
  }

  static async markAsRead(messageId, userId) {
    try {
      const messageRef = doc(db, 'messages', messageId);
      await updateDoc(messageRef, {
        readBy: [userId]
      });
    } catch (error) {
      console.error('Error marking message as read:', error);
      throw error;
    }
  }

  static async getUnreadCount(conversationId, userId) {
    try {
      const messagesRef = collection(db, 'messages');
      const q = query(
        messagesRef,
        where('conversationId', '==', conversationId),
        where('readBy', 'array-contains', userId)
      );
      
      const querySnapshot = await getDocs(q);
      return querySnapshot.size;
    } catch (error) {
      console.error('Error getting unread count:', error);
      throw error;
    }
  }

  static async deleteMessage(messageId) {
    try {
      await deleteDoc(doc(db, 'messages', messageId));
      return true;
    } catch (error) {
      console.error('Error deleting message:', error);
      throw error;
    }
  }

  static async searchMessages(userId, searchQuery) {
    try {
      const conversationsRef = collection(db, 'conversations');
      const q = query(
        conversationsRef,
        where('participants', 'array-contains', userId)
      );
      
      const querySnapshot = await getDocs(q);
      const conversations = [];
      
      querySnapshot.forEach(doc => {
        if (doc.data().lastMessage.toLowerCase().includes(searchQuery.toLowerCase())) {
          conversations.push({ id: doc.id, ...doc.data() });
        }
      });
      
      return conversations;
    } catch (error) {
      console.error('Error searching messages:', error);
      throw error;
    }
  }
}

export default MessageService;
