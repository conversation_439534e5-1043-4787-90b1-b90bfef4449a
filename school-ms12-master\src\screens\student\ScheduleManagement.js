import React, { useState, useEffect, useRef } from 'react';
import { View, ScrollView, StyleSheet, StatusBar, TouchableOpacity, RefreshControl, Animated } from 'react-native';
import { Card, Title, Text, DataTable, List, Chip, useTheme, IconButton, ActivityIndicator, Divider, Surface, SegmentedButtons, Badge } from 'react-native-paper';
import ScrollableTable from '../../components/common/ScrollableTable';
import { tableStyles } from '../../styles/tableStyles';
import { db } from '../../config/firebase';
import { collection, query, getDocs, where, onSnapshot } from 'firebase/firestore';
import { useAuth } from '../../context/AuthContext';
import { useLanguage } from '../../context/LanguageContext';
import { useNavigation } from '@react-navigation/native';
import * as Animatable from 'react-native-animatable';
import { SafeAreaView } from 'react-native-safe-area-context';
import StudentAppHeader from '../../components/common/StudentAppHeader';
import StudentSidebar from '../../components/common/StudentSidebar';
import SidebarBackdrop from '../../components/common/SidebarBackdrop';

const ScheduleManagement = () => {
  const { user } = useAuth();
  const navigation = useNavigation();
  // No theme needed
  const { translate, language, isRTL } = useLanguage();

  // State variables
  const [schedule, setSchedule] = useState([]);
  const [selectedDay, setSelectedDay] = useState('Monday');
  const [classInfo, setClassInfo] = useState(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState(null);
  const [viewMode, setViewMode] = useState('week'); // 'week', 'day', 'list'
  const [filterSession, setFilterSession] = useState('all'); // 'all', 'morning', 'afternoon'
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [activeSidebarItem, setActiveSidebarItem] = useState('ScheduleManagement');

  // Animation refs
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const drawerAnim = useRef(new Animated.Value(-300)).current;
  const backdropFadeAnim = useRef(new Animated.Value(0)).current;

  // Days of the week
  const days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];

  // Time slots for 40-minute periods
  const morningTimeSlots = [
    { period: 1, start: '08:00', end: '08:40', session: 'morning' },
    { period: 2, start: '08:45', end: '09:25', session: 'morning' },
    { period: 3, start: '09:30', end: '10:10', session: 'morning' },
    { period: 4, start: '10:15', end: '10:55', session: 'morning' },
    { period: 5, start: '11:00', end: '11:40', session: 'morning' },
    { period: 6, start: '11:45', end: '12:25', session: 'morning' },
  ];

  const afternoonTimeSlots = [
    { period: 1, start: '12:30', end: '13:10', session: 'afternoon' },
    { period: 2, start: '13:15', end: '13:55', session: 'afternoon' },
    { period: 3, start: '14:00', end: '14:40', session: 'afternoon' },
    { period: 4, start: '14:45', end: '15:25', session: 'afternoon' },
    { period: 5, start: '15:30', end: '16:10', session: 'afternoon' },
    { period: 6, start: '16:15', end: '16:55', session: 'afternoon' },
  ];

  // Combined time slots for display
  const allTimeSlots = [...morningTimeSlots, ...afternoonTimeSlots];

  useEffect(() => {
    // Hide header in this screen as we're using our own
    navigation.setOptions({
      headerShown: false,
    });

    // Set today's day of week for initial view
    const today = new Date();
    const dayOfWeek = days[today.getDay() === 0 ? 6 : today.getDay() - 1]; // Convert Sunday (0) to 6
    setSelectedDay(dayOfWeek);

    // Start fade-in animation
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 1000,
      useNativeDriver: true,
    }).start();

    fetchStudentClass();
  }, [navigation]);

  useEffect(() => {
    if (classInfo) {
      fetchSchedule();
    }
  }, [classInfo]);

  // Toggle drawer function
  const toggleDrawer = () => {
    if (drawerOpen) {
      // Close drawer
      Animated.parallel([
        Animated.timing(drawerAnim, {
          toValue: -300,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(backdropFadeAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        })
      ]).start(() => setDrawerOpen(false));
    } else {
      // Open drawer
      setDrawerOpen(true);
      Animated.parallel([
        Animated.timing(drawerAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(backdropFadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        })
      ]).start();
    }
  };

  const fetchStudentClass = async () => {
    try {
      setLoading(true);
      setError(null);

      const studentsRef = collection(db, 'students');
      const studentDoc = await getDocs(query(studentsRef, where('userId', '==', user.uid)));

      if (!studentDoc.empty) {
        const studentData = studentDoc.docs[0].data();
        const classesRef = collection(db, 'classes');
        const classDoc = await getDocs(query(classesRef, where('id', '==', studentData.classId)));

        if (!classDoc.empty) {
          setClassInfo({
            ...classDoc.docs[0].data(),
            studentSection: studentData.section
          });
        } else {
          setError('Class information not found');
        }
      } else {
        setError('Student information not found');
      }

      setLoading(false);
    } catch (error) {
      console.error('Error fetching student class:', error);
      setError('Failed to load class information');
      setLoading(false);
    }
  };

  const fetchSchedule = async () => {
    try {
      setLoading(true);
      setError(null);

      // Create a query to get all timetable entries for this class and section
      const timetablesRef = collection(db, 'timetables');
      const q = query(
        timetablesRef,
        where('classId', '==', classInfo.id),
        where('sectionName', '==', classInfo.studentSection),
        where('published', '==', true)
      );

      // Set up real-time listener
      const unsubscribe = onSnapshot(q, (snapshot) => {
        const scheduleData = [];
        snapshot.forEach((doc) => {
          scheduleData.push({ id: doc.id, ...doc.data() });
        });

        setSchedule(scheduleData);
        setLoading(false);
        setRefreshing(false);
      }, (error) => {
        console.error('Error fetching schedule:', error);
        setError('Failed to load schedule. Please try again.');
        setLoading(false);
        setRefreshing(false);
      });

      // Return unsubscribe function for cleanup
      return unsubscribe;
    } catch (error) {
      console.error('Error setting up schedule listener:', error);
      setError('Failed to load schedule. Please try again.');
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Handle refresh
  const onRefresh = async () => {
    setRefreshing(true);
    await fetchSchedule();
  };

  // Get schedule for a specific day
  const getDaySchedule = (day) => {
    return schedule.filter(item => item.day === day)
      .sort((a, b) => {
        // First sort by session (morning first)
        if (a.session !== b.session) {
          return a.session === 'morning' ? -1 : 1;
        }
        // Then sort by start time
        return a.startTime.localeCompare(b.startTime);
      });
  };

  // Get schedule for a specific day and session
  const getSessionSchedule = (day, session) => {
    if (session === 'all') {
      return getDaySchedule(day);
    }
    return schedule.filter(item =>
      item.day === day &&
      item.session === session
    ).sort((a, b) => a.startTime.localeCompare(b.startTime));
  };

  // Get current period
  const getCurrentPeriod = () => {
    const now = new Date();
    const currentDay = now.getDay();
    const daysMap = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    const currentDayName = daysMap[currentDay];
    const currentTime = now.toLocaleTimeString('en-US', { hour12: false });

    return schedule.find(item =>
      item.day === currentDayName &&
      item.startTime <= currentTime &&
      item.endTime >= currentTime
    );
  };

  // Get next period
  const getNextPeriod = () => {
    const now = new Date();
    const currentDay = now.getDay();
    const daysMap = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    const currentDayName = daysMap[currentDay];
    const currentTime = now.toLocaleTimeString('en-US', { hour12: false });

    // Get all periods for today that start after current time
    const todayUpcoming = schedule
      .filter(item =>
        item.day === currentDayName &&
        item.startTime > currentTime
      )
      .sort((a, b) => a.startTime.localeCompare(b.startTime));

    // Return the first upcoming period if available
    if (todayUpcoming.length > 0) {
      return todayUpcoming[0];
    }

    // If no more periods today, find the first period of the next day
    const nextDayIndex = (currentDay + 1) % 7;
    const nextDayName = daysMap[nextDayIndex];

    const nextDayPeriods = schedule
      .filter(item => item.day === nextDayName)
      .sort((a, b) => a.startTime.localeCompare(b.startTime));

    return nextDayPeriods.length > 0 ? nextDayPeriods[0] : null;
  };

  // Format time for display
  const formatTime = (time) => {
    if (!time) return '';
    const [hours, minutes] = time.split(':');
    const date = new Date();
    date.setHours(parseInt(hours), parseInt(minutes));
    return date.toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit' });
  };

  // Toggle view mode
  const toggleViewMode = (mode) => {
    setViewMode(mode);
  };

  // Toggle session filter
  const toggleSessionFilter = (session) => {
    setFilterSession(session);
  };

  // Get subject name
  const getSubjectName = (subjectId) => {
    // This would ideally fetch from a subjects collection
    // For now, just return the ID as placeholder
    return subjectId || 'Unknown Subject';
  };

  // Get teacher name
  const getTeacherName = (teacherId) => {
    // This would ideally fetch from a teachers collection
    // For now, just return the ID as placeholder
    return teacherId || 'Unknown Teacher';
  };

  // Render loading state
  const renderLoading = () => (
    <View style={styles.loadingContainer}>
      <ActivityIndicator size="large" color={'#1976d2'} />
      <Text style={styles.loadingText}>{translate('common.loading') || "Loading..."}</Text>
    </View>
  );

  // Render error state
  const renderError = () => (
    <View style={styles.errorContainer}>
      <IconButton icon="alert-circle" size={50} color={'#B00020'} />
      <Text style={styles.errorText}>{error}</Text>
      <TouchableOpacity style={styles.retryButton} onPress={fetchSchedule}>
        <Text style={styles.retryButtonText}>{translate('common.retry') || "Retry"}</Text>
      </TouchableOpacity>
    </View>
  );

  // Render empty state
  const renderEmpty = () => (
    <View style={styles.emptyContainer}>
      <IconButton icon="calendar-blank" size={50} color={'#9e9e9e'} />
      <Text style={styles.emptyText}>{translate('schedule.noSchedule') || "No schedule found"}</Text>
      <Text style={styles.emptySubtext}>{translate('schedule.checkLater') || "Please check back later"}</Text>
    </View>
  );

  return (
    <SafeAreaView style={styles.safeArea}>
      <StatusBar backgroundColor={'#1976d2'} barStyle="light-content" />

      {/* Student Sidebar */}
      <StudentSidebar
        visible={drawerOpen}
        onClose={toggleDrawer}
        navigation={navigation}
        activeSidebarItem={activeSidebarItem}
        setActiveSidebarItem={setActiveSidebarItem}
        drawerAnim={drawerAnim}
      />

      {/* Backdrop */}
      <SidebarBackdrop
        visible={drawerOpen}
        onPress={toggleDrawer}
        fadeAnim={backdropFadeAnim}
      />

      {/* Student App Header */}
      <StudentAppHeader
        title={translate('schedule.title') || "Class Schedule"}
        onMenuPress={toggleDrawer}
      />

      {/* Main Content */}
      <Animated.View
        style={[
          styles.content,
          { opacity: fadeAnim }
        ]}
      >
        {/* Class Info Card */}
        {classInfo && (
          <Animatable.View animation="fadeIn" duration={500}>
            <Card style={styles.classCard}>
              <Card.Content>
                <View style={styles.classInfoHeader}>
                  <View>
                    <Title style={styles.classTitle}>{classInfo.name}</Title>
                    <Text style={styles.sectionText}>Section {classInfo.studentSection}</Text>
                  </View>
                  <IconButton icon="school" size={30} color={'#1976d2'} />
                </View>
                {classInfo.teacherName && (
                  <Text style={styles.teacherText}>Class Teacher: {classInfo.teacherName}</Text>
                )}
              </Card.Content>
            </Card>
          </Animatable.View>
        )}

        {/* View Mode Selector */}
        <Animatable.View animation="fadeIn" duration={500} delay={100}>
          <View style={styles.viewModeContainer}>
            <SegmentedButtons
              value={viewMode}
              onValueChange={toggleViewMode}
              buttons={[
                { value: 'week', icon: 'calendar-week', label: translate('schedule.weekly') || 'Weekly' },
                { value: 'day', icon: 'calendar-today', label: translate('schedule.daily') || 'Daily' },
                { value: 'list', icon: 'format-list-bulleted', label: translate('schedule.list') || 'List' },
              ]}
              style={styles.viewToggle}
            />
          </View>
        </Animatable.View>

        {/* Day Selector (for day view) */}
        {viewMode === 'day' && (
          <Animatable.View animation="fadeIn" duration={500} delay={150}>
            <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.daySelector}>
              {days.map(day => (
                <Chip
                  key={day}
                  selected={selectedDay === day}
                  onPress={() => setSelectedDay(day)}
                  style={styles.dayChip}
                  selectedColor={'#1976d2'}
                >
                  {day}
                </Chip>
              ))}
            </ScrollView>
          </Animatable.View>
        )}

        {/* Session Filter (for day view) */}
        {viewMode === 'day' && (
          <Animatable.View animation="fadeIn" duration={500} delay={200}>
            <View style={styles.sessionFilterContainer}>
              <SegmentedButtons
                value={filterSession}
                onValueChange={toggleSessionFilter}
                buttons={[
                  { value: 'all', label: translate('schedule.allSessions') || 'All Sessions' },
                  { value: 'morning', icon: 'weather-sunny', label: translate('schedule.morning') || 'Morning' },
                  { value: 'afternoon', icon: 'weather-night', label: translate('schedule.afternoon') || 'Afternoon' },
                ]}
                style={styles.sessionFilter}
              />
            </View>
          </Animatable.View>
        )}

        {/* Main Schedule Content */}
        <ScrollView
          style={styles.scrollView}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={['#1976d2']}
              tintColor={'#1976d2'}
            />
          }
        >
          {loading ? (
            renderLoading()
          ) : error ? (
            renderError()
          ) : schedule.length === 0 ? (
            renderEmpty()
          ) : viewMode === 'week' ? (
            // Weekly View
            <Animatable.View animation="fadeIn" duration={500} delay={250}>
              {/* Current Status Card */}
              <Card style={styles.currentCard}>
                <Card.Content>
                  <View style={styles.currentHeader}>
                    <Title style={styles.currentTitle}>{translate('schedule.currentStatus') || 'Current Status'}</Title>
                    <IconButton icon="clock" size={24} color={'#1976d2'} />
                  </View>

                  {getCurrentPeriod() ? (
                    <Surface style={styles.currentPeriodSurface}>
                      <View style={styles.currentPeriodHeader}>
                        <Badge style={styles.currentBadge}>Now</Badge>
                        <Text style={styles.currentTime}>
                          {formatTime(getCurrentPeriod().startTime)} - {formatTime(getCurrentPeriod().endTime)}
                        </Text>
                      </View>
                      <Text style={styles.currentSubject}>
                        {getSubjectName(getCurrentPeriod().subjectId)}
                      </Text>
                      <Text style={styles.currentTeacher}>
                        Teacher: {getTeacherName(getCurrentPeriod().teacherId)}
                      </Text>
                      <Text style={styles.currentRoom}>
                        Room: {getCurrentPeriod().roomNumber}
                      </Text>
                      <Chip
                        mode="outlined"
                        style={{
                          marginTop: 8,
                          backgroundColor: getCurrentPeriod().session === 'morning' ? '#E3F2FD' : '#EDE7F6',
                        }}
                        icon={getCurrentPeriod().session === 'morning' ? 'weather-sunny' : 'weather-night'}
                      >
                        {getCurrentPeriod().session === 'morning' ? 'Morning Session' : 'Afternoon Session'}
                      </Chip>
                    </Surface>
                  ) : (
                    <Surface style={styles.noCurrentPeriod}>
                      <IconButton icon="clock-outline" size={30} color={'#9e9e9e'} />
                      <Text style={styles.noCurrentText}>{translate('schedule.noOngoingClass') || 'No ongoing class'}</Text>
                    </Surface>
                  )}

                  {getNextPeriod() && (
                    <>
                      <View style={styles.nextHeader}>
                        <Title style={styles.nextTitle}>{translate('schedule.nextPeriod') || 'Next Period'}</Title>
                        <IconButton icon="clock-fast" size={24} color="#FF9800" />
                      </View>

                      <Surface style={styles.nextPeriodSurface}>
                        <View style={styles.nextPeriodHeader}>
                          <Badge style={styles.nextBadge}>Next</Badge>
                          <Text style={styles.nextTime}>
                            {formatTime(getNextPeriod().startTime)} - {formatTime(getNextPeriod().endTime)}
                          </Text>
                          <Text style={styles.nextDay}>
                            {getNextPeriod().day !== getCurrentPeriod()?.day ? `(${getNextPeriod().day})` : ''}
                          </Text>
                        </View>
                        <Text style={styles.nextSubject}>
                          {getSubjectName(getNextPeriod().subjectId)}
                        </Text>
                        <Text style={styles.nextTeacher}>
                          Teacher: {getTeacherName(getNextPeriod().teacherId)}
                        </Text>
                        <Text style={styles.nextRoom}>
                          Room: {getNextPeriod().roomNumber}
                        </Text>
                        <Chip
                          mode="outlined"
                          style={{
                            marginTop: 8,
                            backgroundColor: getNextPeriod().session === 'morning' ? '#E3F2FD' : '#EDE7F6',
                          }}
                          icon={getNextPeriod().session === 'morning' ? 'weather-sunny' : 'weather-night'}
                        >
                          {getNextPeriod().session === 'morning' ? 'Morning Session' : 'Afternoon Session'}
                        </Chip>
                      </Surface>
                    </>
                  )}
                </Card.Content>
              </Card>

              {/* Morning Session */}
              <Card style={styles.sessionCard}>
                <Card.Content>
                  <View style={styles.sessionHeader}>
                    <Title style={styles.sessionTitle}>
                      {translate('schedule.morningSession') || 'Morning Session (8:00 - 12:30)'}
                    </Title>
                    <IconButton icon="weather-sunny" size={24} color="#2196F3" />
                  </View>

                  <Surface style={styles.timetableSurface}>
                    <ScrollView horizontal style={styles.timetableContainer}>
                      <DataTable style={styles.timetableTable}>
                        <DataTable.Header>
                          <DataTable.Title style={styles.periodColumn}>Period</DataTable.Title>
                          {days.map((day) => (
                            <DataTable.Title key={day} style={styles.dayColumn}>{day}</DataTable.Title>
                          ))}
                        </DataTable.Header>

                        {/* Render morning periods as rows */}
                        {morningTimeSlots.map((timeSlot) => (
                          <DataTable.Row key={timeSlot.period + '-morning'}>
                            <DataTable.Cell style={styles.periodColumn}>
                              <Text style={styles.periodText}>Period {timeSlot.period}</Text>
                              <Text style={styles.timeText}>{timeSlot.start} - {timeSlot.end}</Text>
                            </DataTable.Cell>

                            {/* Render cells for each day */}
                            {days.map((day) => {
                              // Find schedule entry for this day and period
                              const entry = schedule.find(e =>
                                e.day === day &&
                                e.startTime === timeSlot.start &&
                                e.endTime === timeSlot.end &&
                                e.session === 'morning'
                              );

                              return (
                                <DataTable.Cell key={day} style={styles.dayColumn}>
                                  {entry ? (
                                    <View style={[styles.scheduleCell, { borderLeftColor: '#2196F3' }]}>
                                      <Text style={styles.subjectText}>
                                        {getSubjectName(entry.subjectId)}
                                      </Text>
                                      <Text style={styles.teacherText}>
                                        {getTeacherName(entry.teacherId)}
                                      </Text>
                                      <Text style={styles.roomText}>Room: {entry.roomNumber}</Text>
                                    </View>
                                  ) : (
                                    <View style={styles.emptyCell}>
                                      <Text style={styles.emptyCellText}>Free</Text>
                                    </View>
                                  )}
                                </DataTable.Cell>
                              );
                            })}
                          </DataTable.Row>
                        ))}
                      </DataTable>
                    </ScrollView>
                  </Surface>
                </Card.Content>
              </Card>

              {/* Afternoon Session */}
              <Card style={styles.sessionCard}>
                <Card.Content>
                  <View style={styles.sessionHeader}>
                    <Title style={styles.sessionTitle}>
                      {translate('schedule.afternoonSession') || 'Afternoon Session (12:30 - 5:00)'}
                    </Title>
                    <IconButton icon="weather-night" size={24} color="#673AB7" />
                  </View>

                  <Surface style={styles.timetableSurface}>
                    <ScrollView horizontal style={styles.timetableContainer}>
                      <DataTable style={styles.timetableTable}>
                        <DataTable.Header>
                          <DataTable.Title style={styles.periodColumn}>Period</DataTable.Title>
                          {days.map((day) => (
                            <DataTable.Title key={day} style={styles.dayColumn}>{day}</DataTable.Title>
                          ))}
                        </DataTable.Header>

                        {/* Render afternoon periods as rows */}
                        {afternoonTimeSlots.map((timeSlot) => (
                          <DataTable.Row key={timeSlot.period + '-afternoon'}>
                            <DataTable.Cell style={styles.periodColumn}>
                              <Text style={styles.periodText}>Period {timeSlot.period}</Text>
                              <Text style={styles.timeText}>{timeSlot.start} - {timeSlot.end}</Text>
                            </DataTable.Cell>

                            {/* Render cells for each day */}
                            {days.map((day) => {
                              // Find schedule entry for this day and period
                              const entry = schedule.find(e =>
                                e.day === day &&
                                e.startTime === timeSlot.start &&
                                e.endTime === timeSlot.end &&
                                e.session === 'afternoon'
                              );

                              return (
                                <DataTable.Cell key={day} style={styles.dayColumn}>
                                  {entry ? (
                                    <View style={[styles.scheduleCell, { borderLeftColor: '#673AB7' }]}>
                                      <Text style={styles.subjectText}>
                                        {getSubjectName(entry.subjectId)}
                                      </Text>
                                      <Text style={styles.teacherText}>
                                        {getTeacherName(entry.teacherId)}
                                      </Text>
                                      <Text style={styles.roomText}>Room: {entry.roomNumber}</Text>
                                    </View>
                                  ) : (
                                    <View style={styles.emptyCell}>
                                      <Text style={styles.emptyCellText}>Free</Text>
                                    </View>
                                  )}
                                </DataTable.Cell>
                              );
                            })}
                          </DataTable.Row>
                        ))}
                      </DataTable>
                    </ScrollView>
                  </Surface>
                </Card.Content>
              </Card>
            </Animatable.View>
          ) : viewMode === 'day' ? (
            // Daily View
            <Animatable.View animation="fadeIn" duration={500} delay={250}>
              <Card style={styles.dayCard}>
                <Card.Content>
                  <Title style={styles.dayTitle}>{selectedDay}</Title>

                  {getSessionSchedule(selectedDay, filterSession).length === 0 ? (
                    <View style={styles.noDaySchedule}>
                      <IconButton icon="calendar-blank" size={40} color={'#9e9e9e'} />
                      <Text style={styles.noDayScheduleText}>
                        {translate('schedule.noClassesForDay') || `No classes scheduled for ${selectedDay}`}
                      </Text>
                    </View>
                  ) : (
                    <>
                      {/* Morning Classes */}
                      {(filterSession === 'all' || filterSession === 'morning') &&
                        getSessionSchedule(selectedDay, 'morning').length > 0 && (
                        <View style={styles.sessionSection}>
                          <View style={styles.sessionDayHeader}>
                            <Text style={[styles.sessionDayTitle, { color: '#2196F3' }]}>
                              {translate('schedule.morningSession') || 'Morning Session'}
                            </Text>
                            <IconButton icon="weather-sunny" size={20} color="#2196F3" />
                          </View>

                          {getSessionSchedule(selectedDay, 'morning').map(item => (
                            <Surface key={item.id} style={[styles.classItem, { borderLeftColor: '#2196F3' }]}>
                              <View style={styles.classItemHeader}>
                                <Text style={styles.classItemTime}>
                                  {formatTime(item.startTime)} - {formatTime(item.endTime)}
                                </Text>
                                <Chip mode="outlined" style={styles.periodChip}>
                                  Period {item.periodNumber}
                                </Chip>
                              </View>

                              <Text style={styles.classItemSubject}>
                                {getSubjectName(item.subjectId)}
                              </Text>
                              <Text style={styles.classItemTeacher}>
                                Teacher: {getTeacherName(item.teacherId)}
                              </Text>
                              <Text style={styles.classItemRoom}>
                                Room: {item.roomNumber}
                              </Text>
                            </Surface>
                          ))}
                        </View>
                      )}

                      {/* Afternoon Classes */}
                      {(filterSession === 'all' || filterSession === 'afternoon') &&
                        getSessionSchedule(selectedDay, 'afternoon').length > 0 && (
                        <View style={styles.sessionSection}>
                          <View style={styles.sessionDayHeader}>
                            <Text style={[styles.sessionDayTitle, { color: '#673AB7' }]}>
                              {translate('schedule.afternoonSession') || 'Afternoon Session'}
                            </Text>
                            <IconButton icon="weather-night" size={20} color="#673AB7" />
                          </View>

                          {getSessionSchedule(selectedDay, 'afternoon').map(item => (
                            <Surface key={item.id} style={[styles.classItem, { borderLeftColor: '#673AB7' }]}>
                              <View style={styles.classItemHeader}>
                                <Text style={styles.classItemTime}>
                                  {formatTime(item.startTime)} - {formatTime(item.endTime)}
                                </Text>
                                <Chip mode="outlined" style={styles.periodChip}>
                                  Period {item.periodNumber}
                                </Chip>
                              </View>

                              <Text style={styles.classItemSubject}>
                                {getSubjectName(item.subjectId)}
                              </Text>
                              <Text style={styles.classItemTeacher}>
                                Teacher: {getTeacherName(item.teacherId)}
                              </Text>
                              <Text style={styles.classItemRoom}>
                                Room: {item.roomNumber}
                              </Text>
                            </Surface>
                          ))}
                        </View>
                      )}
                    </>
                  )}
                </Card.Content>
              </Card>
            </Animatable.View>
          ) : (
            // List View
            <Animatable.View animation="fadeIn" duration={500} delay={250}>
              <Card style={styles.listCard}>
                <Card.Content>
                  <Title style={styles.listTitle}>
                    {translate('schedule.allClasses') || 'All Classes'}
                  </Title>

                  <ScrollableTable
                    header={
                      <DataTable.Header style={tableStyles.tableHeader}>
                        <DataTable.Title style={{ width: 100 }}>Day</DataTable.Title>
                        <DataTable.Title style={{ width: 150 }}>Time</DataTable.Title>
                        <DataTable.Title style={{ width: 180 }}>Subject</DataTable.Title>
                        <DataTable.Title style={{ width: 100 }}>Room</DataTable.Title>
                      </DataTable.Header>
                    }
                  >
                    {schedule.sort((a, b) => {
                      // First sort by day
                      const dayOrder = days.indexOf(a.day) - days.indexOf(b.day);
                      if (dayOrder !== 0) return dayOrder;

                      // Then sort by session
                      if (a.session !== b.session) {
                        return a.session === 'morning' ? -1 : 1;
                      }

                      // Then sort by start time
                      return a.startTime.localeCompare(b.startTime);
                    }).map(item => (
                      <DataTable.Row key={item.id} style={tableStyles.row}>
                        <DataTable.Cell style={{ width: 100 }}>{item.day}</DataTable.Cell>
                        <DataTable.Cell style={{ width: 150 }}>
                          <View>
                            <Text>{formatTime(item.startTime)} - {formatTime(item.endTime)}</Text>
                            <Chip
                              mode="outlined"
                              style={{
                                height: 20,
                                backgroundColor: item.session === 'morning' ? '#E3F2FD' : '#EDE7F6',
                              }}
                              textStyle={{ fontSize: 10 }}
                            >
                              {item.session === 'morning' ? 'Morning' : 'Afternoon'}
                            </Chip>
                          </View>
                        </DataTable.Cell>
                        <DataTable.Cell style={{ width: 180 }}>{getSubjectName(item.subjectId)}</DataTable.Cell>
                        <DataTable.Cell style={{ width: 100 }}>{item.roomNumber}</DataTable.Cell>
                      </DataTable.Row>
                    ))}
                  </ScrollableTable>
                </Card.Content>
              </Card>
            </Animatable.View>
          )}
        </ScrollView>
      </Animated.View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  // Class Info Card
  classCard: {
    marginBottom: 16,
    borderRadius: 8,
    elevation: 4,
  },
  classInfoHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  classTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  sectionText: {
    fontSize: 14,
    color: '#666',
  },
  teacherText: {
    marginTop: 8,
    fontSize: 14,
    color: '#666',
  },
  // View Mode Selector
  viewModeContainer: {
    marginBottom: 16,
  },
  viewToggle: {
    marginBottom: 8,
  },
  // Day Selector
  daySelector: {
    marginBottom: 16,
  },
  dayChip: {
    marginRight: 8,
  },
  // Session Filter
  sessionFilterContainer: {
    marginBottom: 16,
  },
  sessionFilter: {
    marginBottom: 8,
  },
  // Scroll View
  scrollView: {
    flex: 1,
  },
  // Current Status Card
  currentCard: {
    marginBottom: 16,
    borderRadius: 8,
    elevation: 4,
  },
  currentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  currentTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  currentPeriodSurface: {
    padding: 16,
    borderRadius: 8,
    elevation: 2,
    backgroundColor: '#f9f9f9',
  },
  currentPeriodHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  currentBadge: {
    backgroundColor: '#4CAF50',
    color: 'white',
    marginRight: 8,
  },
  currentTime: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  currentSubject: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  currentTeacher: {
    fontSize: 14,
    marginBottom: 2,
  },
  currentRoom: {
    fontSize: 14,
    color: '#666',
  },
  noCurrentPeriod: {
    padding: 16,
    borderRadius: 8,
    elevation: 2,
    backgroundColor: '#f9f9f9',
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 100,
  },
  noCurrentText: {
    fontSize: 16,
    color: '#999',
    marginTop: 8,
  },
  // Next Period
  nextHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 24,
    marginBottom: 16,
  },
  nextTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  nextPeriodSurface: {
    padding: 16,
    borderRadius: 8,
    elevation: 2,
    backgroundColor: '#FFF8E1',
  },
  nextPeriodHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  nextBadge: {
    backgroundColor: '#FF9800',
    color: 'white',
    marginRight: 8,
  },
  nextTime: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  nextDay: {
    fontSize: 14,
    color: '#666',
    marginLeft: 8,
  },
  nextSubject: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  nextTeacher: {
    fontSize: 14,
    marginBottom: 2,
  },
  nextRoom: {
    fontSize: 14,
    color: '#666',
  },
  // Session Cards
  sessionCard: {
    marginBottom: 16,
    borderRadius: 8,
    elevation: 4,
  },
  sessionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sessionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  // Timetable
  timetableSurface: {
    borderRadius: 8,
    elevation: 2,
  },
  timetableContainer: {
    marginBottom: 16,
  },
  timetableTable: {
    backgroundColor: '#fff',
  },
  periodColumn: {
    width: 100,
    backgroundColor: '#f0f0f0',
  },
  dayColumn: {
    width: 180,
    padding: 5,
  },
  periodText: {
    fontWeight: 'bold',
    fontSize: 14,
  },
  timeText: {
    fontSize: 12,
    color: '#666',
  },
  scheduleCell: {
    padding: 10,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    minHeight: 100,
    elevation: 2,
    borderLeftWidth: 4,
  },
  subjectText: {
    fontWeight: 'bold',
    fontSize: 14,
    marginBottom: 2,
  },
  teacherText: {
    fontSize: 14,
    color: '#333',
    marginBottom: 2,
  },
  roomText: {
    fontSize: 12,
    color: '#666',
    marginBottom: 2,
  },
  emptyCell: {
    padding: 10,
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
    minHeight: 100,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyCellText: {
    color: '#999',
    fontStyle: 'italic',
  },
  // Daily View
  dayCard: {
    marginBottom: 16,
    borderRadius: 8,
    elevation: 4,
  },
  dayTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
  },
  noDaySchedule: {
    padding: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  noDayScheduleText: {
    marginTop: 10,
    fontSize: 16,
    color: '#999',
    textAlign: 'center',
  },
  sessionSection: {
    marginBottom: 16,
  },
  sessionDayHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  sessionDayTitle: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  classItem: {
    padding: 16,
    marginBottom: 12,
    borderRadius: 8,
    elevation: 2,
    borderLeftWidth: 4,
  },
  classItemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  classItemTime: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  periodChip: {
    height: 24,
  },
  classItemSubject: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  classItemTeacher: {
    fontSize: 14,
    marginBottom: 4,
  },
  classItemRoom: {
    fontSize: 12,
    color: '#666',
  },
  // List View
  listCard: {
    marginBottom: 16,
    borderRadius: 8,
    elevation: 4,
  },
  listTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  // Loading, Error, Empty States
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    minHeight: 200,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    minHeight: 200,
  },
  errorText: {
    marginTop: 10,
    fontSize: 16,
    color: '#d32f2f',
    textAlign: 'center',
  },
  retryButton: {
    marginTop: 20,
    padding: 10,
    backgroundColor: '#2196F3',
    borderRadius: 5,
  },
  retryButtonText: {
    color: 'white',
    fontWeight: 'bold',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    minHeight: 200,
  },
  emptyText: {
    marginTop: 10,
    fontSize: 18,
    fontWeight: 'bold',
    color: '#666',
  },
  emptySubtext: {
    marginTop: 5,
    fontSize: 14,
    color: '#999',
    textAlign: 'center',
  },
});

export default ScheduleManagement;
