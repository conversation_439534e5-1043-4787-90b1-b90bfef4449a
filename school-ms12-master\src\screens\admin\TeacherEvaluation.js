import React, { useState, useEffect } from 'react';
import { View, ScrollView, StyleSheet } from 'react-native';
import {
  Card,
  Title,
  Text,
  Portal,
  Modal,
  ActivityIndicator,
  List,
  IconButton,
  Chip,
} from 'react-native-paper';
import { db } from '../../config/firebase';
import {
  collection,
  addDoc,
  query,
  getDocs,
  doc,
  updateDoc,
  deleteDoc,
  where,
  orderBy,
} from 'firebase/firestore';
import CustomButton from '../../components/common/CustomButton';
import CustomInput from '../../components/common/CustomInput';

const TeacherEvaluation = ({ route, navigation }) => {
  const [evaluations, setEvaluations] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedEvaluation, setSelectedEvaluation] = useState(null);
  const [error, setError] = useState(null);

  // Validate route params
  const teacherId = route?.params?.teacherId;

  const defaultFormData = {
    date: new Date().toISOString().split('T')[0],
    evaluator: '',
    teachingSkills: '',
    classroomManagement: '',
    studentEngagement: '',
    professionalism: '',
    curriculum: '',
    strengths: '',
    areasForImprovement: '',
    recommendations: '',
    overallRating: '',
    status: 'pending',
    comments: '',
  };

  const [formData, setFormData] = useState(defaultFormData);

  // Redirect if teacherId is missing
  useEffect(() => {
    if (!teacherId) {
      console.error('TeacherEvaluation: teacherId is missing in route params');
      setError('Teacher ID is missing. Please select a teacher first.');
      // Optionally navigate back to teacher list
      // navigation.goBack();
      return;
    }

    fetchEvaluations();
  }, [teacherId]);

  const fetchEvaluations = async () => {
    if (!teacherId) return;

    try {
      setLoading(true);
      setError(null);

      const evaluationsRef = collection(db, 'evaluations');
      const q = query(
        evaluationsRef,
        where('teacherId', '==', teacherId),
        orderBy('date', 'desc')
      );
      const querySnapshot = await getDocs(q);

      const evaluationsData = [];
      querySnapshot.forEach((doc) => {
        evaluationsData.push({ id: doc.id, ...doc.data() });
      });

      setEvaluations(evaluationsData);
    } catch (error) {
      console.error('Error fetching evaluations:', error);
      setError('Failed to load evaluations. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleAddEvaluation = async () => {
    if (!teacherId) {
      setError('Cannot add evaluation: Teacher ID is missing');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const evaluationsRef = collection(db, 'evaluations');
      await addDoc(evaluationsRef, {
        ...formData,
        teacherId: teacherId,
        createdAt: new Date().toISOString(),
      });

      setModalVisible(false);
      resetForm();
      fetchEvaluations();
    } catch (error) {
      console.error('Error adding evaluation:', error);
      setError('Failed to add evaluation. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateEvaluation = async () => {
    try {
      setLoading(true);
      const evaluationRef = doc(db, 'evaluations', selectedEvaluation.id);
      await updateDoc(evaluationRef, {
        ...formData,
        updatedAt: new Date().toISOString(),
      });

      setModalVisible(false);
      resetForm();
      fetchEvaluations();
    } catch (error) {
      console.error('Error updating evaluation:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteEvaluation = async (evaluationId) => {
    try {
      await deleteDoc(doc(db, 'evaluations', evaluationId));
      fetchEvaluations();
    } catch (error) {
      console.error('Error deleting evaluation:', error);
    }
  };

  const resetForm = () => {
    setSelectedEvaluation(null);
    setFormData(defaultFormData);
  };

  const renderEvaluationForm = () => (
    <ScrollView>
      <CustomInput
        label="Date"
        value={formData.date}
        onChangeText={(text) => setFormData({ ...formData, date: text })}
      />

      <CustomInput
        label="Evaluator"
        value={formData.evaluator}
        onChangeText={(text) => setFormData({ ...formData, evaluator: text })}
      />

      <Title style={styles.sectionTitle}>Performance Metrics</Title>

      <CustomInput
        label="Teaching Skills"
        value={formData.teachingSkills}
        onChangeText={(text) => setFormData({ ...formData, teachingSkills: text })}
        multiline
      />

      <CustomInput
        label="Classroom Management"
        value={formData.classroomManagement}
        onChangeText={(text) => setFormData({ ...formData, classroomManagement: text })}
        multiline
      />

      <CustomInput
        label="Student Engagement"
        value={formData.studentEngagement}
        onChangeText={(text) => setFormData({ ...formData, studentEngagement: text })}
        multiline
      />

      <CustomInput
        label="Professionalism"
        value={formData.professionalism}
        onChangeText={(text) => setFormData({ ...formData, professionalism: text })}
        multiline
      />

      <CustomInput
        label="Curriculum Implementation"
        value={formData.curriculum}
        onChangeText={(text) => setFormData({ ...formData, curriculum: text })}
        multiline
      />

      <Title style={styles.sectionTitle}>Feedback</Title>

      <CustomInput
        label="Strengths"
        value={formData.strengths}
        onChangeText={(text) => setFormData({ ...formData, strengths: text })}
        multiline
      />

      <CustomInput
        label="Areas for Improvement"
        value={formData.areasForImprovement}
        onChangeText={(text) => setFormData({ ...formData, areasForImprovement: text })}
        multiline
      />

      <CustomInput
        label="Recommendations"
        value={formData.recommendations}
        onChangeText={(text) => setFormData({ ...formData, recommendations: text })}
        multiline
      />

      <CustomInput
        label="Overall Rating (1-5)"
        value={formData.overallRating}
        onChangeText={(text) => setFormData({ ...formData, overallRating: text })}
        keyboardType="numeric"
      />

      <List.Section title="Status">
        {['pending', 'completed', 'in review'].map((status) => (
          <List.Item
            key={status}
            title={status.charAt(0).toUpperCase() + status.slice(1)}
            onPress={() => setFormData({ ...formData, status })}
            style={formData.status === status ? styles.selectedItem : null}
            left={props => <List.Icon {...props} icon="checkbox-marked-circle" />}
          />
        ))}
      </List.Section>

      <CustomInput
        label="Additional Comments"
        value={formData.comments}
        onChangeText={(text) => setFormData({ ...formData, comments: text })}
        multiline
        numberOfLines={4}
      />
    </ScrollView>
  );

  const renderEvaluationCard = (evaluation) => (
    <Card key={evaluation.id} style={styles.evaluationCard}>
      <Card.Content>
        <View style={styles.cardHeader}>
          <View>
            <Title>{evaluation.date}</Title>
            <Text>Evaluator: {evaluation.evaluator}</Text>
          </View>
          <Chip mode="outlined">{evaluation.status}</Chip>
        </View>

        <View style={styles.ratingSection}>
          <Text style={styles.ratingText}>Overall Rating: {evaluation.overallRating}/5</Text>
        </View>

        <List.Accordion title="Performance Details">
          <List.Item title="Teaching Skills" description={evaluation.teachingSkills} />
          <List.Item title="Classroom Management" description={evaluation.classroomManagement} />
          <List.Item title="Student Engagement" description={evaluation.studentEngagement} />
          <List.Item title="Professionalism" description={evaluation.professionalism} />
          <List.Item title="Curriculum Implementation" description={evaluation.curriculum} />
        </List.Accordion>

        <List.Accordion title="Feedback">
          <List.Item title="Strengths" description={evaluation.strengths} />
          <List.Item title="Areas for Improvement" description={evaluation.areasForImprovement} />
          <List.Item title="Recommendations" description={evaluation.recommendations} />
          <List.Item title="Additional Comments" description={evaluation.comments} />
        </List.Accordion>

        <View style={styles.cardActions}>
          <IconButton
            icon="pencil"
            onPress={() => {
              setSelectedEvaluation(evaluation);
              setFormData(evaluation);
              setModalVisible(true);
            }}
          />
          <IconButton
            icon="delete"
            onPress={() => handleDeleteEvaluation(evaluation.id)}
          />
        </View>
      </Card.Content>
    </Card>
  );

  return (
    <View style={styles.container}>
      <Card style={styles.headerCard}>
        <Card.Content>
          <Title>Teacher Evaluation</Title>
          <Text>Performance assessment and feedback</Text>
        </Card.Content>
      </Card>

      <ScrollView>
        {loading ? (
          <ActivityIndicator style={styles.loader} />
        ) : error ? (
          <Card style={styles.errorCard}>
            <Card.Content>
              <Text style={styles.errorText}>{error}</Text>
              {teacherId && (
                <CustomButton
                  mode="contained"
                  onPress={fetchEvaluations}
                  style={styles.retryButton}
                >
                  Retry
                </CustomButton>
              )}
            </Card.Content>
          </Card>
        ) : evaluations.length === 0 ? (
          <Card style={styles.emptyCard}>
            <Card.Content>
              <Text style={styles.emptyText}>No evaluations found for this teacher.</Text>
            </Card.Content>
          </Card>
        ) : (
          evaluations.map(renderEvaluationCard)
        )}
      </ScrollView>

      <Portal>
        <Modal
          visible={modalVisible}
          onDismiss={() => {
            setModalVisible(false);
            resetForm();
          }}
          contentContainerStyle={styles.modalContent}
        >
          <Title>{selectedEvaluation ? 'Edit Evaluation' : 'New Evaluation'}</Title>

          {renderEvaluationForm()}

          <View style={styles.modalButtons}>
            <CustomButton
              mode="contained"
              onPress={selectedEvaluation ? handleUpdateEvaluation : handleAddEvaluation}
              loading={loading}
            >
              {selectedEvaluation ? 'Update' : 'Submit'}
            </CustomButton>

            <CustomButton
              mode="outlined"
              onPress={() => {
                setModalVisible(false);
                resetForm();
              }}
            >
              Cancel
            </CustomButton>
          </View>
        </Modal>
      </Portal>

      <CustomButton
        mode="contained"
        icon="plus"
        onPress={() => setModalVisible(true)}
        style={styles.addButton}
      >
        New Evaluation
      </CustomButton>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  headerCard: {
    margin: 10,
    elevation: 4,
  },
  evaluationCard: {
    margin: 10,
    elevation: 2,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  ratingSection: {
    backgroundColor: '#f0f0f0',
    padding: 10,
    borderRadius: 5,
    marginVertical: 10,
  },
  ratingText: {
    fontWeight: 'bold',
  },
  cardActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 10,
  },
  loader: {
    marginTop: 20,
  },
  modalContent: {
    backgroundColor: 'white',
    padding: 20,
    margin: 20,
    borderRadius: 10,
    maxHeight: '80%',
  },
  sectionTitle: {
    fontSize: 18,
    marginTop: 20,
    marginBottom: 10,
  },
  selectedItem: {
    backgroundColor: '#e8f4f8',
  },
  modalButtons: {
    marginTop: 20,
  },
  addButton: {
    margin: 16,
  },
  errorCard: {
    margin: 10,
    backgroundColor: '#ffebee',
  },
  errorText: {
    color: '#d32f2f',
    textAlign: 'center',
    marginBottom: 10,
  },
  retryButton: {
    marginTop: 10,
  },
  emptyCard: {
    margin: 10,
    padding: 20,
  },
  emptyText: {
    textAlign: 'center',
    color: '#757575',
  },
});

export default TeacherEvaluation;
