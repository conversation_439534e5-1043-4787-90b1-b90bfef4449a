import React, { useState, useEffect, useRef } from 'react';
import { View, StyleSheet, StatusBar, SafeAreaView, ScrollView, Animated } from 'react-native';
import {
  Card,
  Title,
  Text,
  Button,
  Paragraph,
  DataTable,
  ActivityIndicator,
  Snackbar,
  Portal,
  Dialog,
  TextInput,
  Chip,
  IconButton,
  Menu
} from 'react-native-paper';
import * as Animatable from 'react-native-animatable';
import { useLanguage } from '../../context/LanguageContext';
import { db, auth } from '../../config/firebase';
import {
  collection,
  query,
  where,
  getDocs,
  doc,
  getDoc,
  updateDoc,
  Timestamp,
  addDoc,
  serverTimestamp
} from 'firebase/firestore';
import NotificationService from '../../services/NotificationService';
import AdminService from '../../services/AdminService';
import AdminAppHeader from '../../components/common/AdminAppHeader';
import AdminSidebar from '../../components/common/AdminSidebar';
import SidebarBackdrop from '../../components/common/SidebarBackdrop';



const AdminGradeApproval = ({ navigation, route }) => {
  const { translate } = useLanguage();
  const backdropFadeAnim = useRef(new Animated.Value(0)).current;

  // Sidebar state
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [activeSidebarItem, setActiveSidebarItem] = useState('AdminGradeApproval');

  // Submission data
  const [submissions, setSubmissions] = useState([]);
  const [filteredSubmissions, setFilteredSubmissions] = useState([]);
  const [selectedSubmission, setSelectedSubmission] = useState(null);
  const [submissionDetails, setSubmissionDetails] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [approveDialogVisible, setApproveDialogVisible] = useState(false);
  const [rejectDialogVisible, setRejectDialogVisible] = useState(false);
  const [rejectReason, setRejectReason] = useState('');

  // Filtering options
  const [searchQuery, setSearchQuery] = useState('');
  const [sortOption, setSortOption] = useState('date'); // 'date', 'class', 'subject'
  const [filterMenuVisible, setFilterMenuVisible] = useState(false);

  // Toggle sidebar
  const toggleDrawer = () => {
    const newValue = !drawerOpen;
    setDrawerOpen(newValue);

    // Animate backdrop
    Animated.timing(backdropFadeAnim, {
      toValue: newValue ? 0.5 : 0,
      duration: 300,
      useNativeDriver: true,
    }).start();
  };

  // Filter and sort submissions
  const filterSubmissions = () => {
    let result = [...submissions];

    // Apply search filter if query exists
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      result = result.filter(submission =>
        (submission.className && submission.className.toLowerCase().includes(query)) ||
        (submission.sectionName && submission.sectionName.toLowerCase().includes(query)) ||
        (submission.subject && submission.subject.toLowerCase().includes(query)) ||
        (submission.teacherName && submission.teacherName.toLowerCase().includes(query))
      );
    }

    // Apply sorting
    switch (sortOption) {
      case 'class':
        result.sort((a, b) => {
          const classA = `${a.className || ''}-${a.sectionName || ''}`;
          const classB = `${b.className || ''}-${b.sectionName || ''}`;
          return classA.localeCompare(classB);
        });
        break;
      case 'subject':
        result.sort((a, b) => {
          const subjectA = a.subject || '';
          const subjectB = b.subject || '';
          return subjectA.localeCompare(subjectB);
        });
        break;
      case 'teacher':
        result.sort((a, b) => {
          const teacherA = a.teacherName || '';
          const teacherB = b.teacherName || '';
          return teacherA.localeCompare(teacherB);
        });
        break;
      case 'date':
      default:
        // Already sorted by date in fetchSubmissions
        break;
    }

    setFilteredSubmissions(result);
  };

  // Update filtered submissions when search query or sort option changes
  useEffect(() => {
    filterSubmissions();
  }, [searchQuery, sortOption, submissions]);

  // Fetch submissions on mount
  useEffect(() => {
    fetchSubmissions();
  }, []);

  // Fetch submission details when a submission is selected
  useEffect(() => {
    if (selectedSubmission) {
      fetchSubmissionDetails(selectedSubmission.id);
    }
  }, [selectedSubmission]);

  // Fetch all grade submissions
  const fetchSubmissions = async () => {
    try {
      setLoading(true);

      // Use AdminService to get pending grade submissions
      const pendingSubmissions = await AdminService.getPendingGradeSubmissions();

      const submissionsData = [];

      // Process each submission document
      for (const submission of pendingSubmissions) {
        // Calculate additional metadata for the submission
        const assessmentTypes = submission.assessments
          ? [...new Set(submission.assessments.map(a => a.type || 'assignment'))]
          : [];

        const studentCount = submission.students ? submission.students.length : 0;
        const totalAssessments = submission.assessments ? submission.assessments.length : 0;

        // Get the submission date in a readable format
        const submissionDate = submission.submittedAt
          ? new Date(submission.submittedAt.seconds * 1000)
          : new Date();

        // Format the date
        const formattedDate = submissionDate.toLocaleDateString();
        const formattedTime = submissionDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

        // Add enhanced submission data
        submissionsData.push({
          ...submission,
          metadata: {
            assessmentTypes,
            studentCount,
            totalAssessments,
            formattedDate,
            formattedTime,
            daysAgo: Math.floor((new Date() - submissionDate) / (1000 * 60 * 60 * 24))
          }
        });
      }

      // Sort submissions by date (newest first)
      submissionsData.sort((a, b) => {
        const dateA = a.submittedAt ? a.submittedAt.seconds : 0;
        const dateB = b.submittedAt ? b.submittedAt.seconds : 0;
        return dateB - dateA;
      });

      setSubmissions(submissionsData);
      setFilteredSubmissions(submissionsData);

      // If there's a submission ID in the route params, select it
      if (route.params?.submissionId) {
        const submission = submissionsData.find(s => s.id === route.params.submissionId);
        if (submission) {
          setSelectedSubmission(submission);
        }
      }
    } catch (error) {
      console.error('Error fetching submissions:', error);
      setError('Failed to fetch submissions');
      setSnackbarMessage('Error loading submissions');
      setSnackbarVisible(true);
    } finally {
      setLoading(false);
    }
  };

  // Fetch details for a specific submission
  const fetchSubmissionDetails = async (submissionId) => {
    try {
      setLoading(true);

      const submissionRef = doc(db, 'gradeSubmissions', submissionId);
      const submissionDoc = await getDoc(submissionRef);

      if (submissionDoc.exists()) {
        setSubmissionDetails(submissionDoc.data());
      } else {
        setError('Submission not found');
        setSnackbarMessage('Submission not found');
        setSnackbarVisible(true);
      }
    } catch (error) {
      console.error('Error fetching submission details:', error);
      setError('Failed to fetch submission details');
      setSnackbarMessage('Error loading submission details');
      setSnackbarVisible(true);
    } finally {
      setLoading(false);
    }
  };

  // Approve submission
  const approveSubmission = async () => {
    try {
      setLoading(true);

      if (!selectedSubmission || !selectedSubmission.id) {
        throw new Error('No submission selected or invalid submission ID');
      }

      if (!auth.currentUser) {
        throw new Error('User not authenticated');
      }

      // Use AdminService to approve the grade submission
      await AdminService.approveGradeSubmission(selectedSubmission.id);

      // Update grades in the class collection to make them visible to students and parents
      if (submissionDetails && submissionDetails.classId && submissionDetails.students) {
        try {
          // Get a reference to the class document
          const classRef = doc(db, 'classes', submissionDetails.classId);

          // Get the current class data
          const classDoc = await getDoc(classRef);

          if (classDoc.exists()) {
            const classData = classDoc.data();

            // Create or update the grades field
            const updatedGrades = classData.grades || {};

            // Add this subject's grades with teacher information
            updatedGrades[submissionDetails.subject] = {
              lastUpdated: serverTimestamp(),
              teacherId: submissionDetails.teacherId || '',
              teacherName: submissionDetails.teacherName || 'Unknown Teacher',
              assessments: submissionDetails.assessments || [],
              studentScores: submissionDetails.students.map(student => ({
                studentId: student.uid,
                studentName: student.name,
                totalScore: student.totalScore,
                assessmentScores: student.assessmentScores || []
              }))
            };

            // Update the class document
            await updateDoc(classRef, {
              grades: updatedGrades
            });

            // Also update the scores collection to mark them as approved
            const scoresRef = collection(db, 'scores');
            const scoresQuery = query(
              scoresRef,
              where('submissionId', '==', selectedSubmission.id)
            );

            const scoresSnapshot = await getDocs(scoresQuery);
            const scoreUpdatePromises = [];

            scoresSnapshot.forEach(scoreDoc => {
              scoreUpdatePromises.push(
                updateDoc(doc(db, 'scores', scoreDoc.id), {
                  status: 'approved',
                  approvedAt: serverTimestamp(),
                  approvedBy: auth.currentUser.uid || 'unknown'
                })
              );
            });

            await Promise.all(scoreUpdatePromises);

            console.log(`Updated grades for ${submissionDetails.className}-${submissionDetails.sectionName} ${submissionDetails.subject}`);
          } else {
            console.error(`Class document not found: ${submissionDetails.classId}`);
          }
        } catch (err) {
          console.error('Error updating class grades:', err);
          // Continue with notifications even if class update fails
        }
      }

      // Send notifications to students and parents
      await sendNotificationsToStudentsAndParents();

      // Update the snackbar message to indicate notifications were sent
      setSnackbarMessage('Submission approved successfully. Students and parents have been notified of new grades.');

      // Notify the teacher that the submission was approved
      try {
        if (selectedSubmission.teacherId) {
          // Use the NotificationService to send the notification
          await NotificationService.sendGradeApprovalNotification(
            selectedSubmission.teacherId,
            selectedSubmission.id,
            submissionDetails.classId,
            submissionDetails.className,
            submissionDetails.sectionName,
            submissionDetails.subject
          );
        }
      } catch (err) {
        console.error('Error sending teacher notification:', err);
        // Continue even if teacher notification fails
      }

      setApproveDialogVisible(false);
      setSelectedSubmission(null);
      setSubmissionDetails(null);
      fetchSubmissions();

      setSnackbarMessage('Submission approved successfully. Students and parents have been notified.');
      setSnackbarVisible(true);
    } catch (error) {
      console.error('Error approving submission:', error);
      setSnackbarMessage(`Error approving submission: ${error.message}`);
      setSnackbarVisible(true);
    } finally {
      setLoading(false);
    }
  };

  // Reject submission
  const rejectSubmission = async () => {
    try {
      setLoading(true);

      if (!selectedSubmission || !selectedSubmission.id) {
        throw new Error('No submission selected or invalid submission ID');
      }

      if (!auth.currentUser) {
        throw new Error('User not authenticated');
      }

      if (!rejectReason.trim()) {
        throw new Error('Please provide a reason for rejection');
      }

      // Use AdminService to reject the grade submission
      await AdminService.rejectGradeSubmission(selectedSubmission.id, rejectReason);

      // Get safe values with fallbacks
      const className = selectedSubmission.className || '';
      const sectionName = selectedSubmission.sectionName || '';
      const subject = selectedSubmission.subject || '';
      const classId = selectedSubmission.classId || '';
      const submissionId = selectedSubmission.id || '';
      const teacherId = selectedSubmission.teacherId || '';

      if (teacherId) {
        // Use the NotificationService to send the notification
        await NotificationService.sendGradeRejectionNotification(
          teacherId,
          submissionId,
          classId,
          className,
          sectionName,
          subject,
          rejectReason
        );
      } else {
        console.warn('No teacher ID found for notification');
      }

      setRejectDialogVisible(false);
      setRejectReason('');
      setSelectedSubmission(null);
      setSubmissionDetails(null);
      fetchSubmissions();

      setSnackbarMessage('Submission rejected successfully');
      setSnackbarVisible(true);
    } catch (error) {
      console.error('Error rejecting submission:', error);
      setSnackbarMessage(`Error rejecting submission: ${error.message}`);
      setSnackbarVisible(true);
    } finally {
      setLoading(false);
    }
  };

  // Send notifications to students and parents
  const sendNotificationsToStudentsAndParents = async () => {
    try {
      if (!submissionDetails || !submissionDetails.students || !selectedSubmission) {
        console.error('Missing submission details or students data');
        return;
      }

      // Get safe values with fallbacks
      const className = submissionDetails.className || '';
      const sectionName = submissionDetails.sectionName || '';
      const subject = submissionDetails.subject || '';
      const classId = submissionDetails.classId || '';

      // Format class and section information for better display
      const classInfo = className ? (sectionName ? `${className}-${sectionName}` : className) : '';

      // Get assessment details for better notification content
      const assessmentCount = submissionDetails.assessments?.length || 0;
      const assessmentTypes = [...new Set(submissionDetails.assessments?.map(a => a.type) || [])];
      const formattedAssessmentTypes = assessmentTypes.map(type =>
        type.charAt(0).toUpperCase() + type.slice(1)
      ).join(', ');

      // Add additional information to the notification
      const additionalInfo = {
        classInfo,
        assessmentCount,
        assessmentTypes: formattedAssessmentTypes
      };

      // Use the NotificationService to send notifications to students and parents
      const result = await NotificationService.sendGradePublishedNotifications(
        classId,
        className,
        sectionName,
        subject,
        submissionDetails.students,
        submissionDetails.assessments,
        additionalInfo
      );

      console.log(`Sent notifications to students and parents. Success: ${result.success}, Failed: ${result.failed}`);

      // Update badge counts for users
      try {
        // Get all student IDs
        const studentIds = submissionDetails.students
          .filter(student => student && student.uid)
          .map(student => student.uid);

        // Get all parent IDs for these students
        const parentIds = [];
        for (const studentId of studentIds) {
          const usersRef = collection(db, 'users');
          const q = query(usersRef, where('role', '==', 'parent'), where('children', 'array-contains', studentId));
          const querySnapshot = await getDocs(q);

          querySnapshot.forEach(doc => {
            if (doc.id) parentIds.push(doc.id);
          });
        }

        // Combine all user IDs that should receive badge count updates
        const userIds = [...new Set([...studentIds, ...parentIds])];

        // Update badge count for each user
        for (const userId of userIds) {
          const userRef = doc(db, 'users', userId);
          const userDoc = await getDoc(userRef);

          if (userDoc.exists()) {
            const userData = userDoc.data();
            const currentBadgeCount = userData.badgeCount || 0;

            await updateDoc(userRef, {
              badgeCount: currentBadgeCount + 1
            });
          }
        }
      } catch (err) {
        console.error('Error updating badge counts:', err);
        // Continue even if badge count update fails
      }
    } catch (error) {
      console.error('Error sending notifications:', error);
      throw error;
    }
  };

  // Render submission list
  const renderSubmissionList = () => {
    if (submissions.length === 0) {
      return (
        <Animatable.View
          animation="fadeIn"
          duration={500}
          style={styles.emptyStateContainer}
        >
          <IconButton
            icon="clipboard-text-outline"
            size={64}
            color="#BDBDBD"
            style={styles.emptyStateIcon}
          />
          <Text style={styles.emptyStateText}>
            {translate('admin.gradeApproval.noSubmissions')}
          </Text>
          <Button
            mode="contained"
            onPress={fetchSubmissions}
            icon="refresh"
            style={styles.refreshButton}
          >
            {translate('admin.gradeApproval.refresh')}
          </Button>
        </Animatable.View>
      );
    }

    return (
      <Animatable.View animation="fadeIn" duration={500} style={styles.submissionListContainer}>
        <Card style={styles.headerCard}>
          <Card.Content>
            <View style={styles.headerRow}>
              <View>
                <Title style={styles.headerTitle}>{translate('admin.gradeApproval.pendingSubmissions')}</Title>
                <Paragraph style={styles.headerSubtitle}>
                  {translate('admin.gradeApproval.submissionsCount', {
                    count: filteredSubmissions.length,
                    total: submissions.length
                  })}
                </Paragraph>
              </View>
              <Button
                mode="contained"
                icon="refresh"
                onPress={fetchSubmissions}
                style={styles.refreshButton}
              >
                {translate('admin.gradeApproval.refresh')}
              </Button>
            </View>

            <View style={styles.filterRow}>
              <TextInput
                label={translate('admin.gradeApproval.search')}
                value={searchQuery}
                onChangeText={setSearchQuery}
                style={styles.searchInput}
                left={<TextInput.Icon icon="magnify" />}
                clearButtonMode="while-editing"
                mode="outlined"
              />

              <Menu
                visible={filterMenuVisible}
                onDismiss={() => setFilterMenuVisible(false)}
                anchor={
                  <Button
                    mode="outlined"
                    icon="sort"
                    onPress={() => setFilterMenuVisible(true)}
                    style={styles.filterButton}
                  >
                    {translate('admin.gradeApproval.sort')}
                  </Button>
                }
                style={styles.sortMenu}
              >
                <Menu.Item
                  title={translate('admin.gradeApproval.date')}
                  onPress={() => {
                    setSortOption('date');
                    setFilterMenuVisible(false);
                  }}
                  leadingIcon="calendar"
                />
                <Menu.Item
                  title={translate('admin.gradeApproval.class')}
                  onPress={() => {
                    setSortOption('class');
                    setFilterMenuVisible(false);
                  }}
                  leadingIcon="school"
                />
                <Menu.Item
                  title={translate('admin.gradeApproval.subject')}
                  onPress={() => {
                    setSortOption('subject');
                    setFilterMenuVisible(false);
                  }}
                  leadingIcon="book"
                />
                <Menu.Item
                  title={translate('admin.gradeApproval.teacher')}
                  onPress={() => {
                    setSortOption('teacher');
                    setFilterMenuVisible(false);
                  }}
                  leadingIcon="account"
                />
              </Menu>
            </View>
          </Card.Content>
        </Card>

        {filteredSubmissions.length === 0 ? (
          <Animatable.View animation="fadeIn" duration={500} style={styles.emptyStateContainer}>
            <Text style={styles.emptyStateText}>
              {translate('admin.gradeApproval.noSubmissions')}
            </Text>
            <Button
              mode="outlined"
              onPress={() => setSearchQuery('')}
              icon="close"
              style={styles.clearButton}
            >
              {translate('common.clearSearch')}
            </Button>
          </Animatable.View>
        ) : (
          <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
            {filteredSubmissions.map((submission) => (
              <Animatable.View
                key={submission.id}
                animation="fadeIn"
                duration={500}
                style={styles.submissionListItem}
              >
                <Card
                  style={styles.submissionListCard}
                  onPress={() => setSelectedSubmission(submission)}
                  mode="outlined"
                >
                  <Card.Content>
                    {/* Header with Class and Time */}
                    <View style={styles.submissionHeader}>
                      <View style={styles.submissionHeaderLeft}>
                        <Text style={styles.submissionTitle}>
                          {submission.className}-{submission.sectionName}
                        </Text>
                        <Text style={styles.submissionSubject}>
                          {submission.subject}
                        </Text>
                        <Chip
                          icon="account-group"
                          style={styles.sectionChip}
                          mode="outlined"
                        >
                          Section: {submission.sectionName}
                        </Chip>
                      </View>

                      <View style={styles.submissionHeaderRight}>
                        <Chip
                          icon="clock-outline"
                          style={styles.timeChip}
                          mode="outlined"
                          textStyle={{ fontSize: 12, color: '#FF9800' }}
                        >
                          {submission.metadata?.daysAgo === 0 ? 'Today' :
                           submission.metadata?.daysAgo === 1 ? 'Yesterday' :
                           `${submission.metadata?.daysAgo} days ago`}
                        </Chip>
                      </View>
                    </View>

                    {/* Teacher Info */}
                    <View style={styles.teacherContainer}>
                      <IconButton
                        icon="account"
                        size={20}
                        style={styles.teacherIcon}
                      />
                      <Text style={styles.teacherName}>
                        {submission.teacherName}
                      </Text>
                    </View>

                    {/* Assessment Types */}
                    <View style={styles.assessmentTypesContainer}>
                      <Text style={styles.sectionLabel}>Assessment Types:</Text>
                      <View style={styles.chipContainer}>
                        {submission.metadata?.assessmentTypes.map((type, index) => (
                          <Chip
                            key={index}
                            style={styles.assessmentTypeChip}
                            textStyle={{ fontSize: 12 }}
                            compact
                          >
                            {type.charAt(0).toUpperCase() + type.slice(1)}
                          </Chip>
                        ))}
                      </View>
                    </View>

                    {/* Stats Row */}
                    <View style={styles.statsContainer}>
                      <View style={styles.statItem}>
                        <IconButton icon="account-group" size={20} style={styles.statIcon} />
                        <Text style={styles.statValue}>{submission.metadata?.studentCount}</Text>
                        <Text style={styles.statLabel}>Students</Text>
                      </View>

                      <View style={styles.statItem}>
                        <IconButton icon="clipboard-list" size={20} style={styles.statIcon} />
                        <Text style={styles.statValue}>{submission.metadata?.totalAssessments}</Text>
                        <Text style={styles.statLabel}>Assessments</Text>
                      </View>

                      <View style={styles.statItem}>
                        <IconButton icon="calendar" size={20} style={styles.statIcon} />
                        <Text style={styles.statValue}>{submission.metadata?.formattedDate}</Text>
                        <Text style={styles.statLabel}>Date</Text>
                      </View>
                    </View>

                    {/* Action Button */}
                    <Button
                      mode="contained"
                      onPress={() => setSelectedSubmission(submission)}
                      style={styles.viewDetailsButton}
                      icon="eye"
                    >
                      {translate('admin.gradeApproval.viewDetails')}
                    </Button>
                  </Card.Content>
                </Card>
              </Animatable.View>
            ))}
          </ScrollView>
        )}
      </Animatable.View>
    );
  };

  // Render submission details
  const renderSubmissionDetails = () => {
    if (!selectedSubmission || !submissionDetails) {
      return (
        <Animatable.View animation="fadeIn" duration={500} style={styles.emptyStateContainer}>
          <ActivityIndicator size="large" color="#1976d2" />
          <Text style={styles.loadingText}>{translate('admin.gradeApproval.loadingSubmissions')}</Text>
        </Animatable.View>
      );
    }

    return (
      <View style={styles.detailsContainer}>
        <Button
          mode="outlined"
          icon="arrow-left"
          onPress={() => setSelectedSubmission(null)}
          style={styles.backButton}
        >
          {translate('common.back')}
        </Button>

        <ScrollView
          style={styles.detailsScrollView}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.detailsScrollContent}
        >
          <Card style={styles.detailsCard}>
            <Card.Content>
              <View style={styles.detailsCardHeader}>
                <Title style={styles.detailsTitle}>
                  {submissionDetails.className}-{submissionDetails.sectionName} {submissionDetails.subject}
                </Title>
                <Chip
                  icon="clock-outline"
                  mode="outlined"
                  style={styles.detailsDateChip}
                >
                  {new Date(submissionDetails.submittedAt?.seconds * 1000).toLocaleDateString()}
                </Chip>
              </View>

              <View style={styles.detailsHeader}>
                <View style={styles.detailsHeaderItem}>
                  <Text style={styles.detailLabel}>Class</Text>
                  <Text style={styles.detailValue}>{submissionDetails.className}-{submissionDetails.sectionName}</Text>
                </View>

                <View style={styles.detailsHeaderItem}>
                  <Text style={styles.detailLabel}>Subject</Text>
                  <Text style={styles.detailValue}>{submissionDetails.subject}</Text>
                </View>

                <View style={styles.detailsHeaderItem}>
                  <Text style={styles.detailLabel}>Teacher</Text>
                  <Text style={styles.detailValue}>{submissionDetails.teacherName}</Text>
                </View>

                <View style={styles.detailsHeaderItem}>
                  <Text style={styles.detailLabel}>Status</Text>
                  <Chip
                    mode="outlined"
                    icon="clock-outline"
                    textStyle={{ color: '#FF9800' }}
                    style={styles.statusChip}
                  >
                    Pending Approval
                  </Chip>
                </View>
              </View>
            </Card.Content>
          </Card>

          <Text style={styles.sectionTitle}>Assessments</Text>
          <View style={styles.assessmentsContainer}>
            {submissionDetails.assessments && submissionDetails.assessments.length > 0 ? (
              submissionDetails.assessments.map((assessment) => (
                <Card key={assessment.id} style={styles.assessmentCard}>
                  <Card.Content>
                    <View style={styles.assessmentHeader}>
                      <View>
                        <Text style={styles.assessmentTitle}>{assessment.title}</Text>
                        <Text style={styles.assessmentType}>
                          {assessment.type?.charAt(0).toUpperCase() + assessment.type?.slice(1) || 'Assignment'}
                        </Text>
                      </View>
                      <Text style={styles.assessmentPoints}>{assessment.points} points</Text>
                    </View>
                  </Card.Content>
                </Card>
              ))
            ) : (
              <Card style={styles.emptyCard}>
                <Card.Content>
                  <Paragraph style={styles.emptyText}>No assessments found</Paragraph>
                </Card.Content>
              </Card>
            )}
          </View>

          <Text style={styles.sectionTitle}>Student Scores</Text>
          <Card style={styles.studentsCard}>
            <DataTable>
              <DataTable.Header style={styles.tableHeader}>
                <DataTable.Title>Student</DataTable.Title>
                <DataTable.Title numeric>Total Score</DataTable.Title>
              </DataTable.Header>

              {submissionDetails.students && submissionDetails.students.length > 0 ? (
                submissionDetails.students.map((student, index) => (
                  <DataTable.Row
                    key={student.id || index}
                    style={index % 2 === 0 ? styles.studentRow : styles.studentRowAlternate}
                  >
                    <DataTable.Cell>{student.name || 'Unknown Student'}</DataTable.Cell>
                    <DataTable.Cell numeric>{student.totalScore || '0'}</DataTable.Cell>
                  </DataTable.Row>
                ))
              ) : (
                <DataTable.Row>
                  <DataTable.Cell>No students found</DataTable.Cell>
                  <DataTable.Cell numeric>-</DataTable.Cell>
                </DataTable.Row>
              )}
            </DataTable>
          </Card>

          <View style={styles.actionButtonsContainer}>
            <Button
              mode="outlined"
              onPress={() => setRejectDialogVisible(true)}
              style={styles.actionButton}
              buttonColor="#FFEBEE"
              textColor="#D32F2F"
              icon="close-circle"
            >
              {translate('admin.gradeApproval.reject')}
            </Button>

            <Button
              mode="contained"
              onPress={() => setApproveDialogVisible(true)}
              style={styles.actionButton}
              buttonColor="#4CAF50"
              icon="check-circle"
            >
              {translate('admin.gradeApproval.approve')}
            </Button>
          </View>
        </ScrollView>
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar backgroundColor="#1976d2" barStyle="light-content" />

      {/* Admin App Header */}
      <AdminAppHeader
        title={translate('admin.gradeApproval.title')}
        onMenuPress={toggleDrawer}
        showNotification={true}
      />

      {/* Admin Sidebar */}
      <AdminSidebar
        visible={drawerOpen}
        onClose={toggleDrawer}
        navigation={navigation}
        activeSidebarItem={activeSidebarItem}
        setActiveSidebarItem={setActiveSidebarItem}
      />

      {/* Sidebar Backdrop */}
      <SidebarBackdrop
        visible={drawerOpen}
        onPress={toggleDrawer}
        fadeAnim={backdropFadeAnim}
      />

      {/* Main Content */}
      <View style={styles.mainContent}>
        {loading && !selectedSubmission ? (
          <Animatable.View animation="fadeIn" duration={500} style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#1976d2" />
            <Text style={styles.loadingText}>{translate('admin.gradeApproval.loadingSubmissions')}</Text>
          </Animatable.View>
        ) : error && !selectedSubmission ? (
          <Animatable.View animation="fadeIn" duration={500} style={styles.errorContainer}>
            <Text style={styles.errorText}>{error}</Text>
            <Button
              mode="contained"
              onPress={fetchSubmissions}
              icon="refresh"
              style={styles.retryButton}
            >
              {translate('admin.gradeApproval.retry')}
            </Button>
          </Animatable.View>
        ) : (
          <View style={styles.contentContainer}>
            {selectedSubmission ? (
              renderSubmissionDetails()
            ) : (
              renderSubmissionList()
            )}
          </View>
        )}
      </View>

      {/* Approve Dialog */}
      <Portal>
        <Dialog
          visible={approveDialogVisible}
          onDismiss={() => setApproveDialogVisible(false)}
          style={styles.dialog}
        >
          <Dialog.Title style={styles.dialogTitle}>{translate('admin.gradeApproval.approveGrades')}</Dialog.Title>
          <Dialog.Content>
            <Paragraph style={styles.dialogParagraph}>
              {translate('admin.gradeApproval.approveConfirmation')}
            </Paragraph>
            <View style={styles.dialogBulletPoints}>
              <Text style={styles.dialogBulletPoint}>{translate('admin.gradeApproval.gradesWillBeVisible')}</Text>
              <Text style={styles.dialogBulletPoint}>{translate('admin.gradeApproval.notificationsWillBeSent')}</Text>
              <Text style={styles.dialogBulletPoint}>{translate('admin.gradeApproval.cannotBeUndone')}</Text>
            </View>
          </Dialog.Content>
          <Dialog.Actions style={styles.dialogActions}>
            <Button
              onPress={() => setApproveDialogVisible(false)}
              style={styles.dialogCancelButton}
            >
              {translate('admin.gradeApproval.cancel')}
            </Button>
            <Button
              mode="contained"
              onPress={approveSubmission}
              loading={loading}
              disabled={loading}
              style={styles.dialogConfirmButton}
              icon="check-circle"
            >
              {translate('admin.gradeApproval.confirmApprove')}
            </Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>

      {/* Reject Dialog */}
      <Portal>
        <Dialog
          visible={rejectDialogVisible}
          onDismiss={() => setRejectDialogVisible(false)}
          style={styles.dialog}
        >
          <Dialog.Title style={styles.dialogTitle}>{translate('admin.gradeApproval.rejectGrades')}</Dialog.Title>
          <Dialog.Content>
            <Paragraph style={styles.dialogParagraph}>
              {translate('admin.gradeApproval.rejectConfirmation')}
            </Paragraph>
            <TextInput
              label={translate('admin.gradeApproval.rejectReason')}
              placeholder={translate('admin.gradeApproval.rejectReasonPlaceholder')}
              value={rejectReason}
              onChangeText={setRejectReason}
              mode="outlined"
              multiline
              numberOfLines={3}
              style={styles.rejectReasonInput}
              error={rejectDialogVisible && !rejectReason.trim()}
            />
            {rejectDialogVisible && !rejectReason.trim() && (
              <Text style={styles.errorText}>
                {translate('admin.gradeApproval.rejectReasonRequired')}
              </Text>
            )}
          </Dialog.Content>
          <Dialog.Actions style={styles.dialogActions}>
            <Button
              onPress={() => setRejectDialogVisible(false)}
              style={styles.dialogCancelButton}
            >
              {translate('admin.gradeApproval.cancel')}
            </Button>
            <Button
              mode="contained"
              onPress={rejectSubmission}
              loading={loading}
              disabled={loading || !rejectReason.trim()}
              style={[styles.dialogConfirmButton, styles.rejectButton]}
              icon="close-circle"
            >
              {translate('admin.gradeApproval.confirmReject')}
            </Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>

      {/* Snackbar for notifications */}
      <Snackbar
        visible={snackbarVisible}
        onDismiss={() => setSnackbarVisible(false)}
        duration={5000}
        style={styles.snackbar}
        action={{
          label: translate('common.dismiss'),
          onPress: () => setSnackbarVisible(false),
        }}
      >
        {snackbarMessage}
      </Snackbar>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5'
  },
  mainContent: {
    flex: 1,
    padding: 16
  },
  contentContainer: {
    flex: 1
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
    textAlign: 'center'
  },
  retryButton: {
    marginTop: 16,
    paddingHorizontal: 24,
    borderRadius: 8
  },
  dialog: {
    borderRadius: 12,
    backgroundColor: '#fff',
    elevation: 24
  },
  dialogTitle: {
    fontSize: 20,
    textAlign: 'center',
    fontWeight: 'bold'
  },
  dialogParagraph: {
    fontSize: 16,
    lineHeight: 24,
    marginBottom: 16
  },
  dialogBulletPoints: {
    marginLeft: 8,
    marginBottom: 16
  },
  dialogBulletPoint: {
    fontSize: 15,
    lineHeight: 24,
    marginBottom: 8
  },
  dialogActions: {
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingBottom: 16
  },
  dialogCancelButton: {
    minWidth: 100
  },
  dialogConfirmButton: {
    minWidth: 120,
    borderRadius: 8
  },
  rejectButton: {
    backgroundColor: '#D32F2F'
  },
  // Submission List Styles
  scrollView: {
    flex: 1
  },
  submissionListItem: {
    marginBottom: 16
  },
  submissionListCard: {
    elevation: 3,
    borderRadius: 8,
    borderColor: '#E0E0E0'
  },
  selectedCard: {
    backgroundColor: '#E3F2FD'
  },
  submissionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12
  },
  submissionHeaderLeft: {
    flex: 1,
    marginRight: 8
  },
  submissionHeaderRight: {
    alignItems: 'flex-end'
  },
  submissionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 4,
    color: '#333'
  },
  submissionSubject: {
    fontSize: 16,
    color: '#555',
    fontWeight: '500'
  },
  timeChip: {
    backgroundColor: '#FFF8E1'
  },
  sectionChip: {
    backgroundColor: '#E8F5E9',
    marginTop: 4
  },
  teacherContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    backgroundColor: '#F5F5F5',
    padding: 8,
    borderRadius: 8
  },
  teacherIcon: {
    margin: 0,
    backgroundColor: '#E0E0E0'
  },
  teacherName: {
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 4
  },
  sectionLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
    color: '#555'
  },
  assessmentTypesContainer: {
    marginBottom: 16
  },
  chipContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap'
  },
  assessmentTypeChip: {
    marginRight: 8,
    marginBottom: 8,
    backgroundColor: '#E8F5E9'
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
    backgroundColor: '#F5F5F5',
    padding: 8,
    borderRadius: 8
  },
  statItem: {
    alignItems: 'center',
    flex: 1
  },
  statIcon: {
    margin: 0
  },
  statValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333'
  },
  statLabel: {
    fontSize: 12,
    color: '#757575'
  },
  viewDetailsButton: {
    borderRadius: 8
  },
  // Details Styles
  detailsContainer: {
    flex: 1,
    display: 'flex',
    flexDirection: 'column'
  },
  detailsScrollView: {
    flex: 1
  },
  detailsScrollContent: {
    paddingBottom: 24
  },
  detailsCard: {
    marginBottom: 16,
    elevation: 3,
    borderRadius: 8
  },
  detailsCardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16
  },
  detailsTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    flex: 1,
    marginRight: 8
  },
  detailsDateChip: {
    backgroundColor: '#FFF8E1'
  },
  detailsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginTop: 8,
    marginBottom: 8,
    flexWrap: 'wrap'
  },
  detailsHeaderItem: {
    minWidth: '45%',
    marginBottom: 16,
    backgroundColor: '#F5F5F5',
    padding: 8,
    borderRadius: 8
  },
  detailLabel: {
    fontSize: 12,
    color: '#757575',
    marginBottom: 4
  },
  detailValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333'
  },
  backButton: {
    marginBottom: 16,
    alignSelf: 'flex-start',
    borderRadius: 8
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 12,
    color: '#333',
    backgroundColor: '#E3F2FD',
    padding: 8,
    borderRadius: 8
  },
  assessmentsContainer: {
    marginBottom: 16
  },
  assessmentCard: {
    marginBottom: 8,
    elevation: 2,
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: '#4CAF50'
  },
  assessmentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center'
  },
  assessmentTitle: {
    fontWeight: 'bold',
    fontSize: 16,
    color: '#333'
  },
  assessmentType: {
    fontSize: 12,
    color: '#757575',
    marginTop: 4
  },
  assessmentPoints: {
    fontWeight: 'bold',
    color: '#4CAF50'
  },
  studentsCard: {
    marginBottom: 16,
    elevation: 2,
    borderRadius: 8,
    overflow: 'hidden'
  },
  tableHeader: {
    backgroundColor: '#E8F5E9'
  },
  emptyCard: {
    padding: 8,
    backgroundColor: '#F5F5F5',
    borderRadius: 8
  },
  emptyText: {
    textAlign: 'center',
    color: '#757575',
    fontStyle: 'italic'
  },
  studentRow: {
    backgroundColor: '#fff'
  },
  studentRowAlternate: {
    backgroundColor: '#f5f5f5'
  },
  actionButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 24,
    marginBottom: 16
  },
  actionButton: {
    marginLeft: 16
  },
  rejectReasonInput: {
    marginTop: 16
  },
  loader: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center'
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16
  },
  errorText: {
    color: 'red',
    marginBottom: 16,
    textAlign: 'center'
  },
  snackbar: {
    bottom: 16
  },
  emptyStateContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32
  },
  emptyStateIcon: {
    marginBottom: 16
  },
  emptyStateText: {
    fontSize: 16,
    color: '#757575',
    textAlign: 'center',
    marginBottom: 16
  },
  submissionListContainer: {
    flex: 1
  },
  headerCard: {
    marginBottom: 16,
    borderRadius: 12,
    elevation: 4
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold'
  },
  headerSubtitle: {
    color: '#757575',
    fontSize: 14
  },
  refreshButton: {
    borderRadius: 8
  },
  filterRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8
  },
  searchInput: {
    flex: 1,
    marginRight: 8,
    backgroundColor: '#fff'
  },
  filterButton: {
    borderRadius: 8
  },
  filterMenu: {
    elevation: 8
  },
  sortMenu: {
    elevation: 8
  },
  clearButton: {
    marginTop: 8,
    borderRadius: 8
  }
});

export default AdminGradeApproval;
