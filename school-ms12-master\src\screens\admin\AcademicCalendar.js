import React, { useState, useEffect, useRef } from 'react';
import { View, ScrollView, StyleSheet, Text, Animated } from 'react-native';
import { Title, Card, List, FAB, Portal, Modal, ActivityIndicator, Snackbar, useTheme, Surface, Divider, IconButton } from 'react-native-paper';
import { collection, getDocs, updateDoc, doc } from 'firebase/firestore';
import { db } from '../../config/firebase';
import CustomButton from '../../components/common/CustomButton';
import CustomInput from '../../components/common/CustomInput';
import EthiopianDatePicker from '../../components/common/EthiopianDatePicker';
import { useLanguage } from '../../context/LanguageContext';
import { useEthiopianCalendar } from '../../hooks/useEthiopianCalendar';
import AdminAppHeader from '../../components/common/AdminAppHeader';
import AdminSidebar from '../../components/common/AdminSidebar';
import SidebarBackdrop from '../../components/common/SidebarBackdrop';
import { useNavigation } from '@react-navigation/native';
import * as Animatable from 'react-native-animatable';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';

// Using Ethiopian calendar for date formatting

const AcademicCalendar = () => {
  const { translate, language, isRTL } = useLanguage();
  const { formatDate } = useEthiopianCalendar();
  // No theme needed
  const navigation = useNavigation();
  const [academicYear, setAcademicYear] = useState({
    startDate: '',
    endDate: '',
    terms: [],
    holidays: []
  });
  const [visible, setVisible] = useState(false);
  const [modalType, setModalType] = useState('');
  const [fabOpen, setFabOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [drawerOpen, setDrawerOpen] = useState(false);
  const drawerAnim = useRef(new Animated.Value(-300)).current;
  const backdropFadeAnim = useRef(new Animated.Value(0)).current;
  const [activeSidebarItem, setActiveSidebarItem] = useState('AcademicCalendar');
  const [formData, setFormData] = useState({
    name: '',
    startDate: '',
    endDate: '',
    description: ''
  });

  useEffect(() => {
    fetchAcademicYear();
  }, [language]);

  useEffect(() => {
    navigation.setOptions({
      headerShown: false,
    });
  }, [navigation]);

  const toggleDrawer = () => {
    if (drawerOpen) {
      // Close drawer
      Animated.parallel([
        Animated.timing(drawerAnim, {
          toValue: -300,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(backdropFadeAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        })
      ]).start(() => setDrawerOpen(false));
    } else {
      // Open drawer
      setDrawerOpen(true);
      Animated.parallel([
        Animated.timing(drawerAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(backdropFadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        })
      ]).start();
    }
  };

  const fetchAcademicYear = async () => {
    setLoading(true);
    setError('');
    try {
      const querySnapshot = await getDocs(collection(db, 'academicYears'));
      if (!querySnapshot.empty) {
        const yearDoc = querySnapshot.docs[0];
        setAcademicYear({ id: yearDoc.id, ...yearDoc.data() });
      }
    } catch (err) {
      setError(translate('academicCalendar.error'));
      console.error('Error fetching academic year:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    setLoading(true);
    setError('');
    try {
      if (modalType === 'term') {
        const updatedTerms = [...academicYear.terms, formData];
        await updateDoc(doc(db, 'academicYears', academicYear.id), {
          terms: updatedTerms
        });
        setAcademicYear(prev => ({ ...prev, terms: updatedTerms }));
        setError(translate('academicCalendar.termAdded'));
      } else {
        const updatedHolidays = [...academicYear.holidays, formData];
        await updateDoc(doc(db, 'academicYears', academicYear.id), {
          holidays: updatedHolidays
        });
        setAcademicYear(prev => ({ ...prev, holidays: updatedHolidays }));
        setError(translate('academicCalendar.holidayAdded'));
      }
      setVisible(false);
      setFormData({ name: '', startDate: '', endDate: '', description: '' });
    } catch (err) {
      setError(translate('academicCalendar.error'));
      console.error('Error saving:', err);
    } finally {
      setLoading(false);
    }
  };

  const renderModal = () => (
    <Portal>
      <Modal visible={visible} onDismiss={() => setVisible(false)} contentContainerStyle={[styles.modal, { backgroundColor: '#ffffff' }]}>
        <Animatable.View animation="fadeIn" duration={300}>
          <View style={styles.modalHeader}>
            <MaterialCommunityIcons
              name={modalType === 'term' ? 'calendar-text' : 'beach'}
              size={24}
              color={'#1976d2'}
              style={styles.modalIcon}
            />
            <Title style={[styles.modalTitle, isRTL && styles.rtlText]}>
              {modalType === 'term' ? translate('academicCalendar.addTerm') : translate('academicCalendar.addHoliday')}
            </Title>
            <IconButton
              icon="close"
              size={20}
              style={styles.closeButton}
              onPress={() => setVisible(false)}
            />
          </View>
          <Divider style={styles.modalDivider} />

          <View style={styles.modalContent}>
            <CustomInput
              label={modalType === 'term' ? translate('academicCalendar.termName') : translate('academicCalendar.holidayName')}
              value={formData.name}
              onChangeText={text => setFormData(prev => ({ ...prev, name: text }))}
              style={styles.modalInput}
              isRTL={isRTL}
            />

            <View style={styles.datePickerContainer}>
              <Text style={[styles.datePickerLabel, isRTL && styles.rtlText]}>
                {translate('academicCalendar.startDate')}
              </Text>
              <EthiopianDatePicker
                value={formData.startDate ? new Date(formData.startDate) : new Date()}
                onChange={(date) => setFormData(prev => ({ ...prev, startDate: date.toISOString() }))}
                label={translate('academicCalendar.selectStartDate')}
                language={language}
                isRTL={isRTL}
              />
            </View>

            <View style={styles.datePickerContainer}>
              <Text style={[styles.datePickerLabel, isRTL && styles.rtlText]}>
                {translate('academicCalendar.endDate')}
              </Text>
              <EthiopianDatePicker
                value={formData.endDate ? new Date(formData.endDate) : new Date()}
                onChange={(date) => setFormData(prev => ({ ...prev, endDate: date.toISOString() }))}
                label={translate('academicCalendar.selectEndDate')}
                minDate={formData.startDate}
                language={language}
                isRTL={isRTL}
              />
            </View>

            <CustomInput
              label={translate('academicCalendar.description')}
              value={formData.description}
              onChangeText={text => setFormData(prev => ({ ...prev, description: text }))}
              multiline
              numberOfLines={4}
              style={styles.modalInput}
              isRTL={isRTL}
            />

            <View style={styles.modalButtons}>
              <CustomButton
                onPress={() => setVisible(false)}
                mode="outlined"
                style={styles.modalButton}
                icon="close"
              >
                {translate('actions.cancel')}
              </CustomButton>
              <CustomButton
                onPress={handleSave}
                mode="contained"
                style={styles.modalButton}
                icon={modalType === 'term' ? 'calendar-plus' : 'beach'}
                loading={loading}
                disabled={!formData.name || !formData.startDate || !formData.endDate}
              >
                {translate('actions.save')}
              </CustomButton>
            </View>
          </View>
        </Animatable.View>
      </Modal>
    </Portal>
  );

  if (loading) {
    return (
      <View style={styles.centered}>
        <ActivityIndicator size="large" />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Sidebar */}
      <AdminSidebar
        drawerAnim={drawerAnim}
        activeSidebarItem={activeSidebarItem}
        setActiveSidebarItem={setActiveSidebarItem}
        toggleDrawer={toggleDrawer}
      />

      {/* Backdrop */}
      <SidebarBackdrop
        visible={drawerOpen}
        onPress={toggleDrawer}
        fadeAnim={backdropFadeAnim}
      />

      {/* Admin App Header */}
      <AdminAppHeader
        title={translate('academicCalendar.title')}
        onMenuPress={toggleDrawer}
      />

      <View style={styles.mainContent}>
        <Animatable.View animation="fadeIn" duration={800}>
          <Surface style={styles.headerCard}>
            <LinearGradient
              colors={['#1976d2' + '20', '#f5f5f5']}
              style={styles.headerGradient}
            >
              <View style={styles.headerContent}>
                <MaterialCommunityIcons name="calendar" size={24} color={'#1976d2'} />
                <Title style={[styles.headerTitle, isRTL && styles.rtlText]}>
                  {translate('academicCalendar.title')}
                </Title>
              </View>
              <Text style={[styles.headerSubtitle, isRTL && styles.rtlText]}>
                {translate('academicCalendar.subtitle')}
              </Text>
            </LinearGradient>
          </Surface>
        </Animatable.View>

        <ScrollView style={styles.scrollView}>
          <Animatable.View animation="fadeInUp" duration={800} delay={300}>
            <Card style={styles.card}>
              <Card.Content>
                <View style={styles.sectionHeader}>
                  <MaterialCommunityIcons name="calendar-text" size={20} color={'#1976d2'} />
                  <Title style={[styles.sectionTitle, isRTL && styles.rtlText]}>
                    {translate('academicCalendar.terms')}
                  </Title>
                </View>
                <Divider style={styles.divider} />

                {academicYear.terms?.length > 0 ? (
                  academicYear.terms.map((term, index) => (
                    <Animatable.View key={index} animation="fadeIn" duration={500} delay={index * 100}>
                      <Surface style={styles.itemCard}>
                        <List.Item
                          title={term.name}
                          titleStyle={[styles.itemTitle, isRTL && styles.rtlText]}
                          description={`${formatDate(term.startDate)} - ${formatDate(term.endDate)}`}
                          descriptionStyle={[styles.itemDescription, isRTL && styles.rtlText]}
                          left={props => <List.Icon {...props} icon="calendar-month" color={'#1976d2'} />}
                          right={props => (
                            <IconButton
                              {...props}
                              icon="information-outline"
                              onPress={() => alert(term.description || translate('academicCalendar.noDescription'))}
                            />
                          )}
                        />
                      </Surface>
                    </Animatable.View>
                  ))
                ) : (
                  <Animatable.View animation="fadeIn" duration={500}>
                    <Surface style={styles.emptyContainer}>
                      <MaterialCommunityIcons name="calendar-alert" size={40} color={'#1976d2' + '80'} />
                      <Text style={styles.emptyText}>{translate('academicCalendar.noTerms')}</Text>
                    </Surface>
                  </Animatable.View>
                )}
              </Card.Content>
            </Card>
          </Animatable.View>

          <Animatable.View animation="fadeInUp" duration={800} delay={500}>
            <Card style={styles.card}>
              <Card.Content>
                <View style={styles.sectionHeader}>
                  <MaterialCommunityIcons name="beach" size={20} color={'#1976d2'} />
                  <Title style={[styles.sectionTitle, isRTL && styles.rtlText]}>
                    {translate('academicCalendar.holidays')}
                  </Title>
                </View>
                <Divider style={styles.divider} />

                {academicYear.holidays?.length > 0 ? (
                  academicYear.holidays.map((holiday, index) => (
                    <Animatable.View key={index} animation="fadeIn" duration={500} delay={index * 100}>
                      <Surface style={styles.itemCard}>
                        <List.Item
                          title={holiday.name}
                          titleStyle={[styles.itemTitle, isRTL && styles.rtlText]}
                          description={`${formatDate(holiday.startDate)} - ${formatDate(holiday.endDate)}`}
                          descriptionStyle={[styles.itemDescription, isRTL && styles.rtlText]}
                          left={props => <List.Icon {...props} icon="beach" color={'#9c27b0'} />}
                          right={props => (
                            <IconButton
                              {...props}
                              icon="information-outline"
                              onPress={() => alert(holiday.description || translate('academicCalendar.noDescription'))}
                            />
                          )}
                        />
                      </Surface>
                    </Animatable.View>
                  ))
                ) : (
                  <Animatable.View animation="fadeIn" duration={500}>
                    <Surface style={styles.emptyContainer}>
                      <MaterialCommunityIcons name="beach" size={40} color={'#1976d2'} />
                      <Text style={styles.emptyText}>{translate('academicCalendar.noHolidays')}</Text>
                    </Surface>
                  </Animatable.View>
                )}
              </Card.Content>
            </Card>
          </Animatable.View>
        </ScrollView>
      </View>

      <Portal>
        <FAB.Group
          open={fabOpen}
          icon={fabOpen ? 'close' : 'plus'}
          actions={[
            {
              icon: 'calendar',
              label: translate('academicCalendar.addTerm'),
              onPress: () => {
                setModalType('term');
                setVisible(true);
              },
            },
            {
              icon: 'beach',
              label: translate('academicCalendar.addHoliday'),
              onPress: () => {
                setModalType('holiday');
                setVisible(true);
              },
            },
          ]}
          onStateChange={({ open }) => setFabOpen(open)}
          style={styles.fab}
        />
      </Portal>

      {renderModal()}

      <Snackbar
        visible={!!error}
        onDismiss={() => setError('')}
        action={{
          label: translate('actions.dismiss'),
          onPress: () => setError(''),
        }}>
        {error}
      </Snackbar>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  mainContent: {
    flex: 1,
    paddingBottom: 80, // Space for FAB
  },
  scrollView: {
    flex: 1,
    padding: 16,
  },
  headerCard: {
    marginHorizontal: 16,
    marginTop: 16,
    marginBottom: 8,
    borderRadius: 12,
    overflow: 'hidden',
    elevation: 2,
  },
  headerGradient: {
    padding: 16,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  headerSubtitle: {
    marginTop: 4,
    marginLeft: 32,
    opacity: 0.7,
  },
  card: {
    marginHorizontal: 16,
    marginVertical: 8,
    borderRadius: 12,
    overflow: 'hidden',
    elevation: 2,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  sectionTitle: {
    fontSize: 18,
    marginLeft: 8,
  },
  divider: {
    marginBottom: 16,
  },
  itemCard: {
    marginBottom: 8,
    borderRadius: 8,
    overflow: 'hidden',
    elevation: 1,
  },
  itemTitle: {
    fontWeight: 'bold',
  },
  itemDescription: {
    opacity: 0.7,
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 24,
    borderRadius: 8,
  },
  emptyText: {
    textAlign: 'center',
    padding: 16,
    color: '#666',
    fontStyle: 'italic',
  },
  modal: {
    margin: 20,
    borderRadius: 12,
    overflow: 'hidden',
    elevation: 4,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    position: 'relative',
  },
  modalIcon: {
    marginRight: 8,
  },
  modalTitle: {
    flex: 1,
  },
  closeButton: {
    position: 'absolute',
    right: 8,
    top: 8,
  },
  modalDivider: {
    height: 1,
  },
  modalContent: {
    padding: 16,
  },
  modalInput: {
    marginBottom: 16,
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 16,
    gap: 8,
  },
  modalButton: {
    marginLeft: 8,
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
  },
  datePickerContainer: {
    marginBottom: 16,
  },
  datePickerLabel: {
    fontSize: 14,
    marginBottom: 8,
    color: '#666',
  },
  rtlText: {
    textAlign: 'right',
  },
});

export default AcademicCalendar;
