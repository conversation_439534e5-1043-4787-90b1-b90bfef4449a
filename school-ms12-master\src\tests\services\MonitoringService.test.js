import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import MonitoringService from '../../services/MonitoringService';
import { collection, addDoc, query, where, getDocs, orderBy, limit } from 'firebase/firestore';
import { db } from '../../config/firebase';

// Mock Firebase
jest.mock('../../config/firebase', () => ({
    db: {
        collection: jest.fn()
    }
}));

jest.mock('firebase/firestore', () => ({
    collection: jest.fn(),
    addDoc: jest.fn(),
    query: jest.fn(),
    where: jest.fn(),
    orderBy: jest.fn(),
    limit: jest.fn(),
    getDocs: jest.fn()
}));

describe('MonitoringService', () => {
    let monitoringService;

    beforeEach(() => {
        // Clear all mocks
        jest.clearAllMocks();
        
        // Reset performance metrics
        if (global.performance) {
            delete global.performance;
        }
        
        global.performance = {
            memory: {
                usedJSHeapSize: 1000000,
                totalJSHeapSize: 2000000
            },
            navigation: {
                type: 0,
                redirectCount: 0
            },
            timing: {
                navigationStart: Date.now() - 1000,
                loadEventEnd: Date.now(),
                toJSON: () => ({})
            }
        };
        
        // Initialize service
        monitoringService = new MonitoringService();
    });

    describe('Resource Monitoring', () => {
        it('should track resource usage successfully', async () => {
            const resourceType = 'library';
            const action = 'borrow';

            addDoc.mockResolvedValueOnce({ id: 'test-doc-id' });
            getDocs.mockResolvedValueOnce({
                empty: false,
                docs: [{
                    data: () => ({
                        utilization: 0.5,
                        inventory: 100
                    })
                }]
            });

            await monitoringService.trackResourceUsage(resourceType, action);

            expect(addDoc).toHaveBeenCalledWith(
                expect.anything(),
                expect.objectContaining({
                    type: 'resource_usage',
                    data: expect.objectContaining({
                        type: resourceType,
                        action
                    })
                })
            );
        });

        it('should handle resource threshold checks', async () => {
            const resourceType = 'library';
            
            // Mock high utilization scenario
            getDocs.mockResolvedValueOnce({
                empty: false,
                docs: [{
                    data: () => ({
                        utilization: 0.95,
                        inventory: 5
                    })
                }]
            });

            // Mock last maintenance date
            getDocs.mockResolvedValueOnce({
                empty: false,
                docs: [{
                    data: () => ({
                        timestamp: Date.now() - (31 * 24 * 60 * 60 * 1000) // 31 days ago
                    })
                }]
            });

            const notifyAdminsSpy = jest.spyOn(monitoringService, 'notifyAdmins');
            
            await monitoringService.checkResourceThresholds(resourceType);

            expect(notifyAdminsSpy).toHaveBeenCalledWith(
                expect.arrayContaining([
                    expect.objectContaining({
                        type: 'high_resource_utilization'
                    }),
                    expect.objectContaining({
                        type: 'low_inventory'
                    })
                ])
            );
        });
    });

    describe('Performance Monitoring', () => {
        it('should monitor system performance', async () => {
            addDoc.mockResolvedValueOnce({ id: 'test-doc-id' });

            await monitoringService.monitorPerformance();

            expect(addDoc).toHaveBeenCalledWith(
                expect.anything(),
                expect.objectContaining({
                    type: 'performance',
                    data: expect.objectContaining({
                        memory: expect.any(Object),
                        navigation: expect.any(Object),
                        timing: expect.any(Object)
                    })
                })
            );
        });

        it('should analyze performance metrics', async () => {
            const metrics = {
                memory: {
                    usedJSHeapSize: 1800000,
                    totalJSHeapSize: 2000000
                },
                timing: {
                    navigationStart: Date.now() - 6000,
                    loadEventEnd: Date.now()
                }
            };

            const notifyAdminsSpy = jest.spyOn(monitoringService, 'notifyAdmins');
            
            await monitoringService.analyzePerformance(metrics);

            expect(notifyAdminsSpy).toHaveBeenCalledWith(
                expect.arrayContaining([
                    expect.objectContaining({
                        type: 'high_memory_usage'
                    }),
                    expect.objectContaining({
                        type: 'slow_page_load'
                    })
                ])
            );
        });
    });

    describe('Error Monitoring', () => {
        it('should track error events', async () => {
            const error = new Error('Test error');
            const message = 'Test error message';
            const source = 'test.js';
            const lineno = 1;
            const colno = 1;

            addDoc.mockResolvedValueOnce({ id: 'test-doc-id' });

            await monitoringService.monitorErrors();
            window.onerror(message, source, lineno, colno, error);

            expect(addDoc).toHaveBeenCalledWith(
                expect.anything(),
                expect.objectContaining({
                    type: 'errors',
                    data: expect.objectContaining({
                        message,
                        source,
                        lineno,
                        colno,
                        stack: error.stack
                    })
                })
            );
        });

        it('should analyze error rates', async () => {
            // Mock high error rate scenario
            getDocs.mockResolvedValueOnce({
                size: 11,
                docs: Array(11).fill({
                    data: () => ({
                        timestamp: Date.now(),
                        message: 'Test error'
                    })
                })
            });

            const notifyAdminsSpy = jest.spyOn(monitoringService, 'notifyAdmins');
            
            await monitoringService.analyzeErrorRate();

            expect(notifyAdminsSpy).toHaveBeenCalledWith(
                expect.arrayContaining([
                    expect.objectContaining({
                        type: 'high_error_rate',
                        count: 11
                    })
                ])
            );
        });
    });

    describe('Usage Monitoring', () => {
        it('should track user interactions', async () => {
            const component = 'ResourceDashboard';

            addDoc.mockResolvedValueOnce({ id: 'test-doc-id' });

            await monitoringService.trackUserInteraction(component);

            expect(addDoc).toHaveBeenCalledWith(
                expect.anything(),
                expect.objectContaining({
                    type: 'user_interaction',
                    data: expect.objectContaining({
                        component
                    })
                })
            );
        });

        it('should track report generation', async () => {
            const reportType = 'academic';

            addDoc.mockResolvedValueOnce({ id: 'test-doc-id' });

            await monitoringService.trackReportGeneration(reportType);

            expect(addDoc).toHaveBeenCalledWith(
                expect.anything(),
                expect.objectContaining({
                    type: 'report_generation',
                    data: expect.objectContaining({
                        type: reportType
                    })
                })
            );
        });
    });
});
