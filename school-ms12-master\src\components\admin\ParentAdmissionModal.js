import React, { useState, useEffect } from 'react';
import { View, ScrollView, StyleSheet, Image, Alert } from 'react-native';
import {
  Portal,
  Modal,
  Title,
  Text,
  Button,
  IconButton,
  Surface,
  TextInput,
  ActivityIndicator,
  Divider,
} from 'react-native-paper';
import { LinearGradient } from 'expo-linear-gradient';
import * as ImagePicker from 'expo-image-picker';
import { db, auth } from '../../config/firebase';
import { collection, doc, setDoc, serverTimestamp, addDoc, updateDoc } from 'firebase/firestore';
import CustomInput from '../../components/common/CustomInput';
import VerificationStatus from '../../components/common/VerificationStatus';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import * as Animatable from 'react-native-animatable';
import EmailVerificationService from '../../services/EmailVerificationService';
import EmailNotificationService from '../../services/EmailNotificationService';
import UserRegistrationService from '../../services/UserRegistrationService';
import ActivityService from '../../services/ActivityService';
import EventService, { EVENTS } from '../../services/EventService';
import CloudinaryService from '../../services/CloudinaryService';

// Accept language props instead of using useLanguage hook directly
const ParentAdmissionModal = ({
  visible,
  onClose,
  parentEmail = '',
  translate = (key) => key, // Default function that returns the key if no translation is provided
  isRTL = false
}) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [profileImage, setProfileImage] = useState(null);
  const [kebeleIdImage, setKebeleIdImage] = useState(null);
  const [emailVerified, setEmailVerified] = useState(false);
  const [verificationSent, setVerificationSent] = useState(false);
  const [createdUser, setCreatedUser] = useState(null);
  const [showSuccessDialog, setShowSuccessDialog] = useState(false);

  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: parentEmail || '',
    phone: '',
    alternatePhone: '',
    address: {
      street: '',
      city: 'Asella',
      state: 'Oromia',
      country: 'Ethiopia',
    },
    occupation: '',
    workAddress: '',
    workPhone: '',
    relationship: 'father',
    emergencyContact: false,
    children: [],
    preferredContactMethod: 'email',
    notificationPreferences: {
      attendance: true,
      grades: true,
      behavior: true,
      announcements: true,
      events: true,
    },
    status: 'active',
    password: '1234qwer', // Default password
    role: 'parent',
    imageUrl: '',
    kebeleIdUrl: '',
  });

  useEffect(() => {
    if (parentEmail) {
      setFormData(prev => ({ ...prev, email: parentEmail }));
    }
  }, [parentEmail]);

  const pickImage = async (type) => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 1,
      });

      if (!result.canceled) {
        const selectedImage = result.assets[0];
        if (type === 'profile') {
          setProfileImage(selectedImage.uri);
        } else if (type === 'kebeleId') {
          setKebeleIdImage(selectedImage.uri);
        }
      }
    } catch (error) {
      console.error('Error picking image:', error);
      setError('Failed to pick image');
    }
  };

  const uploadImage = async (uri, folder) => {
    if (!uri) return null;

    try {
      // Upload image to Cloudinary
      const result = await CloudinaryService.uploadFromUri(uri, {
        folder: folder,
        tags: ['profile', 'parent'],
        publicId: `parent_${formData.email.replace('@', '_')}`
      });

      return result.url;
    } catch (error) {
      console.error('Error uploading image:', error);
      throw new Error('Failed to upload image');
    }
  };

  const handleSubmit = async () => {
    try {
      setLoading(true);
      setError(null);

      if (!formData.firstName || !formData.lastName || !formData.email || !formData.password) {
        setError('Please fill in all required fields');
        setLoading(false);
        return;
      }

      // Upload images if selected
      let imageUrl = null;
      let kebeleIdUrl = null;

      if (profileImage) {
        imageUrl = await uploadImage(
          profileImage,
          'parents/profile_images'
        );
      }

      if (kebeleIdImage) {
        kebeleIdUrl = await uploadImage(
          kebeleIdImage,
          'parents/documents'
        );
      }

      // Use UserRegistrationService imported at the top of the file

      // Create user in Firebase Auth using adminCreateUser to avoid logging out the admin
      const result = await UserRegistrationService.adminCreateUser(
        formData.email,
        formData.password,
        'parent',
        {
          firstName: formData.firstName,
          lastName: formData.lastName,
          displayName: `${formData.firstName} ${formData.lastName}`,
          emailVerified: true // Auto-verify in development mode
        }
      );

      if (!result.success) {
        throw new Error(result.error || 'Failed to create parent account');
      }

      const userCredential = { user: result.user };

      // Store the created user for verification
      setCreatedUser(userCredential.user);

      // In development mode, auto-verify the email
      setEmailVerified(true);
      setVerificationSent(true);

      // Log verification email sent
      try {
        await addDoc(collection(db, 'verification_logs'), {
          userId: userCredential.user.uid,
          email: userCredential.user.email,
          type: 'parent_registration',
          timestamp: serverTimestamp(),
          status: 'sent'
        });

        // Log activity using ActivityService
        await ActivityService.logActivity({
          type: 'parent_registration',
          userId: userCredential.user.uid,
          performedBy: auth.currentUser?.uid || 'admin',
          title: 'Parent Registration',
          description: `Parent ${formData.firstName} ${formData.lastName} registered`,
          details: {
            parentName: `${formData.firstName} ${formData.lastName}`,
            parentEmail: formData.email
          }
        });
      } catch (logError) {
        console.error('Error logging verification:', logError);
        // Continue even if logging fails
      }

      const parentDoc = {
        ...formData,
        uid: userCredential.user.uid,
        imageUrl: imageUrl || formData.imageUrl,
        kebeleIdUrl: kebeleIdUrl || formData.kebeleIdUrl,
        emailVerified: false,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      };

      delete parentDoc.password;

      // Use the UID as the document ID
      await setDoc(doc(db, 'users', userCredential.user.uid), parentDoc);
      const newParent = { id: userCredential.user.uid, ...parentDoc };

      // Show success dialog if email is verified
      if (emailVerified) {
        // Emit event with the new parent data
        EventService.emit(EVENTS.PARENT_ADDED, newParent);
        setShowSuccessDialog(true);
      }

      // Show success message
      setError(null);
    } catch (error) {
      console.error('Error adding parent:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleVerificationSent = () => {
    setVerificationSent(true);
  };

  const handleVerificationStatusChange = (status) => {
    setEmailVerified(status);

    // Update the user document if verified
    if (status && createdUser) {
      const userRef = doc(db, 'users', createdUser.uid);
      setDoc(userRef, {
        emailVerified: true,
        updatedAt: serverTimestamp()
      }, { merge: true }).catch(error => {
        console.error('Error updating email verification status:', error);
      });
    }
  };

  const handleFinish = () => {
    if (createdUser) {
      const newParent = {
        id: createdUser.uid,
        ...formData,
        emailVerified: true,
        uid: createdUser.uid
      };

      // Emit event with the new parent data
      EventService.emit(EVENTS.PARENT_ADDED, newParent);
    }

    // Close the modal
    setShowSuccessDialog(false);
    onClose();
  };

  const resetForm = () => {
    setFormData({
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      alternatePhone: '',
      address: {
        street: '',
        city: 'Asella',
        state: 'Oromia',
        country: 'Ethiopia',
      },
      occupation: '',
      workAddress: '',
      workPhone: '',
      relationship: 'father',
      emergencyContact: false,
      children: [],
      preferredContactMethod: 'email',
      notificationPreferences: {
        attendance: true,
        grades: true,
        behavior: true,
        announcements: true,
        events: true,
      },
      status: 'active',
      password: '1234qwer',
      role: 'parent',
      imageUrl: '',
      kebeleIdUrl: '',
    });
    setProfileImage(null);
    setKebeleIdImage(null);
    setEmailVerified(false);
    setVerificationSent(false);
    setCreatedUser(null);
    setError(null);
  };

  return (
    <Portal>
      <Modal
        visible={visible}
        onDismiss={onClose}
        contentContainerStyle={styles.modalContainer}
      >
        <LinearGradient
          colors={['#1976d2', '#f5f5f5']}
          style={styles.headerGradient}
        >
          <View style={styles.header}>
            <Title style={styles.headerTitle}>
              {translate('parent.admission.title') || 'Add New Parent'}
            </Title>
            <IconButton
              icon="close"
              size={24}
              color="white"
              onPress={onClose}
            />
          </View>
        </LinearGradient>

        <ScrollView style={styles.scrollView}>
          <View style={styles.content}>
            {/* Profile Image */}
            <View style={styles.imageUploadContainer}>
              <Text style={styles.sectionTitle}>{translate('parent.admission.profileImage') || 'Profile Image'}</Text>
              {profileImage ? (
                <View style={styles.imagePreviewContainer}>
                  <Image source={{ uri: profileImage }} style={styles.previewImage} />
                  <IconButton
                    icon="close-circle"
                    size={24}
                    style={styles.removeImageButton}
                    onPress={() => setProfileImage(null)}
                  />
                </View>
              ) : (
                <Surface style={styles.imagePlaceholder}>
                  <MaterialCommunityIcons name="account" size={60} color="#ccc" />
                </Surface>
              )}
              <Button
                mode="contained"
                onPress={() => pickImage('profile')}
                style={styles.uploadButton}
                icon="camera"
              >
                {profileImage ? (translate('parent.admission.changePhoto') || 'Change Photo') : (translate('parent.admission.addPhoto') || 'Add Photo')}
              </Button>
            </View>

            <Divider style={styles.divider} />

            {/* Basic Information */}
            <Text style={styles.sectionTitle}>{translate('parent.admission.basicInfo') || 'Basic Information'}</Text>
            <CustomInput
              label={translate('parent.admission.firstName') || 'First Name'}
              value={formData.firstName}
              onChangeText={(text) => setFormData({ ...formData, firstName: text })}
              required
            />
            <CustomInput
              label={translate('parent.admission.lastName') || 'Last Name'}
              value={formData.lastName}
              onChangeText={(text) => setFormData({ ...formData, lastName: text })}
              required
            />

            {/* Email and Verification */}
            <View style={styles.emailVerificationContainer}>
              <CustomInput
                label={translate('parent.admission.email') || 'Email'}
                value={formData.email}
                onChangeText={(text) => {
                  setFormData({ ...formData, email: text });
                  setEmailVerified(false);
                  setVerificationSent(false);
                }}
                keyboardType="email-address"
                required
                disabled={verificationSent || emailVerified}
              />
            </View>

            {formData.email && (
              <Surface style={styles.verificationContainer}>
                <VerificationStatus
                  email={formData.email}
                  isVerified={emailVerified}
                  verificationSent={verificationSent}
                  onVerificationSent={handleVerificationSent}
                  onVerificationStatusChange={handleVerificationStatusChange}
                  userType="parent"
                />

                {verificationSent && !emailVerified && (
                  <Text style={styles.verificationMessage}>
                    {translate('verification.checkEmail') || 'Please check your email and verify your account before continuing.'}
                  </Text>
                )}
              </Surface>
            )}

            <CustomInput
              label={translate('parent.admission.password') || 'Password'}
              value={formData.password}
              onChangeText={(text) => setFormData({ ...formData, password: text })}
              secureTextEntry
              required
            />

            <CustomInput
              label={translate('parent.admission.phone') || 'Phone Number'}
              value={formData.phone}
              onChangeText={(text) => setFormData({ ...formData, phone: text })}
              keyboardType="phone-pad"
            />

            <CustomInput
              label={translate('parent.admission.alternatePhone') || 'Alternate Phone'}
              value={formData.alternatePhone}
              onChangeText={(text) => setFormData({ ...formData, alternatePhone: text })}
              keyboardType="phone-pad"
            />

            <CustomInput
              label={translate('parent.admission.relationship') || 'Relationship to Student'}
              value={formData.relationship}
              onChangeText={(text) => setFormData({ ...formData, relationship: text })}
            />

            <Divider style={styles.divider} />

            {/* Address Information */}
            <Text style={styles.sectionTitle}>{translate('parent.admission.addressInfo') || 'Address Information'}</Text>
            <CustomInput
              label={translate('parent.admission.street') || 'Street Address'}
              value={formData.address.street}
              onChangeText={(text) => setFormData({
                ...formData,
                address: { ...formData.address, street: text }
              })}
            />
            <CustomInput
              label={translate('parent.admission.city') || 'City'}
              value={formData.address.city}
              onChangeText={(text) => setFormData({
                ...formData,
                address: { ...formData.address, city: text }
              })}
            />
            <CustomInput
              label={translate('parent.admission.state') || 'State/Region'}
              value={formData.address.state}
              onChangeText={(text) => setFormData({
                ...formData,
                address: { ...formData.address, state: text }
              })}
            />
            <CustomInput
              label={translate('parent.admission.country') || 'Country'}
              value={formData.address.country}
              onChangeText={(text) => setFormData({
                ...formData,
                address: { ...formData.address, country: text }
              })}
            />

            <Divider style={styles.divider} />

            {/* Work Information */}
            <Text style={styles.sectionTitle}>{translate('parent.admission.workInfo') || 'Work Information'}</Text>
            <CustomInput
              label={translate('parent.admission.occupation') || 'Occupation'}
              value={formData.occupation}
              onChangeText={(text) => setFormData({ ...formData, occupation: text })}
            />
            <CustomInput
              label={translate('parent.admission.workAddress') || 'Work Address'}
              value={formData.workAddress}
              onChangeText={(text) => setFormData({ ...formData, workAddress: text })}
              multiline
            />
            <CustomInput
              label={translate('parent.admission.workPhone') || 'Work Phone'}
              value={formData.workPhone}
              onChangeText={(text) => setFormData({ ...formData, workPhone: text })}
              keyboardType="phone-pad"
            />

            <Divider style={styles.divider} />

            {/* ID Document */}
            <View style={styles.imageUploadContainer}>
              <Text style={styles.sectionTitle}>{translate('parent.admission.kebeleId') || 'Kebele ID'}</Text>
              {kebeleIdImage ? (
                <View style={styles.imagePreviewContainer}>
                  <Image source={{ uri: kebeleIdImage }} style={styles.previewImage} />
                  <IconButton
                    icon="close-circle"
                    size={24}
                    style={styles.removeImageButton}
                    onPress={() => setKebeleIdImage(null)}
                  />
                </View>
              ) : (
                <Surface style={styles.imagePlaceholder}>
                  <MaterialCommunityIcons name="card-account-details" size={60} color="#ccc" />
                </Surface>
              )}
              <Button
                mode="contained"
                onPress={() => pickImage('kebeleId')}
                style={styles.uploadButton}
                icon="camera"
              >
                {kebeleIdImage ? (translate('parent.admission.changeId') || 'Change ID') : (translate('parent.admission.addId') || 'Add ID')}
              </Button>
            </View>

            {error && (
              <Surface style={styles.errorContainer}>
                <Text style={styles.errorText}>{error}</Text>
              </Surface>
            )}

            <View style={styles.buttonContainer}>
              <Button
                mode="outlined"
                onPress={onClose}
                style={styles.cancelButton}
              >
                {translate('common.cancel') || 'Cancel'}
              </Button>
              <Button
                mode="contained"
                onPress={handleSubmit}
                style={styles.submitButton}
                loading={loading}
                disabled={loading || (verificationSent && !emailVerified)}
              >
                {translate('parent.admission.add') || 'Add Parent'}
              </Button>
            </View>
          </View>
        </ScrollView>

        {/* Success Dialog */}
        <Portal>
          <Modal
            visible={showSuccessDialog}
            onDismiss={handleFinish}
            contentContainerStyle={styles.successModalContainer}
          >
            <Animatable.View animation="bounceIn" duration={1000} style={styles.successContent}>
              <MaterialCommunityIcons name="check-circle" size={80} color="#4CAF50" style={styles.successIcon} />
              <Title style={styles.successTitle}>{translate('parent.admission.success') || 'Parent Added Successfully!'}</Title>
              <Text style={styles.successText}>
                {translate('parent.admission.successMessage') || 'The parent has been successfully added to the system.'}
              </Text>
              <Button
                mode="contained"
                onPress={handleFinish}
                style={styles.successButton}
              >
                {translate('common.continue') || 'Continue'}
              </Button>
            </Animatable.View>
          </Modal>
        </Portal>
      </Modal>
    </Portal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    backgroundColor: 'white',
    margin: 20,
    borderRadius: 8,
    maxHeight: '90%',
    width: '90%',
    alignSelf: 'center',
  },
  headerGradient: {
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  headerTitle: {
    color: 'white',
    fontSize: 20,
    fontWeight: 'bold',
  },
  scrollView: {
    maxHeight: '80%',
  },
  content: {
    padding: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginVertical: 8,
  },
  divider: {
    marginVertical: 16,
  },
  imageUploadContainer: {
    alignItems: 'center',
    marginVertical: 16,
  },
  imagePreviewContainer: {
    position: 'relative',
    marginVertical: 8,
  },
  previewImage: {
    width: 150,
    height: 150,
    borderRadius: 75,
  },
  removeImageButton: {
    position: 'absolute',
    top: -10,
    right: -10,
    backgroundColor: 'white',
  },
  imagePlaceholder: {
    width: 150,
    height: 150,
    borderRadius: 75,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    marginVertical: 8,
  },
  uploadButton: {
    marginTop: 8,
  },
  emailVerificationContainer: {
    marginVertical: 8,
  },
  verificationContainer: {
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
    backgroundColor: '#f5f5f5',
  },
  verificationMessage: {
    marginTop: 8,
    fontStyle: 'italic',
    color: '#555',
  },
  errorContainer: {
    backgroundColor: '#ffebee',
    padding: 16,
    borderRadius: 8,
    marginVertical: 16,
  },
  errorText: {
    color: '#d32f2f',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 24,
    marginBottom: 16,
  },
  cancelButton: {
    flex: 1,
    marginRight: 8,
  },
  submitButton: {
    flex: 2,
    marginLeft: 8,
  },
  successModalContainer: {
    backgroundColor: 'white',
    margin: 20,
    borderRadius: 8,
    padding: 24,
    alignItems: 'center',
    maxWidth: '80%',
    alignSelf: 'center',
  },
  successContent: {
    alignItems: 'center',
  },
  successIcon: {
    marginBottom: 16,
  },
  successTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#4CAF50',
    marginBottom: 16,
    textAlign: 'center',
  },
  successText: {
    textAlign: 'center',
    marginBottom: 24,
    fontSize: 16,
  },
  successButton: {
    paddingHorizontal: 32,
  },
});

export default ParentAdmissionModal;
