import React, { useState, useEffect, useRef } from 'react';
import { View, ScrollView, StyleSheet, TouchableOpacity, Animated, RefreshControl } from 'react-native';
import { Card, Title, Text, Chip, Divider, useTheme, IconButton, ActivityIndicator, Searchbar, SegmentedButtons, Surface, Badge, DataTable } from 'react-native-paper';
import { db, auth } from '../../config/firebase';
import { collection, query, getDocs, where, doc, getDoc, orderBy, onSnapshot } from 'firebase/firestore';
import { useNavigation } from '@react-navigation/native';
import { useLanguage } from '../../context/LanguageContext';
import { useAuth } from '../../context/AuthContext';
import * as Animatable from 'react-native-animatable';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { Calendar } from 'react-native-calendars';
import TeacherScreenWrapper from '../../components/common/TeacherScreenWrapper';

const TeacherClassSchedule = () => {
  const navigation = useNavigation();
  // No theme needed
  const { translate, language, isRTL } = useLanguage();
  const { user } = useAuth();

  // State variables
  const [schedule, setSchedule] = useState([]);
  const [classes, setClasses] = useState([]);
  const [subjects, setSubjects] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState(null);
  const [viewMode, setViewMode] = useState('week'); // 'week', 'day', 'list'
  const [selectedDay, setSelectedDay] = useState('');
  const [filterDay, setFilterDay] = useState('all');
  const [showFilters, setShowFilters] = useState(true);
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [activeSidebarItem, setActiveSidebarItem] = useState('TeacherClassSchedule');

  // Animation refs
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const drawerAnim = useRef(new Animated.Value(-300)).current;
  const backdropFadeAnim = useRef(new Animated.Value(0)).current;

  // Days of the week
  const days = [
    'Monday',
    'Tuesday',
    'Wednesday',
    'Thursday',
    'Friday',
    'Saturday',
    'Sunday',
  ];

  // Time slots for 40-minute periods
  const morningTimeSlots = [
    { period: 1, start: '08:00', end: '08:40', session: 'morning' },
    { period: 2, start: '08:45', end: '09:25', session: 'morning' },
    { period: 3, start: '09:30', end: '10:10', session: 'morning' },
    { period: 4, start: '10:15', end: '10:55', session: 'morning' },
    { period: 5, start: '11:00', end: '11:40', session: 'morning' },
    { period: 6, start: '11:45', end: '12:25', session: 'morning' },
  ];

  const afternoonTimeSlots = [
    { period: 1, start: '12:30', end: '13:10', session: 'afternoon' },
    { period: 2, start: '13:15', end: '13:55', session: 'afternoon' },
    { period: 3, start: '14:00', end: '14:40', session: 'afternoon' },
    { period: 4, start: '14:45', end: '15:25', session: 'afternoon' },
    { period: 5, start: '15:30', end: '16:10', session: 'afternoon' },
    { period: 6, start: '16:15', end: '16:55', session: 'afternoon' },
  ];

  // Combined time slots for display
  const allTimeSlots = [...morningTimeSlots, ...afternoonTimeSlots];

  useEffect(() => {
    // Hide header in this screen as we're using our own
    navigation.setOptions({
      headerShown: false,
    });

    // Set today's day of week for initial view
    const today = new Date();
    const dayOfWeek = days[today.getDay() === 0 ? 6 : today.getDay() - 1]; // Convert Sunday (0) to 6
    setSelectedDay(dayOfWeek);

    // Start fade-in animation
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 1000,
      useNativeDriver: true,
    }).start();

    // Fetch initial data
    fetchSchedule();
    fetchClasses();
    fetchSubjects();
  }, [navigation]);

  // Toggle drawer function
  const toggleDrawer = () => {
    if (drawerOpen) {
      // Close drawer
      Animated.parallel([
        Animated.timing(drawerAnim, {
          toValue: -300,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(backdropFadeAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        })
      ]).start(() => setDrawerOpen(false));
    } else {
      // Open drawer
      setDrawerOpen(true);
      Animated.parallel([
        Animated.timing(drawerAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(backdropFadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        })
      ]).start();
    }
  };

  // Fetch teacher's schedule
  const fetchSchedule = async () => {
    try {
      setLoading(true);
      setError(null);

      // Create a query to get all timetable entries for this teacher
      const timetablesRef = collection(db, 'timetables');
      const q = query(
        timetablesRef,
        where('teacherId', '==', user.uid),
        where('published', '==', true)
      );

      // Set up real-time listener
      const unsubscribe = onSnapshot(q, (snapshot) => {
        const scheduleData = [];
        snapshot.forEach((doc) => {
          scheduleData.push({ id: doc.id, ...doc.data() });
        });

        setSchedule(scheduleData);
        setLoading(false);
        setRefreshing(false);
      }, (error) => {
        console.error('Error fetching schedule:', error);
        setError('Failed to load schedule. Please try again.');
        setLoading(false);
        setRefreshing(false);
      });

      // Return unsubscribe function for cleanup
      return unsubscribe;
    } catch (error) {
      console.error('Error setting up schedule listener:', error);
      setError('Failed to load schedule. Please try again.');
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Fetch classes data
  const fetchClasses = async () => {
    try {
      const classesRef = collection(db, 'classes');
      const querySnapshot = await getDocs(classesRef);

      const classData = [];
      querySnapshot.forEach((doc) => {
        classData.push({ id: doc.id, ...doc.data() });
      });

      setClasses(classData);
    } catch (error) {
      console.error('Error fetching classes:', error);
    }
  };

  // Fetch subjects data
  const fetchSubjects = async () => {
    try {
      const subjectsRef = collection(db, 'subjects');
      const querySnapshot = await getDocs(subjectsRef);

      const subjectData = [];
      querySnapshot.forEach((doc) => {
        subjectData.push({ id: doc.id, ...doc.data() });
      });

      setSubjects(subjectData);
    } catch (error) {
      console.error('Error fetching subjects:', error);
    }
  };

  // Handle refresh
  const onRefresh = async () => {
    setRefreshing(true);
    await fetchSchedule();
  };

  // Toggle filters visibility
  const toggleFilters = () => {
    setShowFilters(!showFilters);
  };

  // Toggle view mode
  const toggleViewMode = (mode) => {
    setViewMode(mode);
  };

  // Get class name by ID
  const getClassName = (classId) => {
    const classObj = classes.find(c => c.id === classId);
    return classObj ? classObj.name : 'Unknown Class';
  };

  // Get subject name by ID
  const getSubjectName = (subjectId) => {
    const subject = subjects.find(s => s.id === subjectId);
    return subject ? subject.name : 'Unknown Subject';
  };

  // Filter schedule by day
  const getScheduleForDay = (day) => {
    return schedule.filter(item => item.day === day)
      .sort((a, b) => {
        // First sort by session (morning first)
        if (a.session !== b.session) {
          return a.session === 'morning' ? -1 : 1;
        }
        // Then sort by start time
        return a.startTime.localeCompare(b.startTime);
      });
  };

  // Get all schedule entries for the current view
  const getFilteredSchedule = () => {
    if (filterDay !== 'all') {
      return schedule.filter(item => item.day === filterDay);
    }
    return schedule;
  };

  // Format time for display
  const formatTime = (time) => {
    const [hours, minutes] = time.split(':');
    const date = new Date();
    date.setHours(parseInt(hours), parseInt(minutes));
    return date.toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit' });
  };

  // Render loading state
  const renderLoading = () => (
    <View style={styles.loadingContainer}>
      <ActivityIndicator size="large" color={'#1976d2'} />
      <Text style={styles.loadingText}>{translate('common.loading') || "Loading..."}</Text>
    </View>
  );

  // Render error state
  const renderError = () => (
    <View style={styles.errorContainer}>
      <IconButton icon="alert-circle" size={50} color={'#B00020'} />
      <Text style={styles.errorText}>{error}</Text>
      <TouchableOpacity style={styles.retryButton} onPress={fetchSchedule}>
        <Text style={styles.retryButtonText}>{translate('common.retry') || "Retry"}</Text>
      </TouchableOpacity>
    </View>
  );

  // Render empty state
  const renderEmpty = () => (
    <View style={styles.emptyContainer}>
      <IconButton icon="calendar-blank" size={50} color={'#9e9e9e'} />
      <Text style={styles.emptyText}>{translate('schedule.noSchedule') || "No schedule found"}</Text>
      <Text style={styles.emptySubtext}>{translate('schedule.checkLater') || "Please check back later"}</Text>
    </View>
  );

  // Return the component with TeacherScreenWrapper
  return (
    <TeacherScreenWrapper
      title={translate('schedule.title') || "Class Schedule"}
      showBack={true}
    >
      {/* Main Content */}
      <Animated.View
        style={[
          styles.content,
          { opacity: fadeAnim }
        ]}
      >
        {/* Placeholder for filter controls */}
        <Animatable.View animation="fadeIn" duration={500}>
          <View style={styles.filterContainer}>
            <SegmentedButtons
              value={viewMode}
              onValueChange={toggleViewMode}
              buttons={[
                { value: 'week', icon: 'calendar-week', label: translate('schedule.weekly') || 'Weekly' },
                { value: 'day', icon: 'calendar-today', label: translate('schedule.daily') || 'Daily' },
                { value: 'list', icon: 'format-list-bulleted', label: translate('schedule.list') || 'List' },
              ]}
              style={styles.viewToggle}
            />
            <IconButton
              icon={showFilters ? "filter-variant" : "filter-variant-plus"}
              size={24}
              style={styles.filterToggleButton}
              onPress={toggleFilters}
              color={'#1976d2'}
            />
          </View>
        </Animatable.View>

        {/* Placeholder for day selection in daily view */}
        {viewMode === 'day' && (
          <Animatable.View animation="fadeIn" duration={500}>
            <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.daySelector}>
              {days.map(day => (
                <Chip
                  key={day}
                  selected={selectedDay === day}
                  onPress={() => setSelectedDay(day)}
                  style={styles.dayChip}
                  selectedColor={'#1976d2'}
                >
                  {day}
                </Chip>
              ))}
            </ScrollView>
          </Animatable.View>
        )}

        {/* Main content area */}
        <ScrollView
          style={styles.scrollView}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={['#1976d2']}
              tintColor={'#1976d2'}
            />
          }
        >
          {loading ? (
            renderLoading()
          ) : error ? (
            renderError()
          ) : schedule.length === 0 ? (
            renderEmpty()
          ) : viewMode === 'week' ? (
            // Weekly View
            <Animatable.View animation="fadeIn" duration={500} delay={200}>
              {/* Morning Session */}
              <Card style={styles.sessionCard}>
                <Card.Content>
                  <View style={styles.sessionHeader}>
                    <Title style={styles.sessionTitle}>
                      {translate('schedule.morningSession') || 'Morning Session (8:00 - 12:30)'}
                    </Title>
                    <IconButton icon="weather-sunny" size={24} color={'#1976d2'} />
                  </View>

                  <Surface style={styles.timetableSurface}>
                    <View style={styles.scrollableTableContainer}>
                      <ScrollView horizontal>
                        <View>
                          <DataTable style={styles.timetableTable}>
                            <DataTable.Header style={styles.tableHeader}>
                              <DataTable.Title style={styles.periodColumn}>Period</DataTable.Title>
                              {days.map((day) => (
                                <DataTable.Title key={day} style={styles.dayColumn}>{day}</DataTable.Title>
                              ))}
                            </DataTable.Header>
                          </DataTable>

                          <ScrollView style={styles.tableScrollView} nestedScrollEnabled={true}>
                            <DataTable style={styles.timetableTable}>
                              {/* Render morning periods as rows */}
                              {morningTimeSlots.map((timeSlot) => (
                                <DataTable.Row key={timeSlot.period + '-morning'} style={styles.tableRow}>
                                  <DataTable.Cell style={styles.periodColumn}>
                                    <Text style={styles.periodText}>Period {timeSlot.period}</Text>
                                    <Text style={styles.timeText}>{timeSlot.start} - {timeSlot.end}</Text>
                                  </DataTable.Cell>

                                  {/* Render cells for each day */}
                                  {days.map((day) => {
                                    // Find schedule entry for this day and period
                                    const entry = schedule.find(e =>
                                      e.day === day &&
                                      e.startTime === timeSlot.start &&
                                      e.endTime === timeSlot.end &&
                                      e.session === 'morning'
                                    );

                                    return (
                                      <DataTable.Cell key={day} style={styles.dayColumn}>
                                        {entry ? (
                                          <View style={[styles.scheduleCell, { borderLeftColor: '#2196F3' }]}>
                                            <Text style={styles.classText}>
                                              {getClassName(entry.classId)} - {entry.sectionName}
                                            </Text>
                                            <Text style={styles.subjectText}>
                                              {getSubjectName(entry.subjectId)}
                                            </Text>
                                            <Text style={styles.roomText}>Room: {entry.roomNumber}</Text>
                                          </View>
                                        ) : (
                                          <View style={styles.emptyCell}>
                                            <Text style={styles.emptyCellText}>Free</Text>
                                          </View>
                                        )}
                                      </DataTable.Cell>
                                    );
                                  })}
                                </DataTable.Row>
                              ))}
                            </DataTable>
                          </ScrollView>
                        </View>
                      </ScrollView>
                    </View>
                  </Surface>
                </Card.Content>
              </Card>

              {/* Afternoon Session */}
              <Card style={styles.sessionCard}>
                <Card.Content>
                  <View style={styles.sessionHeader}>
                    <Title style={styles.sessionTitle}>
                      {translate('schedule.afternoonSession') || 'Afternoon Session (12:30 - 5:00)'}
                    </Title>
                    <IconButton icon="weather-night" size={24} color="#673AB7" />
                  </View>

                  <Surface style={styles.timetableSurface}>
                    <View style={styles.scrollableTableContainer}>
                      <ScrollView horizontal>
                        <View>
                          <DataTable style={styles.timetableTable}>
                            <DataTable.Header style={styles.tableHeader}>
                              <DataTable.Title style={styles.periodColumn}>Period</DataTable.Title>
                              {days.map((day) => (
                                <DataTable.Title key={day} style={styles.dayColumn}>{day}</DataTable.Title>
                              ))}
                            </DataTable.Header>
                          </DataTable>

                          <ScrollView style={styles.tableScrollView} nestedScrollEnabled={true}>
                            <DataTable style={styles.timetableTable}>
                              {/* Render afternoon periods as rows */}
                              {afternoonTimeSlots.map((timeSlot) => (
                                <DataTable.Row key={timeSlot.period + '-afternoon'} style={styles.tableRow}>
                                  <DataTable.Cell style={styles.periodColumn}>
                                    <Text style={styles.periodText}>Period {timeSlot.period}</Text>
                                    <Text style={styles.timeText}>{timeSlot.start} - {timeSlot.end}</Text>
                                  </DataTable.Cell>

                                  {/* Render cells for each day */}
                                  {days.map((day) => {
                                    // Find schedule entry for this day and period
                                    const entry = schedule.find(e =>
                                      e.day === day &&
                                      e.startTime === timeSlot.start &&
                                      e.endTime === timeSlot.end &&
                                      e.session === 'afternoon'
                                    );

                                    return (
                                      <DataTable.Cell key={day} style={styles.dayColumn}>
                                        {entry ? (
                                          <View style={[styles.scheduleCell, { borderLeftColor: '#673AB7' }]}>
                                            <Text style={styles.classText}>
                                              {getClassName(entry.classId)} - {entry.sectionName}
                                            </Text>
                                            <Text style={styles.subjectText}>
                                              {getSubjectName(entry.subjectId)}
                                            </Text>
                                            <Text style={styles.roomText}>Room: {entry.roomNumber}</Text>
                                          </View>
                                        ) : (
                                          <View style={styles.emptyCell}>
                                            <Text style={styles.emptyCellText}>Free</Text>
                                          </View>
                                        )}
                                      </DataTable.Cell>
                                    );
                                  })}
                                </DataTable.Row>
                              ))}
                            </DataTable>
                          </ScrollView>
                        </View>
                      </ScrollView>
                    </View>
                  </Surface>
                </Card.Content>
              </Card>
            </Animatable.View>
          ) : viewMode === 'day' ? (
            // Daily View
            <Animatable.View animation="fadeIn" duration={500} delay={200}>
              <Card style={styles.dayCard}>
                <Card.Content>
                  <Title style={styles.dayTitle}>{selectedDay}</Title>

                  {getScheduleForDay(selectedDay).length === 0 ? (
                    <View style={styles.noDaySchedule}>
                      <IconButton icon="calendar-blank" size={40} color={'#9e9e9e'} />
                      <Text style={styles.noDayScheduleText}>
                        {translate('schedule.noClassesForDay') || `No classes scheduled for ${selectedDay}`}
                      </Text>
                    </View>
                  ) : (
                    <>
                      {/* Morning Classes */}
                      {getScheduleForDay(selectedDay).filter(item => item.session === 'morning').length > 0 && (
                        <View style={styles.sessionSection}>
                          <View style={styles.sessionDayHeader}>
                            <Text style={[styles.sessionDayTitle, { color: '#2196F3' }]}>
                              {translate('schedule.morningSession') || 'Morning Session'}
                            </Text>
                            <IconButton icon="weather-sunny" size={20} color="#2196F3" />
                          </View>

                          {getScheduleForDay(selectedDay)
                            .filter(item => item.session === 'morning')
                            .map(item => (
                              <Surface key={item.id} style={[styles.classItem, { borderLeftColor: '#2196F3' }]}>
                                <View style={styles.classItemHeader}>
                                  <Text style={styles.classItemTime}>
                                    {formatTime(item.startTime)} - {formatTime(item.endTime)}
                                  </Text>
                                  <Chip mode="outlined" style={styles.periodChip}>
                                    Period {item.periodNumber}
                                  </Chip>
                                </View>

                                <Text style={styles.classItemSubject}>
                                  {getSubjectName(item.subjectId)}
                                </Text>
                                <Text style={styles.classItemClass}>
                                  {getClassName(item.classId)} - {item.sectionName}
                                </Text>
                                <Text style={styles.classItemRoom}>
                                  Room: {item.roomNumber}
                                </Text>
                              </Surface>
                            ))}
                        </View>
                      )}

                      {/* Afternoon Classes */}
                      {getScheduleForDay(selectedDay).filter(item => item.session === 'afternoon').length > 0 && (
                        <View style={styles.sessionSection}>
                          <View style={styles.sessionDayHeader}>
                            <Text style={[styles.sessionDayTitle, { color: '#673AB7' }]}>
                              {translate('schedule.afternoonSession') || 'Afternoon Session'}
                            </Text>
                            <IconButton icon="weather-night" size={20} color="#673AB7" />
                          </View>

                          {getScheduleForDay(selectedDay)
                            .filter(item => item.session === 'afternoon')
                            .map(item => (
                              <Surface key={item.id} style={[styles.classItem, { borderLeftColor: '#673AB7' }]}>
                                <View style={styles.classItemHeader}>
                                  <Text style={styles.classItemTime}>
                                    {formatTime(item.startTime)} - {formatTime(item.endTime)}
                                  </Text>
                                  <Chip mode="outlined" style={styles.periodChip}>
                                    Period {item.periodNumber}
                                  </Chip>
                                </View>

                                <Text style={styles.classItemSubject}>
                                  {getSubjectName(item.subjectId)}
                                </Text>
                                <Text style={styles.classItemClass}>
                                  {getClassName(item.classId)} - {item.sectionName}
                                </Text>
                                <Text style={styles.classItemRoom}>
                                  Room: {item.roomNumber}
                                </Text>
                              </Surface>
                            ))}
                        </View>
                      )}
                    </>
                  )}
                </Card.Content>
              </Card>
            </Animatable.View>
          ) : (
            // List View
            <Animatable.View animation="fadeIn" duration={500} delay={200}>
              <Card style={styles.listCard}>
                <Card.Content>
                  <Title style={styles.listTitle}>
                    {translate('schedule.allClasses') || 'All Classes'}
                  </Title>

                  <DataTable>
                    <DataTable.Header>
                      <DataTable.Title>Day</DataTable.Title>
                      <DataTable.Title>Time</DataTable.Title>
                      <DataTable.Title>Subject</DataTable.Title>
                      <DataTable.Title>Class</DataTable.Title>
                      <DataTable.Title>Room</DataTable.Title>
                    </DataTable.Header>

                    {getFilteredSchedule().map(item => (
                      <DataTable.Row key={item.id}>
                        <DataTable.Cell>{item.day}</DataTable.Cell>
                        <DataTable.Cell>
                          <View>
                            <Text>{formatTime(item.startTime)} - {formatTime(item.endTime)}</Text>
                            <Chip
                              mode="outlined"
                              style={{
                                height: 20,
                                backgroundColor: item.session === 'morning' ? '#E3F2FD' : '#EDE7F6',
                              }}
                              textStyle={{ fontSize: 10 }}
                            >
                              {item.session === 'morning' ? 'Morning' : 'Afternoon'}
                            </Chip>
                          </View>
                        </DataTable.Cell>
                        <DataTable.Cell>{getSubjectName(item.subjectId)}</DataTable.Cell>
                        <DataTable.Cell>{getClassName(item.classId)} - {item.sectionName}</DataTable.Cell>
                        <DataTable.Cell>{item.roomNumber}</DataTable.Cell>
                      </DataTable.Row>
                    ))}
                  </DataTable>
                </Card.Content>
              </Card>
            </Animatable.View>
          )}
        </ScrollView>
      </Animated.View>
    </TeacherScreenWrapper>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  filterContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  viewToggle: {
    flex: 1,
  },
  filterToggleButton: {
    marginLeft: 8,
  },
  daySelector: {
    marginBottom: 16,
  },
  dayChip: {
    marginRight: 8,
  },
  scrollView: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    marginTop: 10,
    fontSize: 16,
    color: '#d32f2f',
    textAlign: 'center',
  },
  retryButton: {
    marginTop: 20,
    padding: 10,
    backgroundColor: '#2196F3',
    borderRadius: 5,
  },
  retryButtonText: {
    color: 'white',
    fontWeight: 'bold',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    marginTop: 10,
    fontSize: 18,
    fontWeight: 'bold',
    color: '#666',
  },
  emptySubtext: {
    marginTop: 5,
    fontSize: 14,
    color: '#999',
    textAlign: 'center',
  },
  // Weekly View Styles
  sessionCard: {
    marginBottom: 16,
    borderRadius: 8,
    elevation: 4,
  },
  sessionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sessionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  timetableSurface: {
    borderRadius: 8,
    elevation: 2,
    marginBottom: 16,
  },
  scrollableTableContainer: {
    flexDirection: 'column',
    width: '100%',
  },
  timetableContainer: {
    marginBottom: 16,
  },
  timetableTable: {
    backgroundColor: '#fff',
  },
  tableHeader: {
    backgroundColor: '#f5f5f5',
  },
  tableScrollView: {
    maxHeight: 300,
  },
  tableRow: {
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  periodColumn: {
    width: 100,
    backgroundColor: '#f0f0f0',
  },
  dayColumn: {
    width: 180,
    padding: 5,
  },
  periodText: {
    fontWeight: 'bold',
    fontSize: 14,
  },
  timeText: {
    fontSize: 12,
    color: '#666',
  },
  scheduleCell: {
    padding: 10,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    minHeight: 100,
    elevation: 2,
    borderLeftWidth: 4,
  },
  classText: {
    fontWeight: 'bold',
    fontSize: 14,
    marginBottom: 2,
  },
  subjectText: {
    fontSize: 14,
    color: '#333',
    marginBottom: 2,
  },
  roomText: {
    fontSize: 12,
    color: '#666',
    marginBottom: 2,
  },
  emptyCell: {
    padding: 10,
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
    minHeight: 100,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyCellText: {
    color: '#999',
    fontStyle: 'italic',
  },
  // Daily View Styles
  dayCard: {
    marginBottom: 16,
    borderRadius: 8,
    elevation: 4,
  },
  dayTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
  },
  noDaySchedule: {
    padding: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  noDayScheduleText: {
    marginTop: 10,
    fontSize: 16,
    color: '#999',
    textAlign: 'center',
  },
  sessionSection: {
    marginBottom: 16,
  },
  sessionDayHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  sessionDayTitle: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  classItem: {
    padding: 16,
    marginBottom: 12,
    borderRadius: 8,
    elevation: 2,
    borderLeftWidth: 4,
  },
  classItemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  classItemTime: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  periodChip: {
    height: 24,
  },
  classItemSubject: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  classItemClass: {
    fontSize: 14,
    marginBottom: 4,
  },
  classItemRoom: {
    fontSize: 12,
    color: '#666',
  },
  // List View Styles
  listCard: {
    marginBottom: 16,
    borderRadius: 8,
    elevation: 4,
  },
  listTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
});

export default TeacherClassSchedule;
