/**
 * Utility functions for network operations
 */

/**
 * Check if the device has internet connectivity
 * @param {string} testUrl - URL to test connectivity against (defaults to Cloudinary API)
 * @param {number} timeout - Timeout in milliseconds
 * @returns {Promise<boolean>} - True if connected, false otherwise
 */
export const checkInternetConnectivity = async (testUrl = 'https://api.cloudinary.com', timeout = 5000) => {
  try {
    // Create an AbortController to handle timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);
    
    // Make a HEAD request to the test URL
    const response = await fetch(testUrl, {
      method: 'HEAD',
      signal: controller.signal
    });
    
    // Clear the timeout
    clearTimeout(timeoutId);
    
    // Return true if the response is ok
    return response.ok;
  } catch (error) {
    console.error('Network connectivity check failed:', error);
    return false;
  }
};

/**
 * Check if a specific API is available
 * @param {string} apiUrl - API URL to test
 * @param {number} timeout - Timeout in milliseconds
 * @returns {Promise<{available: boolean, status: number|null, error: string|null}>} - API availability status
 */
export const checkApiAvailability = async (apiUrl, timeout = 5000) => {
  try {
    // Create an AbortController to handle timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);
    
    // Make a HEAD request to the API URL
    const response = await fetch(apiUrl, {
      method: 'HEAD',
      signal: controller.signal
    });
    
    // Clear the timeout
    clearTimeout(timeoutId);
    
    // Return the API availability status
    return {
      available: response.ok,
      status: response.status,
      error: null
    };
  } catch (error) {
    // Handle different error types
    if (error.name === 'AbortError') {
      return {
        available: false,
        status: null,
        error: 'Request timed out'
      };
    } else if (error.message.includes('Network request failed')) {
      return {
        available: false,
        status: null,
        error: 'Network request failed'
      };
    } else {
      return {
        available: false,
        status: null,
        error: error.message
      };
    }
  }
};

/**
 * Check if Cloudinary API is available
 * @param {string} cloudName - Cloudinary cloud name
 * @param {number} timeout - Timeout in milliseconds
 * @returns {Promise<{available: boolean, status: number|null, error: string|null}>} - Cloudinary API availability status
 */
export const checkCloudinaryAvailability = async (cloudName, timeout = 5000) => {
  const cloudinaryApiUrl = `https://api.cloudinary.com/v1_1/${cloudName}/ping`;
  return await checkApiAvailability(cloudinaryApiUrl, timeout);
};

export default {
  checkInternetConnectivity,
  checkApiAvailability,
  checkCloudinaryAvailability
};
