import { auth, db } from '../config/firebase';
import {
  createUserWithEmailAndPassword,
  updateProfile,
  sendEmailVerification,
  signInWithEmailAndPassword,
  signOut
} from 'firebase/auth';
import {
  doc,
  setDoc,
  collection,
  serverTimestamp,
  addDoc,
  getDoc,
  updateDoc
} from 'firebase/firestore';
import ActivityService from './ActivityService';
import EmailVerificationService from './EmailVerificationService';
import EmailNotificationService from './EmailNotificationService';

class UserRegistrationService {
  /**
   * Register a new user with email verification
   * @param {string} email - User email
   * @param {string} password - User password
   * @param {string} role - User role (student, parent, teacher, admin)
   * @param {object} userData - User data (firstName, lastName, etc.)
   * @param {boolean} sendVerificationEmail - Whether to send verification email
   * @returns {Promise<object>} - Registration result
   */
  static async registerUser(email, password, role, userData, sendVerificationEmail = true) {
    try {
      // Create user with Firebase Auth
      const userCredential = await createUserWithEmailAndPassword(auth, email, password);
      const user = userCredential.user;

      // Set display name
      await updateProfile(user, {
        displayName: userData.displayName || `${userData.firstName} ${userData.lastName}`
      });

      // Create user document in Firestore
      const userRef = doc(db, 'users', user.uid);
      await setDoc(userRef, {
        uid: user.uid,
        email: user.email,
        role,
        firstName: userData.firstName,
        lastName: userData.lastName,
        displayName: userData.displayName || `${userData.firstName} ${userData.lastName}`,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        status: 'active',
        emailVerified: false
      });

      // Create role-specific document if needed
      if (['student', 'teacher', 'parent', 'admin'].includes(role)) {
        await setDoc(doc(db, `${role}s`, user.uid), {
          userId: user.uid,
          ...userData,
          createdAt: serverTimestamp()
        });
      }

      // Send verification email if requested
      if (sendVerificationEmail) {
        try {
          // Send Firebase verification email
          await sendEmailVerification(user);

          // Log verification email sent
          await addDoc(collection(db, 'verification_logs'), {
            userId: user.uid,
            email: user.email,
            type: 'registration',
            timestamp: serverTimestamp(),
            status: 'sent'
          });

          // Try to send custom verification email
          try {
            // Generate verification token
            const { token } = await EmailVerificationService.generateVerificationToken(email, role);

            // Send custom verification email
            await EmailNotificationService.sendVerificationEmail(email, token, role);
          } catch (customEmailError) {
            console.error('Error sending custom verification email:', customEmailError);
            // Continue even if custom email fails, as Firebase will send its own
          }
        } catch (verificationError) {
          console.error('Error sending verification email:', verificationError);
          // Continue even if verification email fails
        }
      }

      // Log activity with proper error handling
      try {
        await ActivityService.logActivity({
          type: 'user_registration',
          userId: user.uid,
          title: `${role.charAt(0).toUpperCase() + role.slice(1)} Registration`,
          description: `${role.charAt(0).toUpperCase() + role.slice(1)} registered with email ${user.email}`,
          details: {
            role,
            email: user.email,
            verificationSent: sendVerificationEmail,
            firstName: userData.firstName,
            lastName: userData.lastName
          }
        });
        console.log('Activity logged successfully for user registration');
      } catch (activityError) {
        console.error('Error logging activity:', activityError);
        // Continue even if activity logging fails
      }

      return {
        success: true,
        user,
        verificationSent: sendVerificationEmail
      };
    } catch (error) {
      console.error('Registration error:', error);

      let errorMessage = 'Registration failed. Please try again.';
      if (error.code === 'auth/email-already-in-use') {
        errorMessage = 'This email is already registered.';
      } else if (error.code === 'auth/invalid-email') {
        errorMessage = 'Invalid email address.';
      } else if (error.code === 'auth/weak-password') {
        errorMessage = 'Password is too weak. Please use a stronger password.';
      }

      return {
        success: false,
        error: errorMessage
      };
    }
  }

  /**
   * Verify a user's email
   * @param {string} actionCode - The action code from the verification email
   * @returns {Promise<object>} - Verification result
   */
  static async verifyEmail(actionCode) {
    return EmailVerificationService.applyVerificationCode(actionCode);
  }

  /**
   * Resend verification email to a user
   * @param {object} user - The user object
   * @param {string} role - User role
   * @returns {Promise<boolean>} - Whether the email was sent
   */
  static async resendVerificationEmail(user, role) {
    try {
      // Send Firebase verification email
      await sendEmailVerification(user);

      // Log verification email sent
      await addDoc(collection(db, 'verification_logs'), {
        userId: user.uid,
        email: user.email,
        type: 'resend',
        timestamp: serverTimestamp(),
        status: 'sent'
      });

      // Try to send custom verification email
      try {
        // Generate verification token
        const { token } = await EmailVerificationService.generateVerificationToken(user.email, role);

        // Send custom verification email
        await EmailNotificationService.sendVerificationEmail(user.email, token, role);
      } catch (customEmailError) {
        console.error('Error sending custom verification email:', customEmailError);
        // Continue even if custom email fails, as Firebase will send its own
      }

      return true;
    } catch (error) {
      console.error('Error resending verification email:', error);
      throw error;
    }
  }

  /**
   * Send verification email to an email address (without a user object)
   * @param {string} email - The email address
   * @param {string} role - User role
   * @returns {Promise<boolean>} - Whether the email was sent
   */
  static async sendVerificationEmail(email, role) {
    try {
      // Generate verification token
      const { token } = await EmailVerificationService.generateVerificationToken(email, role);

      // Send custom verification email
      await EmailNotificationService.sendVerificationEmail(email, token, role);

      // Log verification email sent
      await addDoc(collection(db, 'verification_logs'), {
        email,
        type: 'custom',
        timestamp: serverTimestamp(),
        status: 'sent'
      });

      return true;
    } catch (error) {
      console.error('Error sending verification email:', error);
      throw error;
    }
  }

  /**
   * Check if an email is verified
   * @param {string} email - The email address
   * @returns {Promise<boolean>} - Whether the email is verified
   */
  static async isEmailVerified(email) {
    return EmailVerificationService.isEmailVerified(email);
  }

  /**
   * Register a new user by an admin without affecting the admin's authentication state
   * @param {string} email - User email
   * @param {string} password - User password
   * @param {string} role - User role (student, parent, teacher, admin)
   * @param {object} userData - User data (firstName, lastName, etc.)
   * @returns {Promise<object>} - Registration result
   */
  static async adminCreateUser(email, password, role, userData) {
    try {
      // Save the current admin user credentials
      const adminUser = auth.currentUser;
      if (!adminUser) {
        throw new Error('No admin user is currently signed in');
      }

      // Get admin email from Firestore to ensure we have the correct one
      const adminDoc = await getDoc(doc(db, 'users', adminUser.uid));
      if (!adminDoc.exists()) {
        throw new Error('Admin user document not found');
      }

      const adminEmail = adminDoc.data().email;

      // IMPORTANT: Save the current auth state before creating a new user
      // This is necessary because Firebase will automatically sign in as the newly created user
      const currentUser = auth.currentUser;

      // Create the new user
      const userCredential = await createUserWithEmailAndPassword(auth, email, password);
      const user = userCredential.user;

      // Set display name
      await updateProfile(user, {
        displayName: userData.displayName || `${userData.firstName} ${userData.lastName}`
      });

      // Create user document in Firestore
      const userRef = doc(db, 'users', user.uid);
      await setDoc(userRef, {
        uid: user.uid,
        email: user.email,
        role,
        firstName: userData.firstName,
        lastName: userData.lastName,
        displayName: userData.displayName || `${userData.firstName} ${userData.lastName}`,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        status: 'active',
        emailVerified: false
      });

      // Create role-specific document
      if (['student', 'teacher', 'parent', 'admin'].includes(role)) {
        await setDoc(doc(db, `${role}s`, user.uid), {
          userId: user.uid,
          ...userData,
          createdAt: serverTimestamp()
        });
      }

      // Send verification email
      try {
        // Send Firebase verification email
        await sendEmailVerification(user);

        // Log verification email sent
        await addDoc(collection(db, 'verification_logs'), {
          userId: user.uid,
          email: user.email,
          type: 'admin_registration',
          timestamp: serverTimestamp(),
          status: 'sent'
        });

        // Try to send custom verification email
        try {
          // Generate verification token
          const { token } = await EmailVerificationService.generateVerificationToken(email, role);

          // Send custom verification email
          await EmailNotificationService.sendVerificationEmail(email, token, role);
        } catch (customEmailError) {
          console.error('Error sending custom verification email:', customEmailError);
          // Continue even if custom email fails, as Firebase will send its own
        }
      } catch (verificationError) {
        console.error('Error sending verification email:', verificationError);
        // Continue even if verification email fails
      }

      // Log activity with proper error handling
      try {
        await ActivityService.logActivity({
          type: 'admin_user_creation',
          userId: user.uid,
          performedBy: adminUser.uid,
          title: `${role.charAt(0).toUpperCase() + role.slice(1)} Creation`,
          description: `${role.charAt(0).toUpperCase() + role.slice(1)} ${userData.firstName} ${userData.lastName} created by admin`,
          details: {
            role,
            email: user.email,
            firstName: userData.firstName,
            lastName: userData.lastName
          }
        });
        console.log('Activity logged successfully for user creation');
      } catch (activityError) {
        console.error('Error logging activity:', activityError);
        // Continue even if activity logging fails
      }

      // IMPORTANT: Explicitly sign out the newly created user
      // Firebase automatically signs in as the newly created user, so we need to sign out
      try {
        // Sign out the newly created user
        await signOut(auth);

        console.log('Successfully signed out newly created user');

        // We won't try to sign back in as admin here - this will be handled by the app's auth state listener
        // This prevents issues with missing password and allows the app to handle auth state changes properly
      } catch (signOutError) {
        console.error('Error signing out newly created user:', signOutError);
        // Even if sign-out fails, continue with the user creation process
      }

      // Send credentials email to the new user
      try {
        await EmailNotificationService.sendCredentialsEmail(
          email,
          password,
          userData,
          role
        );
        console.log(`Credentials email sent to ${role} (${email})`);
      } catch (emailError) {
        console.error(`Error sending credentials email to ${role}:`, emailError);
        // Continue even if email sending fails
      }

      // Return the created user
      return {
        success: true,
        user,
        verificationSent: true
      };
    } catch (error) {
      console.error('Admin user creation error:', error);

      let errorMessage = 'User creation failed. Please try again.';
      if (error.code === 'auth/email-already-in-use') {
        errorMessage = 'This email is already registered.';
      } else if (error.code === 'auth/invalid-email') {
        errorMessage = 'Invalid email address.';
      } else if (error.code === 'auth/weak-password') {
        errorMessage = 'Password is too weak. Please use a stronger password.';
      }

      return {
        success: false,
        error: errorMessage
      };
    }
  }
}

export default UserRegistrationService;
