import React, { useState, useEffect, useCallback } from 'react';
import { View, ScrollView, StyleSheet, Dimensions, TouchableOpacity, Text, Platform, SafeAreaView, StatusBar, Animated } from 'react-native';
import { Card, Title, Paragraph, IconButton, Portal, Dialog, Button, Avatar, Badge, Divider, List, ActivityIndicator, Searchbar, Surface, FAB, useTheme, Menu, Chip, TextInput } from 'react-native-paper';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { collection, query, getDocs, addDoc, updateDoc, deleteDoc, doc, where, orderBy } from 'firebase/firestore';
import { db } from '../../config/firebase';
import { useLanguage } from '../../context/LanguageContext';
import { useAuth } from '../../context/AuthContext';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import * as Animatable from 'react-native-animatable';
import { format } from 'date-fns';
import ActivityService from '../../services/ActivityService';

const screenWidth = Dimensions.get('window').width;

const SemesterManagement = () => {
  const { translate } = useLanguage();
  const { user } = useAuth();
  const navigation = useNavigation();
  // No theme needed

  const [semesters, setSemesters] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedSemester, setSelectedSemester] = useState(null);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [showSemesterDialog, setShowSemesterDialog] = useState(false);
  const [newSemester, setNewSemester] = useState({
    name: '',
    startDate: new Date(),
    endDate: new Date(),
    status: 'active'
  });
  const [menuVisible, setMenuVisible] = useState(false);
  const [selectedMenuSemester, setSelectedMenuSemester] = useState(null);
  const [fadeAnim] = useState(new Animated.Value(0));
  const [slideAnim] = useState(new Animated.Value(50));

  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      })
    ]).start();
  }, []);

  const fetchSemesters = async () => {
    try {
      setLoading(true);
      const semestersRef = collection(db, 'semesters');
      const q = query(semestersRef, orderBy('startDate', 'desc'));
      const snapshot = await getDocs(q);
      
      const semestersData = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        startDate: doc.data().startDate?.toDate(),
        endDate: doc.data().endDate?.toDate()
      }));
      
      setSemesters(semestersData);
    } catch (error) {
      console.error('Error fetching semesters:', error);
      setError(translate('errors.fetchError'));
    } finally {
      setLoading(false);
    }
  };

  useFocusEffect(
    useCallback(() => {
      fetchSemesters();
    }, [])
  );

  const handleAddSemester = async () => {
    try {
      setLoading(true);
      const semestersRef = collection(db, 'semesters');
      await addDoc(semestersRef, {
        ...newSemester,
        createdAt: new Date(),
        createdBy: user.uid
      });

      // Log activity
      await ActivityService.logActivity(
        user.uid,
        'academic',
        translate('activities.newSemester'),
        translate('activities.newSemesterDesc', { semester: newSemester.name })
      );

      setShowSemesterDialog(false);
      setNewSemester({
        name: '',
        startDate: new Date(),
        endDate: new Date(),
        status: 'active'
      });
      fetchSemesters();
    } catch (error) {
      console.error('Error adding semester:', error);
      setError(translate('errors.addError'));
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateSemester = async (semesterId, updates) => {
    try {
      setLoading(true);
      const semesterRef = doc(db, 'semesters', semesterId);
      await updateDoc(semesterRef, updates);

      // Log activity
      await ActivityService.logActivity(
        user.uid,
        'academic',
        translate('activities.updateSemester'),
        translate('activities.updateSemesterDesc', { semester: updates.name })
      );

      fetchSemesters();
    } catch (error) {
      console.error('Error updating semester:', error);
      setError(translate('errors.updateError'));
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteSemester = async () => {
    if (!selectedSemester) return;

    try {
      setLoading(true);
      const semesterRef = doc(db, 'semesters', selectedSemester.id);
      await deleteDoc(semesterRef);

      // Log activity
      await ActivityService.logActivity(
        user.uid,
        'academic',
        translate('activities.deleteSemester'),
        translate('activities.deleteSemesterDesc', { semester: selectedSemester.name })
      );

      setShowDeleteDialog(false);
      setSelectedSemester(null);
      fetchSemesters();
    } catch (error) {
      console.error('Error deleting semester:', error);
      setError(translate('errors.deleteError'));
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active':
        return '#4CAF50';
      case 'upcoming':
        return '#FFC107';
      case 'completed':
        return '#9E9E9E';
      default:
        return '#2196F3';
    }
  };

  const renderSemesterCard = (semester, index) => (
    <Animatable.View
      key={semester.id}
      animation="fadeInUp"
      duration={500}
      delay={index * 100}
    >
      <Surface style={styles.semesterCard}>
        <LinearGradient
          colors={['#2196F3', '#1976D2']}
          style={styles.cardGradient}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        >
          <View style={styles.cardHeader}>
            <Title style={styles.semesterTitle}>{semester.name}</Title>
            <Chip
              mode="outlined"
              style={[styles.statusChip, { borderColor: getStatusColor(semester.status) }]}
              textStyle={{ color: getStatusColor(semester.status) }}
            >
              {translate(`semester.status.${semester.status}`)}
            </Chip>
          </View>
          
          <View style={styles.dateContainer}>
            <View style={styles.dateItem}>
              <Ionicons name="calendar-outline" size={20} color="#fff" />
              <Text style={styles.dateText}>
                {format(semester.startDate, 'MMM d, yyyy')}
              </Text>
            </View>
            <View style={styles.dateItem}>
              <Ionicons name="calendar-outline" size={20} color="#fff" />
              <Text style={styles.dateText}>
                {format(semester.endDate, 'MMM d, yyyy')}
              </Text>
            </View>
          </View>

          <View style={styles.cardFooter}>
            <Button
              mode="contained"
              onPress={() => {
                setSelectedSemester(semester);
                setShowSemesterDialog(true);
              }}
              style={styles.editButton}
              icon="pencil"
            >
              {translate('actions.edit')}
            </Button>
            <IconButton
              icon="dots-vertical"
              size={24}
              color="#fff"
              onPress={() => {
                setSelectedMenuSemester(semester);
                setMenuVisible(true);
              }}
            />
          </View>
        </LinearGradient>
      </Surface>
    </Animatable.View>
  );

  return (
    <SafeAreaView style={styles.safeArea}>
      <StatusBar barStyle="light-content" backgroundColor="#2196F3" />
      <LinearGradient
        colors={['#2196F3', '#1976D2']}
        style={styles.headerGradient}
      >
        <View style={styles.container}>
          <View style={styles.header}>
            <View style={styles.headerLeft}>
              <IconButton
                icon="arrow-left"
                size={24}
                color="#fff"
                onPress={() => navigation.goBack()}
              />
              <Title style={styles.headerTitle}>
                {translate('semester.title')}
              </Title>
            </View>
          </View>

          <Searchbar
            placeholder={translate('semester.search')}
            onChangeText={query => setSearchQuery(query)}
            value={searchQuery}
            style={styles.searchBar}
            icon="magnify"
            clearIcon="close"
            onClearIconPress={() => setSearchQuery('')}
          />

          <ScrollView 
            style={styles.content}
            contentContainerStyle={styles.contentContainer}
            showsVerticalScrollIndicator={false}
          >
            {loading ? (
              <ActivityIndicator style={styles.loader} size="large" />
            ) : error ? (
              <View style={styles.errorContainer}>
                <IconButton
                  icon="alert-circle"
                  size={48}
                  color="#f44336"
                />
                <Text style={styles.errorText}>{error}</Text>
                <Button
                  mode="contained"
                  onPress={fetchSemesters}
                  style={styles.retryButton}
                >
                  {translate('actions.retry')}
                </Button>
              </View>
            ) : (
              <Animated.View
                style={[
                  styles.semestersList,
                  {
                    opacity: fadeAnim,
                    transform: [{ translateY: slideAnim }]
                  }
                ]}
              >
                {semesters
                  .filter(semester => 
                    semester.name.toLowerCase().includes(searchQuery.toLowerCase())
                  )
                  .map((semester, index) => renderSemesterCard(semester, index))
                }
              </Animated.View>
            )}
          </ScrollView>

          <FAB
            icon="plus"
            style={styles.fab}
            onPress={() => {
              setSelectedSemester(null);
              setShowSemesterDialog(true);
            }}
          />
                  </View>
      </LinearGradient>

      <Portal>
        <Dialog
          visible={showSemesterDialog}
          onDismiss={() => setShowSemesterDialog(false)}
          style={styles.dialog}
        >
          <Dialog.Title>
            {selectedSemester 
              ? translate('semester.editSemester')
              : translate('semester.addSemester')
            }
          </Dialog.Title>
          <Dialog.Content>
            <TextInput
              label={translate('semester.name')}
              value={selectedSemester ? selectedSemester.name : newSemester.name}
              onChangeText={text => 
                selectedSemester
                  ? setSelectedSemester({ ...selectedSemester, name: text })
                  : setNewSemester({ ...newSemester, name: text })
              }
              style={styles.input}
            />
            <View style={styles.dateInputs}>
              <Button
                mode="outlined"
                onPress={() => {
                  // Implement date picker
                }}
                style={styles.dateButton}
              >
                {selectedSemester
                  ? format(selectedSemester.startDate, 'MMM d, yyyy')
                  : format(newSemester.startDate, 'MMM d, yyyy')
                }
              </Button>
              <Button
                mode="outlined"
                onPress={() => {
                  // Implement date picker
                }}
                style={styles.dateButton}
              >
                {selectedSemester
                  ? format(selectedSemester.endDate, 'MMM d, yyyy')
                  : format(newSemester.endDate, 'MMM d, yyyy')
                }
              </Button>
            </View>
            <View style={styles.statusContainer}>
              <Text style={styles.statusLabel}>
                {translate('semester.status')}
              </Text>
              <View style={styles.statusButtons}>
                {['active', 'upcoming', 'completed'].map(status => (
                  <Chip
                    key={status}
                    selected={selectedSemester
                      ? selectedSemester.status === status
                      : newSemester.status === status
                    }
                    onPress={() =>
                      selectedSemester
                        ? setSelectedSemester({ ...selectedSemester, status })
                        : setNewSemester({ ...newSemester, status })
                    }
                    style={[
                      styles.statusButton,
                      {
                        backgroundColor:
                          (selectedSemester
                            ? selectedSemester.status === status
                            : newSemester.status === status)
                            ? getStatusColor(status)
                            : 'transparent'
                      }
                    ]}
                    textStyle={{
                      color:
                        (selectedSemester
                          ? selectedSemester.status === status
                          : newSemester.status === status)
                          ? '#fff'
                          : getStatusColor(status)
                    }}
                  >
                    {translate(`semester.status.${status}`)}
                  </Chip>
                ))}
              </View>
            </View>
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={() => setShowSemesterDialog(false)}>
              {translate('actions.cancel')}
            </Button>
            <Button
              mode="contained"
              onPress={() =>
                selectedSemester
                  ? handleUpdateSemester(selectedSemester.id, {
                      name: selectedSemester.name,
                      status: selectedSemester.status
                    })
                  : handleAddSemester()
              }
            >
              {selectedSemester
                ? translate('actions.save')
                : translate('actions.add')
              }
            </Button>
          </Dialog.Actions>
        </Dialog>

        <Dialog
          visible={showDeleteDialog}
          onDismiss={() => setShowDeleteDialog(false)}
        >
          <Dialog.Title>
            {translate('semester.deleteConfirm')}
          </Dialog.Title>
          <Dialog.Content>
            <Text>
              {translate('semester.deleteMessage', {
                semester: selectedSemester?.name
              })}
            </Text>
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={() => setShowDeleteDialog(false)}>
              {translate('actions.cancel')}
            </Button>
            <Button
              mode="contained"
              onPress={handleDeleteSemester}
              style={styles.deleteButton}
            >
              {translate('actions.delete')}
            </Button>
          </Dialog.Actions>
        </Dialog>

        <Menu
          visible={menuVisible}
          onDismiss={() => setMenuVisible(false)}
          anchor={
            <View style={styles.menuAnchor} />
          }
        >
          <Menu.Item
            onPress={() => {
              setMenuVisible(false);
              setSelectedSemester(selectedMenuSemester);
              setShowDeleteDialog(true);
            }}
            title={translate('actions.delete')}
            leadingIcon="delete"
          />
          <Menu.Item
            onPress={() => {
              setMenuVisible(false);
              // Implement duplicate functionality
            }}
            title={translate('actions.duplicate')}
            leadingIcon="content-copy"
          />
        </Menu>
      </Portal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#fff',
  },
  headerGradient: {
    flex: 1,
  },
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerTitle: {
    color: '#fff',
    marginLeft: 8,
  },
  searchBar: {
    margin: 16,
    elevation: 4,
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
  },
  loader: {
    marginTop: 32,
  },
  errorContainer: {
    alignItems: 'center',
    padding: 32,
  },
  errorText: {
    color: '#f44336',
    textAlign: 'center',
    marginVertical: 16,
  },
  retryButton: {
    marginTop: 16,
  },
  semestersList: {
    gap: 16,
  },
  semesterCard: {
    borderRadius: 12,
    elevation: 4,
    overflow: 'hidden',
  },
  cardGradient: {
    padding: 16,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  semesterTitle: {
    color: '#fff',
    fontSize: 20,
    fontWeight: 'bold',
  },
  statusChip: {
    backgroundColor: 'transparent',
  },
  dateContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  dateItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dateText: {
    color: '#fff',
    marginLeft: 8,
  },
  cardFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  editButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
  },
  dialog: {
    borderRadius: 12,
  },
  input: {
    marginBottom: 16,
  },
  dateInputs: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  dateButton: {
    flex: 1,
    marginHorizontal: 4,
  },
  statusContainer: {
    marginBottom: 16,
  },
  statusLabel: {
    marginBottom: 8,
  },
  statusButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statusButton: {
    flex: 1,
    marginHorizontal: 4,
  },
  deleteButton: {
    backgroundColor: '#f44336',
  },
  menuAnchor: {
    position: 'absolute',
    right: 0,
    top: 0,
  },
});

export default SemesterManagement;
