# Multilanguage Implementation Guide

This document provides guidelines on how to implement and use the multilanguage features across the School Management System application.

## Supported Languages

The application currently supports the following languages:

- English (en) - Default
- Amharic (am) - አማርኛ
- <PERSON><PERSON><PERSON> (om)

## Translation Architecture

The translation system is built on these components:

1. **Translation Files**: JSON files containing key-value pairs for each language
2. **LanguageContext**: React Context for managing language state and providing translation functions
3. **LanguageSelector**: Component for switching between languages
4. **useLanguage** hook: Convenient access to translation functions and language state

## How to Use Translations in Components

### 1. Import the useLanguage hook

```javascript
import { useLanguage } from '../../context/LanguageContext';
```

### 2. Access translation functions in your component

```javascript
const MyComponent = () => {
  const { translate, language, getTextStyle, isRTL } = useLanguage();

  // Now you can use these functions and properties
  return (
    <View>
      <Text style={getTextStyle()}>
        {translate('path.to.translation.key')}
      </Text>
    </View>
  );
};
```

### 3. Use translations with parameters

```javascript
// With the translation: "welcome": "Welcome, {{name}}"
<Text>{translate('dashboard.welcome', { name: userData.displayName })}</Text>
```

## Available Functions

| Function | Description |
|----------|-------------|
| `translate(key, params)` | Translates text using the specified key. Optional params object for replacing placeholders. |
| `getTextStyle(customStyle)` | Returns appropriate text styling for the current language (RTL, font size adjustments) |
| `setLanguage(langCode)` | Changes the application language |

## Properties

| Property | Description |
|----------|-------------|
| `language` | Current language code (e.g., 'en', 'am', 'om') |
| `isRTL` | Boolean indicating if current language is right-to-left |
| `supportedLanguages` | Object containing available language codes and names |

## Adding New Translation Keys

1. First, add the key to `src/translations/en.json` (English is the reference language)
2. Then add the same key structure to `src/translations/am.json` and `src/translations/om.json`
3. Ensure the key hierarchy is consistent across all translation files

Example:
```json
// In en.json
{
  "component": {
    "featureName": {
      "key": "English text"
    }
  }
}

// In am.json
{
  "component": {
    "featureName": {
      "key": "Amharic text (አማርኛ)"
    }
  }
}

// In om.json
{
  "component": {
    "featureName": {
      "key": "Afaan Oromo text"
    }
  }
}
```

## Adding New Languages

To add a new language:

1. Create a new translation file in `src/translations/` (e.g., `ti.json` for Tigrinya)
2. Add the language to the `supportedLanguages` object in `LanguageContext.js`
3. Add appropriate language configuration in `languageConfig` object in `LanguageContext.js`

```javascript
// LanguageContext.js
const supportedLanguages = {
  en: 'English',
  am: 'አማርኛ',
  om: 'Afaan Oromoo',
  ti: 'ትግርኛ' // New language
};

const languageConfig = {
  en: { isRTL: false, fontScale: 1 },
  am: { isRTL: false, fontScale: 1.1 },
  om: { isRTL: false, fontScale: 1 },
  ti: { isRTL: false, fontScale: 1.1 } // New language config
};
```

## Useful Components

### LanguageSelector

The `LanguageSelector` component provides a dropdown menu for users to select their preferred language:

```javascript
import LanguageSelector from '../../components/common/LanguageSelector';

// Use in your component/screen:
<View>
  <LanguageSelector />
</View>
```

## Best Practices

1. **Use Descriptive Keys**: Create a logical hierarchy and use descriptive keys
2. **Organize by Feature**: Group translations by features or screens
3. **Reuse Common Terms**: Place frequently used terms in the "common" section
4. **Use Parameters**: For dynamic content, use parameters like `{{name}}` rather than string concatenation
5. **Handle RTL**: Be mindful of RTL languages when designing layouts
6. **Font Scaling**: Some languages (like Amharic) may need larger font sizes

## Test Your Translations

Always test your application in all supported languages to ensure:

1. Text fits within UI components
2. RTL layouts work correctly
3. Special characters display properly
4. Dynamic content with parameters renders correctly

## Example Implementation

Here's a complete example of a component with multilanguage support:

```javascript
import React from 'react';
import { View, Text, Button } from 'react-native';
import { useLanguage } from '../../context/LanguageContext';
import LanguageSelector from '../../components/common/LanguageSelector';

const WelcomeScreen = ({ userName }) => {
  const { translate, getTextStyle, language } = useLanguage();

  return (
    <View style={{ flex: 1, padding: 20 }}>
      <LanguageSelector />

      <Text style={getTextStyle({ fontSize: 24, fontWeight: 'bold' })}>
        {translate('welcome.title')}
      </Text>

      <Text style={getTextStyle()}>
        {translate('welcome.greeting', { name: userName })}
      </Text>

      <Button
        title={translate('common.continue')}
        onPress={() => console.log('Continuing in', language)}
      />
    </View>
  );
};

export default WelcomeScreen;
```