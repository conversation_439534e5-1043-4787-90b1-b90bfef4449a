import React from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import { Card, Title, Paragraph, Chip, Divider } from 'react-native-paper';
import { useTranslation } from '../../hooks/useTranslation';

// Define severity colors
const SEVERITY_COLORS = {
    minor: '#2196F3',  // info
    moderate: '#FF9800', // warning
    major: '#F44336',  // error
    positive: '#4CAF50' // success
};

const BehaviorReport = ({
    studentInfo,
    behaviorRecords,
    recommendations,
    schoolInfo,
    reportPeriod
}) => {
    const {
        t,
        language,
        formatDate,
        getGradeName
    } = useTranslation();

    const renderHeader = () => (
        <View style={styles.header}>
            <Title style={styles.title}>{schoolInfo.name[language]}</Title>
            <Title style={styles.subtitle}>{t('behavior_discipline_report')}</Title>
            <Paragraph>{t('report_period')}: {reportPeriod}</Paragraph>
        </View>
    );

    const renderStudentInfo = () => (
        <Card style={styles.card}>
            <Card.Content>
                <Title style={styles.sectionTitle}>{t('student_information')}</Title>
                <View style={styles.infoRow}>
                    <Paragraph style={styles.label}>{t('student_name')}:</Paragraph>
                    <Paragraph style={styles.value}>{studentInfo.name[language]}</Paragraph>
                </View>
                <View style={styles.infoRow}>
                    <Paragraph style={styles.label}>{t('grade_section')}:</Paragraph>
                    <Paragraph style={styles.value}>
                        {getGradeName(studentInfo.grade)} - {studentInfo.section}
                    </Paragraph>
                </View>
                <View style={styles.infoRow}>
                    <Paragraph style={styles.label}>{t('student_id')}:</Paragraph>
                    <Paragraph style={styles.value}>{studentInfo.id}</Paragraph>
                </View>
                <Divider style={styles.divider} />
                <View style={styles.infoRow}>
                    <Paragraph style={styles.label}>{t('homeroom_teacher')}:</Paragraph>
                    <Paragraph style={styles.value}>{studentInfo.homeroomTeacher[language]}</Paragraph>
                </View>
                <View style={styles.infoRow}>
                    <Paragraph style={styles.label}>{t('counselor')}:</Paragraph>
                    <Paragraph style={styles.value}>{studentInfo.counselor[language]}</Paragraph>
                </View>
            </Card.Content>
        </Card>
    );

    const renderBehaviorSummary = () => {
        const summary = behaviorRecords.reduce((acc, record) => {
            acc[record.type] = (acc[record.type] || 0) + 1;
            return acc;
        }, {});

        return (
            <Card style={styles.card}>
                <Card.Content>
                    <Title style={styles.sectionTitle}>{t('behavior_summary')}</Title>
                    <View style={styles.summaryContainer}>
                        {Object.entries(summary).map(([type, count]) => (
                            <View style={styles.summaryItem} key={type}>
                                <Title style={styles.summaryCount}>{count}</Title>
                                <Paragraph style={styles.summaryLabel}>
                                    {t(`behavior_type_${type}`)}
                                </Paragraph>
                            </View>
                        ))}
                    </View>
                </Card.Content>
            </Card>
        );
    };

    const renderBehaviorTimeline = () => (
        <Card style={styles.card}>
            <Card.Content>
                <Title style={styles.sectionTitle}>{t('behavior_timeline')}</Title>
                {behaviorRecords.map((record, index) => (
                    <View key={index} style={styles.timelineItem}>
                        <View style={styles.timelineHeader}>
                            <Chip
                                style={{backgroundColor: SEVERITY_COLORS[record.severity]}}
                                textStyle={{color: 'white'}}
                            >
                                {t(`severity_${record.severity}`)}
                            </Chip>
                            <View style={styles.timelineHeaderText}>
                                <Paragraph style={styles.timelineTitle}>
                                    {record.description[language]}
                                </Paragraph>
                                <Paragraph style={styles.timelineSubtitle}>
                                    {formatDate(record.date)} - {record.location[language]}
                                </Paragraph>
                            </View>
                        </View>
                        <View style={styles.timelineContent}>
                            <Paragraph>
                                {t('reported_by')}: {record.reportedBy[language]}
                            </Paragraph>
                            <Paragraph>
                                {t('action_taken')}: {record.actionTaken[language]}
                            </Paragraph>
                        </View>
                        {index < behaviorRecords.length - 1 && <Divider style={styles.divider} />}
                    </View>
                ))}
            </Card.Content>
        </Card>
    );

    const renderRecommendations = () => (
        <Card style={styles.card}>
            <Card.Content>
                <Title style={styles.sectionTitle}>{t('recommendations_interventions')}</Title>
                {recommendations.map((recommendation, index) => (
                    <View key={index} style={styles.recommendationItem}>
                        <Title style={styles.recommendationTitle}>
                            {recommendation.title[language]}
                        </Title>
                        <Paragraph style={styles.recommendationDescription}>
                            {recommendation.description[language]}
                        </Paragraph>
                        <View style={styles.tagsContainer}>
                            {recommendation.tags.map((tag, tagIndex) => (
                                <Chip
                                    key={tagIndex}
                                    style={styles.tag}
                                >
                                    {t(`tag_${tag}`)}
                                </Chip>
                            ))}
                        </View>
                        {index < recommendations.length - 1 && <Divider style={styles.divider} />}
                    </View>
                ))}
            </Card.Content>
        </Card>
    );

    const renderSignatures = () => (
        <Card style={styles.card}>
            <Card.Content>
                <View style={styles.signaturesContainer}>
                    <View style={styles.signatureItem}>
                        <Paragraph>{t('homeroom_teacher')}</Paragraph>
                        <View style={styles.signatureLine} />
                        <Paragraph>{formatDate(new Date())}</Paragraph>
                    </View>
                    <View style={styles.signatureItem}>
                        <Paragraph>{t('counselor')}</Paragraph>
                        <View style={styles.signatureLine} />
                        <Paragraph>{formatDate(new Date())}</Paragraph>
                    </View>
                    <View style={styles.signatureItem}>
                        <Paragraph>{t('parent_acknowledgment')}</Paragraph>
                        <View style={styles.signatureLine} />
                        <Paragraph>{t('signature_date')}</Paragraph>
                    </View>
                </View>
            </Card.Content>
        </Card>
    );

    return (
        <ScrollView style={styles.container}>
            {renderHeader()}
            {renderStudentInfo()}
            {renderBehaviorSummary()}
            {renderBehaviorTimeline()}
            {renderRecommendations()}
            {renderSignatures()}
        </ScrollView>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        padding: 16,
        backgroundColor: '#f5f5f5',
    },
    header: {
        alignItems: 'center',
        marginBottom: 16,
    },
    title: {
        fontSize: 20,
        fontWeight: 'bold',
        textAlign: 'center',
    },
    subtitle: {
        fontSize: 18,
        marginVertical: 8,
        textAlign: 'center',
    },
    card: {
        marginBottom: 16,
        elevation: 2,
    },
    sectionTitle: {
        fontSize: 18,
        marginBottom: 12,
    },
    infoRow: {
        flexDirection: 'row',
        marginBottom: 8,
    },
    label: {
        flex: 1,
        fontWeight: 'bold',
    },
    value: {
        flex: 2,
    },
    divider: {
        marginVertical: 12,
    },
    summaryContainer: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'space-between',
    },
    summaryItem: {
        width: '48%',
        backgroundColor: '#fff',
        padding: 12,
        borderRadius: 8,
        marginBottom: 12,
        elevation: 1,
        alignItems: 'center',
    },
    summaryCount: {
        fontSize: 24,
        color: '#1976d2',
    },
    summaryLabel: {
        textAlign: 'center',
        color: '#666',
    },
    timelineItem: {
        marginBottom: 16,
    },
    timelineHeader: {
        flexDirection: 'row',
        marginBottom: 8,
    },
    timelineHeaderText: {
        marginLeft: 12,
        flex: 1,
    },
    timelineTitle: {
        fontWeight: 'bold',
    },
    timelineSubtitle: {
        color: '#666',
        fontSize: 12,
    },
    timelineContent: {
        marginLeft: 12,
        marginTop: 8,
    },
    recommendationItem: {
        marginBottom: 16,
    },
    recommendationTitle: {
        fontSize: 16,
        marginBottom: 8,
    },
    recommendationDescription: {
        marginBottom: 8,
    },
    tagsContainer: {
        flexDirection: 'row',
        flexWrap: 'wrap',
    },
    tag: {
        marginRight: 8,
        marginBottom: 8,
    },
    signaturesContainer: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'space-between',
    },
    signatureItem: {
        width: '30%',
        alignItems: 'center',
        marginTop: 24,
    },
    signatureLine: {
        width: 120,
        height: 1,
        backgroundColor: '#000',
        marginVertical: 16,
    },
});

export default BehaviorReport;
