import React from 'react';
import { TouchableOpacity, View } from 'react-native';
import { createStackNavigator } from '@react-navigation/stack';
import { useAuth } from '../context/AuthContext';
import { useLanguage } from '../context/LanguageContext';
import { Ionicons } from '@expo/vector-icons';
import { AdminSidebarProvider } from '../context/AdminSidebarContext';
import AdminDashboard from '../screens/admin/AdminDashboard';
import MobileUserManagement from '../screens/admin/UserManagement';
import AdminManagement from '../screens/admin/AdminManagement';
import AcademicCalendar from '../screens/admin/AcademicCalendar';
import GradeSettings from '../screens/admin/GradeSettings';
import LibraryManagement from '../screens/admin/LibraryManagement';
import SystemReports from '../screens/admin/SystemReports';
import ClassManagementWrapper from '../screens/admin/ClassManagementWrapper';
import ExamManagement from '../screens/admin/ExamSchedule';
import LibrarySettings from '../screens/admin/LibrarySettings';
import ParentManagement from '../screens/admin/ParentManagement';
import ResultsManagement from '../screens/admin/ResultsManagement';
import SchoolSettings from '../screens/admin/SchoolSettings';
import StudentManagement from '../screens/admin/StudentManagement';
import StudentDetails from '../screens/admin/StudentDetails';
import SubjectManagement from '../screens/admin/SubjectManagement';
import TeacherManagementWrapper from '../screens/admin/TeacherManagementWrapper';
import AnnouncementManagement from '../screens/admin/AnnouncementManagement';
import ProfileManagement from '../screens/admin/ProfileManagement';
import SemesterManagement from '../screens/admin/SemesterManagement';
import TeacherPermissions from '../screens/admin/TeacherPermissions';
import TimeSettings from '../screens/admin/TimeSettings';
import ClassSchedule from '../screens/admin/ClassSchedule';
import ResourceManagement from '../screens/admin/ResourceManagement';
import PerformanceAnalytics from '../screens/admin/PerformanceAnalytics';
import NotificationCenter from '../screens/shared/NotificationCenter';
import NotificationSettings from '../screens/shared/NotificationSettings';
import NotificationTestScreen from '../screens/shared/NotificationTestScreen';
import ReportCenter from '../screens/shared/ReportCenter';
import TeacherSchedule from '../screens/admin/TeacherSchedule';
import TeacherEvaluation from '../screens/admin/TeacherEvaluation';
import TeacherDocuments from '../screens/admin/TeacherDocuments';
import TeacherAttendance from '../screens/admin/TeacherAttendance';
import CommunicationCenter from '../screens/admin/CommunicationCenter';
import AdminGradeApproval from '../screens/admin/AdminGradeApproval';
import AdminAttendanceApproval from '../screens/admin/AdminAttendanceApproval';
import AttendanceReports from '../screens/admin/AttendanceReports';
import AttendanceManagement from '../screens/teacher/AttendanceManagement';
import LanguageSelector from '../components/common/LanguageSelector';
import ChatList from '../screens/shared/ChatList';
import ActivityDetailScreen from '../screens/admin/ActivityDetailScreen';
import ActivityManagement from '../screens/admin/ActivityManagement';
import CloudinaryTestScreen from '../screens/CloudinaryTestScreen';

const Stack = createStackNavigator();

const AdminNavigator = () => {
  const { logout } = useAuth();
  const { translate } = useLanguage();

  const screenOptions = {
    headerRight: () => (
      <View style={{ flexDirection: 'row', alignItems: 'center', marginRight: 10 }}>
        <LanguageSelector />
        <TouchableOpacity
          onPress={logout}
          style={{ marginRight: 15 }}
        >
          <Ionicons name="log-out-outline" size={24} color="#000" />
        </TouchableOpacity>
      </View>
    ),
  };

  return (
    <AdminSidebarProvider>
      <Stack.Navigator
        initialRouteName="AdminDashboard"
        screenOptions={{
          ...screenOptions,
          headerStyle: {
            backgroundColor: '#fff',
            elevation: 0,
            shadowOpacity: 0,
          },
          headerTintColor: '#000',
          headerTitleStyle: {
            fontWeight: 'bold',
          },
          headerShown: false, // Hide the default header since we're using AdminAppHeader
        }}
      >
      <Stack.Screen
        name="AdminDashboard"
        component={AdminDashboard}
        options={{
          title: translate('dashboard.title'),
        }}
      />
      <Stack.Screen
        name="NotificationCenter"
        component={NotificationCenter}
        options={{ title: translate('screens.notifications'), headerShown: false }}
      />
      <Stack.Screen
        name="NotificationSettings"
        component={NotificationSettings}
        options={{ title: translate('screens.notificationSettings'), headerShown: false }}
      />
      <Stack.Screen
        name="NotificationTest"
        component={NotificationTestScreen}
        options={{ title: 'Notification Test' }}
      />
      <Stack.Screen
        name="UserManagement"
        component={MobileUserManagement}
        options={{
          title: translate('screens.userManagement'),
          headerShown: false
        }}
      />
      <Stack.Screen
        name="AdminManagement"
        component={AdminManagement}
        options={{ title: translate('screens.adminManagement') }}
      />
      <Stack.Screen
        name="AcademicCalendar"
        component={AcademicCalendar}
        options={{ title: translate('screens.academicCalendar') }}
      />
      <Stack.Screen
        name="GradeSettings"
        component={GradeSettings}
        options={{ title: translate('screens.gradeSettings') }}
      />
      <Stack.Screen
        name="LibraryManagement"
        component={LibraryManagement}
        options={{ title: translate('screens.libraryManagement') }}
      />
      <Stack.Screen
        name="SystemReports"
        component={SystemReports}
        options={{ title: translate('screens.systemReports') }}
      />
      <Stack.Screen
        name="ClassManagement"
        component={ClassManagementWrapper}
        options={{ title: translate('screens.classManagement') }}
      />
      <Stack.Screen
        name="ExamManagement"
        component={ExamManagement}
        options={{ title: translate('screens.examschedule') }}
      />
      <Stack.Screen
        name="LibrarySettings"
        component={LibrarySettings}
        options={{ title: translate('screens.librarySettings') }}
      />
      <Stack.Screen
        name="ParentManagement"
        component={ParentManagement}
        options={{ title: translate('screens.parentManagement') }}
      />
      <Stack.Screen
        name="ResultsManagement"
        component={ResultsManagement}
        options={{
          title: translate('screens.resultsManagement'),
          headerShown: true
        }}
      />
      <Stack.Screen
        name="SchoolSettings"
        component={SchoolSettings}
        options={{ title: translate('screens.schoolSettings') }}
      />
      <Stack.Screen
        name="StudentManagement"
        component={StudentManagement}
        options={{ title: translate('screens.studentManagement') }}
      />
      <Stack.Screen
        name="StudentDetails"
        component={StudentDetails}
        options={{ title: translate('screens.studentDetails') }}
      />
      <Stack.Screen
        name="SubjectManagement"
        component={SubjectManagement}
        options={{ title: translate('screens.subjectManagement') }}
      />
      <Stack.Screen
        name="TeacherManagement"
        component={TeacherManagementWrapper}
        options={{ title: translate('screens.teacherManagement') }}
      />
      <Stack.Screen
        name="AnnouncementManagement"
        component={AnnouncementManagement}
        options={{ title: translate('screens.announcementManagement') }}
      />
      <Stack.Screen
        name="ProfileManagement"
        component={ProfileManagement}
        options={{ title: translate('screens.profileManagement') }}
      />
      <Stack.Screen
        name="SemesterManagement"
        component={SemesterManagement}
        options={{ title: translate('screens.semesterManagement') }}
      />
      <Stack.Screen
        name="TeacherPermissions"
        component={TeacherPermissions}
        options={{ title: translate('screens.teacherPermissions') }}
      />
      <Stack.Screen
        name="TimeSettings"
        component={TimeSettings}
        options={{ title: translate('screens.timeSettings') }}
      />
      <Stack.Screen
        name="ClassSchedule"
        component={ClassSchedule}
        options={{ title: translate('screens.classchedule') }}
      />
      <Stack.Screen
        name="ResourceManagement"
        component={ResourceManagement}
        options={{ title: translate('screens.resourceManagement') }}
      />
      <Stack.Screen
        name="PerformanceAnalytics"
        component={PerformanceAnalytics}
        options={{ title: translate('screens.performanceAnalytics') }}
      />
      <Stack.Screen
        name="Reports"
        component={ReportCenter}
        options={{ title: translate('screens.reportCenter') }}
      />
      <Stack.Screen
        name="TeacherSchedule"
        component={TeacherSchedule}
        options={{ title: translate('screens.teacherSchedule') }}
      />
      <Stack.Screen
        name="TeacherEvaluation"
        component={TeacherEvaluation}
        options={{ title: translate('screens.teacherEvaluation') }}
      />
      <Stack.Screen
        name="TeacherDocuments"
        component={TeacherDocuments}
        options={{ title: translate('screens.teacherDocuments') }}
      />
      <Stack.Screen
        name="TeacherAttendance"
        component={TeacherAttendance}
        options={{ title: translate('screens.teacherAttendance') }}
      />
      <Stack.Screen
        name="CommunicationCenter"
        component={CommunicationCenter}
        options={{ title: translate('screens.communicationCenter') }}
      />
      <Stack.Screen
        name="AdminGradeApproval"
        component={AdminGradeApproval}
        options={{
          title: translate('screens.gradeApproval') || 'Grade Approval',
          headerShown: false
        }}
      />
      <Stack.Screen
        name="AdminAttendanceApproval"
        component={AdminAttendanceApproval}
        options={{
          title: translate('screens.attendanceApproval') || 'Attendance Approval',
          headerShown: false
        }}
      />
      <Stack.Screen
        name="AttendanceReports"
        component={AttendanceReports}
        options={{
          title: translate('screens.attendanceReports') || 'Attendance Reports',
          headerShown: false
        }}
      />
      <Stack.Screen
        name="AttendanceManagement"
        component={AttendanceManagement}
        options={{
          title: translate('screens.attendanceManagement') || 'Attendance Management',
          headerShown: false
        }}
      />
      <Stack.Screen
        name="ChatList"
        component={ChatList}
        options={{
          title: translate('navigation.chat'),
          headerTitleAlign: 'center',
        }}
      />
      <Stack.Screen
        name="ActivityDetailScreen"
        component={ActivityDetailScreen}
        options={{
          title: translate('activities.details'),
        }}
      />
      <Stack.Screen
        name="ActivityManagement"
        component={ActivityManagement}
        options={{
          title: translate('activities.management'),
        }}
      />
      <Stack.Screen
        name="CloudinaryTest"
        component={CloudinaryTestScreen}
        options={{
          title: translate('Cloudinary Test'),
          headerShown: true
        }}
      />
    </Stack.Navigator>
    </AdminSidebarProvider>
  );
};

export default AdminNavigator;