{
  "common": {
    "photo": "Photo",
    "name": "Name",
    "remarks": "Remarks",
    "roll": "Roll",
    "save": "Save",
    "submit": "Submit",
    "cancel": "Cancel",
    "confirm": "Confirm",
    "dismiss": "Dismiss",
    "notes": "Notes",
    "unnamed": "Unnamed",
    "notAssigned": "Not Assigned",
    "addRemarks": "Add Remarks...",
    "selectLanguage": "Select Language",
    "view": "View",
    "details": "Details",
    "loading": "Loading...",
    "confirmLogout": "Confirm Logout",
    "logoutConfirmMessage": "Are you sure you want to logout?",
    "confirmLogoutMessage": "Are you sure you want to logout from the system?",
    "logout": "Logout",
    "noData": "No data available",
    "viewAll": "View All",
    "profile": "Profile",
    "settings": "Settings",
    "search": "Search",
    "clearSearch": "Clear Search",
    "filter": "Filter",
    "sort": "Sort",
    "more": "More",
    "refresh": "Refresh",
    "edit": "Edit",
    "delete": "Delete",
    "add": "Add",
    "create": "Create",
    "update": "Update",
    "back": "Back",
    "next": "Next",
    "previous": "Previous",
    "yes": "Yes",
    "no": "No",
    "approve": "Approve",
    "reject": "Reject",
    "pending": "Pending",
    "completed": "Completed",
    "active": "Active",
    "inactive": "Inactive",
    "date": "Date",
    "time": "Time",
    "status": "Status",
    "actions": "Actions",
    "login": "Login",
    "register": "Register",
    "email": "Email",
    "password": "Password",
    "selectChild": "Select Child",
    "retry": "Retry",
    "all": "All",
    "close": "Close",
    "now": "Now",
    "unknownSubject": "Unknown Subject",
    "unknownTeacher": "Unknown Teacher",
    "room": "Room",
    "due": "Due",
    "loadingText": "Loading...",
    "errorLoadingData": "Error loading data",
    "errorFetchingData": "Error fetching data. Please try again.",
    "noInternetConnection": "No internet connection. Please check your network.",
    "offlineMode": "You are currently in offline mode",
    "navigatingTo": "Navigating to",
    "dateFormat": "MM/DD/YYYY",
    "menuOpened": "Menu opened",
    "menuClosed": "Menu closed",
    "showMore": "Show more",
    "viewDetails": "View Details",
    "selected": "Selected",
    "tapToView": "Tap to view details",
    "dashboard": "Dashboard",
    "usingOfflineData": "Using offline data",
    "success": "Success",
    "version": "Version",
    "section": "Section",
    "help": "Help",
    "enrollment": "Enrollment",
    "performance": "Performance",
    "resources": "Resources",
    "analytics": "Analytics",
    "step": "Step",
    "of": "of",
    "or": "or",
    "ok": "OK",
    "minutesAgo": "{{count}} minutes ago",
    "just now": "just now",
    "24 minutes ago": "24 minutes ago",
    "9 minutes ago": "9 minutes ago",
    "3 days ago": "3 days ago",
    "3 hours ago": "3 hours ago"
  },
  "userManagement": {
    "quickAccess": "Quick Access",
    "addAdmin": "Add Admin",
    "addTeacher": "Add Teacher",
    "addStudent": "Add Student",
    "addParent": "Add Parent",
    "userStatistics": "User Statistics",
    "newUsers": "New Users",
    "search": "Search",
    "tooltipFilter": "Filter Users",
    "tooltipSort": "Sort Users",
    "tooltipChangeView": "Change View",
    "admin": "Admin",
    "active": "Active",
    "tooltipEdit": "Edit User",
    "resetPassword": "Reset Password",
    "deactivate": "Deactivate",
    "student": "Student",
    "unknown": "Unknown",
    "parent": "Parent",
    "teacher": "Teacher",
    "allRoles": "All Roles",
    "allStatuses": "All Statuses",
    "inactive": "Inactive",
    "sortName": "Sort by Name",
    "sortEmail": "Sort by Email",
    "sortRole": "Sort by Role",
    "sortStatus": "Sort by Status",
    "sortCreated": "Sort by Creation Date",
    "ascending": "Ascending Order",
    "descending": "Descending Order",
    "confirmDelete": "Confirm Delete",
    "confirmDeleteMessage": "Are you sure you want to delete this user?",
    "confirmActivate": "Confirm Activate",
    "confirmActivateMessage": "Are you sure you want to activate this user?",
    "activate": "Activate",
    "confirmDeactivate": "Confirm Deactivate",
    "confirmDeactivateMessage": "Are you sure you want to deactivate this user?",
    "resetPasswordMessage": "Are you sure you want to reset the password for this user?",
    "sendResetEmail": "Send Reset Email",
    "role": "Role",
    "status": "Status",
    "phoneNumber": "Phone Number",
    "lastLogin": "Last Login",
    "never": "Never",
    "createdAt": "Created At",
    "title": "User Management",
    "addUser": "Add User",
    "editUser": "Edit User",
    "deleteUser": "Delete User",
    "userDetails": "User Details",
    "userList": "User List",
    "email": "Email",
    "phone": "Phone",
    "address": "Address",
    "joinDate": "Join Date",
    "actions": "Actions",
    "searchUser": "Search User",
    "filterByRole": "Filter by Role",
    "filterByStatus": "Filter by Status",
    "sortBy": "Sort by",
    "userDeleted": "User deleted successfully",
    "userAdded": "User added successfully",
    "userUpdated": "User updated successfully",
    "clearFilters": "Clear Filters",
    "clearFilter": "Clear Filter",
    "loading": "Loading users...",
    "noUsers": "No users found",
    "errorFetching": "Error fetching users",
    "errorUpdating": "Error updating user status",
    "errorDeleting": "Error deleting user",
    "userActivated": "User activated successfully",
    "userDeactivated": "User deactivated successfully",
    "passwordResetSent": "Password reset email sent successfully",
    "errorPasswordReset": "Failed to send password reset email",
    "authDeletionPending": "Auth account will be cleaned up later",
    "success": "Success",
    "notProvided": "Not provided",
    "fetchedUsers": "Fetched {{count}} users",
    "teachingCount": "Teaching {{count}} sections"
  },
  "auth": {
    "login": {
      "email": "Email",
      "password": "Password",
      "forgotPassword": "Forgot Password",
      "submit": "Login",
      "back": "Back"
    },
    "register": "Register",
    "logoutMessage": "Are you sure you want to logout?",
    "forgotPassword": {
      "emailLabel": "Enter your email address",
      "resetButton": "Reset Password",
      "backToLogin": "Back to Login"
    },
    "landing": {
      "title": "ASELLA SECONDARY SCHOOL",
      "subtitle": "Excellence in Education",
      "schoolName": "School Management System",
      "schoolNameFull": "Asella Secondary School Management System",
      "schoolNameShort": "ASS",
      "motto": "Excellence in Education",
      "features": {
        // ... existing code ...
      },
      "buttons": {
        // ... existing code ...
      },
      "languageSelector": {
        // ... existing code ...
      },
      "footer": {
        "copyright": "© 2024 School Management System. All rights reserved.",
        "motto": "Excellence in Education",
        "rights": "All Rights Reserved",
        "contactInfo": "Contact Information",
        "address": "Asella, Ethiopia",
        "phone": "+251 123 456 789",
        "email": "<EMAIL>"
      }
    },
    "User logged in": "User logged in",
    "User logged out": "User logged out",
    "Admin User logged into the system": "Admin User logged into the system",
    "Admin User logged out of the system": "Admin User logged out of the system"
  },
  "admin": {
    "dashboard": {
      "title": "Admin Dashboard",
      "analytics": "Analytics",
      "welcome": "Welcome, {{name}}",
      "overview": "Overview",
      "quickAccess": "Quick Access",
      "recentActivities": "Recent Activities",
      "noActivities": "No recent activities",
      "totalStudents": "Total Students",
      "totalTeachers": "Total Teachers",
      "totalClasses": "Total Classes",
      "activeUsers": "Active Users",
      "enrollment": "Enrollment",
      "performance": "Performance",
      "resources": "Resources",
      "showMore": "Show More",
      "showLess": "Show Less",
      "searchResults": "Search Results",
      "administrator": "Administrator",
      "present": "Present",
      "absent": "Absent",
      "gradeDistribution": "Grade Distribution",
      "performanceTrends": "Performance Trends",
      "attendanceRate": "Attendance Rate",
      "topPerformers": "Top Performers",
      "improvementAreas": "Areas for Improvement",
      "average": "Average",
      "attendance": "Attendance",
      "averageScore": "Average Score",
      "academicManagement": "Academic Management",
      "userManagement": "User Management",
      "classExam": "Class & Exam",
      "teacherTools": "Teacher Tools",
      "resourcesLibrary": "Resources & Library",
      "communication": "Communication",
      "systemReports": "System & Reports",
      "attendanceManagement": "Attendance Management",
      "attendanceApproval": "Attendance Approval",
      "attendanceReports": "Attendance Reports",
      "schoolOverview": "School Overview",
      "lastUpdated": "Last updated",
      "dataUpdated": "Data updated",
      "errorFetchingStats": "Error fetching statistics",
      "systemStarted": "System Started",
      "systemInitialized": "The system has been initialized successfully",
      "justNow": "Just now",
      "studentManagement": {
        "bulkImport": "Bulk Import"
      },
      "classManagement": {
        "title": "Class Management",
        "addClass": "Add Class",
        "editClass": "Edit Class",
        "deleteClass": "Delete Class",
        "classDetails": "Class Details",
        "classList": "Class List",
        "class": "Class",
        "section": "Section",
        "sections": "Sections",
        "capacity": "Capacity",
        "studentsCount": "{{current}}/{{capacity}} Students",
        "lowOccupancy": "Low Occupancy",
        "mediumOccupancy": "Medium Occupancy",
        "highOccupancy": "High Occupancy",
        "students": "Students",
        "teachers": "Teachers",
        "teachersTitle": "Teachers",
        "noTeachers": "No Teachers Assigned",
        "addTeacher": "Add Teacher",
        "viewStudents": "View Students",
        "description": "Description",
        "noSections": "No Sections Available",
        "addSection": "Add Section",
        "assignTeacherToSection": "Assign Teacher to Section",
        "selectSubject": "Select Subject",
        "noSubjectsAvailable": "No Subjects Available",
        "retry": "Retry",
        "searchTeachers": "Search Teachers",
        "assign": "Assign",
        "cancel": "Cancel",
        "teacherAssignedSuccess": "Teacher assigned successfully",
        "errorAssigningTeacher": "Error assigning teacher",
        "classNameRequired": "Class name is required",
        "descriptionRequired": "Description is required",
        "academicYearRequired": "Academic year is required",
        "sectionRequired": "At least one section is required",
        "sectionsValidation": "All sections must have a name and capacity",
        "groupRequired": "At least one group is required",
        "classAddedSuccess": "Class added successfully",
        "errorAddingClass": "Error adding class",
        "errorUpdatingClass": "Error updating class",
        "errorFetchingClasses": "Error fetching classes",
        "errorFetchingTeachers": "Error fetching teachers",
        "cannotDeleteClassWithStudents": "Cannot delete class with assigned students",
        "errorDeletingClass": "Error deleting class",
        "topPerformers": "Top Performers",
        "loadingStudents": "Loading students...",
        "searchStudents": "Search students",
        "name": "Name",
        "rollNumber": "Roll Number",
        "email": "Email",
        "averageScore": "Average Score",
        "actions": "Actions"
      },
      "activities": {
        "templates": {
          "login": "{{user}} logged into the system",
          "logout": "{{user}} logged out of the system",
          "createStudent": "New student {{name}} added to the system",
          "updateStudent": "Student {{name}} information updated",
          "deleteStudent": "Student {{name}} removed from the system",
          "createTeacher": "New teacher {{name}} added to the system",
          "updateTeacher": "Teacher {{name}} information updated",
          "deleteTeacher": "Teacher {{name}} removed from the system",
          "createParent": "New parent {{name}} added to the system",
          "updateParent": "Parent {{name}} information updated",
          "deleteParent": "Parent {{name}} removed from the system",
          "createAdmin": "New admin {{name}} added to the system",
          "updateAdmin": "Admin {{name}} information updated",
          "deleteAdmin": "Admin {{name}} removed from the system",
          "createClass": "New class {{name}} created",
          "updateClass": "Class {{name}} information updated",
          "deleteClass": "Class {{name}} removed from the system",
          "createExam": "New exam {{name}} scheduled",
          "updateExam": "Exam {{name}} details updated",
          "deleteExam": "Exam {{name}} removed from schedule",
          "submitGrades": "Grades for class {{class}} subject {{subject}} submitted",
          "approveGrades": "Grades for class {{class}} subject {{subject}} approved",
          "rejectGrades": "Grades for class {{class}} subject {{subject}} rejected",
          "submitAttendance": "Attendance for class {{class}} on {{date}} submitted",
          "approveAttendance": "Attendance for class {{class}} on {{date}} approved",
          "rejectAttendance": "Attendance for class {{class}} on {{date}} rejected",
          "createAnnouncement": "New announcement created: {{title}}",
          "updateAnnouncement": "Announcement updated: {{title}}",
          "deleteAnnouncement": "Announcement deleted: {{title}}",
          "systemUpdate": "System updated to version {{version}}",
          "settingsChanged": "System settings updated",
          "profileUpdated": "{{user}} profile updated",
          "passwordChanged": "{{user}} password changed",
          "documentUploaded": "Document uploaded: {{name}}",
          "documentDownloaded": "Document downloaded: {{name}}",
          "userCreated": "User {{name}} created in the system",
          "userUpdated": "User {{name}} information updated",
          "userDeleted": "User {{name}} removed from the system",
          "gradeSubmitted": "Grades for class {{class}} submitted",
          "gradeApproved": "Grades for class {{class}} approved",
          "gradeRejected": "Grades for class {{class}} rejected"
        }
      }
    },
    "studentManagement": {
      "title": "Student Management",
      "subtitle": "Manage all students in the system",
      "addStudent": "Add New Student",
      "bulkImport": "Bulk Import Students",
      "searchPlaceholder": "Search by name, email, class, section...",
      "searchingFor": "Searching for",
      "searchResults": "Found {{count}} student(s)",
      "noSearchResults": "No students match your search criteria",
      "clearSearch": "Clear Search",
      "totalStudents": "Total Students",
      "totalClasses": "Total Classes",
      "noStudentsFound": "No students found",
      "addFirstStudent": "Add Your First Student",
      "loadingStudents": "Loading students...",
      "loadingMoreStudents": "Loading more students...",
      "endOfList": "End of list",
      "errorFetchingStudents": "Error fetching students. Please try again.",
      "errorFetchingParents": "Error fetching parents. Please try again.",
      "errorFetchingClasses": "Error fetching classes. Please try again.",
      "assignSection": "Assign Section",
      "assignParent": "Assign Parent",
      "selectParent": "Select Parent",
      "selectParentForStudent": "Select a parent for",
      "addNewParent": "Add New Parent",
      "noParent": "No Parent",
      "parentInfo": "Parent Information",
      "parentAssigned": "Parent assigned successfully",
      "errorAssigningParent": "Failed to assign parent",
      "selectClass": "Select Class",
      "selectSection": "Select Section",
      "assign": "Assign",
      "cancel": "Cancel",
      "confirmDelete": "Confirm Delete",
      "deleteConfirmMessage": "Are you sure you want to delete {{name}}? This action cannot be undone.",
      "deleteSuccess": "Student deleted successfully",
      "deleteError": "Failed to delete student",
      "notAssigned": "Not Assigned"
    },
    "results": {
      "save": "Save Results",
      "approve": "Approve & Notify",
      "saveResults": "Save Results",
      "approveResults": "Approve Results",
      "saveConfirmation": "Are you sure you want to save the results for this class?",
      "approveConfirmation": "Are you sure you want to approve the results for this class?",
      "saveInfo": "This will save the current results to the database. You can approve them later to make them visible to students and parents.",
      "approveWarning": "This will notify all students, parents, and teachers about the approved results.",
      "processing": "Processing...",
      "confirm": "Confirm"
    },
    "navigation": {
      "adminDashboard": "Admin Dashboard",
      "dashboard": "Dashboard",
      "academicManagement": "Academic Management",
      "academicCalendar": "Academic Calendar",
      "semester": "Semester Management",
      "courseManagement": "Course Management",
      "subjectManagement": "Subject Management",
      "gradeSettings": "Grade Settings",
      "userManagement": "User Management",
      "students": "Students",
      "teachers": "Teachers",
      "parents": "Parents",
      "admins": "Administrators",
      "classExam": "Class & Exam",
      "classManagement": "Class Management",
      "examManagement": "Exam Management",
      "examTimetable": "Exam Timetable",
      "classSchedule": "Class Schedule",
      "examRoutine": "Exam Routine",
      "examSchedule": "Exam Schedule",
      "weeklyClassSchedule": "Weekly Class Schedule",
      "results": "Results Management",
      "resultApproval": "Result Approval",
      "gradeApproval": "Grade Approval",
      "teacherTools": "Teacher Tools",
      "teacherSchedule": "Teacher Schedule",
      "teacherEvaluation": "Teacher Evaluation",
      "teacherDocuments": "Teacher Documents",
      "teacherAttendance": "Teacher Attendance",
      "teacherPermissions": "Teacher Permissions",
      "resourcesLibrary": "Resources & Library",
      "libraryManagement": "Library Management",
      "librarySettings": "Library Settings",
      "resourceManagement": "Resource Management",
      "communication": "Communication",
      "messageCenter": "Message Center",
      "announcements": "Announcements",
      "notifications": "Notifications",
      "systemReports": "System & Reports",
      "schoolSettings": "School Settings",
      "timeSettings": "Time Settings",
      "performanceAnalytics": "Performance Analytics"
    },
    "actions": {
      "newMessage": "New Message",
      "newAnnouncement": "New Announcement",
      "addUser": "Add User",
      "logout": "Logout",
      "cancel": "Cancel"
    },
    "messages": {
      "logoutConfirmation": "Are you sure you want to logout from the system?"
    },
    "roles": {
      "admin": "Administrator",
      "teacher": "Teacher",
      "student": "Student",
      "parent": "Parent"
    },
    "student": {
      "dashboard": {
        "title": "Student Dashboard",
        "welcome": "Welcome",
        "searchPlaceholder": "Search...",
        "mainMenu": "Main Menu",
        "todaySchedule": "Today's Schedule",
        "noScheduleToday": "No classes scheduled for today",
        "homework": "Homework",
        "noHomework": "No homework assignments",
        "behavior": "Behavior",
        "noBehavior": "No behavior records",
        "academicPerformance": "Academic Performance",
        "attendanceOverview": "Attendance Overview",
        "recentNotifications": "Recent Notifications",
        "term1": "Term 1",
        "term2": "Term 2",
        "term3": "Term 3",
        "term4": "Term 4",
        "present": "Present",
        "absent": "Absent",
        "gpa": "GPA",
        "dashboard": "Dashboard",
        "analytics": "Analytics",
        "courses": "Courses",
        "assignments": "Assignments",
        "grades": "Grades",
        "attendance": "Attendance",
        "calendar": "Calendar",
        "messaging": "Messaging",
        "profile": "Profile",
        "logout": "Logout",
        "settings": "Settings",
        "menuCategories": {
          "academic": "Academic",
          "scheduleAttendance": "Schedule & Attendance",
          "resources": "Resources & Support",
          "communication": "Communication & Reports"
        },
        "menuItems": {
          "grades": "Grades",
          "results": "Results",
          "subjects": "Subjects",
          "homework": "Homework",
          "schedule": "Schedule",
          "attendance": "Attendance",
          "calendar": "Calendar",
          "examSchedule": "Exam Schedule",
          "library": "Library",
          "portfolio": "Portfolio",
          "behavior": "Behavior",
          "messages": "Messages",
          "reports": "Reports"
        }
      },
      "sidebar": {
        "title": "Student Menu",
        "dashboard": "Dashboard",
        "academic": "Academic",
        "grades": "Grades & Results",
        "subjects": "Subjects",
        "homework": "Homework",
        "schedule": "Schedule & Attendance",
        "classSchedule": "Class Schedule",
        "attendance": "Attendance",
        "examSchedule": "Exam Schedule",
        "resources": "Resources & Support",
        "library": "Library",
        "portfolio": "Portfolio",
        "behavior": "Behavior",
        "communication": "Communication & Reports",
        "messages": "Messages",
        "reports": "Reports",
        "settings": "Settings",
        "profile": "Profile",
        "logout": "Logout"
      },
      "header": {
        "title": "Student Dashboard",
        "notifications": "Notifications",
        "messages": "Messages",
        "profile": "Profile",
        "settings": "Settings",
        "logout": "Logout",
        "search": "Search...",
        "noNotifications": "No notifications",
        "noMessages": "No messages",
        "viewAll": "View All",
        "markAllAsRead": "Mark All as Read"
      },
      "schedule": {
        "title": "Class Schedule",
        "today": "Today",
        "week": "Week",
        "month": "Month",
        "noClasses": "No classes scheduled",
        "subject": "Subject",
        "time": "Time",
        "room": "Room",
        "teacher": "Teacher",
        "currentClass": "Current Class",
        "nextClass": "Next Class",
        "period": "Period",
        "startTime": "Start Time",
        "endTime": "End Time",
        "day": "Day",
        "monday": "Monday",
        "tuesday": "Tuesday",
        "wednesday": "Wednesday",
        "thursday": "Thursday",
        "friday": "Friday",
        "saturday": "Saturday",
        "sunday": "Sunday"
      },
      "homework": {
        "title": "Homework",
        "dueDate": "Due Date",
        "subject": "Subject",
        "status": "Status",
        "submitted": "Submitted",
        "pending": "Pending",
        "late": "Late",
        "graded": "Graded",
        "noHomework": "No homework assignments",
        "submitHomework": "Submit Homework",
        "viewDetails": "View Details",
        "description": "Description",
        "attachments": "Attachments",
        "addAttachment": "Add Attachment",
        "submit": "Submit",
        "cancel": "Cancel",
        "homeworkSubmitted": "Homework submitted successfully",
        "homeworkUpdated": "Homework updated successfully",
        "due": "Due"
      },
      "behavior": {
        "title": "Behavior Records",
        "date": "Date",
        "type": "Type",
        "category": "Category",
        "description": "Description",
        "points": "Points",
        "teacher": "Teacher",
        "positive": "Positive",
        "negative": "Negative",
        "neutral": "Neutral",
        "noBehavior": "No behavior records",
        "viewDetails": "View Details",
        "behaviorDetails": "Behavior Details"
      },
      "grades": {
        "title": "Grades & Results",
        "subject": "Subject",
        "grade": "Grade",
        "percentage": "Percentage",
        "term": "Term",
        "examType": "Exam Type",
        "date": "Date",
        "noGrades": "No grades available",
        "viewDetails": "View Details",
        "gradeDetails": "Grade Details",
        "comments": "Comments",
        "overallGrade": "Overall Grade",
        "classAverage": "Class Average",
        "yourScore": "Your Score",
        "maxScore": "Max Score"
      },
      "attendance": {
        "title": "Attendance Records",
        "date": "Date",
        "status": "Status",
        "present": "Present",
        "absent": "Absent",
        "late": "Late",
        "excused": "Excused",
        "noAttendance": "No attendance records",
        "viewDetails": "View Details",
        "attendanceDetails": "Attendance Details",
        "reason": "Reason",
        "month": "Month",
        "week": "Week",
        "day": "Day",
        "attendanceRate": "Attendance Rate",
        "presentDays": "Present Days",
        "absentDays": "Absent Days",
        "lateDays": "Late Days",
        "excusedDays": "Excused Days"
      },
      "examSchedule": {
        "title": "Exam Schedule",
        "date": "Date",
        "subject": "Subject",
        "time": "Time",
        "room": "Room",
        "duration": "Duration",
        "noExams": "No exams scheduled",
        "viewDetails": "View Details",
        "examDetails": "Exam Details",
        "upcoming": "Upcoming",
        "past": "Past",
        "today": "Today",
        "examType": "Exam Type",
        "instructions": "Instructions",
        "materials": "Required Materials"
      },
      "profile": {
        "title": "Student Profile",
        "personalInfo": "Personal Information",
        "academicInfo": "Academic Information",
        "contactInfo": "Contact Information",
        "name": "Name",
        "email": "Email",
        "phone": "Phone",
        "address": "Address",
        "dateOfBirth": "Date of Birth",
        "gender": "Gender",
        "male": "Male",
        "female": "Female",
        "other": "Other",
        "class": "Class",
        "section": "Section",
        "rollNumber": "Roll Number",
        "admissionDate": "Admission Date",
        "parentInfo": "Parent Information",
        "fatherName": "Father's Name",
        "motherName": "Mother's Name",
        "parentPhone": "Parent's Phone",
        "parentEmail": "Parent's Email",
        "editProfile": "Edit Profile",
        "changePassword": "Change Password",
        "save": "Save",
        "cancel": "Cancel",
        "profileUpdated": "Profile updated successfully",
        "passwordChanged": "Password changed successfully",
        "currentPassword": "Current Password",
        "newPassword": "New Password",
        "confirmPassword": "Confirm Password"
      },
      "messages": {
        "title": "Messages",
        "inbox": "Inbox",
        "sent": "Sent",
        "drafts": "Drafts",
        "trash": "Trash",
        "compose": "Compose",
        "to": "To",
        "subject": "Subject",
        "message": "Message",
        "send": "Send",
        "cancel": "Cancel",
        "reply": "Reply",
        "forward": "Forward",
        "delete": "Delete",
        "noMessages": "No messages",
        "messageSent": "Message sent successfully",
        "messageDeleted": "Message deleted successfully",
        "selectRecipient": "Select Recipient",
        "teachers": "Teachers",
        "students": "Students",
        "parents": "Parents",
        "administrators": "Administrators"
      }
    },
    "homework": {
      "submitted": "Submitted",
      "graded": "Graded",
      "late": "Late",
      "pending": "Pending"
    },
    "behavior": {
      "positive": "Positive",
      "negative": "Negative",
      "neutral": "Neutral",
      "points": "Points"
    },
    "studentDashboard": {
      "title": "Student Dashboard",
      "dashboard": "Dashboard",
      "welcome": "Welcome",
      "gpa": "GPA",
      "attendance": "Attendance",
      "quickAccess": "Quick Access",
      "showAll": "Show All",
      "searchMenu": "Search Menu",
      "todaySchedule": "Today's Schedule",
      "upcomingExams": "Upcoming Exams",
      "noExams": "No upcoming exams",
      "homework": "Homework",
      "noHomework": "No homework assigned",
      "analytics": "Analytics",
      "mainMenu": "Main Menu",
      "courses": "Courses",
      "assignments": "Assignments",
      "grades": "Grades",
      "calendar": "Calendar",
      "messaging": "Messaging",
      "settings": "Settings",
      "profile": "Profile",
      "logout": "Logout",
      "menuCategories": {
        "academic": "Academic",
        "scheduleAttendance": "Schedule & Attendance",
        "resources": "Resources",
        "communication": "Communication",
        "personalTools": "Personal Tools"
      },
      "menuItems": {
        "grades": "Grades",
        "results": "Results",
        "subjects": "Subjects",
        "homework": "Homework",
        "schedule": "Schedule",
        "attendance": "Attendance",
        "calendar": "Calendar",
        "examSchedule": "Exam Schedule",
        "library": "Library",
        "portfolio": "Portfolio",
        "behavior": "Behavior",
        "achievements": "Achievements",
        "messages": "Messages",
        "reports": "Reports",
        "notifications": "Notifications",
        "profile": "Profile",
        "settings": "Settings",
        "help": "Help"
      }
    },
    "homework": {
      "submitted": "Submitted",
      "graded": "Graded",
      "late": "Late",
      "pending": "Pending"
    },
    "behavior": {
      "positive": "Positive",
      "negative": "Negative",
      "neutral": "Neutral",
      "points": "Points"
    },
    "userManagement": {
      "title": "User Management",
      "addUser": "Add User",
      "editUser": "Edit User",
      "deleteUser": "Delete User",
      "userDetails": "User Details",
      "userList": "User List",
      "role": "Role",
      "email": "Email",
      "phone": "Phone",
      "address": "Address",
      "joinDate": "Join Date",
      "status": "Status",
      "actions": "Actions",
      "searchUser": "Search User",
      "filterByRole": "Filter by Role",
      "sortBy": "Sort by",
      "ascending": "Ascending",
      "descending": "Descending",
      "confirmDelete": "Are you sure you want to delete this user?",
      "userDeleted": "User deleted successfully",
      "userAdded": "User added successfully",
      "userUpdated": "User updated successfully",
      "quickAccess": "Quick Access",
      "addAdmin": "Add Admin",
      "addTeacher": "Add Teacher",
      "addStudent": "Add Student",
      "addParent": "Add Parent",
      "userStatistics": "User Statistics",
      "newUsers": "New Users",
      "search": "Search",
      "tooltipFilter": "Filter Users",
      "tooltipSort": "Sort Users",
      "tooltipChangeView": "Change View",
      "admin": "Admin",
      "active": "Active",
      "tooltipEdit": "Edit User",
      "resetPassword": "Reset Password",
      "deactivate": "Deactivate",
      "student": "Student",
      "unknown": "Unknown",
      "parent": "Parent",
      "teacher": "Teacher",
      "allRoles": "All Roles",
      "allStatuses": "All Statuses",
      "inactive": "Inactive",
      "sortName": "Sort by Name",
      "sortEmail": "Sort by Email",
      "sortRole": "Sort by Role",
      "sortStatus": "Sort by Status",
      "sortCreated": "Sort by Creation Date",
      "ascending": "Ascending Order",
      "confirmDeleteMessage": "Are you sure you want to delete this user?",
      "confirmActivate": "Confirm Activate",
      "confirmActivateMessage": "Are you sure you want to activate this user?",
      "confirmDeactivate": "Confirm Deactivate",
      "confirmDeactivateMessage": "Are you sure you want to deactivate this user?",
      "resetPasswordMessage": "Are you sure you want to reset the password for this user?",
      "sendResetEmail": "Send Reset Email"
    },
    "studentManagement": {
      "title": "Student Management",
      "subtitle": "Manage student information",
      "addStudent": "Add New Student",
      "bulkImport": "Bulk Import Students",
      "searchPlaceholder": "Search students...",
      "totalStudents": "Total Students",
      "totalClasses": "Total Classes",
      "name": "Name",
      "email": "Email",
      "class": "Class",
      "section": "Section",
      "parent": "Parent",
      "assignSection": "Assign Section",
      "selectClass": "Select Class",
      "selectSection": "Select Section",
      "cancel": "Cancel",
      "assign": "Assign",
      "assignParent": "Assign Parent",
      "selectParentForStudent": "Select Parent for Student",
      "selectParent": "Select Parent",
      "addNewParent": "Add New Parent",
      "registration": "Student Registration",
      "profileImage": {
        "add": "Add Profile Image"
      },
      "details": {
        "personalInfo": "Personal Information"
      },
      "fields": {
        "firstName": "First Name",
        "lastName": "Last Name",
        "email": "Email",
        "dateOfBirth": "Date of Birth",
        "selectDateOfBirth": "Select Date of Birth",
        "gender": "Gender",
        "gender": {
          "male": "Male",
          "female": "Female",
          "other": "Other"
        },
        "admissionNumber": "Admission Number",
        "phone": "Phone",
        "emergencyContact": "Emergency Contact"
      },
      "address": "Address",
      "address": {
        "street": "Street",
        "city": "City",
        "state": "State/Province",
        "country": "Country"
      },
      "parentInformation": "Parent Information",
      "parent": {
        "selectParent": "Select Parent",
        "email": "Parent Email",
        "addNew": "Add New Parent",
        "instructions": "You can either select an existing parent or add a new one",
        "found": "Parent Found",
        "foundMessage": "A parent with this email already exists",
        "linkQuestion": "Do you want to link this parent to the student?"
      },
      "academicDetails": "Academic Details",
      "academicDetails": {
        "previousSchool": "Previous School",
        "previousGrade": "Previous Grade",
        "reasonForLeaving": "Reason for Leaving",
        "previousResultImage": "Previous Result Image",
        "addResultImage": "Add Result Image"
      },
      "add": "Add Student",
      "messages": {
        "registrationSuccess": "Student Registration Successful",
        "addSuccess": "Student Added Successfully"
      }
    },
    "validation": {
      "password": "Password must be at least 6 characters"
    },
    "bulkImport": {
      "title": "Bulk Import Students",
      "description": "Upload a CSV file to import multiple students at once",
      "requiredColumns": "Required Columns",
      "optionalColumns": "Optional Columns",
      "parentColumns": "Parent Columns",
      "selectFile": "Select File"
    },
    "Dashboard": "Admin Dashboard"
  },
  "teacher": {
    "dashboard": {
      "title": "Teacher Dashboard",
      "welcome": "Welcome",
      "quickStats": "Quick Stats",
      "quickAccess": "Quick Access",
      "classes": "Classes",
      "students": "Students",
      "assignments": "Assignments",
      "attendance": "Attendance",
      "messages": "Messages",
      "notifications": "Notifications",
      "studentPerformance": "Student Performance",
      "performanceSubtitle": "Average scores measured over time",
      "averagePerformance": "Average Performance",
      "viewDetailedPerformance": "View detailed performance",
      "attendanceStats": "Attendance Statistics",
      "present": "Present",
      "absent": "Absent",
      "late": "Late",
      "excused": "Excused",
      "gradingProgress": "Grading Progress",
      "completed": "Completed",
      "todaySchedule": "Today's Schedule",
      "noSchedule": "No schedule for today",
      "viewFullSchedule": "View full schedule",
      "recentActivities": "Recent Activities",
      "viewAll": "View all",
      "noActivities": "No recent activities found",
      "statsRefreshed": "Statistics refreshed",
      "activityDetails": "Activity Details",
      "activitySelected": "Selected activity: {{title}}",
      "newExamSchedule": "New Exam Schedule",
      "examScheduleNotification": "There is a new exam schedule",
      "currentClass": "Current Class",
      "current": "Current",
      "preparingDashboard": "Preparing dashboard",
      "viewClassPerformance": "View class performance",
      "viewStudentPerformance": "View student performance",
      "exportData": "Export data",
      "manageClasses": "Manage your classes",
      "createAssessments": "Create and manage assessments",
      "recordGrades": "Record and manage grades",
      "assignHomework": "Assign and track homework",
      "attendanceMonitoring": "Attendance & Monitoring",
      "realTimeAttendance": "Real-time Attendance",
      "examAttendance": "Exam Attendance",
      "examSchedule": "Exam Schedule",
      "behavior": "Behavior",
      "takeAttendance": "Take and manage attendance",
      "monitorAttendance": "Monitor attendance in real-time",
      "trackExamAttendance": "Track exam attendance",
      "viewExamSchedules": "View exam schedules",
      "monitorBehavior": "Monitor student behavior",
      "reportsAnalytics": "Reports & Analytics",
      "reports": "Reports",
      "classAnalytics": "Class Analytics",
      "markSheets": "Mark Sheets",
      "studentPortfolios": "Student Portfolios",
      "viewReports": "View reports",
      "analyzePerformance": "Analyze performance",
      "submitMarkSheets": "Submit mark sheets",
      "reviewPortfolios": "Review portfolios",
      "communicationResources": "Communication & Resources",
      "messaging": "Messaging",
      "calendar": "Calendar",
      "library": "Library",
      "resources": "Resources",
      "communicate": "Communicate with students and parents",
      "manageSchedules": "Manage schedules",
      "accessLibrary": "Access library",
      "requestResources": "Request resources",
      "assessments": "Assessments",
      "grades": "Grades",
      "homework": "Homework",
      "profile": "Profile",
      "settings": "Settings",
      "help": "Help",
      "logout": "Logout",
      "dashboard": "Dashboard"
    },
    "drawerMenu": {
      "academic": "Academic Management",
      "personal": "Personal"
    },
    "communication": {
      "newMessage": "New Message",
      "teacherTitle": "Teacher Communication",
      "chats": "Chats",
      "searchMessages": "Search messages",
      "findUsers": "Find users",
      "noConversations": "No conversations yet",
      "startNew": "Start a new conversation",
      "selectConversation": "Select a conversation",
      "selectUser": "Select a user",
      "searchUsers": "Search users",
      "noUsersFound": "No users found",
      "to": "To",
      "subject": "Subject",
      "message": "Message",
      "loading": "Loading..."
    },
    "attendance": {
      "title": "Attendance Management",
      "studentList": "Student List"
    },
    "grades": {
      "title": "Grade Management"
    },
    "homework": {
      "title": "Homework Management"
    },
    "exams": {
      "title": "Exams"
    },
    "classes": {
      "title": "Classes"
    },
    "calendar": {
      "title": "Calendar Management"
    },
    "schedule": {
      "title": "Schedule Management"
    },
    "directory": {
      "title": "Teacher Directory",
      "searchPlaceholder": "Search teachers...",
      "allDepartments": "All Departments",
      "subjects": "Subjects",
      "noSubjects": "No subjects assigned"
    },
    "classManagement": {
      "title": "Class Management",
      "myClasses": "My Classes",
      "allClasses": "All Classes",
      "activeClasses": "Active Classes",
      "pastClasses": "Past Classes",
      "upcomingClasses": "Upcoming Classes",
      "class": "Class",
      "section": "Section",
      "teacher": "Teacher",
      "capacity": "Capacity",
      "students": "Students",
      "sections": "Sections",
      "subjects": "Subjects",
      "noDescription": "No description available",
      "noClasses": "No classes available",
      "noClassesFound": "No classes found",
      "noSections": "No sections available",
      "noStudents": "No students in this section",
      "studentsList": "Students List",
      "youTeachHere": "You teach here",
      "yourSection": "Your section",
      "errorFetchingTeacher": "Error fetching teacher data",
      "errorFetchingClasses": "Error fetching classes",
      "errorProcessingSections": "Error processing sections",
      "errorFetchingStudents": "Error fetching students",
      "errorLoadingSections": "Error loading sections",
      "errorLoadingStudents": "Error loading students"
    }
  },
  "Cloudinary Test": "Cloudinary Test",
  "Admin Dashboard": "Admin Dashboard",
  "parent": {
    "dashboard": {
      "title": "Parent Dashboard",
      "welcome": "Welcome",
      "selectChild": "Select Child",
      "noChildrenRegistered": "No children registered in the system.",
      "updateProfile": "Update Profile",
      "searchMenuItems": "Search menu items...",
      "active": "Active",
      "gpa": "GPA",
      "attendance": "Attendance",
      "behavior": "Behavior",
      "classStatus": "Class Status",
      "academicProgress": "Academic Progress",
      "academicSummary": "Your child is doing well in most subjects and has shown good improvement in English and Math.",
      "viewGrades": "View Detailed Grades",
      "attendanceOverview": "Attendance Overview",
      "noSearchResults": "No results found for your search",
      "clearSearch": "Clear Search",
      "errorLoadingData": "Failed to load dashboard data. Please try again.",
      "errorLoadingChildData": "Failed to load child data. Please try again.",
      "present": "Present",
      "absent": "Absent",
      "late": "Late",
      "viewAttendance": "View Attendance Records",
      "behaviorAnalysis": "Behavior Analysis",
      "behaviorSummary": "Your child demonstrates excellent behavior in class, actively participates, and shows respect for teachers and peers.",
      "viewBehaviorRecords": "View Behavior Records",
      "upcomingEvents": "Upcoming Events",
      "viewCalendar": "View Full Calendar",
      "quickAccess": "Quick Access",
      "showLess": "Show Less",
      "showAll": "Show All",
      "showMoreCategories": "Show More Categories",
      "examSchedule": "Exam Schedule",
      "classSchedule": "Class Schedule",
      "messages": "Messages",
      "noChildrenTitle": "No Children Found",
      "noChildrenMessage": "No children are associated with your account. Please contact the administrator.",
      "errorTitle": "Error",
      "childDataError": "There was a problem loading your children's data. Some information may be unavailable.",
      "parentDataError": "Your parent profile could not be found. Please contact the administrator.",
      "dataLoadError": "There was a problem loading your data. Please try again later."
    },
    "childSchedule": "Child Schedule",
    "selectChild": "Select Child",
    "navigation": {
      "yourChildren": "Your Children",
      "dashboard": "Dashboard",
      "academics": "Academics",
      "grades": "Grades & Results",
      "homework": "Homework",
      "classSchedule": "Class Schedule",
      "examSchedule": "Exam Schedule",
      "attendance": "Attendance",
      "attendanceView": "View Attendance",
      "attendanceReport": "Attendance Report",
      "behavior": "Behavior",
      "behaviorView": "View Behavior",
      "behaviorReport": "Behavior Report",
      "communication": "Communication",
      "notifications": "Notifications",
      "profile": "Profile",
      "settings": "Settings",
      "selectChildFirst": "Please select a child first",
      "weeklyClassSchedule": "Weekly Class Schedule",
      "messages": "Messages",
      "parentTeacherMeetings": "Parent-Teacher Meetings"
    },
    "menu": {
      "academicMonitoring": "Academic Monitoring",
      "academicProgress": "Academic Progress",
      "viewGrades": "View Grades",
      "attendanceRecords": "Attendance Records",
      "examSchedule": "Exam Schedule",
      "classSchedule": "Class Schedule",
      "homeworkStatus": "Homework Status",
      "subjectPerformance": "Subject Performance",
      "behaviorRecords": "Behavior Records",
      "communication": "Communication",
      "messageCenter": "Message Center",
      "schoolAnnouncements": "School Announcements",
      "parentTeacherMeetings": "Parent-Teacher Meetings",
      "eventCalendar": "Event Calendar",
      "notifications": "Notifications",
      "settings": "Settings",
      "profileManagement": "Profile Management"
    },
    "classSchedule": {
      "loadingSchedule": "Loading today's schedule...",
      "failedToLoadSchedule": "Failed to load schedule. Please try again.",
      "noClassesToday": "No classes scheduled for today",
      "viewFullSchedule": "View Full Schedule",
      "today": "Today",
      "classes": "Classes",
      "current": "Current",
      "completed": "Completed",
      "upcoming": "Upcoming",
      "complete": "complete",
      "teacher": "Teacher",
      "room": "Room",
      "contactTeacher": "Contact Teacher"
    },
    "sidebar": {
      "yourChildren": "Your Children",
      "dashboard": "Dashboard",
      "academics": "Academics",
      "attendance": "Attendance",
      "behavior": "Behavior",
      "communication": "Communication",
      "profile": "Profile",
      "settings": "Settings",
      "logout": "Logout"
    },
    "studentMonitoring": {
      "title": "Student Progress"
    },
    "attendance": {
      "title": "Attendance Records"
    },
    "communication": {
      "title": "Communication",
      "parentTitle": "Parent Communication Center"
    },
    "reports": {
      "title": "Report Center"
    }
  }
}