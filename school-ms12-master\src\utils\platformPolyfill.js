/**
 * Platform polyfill for environments where react-native's Platform is not available
 * This provides a fallback implementation when the standard Platform module is not accessible
 */

// Create a global Platform object if it doesn't exist
if (typeof Platform === 'undefined') {
  console.log('Applying Platform polyfill');
  
  // Determine platform based on available global objects
  let detectedOS = 'unknown';
  
  // Try to detect platform
  if (typeof navigator !== 'undefined') {
    const userAgent = navigator.userAgent || '';
    if (/android/i.test(userAgent)) {
      detectedOS = 'android';
    } else if (/iPad|iPhone|iPod/.test(userAgent) || 
              (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1)) {
      detectedOS = 'ios';
    } else if (/Win/.test(navigator.platform)) {
      detectedOS = 'windows';
    } else if (/Mac/.test(navigator.platform)) {
      detectedOS = 'macos';
    } else if (/Linux/.test(navigator.platform)) {
      detectedOS = 'linux';
    } else if (typeof document !== 'undefined') {
      detectedOS = 'web';
    }
  }
  
  // Create global Platform object
  global.Platform = {
    OS: detectedOS,
    select: function(obj) {
      return obj[this.OS] || obj.default || null;
    },
    // Add other Platform properties as needed
    Version: 0,
    isPad: false,
    isTV: false,
    isTesting: false
  };
}

export default global.Platform;
