import React, { createContext, useContext, useState, useEffect } from 'react';
import { db } from '../config/firebase';
import { collection, query, where, getDocs, onSnapshot } from 'firebase/firestore';
import { useAuth } from './AuthContext';

const DataContext = createContext();

export const useData = () => useContext(DataContext);

export const DataProvider = ({ children }) => {
  const { user } = useAuth();
  const [userData, setUserData] = useState(null);
  const [userRole, setUserRole] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Cache for frequently accessed data
  const [classesCache, setClassesCache] = useState({});
  const [studentsCache, setStudentsCache] = useState({});
  const [teachersCache, setTeachersCache] = useState({});
  const [parentsCache, setParentsCache] = useState({});

  useEffect(() => {
    if (user) {
      fetchUserRole();
    }
  }, [user]);

  const fetchUserRole = async () => {
    try {
      // Check each role collection
      const roles = ['admins', 'teachers', 'students', 'parents'];
      for (const role of roles) {
        const q = query(collection(db, role), where('userId', '==', user.uid));
        const snapshot = await getDocs(q);
        if (!snapshot.empty) {
          const data = snapshot.docs[0].data();
          setUserRole(role);
          setUserData(data);
          break;
        }
      }
    } catch (error) {
      console.error('Error fetching user role:', error);
      setError(error);
    } finally {
      setLoading(false);
    }
  };

  // Real-time listeners for different data types
  const subscribeToCollection = (collectionName, queryConstraints = [], callback) => {
    const q = query(collection(db, collectionName), ...queryConstraints);
    return onSnapshot(q, (snapshot) => {
      const data = {};
      snapshot.forEach((doc) => {
        data[doc.id] = { id: doc.id, ...doc.data() };
      });
      callback(data);
    });
  };

  // Cache management functions
  const updateCache = (cacheName, data) => {
    switch (cacheName) {
      case 'classes':
        setClassesCache(data);
        break;
      case 'students':
        setStudentsCache(data);
        break;
      case 'teachers':
        setTeachersCache(data);
        break;
      case 'parents':
        setParentsCache(data);
        break;
      default:
        console.warn('Unknown cache type:', cacheName);
    }
  };

  // Data access functions based on user role
  const getAccessibleData = async (collectionName, constraints = []) => {
    try {
      const q = query(collection(db, collectionName), ...constraints);
      const snapshot = await getDocs(q);
      const data = {};
      snapshot.forEach((doc) => {
        data[doc.id] = { id: doc.id, ...doc.data() };
      });
      return data;
    } catch (error) {
      console.error('Error fetching data:', error);
      throw error;
    }
  };

  // Role-specific data access
  const getAdminData = async () => {
    if (userRole !== 'admins') return null;
    return {
      classes: await getAccessibleData('classes'),
      teachers: await getAccessibleData('teachers'),
      students: await getAccessibleData('students'),
      parents: await getAccessibleData('parents'),
    };
  };

  const getTeacherData = async () => {
    if (userRole !== 'teachers') return null;
    const teacherClasses = await getAccessibleData('classes', [
      where('teacherId', '==', user.uid)
    ]);
    const classIds = Object.keys(teacherClasses);
    return {
      classes: teacherClasses,
      students: await getAccessibleData('students', [
        where('classId', 'in', classIds)
      ]),
      parents: await getAccessibleData('parents', [
        where('classId', 'in', classIds)
      ]),
    };
  };

  const getStudentData = async () => {
    if (userRole !== 'students') return null;
    const studentData = userData;
    return {
      class: await getAccessibleData('classes', [
        where('id', '==', studentData.classId)
      ]),
      teachers: await getAccessibleData('teachers', [
        where('classId', '==', studentData.classId)
      ]),
    };
  };

  const getParentData = async () => {
    if (userRole !== 'parents') return null;
    const parentData = userData;
    return {
      student: await getAccessibleData('students', [
        where('id', '==', parentData.studentId)
      ]),
      class: await getAccessibleData('classes', [
        where('id', '==', parentData.classId)
      ]),
      teachers: await getAccessibleData('teachers', [
        where('classId', '==', parentData.classId)
      ]),
    };
  };

  // Integration functions
  const integrationFunctions = {
    admin: {
      manageUsers: async (action, userData) => {
        // Implement user management logic
      },
      publishAnnouncement: async (announcement) => {
        // Implement announcement publishing logic
      },
    },
    teacher: {
      submitGrades: async (grades) => {
        // Implement grade submission logic
      },
      recordAttendance: async (attendance) => {
        // Implement attendance recording logic
      },
    },
    student: {
      viewGrades: async () => {
        // Implement grade viewing logic
      },
      submitAssignment: async (assignment) => {
        // Implement assignment submission logic
      },
    },
    parent: {
      trackProgress: async () => {
        // Implement progress tracking logic
      },
      communicateWithTeacher: async (message) => {
        // Implement teacher communication logic
      },
    },
  };

  const value = {
    userData,
    userRole,
    loading,
    error,
    classesCache,
    studentsCache,
    teachersCache,
    parentsCache,
    getAdminData,
    getTeacherData,
    getStudentData,
    getParentData,
    integrationFunctions: integrationFunctions[userRole] || {},
    subscribeToCollection,
    updateCache,
  };

  return (
    <DataContext.Provider value={value}>
      {children}
    </DataContext.Provider>
  );
};

export default DataContext;
