import React, { useState, useEffect } from 'react';
import { View, ScrollView, StyleSheet, Dimensions } from 'react-native';
import { Card, Title, Text, DataTable, ActivityIndicator, Chip } from 'react-native-paper';
import { db, auth } from '../../config/firebase';
import { collection, query, where, getDocs, doc, getDoc } from 'firebase/firestore';
import CustomButton from '../../components/common/CustomButton';
import { Picker } from '@react-native-picker/picker';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Pie<PERSON>hart } from 'react-native-chart-kit';
import EthiopianCalendar from '../../utils/EthiopianCalendar';
import { sanitizeChartData, sanitizeChartDatasets } from '../../utils/ChartUtils';

const ClassPerformance = () => {
  const [classes, setClasses] = useState([]);
  const [selectedClass, setSelectedClass] = useState('');
  const [subjects, setSubjects] = useState([]);
  const [selectedSubject, setSelectedSubject] = useState('');
  const [loading, setLoading] = useState(true);
  const [analytics, setAnalytics] = useState({
    gradeDistribution: {},
    averageScore: 0,
    attendanceRate: 0,
    performanceTrends: [],
    studentPerformance: [],
    weakAreas: [],
  });

  useEffect(() => {
    fetchTeacherClasses();
  }, []);

  useEffect(() => {
    if (selectedClass) {
      fetchSubjects();
      fetchAnalytics();
    }
  }, [selectedClass, selectedSubject]);

  const fetchTeacherClasses = async () => {
    try {
      const userDoc = await getDoc(doc(db, 'users', auth.currentUser.uid));
      const assignedClasses = userDoc.data().assignedClasses || [];

      const classesRef = collection(db, 'classes');
      const q = query(classesRef, where('__name__', 'in', assignedClasses));
      const querySnapshot = await getDocs(q);

      const classData = [];
      querySnapshot.forEach((doc) => {
        classData.push({ id: doc.id, ...doc.data() });
      });

      setClasses(classData.sort((a, b) => a.name.localeCompare(b.name)));
      setLoading(false);
    } catch (error) {
      console.error('Error fetching classes:', error);
      setLoading(false);
    }
  };

  const fetchSubjects = async () => {
    try {
      const subjectsRef = collection(db, 'subjects');
      const q = query(
        subjectsRef,
        where('classId', '==', selectedClass),
        where('teacherId', '==', auth.currentUser.uid)
      );
      const querySnapshot = await getDocs(q);

      const subjectData = [];
      querySnapshot.forEach((doc) => {
        subjectData.push({ id: doc.id, ...doc.data() });
      });

      setSubjects(subjectData.sort((a, b) => a.name.localeCompare(b.name)));
    } catch (error) {
      console.error('Error fetching subjects:', error);
    }
  };

  const fetchAnalytics = async () => {
    try {
      setLoading(true);

      // Fetch grades
      const gradesRef = collection(db, 'grades');
      let q = query(gradesRef, where('classId', '==', selectedClass));
      if (selectedSubject) {
        q = query(q, where('subjectId', '==', selectedSubject));
      }
      const gradesSnapshot = await getDocs(q);

      // Fetch attendance
      const attendanceRef = collection(db, 'attendance');
      q = query(attendanceRef, where('classId', '==', selectedClass));
      const attendanceSnapshot = await getDocs(q);

      // Process grades data
      const grades = [];
      const gradeDistribution = { A: 0, B: 0, C: 0, D: 0, F: 0 };
      let totalScore = 0;
      let totalGrades = 0;
      const studentScores = {};

      gradesSnapshot.forEach((doc) => {
        const grade = doc.data();
        grades.push(grade);

        const score = parseInt(grade.score);
        if (score >= 90) gradeDistribution.A++;
        else if (score >= 80) gradeDistribution.B++;
        else if (score >= 70) gradeDistribution.C++;
        else if (score >= 60) gradeDistribution.D++;
        else gradeDistribution.F++;

        totalScore += score;
        totalGrades++;

        if (!studentScores[grade.studentId]) {
          studentScores[grade.studentId] = [];
        }
        studentScores[grade.studentId].push(score);
      });

      // Process attendance data
      let totalAttendance = 0;
      let totalDays = 0;
      attendanceSnapshot.forEach((doc) => {
        const attendance = doc.data();
        totalDays++;
        if (attendance.status === 'present') {
          totalAttendance++;
        }
      });

      // Calculate student performance
      const studentPerformance = await Promise.all(
        Object.entries(studentScores).map(async ([studentId, scores]) => {
          const average = scores.reduce((a, b) => a + b, 0) / scores.length;
          const studentDoc = await getDoc(doc(db, 'users', studentId));
          const studentData = studentDoc.data();
          return {
            studentId,
            name: studentData.displayName,
            average,
            scores,
          };
        })
      );

      studentPerformance.sort((a, b) => b.average - a.average);

      // Calculate weak areas based on score distribution
      const weakAreas = [];
      if (gradeDistribution.D + gradeDistribution.F > totalGrades * 0.3) {
        weakAreas.push('High failure rate');
      }
      if (gradeDistribution.A + gradeDistribution.B < totalGrades * 0.2) {
        weakAreas.push('Low high-performance rate');
      }
      if (totalAttendance / totalDays < 0.8) {
        weakAreas.push('Poor attendance');
      }

      setAnalytics({
        gradeDistribution,
        averageScore: totalScore / totalGrades,
        attendanceRate: (totalAttendance / totalDays) * 100,
        performanceTrends: studentPerformance.map(student => student.average),
        studentPerformance,
        weakAreas,
      });

      setLoading(false);
    } catch (error) {
      console.error('Error fetching analytics:', error);
      setLoading(false);
    }
  };

  const getGradeDistributionData = () => {
    const colors = ['#4CAF50', '#8BC34A', '#FFC107', '#FF9800', '#f44336'];
    return {
      labels: ['A', 'B', 'C', 'D', 'F'],
      datasets: [{
        data: sanitizeChartData([
          analytics.gradeDistribution.A || 0,
          analytics.gradeDistribution.B || 0,
          analytics.gradeDistribution.C || 0,
          analytics.gradeDistribution.D || 0,
          analytics.gradeDistribution.F || 0,
        ]),
      }],
      colors,
    };
  };

  const getPerformanceDistributionData = () => {
    const performanceRanges = {
      '90-100': 0,
      '80-89': 0,
      '70-79': 0,
      '60-69': 0,
      '<60': 0,
    };

    analytics.studentPerformance.forEach(student => {
      if (student.average >= 90) performanceRanges['90-100']++;
      else if (student.average >= 80) performanceRanges['80-89']++;
      else if (student.average >= 70) performanceRanges['70-79']++;
      else if (student.average >= 60) performanceRanges['60-69']++;
      else performanceRanges['<60']++;
    });

    return {
      labels: Object.keys(performanceRanges),
      datasets: [{
        data: sanitizeChartData(Object.values(performanceRanges)),
      }],
    };
  };

  return (
    <ScrollView style={styles.container}>
      <Card style={styles.card}>
        <Card.Content>
          <Title>Class Performance Analytics</Title>

          <View style={styles.filterContainer}>
            <View style={styles.pickerContainer}>
              <Text style={styles.pickerLabel}>Select Class</Text>
              <Picker
                selectedValue={selectedClass}
                onValueChange={(value) => {
                  setSelectedClass(value);
                  setSelectedSubject('');
                }}
                style={styles.picker}
              >
                <Picker.Item label="Select..." value="" />
                {classes.map((cls) => (
                  <Picker.Item
                    key={cls.id}
                    label={cls.name}
                    value={cls.id}
                  />
                ))}
              </Picker>
            </View>

            <View style={styles.pickerContainer}>
              <Text style={styles.pickerLabel}>Select Subject</Text>
              <Picker
                selectedValue={selectedSubject}
                onValueChange={setSelectedSubject}
                style={styles.picker}
                enabled={!!selectedClass}
              >
                <Picker.Item label="All Subjects" value="" />
                {subjects.map((subject) => (
                  <Picker.Item
                    key={subject.id}
                    label={subject.name}
                    value={subject.id}
                  />
                ))}
              </Picker>
            </View>
          </View>

          {loading ? (
            <ActivityIndicator style={styles.loader} />
          ) : selectedClass ? (
            <View>
              <View style={styles.summaryContainer}>
                <Card style={styles.summaryCard}>
                  <Card.Content>
                    <Title>Average Score</Title>
                    <Text style={styles.summaryText}>
                      {analytics.averageScore.toFixed(1)}%
                    </Text>
                  </Card.Content>
                </Card>

                <Card style={styles.summaryCard}>
                  <Card.Content>
                    <Title>Attendance Rate</Title>
                    <Text style={styles.summaryText}>
                      {analytics.attendanceRate.toFixed(1)}%
                    </Text>
                  </Card.Content>
                </Card>
              </View>

              <Card style={styles.analyticsCard}>
                <Card.Content>
                  <Title>Grade Distribution</Title>
                  <BarChart
                    data={sanitizeChartDatasets(getGradeDistributionData())}
                    width={Dimensions.get('window').width - 80}
                    height={220}
                    chartConfig={{
                      backgroundColor: '#ffffff',
                      backgroundGradientFrom: '#ffffff',
                      backgroundGradientTo: '#ffffff',
                      decimalPlaces: 0,
                      color: (opacity = 1) => `rgba(33, 150, 243, ${opacity})`,
                    }}
                    style={styles.chart}
                  />
                </Card.Content>
              </Card>

              <Card style={styles.analyticsCard}>
                <Card.Content>
                  <Title>Performance Distribution</Title>
                  <BarChart
                    data={sanitizeChartDatasets(getPerformanceDistributionData())}
                    width={Dimensions.get('window').width - 80}
                    height={220}
                    chartConfig={{
                      backgroundColor: '#ffffff',
                      backgroundGradientFrom: '#ffffff',
                      backgroundGradientTo: '#ffffff',
                      decimalPlaces: 0,
                      color: (opacity = 1) => `rgba(33, 150, 243, ${opacity})`,
                    }}
                    style={styles.chart}
                  />
                </Card.Content>
              </Card>

              <Card style={styles.analyticsCard}>
                <Card.Content>
                  <Title>Student Performance</Title>
                  <DataTable>
                    <DataTable.Header>
                      <DataTable.Title>Student</DataTable.Title>
                      <DataTable.Title numeric>Average</DataTable.Title>
                      <DataTable.Title>Status</DataTable.Title>
                    </DataTable.Header>

                    {analytics.studentPerformance.map((student) => (
                      <DataTable.Row key={student.studentId}>
                        <DataTable.Cell>{student.name}</DataTable.Cell>
                        <DataTable.Cell numeric>
                          {student.average.toFixed(1)}%
                        </DataTable.Cell>
                        <DataTable.Cell>
                          <Chip
                            style={[
                              styles.statusChip,
                              {
                                backgroundColor: student.average >= 70
                                  ? '#4CAF50'
                                  : student.average >= 60
                                    ? '#FF9800'
                                    : '#f44336'
                              }
                            ]}
                          >
                            {student.average >= 70
                              ? 'Good'
                              : student.average >= 60
                                ? 'Average'
                                : 'Needs Help'}
                          </Chip>
                        </DataTable.Cell>
                      </DataTable.Row>
                    ))}
                  </DataTable>
                </Card.Content>
              </Card>

              {analytics.weakAreas.length > 0 && (
                <Card style={styles.analyticsCard}>
                  <Card.Content>
                    <Title>Areas for Improvement</Title>
                    {analytics.weakAreas.map((area, index) => (
                      <Chip
                        key={index}
                        style={styles.weakAreaChip}
                        textStyle={styles.weakAreaText}
                      >
                        {area}
                      </Chip>
                    ))}
                  </Card.Content>
                </Card>
              )}
            </View>
          ) : (
            <Text style={styles.selectPrompt}>
              Please select a class to view performance analytics.
            </Text>
          )}
        </Card.Content>
      </Card>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  card: {
    margin: 16,
  },
  filterContainer: {
    marginVertical: 16,
  },
  pickerContainer: {
    marginBottom: 16,
  },
  pickerLabel: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
  },
  picker: {
    backgroundColor: '#f5f5f5',
    borderRadius: 4,
  },
  loader: {
    marginVertical: 20,
  },
  summaryContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  summaryCard: {
    flex: 1,
    marginHorizontal: 4,
  },
  summaryText: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginTop: 8,
  },
  analyticsCard: {
    marginVertical: 8,
  },
  chart: {
    marginVertical: 8,
    borderRadius: 16,
  },
  selectPrompt: {
    textAlign: 'center',
    marginTop: 20,
    color: '#666',
  },
  statusChip: {
    alignSelf: 'flex-start',
  },
  weakAreaChip: {
    marginVertical: 4,
    backgroundColor: '#ffebee',
  },
  weakAreaText: {
    color: '#d32f2f',
  },
});

export default ClassPerformance;
