  // Render user details modal
  const renderUserDetailsModal = () => {
    if (!selectedUser) return null;
    
    const fullName = `${selectedUser.firstName || ''} ${selectedUser.lastName || ''}`;
    
    // Get role-specific color
    const getRoleColor = (role) => {
      switch(role) {
        case 'admin': return mobileTheme.colors.admin;
        case 'teacher': return mobileTheme.colors.teacher;
        case 'student': return mobileTheme.colors.student;
        case 'parent': return mobileTheme.colors.parent;
        default: return mobileTheme.colors.primary;
      }
    };
    
    return (
      <Portal>
        <Modal
          visible={showUserDetailsModal}
          onDismiss={() => setShowUserDetailsModal(false)}
          contentContainerStyle={styles.modalContainer}
        >
          <Surface style={styles.modalSurface}>
            <View style={styles.modalHeader}>
              <Title style={styles.modalTitle}>
                {translate('userManagement.userDetails')}
              </Title>
              <IconButton
                icon="close"
                size={24}
                onPress={() => setShowUserDetailsModal(false)}
              />
            </View>
            
            <Divider />
            
            <ScrollView style={styles.modalContent}>
              <View style={styles.userDetailHeader}>
                {selectedUser.photoURL ? (
                  <Avatar.Image 
                    source={{ uri: selectedUser.photoURL }} 
                    size={80} 
                    style={styles.detailAvatar}
                  />
                ) : (
                  <Avatar.Text 
                    label={`${selectedUser.firstName?.[0] || ''}${selectedUser.lastName?.[0] || ''}`.toUpperCase()} 
                    size={80} 
                    style={[styles.detailAvatar, { backgroundColor: getRoleColor(selectedUser.role) }]}
                  />
                )}
                
                <View style={styles.userDetailInfo}>
                  <Title style={styles.detailName}>{fullName}</Title>
                  <Paragraph style={styles.detailEmail}>{selectedUser.email}</Paragraph>
                  
                  <View style={styles.detailChips}>
                    <Chip 
                      mode="outlined" 
                      style={[styles.roleChip, { borderColor: getRoleColor(selectedUser.role) }]}
                      textStyle={{ color: getRoleColor(selectedUser.role) }}
                    >
                      {translate(`userManagement.roles.${selectedUser.role}`) || selectedUser.role}
                    </Chip>
                    
                    <Chip 
                      mode="outlined" 
                      style={[
                        styles.statusChip, 
                        { 
                          borderColor: selectedUser.status === 'active' ? mobileTheme.colors.success : mobileTheme.colors.error,
                          backgroundColor: selectedUser.status === 'active' ? mobileTheme.colors.success + '20' : mobileTheme.colors.error + '20'
                        }
                      ]}
                      textStyle={{ 
                        color: selectedUser.status === 'active' ? mobileTheme.colors.success : mobileTheme.colors.error 
                      }}
                    >
                      {translate(`userManagement.status.${selectedUser.status}`) || selectedUser.status}
                    </Chip>
                  </View>
                </View>
              </View>
              
              <Divider style={styles.divider} />
              
              <List.Section>
                <List.Subheader>{translate('userManagement.contactInfo')}</List.Subheader>
                
                <List.Item
                  title={translate('userManagement.email')}
                  description={selectedUser.email || translate('common.notAvailable')}
                  left={props => <List.Icon {...props} icon="email" />}
                />
                
                <List.Item
                  title={translate('userManagement.phone')}
                  description={selectedUser.phoneNumber || translate('common.notAvailable')}
                  left={props => <List.Icon {...props} icon="phone" />}
                />
                
                <List.Item
                  title={translate('userManagement.address')}
                  description={
                    typeof selectedUser.address === 'string' 
                      ? selectedUser.address 
                      : (selectedUser.address?.street 
                          ? `${selectedUser.address.street}, ${selectedUser.address.city || ''}`
                          : translate('common.notAvailable'))
                  }
                  left={props => <List.Icon {...props} icon="map-marker" />}
                />
              </List.Section>
              
              <Divider style={styles.divider} />
              
              <List.Section>
                <List.Subheader>{translate('userManagement.accountInfo')}</List.Subheader>
                
                <List.Item
                  title={translate('userManagement.createdAt')}
                  description={
                    selectedUser.createdAt 
                      ? format(new Date(selectedUser.createdAt), 'PPP')
                      : translate('common.notAvailable')
                  }
                  left={props => <List.Icon {...props} icon="calendar-plus" />}
                />
                
                <List.Item
                  title={translate('userManagement.lastLogin')}
                  description={
                    selectedUser.lastLogin 
                      ? formatDistance(new Date(selectedUser.lastLogin), new Date(), { addSuffix: true })
                      : translate('common.notAvailable')
                  }
                  left={props => <List.Icon {...props} icon="login" />}
                />
                
                <List.Item
                  title={translate('userManagement.emailVerified')}
                  description={
                    selectedUser.emailVerified 
                      ? translate('common.yes')
                      : translate('common.no')
                  }
                  left={props => <List.Icon {...props} icon={selectedUser.emailVerified ? "check-circle" : "alert-circle"} />}
                />
              </List.Section>
            </ScrollView>
            
            <Divider />
            
            <View style={styles.modalActions}>
              <Button
                mode="outlined"
                onPress={() => setShowUserDetailsModal(false)}
                style={styles.cancelButton}
              >
                {translate('common.close')}
              </Button>
              
              <Button
                mode="outlined"
                onPress={() => {
                  setShowUserDetailsModal(false);
                  setShowPasswordResetDialog(true);
                }}
                style={styles.resetButton}
                icon="key"
              >
                {translate('userManagement.resetPassword')}
              </Button>
              
              <Button
                mode="contained"
                onPress={() => {
                  setShowUserDetailsModal(false);
                  navigation.navigate('UserEdit', { userId: selectedUser.id });
                }}
                style={styles.editButton}
                icon="pencil"
              >
                {translate('common.edit')}
              </Button>
            </View>
          </Surface>
        </Modal>
      </Portal>
    );
  };
