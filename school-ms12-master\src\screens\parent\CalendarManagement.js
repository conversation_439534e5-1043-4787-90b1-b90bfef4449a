import React, { useState, useEffect } from 'react';
import { View, ScrollView, StyleSheet } from 'react-native';
import { Card, Title, Text, FAB, Portal, Modal, List } from 'react-native-paper';
import { Calendar } from 'react-native-calendars';
import { db } from '../../config/firebase';
import { collection, query, getDocs, addDoc, where, deleteDoc, doc, serverTimestamp } from 'firebase/firestore';
import { useAuth } from '../../context/AuthContext';
import { useLanguage } from '../../context/LanguageContext';
import CustomButton from '../../components/common/CustomButton';
import CustomInput from '../../components/common/CustomInput';
import EthiopianCalendar from '../../utils/EthiopianCalendar';
import EthiopianDatePicker from '../../components/common/EthiopianDatePicker';
import EthiopianTimePicker from '../../components/common/EthiopianTimePicker';

const CalendarManagement = () => {
  const { user } = useAuth();
  const { language } = useLanguage();
  const [events, setEvents] = useState([]);
  const [schoolEvents, setSchoolEvents] = useState([]);
  const [selectedDate, setSelectedDate] = useState('');
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [markedDates, setMarkedDates] = useState({});
  const [student, setStudent] = useState(null);

  const [formData, setFormData] = useState({
    title: '',
    description: '',
    startTime: '',
    endTime: '',
    location: '',
    type: 'personal', // personal, meeting, reminder
  });

  useEffect(() => {
    fetchStudentInfo();
    fetchEvents();
    fetchSchoolEvents();
  }, []);

  useEffect(() => {
    updateMarkedDates();
  }, [events, schoolEvents]);

  const fetchStudentInfo = async () => {
    try {
      const parentsRef = collection(db, 'parents');
      const parentDoc = await getDocs(query(parentsRef, where('userId', '==', user.uid)));

      if (!parentDoc.empty) {
        const parentData = parentDoc.docs[0].data();
        const studentsRef = collection(db, 'students');
        const studentDoc = await getDocs(query(studentsRef, where('id', '==', parentData.studentId)));

        if (!studentDoc.empty) {
          setStudent(studentDoc.docs[0].data());
        }
      }
    } catch (error) {
      console.error('Error fetching student info:', error);
    }
  };

  const fetchEvents = async () => {
    try {
      const eventsRef = collection(db, 'events');
      const q = query(eventsRef, where('userId', '==', user.uid));
      const querySnapshot = await getDocs(q);

      const eventsData = [];
      querySnapshot.forEach((doc) => {
        eventsData.push({ id: doc.id, ...doc.data() });
      });

      setEvents(eventsData);
    } catch (error) {
      console.error('Error fetching events:', error);
    }
  };

  const fetchSchoolEvents = async () => {
    try {
      const eventsRef = collection(db, 'schoolEvents');
      const q = query(
        eventsRef,
        where('status', '==', 'active'),
        where('targetAudience', 'array-contains', 'parents')
      );
      const querySnapshot = await getDocs(q);

      const eventsData = [];
      querySnapshot.forEach((doc) => {
        eventsData.push({ id: doc.id, ...doc.data() });
      });

      setSchoolEvents(eventsData);
    } catch (error) {
      console.error('Error fetching school events:', error);
    }
  };

  const updateMarkedDates = () => {
    const marked = {};

    // Mark personal events
    events.forEach(event => {
      marked[event.date] = {
        marked: true,
        dotColor: getEventColor(event.type),
      };
    });

    // Mark school events
    schoolEvents.forEach(event => {
      if (marked[event.date]) {
        marked[event.date].dots = [
          { color: marked[event.date].dotColor },
          { color: '#F44336' },
        ];
      } else {
        marked[event.date] = {
          marked: true,
          dotColor: '#F44336',
        };
      }
    });

    setMarkedDates(marked);
  };

  const getEventColor = (type) => {
    switch (type) {
      case 'meeting':
        return '#2196F3';
      case 'reminder':
        return '#FFC107';
      default:
        return '#4CAF50';
    }
  };

  const handleAddEvent = async () => {
    try {
      setLoading(true);

      const eventData = {
        ...formData,
        date: selectedDate,
        userId: user.uid,
        studentId: student.id,
        createdAt: serverTimestamp(),
      };

      const eventsRef = collection(db, 'events');
      await addDoc(eventsRef, eventData);

      setVisible(false);
      setFormData({
        title: '',
        description: '',
        startTime: '',
        endTime: '',
        location: '',
        type: 'personal',
      });
      fetchEvents();
    } catch (error) {
      console.error('Error adding event:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteEvent = async (eventId) => {
    try {
      await deleteDoc(doc(db, 'events', eventId));
      fetchEvents();
    } catch (error) {
      console.error('Error deleting event:', error);
    }
  };

  const getDayEvents = (date) => {
    const personalEvents = events.filter(event => event.date === date);
    const schoolEventsForDay = schoolEvents.filter(event => event.date === date);
    return [...personalEvents, ...schoolEventsForDay];
  };

  return (
    <View style={styles.container}>
      <Card style={styles.calendarCard}>
        <Card.Content>
          <Calendar
            onDayPress={(day) => setSelectedDate(day.dateString)}
            markedDates={{
              ...markedDates,
              [selectedDate]: {
                ...markedDates[selectedDate],
                selected: true,
              },
            }}
            theme={{
              selectedDayBackgroundColor: '#2196F3',
              todayTextColor: '#2196F3',
              arrowColor: '#2196F3',
            }}
            markingType="multi-dot"
          />
        </Card.Content>
      </Card>

      {selectedDate && (
        <Card style={styles.eventsCard}>
          <Card.Content>
            <Title>Events for {EthiopianCalendar.formatDate(new Date(selectedDate), language)}</Title>
            {getDayEvents(selectedDate).map((event) => (
              <List.Item
                key={event.id}
                title={event.title}
                description={`${event.startTime} - ${event.endTime}\n${event.description}`}
                left={props => (
                  <List.Icon
                    {...props}
                    icon={
                      event.type === 'meeting' ? 'account-group' :
                      event.type === 'reminder' ? 'bell' :
                      'calendar'
                    }
                    color={getEventColor(event.type)}
                  />
                )}
                right={props => (
                  event.userId === user.uid ? (
                    <CustomButton
                      mode="text"
                      onPress={() => handleDeleteEvent(event.id)}
                      {...props}
                    >
                      Delete
                    </CustomButton>
                  ) : null
                )}
              />
            ))}
          </Card.Content>
        </Card>
      )}

      <Portal>
        <Modal
          visible={visible}
          onDismiss={() => setVisible(false)}
          contentContainerStyle={styles.modalContent}
        >
          <ScrollView>
            <Title>Add New Event</Title>

            <CustomInput
              label="Title"
              value={formData.title}
              onChangeText={(text) => setFormData({ ...formData, title: text })}
            />

            <CustomInput
              label="Description"
              value={formData.description}
              onChangeText={(text) => setFormData({ ...formData, description: text })}
              multiline
              numberOfLines={3}
            />

            <View style={styles.datePickerContainer}>
              <Text style={styles.datePickerLabel}>Start Time</Text>
              <EthiopianTimePicker
                value={formData.startTime ? new Date(`2023-01-01T${formData.startTime}:00`) : new Date()}
                onChange={(time) => {
                  const hours = time.getHours().toString().padStart(2, '0');
                  const minutes = time.getMinutes().toString().padStart(2, '0');
                  setFormData({ ...formData, startTime: `${hours}:${minutes}` });
                }}
                label="Select Start Time"
                is24Hour={true}
              />
            </View>

            <View style={styles.datePickerContainer}>
              <Text style={styles.datePickerLabel}>End Time</Text>
              <EthiopianTimePicker
                value={formData.endTime ? new Date(`2023-01-01T${formData.endTime}:00`) : new Date()}
                onChange={(time) => {
                  const hours = time.getHours().toString().padStart(2, '0');
                  const minutes = time.getMinutes().toString().padStart(2, '0');
                  setFormData({ ...formData, endTime: `${hours}:${minutes}` });
                }}
                label="Select End Time"
                is24Hour={true}
              />
            </View>

            <CustomInput
              label="Location"
              value={formData.location}
              onChangeText={(text) => setFormData({ ...formData, location: text })}
            />

            <List.Section title="Event Type">
              <List.Item
                title="Personal"
                left={props => (
                  <List.Icon
                    {...props}
                    icon="calendar"
                    color={getEventColor('personal')}
                  />
                )}
                onPress={() => setFormData({ ...formData, type: 'personal' })}
              />
              <List.Item
                title="Meeting"
                left={props => (
                  <List.Icon
                    {...props}
                    icon="account-group"
                    color={getEventColor('meeting')}
                  />
                )}
                onPress={() => setFormData({ ...formData, type: 'meeting' })}
              />
              <List.Item
                title="Reminder"
                left={props => (
                  <List.Icon
                    {...props}
                    icon="bell"
                    color={getEventColor('reminder')}
                  />
                )}
                onPress={() => setFormData({ ...formData, type: 'reminder' })}
              />
            </List.Section>

            <View style={styles.modalButtons}>
              <CustomButton
                mode="contained"
                onPress={handleAddEvent}
                loading={loading}
              >
                Add Event
              </CustomButton>

              <CustomButton
                mode="outlined"
                onPress={() => setVisible(false)}
                style={styles.cancelButton}
              >
                Cancel
              </CustomButton>
            </View>
          </ScrollView>
        </Modal>
      </Portal>

      <FAB
        style={styles.fab}
        icon="plus"
        onPress={() => {
          if (selectedDate) {
            setVisible(true);
          }
        }}
        disabled={!selectedDate}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  calendarCard: {
    margin: 10,
  },
  eventsCard: {
    margin: 10,
  },
  modalContent: {
    backgroundColor: 'white',
    padding: 20,
    margin: 20,
    borderRadius: 10,
    maxHeight: '80%',
  },
  modalButtons: {
    marginTop: 20,
  },
  cancelButton: {
    marginTop: 10,
  },
  datePickerContainer: {
    marginBottom: 16,
  },
  datePickerLabel: {
    fontSize: 14,
    marginBottom: 8,
    color: '#666',
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
    backgroundColor: '#2196F3',
  },
});

export default CalendarManagement;
