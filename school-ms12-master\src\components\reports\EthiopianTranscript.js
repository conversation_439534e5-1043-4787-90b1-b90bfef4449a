import React from 'react';
import {
    Paper,
    Typography,
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableRow,
    Box,
    Grid,
    Divider
} from '@mui/material';
import { useTranslation } from '../../hooks/useTranslation';
import { styled } from '@mui/material/styles';

const StyledPaper = styled(Paper)(({ theme }) => ({
    padding: theme.spacing(4),
    margin: theme.spacing(2),
    position: 'relative',
    '& .watermark': {
        position: 'absolute',
        top: '50%',
        left: '50%',
        transform: 'translate(-50%, -50%) rotate(-45deg)',
        fontSize: '4rem',
        color: 'rgba(0,0,0,0.1)',
        pointerEvents: 'none',
        zIndex: 0
    }
}));

const EthiopianTranscript = ({
    studentInfo,
    academicRecords,
    schoolInfo,
    certificateNumber
}) => {
    const {
        t,
        language,
        formatDate,
        formatNumber,
        getGradeName,
        getSubjectName
    } = useTranslation();

    const calculateGPA = (grades) => {
        const points = {
            'A+': 4.0, 'A': 4.0, 'A-': 3.7,
            'B+': 3.3, 'B': 3.0, 'B-': 2.7,
            'C+': 2.3, 'C': 2.0, 'C-': 1.7,
            'D+': 1.3, 'D': 1.0, 'F': 0.0
        };

        const totalPoints = grades.reduce((sum, grade) => sum + points[grade.grade], 0);
        return (totalPoints / grades.length).toFixed(2);
    };

    const renderHeader = () => (
        <Box textAlign="center" mb={4}>
            <Typography variant="h4" gutterBottom>
                {schoolInfo.name[language]}
            </Typography>
            <Typography variant="h5" gutterBottom>
                {t('academic_transcript')}
            </Typography>
            <Typography variant="body2" color="textSecondary">
                {t('certificate_number')}: {certificateNumber}
            </Typography>
        </Box>
    );

    const renderStudentInfo = () => (
        <Grid container spacing={2} mb={4}>
            <Grid item xs={12} md={6}>
                <Typography variant="subtitle1" gutterBottom>
                    {t('student_information')}:
                </Typography>
                <Table size="small">
                    <TableBody>
                        <TableRow>
                            <TableCell>{t('full_name')}:</TableCell>
                            <TableCell>{studentInfo.name[language]}</TableCell>
                        </TableRow>
                        <TableRow>
                            <TableCell>{t('id_number')}:</TableCell>
                            <TableCell>{studentInfo.id}</TableCell>
                        </TableRow>
                        <TableRow>
                            <TableCell>{t('date_of_birth')}:</TableCell>
                            <TableCell>{formatDate(studentInfo.dateOfBirth)}</TableCell>
                        </TableRow>
                    </TableBody>
                </Table>
            </Grid>
            <Grid item xs={12} md={6}>
                <Typography variant="subtitle1" gutterBottom>
                    {t('program_information')}:
                </Typography>
                <Table size="small">
                    <TableBody>
                        <TableRow>
                            <TableCell>{t('admission_date')}:</TableCell>
                            <TableCell>{formatDate(studentInfo.admissionDate)}</TableCell>
                        </TableRow>
                        <TableRow>
                            <TableCell>{t('graduation_date')}:</TableCell>
                            <TableCell>{formatDate(studentInfo.graduationDate)}</TableCell>
                        </TableRow>
                        <TableRow>
                            <TableCell>{t('program')}:</TableCell>
                            <TableCell>{t('secondary_education')}</TableCell>
                        </TableRow>
                    </TableBody>
                </Table>
            </Grid>
        </Grid>
    );

    const renderAcademicRecord = () => (
        <Box mb={4}>
            {academicRecords.map((yearRecord, yearIndex) => (
                <Box key={yearIndex} mb={3}>
                    <Typography variant="h6" gutterBottom>
                        {getGradeName(yearRecord.grade)} - {yearRecord.academicYear}
                    </Typography>
                    <Table>
                        <TableHead>
                            <TableRow>
                                <TableCell>{t('subject')}</TableCell>
                                <TableCell align="center">{t('first_semester')}</TableCell>
                                <TableCell align="center">{t('second_semester')}</TableCell>
                                <TableCell align="center">{t('final_grade')}</TableCell>
                            </TableRow>
                        </TableHead>
                        <TableBody>
                            {yearRecord.subjects.map((subject, subjectIndex) => (
                                <TableRow key={subjectIndex}>
                                    <TableCell>{getSubjectName(subject.name)}</TableCell>
                                    <TableCell align="center">
                                        {formatNumber(subject.firstSemester)}
                                    </TableCell>
                                    <TableCell align="center">
                                        {formatNumber(subject.secondSemester)}
                                    </TableCell>
                                    <TableCell align="center">
                                        {subject.finalGrade}
                                    </TableCell>
                                </TableRow>
                            ))}
                            <TableRow>
                                <TableCell colSpan={3} align="right">
                                    <strong>{t('yearly_gpa')}:</strong>
                                </TableCell>
                                <TableCell align="center">
                                    <strong>{calculateGPA(yearRecord.subjects)}</strong>
                                </TableCell>
                            </TableRow>
                        </TableBody>
                    </Table>
                </Box>
            ))}
        </Box>
    );

    const renderSummary = () => {
        const cumulativeGPA = calculateGPA(
            academicRecords.flatMap(year => year.subjects)
        );

        return (
            <Box mb={4}>
                <Divider sx={{ my: 2 }} />
                <Typography variant="h6" gutterBottom>
                    {t('academic_summary')}
                </Typography>
                <Table size="small">
                    <TableBody>
                        <TableRow>
                            <TableCell>{t('cumulative_gpa')}:</TableCell>
                            <TableCell>{cumulativeGPA}</TableCell>
                        </TableRow>
                        <TableRow>
                            <TableCell>{t('rank_in_class')}:</TableCell>
                            <TableCell>
                                {formatNumber(studentInfo.rank)} / {formatNumber(studentInfo.totalStudents)}
                            </TableCell>
                        </TableRow>
                        <TableRow>
                            <TableCell>{t('conduct')}:</TableCell>
                            <TableCell>{t(studentInfo.conduct)}</TableCell>
                        </TableRow>
                    </TableBody>
                </Table>
            </Box>
        );
    };

    const renderAuthentication = () => (
        <Grid container spacing={4} mt={4}>
            <Grid item xs={12} md={4}>
                <Typography variant="body2" gutterBottom>
                    {t('registrar')}
                </Typography>
                <Box mt={4} borderTop={1} width={200} />
                <Typography variant="body2" mt={1}>
                    {t('name_and_signature')}
                </Typography>
            </Grid>
            <Grid item xs={12} md={4}>
                <Typography variant="body2" gutterBottom>
                    {t('academic_director')}
                </Typography>
                <Box mt={4} borderTop={1} width={200} />
                <Typography variant="body2" mt={1}>
                    {t('name_and_signature')}
                </Typography>
            </Grid>
            <Grid item xs={12} md={4}>
                <Typography variant="body2" gutterBottom>
                    {t('school_seal')}
                </Typography>
                <Box 
                    mt={2} 
                    border={1} 
                    borderRadius="50%" 
                    width={100} 
                    height={100} 
                    display="flex" 
                    alignItems="center" 
                    justifyContent="center"
                >
                    <Typography variant="body2" color="textSecondary">
                        {t('seal')}
                    </Typography>
                </Box>
            </Grid>
        </Grid>
    );

    return (
        <StyledPaper>
            <div className="watermark">{schoolInfo.name[language]}</div>
            {renderHeader()}
            {renderStudentInfo()}
            {renderAcademicRecord()}
            {renderSummary()}
            {renderAuthentication()}
        </StyledPaper>
    );
};

export default EthiopianTranscript;
