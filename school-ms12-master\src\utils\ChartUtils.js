/**
 * Utility functions for working with charts in the app
 */

/**
 * Sanitizes chart data to prevent SVG path errors
 * Replaces Infinity, -Infinity, NaN, null, and undefined values with 0
 *
 * @param {Array} data - The data array to sanitize
 * @returns {Array} - Sanitized data array
 */
export const sanitizeChartData = (data) => {
  if (!data) return [0];

  // Handle PieChart data which is an array of objects
  if (data.length > 0 && typeof data[0] === 'object') {
    return data.map(item => {
      // For PieChart data with population property
      if ('population' in item) {
        return {
          ...item,
          population: sanitizeValue(item.population)
        };
      }
      // For PieChart data with value property
      else if ('value' in item) {
        return {
          ...item,
          value: sanitizeValue(item.value)
        };
      }
      return item;
    });
  }

  // For regular chart data (arrays of numbers)
  return data.map(value => sanitizeValue(value));
};

/**
 * Sanitizes a single value to prevent SVG path errors
 *
 * @param {any} value - The value to sanitize
 * @returns {number} - Sanitized value
 */
const sanitizeValue = (value) => {
  // Check for invalid values
  if (
    value === undefined ||
    value === null ||
    value === Infinity ||
    value === -Infinity ||
    isNaN(value)
  ) {
    return 0;
  }
  return value;
};

/**
 * Sanitizes chart dataset to prevent SVG path errors
 *
 * @param {Object} chartData - The chart data object with datasets
 * @returns {Object} - Sanitized chart data object
 */
export const sanitizeChartDatasets = (chartData) => {
  if (!chartData || !chartData.datasets) {
    return {
      labels: [],
      datasets: [{
        data: [0]
      }]
    };
  }

  const sanitizedDatasets = chartData.datasets.map(dataset => ({
    ...dataset,
    data: sanitizeChartData(dataset.data)
  }));

  return {
    ...chartData,
    datasets: sanitizedDatasets
  };
};
