import React, { useState, useEffect } from 'react';
import { View, ScrollView, StyleSheet, Dimensions } from 'react-native';
import { Card, Title, Paragraph, IconButton, Portal, Dialog, Button, Avatar, Badge, Divider, List, Chip, ActivityIndicator, DataTable } from 'react-native-paper';
import { useNavigation } from '@react-navigation/native';
import { signOut } from 'firebase/auth';
import { auth, db } from '../../config/firebase';
import { collection, query, getDocs, where } from 'firebase/firestore';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>hart, PieChart } from 'react-native-chart-kit';
import { sanitizeChartData, sanitizeChartDatasets } from '../../utils/ChartUtils';
import ActivityService from '../../services/ActivityService';
import { useLanguage } from '../../context/LanguageContext';
import { translate, translateActivity } from '../../utils/TranslationUtils';

const screenWidth = Dimensions.get('window').width;

const chartConfig = {
  backgroundColor: '#ffffff',
  backgroundGradientFrom: '#ffffff',
  backgroundGradientTo: '#ffffff',
  decimalPlaces: 0,
  color: (opacity = 1) => `rgba(33, 150, 243, ${opacity})`,
  labelColor: (opacity = 1) => `rgba(0, 0, 0, ${opacity})`,
  style: {
    borderRadius: 16
  }
};

// Sample data
const enrollmentData = {
  labels: ['Grade 9', 'Grade 10', 'Grade 11', 'Grade 12'],
  datasets: [{
    data: sanitizeChartData([120, 115, 105, 95]),
    color: (opacity = 1) => `rgba(33, 150, 243, ${opacity})`,
    strokeWidth: 2
  }],
  legend: ['Students per Grade']
};

const performanceTrendData = {
  labels: ['Q1', 'Q2', 'Q3', 'Q4'],
  datasets: [
    {
      data: sanitizeChartData([78, 82, 85, 88]),
      color: (opacity = 1) => `rgba(76, 175, 80, ${opacity})`,
      strokeWidth: 2
    }
  ],
  legend: ['Average Performance']
};

const resourceAllocationData = [
  {
    name: 'Academic',
    value: 45,
    color: '#2196f3',
    legendFontColor: '#7F7F7F',
    legendFontSize: 12
  },
  {
    name: 'Administrative',
    value: 25,
    color: '#4caf50',
    legendFontColor: '#7F7F7F',
    legendFontSize: 12
  },
  {
    name: 'Infrastructure',
    value: 20,
    color: '#ff9800',
    legendFontColor: '#7F7F7F',
    legendFontSize: 12
  },
  {
    name: 'Other',
    value: 10,
    color: '#f44336',
    legendFontColor: '#7F7F7F',
    legendFontSize: 12
  }
];

const AdminDashboard = () => {
  const navigation = useNavigation();
  const { translate: t, language } = useLanguage();
  const [logoutDialogVisible, setLogoutDialogVisible] = useState(false);
  const [currentTab, setCurrentTab] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [stats, setStats] = useState({
    totalStudents: 0,
    totalTeachers: 0,
    totalClasses: 0,
    activeUsers: 0
  });
  const [recentActivities, setRecentActivities] = useState([]);
  const [activitiesLoading, setActivitiesLoading] = useState(false);

  useEffect(() => {
    fetchStats();
    fetchRecentActivities();
  }, [language]); // Refetch when language changes

  const fetchStats = async () => {
    try {
      setIsLoading(true);
      const studentsQuery = query(collection(db, 'users'), where('role', '==', 'student'));
      const teachersQuery = query(collection(db, 'users'), where('role', '==', 'teacher'));
      const classesQuery = query(collection(db, 'classes'));

      const [studentsSnap, teachersSnap, classesSnap] = await Promise.all([
        getDocs(studentsQuery),
        getDocs(teachersQuery),
        getDocs(classesQuery)
      ]);

      setStats({
        totalStudents: studentsSnap.size,
        totalTeachers: teachersSnap.size,
        totalClasses: classesSnap.size,
        activeUsers: studentsSnap.size + teachersSnap.size
      });
    } catch (error) {
      console.error('Error fetching stats:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchRecentActivities = async () => {
    try {
      setActivitiesLoading(true);
      // Use ActivityService to fetch real activities
      const activitiesData = await ActivityService.getRecentActivities(5);
      
      // Process and translate activities
      setRecentActivities(activitiesData);
    } catch (error) {
      console.error('Error fetching recent activities:', error);
      // If there's an error, set empty activities
      setRecentActivities([]);
    } finally {
      setActivitiesLoading(false);
    }
  };

  const handleLogout = async () => {
    try {
      await signOut(auth);
      navigation.reset({
        index: 0,
        routes: [{ name: 'Login' }],
      });
    } catch (error) {
      console.error('Error logging out:', error);
    }
  };

  const getActivityIcon = (type) => {
    switch (type) {
      case 'login':
      case 'logout':
        return 'account-key';
      case 'user':
        return 'account';
      case 'class':
        return 'school';
      case 'exam':
        return 'file-document-edit';
      case 'academic':
        return 'calendar';
      case 'attendance':
        return 'calendar-check';
      case 'grade':
        return 'chart-box';
      case 'announcement':
        return 'bullhorn';
      case 'system':
        return 'cog';
      case 'profile':
        return 'account-edit';
      case 'document':
        return 'file-document';
      default:
        return 'information';
    }
  };

  const getActivityColor = (type) => {
    switch (type) {
      case 'login':
      case 'logout':
        return '#42a5f5'; // light blue
      case 'user':
        return '#2196F3'; // blue
      case 'class':
        return '#4CAF50'; // green
      case 'exam':
        return '#FF9800'; // orange
      case 'academic':
        return '#9C27B0'; // purple
      case 'attendance':
        return '#00BCD4'; // cyan
      case 'grade':
        return '#F44336'; // red
      case 'announcement':
        return '#E91E63'; // pink
      case 'system':
        return '#607D8B'; // blue grey
      case 'profile':
        return '#3F51B5'; // indigo
      case 'document':
        return '#795548'; // brown
      default:
        return '#757575'; // grey
    }
  };

  const renderQuickStats = () => (
    <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.quickStats}>
      {[
        { title: t('admin.dashboard.totalStudents', 'Total Students'), value: stats.totalStudents, icon: 'account-group', color: '#2196f3' },
        { title: t('admin.dashboard.totalTeachers', 'Total Teachers'), value: stats.totalTeachers, icon: 'teach', color: '#4caf50' },
        { title: t('admin.dashboard.totalClasses', 'Total Classes'), value: stats.totalClasses, icon: 'school', color: '#ff9800' },
        { title: t('admin.dashboard.activeUsers', 'Active Users'), value: stats.activeUsers, icon: 'account-check', color: '#f44336' }
      ].map((stat, index) => (
        <Card key={index} style={[styles.statCard, { marginRight: 16 }]}>
          <Card.Content style={styles.statContent}>
            <Avatar.Icon size={48} icon={stat.icon} style={{ backgroundColor: stat.color }} />
            <Title style={styles.statValue}>{stat.value}</Title>
            <Paragraph style={styles.statTitle}>{stat.title}</Paragraph>
          </Card.Content>
        </Card>
      ))}
    </ScrollView>
  );

  const renderEnrollmentSection = () => (
    <Card style={styles.card}>
      <Card.Content>
        <Title>{t('admin.dashboard.enrollment', 'Student Enrollment')}</Title>
        <BarChart
          data={sanitizeChartDatasets(enrollmentData)}
          width={screenWidth - 64}
          height={220}
          chartConfig={chartConfig}
          style={styles.chart}
          showValuesOnTopOfBars
          fromZero
        />
      </Card.Content>
    </Card>
  );

  const renderPerformanceSection = () => (
    <Card style={styles.card}>
      <Card.Content>
        <Title>{t('admin.dashboard.performance', 'Performance Trend')}</Title>
        <LineChart
          data={sanitizeChartDatasets(performanceTrendData)}
          width={screenWidth - 64}
          height={220}
          chartConfig={chartConfig}
          style={styles.chart}
          bezier
        />
      </Card.Content>
    </Card>
  );

  const renderResourceSection = () => (
    <Card style={styles.card}>
      <Card.Content>
        <Title>{t('admin.dashboard.resources', 'Resource Allocation')}</Title>
        <PieChart
          data={resourceAllocationData.map(item => ({
            ...item,
            population: isNaN(item.population) || item.population === undefined || 
                      item.population === null || item.population === Infinity || 
                      item.population === -Infinity ? 0 : item.population
          }))}
          width={screenWidth - 64}
          height={220}
          chartConfig={chartConfig}
          accessor="value"
          backgroundColor="transparent"
          paddingLeft="15"
          style={styles.chart}
        />
      </Card.Content>
    </Card>
  );

  const renderActivities = () => (
    <Card style={styles.card}>
      <Card.Content>
        <Title>{t('activities.recentActivities', 'Recent Activities')}</Title>
        <List.Section>
          {activitiesLoading ? (
            <ActivityIndicator size="small" style={styles.activityLoader} />
          ) : recentActivities.length > 0 ? (
            recentActivities.map((activity, index) => {
              // Translate the activity description using the translation utility
              const translatedActivity = translateActivity(activity, t);
              
              return (
                <React.Fragment key={activity.id || index}>
                  <List.Item
                    title={translatedActivity.title || activity.title}
                    description={translatedActivity.description || activity.description}
                    left={props => (
                      <Avatar.Icon
                        {...props}
                        icon={getActivityIcon(activity.type)}
                        style={{ backgroundColor: getActivityColor(activity.type) }}
                        size={40}
                      />
                    )}
                    right={props => !activity.read && (
                      <Badge {...props} style={{ backgroundColor: '#2196F3', alignSelf: 'center' }} size={8} />
                    )}
                    onPress={() => navigation.navigate('ActivityDetailScreen', { activityId: activity.id })}
                  />
                  {index < recentActivities.length - 1 && <Divider />}
                </React.Fragment>
              );
            })
          ) : (
            <Paragraph style={styles.noActivitiesText}>
              {t('activities.noActivities', 'No recent activities')}
            </Paragraph>
          )}
        </List.Section>
        <Button 
          mode="text" 
          onPress={() => navigation.navigate('ActivityManagement')}
          style={styles.viewAllButton}
        >
          {t('activities.viewAll', 'View All Activities')}
        </Button>
      </Card.Content>
    </Card>
  );

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Title style={styles.headerTitle}>{t('admin.dashboard.title', 'Admin Dashboard')}</Title>
        <View style={styles.headerActions}>
          <IconButton
            icon="bell"
            size={24}
            onPress={() => navigation.navigate('Notifications')}
          />
          <Badge visible={true} size={8} style={styles.badge}>3</Badge>
          <IconButton
            icon="account"
            size={24}
            onPress={() => navigation.navigate('Profile')}
          />
          <IconButton
            icon="logout"
            size={24}
            onPress={() => setLogoutDialogVisible(true)}
          />
        </View>
      </View>

      <ScrollView style={styles.content}>
        {renderQuickStats()}
        
        <View style={styles.tabButtons}>
          <Button
            mode={currentTab === 0 ? 'contained' : 'outlined'}
            onPress={() => setCurrentTab(0)}
            style={styles.tabButton}
          >
            {t('admin.dashboard.enrollment', 'Enrollment')}
          </Button>
          <Button
            mode={currentTab === 1 ? 'contained' : 'outlined'}
            onPress={() => setCurrentTab(1)}
            style={styles.tabButton}
          >
            {t('admin.dashboard.performance', 'Performance')}
          </Button>
          <Button
            mode={currentTab === 2 ? 'contained' : 'outlined'}
            onPress={() => setCurrentTab(2)}
            style={styles.tabButton}
          >
            {t('admin.dashboard.resources', 'Resources')}
          </Button>
        </View>

        {isLoading ? (
          <ActivityIndicator style={styles.loader} size="large" />
        ) : (
          <>
            {currentTab === 0 && renderEnrollmentSection()}
            {currentTab === 1 && renderPerformanceSection()}
            {currentTab === 2 && renderResourceSection()}
          </>
        )}

        {renderActivities()}
      </ScrollView>

      <Portal>
        <Dialog
          visible={logoutDialogVisible}
          onDismiss={() => setLogoutDialogVisible(false)}
        >
          <Dialog.Title>{t('common.logout', 'Logout')}</Dialog.Title>
          <Dialog.Content>
            <Paragraph>{t('common.logoutConfirmMessage', 'Are you sure you want to logout?')}</Paragraph>
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={() => setLogoutDialogVisible(false)}>{t('common.cancel', 'Cancel')}</Button>
            <Button onPress={handleLogout}>{t('common.logout', 'Logout')}</Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5'
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#fff',
    elevation: 4
  },
  headerTitle: {
    fontSize: 24
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  badge: {
    position: 'absolute',
    top: 8,
    right: 88
  },
  content: {
    flex: 1,
    padding: 16
  },
  quickStats: {
    marginBottom: 16
  },
  statCard: {
    width: 160
  },
  statContent: {
    alignItems: 'center'
  },
  statValue: {
    fontSize: 24,
    marginTop: 8
  },
  statTitle: {
    fontSize: 14,
    textAlign: 'center'
  },
  tabButtons: {
    flexDirection: 'row',
    marginBottom: 16
  },
  tabButton: {
    flex: 1,
    marginHorizontal: 4
  },
  card: {
    marginBottom: 16,
    elevation: 4
  },
  chart: {
    marginVertical: 8,
    borderRadius: 16
  },
  loader: {
    marginVertical: 32
  },
  activityLoader: {
    marginVertical: 16
  },
  noActivitiesText: {
    textAlign: 'center',
    marginVertical: 16
  },
  viewAllButton: {
    marginTop: 16,
    marginHorizontal: 16
  }
});

export default AdminDashboard;
