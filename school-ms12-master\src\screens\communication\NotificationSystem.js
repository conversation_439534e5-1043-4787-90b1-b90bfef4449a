import React, { useState, useEffect } from 'react';
import { View, ScrollView, StyleSheet } from 'react-native';
import { Card, Title, FAB, Portal, Modal, List, Chip, Searchbar, IconButton, Text, Switch } from 'react-native-paper';
import { db } from '../../config/firebase';
import { collection, addDoc, query, getDocs, doc, updateDoc, deleteDoc, where, orderBy } from 'firebase/firestore';
import CustomButton from '../../components/common/CustomButton';
import CustomInput from '../../components/common/CustomInput';

const NotificationSystem = () => {
  const [notifications, setNotifications] = useState([]);
  const [templates, setTemplates] = useState([]);
  const [users, setUsers] = useState([]);
  const [visible, setVisible] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  
  const [formData, setFormData] = useState({
    title: '',
    message: '',
    type: 'announcement', // announcement, alert, reminder
    priority: 'normal', // low, normal, high, urgent
    targetAudience: [], // all, teachers, students, parents, specific users
    schedule: {
      immediate: true,
      scheduledDate: new Date().toISOString(),
      repeat: 'none', // none, daily, weekly, monthly
      endDate: '',
    },
    channels: {
      inApp: true,
      email: false,
      sms: false,
      push: true,
    },
    template: '',
    variables: {},
  });

  useEffect(() => {
    fetchNotifications();
    fetchTemplates();
    fetchUsers();
  }, []);

  const fetchNotifications = async () => {
    try {
      const notificationsRef = collection(db, 'notifications');
      const q = query(notificationsRef, orderBy('createdAt', 'desc'));
      const querySnapshot = await getDocs(q);
      
      const notificationsData = [];
      querySnapshot.forEach((doc) => {
        notificationsData.push({ id: doc.id, ...doc.data() });
      });
      
      setNotifications(notificationsData);
    } catch (error) {
      console.error('Error fetching notifications:', error);
    }
  };

  const fetchTemplates = async () => {
    try {
      const templatesRef = collection(db, 'notificationTemplates');
      const querySnapshot = await getDocs(templatesRef);
      
      const templatesData = [];
      querySnapshot.forEach((doc) => {
        templatesData.push({ id: doc.id, ...doc.data() });
      });
      
      setTemplates(templatesData);
    } catch (error) {
      console.error('Error fetching templates:', error);
    }
  };

  const fetchUsers = async () => {
    try {
      const usersRef = collection(db, 'users');
      const querySnapshot = await getDocs(usersRef);
      
      const usersData = [];
      querySnapshot.forEach((doc) => {
        usersData.push({ id: doc.id, ...doc.data() });
      });
      
      setUsers(usersData);
    } catch (error) {
      console.error('Error fetching users:', error);
    }
  };

  const handleCreateNotification = async () => {
    try {
      setLoading(true);
      const notificationsRef = collection(db, 'notifications');
      
      // Create notification
      const notification = {
        ...formData,
        status: formData.schedule.immediate ? 'sent' : 'scheduled',
        createdAt: new Date().toISOString(),
        createdBy: 'admin', // TODO: Get from auth context
      };

      await addDoc(notificationsRef, notification);

      // If immediate, send notifications through selected channels
      if (formData.schedule.immediate) {
        if (formData.channels.email) {
          // TODO: Implement email notification
          console.log('Sending email notifications');
        }
        if (formData.channels.sms) {
          // TODO: Implement SMS notification
          console.log('Sending SMS notifications');
        }
        if (formData.channels.push) {
          // TODO: Implement push notification
          console.log('Sending push notifications');
        }
      }
      
      setVisible(false);
      resetForm();
      fetchNotifications();
    } catch (error) {
      console.error('Error creating notification:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteNotification = async (notificationId) => {
    try {
      await deleteDoc(doc(db, 'notifications', notificationId));
      fetchNotifications();
    } catch (error) {
      console.error('Error deleting notification:', error);
    }
  };

  const resetForm = () => {
    setFormData({
      title: '',
      message: '',
      type: 'announcement',
      priority: 'normal',
      targetAudience: [],
      schedule: {
        immediate: true,
        scheduledDate: new Date().toISOString(),
        repeat: 'none',
        endDate: '',
      },
      channels: {
        inApp: true,
        email: false,
        sms: false,
        push: true,
      },
      template: '',
      variables: {},
    });
    setSelectedTemplate(null);
  };

  const handleTemplateSelect = (templateId) => {
    const template = templates.find(t => t.id === templateId);
    if (template) {
      setSelectedTemplate(template);
      setFormData({
        ...formData,
        title: template.title,
        message: template.message,
        type: template.type,
        priority: template.priority,
        template: templateId,
      });
    }
  };

  const toggleAudience = (audience) => {
    const audiences = formData.targetAudience.includes(audience)
      ? formData.targetAudience.filter(a => a !== audience)
      : [...formData.targetAudience, audience];
    setFormData({ ...formData, targetAudience: audiences });
  };

  const toggleChannel = (channel) => {
    setFormData({
      ...formData,
      channels: {
        ...formData.channels,
        [channel]: !formData.channels[channel],
      },
    });
  };

  const filteredNotifications = notifications.filter(notification =>
    notification.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    notification.message.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'urgent': return '#FF0000';
      case 'high': return '#FF6B6B';
      case 'normal': return '#4CAF50';
      case 'low': return '#2196F3';
      default: return '#000000';
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString();
  };

  return (
    <View style={styles.container}>
      <Searchbar
        placeholder="Search notifications..."
        onChangeText={setSearchQuery}
        value={searchQuery}
        style={styles.searchBar}
      />

      <ScrollView>
        {filteredNotifications.map((notification) => (
          <Card key={notification.id} style={styles.notificationCard}>
            <Card.Content>
              <View style={styles.headerContainer}>
                <Title>{notification.title}</Title>
                <Chip
                  mode="outlined"
                  textStyle={{ color: getPriorityColor(notification.priority) }}
                >
                  {notification.priority}
                </Chip>
              </View>
              
              <Text style={styles.message}>{notification.message}</Text>
              
              <View style={styles.detailsContainer}>
                <Chip icon="clock" style={styles.chip}>
                  {notification.schedule.immediate ? 'Immediate' : formatDate(notification.schedule.scheduledDate)}
                </Chip>
                <Chip icon="account-group" style={styles.chip}>
                  {notification.targetAudience.join(', ')}
                </Chip>
                <Chip icon="bell" style={styles.chip}>
                  {notification.type}
                </Chip>
              </View>
              
              <View style={styles.channelsContainer}>
                {Object.entries(notification.channels).map(([channel, enabled]) => (
                  enabled && (
                    <Chip
                      key={channel}
                      icon={
                        channel === 'inApp' ? 'cellphone' :
                        channel === 'email' ? 'email' :
                        channel === 'sms' ? 'message' :
                        'bell'
                      }
                      style={styles.chip}
                    >
                      {channel}
                    </Chip>
                  )
                ))}
              </View>
              
              <View style={styles.footerContainer}>
                <Text style={styles.timestamp}>
                  Created: {formatDate(notification.createdAt)}
                </Text>
                <IconButton
                  icon="delete"
                  onPress={() => handleDeleteNotification(notification.id)}
                />
              </View>
            </Card.Content>
          </Card>
        ))}
      </ScrollView>

      <Portal>
        <Modal
          visible={visible}
          onDismiss={() => {
            setVisible(false);
            resetForm();
          }}
          contentContainerStyle={styles.modalContent}
        >
          <ScrollView>
            <Title>Create Notification</Title>

            <List.Section title="Template">
              <ScrollView horizontal style={styles.templateScroll}>
                {templates.map((template) => (
                  <Card
                    key={template.id}
                    style={[
                      styles.templateCard,
                      selectedTemplate?.id === template.id && styles.selectedTemplate,
                    ]}
                    onPress={() => handleTemplateSelect(template.id)}
                  >
                    <Card.Content>
                      <Title>{template.title}</Title>
                      <Text numberOfLines={2}>{template.message}</Text>
                    </Card.Content>
                  </Card>
                ))}
              </ScrollView>
            </List.Section>

            <CustomInput
              label="Title"
              value={formData.title}
              onChangeText={(text) => setFormData({ ...formData, title: text })}
            />

            <CustomInput
              label="Message"
              value={formData.message}
              onChangeText={(text) => setFormData({ ...formData, message: text })}
              multiline
              numberOfLines={4}
            />

            <List.Section title="Type">
              {['announcement', 'alert', 'reminder'].map((type) => (
                <List.Item
                  key={type}
                  title={type.charAt(0).toUpperCase() + type.slice(1)}
                  onPress={() => setFormData({ ...formData, type })}
                  style={formData.type === type ? styles.selectedItem : null}
                  left={props => <List.Icon {...props} icon="bell" />}
                />
              ))}
            </List.Section>

            <List.Section title="Priority">
              {['low', 'normal', 'high', 'urgent'].map((priority) => (
                <List.Item
                  key={priority}
                  title={priority.charAt(0).toUpperCase() + priority.slice(1)}
                  onPress={() => setFormData({ ...formData, priority })}
                  style={formData.priority === priority ? styles.selectedItem : null}
                  left={props => (
                    <List.Icon
                      {...props}
                      icon="flag"
                      color={getPriorityColor(priority)}
                    />
                  )}
                />
              ))}
            </List.Section>

            <Title style={styles.sectionTitle}>Target Audience</Title>
            <View style={styles.chipContainer}>
              {['all', 'teachers', 'students', 'parents'].map((audience) => (
                <Chip
                  key={audience}
                  selected={formData.targetAudience.includes(audience)}
                  onPress={() => toggleAudience(audience)}
                  style={styles.chip}
                >
                  {audience.charAt(0).toUpperCase() + audience.slice(1)}
                </Chip>
              ))}
            </View>

            <Title style={styles.sectionTitle}>Schedule</Title>
            <List.Item
              title="Send Immediately"
              onPress={() => setFormData({
                ...formData,
                schedule: { ...formData.schedule, immediate: !formData.schedule.immediate },
              })}
              left={props => (
                <List.Icon
                  {...props}
                  icon={formData.schedule.immediate ? 'checkbox-marked' : 'checkbox-blank-outline'}
                />
              )}
            />

            {!formData.schedule.immediate && (
              <>
                <CustomInput
                  label="Scheduled Date"
                  value={formData.schedule.scheduledDate}
                  onChangeText={(text) => setFormData({
                    ...formData,
                    schedule: { ...formData.schedule, scheduledDate: text },
                  })}
                />

                <List.Section title="Repeat">
                  {['none', 'daily', 'weekly', 'monthly'].map((repeat) => (
                    <List.Item
                      key={repeat}
                      title={repeat.charAt(0).toUpperCase() + repeat.slice(1)}
                      onPress={() => setFormData({
                        ...formData,
                        schedule: { ...formData.schedule, repeat },
                      })}
                      style={formData.schedule.repeat === repeat ? styles.selectedItem : null}
                      left={props => <List.Icon {...props} icon="repeat" />}
                    />
                  ))}
                </List.Section>

                {formData.schedule.repeat !== 'none' && (
                  <CustomInput
                    label="End Date"
                    value={formData.schedule.endDate}
                    onChangeText={(text) => setFormData({
                      ...formData,
                      schedule: { ...formData.schedule, endDate: text },
                    })}
                  />
                )}
              </>
            )}

            <Title style={styles.sectionTitle}>Notification Channels</Title>
            {Object.entries(formData.channels).map(([channel, enabled]) => (
              <List.Item
                key={channel}
                title={channel.charAt(0).toUpperCase() + channel.slice(1)}
                onPress={() => toggleChannel(channel)}
                right={() => (
                  <Switch
                    value={enabled}
                    onValueChange={() => toggleChannel(channel)}
                  />
                )}
                left={props => (
                  <List.Icon
                    {...props}
                    icon={
                      channel === 'inApp' ? 'cellphone' :
                      channel === 'email' ? 'email' :
                      channel === 'sms' ? 'message' :
                      'bell'
                    }
                  />
                )}
              />
            ))}

            <View style={styles.modalButtons}>
              <CustomButton
                mode="contained"
                onPress={handleCreateNotification}
                loading={loading}
              >
                {formData.schedule.immediate ? 'Send Now' : 'Schedule'}
              </CustomButton>
              
              <CustomButton
                mode="outlined"
                onPress={() => {
                  setVisible(false);
                  resetForm();
                }}
              >
                Cancel
              </CustomButton>
            </View>
          </ScrollView>
        </Modal>
      </Portal>

      <FAB
        style={styles.fab}
        icon="plus"
        onPress={() => setVisible(true)}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  searchBar: {
    margin: 10,
    elevation: 2,
  },
  notificationCard: {
    margin: 10,
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  message: {
    marginVertical: 10,
    fontSize: 16,
  },
  detailsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 10,
  },
  channelsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 10,
  },
  footerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 10,
  },
  timestamp: {
    color: '#666',
    fontSize: 12,
  },
  modalContent: {
    backgroundColor: 'white',
    padding: 20,
    margin: 20,
    borderRadius: 10,
    maxHeight: '80%',
  },
  templateScroll: {
    flexGrow: 0,
    marginBottom: 20,
  },
  templateCard: {
    width: 200,
    marginRight: 10,
  },
  selectedTemplate: {
    backgroundColor: '#e8f4f8',
  },
  selectedItem: {
    backgroundColor: '#e8f4f8',
  },
  sectionTitle: {
    fontSize: 18,
    marginTop: 20,
    marginBottom: 10,
  },
  chipContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginVertical: 10,
  },
  chip: {
    margin: 4,
  },
  modalButtons: {
    marginTop: 20,
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
  },
});

export default NotificationSystem;
