import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet
} from 'react-native';
import {
  Card,
  Title,
  Button,
  DataTable,
  ActivityIndicator,
  Text,
  Snackbar,
  Chip,
  Menu
} from 'react-native-paper';
import { db, auth } from '../../config/firebase';
import {
  collection,
  query,
  where,
  getDocs
} from 'firebase/firestore';
import ScrollableTable from '../../components/common/ScrollableTable';
import { tableStyles } from '../../styles/tableStyles';

const ViewResults = () => {
  const [results, setResults] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [examType, setExamType] = useState('all');
  const [menuVisible, setMenuVisible] = useState(false);

  useEffect(() => {
    fetchResults();
  }, [examType]);

  const fetchResults = async () => {
    try {
      setLoading(true);
      const gradesRef = collection(db, 'grades');
      let q = query(
        gradesRef,
        where('studentId', '==', auth.currentUser.uid),
        where('status', '==', 'published')
      );

      if (examType !== 'all') {
        q = query(q, where('examType', '==', examType));
      }

      const snapshot = await getDocs(q);
      const resultData = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      // Sort by timestamp descending
      resultData.sort((a, b) => b.timestamp.seconds - a.timestamp.seconds);

      setResults(resultData);
    } catch (err) {
      console.error('Error fetching results:', err);
      setError('Failed to fetch results');
      setSnackbarMessage('Error loading results. Please try again.');
      setSnackbarVisible(true);
    } finally {
      setLoading(false);
    }
  };

  const getGradeColor = (grade) => {
    const numGrade = parseFloat(grade);
    if (numGrade >= 90) return '#4CAF50';
    if (numGrade >= 80) return '#8BC34A';
    if (numGrade >= 70) return '#FFC107';
    if (numGrade >= 60) return '#FF9800';
    return '#f44336';
  };

  if (loading && results.length === 0) {
    return (
      <View style={styles.container}>
        <ActivityIndicator style={styles.loader} />
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.container}>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
          <Button mode="contained" onPress={fetchResults}>
            Retry
          </Button>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Card style={styles.headerCard}>
        <Card.Content>
          <View style={styles.headerContent}>
            <Title>My Results</Title>
            <Menu
              visible={menuVisible}
              onDismiss={() => setMenuVisible(false)}
              anchor={
                <Button
                  mode="outlined"
                  onPress={() => setMenuVisible(true)}
                >
                  {examType === 'all' ? 'All Exams' : examType.charAt(0).toUpperCase() + examType.slice(1)}
                </Button>
              }
            >
              <Menu.Item
                onPress={() => {
                  setExamType('all');
                  setMenuVisible(false);
                }}
                title="All Exams"
              />
              <Menu.Item
                onPress={() => {
                  setExamType('assignment');
                  setMenuVisible(false);
                }}
                title="Assignment"
              />
              <Menu.Item
                onPress={() => {
                  setExamType('midterm');
                  setMenuVisible(false);
                }}
                title="Midterm"
              />
              <Menu.Item
                onPress={() => {
                  setExamType('final');
                  setMenuVisible(false);
                }}
                title="Final"
              />
            </Menu>
          </View>
        </Card.Content>
      </Card>

      {results.length === 0 ? (
        <Card style={styles.emptyCard}>
          <Card.Content>
            <Text style={styles.emptyText}>No results available</Text>
          </Card.Content>
        </Card>
      ) : (
        <ScrollableTable
          header={
            <DataTable.Header style={tableStyles.tableHeader}>
              <DataTable.Title style={tableStyles.classColumn}>Class</DataTable.Title>
              <DataTable.Title style={tableStyles.statusColumn}>Exam Type</DataTable.Title>
              <DataTable.Title numeric style={tableStyles.gradeColumn}>Grade</DataTable.Title>
              <DataTable.Title style={tableStyles.remarksColumn}>Remarks</DataTable.Title>
              <DataTable.Title style={tableStyles.dateColumn}>Date</DataTable.Title>
            </DataTable.Header>
          }
        >
          {results.map((result) => (
            <DataTable.Row key={result.id} style={tableStyles.row}>
              <DataTable.Cell style={tableStyles.classColumn}>{result.className}</DataTable.Cell>
              <DataTable.Cell style={tableStyles.statusColumn}>
                <Chip mode="outlined">
                  {result.examType}
                </Chip>
              </DataTable.Cell>
              <DataTable.Cell numeric style={tableStyles.gradeColumn}>
                <Text style={{ color: getGradeColor(result.grade), fontWeight: 'bold' }}>
                  {result.grade}
                </Text>
              </DataTable.Cell>
              <DataTable.Cell style={tableStyles.remarksColumn}>{result.remarks || '-'}</DataTable.Cell>
              <DataTable.Cell style={tableStyles.dateColumn}>
                {new Date(result.timestamp.seconds * 1000).toLocaleDateString()}
              </DataTable.Cell>
            </DataTable.Row>
          ))}
        </ScrollableTable>
      )}

      <Snackbar
        visible={snackbarVisible}
        onDismiss={() => setSnackbarVisible(false)}
        duration={3000}
      >
        {snackbarMessage}
      </Snackbar>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5'
  },
  headerCard: {
    margin: 16,
    elevation: 4
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center'
  },
  loader: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center'
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16
  },
  errorText: {
    color: 'red',
    marginBottom: 16,
    textAlign: 'center'
  },
  emptyCard: {
    margin: 16
  },
  emptyText: {
    textAlign: 'center',
    color: '#666'
  }
});

export default ViewResults;
