@echo off
echo ===== School Management System - Direct Run =====

echo Step 1: Setting up environment...
if not exist scripts\NUL (
  mkdir scripts
)

echo Step 2: Creating necessary files...
echo // Placeholder for autolinking.gradle > scripts\autolinking.gradle
echo println "Using simplified autolinking.gradle" >> scripts\autolinking.gradle

echo Step 3: Cleaning Android build...
cd android
call gradlew.bat clean
if %ERRORLEVEL% NEQ 0 (
  echo Error cleaning Android build
  cd ..
  goto :ERROR
)
cd ..

echo Step 4: Building Android app...
cd android
call gradlew.bat assembleDebug --stacktrace
if %ERRORLEVEL% NEQ 0 (
  echo Error building Android app
  cd ..
  goto :ERROR
)

echo Step 5: Installing app on device...
call gradlew.bat installDebug
if %ERRORLEVEL% NEQ 0 (
  echo Error installing Android app
  echo Make sure your device is connected and USB debugging is enabled
  cd ..
  goto :ERROR
)
cd ..

echo Step 6: Starting Expo development server...
call npx expo start --android
goto :END

:ERROR
echo ===== Error occurred during setup or build =====
echo Please check the error messages above
exit /b 1

:END
echo ===== Process completed successfully =====
