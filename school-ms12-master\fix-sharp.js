const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('Starting Sharp fix script...');

// Check if sharp is installed locally
try {
  require.resolve('sharp');
  console.log('Sharp is already installed locally.');
} catch (e) {
  console.log('Installing Sharp locally...');
  execSync('npm install --save-dev sharp', { stdio: 'inherit' });
}

// Check if sharp-cli is installed globally
try {
  const sharpCliPath = execSync('npm root -g').toString().trim() + '/sharp-cli';
  if (fs.existsSync(sharpCliPath)) {
    console.log('Found sharp-cli globally at:', sharpCliPath);
    
    // Check if sharp is installed as a dependency of sharp-cli
    const sharpCliPackageJson = path.join(sharpCliPath, 'package.json');
    if (fs.existsSync(sharpCliPackageJson)) {
      const packageData = JSON.parse(fs.readFileSync(sharpCliPackageJson, 'utf8'));
      console.log('sharp-cli dependencies:', packageData.dependencies);
      
      if (packageData.dependencies && packageData.dependencies.sharp) {
        console.log('sharp-cli depends on sharp version:', packageData.dependencies.sharp);
      }
    }
    
    // Check if sharp is actually installed in sharp-cli/node_modules
    const sharpInCliNodeModules = path.join(sharpCliPath, 'node_modules', 'sharp');
    if (fs.existsSync(sharpInCliNodeModules)) {
      console.log('sharp is installed in sharp-cli/node_modules');
    } else {
      console.log('sharp is NOT installed in sharp-cli/node_modules');
      console.log('Installing sharp in sharp-cli...');
      try {
        execSync('npm install --prefix "' + sharpCliPath + '" sharp', { stdio: 'inherit' });
        console.log('Successfully installed sharp in sharp-cli');
      } catch (error) {
        console.error('Failed to install sharp in sharp-cli:', error.message);
      }
    }
  } else {
    console.log('sharp-cli not found globally');
  }
} catch (e) {
  console.error('Error checking for global sharp-cli:', e.message);
}

console.log('Fix script completed.');
