import 'package:cloud_firestore/cloud_firestore.dart';

class UserModel {
  final String id;
  final String email;
  final String displayName;
  final String role;
  final String? photoURL;
  final bool emailVerified;
  final String status;
  final DateTime createdAt;
  final DateTime updatedAt;
  final Map<String, dynamic> additionalData;

  UserModel({
    required this.id,
    required this.email,
    required this.displayName,
    required this.role,
    this.photoURL,
    required this.emailVerified,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
    this.additionalData = const {},
  });

  factory UserModel.fromMap(Map<String, dynamic> map) {
    return UserModel(
      id: map['id'] ?? '',
      email: map['email'] ?? '',
      displayName: map['displayName'] ?? '',
      role: map['role'] ?? '',
      photoURL: map['photoURL'],
      emailVerified: map['emailVerified'] ?? false,
      status: map['status'] ?? 'active',
      createdAt: (map['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (map['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      additionalData: Map<String, dynamic>.from(map['additionalData'] ?? {}),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'email': email,
      'displayName': displayName,
      'role': role,
      'photoURL': photoURL,
      'emailVerified': emailVerified,
      'status': status,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'additionalData': additionalData,
    };
  }

  UserModel copyWith({
    String? id,
    String? email,
    String? displayName,
    String? role,
    String? photoURL,
    bool? emailVerified,
    String? status,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? additionalData,
  }) {
    return UserModel(
      id: id ?? this.id,
      email: email ?? this.email,
      displayName: displayName ?? this.displayName,
      role: role ?? this.role,
      photoURL: photoURL ?? this.photoURL,
      emailVerified: emailVerified ?? this.emailVerified,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      additionalData: additionalData ?? this.additionalData,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'UserModel(id: $id, email: $email, displayName: $displayName, role: $role)';
  }
}

// Role-specific user models
class AdminModel extends UserModel {
  final List<String> permissions;
  final String department;

  AdminModel({
    required super.id,
    required super.email,
    required super.displayName,
    required super.role,
    super.photoURL,
    required super.emailVerified,
    required super.status,
    required super.createdAt,
    required super.updatedAt,
    super.additionalData,
    required this.permissions,
    required this.department,
  });

  factory AdminModel.fromUserModel(UserModel user, {
    List<String>? permissions,
    String? department,
  }) {
    return AdminModel(
      id: user.id,
      email: user.email,
      displayName: user.displayName,
      role: user.role,
      photoURL: user.photoURL,
      emailVerified: user.emailVerified,
      status: user.status,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      additionalData: user.additionalData,
      permissions: permissions ?? [],
      department: department ?? '',
    );
  }
}

class TeacherModel extends UserModel {
  final String employeeId;
  final String department;
  final List<String> subjects;
  final List<String> classes;
  final String qualification;
  final DateTime joinDate;

  TeacherModel({
    required super.id,
    required super.email,
    required super.displayName,
    required super.role,
    super.photoURL,
    required super.emailVerified,
    required super.status,
    required super.createdAt,
    required super.updatedAt,
    super.additionalData,
    required this.employeeId,
    required this.department,
    required this.subjects,
    required this.classes,
    required this.qualification,
    required this.joinDate,
  });

  factory TeacherModel.fromUserModel(UserModel user, {
    String? employeeId,
    String? department,
    List<String>? subjects,
    List<String>? classes,
    String? qualification,
    DateTime? joinDate,
  }) {
    return TeacherModel(
      id: user.id,
      email: user.email,
      displayName: user.displayName,
      role: user.role,
      photoURL: user.photoURL,
      emailVerified: user.emailVerified,
      status: user.status,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      additionalData: user.additionalData,
      employeeId: employeeId ?? '',
      department: department ?? '',
      subjects: subjects ?? [],
      classes: classes ?? [],
      qualification: qualification ?? '',
      joinDate: joinDate ?? DateTime.now(),
    );
  }
}

class StudentModel extends UserModel {
  final String studentId;
  final String rollNumber;
  final String className;
  final String section;
  final DateTime admissionDate;
  final String parentId;
  final Map<String, dynamic> academicInfo;

  StudentModel({
    required super.id,
    required super.email,
    required super.displayName,
    required super.role,
    super.photoURL,
    required super.emailVerified,
    required super.status,
    required super.createdAt,
    required super.updatedAt,
    super.additionalData,
    required this.studentId,
    required this.rollNumber,
    required this.className,
    required this.section,
    required this.admissionDate,
    required this.parentId,
    this.academicInfo = const {},
  });

  factory StudentModel.fromUserModel(UserModel user, {
    String? studentId,
    String? rollNumber,
    String? className,
    String? section,
    DateTime? admissionDate,
    String? parentId,
    Map<String, dynamic>? academicInfo,
  }) {
    return StudentModel(
      id: user.id,
      email: user.email,
      displayName: user.displayName,
      role: user.role,
      photoURL: user.photoURL,
      emailVerified: user.emailVerified,
      status: user.status,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      additionalData: user.additionalData,
      studentId: studentId ?? '',
      rollNumber: rollNumber ?? '',
      className: className ?? '',
      section: section ?? '',
      admissionDate: admissionDate ?? DateTime.now(),
      parentId: parentId ?? '',
      academicInfo: academicInfo ?? {},
    );
  }
}

class ParentModel extends UserModel {
  final List<String> childrenIds;
  final String relationship;
  final String occupation;
  final String phoneNumber;
  final String address;

  ParentModel({
    required super.id,
    required super.email,
    required super.displayName,
    required super.role,
    super.photoURL,
    required super.emailVerified,
    required super.status,
    required super.createdAt,
    required super.updatedAt,
    super.additionalData,
    required this.childrenIds,
    required this.relationship,
    required this.occupation,
    required this.phoneNumber,
    required this.address,
  });

  factory ParentModel.fromUserModel(UserModel user, {
    List<String>? childrenIds,
    String? relationship,
    String? occupation,
    String? phoneNumber,
    String? address,
  }) {
    return ParentModel(
      id: user.id,
      email: user.email,
      displayName: user.displayName,
      role: user.role,
      photoURL: user.photoURL,
      emailVerified: user.emailVerified,
      status: user.status,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      additionalData: user.additionalData,
      childrenIds: childrenIds ?? [],
      relationship: relationship ?? '',
      occupation: occupation ?? '',
      phoneNumber: phoneNumber ?? '',
      address: address ?? '',
    );
  }
}
