import React, { useState, useEffect, useRef } from 'react';
import { View, ScrollView, StyleSheet, StatusBar, SafeAreaView, TouchableOpacity, Animated, Dimensions, Image, RefreshControl } from 'react-native';
import {
  DataTable,
  Card,
  Title,
  Button,
  Chip,
  IconButton,
  Text,
  ActivityIndicator,
  Snackbar,
  Portal,
  Dialog,
  TextInput,
  SegmentedButtons,
  Checkbox,
  useTheme,
  Paragraph,
  Divider,
  Surface,
  Avatar,
  Menu,
  Headline,
  Caption,
  Badge,
  ProgressBar
} from 'react-native-paper';
import { db, auth } from '../../config/firebase';
import {
  collection,
  query,
  where,
  getDocs,
  addDoc,
  orderBy,
  Timestamp,
  doc,
  getDoc,
  updateDoc,
  serverTimestamp,
  setDoc
} from 'firebase/firestore';
import EthiopianDatePicker from '../../components/common/EthiopianDatePicker';
import { useTranslation } from '../../hooks/useTranslation';
import * as Animatable from 'react-native-animatable';
import { LinearGradient } from 'expo-linear-gradient';
import TeacherAppHeader from '../../components/common/TeacherAppHeader';
import TeacherSidebar from '../../components/common/TeacherSidebar';
import SidebarBackdrop from '../../components/common/SidebarBackdrop';
import { format, isToday, isYesterday, subDays } from 'date-fns';
import AttendanceNotificationService from '../../services/AttendanceNotificationService';

const AttendanceManagement = ({ route, navigation }) => {
  const { t, language, isRTL, getTextStyle } = useTranslation();
  const theme = useTheme();
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;
  const { width } = Dimensions.get('window');
  const isSmallScreen = width < 360;

  // Add default values and error handling for route params
  const params = route?.params || {};
  const classId = params.classId;
  const className = params.className;
  const sectionName = params.sectionName;

  // Sidebar state
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [activeSidebarItem, setActiveSidebarItem] = useState('AttendanceManagement');
  const backdropFadeAnim = useRef(new Animated.Value(0)).current;

  // Attendance data state
  const [students, setStudents] = useState([]);
  const [attendanceData, setAttendanceData] = useState({});
  const [loading, setLoading] = useState(false);
  const [dataLoading, setDataLoading] = useState(true);
  const [error, setError] = useState(null);
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [attendanceDate, setAttendanceDate] = useState(new Date());
  const [confirmDialogVisible, setConfirmDialogVisible] = useState(false);
  const [confirmAction, setConfirmAction] = useState(null);

  // Attendance summary stats
  const [attendanceSummary, setAttendanceSummary] = useState({
    present: 0,
    absent: 0,
    late: 0,
    excused: 0
  });

  // Submission state
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [submissionId, setSubmissionId] = useState(null);
  const [submissionStatus, setSubmissionStatus] = useState('pending');
  const [submitDialogVisible, setSubmitDialogVisible] = useState(false);
  const [submissionNote, setSubmissionNote] = useState('');

  // Toggle sidebar
  const toggleDrawer = () => {
    const newValue = !drawerOpen;
    setDrawerOpen(newValue);

    // Animate backdrop
    Animated.timing(backdropFadeAnim, {
      toValue: newValue ? 0.5 : 0,
      duration: 300,
      useNativeDriver: true,
    }).start();
  };

  // Start animations when component mounts
  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      })
    ]).start();
  }, []);

  // Redirect to ClassManagement if required params are missing
  useEffect(() => {
    if (!classId || !className || !sectionName) {
      navigation.replace('ClassManagement');
      return;
    }
  }, [classId, className, sectionName]);

  useEffect(() => {
    if (classId && className && sectionName) {
      fetchStudentsAndAttendance();
    }
  }, [classId, className, sectionName, attendanceDate]);

  // Calculate attendance summary whenever attendanceData changes
  useEffect(() => {
    calculateAttendanceSummary();
  }, [attendanceData, students]);

  // Calculate summary statistics from attendance data
  const calculateAttendanceSummary = () => {
    const summary = {
      present: 0,
      absent: 0,
      late: 0,
      excused: 0,
    };

    // Count each status type
    Object.values(attendanceData).forEach(data => {
      if (data && data.status) {
        summary[data.status]++;
      }
    });

    setAttendanceSummary(summary);
  };

  // Format date for display
  const formatDateForDisplay = (date) => {
    if (isToday(date)) {
      return t('common.today');
    } else if (isYesterday(date)) {
      return t('common.yesterday');
    } else {
      // Get locale based on current language
      const locale = language === 'en' ? 'en-US' :
                    language === 'am' ? 'am-ET' : 'om-ET';

      return format(date, 'PPP', { locale });
    }
  };

  const fetchStudentsAndAttendance = async () => {
    try {
      setDataLoading(true);
      setError(null);

      // Show loading message
      setSnackbarMessage(t('attendanceManagement.loadingData'));
      setSnackbarVisible(true);

      // Get the class document to get the section's student IDs
      const classRef = doc(db, 'classes', classId);
      const classDoc = await getDoc(classRef);

      if (!classDoc.exists()) {
        throw new Error(t('attendanceManagement.classNotFound'));
      }

      const classData = classDoc.data();
      const section = classData.sections.find(s => s.name === sectionName);

      if (!section || !section.students || section.students.length === 0) {
        setStudents([]);
        setSnackbarMessage(t('attendanceManagement.noStudentsInSection'));
        setSnackbarVisible(true);
        return;
      }

      // Fetch all students in the section
      const usersRef = collection(db, 'users');
      const studentPromises = section.students.map(async studentId => {
        try {
          const studentDoc = await getDoc(doc(usersRef, studentId));
          if (studentDoc.exists()) {
            const studentData = studentDoc.data();

            // Format student name
            const firstName = studentData.firstName || '';
            const lastName = studentData.lastName || '';
            const fullName = [firstName, lastName].filter(Boolean).join(' ');
            const displayName = studentData.displayName || '';

            return {
              id: studentDoc.id,
              name: fullName || displayName || studentData.username || t('common.unnamed'),
              rollNumber: studentData.rollNumber || t('common.notAssigned'),
              profileImage: studentData.profileImage || null,
              gender: studentData.gender || 'unknown'
            };
          }
        } catch (error) {
          console.error(`Error fetching student ${studentId}:`, error);
        }
        return null;
      });

      const studentsData = (await Promise.all(studentPromises))
        .filter(student => student !== null)
        .sort((a, b) => {
          // First sort by roll number if available
          if (a.rollNumber !== t('common.notAssigned') &&
              b.rollNumber !== t('common.notAssigned')) {
            return a.rollNumber.localeCompare(b.rollNumber);
          }
          // Then sort by name
          return a.name.localeCompare(b.name);
        });

      // Initialize default attendance for all students
      const initialAttendance = {};
      studentsData.forEach(student => {
        initialAttendance[student.id] = {
          status: 'present',
          remarks: '',
          id: null  // Add this to track if it's a new or existing record
        };
      });

      // Format date for query
      const formattedDate = attendanceDate.toISOString().split('T')[0];

      // Fetch attendance for the selected date
      const attendanceRef = collection(db, 'attendance');
      const attendanceQuery = query(
        attendanceRef,
        where('classId', '==', classId),
        where('sectionName', '==', sectionName),
        where('date', '==', formattedDate)
      );

      const attendanceSnapshot = await getDocs(attendanceQuery);
      const attendanceMap = { ...initialAttendance };

      attendanceSnapshot.docs.forEach(doc => {
        const data = doc.data();
        if (data.studentId && attendanceMap[data.studentId]) {
          attendanceMap[data.studentId] = {
            id: doc.id,
            status: data.status || 'present',
            remarks: data.remarks || ''
          };
        }
      });

      setStudents(studentsData);
      setAttendanceData(attendanceMap);

      // Check if attendance has been submitted for this date
      const dateString = attendanceDate.toISOString().split('T')[0];
      const submissionsRef = collection(db, 'attendanceSubmissions');
      const submissionQuery = query(
        submissionsRef,
        where('classId', '==', classId),
        where('sectionName', '==', sectionName),
        where('date', '==', dateString)
      );

      const submissionSnapshot = await getDocs(submissionQuery);

      if (!submissionSnapshot.empty) {
        const submissionDoc = submissionSnapshot.docs[0];
        const submissionData = submissionDoc.data();

        setIsSubmitted(true);
        setSubmissionId(submissionDoc.id);
        setSubmissionStatus(submissionData.status || 'pending');
        setSubmissionNote(submissionData.note || '');

        console.log(`Found existing submission with status: ${submissionData.status}`);
      } else {
        setIsSubmitted(false);
        setSubmissionId(null);
        setSubmissionStatus('pending');
        setSubmissionNote('');

        console.log('No existing submission found for this date');
      }

      // Hide loading message
      setSnackbarVisible(false);

    } catch (err) {
      console.error('Error fetching data:', err);
      setError(t('attendanceManagement.fetchError'));
      setSnackbarMessage(t('attendanceManagement.loadingError'));
      setSnackbarVisible(true);
    } finally {
      setDataLoading(false);
    }
  };

  const handleDateChange = (selectedDate) => {
    setAttendanceDate(selectedDate);
  };

  const handleStatusChange = (studentId, newStatus) => {
    setAttendanceData(prev => {
      const newData = { ...prev };
      newData[studentId] = {
        ...newData[studentId],
        status: newStatus
      };
      return newData;
    });

    // Show feedback
    const student = students.find(s => s.id === studentId);
    if (student) {
      const statusText = newStatus === 'present'
        ? t('attendanceManagement.markedPresent')
        : t('attendanceManagement.markedAbsent');

      setSnackbarMessage(`${student.name} ${statusText}`);
      setSnackbarVisible(true);
    }
  };

  const handleRemarksChange = (studentId, remarks) => {
    setAttendanceData(prev => ({
      ...prev,
      [studentId]: {
        ...prev[studentId],
        remarks
      }
    }));
  };

  const handleMarkAll = (status) => {
    setConfirmAction(() => () => {
      setAttendanceData(prev => {
        const newData = { ...prev };
        students.forEach(student => {
          newData[student.id] = {
            ...newData[student.id],
            status: status
          };
        });
        return newData;
      });

      // Show feedback
      const statusText = status === 'present'
        ? t('attendanceManagement.allMarkedPresent')
        : t('attendanceManagement.allMarkedAbsent');

      setSnackbarMessage(statusText);
      setSnackbarVisible(true);
      setConfirmDialogVisible(false);
    });

    setConfirmDialogVisible(true);
  };

  const getAvatarColor = (gender) => {
    switch(gender) {
      case 'male':
        return theme.colors.primary;
      case 'female':
        return '#E91E63';
      default:
        return theme.colors.accent;
    }
  };

  const getAvatarIcon = (gender) => {
    switch(gender) {
      case 'male':
        return 'account';
      case 'female':
        return 'account-outline';
      default:
        return 'account-question';
    }
  };

  const renderStudentList = () => (
    <View style={styles.studentListContainer}>
      <View style={styles.studentListHeader}>
        <Title style={getTextStyle(styles.studentListTitle)}>{t('teacher.attendance.studentList')}</Title>

        {students.length > 0 && (
          <View style={styles.markAllContainer}>
            <Text style={getTextStyle(styles.markAllText)}>{t('teacher.attendance.markAll')}:</Text>
            <View style={styles.markAllButtonsContainer}>
              <Button
                mode="contained"
                onPress={() => handleMarkAll('present')}
                icon="check-circle"
                style={[styles.markAllButton, { backgroundColor: theme.colors.primary }]}
                labelStyle={styles.markAllButtonLabel}
              >
                {t('teacher.attendance.present')}
              </Button>
              <Button
                mode="contained"
                onPress={() => handleMarkAll('absent')}
                icon="close-circle"
                style={[styles.markAllButton, { backgroundColor: theme.colors.error }]}
                labelStyle={styles.markAllButtonLabel}
              >
                {t('teacher.attendance.absent')}
              </Button>
              <Button
                mode="contained"
                onPress={() => handleMarkAll('late')}
                icon="clock"
                style={[styles.markAllButton, { backgroundColor: theme.colors.warning }]}
                labelStyle={styles.markAllButtonLabel}
              >
                {t('teacher.attendance.late')}
              </Button>
            </View>
          </View>
        )}
      </View>

      {students.length === 0 ? (
        <View style={styles.emptyStateContainer}>
          <Avatar.Icon size={64} icon="account-alert" style={styles.emptyStateIcon} />
          <Text style={getTextStyle(styles.emptyStateText)}>
            {t('teacher.attendance.noStudents')}
          </Text>
          <Button
            mode="contained"
            onPress={() => navigation.navigate('ClassManagement')}
            style={styles.emptyStateButton}
          >
            {t('teacher.classes.viewClasses')}
          </Button>
        </View>
      ) : (
        <View style={styles.tableWrapper}>
          <ScrollView horizontal showsHorizontalScrollIndicator={true}>
            <View style={styles.tableContainer}>
              <DataTable style={styles.dataTable}>
                <DataTable.Header style={styles.tableHeader}>
                  <DataTable.Title style={styles.photoColumn}>{t('common.photo')}</DataTable.Title>
                  <DataTable.Title style={styles.rollColumn}>{t('common.roll')}</DataTable.Title>
                  <DataTable.Title style={styles.nameColumn}>{t('common.name')}</DataTable.Title>
                  <DataTable.Title style={styles.statusColumn}>{t('teacher.attendance.status')}</DataTable.Title>
                  <DataTable.Title style={styles.remarksColumn}>{t('common.remarks')}</DataTable.Title>
                </DataTable.Header>

                <ScrollView style={styles.tableBody}>
                  {students.map((student) => (
                    <DataTable.Row key={student.id} style={styles.dataRow}>
                      <DataTable.Cell style={styles.photoColumn}>
                        <Avatar.Image
                          size={40}
                          source={
                            student.profileImage
                              ? { uri: student.profileImage }
                              : require('../../assets/default-avatar.png')
                          }
                          style={{ backgroundColor: getAvatarColor(student.gender) }}
                        />
                      </DataTable.Cell>
                      <DataTable.Cell style={styles.rollColumn}>
                        <Text style={getTextStyle(styles.rollNumber)}>{student.rollNumber}</Text>
                      </DataTable.Cell>
                      <DataTable.Cell style={styles.nameColumn}>
                        <Text style={getTextStyle(styles.studentName)}>{student.name}</Text>
                      </DataTable.Cell>
                      <DataTable.Cell style={styles.statusColumn}>
                        <SegmentedButtons
                          value={attendanceData[student.id]?.status || 'present'}
                          onValueChange={(value) => handleStatusChange(student.id, value)}
                          buttons={[
                            {
                              value: 'present',
                              icon: 'check-circle',
                              label: t('teacher.attendance.present').charAt(0),
                              style: {
                                backgroundColor: attendanceData[student.id]?.status === 'present'
                                  ? theme.colors.primary
                                  : theme.colors.background
                              },
                              labelStyle: {
                                color: attendanceData[student.id]?.status === 'present'
                                  ? theme.colors.background
                                  : theme.colors.text
                              }
                            },
                            {
                              value: 'absent',
                              icon: 'close-circle',
                              label: t('teacher.attendance.absent').charAt(0),
                              style: {
                                backgroundColor: attendanceData[student.id]?.status === 'absent'
                                  ? theme.colors.error
                                  : theme.colors.background
                              },
                              labelStyle: {
                                color: attendanceData[student.id]?.status === 'absent'
                                  ? theme.colors.background
                                  : theme.colors.text
                              }
                            },
                            {
                              value: 'late',
                              icon: 'clock',
                              label: t('teacher.attendance.late').charAt(0),
                              style: {
                                backgroundColor: attendanceData[student.id]?.status === 'late'
                                  ? theme.colors.warning
                                  : theme.colors.background
                              },
                              labelStyle: {
                                color: attendanceData[student.id]?.status === 'late'
                                  ? theme.colors.background
                                  : theme.colors.text
                              }
                            },
                          ]}
                        />
                      </DataTable.Cell>
                      <DataTable.Cell style={styles.remarksColumn}>
                        <TextInput
                          mode="outlined"
                          placeholder={t('common.addRemarks')}
                          value={attendanceData[student.id]?.remarks || ''}
                          onChangeText={(text) => handleRemarksChange(student.id, text)}
                          dense
                          style={styles.remarksInput}
                        />
                      </DataTable.Cell>
                    </DataTable.Row>
                  ))}
                </ScrollView>
              </DataTable>
            </View>
          </ScrollView>
        </View>
      )}

      {students.length > 0 && (
        <View style={styles.actionButtonsContainer}>
          <Button
            mode="contained"
            onPress={saveAttendance}
            loading={loading}
            disabled={loading || students.length === 0}
            icon="content-save"
            style={styles.saveButton}
          >
            {t('common.save')}
          </Button>
        </View>
      )}
    </View>
  );

  const saveAttendance = async () => {
    try {
      setLoading(true);

      // Show saving message
      setSnackbarMessage(t('attendanceManagement.savingAttendance'));
      setSnackbarVisible(true);

      const attendanceRef = collection(db, 'attendance');
      const formattedDate = attendanceDate.toISOString().split('T')[0];

      // Save attendance for each student
      const savePromises = students.map(async student => {
        const studentAttendance = attendanceData[student.id] || { status: 'present', remarks: '' };

        const attendanceRecord = {
          classId,
          className,
          sectionName,
          studentId: student.id,
          studentName: student.name,
          date: formattedDate,
          status: studentAttendance.status,
          remarks: studentAttendance.remarks || '',
          timestamp: Timestamp.now(),
          updatedAt: new Date().toISOString(),
          teacherId: auth.currentUser?.uid || '',
          submitted: false
        };

        try {
          if (studentAttendance.id) {
            // Update existing record
            await updateDoc(doc(attendanceRef, studentAttendance.id), attendanceRecord);
          } else {
            // Create new record
            const docRef = await addDoc(attendanceRef, attendanceRecord);

            // Update local state with the new document ID
            setAttendanceData(prev => ({
              ...prev,
              [student.id]: {
                ...prev[student.id],
                id: docRef.id
              }
            }));
          }
        } catch (error) {
          console.error(`Error saving attendance for student ${student.name}:`, error);
          throw error; // Propagate the error
        }
      });

      await Promise.all(savePromises);

      // Show success message
      setSnackbarMessage(t('attendanceManagement.saveSuccess'));
      setSnackbarVisible(true);

      // Refresh data to ensure we have the latest
      fetchStudentsAndAttendance();

    } catch (err) {
      console.error('Error saving attendance:', err);
      setError(t('attendanceManagement.saveError'));
      setSnackbarMessage(t('attendanceManagement.saveFailed'));
      setSnackbarVisible(true);
    } finally {
      setLoading(false);
    }
  };

  const submitAttendance = async () => {
    try {
      setLoading(true);

      // Show submitting message
      setSnackbarMessage(t('attendanceManagement.submittingAttendance'));
      setSnackbarVisible(true);

      // First save the attendance to ensure all records are up to date
      await saveAttendance();

      const formattedDate = attendanceDate.toISOString().split('T')[0];

      // Get all attendance records for this class, section, and date
      const attendanceRef = collection(db, 'attendance');
      const attendanceQuery = query(
        attendanceRef,
        where('classId', '==', classId),
        where('sectionName', '==', sectionName),
        where('date', '==', formattedDate)
      );

      const attendanceSnapshot = await getDocs(attendanceQuery);

      // Validate that we have attendance records for all students
      if (attendanceSnapshot.docs.length === 0) {
        throw new Error(t('attendanceManagement.noAttendanceRecords'));
      }

      if (attendanceSnapshot.docs.length !== students.length) {
        console.warn(`Attendance records count (${attendanceSnapshot.docs.length}) doesn't match student count (${students.length})`);
      }

      // Create attendance summary
      const presentCount = attendanceSnapshot.docs.filter(doc => doc.data().status === 'present').length;
      const absentCount = attendanceSnapshot.docs.filter(doc => doc.data().status === 'absent').length;
      const lateCount = attendanceSnapshot.docs.filter(doc => doc.data().status === 'late').length;
      const excusedCount = attendanceSnapshot.docs.filter(doc => doc.data().status === 'excused').length;

      // Calculate attendance percentage
      const attendancePercentage = students.length > 0 ? (presentCount / students.length) * 100 : 0;

      // Create or update submission record
      const submissionsRef = collection(db, 'attendanceSubmissions');

      const submissionData = {
        classId,
        className,
        sectionName,
        date: formattedDate,
        teacherId: auth.currentUser?.uid || '',
        teacherName: auth.currentUser?.displayName || auth.currentUser?.email || 'Unknown Teacher',
        submittedAt: serverTimestamp(),
        status: 'pending',
        note: submissionNote,
        summary: {
          total: students.length,
          present: presentCount,
          absent: absentCount,
          late: lateCount,
          excused: excusedCount,
          percentage: attendancePercentage
        },
        attendanceIds: attendanceSnapshot.docs.map(doc => doc.id),
        // Add section-specific information
        sectionDetails: {
          name: sectionName,
          studentCount: students.length,
          attendanceDate: formattedDate,
          formattedDate: new Date(formattedDate).toLocaleDateString()
        }
      };

      let submissionDocRef;

      if (submissionId) {
        // Update existing submission
        submissionDocRef = doc(submissionsRef, submissionId);
        await updateDoc(submissionDocRef, submissionData);
      } else {
        // Create new submission
        submissionDocRef = await addDoc(submissionsRef, submissionData);
        setSubmissionId(submissionDocRef.id);
      }

      // Update all attendance records to mark them as submitted
      const updatePromises = attendanceSnapshot.docs.map(async doc => {
        await updateDoc(doc.ref, {
          submitted: true,
          submissionId: submissionDocRef.id
        });
      });

      await Promise.all(updatePromises);

      // Update state
      setIsSubmitted(true);
      setSubmissionStatus('pending');
      setSubmitDialogVisible(false);

      // Prepare submission object for notification
      const submissionForNotification = {
        id: submissionDocRef.id,
        ...submissionData,
        submittedAt: new Date(), // Use a JavaScript Date object for the notification service
        // Add additional information for better notifications
        sectionInfo: `${className}-${sectionName}`,
        studentCount: students.length,
        attendanceDate: formattedDate,
        formattedDate: new Date(formattedDate).toLocaleDateString(),
        attendanceRate: attendancePercentage.toFixed(1) + '%'
      };

      // Send notification to admin about the new attendance submission
      try {
        await AttendanceNotificationService.notifyAttendanceSubmission(submissionForNotification);
        console.log('Attendance submission notification sent to admins');
      } catch (notificationError) {
        console.error('Error sending attendance submission notification:', notificationError);
        // Continue even if notification fails
      }

      // Show success message
      setSnackbarMessage(t('attendanceManagement.submitSuccess'));
      setSnackbarVisible(true);

      // Refresh data
      fetchStudentsAndAttendance();

    } catch (err) {
      console.error('Error submitting attendance:', err);
      setError(t('attendanceManagement.submitError'));
      setSnackbarMessage(t('attendanceManagement.submitFailed'));
      setSnackbarVisible(true);
    } finally {
      setLoading(false);
    }
  };

  const renderAttendanceSummary = () => {
    const totalStudents = students.length;
    const presentPercent = totalStudents > 0 ? (attendanceSummary.present / totalStudents) * 100 : 0;
    const absentPercent = totalStudents > 0 ? (attendanceSummary.absent / totalStudents) * 100 : 0;
    const latePercent = totalStudents > 0 ? (attendanceSummary.late / totalStudents) * 100 : 0;
    const excusedPercent = totalStudents > 0 ? (attendanceSummary.excused / totalStudents) * 100 : 0;

    return (
      <Animatable.View animation="fadeInUp" duration={800} delay={300}>
        <Card style={styles.summaryCard}>
          <Card.Title
            title={t('attendanceManagement.summary')}
            titleStyle={getTextStyle(styles.summaryTitle)}
            left={(props) => <Avatar.Icon {...props} icon="chart-pie" color="white" style={styles.summaryIcon} />}
          />
          <Card.Content>
            <View style={styles.summaryStatsContainer}>
              <View style={styles.summaryStatItem}>
                <Surface style={[styles.statCircle, { backgroundColor: theme.colors.primary }]}>
                  <Text style={styles.statNumber}>{attendanceSummary.present}</Text>
                </Surface>
                <Text style={getTextStyle(styles.statLabel)}>{t('attendanceManagement.present')}</Text>
                <Text style={styles.statPercent}>{presentPercent.toFixed(0)}%</Text>
                <ProgressBar
                  progress={presentPercent / 100}
                  color={theme.colors.primary}
                  style={styles.statProgress}
                />
              </View>

              <View style={styles.summaryStatItem}>
                <Surface style={[styles.statCircle, { backgroundColor: '#F44336' }]}>
                  <Text style={styles.statNumber}>{attendanceSummary.absent}</Text>
                </Surface>
                <Text style={getTextStyle(styles.statLabel)}>{t('attendanceManagement.absent')}</Text>
                <Text style={styles.statPercent}>{absentPercent.toFixed(0)}%</Text>
                <ProgressBar
                  progress={absentPercent / 100}
                  color="#F44336"
                  style={styles.statProgress}
                />
              </View>

              <View style={styles.summaryStatItem}>
                <Surface style={[styles.statCircle, { backgroundColor: '#FF9800' }]}>
                  <Text style={styles.statNumber}>{attendanceSummary.late}</Text>
                </Surface>
                <Text style={getTextStyle(styles.statLabel)}>{t('attendanceManagement.late')}</Text>
                <Text style={styles.statPercent}>{latePercent.toFixed(0)}%</Text>
                <ProgressBar
                  progress={latePercent / 100}
                  color="#FF9800"
                  style={styles.statProgress}
                />
              </View>

              <View style={styles.summaryStatItem}>
                <Surface style={[styles.statCircle, { backgroundColor: '#4CAF50' }]}>
                  <Text style={styles.statNumber}>{attendanceSummary.excused}</Text>
                </Surface>
                <Text style={getTextStyle(styles.statLabel)}>{t('attendanceManagement.excused')}</Text>
                <Text style={styles.statPercent}>{excusedPercent.toFixed(0)}%</Text>
                <ProgressBar
                  progress={excusedPercent / 100}
                  color="#4CAF50"
                  style={styles.statProgress}
                />
              </View>
            </View>

            <Divider style={styles.summaryDivider} />

            <View style={styles.dateDisplay}>
              <Text style={styles.dateLabel}>{t('attendanceManagement.date')}:</Text>
              <Chip icon="calendar" style={styles.dateChip}>
                {formatDateForDisplay(attendanceDate)}
              </Chip>
              {isSubmitted && (
                <Chip
                  icon={
                    submissionStatus === 'approved' ? 'check-circle' :
                    submissionStatus === 'rejected' ? 'close-circle' : 'clock-outline'
                  }
                  style={[
                    styles.statusChip,
                    {
                      backgroundColor:
                        submissionStatus === 'approved' ? 'rgba(76, 175, 80, 0.2)' :
                        submissionStatus === 'rejected' ? 'rgba(244, 67, 54, 0.2)' :
                        'rgba(255, 152, 0, 0.2)'
                    }
                  ]}
                >
                  {t(`attendanceManagement.${submissionStatus}`)}
                </Chip>
              )}
            </View>
          </Card.Content>
        </Card>
      </Animatable.View>
    );
  };

  if (!classId || !className || !sectionName) {
    return null; // Component will redirect in useEffect
  }

  return (
    <SafeAreaView style={styles.safeArea}>
      <TeacherAppHeader
        title={`${t('attendanceManagement.title')}`}
        subtitle={`${className} ${sectionName}`}
        navigation={navigation}
        onMenuPress={toggleDrawer}
      />

      {/* Sidebar */}
      {drawerOpen && (
        <SidebarBackdrop
          opacity={backdropFadeAnim}
          onPress={toggleDrawer}
        />
      )}

      <TeacherSidebar
        visible={drawerOpen}
        onClose={toggleDrawer}
        navigation={navigation}
        activeSidebarItem={activeSidebarItem}
        setActiveSidebarItem={setActiveSidebarItem}
      />

      {/* Confirmation Dialogs */}
      <Portal>
        <Dialog visible={confirmDialogVisible} onDismiss={() => setConfirmDialogVisible(false)}>
          <Dialog.Title>{t('common.confirm')}</Dialog.Title>
          <Dialog.Content>
            <Paragraph>{t('teacher.attendance.confirmSubmit')}</Paragraph>
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={() => setConfirmDialogVisible(false)}>{t('common.cancel')}</Button>
            <Button onPress={() => {
              setConfirmDialogVisible(false);
              if (confirmAction) confirmAction();
            }}>{t('common.confirm')}</Button>
          </Dialog.Actions>
        </Dialog>

        <Dialog visible={submitDialogVisible} onDismiss={() => setSubmitDialogVisible(false)}>
          <Dialog.Title>{t('attendanceManagement.submitAttendance')}</Dialog.Title>
          <Dialog.Content>
            <Paragraph>{t('teacher.attendance.submitDetails')}</Paragraph>
            <TextInput
              label={t('common.notes')}
              value={submissionNote}
              onChangeText={setSubmissionNote}
              multiline
              numberOfLines={3}
              style={styles.submitNote}
            />
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={() => setSubmitDialogVisible(false)}>{t('common.cancel')}</Button>
            <Button onPress={submitAttendance} loading={loading}>{t('common.submit')}</Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>

      {/* Main Content */}
              <LinearGradient
        colors={['#f5f7fa', '#c3cfe2']}
        style={styles.gradientBackground}
      >
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollViewContent}
          refreshControl={
            <RefreshControl
              refreshing={loading}
              onRefresh={fetchStudentsAndAttendance}
              colors={[theme.colors.primary]}
            />
          }
        >
          <Animated.View
            style={[
              styles.container,
              {
                opacity: fadeAnim,
                transform: [{ translateY: slideAnim }]
              }
            ]}
          >
            {dataLoading ? (
              <Animatable.View animation="pulse" easing="ease-out" iterationCount="infinite">
                <Card style={styles.loadingCard}>
                  <Card.Content style={styles.loadingContent}>
                    <ActivityIndicator size="large" color={theme.colors.primary} />
                    <Text style={[styles.loadingText, getTextStyle()]}>{t('attendanceManagement.loadingData')}</Text>
              </Card.Content>
            </Card>
          </Animatable.View>
        ) : (
          <>
                {renderAttendanceSummary()}

                <Animatable.View animation="fadeInUp" duration={800} delay={400}>
                  <Card style={styles.controlCard}>
                <Card.Content>
                      <View style={styles.datePickerContainer}>
                        <Text style={[styles.datePickerLabel, getTextStyle()]}>{t('attendanceManagement.date')}:</Text>
                    <EthiopianDatePicker
                          date={attendanceDate}
                          onDateChange={handleDateChange}
                          style={styles.datePicker}
                      language={language}
                    />
                  </View>

                <View style={styles.buttonContainer}>
                  <Button
                    mode="contained"
                    icon="content-save"
                          style={styles.actionButton}
                    onPress={saveAttendance}
                    loading={loading}
                          disabled={isSubmitted && submissionStatus === 'approved'}
                  >
                          {t('attendanceManagement.saveAttendance')}
                  </Button>

                  <Button
                    mode="contained"
                    icon="send"
                    style={[styles.actionButton, styles.submitButton]}
                          onPress={() => setSubmitDialogVisible(true)}
                          disabled={students.length === 0 || (isSubmitted && submissionStatus !== 'rejected')}
                  >
                          {t('attendanceManagement.submitAttendance')}
                  </Button>
                </View>
                    </Card.Content>
                  </Card>
            </Animatable.View>

                <Animatable.View animation="fadeInUp" duration={800} delay={500}>
                  {renderStudentList()}
                </Animatable.View>
              </>
            )}
          </Animated.View>
        </ScrollView>
      </LinearGradient>

      <Snackbar
        visible={snackbarVisible}
        onDismiss={() => setSnackbarVisible(false)}
        duration={3000}
        action={{
          label: t('common.dismiss'),
          onPress: () => setSnackbarVisible(false),
        }}
        style={styles.snackbar}
      >
        {snackbarMessage}
      </Snackbar>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#f5f7fa'
  },
  gradientBackground: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    padding: 16,
    paddingBottom: 32,
  },
  container: {
    flex: 1,
  },
  loadingCard: {
    padding: 16,
    borderRadius: 12,
    elevation: 2,
    marginBottom: 16,
  },
  loadingContent: {
    alignItems: 'center',
    padding: 24,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    textAlign: 'center',
  },
  summaryCard: {
    borderRadius: 12,
    elevation: 2,
    marginBottom: 16,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.05)',
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  summaryIcon: {
    backgroundColor: '#2196F3',
  },
  summaryStatsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flexWrap: 'wrap',
    marginBottom: 16,
  },
  summaryStatItem: {
    alignItems: 'center',
    width: '25%',
    padding: 4,
  },
  statCircle: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 2,
    marginBottom: 8,
  },
  statNumber: {
    fontSize: 18,
    fontWeight: 'bold',
    color: 'white',
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
    marginBottom: 4,
  },
  statPercent: {
    fontSize: 12,
    color: '#666',
    fontWeight: 'bold',
  },
  statProgress: {
    width: '100%',
    height: 4,
    borderRadius: 2,
    marginTop: 4,
  },
  summaryDivider: {
    marginVertical: 12,
  },
  dateDisplay: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
    gap: 8,
  },
  dateLabel: {
    fontSize: 14,
    color: '#666',
    fontWeight: 'bold',
  },
  dateChip: {
    backgroundColor: 'rgba(33, 150, 243, 0.1)',
  },
  statusChip: {
    marginRight: 8,
  },
  controlCard: {
    borderRadius: 12,
    elevation: 2,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.05)',
  },
  datePickerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    flexWrap: 'wrap',
  },
  datePickerLabel: {
    fontSize: 16,
    marginRight: 8,
    fontWeight: 'bold',
    color: '#333',
  },
  datePicker: {
    flex: 1,
    minWidth: 200,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
  },
  actionButton: {
    flex: 1,
    borderRadius: 8,
    elevation: 2,
  },
  submitButton: {
    backgroundColor: '#4CAF50',
  },
  studentCard: {
    borderRadius: 12,
    elevation: 2,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.05)',
  },
  studentIcon: {
    backgroundColor: '#2196F3',
  },
  filterContainer: {
    marginBottom: 16,
  },
  filterButtons: {
    marginVertical: 8,
  },
  markAllContainer: {
    marginBottom: 16,
  },
  markAllText: {
    fontSize: 16,
    marginBottom: 8,
    fontWeight: 'bold',
    color: '#333',
  },
  markAllButtons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    justifyContent: 'space-between',
  },
  markButton: {
    marginBottom: 8,
    flex: 1,
    minWidth: '48%',
  },
  markButtonContent: {
    height: 36,
  },
  divider: {
    marginVertical: 12,
  },
  studentItemSurface: {
    borderRadius: 12,
    marginBottom: 12,
    padding: 12,
    elevation: 1,
    backgroundColor: 'white',
    borderLeftWidth: 4,
    borderLeftColor: '#2196F3',
  },
  studentItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  studentImageContainer: {
    marginRight: 12,
  },
  studentAvatar: {
    borderWidth: 2,
    borderColor: 'rgba(0,0,0,0.05)',
  },
  studentInfo: {
    flex: 1,
  },
  studentName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  rollNumber: {
    fontSize: 14,
    color: '#757575',
  },
  statusButtonsContainer: {
    width: '100%',
    marginTop: 8,
  },
  statusButtons: {
    marginVertical: 4,
  },
  remarksContainer: {
    marginTop: 8,
  },
  remarksInput: {
    backgroundColor: 'rgba(0,0,0,0.02)',
    fontSize: 14,
  },
  submitNote: {
    marginTop: 12,
  },
  snackbar: {
    margin: 16,
    borderRadius: 8,
    elevation: 6,
  },
  // Student List Table Styles
  studentListContainer: {
    flex: 1,
    marginBottom: 16,
    backgroundColor: '#ffffff',
    borderRadius: 8,
    elevation: 2,
    overflow: 'hidden',
  },
  studentListHeader: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
    backgroundColor: '#f9f9f9',
  },
  studentListTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  // Table Styles
  tableWrapper: {
    flex: 1,
    borderRadius: 0,
  },
  tableContainer: {
    flex: 1,
    minWidth: '100%',
  },
  dataTable: {
    backgroundColor: '#ffffff',
  },
  tableHeader: {
    backgroundColor: '#f0f0f0',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    elevation: 2,
  },
  tableBody: {
    flex: 1,
    maxHeight: 400, // Set a max height to enable vertical scrolling
  },
  dataRow: {
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
    minHeight: 70,
  },
  // Column Styles
  photoColumn: {
    width: 70,
    justifyContent: 'center',
  },
  rollColumn: {
    width: 100,
    justifyContent: 'center',
  },
  nameColumn: {
    width: 180,
    justifyContent: 'center',
  },
  statusColumn: {
    width: 180,
    justifyContent: 'center',
  },
  remarksColumn: {
    width: 250,
    justifyContent: 'center',
  },
  // Action Buttons
  actionButtonsContainer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
    backgroundColor: '#f9f9f9',
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  saveButton: {
    minWidth: 120,
  },
  // Mark All Buttons Container
  markAllButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flexWrap: 'wrap',
  },
  markAllButton: {
    marginRight: 8,
    marginBottom: 8,
    flex: 1,
    minWidth: 100,
  },
  markAllButtonLabel: {
    fontSize: 12,
    color: '#ffffff',
  },

  // Responsive adjustments for small screens
  ...Dimensions.get('window').width < 360 ? {
    summaryStatItem: {
      width: '50%',
      marginBottom: 12,
    },
    buttonContainer: {
      flexDirection: 'column',
    },
    actionButton: {
      marginBottom: 8,
    },
    markButton: {
      width: '100%',
    },
    // Responsive adjustments for table
    photoColumn: {
      width: 60,
    },
    rollColumn: {
      width: 80,
    },
    nameColumn: {
      width: 150,
    },
    statusColumn: {
      width: 150,
    },
    remarksColumn: {
      width: 200,
    }
  } : {}
});

export default AttendanceManagement;
