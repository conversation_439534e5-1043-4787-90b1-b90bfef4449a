import { initializeApp } from 'firebase/app';
import { initializeAuth, getReactNativePersistence } from 'firebase/auth';
import { getFirestore, collection, doc, addDoc, getDoc, getDocs, updateDoc, deleteDoc, query, where, orderBy, limit } from 'firebase/firestore';
import { getStorage } from 'firebase/storage';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Import environment variables directly
import {
  REACT_APP_FIREBASE_API_KEY,
  REACT_APP_FIREBASE_AUTH_DOMAIN,
  REACT_APP_FIREBASE_PROJECT_ID,
  REACT_APP_FIREBASE_STORAGE_BUCKET,
  REACT_APP_FIREBASE_MESSAGING_SENDER_ID,
  REACT_APP_FIREBASE_APP_ID
} from '@env';

// Define Firebase configuration
const firebaseConfig = {
  apiKey: REACT_APP_FIREBASE_API_KEY || "AIzaSyBHkGAhm80xBIECyaHbZ_DbeNzu3jnVuDg",
  authDomain: REACT_APP_FIREBASE_AUTH_DOMAIN || "schoolmn-16cbc.firebaseapp.com",
  projectId: REACT_APP_FIREBASE_PROJECT_ID || "schoolmn-16cbc",
  storageBucket: REACT_APP_FIREBASE_STORAGE_BUCKET || "schoolmn-16cbc.appspot.com",
  messagingSenderId: REACT_APP_FIREBASE_MESSAGING_SENDER_ID || "999485613068",
  appId: REACT_APP_FIREBASE_APP_ID || "1:999485613068:web:e9c0c3e0a4c7f4e4c8b8b8"
};

// Log Firebase configuration status
const usingEnvVars = REACT_APP_FIREBASE_API_KEY && REACT_APP_FIREBASE_PROJECT_ID;
console.log(`Firebase config: Using ${usingEnvVars ? 'environment variables' : 'fallback values'}`);

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Auth with AsyncStorage persistence
const auth = initializeAuth(app, {
  persistence: getReactNativePersistence(AsyncStorage)
});

// Initialize Firestore
const db = getFirestore(app);

// Initialize Storage
const storage = getStorage(app);

console.log('Firebase initialized successfully');
console.log('Current config:', {
  authDomain: firebaseConfig.authDomain,
  projectId: firebaseConfig.projectId
});

export {
  auth,
  db,
  storage,
  app as firebaseApp,
  // Export Firestore functions
  collection,
  doc,
  addDoc,
  getDoc,
  getDocs,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit
};
