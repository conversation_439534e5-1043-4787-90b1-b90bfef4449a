const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Function to run a script
const runScript = (scriptPath, args = '') => {
  console.log(`Running script: ${scriptPath} ${args}`);
  try {
    // Use __dirname to get the current directory and resolve the path correctly
    const fullPath = path.resolve(__dirname, scriptPath);
    execSync(`node "${fullPath}" ${args}`, { stdio: 'inherit' });
    console.log(`Successfully executed: ${scriptPath}`);
    return true;
  } catch (error) {
    console.error(`Failed to execute ${scriptPath}:`, error.message);
    return false;
  }
};

// Check if scripts exist
const deployRulesPath = path.join(__dirname, 'deploy-firestore-rules.js');
const updateDataPath = path.join(__dirname, 'update-timetable-data.js');
const testTrackingPath = path.join(__dirname, 'test-class-tracking.js');

const deployRulesExists = fs.existsSync(deployRulesPath);
const updateDataExists = fs.existsSync(updateDataPath);
const testTrackingExists = fs.existsSync(testTrackingPath);

if (!deployRulesExists) {
  console.error(`Error: ${deployRulesPath} does not exist.`);
}

if (!updateDataExists) {
  console.error(`Error: ${updateDataPath} does not exist.`);
}

if (!testTrackingExists) {
  console.error(`Error: ${testTrackingPath} does not exist.`);
}

if (!deployRulesExists || !updateDataExists || !testTrackingExists) {
  console.error('Please ensure all scripts exist in the correct location.');
  process.exit(1);
}

// Main function
const main = async () => {
  console.log('=== Class Tracking Setup Tool ===');
  console.log('This tool will help you set up and test the class tracking features.');
  console.log('');

  // Ask if user wants to deploy Firestore rules
  rl.question('Do you want to deploy Firestore security rules? (y/n): ', (deployAnswer) => {
    if (deployAnswer.toLowerCase() === 'y') {
      const deploySuccess = runScript(deployRulesPath);
      if (!deploySuccess) {
        console.error('Failed to deploy Firestore security rules.');
      }
    }

    // Ask if user wants to update timetable data
    rl.question('Do you want to update timetable data? (y/n): ', (updateAnswer) => {
      if (updateAnswer.toLowerCase() === 'y') {
        const updateSuccess = runScript(updateDataPath);
        if (!updateSuccess) {
          console.error('Failed to update timetable data.');
        }
      }

      // Ask if user wants to run tests
      rl.question('Do you want to run tests for class tracking features? (y/n): ', (testAnswer) => {
        if (testAnswer.toLowerCase() === 'y') {
          rl.question('Which user role do you want to test? (teacher/student/parent): ', (role) => {
            if (!['teacher', 'student', 'parent'].includes(role.toLowerCase())) {
              console.error('Invalid role. Please choose teacher, student, or parent.');
              rl.close();
              return;
            }

            const testSuccess = runScript(testTrackingPath, role);
            if (!testSuccess) {
              console.error('Failed to run class tracking tests.');
            }

            console.log('');
            console.log('Class tracking setup and testing process completed.');
            rl.close();
          });
        } else {
          console.log('');
          console.log('Class tracking setup process completed.');
          rl.close();
        }
      });
    });
  });
};

// Run the main function
main().catch(error => {
  console.error('An error occurred:', error);
  process.exit(1);
});
