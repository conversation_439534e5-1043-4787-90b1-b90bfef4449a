import React, { useState, useEffect } from 'react';
import { View, StyleSheet } from 'react-native';
import { Text, Button, ActivityIndicator, Surface } from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { db } from '../../config/firebase';
import { collection, query, where, getDocs, updateDoc, doc, serverTimestamp, addDoc, getDoc } from 'firebase/firestore';

/**
 * A component to handle email verification directly in the app
 * This is used when email delivery is not reliable
 */
const EmailVerificationHandler = ({ email, onVerificationComplete }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [verificationInfo, setVerificationInfo] = useState(null);
  const [verified, setVerified] = useState(false);

  // Check if there's a verification record for this email
  useEffect(() => {
    const checkVerification = async () => {
      if (!email) return;

      try {
        setLoading(true);
        setError(null);

        const verificationQuery = query(
          collection(db, 'app_verifications'),
          where('email', '==', email),
          where('verified', '==', false)
        );

        const querySnapshot = await getDocs(verificationQuery);

        if (!querySnapshot.empty) {
          // Get the most recent verification
          const verifications = querySnapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data()
          }));

          // Sort by creation date (newest first)
          verifications.sort((a, b) => {
            return b.createdAt.toDate() - a.createdAt.toDate();
          });

          setVerificationInfo(verifications[0]);
        } else {
          // No verification record found, create one
          console.log(`No verification record found for ${email}, creating one...`);

          // Generate a simple token
          const token = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);

          // Store verification info
          const docRef = await addDoc(collection(db, 'app_verifications'), {
            email,
            token,
            type: 'user',
            createdAt: new Date(),
            expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
            verified: false
          });

          // Get the created document
          const docSnap = await getDoc(doc(db, 'app_verifications', docRef.id));

          if (docSnap.exists()) {
            setVerificationInfo({
              id: docRef.id,
              ...docSnap.data()
            });
          }
        }
      } catch (error) {
        console.error('Error checking verification:', error);
        setError('Failed to check verification status');
      } finally {
        setLoading(false);
      }
    };

    checkVerification();
  }, [email]);

  // Handle verification
  const handleVerify = async () => {
    if (!verificationInfo) return;

    try {
      setLoading(true);
      setError(null);

      // Update the verification record
      await updateDoc(doc(db, 'app_verifications', verificationInfo.id), {
        verified: true,
        verifiedAt: serverTimestamp()
      });

      // Update the user document
      const usersQuery = query(
        collection(db, 'users'),
        where('email', '==', email)
      );

      const userSnapshot = await getDocs(usersQuery);

      if (!userSnapshot.empty) {
        const userDoc = userSnapshot.docs[0];
        await updateDoc(doc(db, 'users', userDoc.id), {
          emailVerified: true,
          updatedAt: serverTimestamp()
        });
      }

      setVerified(true);

      // Call the callback
      if (onVerificationComplete) {
        onVerificationComplete(true);
      }
    } catch (error) {
      console.error('Error verifying email:', error);
      setError('Failed to verify email');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <Surface style={styles.container}>
        <ActivityIndicator size="small" />
        <Text style={styles.loadingText}>
          Checking verification status...
        </Text>
      </Surface>
    );
  }

  if (error) {
    return (
      <Surface style={styles.container}>
        <MaterialCommunityIcons name="alert-circle" size={24} color="#F44336" />
        <Text style={styles.errorText}>{error}</Text>
      </Surface>
    );
  }

  if (verified) {
    return (
      <Surface style={styles.container}>
        <MaterialCommunityIcons name="check-circle" size={24} color="#4CAF50" />
        <Text style={styles.successText}>
          Email verified successfully!
        </Text>
      </Surface>
    );
  }

  if (verificationInfo) {
    return (
      <Surface style={styles.container}>
        <Text style={styles.infoText}>
          Verification is pending. Click the button below to verify your email directly in the app:
        </Text>
        <Button
          mode="contained"
          onPress={handleVerify}
          style={styles.button}
          icon="check-circle"
        >
          Verify Email Now
        </Button>
      </Surface>
    );
  }

  return (
    <Surface style={styles.container}>
      <Text style={styles.infoText}>
        No pending verification found for this email.
      </Text>
      <Button
        mode="contained"
        onPress={() => {
          // Create a verification record and then verify it
          const createAndVerify = async () => {
            try {
              setLoading(true);
              // Generate a simple token
              const token = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);

              // Store verification info
              const docRef = await addDoc(collection(db, 'app_verifications'), {
                email,
                token,
                type: 'user',
                createdAt: new Date(),
                expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
                verified: true // Directly mark as verified
              });

              console.log(`Direct verification created for ${email}`);

              // Update user document if it exists
              const usersQuery = query(
                collection(db, 'users'),
                where('email', '==', email)
              );

              const userSnapshot = await getDocs(usersQuery);

              if (!userSnapshot.empty) {
                const userDoc = userSnapshot.docs[0];
                await updateDoc(doc(db, 'users', userDoc.id), {
                  emailVerified: true,
                  updatedAt: serverTimestamp()
                });
              }

              setVerified(true);

              // Call the callback
              if (onVerificationComplete) {
                onVerificationComplete(true);
              }
            } catch (error) {
              console.error('Error creating verification:', error);
              setError('Failed to verify email');
            } finally {
              setLoading(false);
            }
          };

          createAndVerify();
        }}
        style={[styles.button, { backgroundColor: '#4CAF50', marginTop: 16 }]}
        icon="check-circle"
      >
        Verify Email Now
      </Button>
    </Surface>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
    borderRadius: 8,
    marginVertical: 8,
  },
  loadingText: {
    marginTop: 8,
    color: '#666',
  },
  errorText: {
    color: '#F44336',
    marginTop: 8,
  },
  successText: {
    color: '#4CAF50',
    marginTop: 8,
    fontWeight: 'bold',
  },
  infoText: {
    marginBottom: 16,
  },
  button: {
    marginTop: 8,
  },
});

export default EmailVerificationHandler;
