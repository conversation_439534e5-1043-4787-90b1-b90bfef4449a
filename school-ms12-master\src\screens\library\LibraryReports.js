import React, { useState, useEffect } from 'react';
import { View, ScrollView, StyleSheet } from 'react-native';
import { Card, Title, Text, List, Searchbar, DataTable, Portal, Modal } from 'react-native-paper';
import { db } from '../../config/firebase';
import { collection, query, getDocs, where, orderBy } from 'firebase/firestore';
import CustomButton from '../../components/common/CustomButton';
import { LineChart, PieChart } from 'react-native-chart-kit';
import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';
import { sanitizeChartData, sanitizeChartDatasets } from '../../utils/ChartUtils';

const LibraryReports = () => {
  const [reportType, setReportType] = useState('overview');
  const [reportData, setReportData] = useState(null);
  const [dateRange, setDateRange] = useState('week'); // week, month, year
  const [loading, setLoading] = useState(false);
  const [visible, setVisible] = useState(false);
  const [selectedReport, setSelectedReport] = useState(null);

  useEffect(() => {
    fetchReportData();
  }, [reportType, dateRange]);

  const fetchReportData = async () => {
    setLoading(true);
    try {
      switch (reportType) {
        case 'overview':
          await fetchOverviewData();
          break;
        case 'borrowings':
          await fetchBorrowingsData();
          break;
        case 'overdue':
          await fetchOverdueData();
          break;
        case 'popular':
          await fetchPopularBooksData();
          break;
      }
    } catch (error) {
      console.error('Error fetching report data:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchOverviewData = async () => {
    const booksRef = collection(db, 'books');
    const borrowingsRef = collection(db, 'bookBorrowings');
    const categoriesRef = collection(db, 'bookCategories');

    const [booksSnap, borrowingsSnap, categoriesSnap] = await Promise.all([
      getDocs(booksRef),
      getDocs(borrowingsRef),
      getDocs(categoriesRef),
    ]);

    const totalBooks = booksSnap.size;
    const totalBorrowings = borrowingsSnap.size;
    const totalCategories = categoriesSnap.size;

    let availableBooks = 0;
    booksSnap.forEach(doc => {
      availableBooks += doc.data().availableQuantity;
    });

    setReportData({
      totalBooks,
      availableBooks,
      borrowedBooks: totalBooks - availableBooks,
      totalBorrowings,
      totalCategories,
    });
  };

  const fetchBorrowingsData = async () => {
    const startDate = getStartDate();
    const borrowingsRef = collection(db, 'bookBorrowings');
    const q = query(
      borrowingsRef,
      where('borrowDate', '>=', startDate.toISOString()),
      orderBy('borrowDate', 'desc')
    );

    const snapshot = await getDocs(q);
    const borrowings = [];
    snapshot.forEach(doc => {
      borrowings.push({ id: doc.id, ...doc.data() });
    });

    setReportData({ borrowings });
  };

  const fetchOverdueData = async () => {
    const borrowingsRef = collection(db, 'bookBorrowings');
    const now = new Date().toISOString();
    const q = query(
      borrowingsRef,
      where('status', '==', 'borrowed'),
      where('dueDate', '<', now)
    );

    const snapshot = await getDocs(q);
    const overdueBooks = [];
    snapshot.forEach(doc => {
      overdueBooks.push({ id: doc.id, ...doc.data() });
    });

    setReportData({ overdueBooks });
  };

  const fetchPopularBooksData = async () => {
    const borrowingsRef = collection(db, 'bookBorrowings');
    const startDate = getStartDate();
    const q = query(
      borrowingsRef,
      where('borrowDate', '>=', startDate.toISOString())
    );

    const snapshot = await getDocs(q);
    const bookCounts = {};
    snapshot.forEach(doc => {
      const bookId = doc.data().bookId;
      bookCounts[bookId] = (bookCounts[bookId] || 0) + 1;
    });

    // Get book details
    const booksRef = collection(db, 'books');
    const booksSnapshot = await getDocs(booksRef);
    const books = {};
    booksSnapshot.forEach(doc => {
      books[doc.id] = doc.data();
    });

    const popularBooks = Object.entries(bookCounts)
      .map(([bookId, count]) => ({
        bookId,
        title: books[bookId]?.title,
        count,
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    setReportData({ popularBooks });
  };

  const getStartDate = () => {
    const now = new Date();
    switch (dateRange) {
      case 'week':
        return new Date(now.setDate(now.getDate() - 7));
      case 'month':
        return new Date(now.setMonth(now.getMonth() - 1));
      case 'year':
        return new Date(now.setFullYear(now.getFullYear() - 1));
    }
  };

  const exportToCSV = async () => {
    try {
      let csvContent = '';
      switch (reportType) {
        case 'overview':
          csvContent = `Total Books,Available Books,Borrowed Books,Total Borrowings,Total Categories\n${reportData.totalBooks},${reportData.availableBooks},${reportData.borrowedBooks},${reportData.totalBorrowings},${reportData.totalCategories}`;
          break;
        case 'borrowings':
          csvContent = 'User ID,Book ID,Borrow Date,Due Date,Status\n' +
            reportData.borrowings.map(b => 
              `${b.userId},${b.bookId},${b.borrowDate},${b.dueDate},${b.status}`
            ).join('\n');
          break;
        // Add other report types...
      }

      const fileUri = `${FileSystem.documentDirectory}library_report_${reportType}_${Date.now()}.csv`;
      await FileSystem.writeAsStringAsync(fileUri, csvContent);
      await Sharing.shareAsync(fileUri);
    } catch (error) {
      console.error('Error exporting report:', error);
    }
  };

  const renderOverviewReport = () => (
    <Card style={styles.card}>
      <Card.Content>
        <Title>Library Overview</Title>
        <View style={styles.statsGrid}>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{reportData.totalBooks}</Text>
            <Text style={styles.statLabel}>Total Books</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{reportData.availableBooks}</Text>
            <Text style={styles.statLabel}>Available</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{reportData.borrowedBooks}</Text>
            <Text style={styles.statLabel}>Borrowed</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{reportData.totalCategories}</Text>
            <Text style={styles.statLabel}>Categories</Text>
          </View>
        </View>

        <PieChart
          data={[
            {
              name: 'Available',
              population: reportData.availableBooks,
              color: '#4CAF50',
              legendFontColor: '#7F7F7F',
            .map(item => ({
        ...item,
        population: isNaN(item.population) || item.population === undefined || 
                    item.population === null || item.population === Infinity || 
                    item.population === -Infinity ? 0 : item.population
      }))},
            {
              name: 'Borrowed',
              population: reportData.borrowedBooks,
              color: '#2196F3',
              legendFontColor: '#7F7F7F',
            },
          ]}
          width={300}
          height={200}
          chartConfig={{
            backgroundColor: '#FFFFFF',
            backgroundGradientFrom: '#FFFFFF',
            backgroundGradientTo: '#FFFFFF',
            color: (opacity = 1) => `rgba(0, 0, 0, ${opacity})`,
          }}
          accessor="population"
          backgroundColor="transparent"
          paddingLeft="15"
        />
      </Card.Content>
    </Card>
  );

  const renderBorrowingsReport = () => (
    <Card style={styles.card}>
      <Card.Content>
        <Title>Borrowing History</Title>
        <DataTable>
          <DataTable.Header>
            <DataTable.Title>User</DataTable.Title>
            <DataTable.Title>Book</DataTable.Title>
            <DataTable.Title>Date</DataTable.Title>
            <DataTable.Title>Status</DataTable.Title>
          </DataTable.Header>

          {reportData.borrowings.map((borrowing) => (
            <DataTable.Row key={borrowing.id}>
              <DataTable.Cell>{borrowing.userId}</DataTable.Cell>
              <DataTable.Cell>{borrowing.bookId}</DataTable.Cell>
              <DataTable.Cell>
                {new Date(borrowing.borrowDate).toLocaleDateString()}
              </DataTable.Cell>
              <DataTable.Cell>{borrowing.status}</DataTable.Cell>
            </DataTable.Row>
          ))}
        </DataTable>
      </Card.Content>
    </Card>
  );

  const renderOverdueReport = () => (
    <Card style={styles.card}>
      <Card.Content>
        <Title>Overdue Books</Title>
        <DataTable>
          <DataTable.Header>
            <DataTable.Title>Book</DataTable.Title>
            <DataTable.Title>User</DataTable.Title>
            <DataTable.Title>Due Date</DataTable.Title>
            <DataTable.Title>Days Overdue</DataTable.Title>
          </DataTable.Header>

          {reportData.overdueBooks.map((book) => (
            <DataTable.Row key={book.id}>
              <DataTable.Cell>{book.bookId}</DataTable.Cell>
              <DataTable.Cell>{book.userId}</DataTable.Cell>
              <DataTable.Cell>
                {new Date(book.dueDate).toLocaleDateString()}
              </DataTable.Cell>
              <DataTable.Cell>
                {Math.ceil((new Date() - new Date(book.dueDate)) / (1000 * 60 * 60 * 24))}
              </DataTable.Cell>
            </DataTable.Row>
          ))}
        </DataTable>
      </Card.Content>
    </Card>
  );

  const renderPopularBooksReport = () => (
    <Card style={styles.card}>
      <Card.Content>
        <Title>Popular Books</Title>
        <DataTable>
          <DataTable.Header>
            <DataTable.Title>Title</DataTable.Title>
            <DataTable.Title numeric>Times Borrowed</DataTable.Title>
          </DataTable.Header>

          {reportData.popularBooks.map((book) => (
            <DataTable.Row key={book.bookId}>
              <DataTable.Cell>{book.title}</DataTable.Cell>
              <DataTable.Cell numeric>{book.count}</DataTable.Cell>
            </DataTable.Row>
          ))}
        </DataTable>
      </Card.Content>
    </Card>
  );

  return (
    <View style={styles.container}>
      <View style={styles.filterContainer}>
        <List.Section>
          <List.Accordion
            title="Report Type"
            left={props => <List.Icon {...props} icon="file-document" />}
          >
            <List.Item
              title="Overview"
              onPress={() => setReportType('overview')}
            />
            <List.Item
              title="Borrowing History"
              onPress={() => setReportType('borrowings')}
            />
            <List.Item
              title="Overdue Books"
              onPress={() => setReportType('overdue')}
            />
            <List.Item
              title="Popular Books"
              onPress={() => setReportType('popular')}
            />
          </List.Accordion>

          <List.Accordion
            title="Date Range"
            left={props => <List.Icon {...props} icon="calendar" />}
          >
            <List.Item
              title="Last Week"
              onPress={() => setDateRange('week')}
            />
            <List.Item
              title="Last Month"
              onPress={() => setDateRange('month')}
            />
            <List.Item
              title="Last Year"
              onPress={() => setDateRange('year')}
            />
          </List.Accordion>
        </List.Section>

        <CustomButton
          mode="contained"
          onPress={exportToCSV}
          style={styles.exportButton}
        >
          Export Report
        </CustomButton>
      </View>

      <ScrollView>
        {loading ? (
          <ActivityIndicator size="large" style={styles.loader} />
        ) : (
          reportData && (
            <>
              {reportType === 'overview' && renderOverviewReport()}
              {reportType === 'borrowings' && renderBorrowingsReport()}
              {reportType === 'overdue' && renderOverdueReport()}
              {reportType === 'popular' && renderPopularBooksReport()}
            </>
          )
        )}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  filterContainer: {
    backgroundColor: 'white',
    padding: 10,
    elevation: 2,
  },
  card: {
    margin: 10,
  },
  exportButton: {
    margin: 10,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginVertical: 10,
  },
  statItem: {
    width: '48%',
    backgroundColor: '#f5f5f5',
    padding: 15,
    borderRadius: 8,
    marginBottom: 10,
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  statLabel: {
    fontSize: 14,
    color: '#666',
    marginTop: 5,
  },
  loader: {
    marginTop: 20,
  },
});

export default LibraryReports;
