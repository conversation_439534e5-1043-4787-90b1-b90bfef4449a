import React, { useState, useEffect } from 'react';
import { View, ScrollView, StyleSheet, Image } from 'react-native';
import { Card, Title, Text, Portal, Modal, List, ActivityIndicator, Divider } from 'react-native-paper';
import { db } from '../../config/firebase';
import { doc, getDoc, updateDoc } from 'firebase/firestore';
import { useAuth } from '../../context/AuthContext';
import CustomButton from '../../components/common/CustomButton';
import CustomInput from '../../components/common/CustomInput';
import ChangePasswordModal from '../../components/common/ChangePasswordModal';
import { useLanguage } from '../../context/LanguageContext';
import * as ImagePicker from 'expo-image-picker';
import ProfileImageService from '../../services/ProfileImageService';
import CloudinaryAvatar from '../../components/common/CloudinaryAvatar';

const ProfileManagement = () => {
  const { user, updateUserProfile } = useAuth();
  const { translate, getTextStyle } = useLanguage();
  const [profile, setProfile] = useState(null);
  const [visible, setVisible] = useState(false);
  const [passwordModalVisible, setPasswordModalVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [imageUri, setImageUri] = useState(null);

  const [formData, setFormData] = useState({
    displayName: '',
    email: '',
    phone: '',
    address: '',
    qualification: '',
    specialization: '',
    experience: '',
    bio: '',
  });

  useEffect(() => {
    fetchProfile();
  }, []);

  const fetchProfile = async () => {
    try {
      setLoading(true);
      const profileRef = doc(db, 'teachers', user.uid);
      const docSnap = await getDoc(profileRef);

      if (docSnap.exists()) {
        const profileData = docSnap.data();
        setProfile(profileData);
        setFormData({
          displayName: profileData.displayName || '',
          email: profileData.email || '',
          phone: profileData.phone || '',
          address: profileData.address || '',
          qualification: profileData.qualification || '',
          specialization: profileData.specialization || '',
          experience: profileData.experience || '',
          bio: profileData.bio || '',
        });
        setImageUri(profileData.photoURL);
      } else {
        // Initialize with user's auth data if profile doesn't exist
        setProfile({
          displayName: user.displayName || '',
          email: user.email || '',
          photoURL: user.photoURL || '',
        });
        setFormData({
          displayName: user.displayName || '',
          email: user.email || '',
          phone: '',
          address: '',
          qualification: '',
          specialization: '',
          experience: '',
          bio: '',
        });
        setImageUri(user.photoURL);
      }
    } catch (error) {
      console.error('Error fetching profile:', error);
    } finally {
      setLoading(false);
    }
  };

  const pickImage = async () => {
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [1, 1],
      quality: 1,
    });

    if (!result.canceled) {
      setImageUri(result.assets[0].uri);
    }
  };

  const handleUpdateProfile = async () => {
    try {
      setLoading(true);

      // Handle profile image upload if changed
      let photoURL = imageUri;
      if (imageUri && !imageUri.startsWith('http')) {
        try {
          // Upload and update profile image using the ProfileImageService
          photoURL = await ProfileImageService.uploadAndUpdateProfileImage(
            imageUri,
            user,
            'teacher'
          );
        } catch (imageError) {
          console.error('Error uploading profile image:', imageError);
          // Continue with other updates even if image upload fails
        }
      }

      // Update profile in Firestore
      const profileRef = doc(db, 'teachers', user.uid);
      await updateDoc(profileRef, {
        ...formData,
        photoURL,
        updatedAt: new Date().toISOString(),
      });

      // Update auth profile
      await updateUserProfile({
        displayName: formData.displayName,
        photoURL,
      });

      setVisible(false);
      await fetchProfile();
    } catch (error) {
      console.error('Error updating profile:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <View style={[styles.container, styles.centerContent]}>
        <ActivityIndicator size="large" />
      </View>
    );
  }

  if (!profile) {
    return (
      <View style={[styles.container, styles.centerContent]}>
        <Text>No profile data found</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <Card style={styles.profileCard}>
        <View style={styles.avatarContainer}>
          <CloudinaryAvatar
            source={imageUri}
            label={profile.displayName?.substring(0, 2) || 'TC'}
            size={120}
            backgroundColor="#26a69a"
          />
          <Title style={styles.name}>{profile.displayName}</Title>
          <Text style={styles.role}>{translate('roles.teacher') || 'Teacher'}</Text>
        </View>

        <Card.Content>
          <List.Section>
            <List.Item
              title="Email"
              description={profile.email}
              left={props => <List.Icon {...props} icon="email" />}
            />
            <List.Item
              title="Phone"
              description={profile.phone}
              left={props => <List.Icon {...props} icon="phone" />}
            />
            <List.Item
              title="Address"
              description={profile.address}
              left={props => <List.Icon {...props} icon="map-marker" />}
            />
            <List.Item
              title="Qualification"
              description={profile.qualification}
              left={props => <List.Icon {...props} icon="school" />}
            />
            <List.Item
              title="Specialization"
              description={profile.specialization}
              left={props => <List.Icon {...props} icon="book" />}
            />
            <List.Item
              title="Experience"
              description={`${profile.experience} years`}
              left={props => <List.Icon {...props} icon="briefcase" />}
            />
          </List.Section>

          <Card style={styles.bioCard}>
            <Card.Content>
              <Title>Bio</Title>
              <Text>{profile.bio}</Text>
            </Card.Content>
          </Card>

          <CustomButton
            mode="contained"
            onPress={() => setVisible(true)}
            style={styles.editButton}
          >
            {translate('profileManagement.editProfile') || 'Edit Profile'}
          </CustomButton>

          <CustomButton
            mode="outlined"
            onPress={() => setPasswordModalVisible(true)}
            style={styles.passwordButton}
            icon="lock-reset"
          >
            {translate('profileManagement.changePassword') || 'Change Password'}
          </CustomButton>
        </Card.Content>
      </Card>

      {/* Change Password Modal */}
      <ChangePasswordModal
        visible={passwordModalVisible}
        onDismiss={() => setPasswordModalVisible(false)}
        onSuccess={() => setPasswordModalVisible(false)}
      />

      <Portal>
        <Modal
          visible={visible}
          onDismiss={() => setVisible(false)}
          contentContainerStyle={styles.modalContent}
        >
          <ScrollView>
            <Title>Edit Profile</Title>

            <View style={styles.imagePickerContainer}>
              <CloudinaryAvatar
                source={imageUri}
                label={profile.displayName?.substring(0, 2) || 'TC'}
                size={100}
                backgroundColor="#26a69a"
              />
              <CustomButton
                mode="outlined"
                onPress={pickImage}
                style={styles.imagePickerButton}
              >
                {translate('profileManagement.changePhoto') || 'Change Photo'}
              </CustomButton>
            </View>

            <CustomInput
              label="Display Name"
              value={formData.displayName}
              onChangeText={(text) => setFormData({ ...formData, displayName: text })}
            />

            <CustomInput
              label="Email"
              value={formData.email}
              onChangeText={(text) => setFormData({ ...formData, email: text })}
              keyboardType="email-address"
            />

            <CustomInput
              label="Phone"
              value={formData.phone}
              onChangeText={(text) => setFormData({ ...formData, phone: text })}
              keyboardType="phone-pad"
            />

            <CustomInput
              label="Address"
              value={formData.address}
              onChangeText={(text) => setFormData({ ...formData, address: text })}
              multiline
              numberOfLines={2}
            />

            <CustomInput
              label="Qualification"
              value={formData.qualification}
              onChangeText={(text) => setFormData({ ...formData, qualification: text })}
            />

            <CustomInput
              label="Specialization"
              value={formData.specialization}
              onChangeText={(text) => setFormData({ ...formData, specialization: text })}
            />

            <CustomInput
              label="Experience (years)"
              value={formData.experience}
              onChangeText={(text) => setFormData({ ...formData, experience: text })}
              keyboardType="numeric"
            />

            <CustomInput
              label="Bio"
              value={formData.bio}
              onChangeText={(text) => setFormData({ ...formData, bio: text })}
              multiline
              numberOfLines={4}
            />

            <View style={styles.modalButtons}>
              <CustomButton
                mode="contained"
                onPress={handleUpdateProfile}
                loading={loading}
              >
                Save Changes
              </CustomButton>

              <CustomButton
                mode="outlined"
                onPress={() => setVisible(false)}
                style={styles.cancelButton}
              >
                Cancel
              </CustomButton>
            </View>
          </ScrollView>
        </Modal>
      </Portal>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  profileCard: {
    margin: 10,
  },
  avatarContainer: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  name: {
    marginTop: 10,
    fontSize: 24,
  },
  role: {
    color: '#666',
    marginTop: 5,
  },
  bioCard: {
    marginTop: 20,
    backgroundColor: '#f9f9f9',
  },
  editButton: {
    marginTop: 20,
  },
  passwordButton: {
    marginTop: 10,
  },
  modalContent: {
    backgroundColor: 'white',
    padding: 20,
    margin: 20,
    borderRadius: 10,
    maxHeight: '80%',
  },
  imagePickerContainer: {
    alignItems: 'center',
    marginVertical: 20,
  },
  imagePickerButton: {
    marginTop: 10,
  },
  modalButtons: {
    marginTop: 20,
  },
  cancelButton: {
    marginTop: 10,
  },
});

export default ProfileManagement;
