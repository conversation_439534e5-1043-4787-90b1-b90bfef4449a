import React, { useState, useEffect, useRef, useCallback } from 'react';
import { View, ScrollView, StyleSheet, Dimensions, TouchableOpacity, Animated, StatusBar, SafeAreaView, Alert, Linking, RefreshControl, Platform, ImageBackground } from 'react-native';
import { Card, Title, Paragraph, IconButton, Portal, Dialog, Button, Avatar, Badge, Divider, List, Chip, ActivityIndicator, Surface, Drawer, Text, FAB, Menu, Appbar, useTheme as usePaperTheme, ProgressBar, Provider as PaperProvider, Snackbar } from 'react-native-paper';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { signOut } from 'firebase/auth';
import { auth, db } from '../../config/firebase';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>hart } from 'react-native-chart-kit';
import { doc, getDoc, collection, query, where, getDocs, orderBy, limit, Timestamp, onSnapshot } from 'firebase/firestore';
import NotificationService from '../../services/NotificationService';
import MessagingFAB from '../../components/common/MessagingFAB';
import { useTranslation } from '../../hooks/useTranslation';
import * as Animatable from 'react-native-animatable';
import { LinearGradient } from 'expo-linear-gradient';
import LanguageSelector from '../../components/common/LanguageSelector';
import { MaterialCommunityIcons, Ionicons, FontAwesome5, MaterialIcons } from '@expo/vector-icons';
import DashboardClassWidget from '../../components/common/DashboardClassWidget';
import SidebarBackdrop from '../../components/common/SidebarBackdrop';
import { TouchableRipple } from 'react-native-paper';
import * as Haptics from 'expo-haptics';

const screenWidth = Dimensions.get('window').width;
const isTablet = screenWidth > 768;

const chartConfig = {
  backgroundColor: '#ffffff',
  backgroundGradientFrom: '#ffffff',
  backgroundGradientTo: '#ffffff',
  decimalPlaces: 0,
  color: (opacity = 1) => `rgba(33, 150, 243, ${opacity})`,
  labelColor: (opacity = 1) => `rgba(0, 0, 0, ${opacity})`,
  style: {
    borderRadius: 16
  }
};

// Custom theme for this screen
const theme = {
  colors: {
    primary: '#2196F3',
    accent: '#4CAF50',
    background: '#F5F7FA',
    surface: '#FFFFFF',
    text: '#333333',
    placeholder: '#9E9E9E',
    backdrop: 'rgba(0, 0, 0, 0.5)',
    notification: '#FF9800',
    error: '#F44336',
    success: '#4CAF50',
    warning: '#FF9800',
    info: '#2196F3'
  }
};

const TeacherDashboard = () => {
  const navigation = useNavigation();
  const { t, language, getTextStyle } = useTranslation();
  const paperTheme = usePaperTheme();
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;
  const drawerAnim = useRef(new Animated.Value(-300)).current;
  const backdropFadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.95)).current;

  // Dashboard quick access items
  const dashboardItems = [
    {
      translationKey: 'academic',
      items: [
        {
          icon: 'account-check',
          color: theme.colors.info,
          translationKey: 'attendance',
          screen: 'AttendanceManagement'
        },
        {
          icon: 'school',
          color: theme.colors.success,
          translationKey: 'grades',
          screen: 'GradeManagement'
        },
        {
          icon: 'book-open-variant',
          color: theme.colors.warning,
          translationKey: 'homework',
          screen: 'HomeworkManagement'
        },
        {
          icon: 'clipboard-text',
          color: theme.colors.error,
          translationKey: 'exams',
          screen: 'TeacherExamSchedule'
        },
        {
          icon: 'calendar-week',
          color: '#00BCD4',
          translationKey: 'schedule',
          screen: 'TeacherClassSchedule'
        }
      ]
    },
    {
      translationKey: 'communication',
      items: [
        {
          icon: 'message-text',
          color: '#2196F3',
          translationKey: 'messages',
          screen: 'TeacherMessaging'
        },
        {
          icon: 'bell',
          color: '#FF9800',
          translationKey: 'notifications',
          screen: 'NotificationCenter'
        },
        {
          icon: 'bullhorn',
          color: '#9C27B0',
          translationKey: 'announcements',
          screen: 'AnnouncementManagement'
        }
      ]
    },
    {
      translationKey: 'management',
      items: [
        {
          icon: 'google-classroom',
          color: '#9C27B0',
          translationKey: 'classes',
          screen: 'ClassManagement'
        },
        {
          icon: 'calendar',
          color: '#00BCD4',
          translationKey: 'calendar',
          screen: 'CalendarManagement'
        },
        {
          icon: 'account-group',
          color: '#4CAF50',
          translationKey: 'students',
          screen: 'StudentManagement'
        },
        {
          icon: 'account-tie',
          color: '#673AB7',
          translationKey: 'teachers',
          screen: 'TeacherDirectory'
        },
        {
          icon: 'file-document',
          color: '#795548',
          translationKey: 'reports',
          screen: 'ReportManagement'
        }
      ]
    }
  ];

  // Debug log for translation
  useEffect(() => {
    console.log('Current language:', language);
    console.log('Translation test:', t('teacher.dashboard.title'));
  }, [language, t]);

  // State variables
  const [logoutDialogVisible, setLogoutDialogVisible] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [teacherData, setTeacherData] = useState(null);
  const [notifications, setNotifications] = useState([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [examSchedules, setExamSchedules] = useState([]);
  const [hasRegistrationPermission, setHasRegistrationPermission] = useState(false);
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [activeSection, setActiveSection] = useState('dashboard');
  const [menuVisible, setMenuVisible] = useState(false);
  const [recentActivities, setRecentActivities] = useState([]);
  const [loadingActivities, setLoadingActivities] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [attendanceStats, setAttendanceStats] = useState({ present: 0, absent: 0, late: 0, excused: 0, total: 0 });
  const [gradingProgress, setGradingProgress] = useState(0);
  const [assignmentsStats, setAssignmentsStats] = useState({ completed: 0, pending: 0, total: 0 });
  const [dailySchedule, setDailySchedule] = useState([]);
  const [currentActiveClass, setCurrentActiveClass] = useState(null);
  const [teacherClasses, setTeacherClasses] = useState([]);
  const [teacherSubjects, setTeacherSubjects] = useState([]);
  const [showAllQuickAccess, setShowAllQuickAccess] = useState(false);

  // Interactive UI states
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [selectedQuickAction, setSelectedQuickAction] = useState(null);
  const [expandedSection, setExpandedSection] = useState(null);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [loadingSection, setLoadingSection] = useState(null);

  useEffect(() => {
    // Show loading state
    setIsLoading(true);

    // Fetch all data in parallel
    Promise.all([
      fetchTeacherData(),
      checkRegistrationPermission(),
      fetchNotifications(),
      fetchRecentActivities(),
      fetchExamSchedules(),
      fetchDailySchedule(),
      fetchTeacherClasses()
    ]).then(() => {
      // When all data is loaded, animate in the dashboard
      setIsLoading(false);

      // Enhanced animations with sequence and parallel
      Animated.sequence([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
        }),
        Animated.parallel([
          Animated.spring(slideAnim, {
            toValue: 0,
            speed: 20,
            bounciness: 5,
            useNativeDriver: true,
          }),
          Animated.spring(scaleAnim, {
            toValue: 1,
            tension: 50,
            friction: 7,
            useNativeDriver: true,
          }),
          Animated.timing(drawerAnim, {
            toValue: -300,
            duration: 0,
            useNativeDriver: true,
          })
        ])
      ]).start();

    }).catch(error => {
      console.error('Error loading dashboard data:', error);
      setIsLoading(false);
      setSnackbarMessage(t('common.errorLoadingData'));
      setSnackbarVisible(true);
    });

    // Set up timer to update current class
    const timer = setInterval(() => {
      updateCurrentClass();
    }, 60000); // Check every minute

    // Initial check
    updateCurrentClass();

    // Set up notification listeners
    const notificationListener = NotificationService.addNotificationListener(
      notification => {
        handleNotification(notification);
      }
    );

    const responseListener = NotificationService.addNotificationResponseListener(
      response => {
        handleNotificationResponse(response);
      }
    );

    // Clean up timer and listeners on unmount
    return () => {
      clearInterval(timer);
      NotificationService.removeNotificationListener(notificationListener);
      NotificationService.removeNotificationListener(responseListener);
    };
  }, [language]); // Add language dependency to reload when language changes

  // Fetch attendance stats, assignments, and grading progress after classes are loaded
  useEffect(() => {
    if (teacherClasses.length > 0) {
      fetchAttendanceStats();
      fetchAssignmentsStats();
      fetchGradingProgress();
    }
  }, [teacherClasses]);

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    setIsRefreshing(true);

    // Show feedback to user
    setSnackbarMessage(t('common.refreshingData'));
    setSnackbarVisible(true);

    try {
      await Promise.all([
        fetchTeacherData(),
        fetchNotifications(),
        fetchRecentActivities(),
        fetchExamSchedules(),
        fetchDailySchedule(),
        fetchTeacherClasses()
      ]);
      updateCurrentClass();

      // Fetch data that depends on teacher classes
      if (teacherClasses.length > 0) {
        await Promise.all([
          fetchAttendanceStats(),
          fetchAssignmentsStats(),
          fetchGradingProgress()
        ]);
      }

      // Show success message
      setSnackbarMessage(t('common.dataRefreshed'));
      setSnackbarVisible(true);

      // Animate the refresh completion
      Animated.sequence([
        Animated.timing(scaleAnim, {
          toValue: 0.98,
          duration: 100,
          useNativeDriver: true,
        }),
        Animated.spring(scaleAnim, {
          toValue: 1,
          tension: 50,
          friction: 3,
          useNativeDriver: true,
        })
      ]).start();

    } catch (error) {
      console.error('Error refreshing dashboard:', error);
      setSnackbarMessage(t('common.errorRefreshingData'));
      setSnackbarVisible(true);
    } finally {
      setRefreshing(false);
      setIsRefreshing(false);
    }
  }, [t]);

  const toggleDrawer = () => {
    if (drawerOpen) {
      // Close drawer with spring animation
      Animated.parallel([
        Animated.spring(drawerAnim, {
          toValue: -300,
          tension: 80,
          friction: 10,
          useNativeDriver: true,
        }),
        Animated.timing(backdropFadeAnim, {
          toValue: 0,
          duration: 250,
          useNativeDriver: true,
        })
      ]).start(() => setDrawerOpen(false));
    } else {
      // Open drawer with spring animation
      setDrawerOpen(true);

      Animated.parallel([
        Animated.spring(drawerAnim, {
          toValue: 0,
          tension: 50,
          friction: 10,
          useNativeDriver: true,
        }),
        Animated.timing(backdropFadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        })
      ]).start();

      // Add haptic feedback if available
      if (Platform.OS === 'ios' && 'impactAsync' in Haptics) {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      }
    }

    // Show feedback to user
    setSnackbarMessage(drawerOpen ? t('common.menuClosed') : t('common.menuOpened'));
    setSnackbarVisible(true);
  };

  const handleSectionPress = (section) => {
    // Add haptic feedback
    if (Platform.OS === 'ios' && 'impactAsync' in Haptics) {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }

    // Highlight the selected section
    setActiveSection(section);

    // Handle section navigation
    if (section === 'logout') {
      setLogoutDialogVisible(true);
    } else if (section !== 'dashboard') {
      // Show loading indicator for the section
      setLoadingSection(section);

      // Navigate to the selected screen with a small delay for better UX
      setTimeout(() => {
        navigation.navigate(section);
        setLoadingSection(null);
      }, 300);

      // Show feedback to user
      setSnackbarMessage(t('common.navigatingTo') + ' ' + t(`teacher.${section}.title`));
      setSnackbarVisible(true);
    } else {
      // If dashboard is selected, scroll to top
      if (section === 'dashboard') {
        // Provide feedback
        setSnackbarMessage(t('common.dashboardRefreshed'));
        setSnackbarVisible(true);
      }
    }

    // Close drawer on mobile
    if (Dimensions.get('window').width < 768) {
      toggleDrawer();
    }
  };

  const handleActivityPress = (activity) => {
    // Add haptic feedback
    if (Platform.OS === 'ios' && 'impactAsync' in Haptics) {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }

    // Set the selected activity for visual feedback
    setSelectedQuickAction(activity.id);

    // Show loading state
    setLoadingSection(activity.type);

    // Show feedback to user
    setSnackbarMessage(t('common.openingActivity') + ': ' + activity.title);
    setSnackbarVisible(true);

    // Navigate to different screens based on activity type with a small delay for better UX
    setTimeout(() => {
      switch (activity.type) {
        case 'attendance':
          navigation.navigate('AttendanceManagement', {
            screen: 'AttendanceDetails',
            params: { attendanceId: activity.id, attendanceData: activity.data }
          });
          break;
        case 'assignment':
          navigation.navigate('AssessmentManagement', {
            screen: 'AssignmentDetails',
            params: { assignmentId: activity.id, assignmentData: activity.data }
          });
          break;
        case 'exam':
          navigation.navigate('GradeManagement', {
            screen: 'ExamResults',
            params: { examId: activity.id, examData: activity.data }
          });
          break;
        case 'message':
          navigation.navigate('Communication', {
            screen: 'MessageDetails',
            params: { messageId: activity.id, messageData: activity.data }
          });
          break;
        default:
          // If type is unknown, just show an alert
          Alert.alert(t('teacher.dashboard.activityDetails'), t('teacher.dashboard.activitySelected', { title: activity.title }));
      }

      // Reset states
      setSelectedQuickAction(null);
      setLoadingSection(null);
    }, 300);
  };

  // Handle incoming notification while app is in foreground
  const handleNotification = (notification) => {
    try {
      console.log('Received notification:', notification);

      // Refresh notifications list
      fetchNotifications();

      // Handle specific notification types
      if (notification.request?.content?.data?.type === 'exam_schedule') {
        // Show an alert for exam schedule notification
        Alert.alert(
          notification.request.content.title || t('teacher.dashboard.newExamSchedule'),
          notification.request.content.body || t('teacher.dashboard.examScheduleNotification'),
          [
            {
              text: t('common.viewDetails'),
              onPress: async () => {
                const navAction = await NotificationService.handleExamScheduleNotification(notification.request.content);
                if (navAction) {
                  navigation.navigate(navAction.screen, navAction.params);
                }
              },
            },
            {
              text: t('common.dismiss'),
              style: 'cancel',
            },
          ]
        );
      }
    } catch (error) {
      console.error('Error handling notification:', error);
    }
  };

  // Handle notification response when user taps on a notification
  const handleNotificationResponse = async (response) => {
    try {
      console.log('Notification response:', response);

      const { notification } = response;
      const data = notification.request.content.data;

      console.log('Notification data:', data);

      // Mark the notification as read
      if (data.notificationId) {
        console.log('Marking notification as read:', data.notificationId);
        await NotificationService.markAsRead(data.notificationId);
      }

      // Handle specific notification types
      if (data.type === 'exam_schedule') {
        console.log('Handling exam schedule notification');
        const navAction = await NotificationService.handleExamScheduleNotification(notification.request.content);
        console.log('Navigation action:', navAction);

        if (navAction) {
          console.log(`Navigating to ${navAction.screen}`, navAction.params);
          navigation.navigate(navAction.screen, navAction.params);
        } else {
          console.log('No navigation action returned, navigating to TeacherExamSchedule');
          navigation.navigate('TeacherExamSchedule');
        }
      }

      // Refresh notifications
      fetchNotifications();
    } catch (error) {
      console.error('Error handling notification response:', error);
      // Fallback navigation
      try {
        navigation.navigate('TeacherExamSchedule');
      } catch (navError) {
        console.error('Error navigating to TeacherExamSchedule:', navError);
      }
    }
  };

  const fetchTeacherData = async () => {
    try {
      const teacherRef = doc(db, 'users', auth.currentUser.uid);
      const teacherDoc = await getDoc(teacherRef);

      if (teacherDoc.exists()) {
        setTeacherData(teacherDoc.data());
      }
    } catch (error) {
      console.error('Error fetching teacher data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const checkRegistrationPermission = async () => {
    try {
      const permissionsRef = collection(db, 'permissions');
      const q = query(
        permissionsRef,
        where('teacherId', '==', auth.currentUser.uid),
        where('type', '==', 'registration'),
        where('status', '==', 'approved')
      );
      const querySnapshot = await getDocs(q);
      setHasRegistrationPermission(!querySnapshot.empty);
    } catch (error) {
      console.error('Error checking registration permission:', error);
    }
  };

  const fetchNotifications = async () => {
    try {
      setIsLoading(true);
      const notificationData = await NotificationService.getNotifications(20);
      setNotifications(notificationData);
      const count = await NotificationService.getUnreadCount();
      setUnreadCount(count);
    } catch (error) {
      console.error('Error fetching notifications:', error);
      setNotifications([]);
      setUnreadCount(0);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchRecentActivities = async () => {
    try {
      setLoadingActivities(true);
      // Get teacher's user ID
      const teacherId = auth.currentUser.uid;

      // Query recent activities from Firestore
      const activitiesRef = collection(db, 'activities');
      const q = query(
        activitiesRef,
        where('teacherId', '==', teacherId),
        orderBy('timestamp', 'desc'),
        limit(10)
      );

      const querySnapshot = await getDocs(q);
      const activitiesData = [];

      querySnapshot.forEach((doc) => {
        activitiesData.push({
          id: doc.id,
          ...doc.data(),
          // Ensure activity has all required properties
          icon: doc.data().icon || getDefaultIcon(doc.data().type),
          color: doc.data().color || getDefaultColor(doc.data().type),
        });
      });

      setRecentActivities(activitiesData);
    } catch (error) {
      console.error('Error fetching recent activities:', error);
      setRecentActivities([]);
    } finally {
      setLoadingActivities(false);
    }
  };

  // Helper function to get default icon based on activity type
  const getDefaultIcon = (type) => {
    switch (type) {
      case 'attendance': return 'clipboard-check';
      case 'assignment': return 'file-document-edit';
      case 'exam': return 'school';
      case 'message': return 'message-text';
      default: return 'bell';
    }
  };

  // Helper function to get default color based on activity type
  const getDefaultColor = (type) => {
    switch (type) {
      case 'attendance': return '#2196F3';
      case 'assignment': return '#4CAF50';
      case 'exam': return '#9C27B0';
      case 'message': return '#FF9800';
      default: return '#757575';
    }
  };

  // Helper function to safely format dates with locale
  const safeFormatDate = (date, options, localeCode, defaultLocale = 'en-US') => {
    try {
      return date.toLocaleDateString(localeCode, options);
    } catch (error) {
      console.error(`Error formatting date with locale ${localeCode}:`, error);
      // Fallback to provided default locale
      try {
        return date.toLocaleDateString(defaultLocale, options);
      } catch (fallbackError) {
        console.error(`Error with fallback date formatting (${defaultLocale}):`, fallbackError);
        // Ultimate fallback
        try {
          return date.toLocaleDateString(undefined, options);
        } catch (finalError) {
          console.error('Error with final fallback date formatting:', finalError);
          return date.toString();
        }
      }
    }
  };

  // Format date helper function
  const formatDate = (timestamp) => {
    if (!timestamp) return '';

    let date;
    if (timestamp instanceof Timestamp) {
      // Convert Firebase Timestamp to JS Date
      date = timestamp.toDate();
    } else if (timestamp instanceof Date) {
      date = timestamp;
    } else if (typeof timestamp === 'string') {
      date = new Date(timestamp);
    } else {
      return '';
    }

    // Check if date is today
    const today = new Date();
    const isToday = date.getDate() === today.getDate() &&
                    date.getMonth() === today.getMonth() &&
                    date.getFullYear() === today.getFullYear();

    try {
      const langCode = language === 'am' ? 'am-ET' :
                       language === 'om' ? 'om-ET' : 'en-US';

      if (isToday) {
        // If today, return time only
        try {
          return date.toLocaleTimeString(langCode, { hour: '2-digit', minute: '2-digit' });
        } catch (timeError) {
          console.error(`Error formatting time with locale ${langCode}:`, timeError);
          return date.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' });
        }
      } else {
        // If not today, return date in appropriate format for the language
        return safeFormatDate(date, { day: 'numeric', month: 'short', year: 'numeric' }, langCode, 'en-US');
      }
    } catch (error) {
      console.error('Error formatting date with locale:', error);
      // Fallback to default format
      return date.toLocaleDateString();
    }
  };

  const fetchDailySchedule = async () => {
    try {
      // Get current day of week (0 = Sunday, 1 = Monday, etc.)
      const today = new Date().getDay();
      const dayNames = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
      const dayName = dayNames[today];

      // Get teacher's schedule
      const teacherId = auth.currentUser.uid;
      const scheduleRef = collection(db, 'schedules');
      const q = query(
        scheduleRef,
        where('teacherId', '==', teacherId),
        where('day', '==', dayName)
      );

      const querySnapshot = await getDocs(q);
      const scheduleData = [];

      querySnapshot.forEach((doc) => {
        scheduleData.push({
          id: doc.id,
          ...doc.data()
        });
      });

      // Sort by start time
      scheduleData.sort((a, b) => {
        const timeA = a.startTime.split(':').map(Number);
        const timeB = b.startTime.split(':').map(Number);

        if (timeA[0] !== timeB[0]) {
          return timeA[0] - timeB[0];
        }
        return timeA[1] - timeB[1];
      });

      setDailySchedule(scheduleData);
      updateCurrentClass();
    } catch (error) {
      console.error('Error fetching daily schedule:', error);
      setDailySchedule([]);
    }
  };

  const updateCurrentClass = () => {
    try {
      if (dailySchedule.length === 0) return;

      const now = new Date();
      const currentHour = now.getHours();
      const currentMinute = now.getMinutes();

      // Find the current active class
      const activeClass = dailySchedule.find(schedule => {
        const [startHour, startMinute] = schedule.startTime.split(':').map(Number);
        const [endHour, endMinute] = schedule.endTime.split(':').map(Number);

        // Convert to minutes for easier comparison
        const currentTimeInMinutes = currentHour * 60 + currentMinute;
        const startTimeInMinutes = startHour * 60 + startMinute;
        const endTimeInMinutes = endHour * 60 + endMinute;

        return currentTimeInMinutes >= startTimeInMinutes && currentTimeInMinutes <= endTimeInMinutes;
      });

      setCurrentActiveClass(activeClass ? activeClass.id : null);
    } catch (error) {
      console.error('Error updating current class:', error);
    }
  };

  // Fetch teacher's classes
  const fetchTeacherClasses = async () => {
    try {
      const teacherId = auth.currentUser.uid;
      const classesRef = collection(db, 'classes');
      const q = query(classesRef, where('teacherId', '==', teacherId));
      const querySnapshot = await getDocs(q);

      const classesData = [];
      querySnapshot.forEach((doc) => {
        classesData.push({ id: doc.id, ...doc.data() });
      });

      setTeacherClasses(classesData);

      // Extract subject IDs from classes
      const subjectIds = new Set();
      classesData.forEach(classData => {
        if (classData.subjects && Array.isArray(classData.subjects)) {
          classData.subjects.forEach(subject => {
            if (subject.id) subjectIds.add(subject.id);
          });
        }
      });

      setTeacherSubjects(Array.from(subjectIds));

      return classesData;
    } catch (error) {
      console.error('Error fetching teacher classes:', error);
      setTeacherClasses([]);
      return [];
    }
  };

  // Fetch attendance statistics
  const fetchAttendanceStats = async () => {
    try {
      const teacherId = auth.currentUser.uid;
      const classIds = teacherClasses.map(c => c.id);

      if (classIds.length === 0) {
        setAttendanceStats({ present: 0, absent: 0, late: 0, excused: 0, total: 0 });
        return;
      }

      // Get current date
      const today = new Date();
      const oneMonthAgo = new Date();
      oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);

      const formattedToday = today.toISOString().split('T')[0];
      const formattedOneMonthAgo = oneMonthAgo.toISOString().split('T')[0];

      // Query attendance records for the teacher's classes in the last month
      const attendanceRef = collection(db, 'attendance');

      // We need to query each class separately due to Firestore limitations
      let allAttendance = [];

      for (const classId of classIds) {
        const q = query(
          attendanceRef,
          where('classId', '==', classId),
          where('date', '>=', formattedOneMonthAgo),
          where('date', '<=', formattedToday)
        );

        const querySnapshot = await getDocs(q);
        querySnapshot.forEach(doc => {
          allAttendance.push(doc.data());
        });
      }

      // Calculate statistics
      const total = allAttendance.length;
      const present = allAttendance.filter(record => record.status === 'present').length;
      const absent = allAttendance.filter(record => record.status === 'absent').length;
      const late = allAttendance.filter(record => record.status === 'late').length;
      const excused = allAttendance.filter(record => record.status === 'excused').length;

      setAttendanceStats({
        present: total > 0 ? Math.round((present / total) * 100) : 0,
        absent: total > 0 ? Math.round((absent / total) * 100) : 0,
        late: total > 0 ? Math.round((late / total) * 100) : 0,
        excused: total > 0 ? Math.round((excused / total) * 100) : 0,
        total
      });
    } catch (error) {
      console.error('Error fetching attendance statistics:', error);
      setAttendanceStats({ present: 0, absent: 0, late: 0, excused: 0, total: 0 });
    }
  };

  // Fetch assignments statistics
  const fetchAssignmentsStats = async () => {
    try {
      const teacherId = auth.currentUser.uid;
      const classIds = teacherClasses.map(c => c.id);

      if (classIds.length === 0) {
        setAssignmentsStats({ completed: 0, pending: 0, total: 0 });
        return;
      }

      // Query homework/assignments for the teacher's classes
      const homeworkRef = collection(db, 'homework');

      let allHomework = [];

      for (const classId of classIds) {
        const q = query(
          homeworkRef,
          where('classId', '==', classId),
          where('teacherId', '==', teacherId)
        );

        const querySnapshot = await getDocs(q);
        querySnapshot.forEach(doc => {
          allHomework.push({ id: doc.id, ...doc.data() });
        });
      }

      // Calculate statistics
      const total = allHomework.length;
      const now = new Date();

      // Count completed (past due date) and pending assignments
      const completed = allHomework.filter(hw => {
        const dueDate = hw.dueDate instanceof Timestamp
          ? hw.dueDate.toDate()
          : new Date(hw.dueDate);
        return dueDate < now;
      }).length;

      const pending = total - completed;

      setAssignmentsStats({
        completed,
        pending,
        total
      });
    } catch (error) {
      console.error('Error fetching assignments statistics:', error);
      setAssignmentsStats({ completed: 0, pending: 0, total: 0 });
    }
  };

  // Fetch grading progress
  const fetchGradingProgress = async () => {
    try {
      const teacherId = auth.currentUser.uid;
      const classIds = teacherClasses.map(c => c.id);

      if (classIds.length === 0) {
        setGradingProgress(0);
        return;
      }

      // Query grades for the teacher's classes
      const gradesRef = collection(db, 'grades');

      let totalAssignments = 0;
      let gradedAssignments = 0;

      for (const classId of classIds) {
        // First, get all assignments for this class
        const assignmentsRef = collection(db, 'assignments');
        const assignmentsQuery = query(
          assignmentsRef,
          where('classId', '==', classId),
          where('teacherId', '==', teacherId)
        );

        const assignmentsSnapshot = await getDocs(assignmentsQuery);
        const assignments = [];
        assignmentsSnapshot.forEach(doc => {
          assignments.push({ id: doc.id, ...doc.data() });
        });

        totalAssignments += assignments.length;

        // Now check how many have grades recorded
        for (const assignment of assignments) {
          const gradesQuery = query(
            gradesRef,
            where('assignmentId', '==', assignment.id)
          );

          const gradesSnapshot = await getDocs(gradesQuery);
          if (!gradesSnapshot.empty) {
            gradedAssignments++;
          }
        }
      }

      // Calculate grading progress percentage
      const progress = totalAssignments > 0
        ? Math.round((gradedAssignments / totalAssignments) * 100)
        : 0;

      setGradingProgress(progress);
    } catch (error) {
      console.error('Error fetching grading progress:', error);
      setGradingProgress(0);
    }
  };

  const fetchExamSchedules = async () => {
    try {
      console.log('Fetching exam schedules for teacher');

      // Get teacher's subjects
      const teacherRef = doc(db, 'users', auth.currentUser.uid);
      const teacherDoc = await getDoc(teacherRef);

      if (!teacherDoc.exists()) {
        console.log('Teacher document not found');
        setExamSchedules([]);
        return;
      }

      const teacherData = teacherDoc.data();
      const teacherSubjects = teacherData?.subjects || [];

      if (teacherSubjects.length === 0) {
        console.log('Teacher has no subjects assigned');
        setExamSchedules([]);
        return;
      }

      console.log('Teacher subjects:', teacherSubjects);

      // Get exams for the teacher's subjects
      const examsRef = collection(db, 'exams');

      // First, get all published exams
      const publishedExamsQuery = query(
        examsRef,
        where('published', '==', true)
      );

      const publishedExamsSnapshot = await getDocs(publishedExamsQuery);

      // Filter locally for teacher's subjects to avoid compound query issues
      const examsData = [];
      publishedExamsSnapshot.forEach((doc) => {
        const examData = doc.data();
        if (teacherSubjects.includes(examData.subjectId)) {
          examsData.push({ id: doc.id, ...examData });
        }
      });

      // Sort by date
      examsData.sort((a, b) => {
        if (a.date < b.date) return -1;
        if (a.date > b.date) return 1;
        return 0;
      });

      console.log(`Found ${examsData.length} exam schedules for teacher`);
      setExamSchedules(examsData);
    } catch (error) {
      console.error('Error fetching exam schedules:', error);
      setExamSchedules([]);
    }
  };

  const handleLogout = async () => {
    try {
      await signOut(auth);
      navigation.reset({
        index: 0,
        routes: [{ name: 'Login' }],
      });
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  // We're using the dashboardItems defined earlier in the component



  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <StatusBar
          barStyle="dark-content"
          backgroundColor="#FFFFFF"
        />
        <Animatable.View animation="pulse" easing="ease-out" iterationCount="infinite">
          <Surface style={styles.loadingCard}>
            <LinearGradient
              colors={['#2196F3', '#e3f2fd']}
              style={styles.loadingGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
            >
              <Animatable.View animation="rotate" easing="linear" iterationCount="infinite" duration={2000}>
                <Avatar.Icon
                  size={80}
                  icon="school"
                  color="white"
                  style={{ backgroundColor: 'transparent' }}
                />
              </Animatable.View>
              <ActivityIndicator size="large" color="white" style={{ marginTop: 20 }} />
              <Animatable.Text
                animation="fadeIn"
                style={styles.loadingText}
              >
                {t('common.loading')}
              </Animatable.Text>
              <Animatable.Text
                animation="fadeIn"
                delay={500}
                style={styles.loadingSubText}
              >
                {t('teacher.dashboard.preparingDashboard')}
              </Animatable.Text>
            </LinearGradient>
          </Surface>
        </Animatable.View>
      </View>
    );
  }

  // Render app header
  const renderHeader = () => (
    <Animatable.View animation="fadeInDown" duration={800}>
      <Appbar.Header style={styles.appHeader}>
        <Appbar.Action
          icon="menu"
          onPress={toggleDrawer}
          color={theme.colors.primary}
          style={styles.menuButton}
        />
        <Appbar.Content
          title={t('teacher.dashboard.title')}
          titleStyle={[styles.appbarTitle, getTextStyle()]}
          subtitle={teacherData ? `${teacherData.firstName || ''} ${teacherData.lastName || ''}` : ''}
          subtitleStyle={styles.appbarSubtitle}
        />
        <View style={styles.appbarActions}>
          <Animatable.View animation="bounceIn" duration={1000} delay={300}>
            <Appbar.Action
              icon="bell"
              color={theme.colors.primary}
              onPress={() => {
                // Add haptic feedback
                if (Platform.OS === 'ios' && 'impactAsync' in Haptics) {
                  Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                }
                navigation.navigate('NotificationCenter');
              }}
              style={styles.notificationButton}
            />
            {unreadCount > 0 && (
              <Animatable.View animation="pulse" easing="ease-out" iterationCount="infinite">
                <Badge style={styles.notificationBadge}>{unreadCount}</Badge>
              </Animatable.View>
            )}
          </Animatable.View>
          <Animatable.View animation="bounceIn" duration={1000} delay={500}>
            <LanguageSelector />
          </Animatable.View>
        </View>
      </Appbar.Header>
    </Animatable.View>
  );

  // Render dashboard content
  const renderDashboard = () => (
    <ScrollView
      style={{ flex: 1 }}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      {/* Welcome Banner */}
      <Animatable.View animation="fadeIn" duration={800}>
        <TouchableRipple
          onPress={() => {
            // Add haptic feedback
            if (Platform.OS === 'ios' && 'impactAsync' in Haptics) {
              Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
            }
            // Show welcome message
            setSnackbarMessage(t('teacher.dashboard.welcomeMessage'));
            setSnackbarVisible(true);
          }}
          rippleColor="rgba(255, 255, 255, 0.2)"
          borderless
        >
          <Card style={styles.welcomeBanner}>
            <LinearGradient
              colors={['#2196F3', '#1976D2']}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
              style={styles.welcomeGradient}
            >
              <Animatable.View
                animation="fadeIn"
                duration={1000}
                style={styles.welcomeContent}
              >
                <View>
                  <Animatable.Text
                    animation="fadeInLeft"
                    duration={800}
                    delay={300}
                    style={[styles.welcomeGreeting, getTextStyle()]}
                  >
                    {t('teacher.dashboard.welcome')}
                  </Animatable.Text>
                  <Animatable.Text
                    animation="fadeInLeft"
                    duration={800}
                    delay={500}
                    style={[styles.welcomeName, getTextStyle()]}
                  >
                    {teacherData ? `${teacherData.firstName || ''} ${teacherData.lastName || ''}` : t('common.loading')}
                  </Animatable.Text>
                  <Animatable.View
                    animation="fadeInLeft"
                    duration={800}
                    delay={700}
                    style={styles.dateContainer}
                  >
                    <Text style={[styles.currentDate, getTextStyle()]}>
                      {(() => {
                        try {
                          const langCode = language === 'am' ? 'am-ET' :
                                           language === 'om' ? 'om-ET' : 'en-US';

                          const dateOptions = {
                            weekday: 'long',
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric'
                          };

                          return safeFormatDate(
                            new Date(),
                            dateOptions,
                            langCode,
                            'en-US'
                          );
                        } catch (error) {
                          console.error('Error formatting date in welcome banner:', error);
                          return new Date().toLocaleDateString();
                        }
                      })()}
                    </Text>
                  </Animatable.View>
                </View>
                <Animatable.View
                  animation="bounceIn"
                  duration={1000}
                  delay={800}
                  style={styles.welcomeAvatarContainer}
                >
                  {teacherData?.photoURL ? (
                    <Avatar.Image
                      size={70}
                      source={{ uri: teacherData.photoURL }}
                      style={styles.welcomeAvatar}
                    />
                  ) : (
                    <Avatar.Text
                      size={70}
                      label={teacherData ? `${teacherData.firstName?.charAt(0) || ''}${teacherData.lastName?.charAt(0) || ''}` : 'T'}
                      style={styles.welcomeAvatar}
                    />
                  )}
                </Animatable.View>
              </Animatable.View>
            </LinearGradient>
          </Card>
        </TouchableRipple>
      </Animatable.View>

      {/* Quick Stats Section */}
      <Animatable.View animation="fadeInUp" duration={800} delay={200}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionHeaderText}>{t('teacher.dashboard.quickStats')}</Text>
          <TouchableOpacity
            onPress={() => {
              // Add haptic feedback
              if (Platform.OS === 'ios' && 'impactAsync' in Haptics) {
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
              }
              // Show message
              setSnackbarMessage(t('teacher.dashboard.statsRefreshed'));
              setSnackbarVisible(true);

              // Animate refresh
              Animated.sequence([
                Animated.timing(scaleAnim, {
                  toValue: 0.98,
                  duration: 100,
                  useNativeDriver: true,
                }),
                Animated.spring(scaleAnim, {
                  toValue: 1,
                  tension: 50,
                  friction: 3,
                  useNativeDriver: true,
                })
              ]).start();
            }}
            style={styles.refreshButton}
          >
            <MaterialCommunityIcons name="refresh" size={18} color={theme.colors.primary} />
            <Text style={styles.refreshButtonText}>{t('common.refresh')}</Text>
          </TouchableOpacity>
        </View>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          decelerationRate="fast"
          snapToInterval={screenWidth * 0.38}
          style={styles.quickStatsContainer}
        >
          {[
            { title: t('teacher.dashboard.classes'), value: teacherClasses.length.toString(), icon: 'school', color: '#2196f3', gradient: ['#2196f3', '#03a9f4'], screen: 'ClassManagement' },
            { title: t('teacher.dashboard.students'), value: isLoading ? '...' : teacherClasses.reduce((total, cls) => total + (cls.studentCount || 0), 0).toString(), icon: 'account-group', color: '#4caf50', gradient: ['#4caf50', '#8bc34a'], screen: 'StudentList' },
            { title: t('teacher.dashboard.assignments'), value: `${assignmentsStats.completed}/${assignmentsStats.total}`, icon: 'clipboard-text', color: '#ff9800', gradient: ['#ff9800', '#ffb74d'], screen: 'AssessmentManagement' },
            { title: t('teacher.dashboard.attendance'), value: `${attendanceStats.present}%`, icon: 'calendar-check', color: '#f44336', gradient: ['#f44336', '#e57373'], screen: 'AttendanceManagement' }
          ].map((stat, index) => (
            <Animatable.View
              key={index}
              animation="fadeInRight"
              duration={500}
              delay={index * 200}
            >
              <TouchableRipple
                onPress={() => {
                  // Add haptic feedback
                  if (Platform.OS === 'ios' && 'impactAsync' in Haptics) {
                    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                  }

                  // Navigate to the corresponding screen
                  if (stat.screen) {
                    setSnackbarMessage(t('common.navigatingTo') + ' ' + stat.title);
                    setSnackbarVisible(true);

                    // Show loading state
                    setLoadingSection(stat.screen);

                    // Navigate with a small delay for better UX
                    setTimeout(() => {
                      navigation.navigate(stat.screen);
                      setLoadingSection(null);
                    }, 300);
                  }
                }}
                rippleColor="rgba(255, 255, 255, 0.2)"
                style={{ borderRadius: 12 }}
              >
                <Surface style={[styles.statCard, { borderLeftColor: stat.color, borderLeftWidth: 4 }]}>
                  <LinearGradient
                    colors={stat.gradient}
                    style={styles.statGradient}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 1 }}
                  >
                    <Avatar.Icon size={48} icon={stat.icon} style={{ backgroundColor: 'transparent' }} />
                    <Title style={styles.statValue}>{stat.value}</Title>
                    <Paragraph style={styles.statTitle}>{stat.title}</Paragraph>

                    {/* Show loading indicator when navigating */}
                    {loadingSection === stat.screen && (
                      <View style={styles.statLoadingOverlay}>
                        <ActivityIndicator color="white" size="small" />
                      </View>
                    )}
                  </LinearGradient>
                </Surface>
              </TouchableRipple>
            </Animatable.View>
          ))}
        </ScrollView>
      </Animatable.View>

      {/* Performance Chart */}
      <Animatable.View animation="fadeInUp" duration={800} delay={400}>
        <Card style={styles.chartCard}>
          <Card.Title
            title={t('teacher.dashboard.studentPerformance')}
            titleStyle={getTextStyle()}
            left={(props) => <MaterialCommunityIcons {...props} name="chart-line" size={24} color={theme.colors.primary} />}
            right={(props) => (
              <IconButton
                {...props}
                icon="dots-vertical"
                onPress={() => {
                  // Add haptic feedback
                  if (Platform.OS === 'ios' && 'impactAsync' in Haptics) {
                    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                  }

                  // Show options menu
                  setMenuVisible(true);

                  // Show feedback
                  setSnackbarMessage(t('teacher.dashboard.viewingPerformanceOptions'));
                  setSnackbarVisible(true);
                }}
              />
            )}
            subtitle={t('teacher.dashboard.performanceSubtitle')}
            subtitleStyle={styles.chartSubtitle}
          />
          <Card.Content>
            <View style={styles.chartContainer}>
              <Animatable.View animation="fadeIn" duration={1000} delay={500}>
                <LineChart
                  data={{
                    labels: ['Q1', 'Q2', 'Q3', 'Q4'],
                    datasets: [
                      {
                        data: [78, 82, 85, 88],
                        color: (opacity = 1) => `rgba(76, 175, 80, ${opacity})`,
                        strokeWidth: 2
                      }
                    ],
                    legend: [t('teacher.dashboard.averagePerformance')]
                  }}
                  width={screenWidth}
                  height={220}
                  chartConfig={{
                    backgroundColor: '#ffffff',
                    backgroundGradientFrom: '#ffffff',
                    backgroundGradientTo: '#ffffff',
                    decimalPlaces: 0,
                    color: (opacity = 1) => `rgba(33, 150, 243, ${opacity})`,
                    labelColor: (opacity = 1) => `rgba(0, 0, 0, ${opacity})`,
                    style: { borderRadius: 16 },
                    propsForDots: {
                      r: "6",
                      strokeWidth: "2",
                      stroke: "#ffa726"
                    }
                  }}
                  style={styles.chart}
                  bezier
                  withInnerLines={false}
                  withDots={true}
                  withShadow={true}
                  fromZero={false}
                />
              </Animatable.View>

              <View style={styles.chartLegend}>
                <View style={styles.legendItem}>
                  <View style={[styles.legendColor, { backgroundColor: '#4CAF50' }]} />
                  <Text style={styles.legendText}>{t('teacher.dashboard.averagePerformance')}</Text>
                </View>
              </View>

              <TouchableRipple
                onPress={() => {
                  // Add haptic feedback
                  if (Platform.OS === 'ios' && 'impactAsync' in Haptics) {
                    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                  }

                  // Navigate to detailed performance screen
                  setSnackbarMessage(t('teacher.dashboard.viewingDetailedPerformance'));
                  setSnackbarVisible(true);

                  // Show loading state
                  setLoadingSection('ClassPerformance');

                  // Navigate with a small delay for better UX
                  setTimeout(() => {
                    navigation.navigate('ClassPerformance');
                    setLoadingSection(null);
                  }, 300);
                }}
                style={styles.viewDetailsButton}
              >
                <Text style={styles.viewDetailsText}>{t('teacher.dashboard.viewDetailedPerformance')}</Text>
              </TouchableRipple>

              {/* Show loading indicator when navigating */}
              {loadingSection === 'ClassPerformance' && (
                <View style={styles.chartLoadingOverlay}>
                  <ActivityIndicator color={theme.colors.primary} size="small" />
                </View>
              )}
            </View>
          </Card.Content>
        </Card>
      </Animatable.View>

      {/* Attendance Chart */}
      <Animatable.View animation="fadeInUp" duration={800} delay={600}>
        <Card style={styles.chartCard}>
          <Card.Title
            title={t('teacher.dashboard.attendanceStats')}
            titleStyle={getTextStyle()}
            left={(props) => <MaterialCommunityIcons {...props} name="calendar-check" size={24} color={theme.colors.primary} />}
          />
          <Card.Content>
            <PieChart
              data={[
                {
                  name: t('teacher.dashboard.present'),
                  value: attendanceStats.present,
                  color: '#4CAF50',
                  legendFontColor: '#7F7F7F',
                  legendFontSize: 12
                },
                {
                  name: t('teacher.dashboard.absent'),
                  value: attendanceStats.absent,
                  color: '#F44336',
                  legendFontColor: '#7F7F7F',
                  legendFontSize: 12
                },
                {
                  name: t('teacher.dashboard.late'),
                  value: attendanceStats.late,
                  color: '#FF9800',
                  legendFontColor: '#7F7F7F',
                  legendFontSize: 12
                },
                {
                  name: t('teacher.dashboard.excused'),
                  value: attendanceStats.excused,
                  color: '#03A9F4',
                  legendFontColor: '#7F7F7F',
                  legendFontSize: 12
                }
              ]}
              width={screenWidth}
              height={220}
              chartConfig={{
                backgroundColor: '#ffffff',
                backgroundGradientFrom: '#ffffff',
                backgroundGradientTo: '#ffffff',
                decimalPlaces: 0,
                color: (opacity = 1) => `rgba(33, 150, 243, ${opacity})`,
                labelColor: (opacity = 1) => `rgba(0, 0, 0, ${opacity})`,
                style: { borderRadius: 16 }
              }}
              accessor="value"
              backgroundColor="transparent"
              paddingLeft="15"
              style={styles.chart}
              hasLegend={true}
            />
          </Card.Content>
        </Card>
      </Animatable.View>

      {/* Quick Access Menu */}
      <Animatable.View animation="fadeInUp" duration={800} delay={700}>
        <Card style={styles.quickAccessCard}>
          <Card.Content>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionHeaderText}>{t('teacher.dashboard.quickAccess')}</Text>
              <TouchableOpacity
                onPress={() => {
                  // Add haptic feedback
                  if (Platform.OS === 'ios' && 'impactAsync' in Haptics) {
                    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                  }
                  setShowAllQuickAccess(!showAllQuickAccess);
                }}
                style={styles.refreshButton}
              >
                <MaterialCommunityIcons
                  name={showAllQuickAccess ? "chevron-up" : "chevron-down"}
                  size={18}
                  color={theme.colors.primary}
                />
                <Text style={styles.refreshButtonText}>
                  {showAllQuickAccess ? t('common.showLess') : t('common.showMore')}
                </Text>
              </TouchableOpacity>
            </View>

            <ScrollView
              horizontal={!showAllQuickAccess}
              showsHorizontalScrollIndicator={false}
              style={styles.quickAccessContainer}
            >
              <View style={[
                styles.quickAccessGrid,
                showAllQuickAccess ? styles.quickAccessGridExpanded : null
              ]}>
                {dashboardItems.flatMap(category =>
                  category.items.slice(0, showAllQuickAccess ? category.items.length : 2).map((item, index) => (
                    <Animatable.View
                      key={`${category.translationKey}-${index}`}
                      animation="fadeIn"
                      duration={500}
                      delay={index * 100}
                    >
                      <TouchableRipple
                        onPress={() => {
                          // Add haptic feedback
                          if (Platform.OS === 'ios' && 'impactAsync' in Haptics) {
                            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                          }

                          // Show loading state
                          setLoadingSection(item.screen);

                          // Navigate with a small delay for better UX
                          setTimeout(() => {
                            navigation.navigate(item.screen);
                            setLoadingSection(null);
                          }, 300);
                        }}
                        style={styles.quickAccessItem}
                        rippleColor="rgba(0, 0, 0, 0.1)"
                      >
                        <LinearGradient
                          colors={[`${item.color}20`, `${item.color}10`]}
                          style={styles.quickAccessItemGradient}
                          start={{ x: 0, y: 0 }}
                          end={{ x: 1, y: 1 }}
                        >
                          <View style={[styles.quickAccessIconContainer, { backgroundColor: `${item.color}30` }]}>
                            <MaterialCommunityIcons name={item.icon} size={24} color={item.color} />
                          </View>
                          <Text style={[styles.quickAccessItemTitle, getTextStyle()]}>{t(`teacher.dashboard.${item.translationKey}`)}</Text>

                          {/* Show loading indicator when navigating */}
                          {loadingSection === item.screen && (
                            <View style={styles.quickAccessLoadingOverlay}>
                              <ActivityIndicator color={item.color} size="small" />
                            </View>
                          )}
                        </LinearGradient>
                      </TouchableRipple>
                    </Animatable.View>
                  ))
                )}
              </View>
            </ScrollView>
          </Card.Content>
        </Card>
      </Animatable.View>

      {/* Grading Progress */}
      <Animatable.View animation="fadeInUp" duration={800} delay={900}>
        <Card style={styles.progressCard}>
          <Card.Title
            title={t('teacher.dashboard.gradingProgress')}
            titleStyle={getTextStyle()}
            left={(props) => <MaterialCommunityIcons {...props} name="clipboard-check" size={24} color={theme.colors.primary} />}
          />
          <Card.Content>
            <View style={styles.progressContainer}>
              <View style={styles.progressTextContainer}>
                <Text style={[styles.progressPercentage, getTextStyle()]}>{gradingProgress}%</Text>
                <Text style={[styles.progressLabel, getTextStyle()]}>{t('teacher.dashboard.completed')}</Text>
              </View>
              <ProgressBar progress={gradingProgress / 100} color={theme.colors.primary} style={styles.progressBar} />
            </View>
          </Card.Content>
        </Card>
      </Animatable.View>

      {/* Today's Schedule */}
      <Animatable.View animation="fadeInUp" duration={800} delay={1000}>
        <Card style={styles.scheduleCard}>
          <Card.Title
            title={t('teacher.dashboard.todaySchedule')}
            titleStyle={getTextStyle()}
            left={(props) => <MaterialCommunityIcons {...props} name="clock-outline" size={24} color={theme.colors.primary} />}
          />
          <Card.Content>
            {dailySchedule.length === 0 ? (
              <Text style={[{ textAlign: 'center', marginVertical: 20, color: '#757575' }, getTextStyle()]}>
                {t('teacher.dashboard.noSchedule')}
              </Text>
            ) : (
              dailySchedule.map((schedule, index) => (
                <Animatable.View
                  key={index}
                  animation="fadeInUp"
                  delay={index * 100}
                  duration={300}
                >
                  <Surface style={[styles.scheduleItem, currentActiveClass === schedule.id && styles.activeScheduleItem]}>
                    <View style={styles.scheduleTimeContainer}>
                      <Text style={[styles.scheduleTime, getTextStyle()]}>{schedule.startTime} - {schedule.endTime}</Text>
                      {currentActiveClass === schedule.id && (
                        <Chip style={styles.activeChip} textStyle={{ color: 'white', fontSize: 10 }}>
                          {t('teacher.dashboard.current')}
                        </Chip>
                      )}
                    </View>
                    <View style={styles.scheduleDetails}>
                      <Text style={[styles.scheduleClass, getTextStyle()]}>{schedule.className}</Text>
                      <Text style={[styles.scheduleSubject, getTextStyle()]}>{schedule.subject}</Text>
                      <Text style={[styles.scheduleRoom, getTextStyle()]}>{schedule.room}</Text>
                    </View>
                  </Surface>
                  {index < dailySchedule.length - 1 && <Divider style={{ marginVertical: 4 }} />}
                </Animatable.View>
              ))
            )}
            <Button
              mode="text"
              onPress={() => navigation.navigate('TeacherClassSchedule')}
              style={{ marginTop: 8 }}
            >
              {t('teacher.dashboard.viewFullSchedule')}
            </Button>
          </Card.Content>
        </Card>
      </Animatable.View>

      {/* Recent Activities */}
      <Animatable.View animation="fadeInUp" duration={800} delay={1200}>
        <Card style={styles.sectionCard}>
          <Card.Title
            title={t('teacher.dashboard.recentActivities')}
            titleStyle={getTextStyle()}
            left={(props) => <MaterialCommunityIcons {...props} name="clock-outline" size={24} color={theme.colors.primary} />}
            right={(props) => <IconButton {...props} icon="dots-vertical" onPress={() => navigation.navigate('ActivityHistory')} />}
          />
          <Card.Content>
            {loadingActivities ? (
              <ActivityIndicator size="small" color={theme.colors.primary} style={{ marginVertical: 20 }} />
            ) : recentActivities.length === 0 ? (
              <Text style={[{ textAlign: 'center', marginVertical: 20, color: '#757575' }, getTextStyle()]}>
                {t('teacher.dashboard.noActivities')}
              </Text>
            ) : (
              recentActivities.slice(0, 5).map((activity, index) => (
                <Animatable.View
                  key={activity.id || index}
                  animation="fadeInUp"
                  delay={index * 100}
                  duration={300}
                >
                  <TouchableOpacity
                    onPress={() => handleActivityPress(activity)}
                    style={[styles.activityItem, { backgroundColor: 'white' }]}
                  >
                    <View style={[styles.summaryIconContainer, { backgroundColor: `${activity.color}20` }]}>
                      <MaterialCommunityIcons name={activity.icon} size={20} color={activity.color} />
                    </View>
                    <View style={styles.activityContent}>
                      <Text style={[styles.activityTitle, getTextStyle()]}>{activity.title}</Text>
                      <Text style={[styles.activityDescription, getTextStyle()]} numberOfLines={1}>
                        {activity.description}
                      </Text>
                      <View style={styles.activityMeta}>
                        <Text style={[styles.activityTime, getTextStyle()]}>
                          {formatDate(activity.timestamp)}
                        </Text>
                        <Chip
                          style={[styles.activityTypeChip, { backgroundColor: `${activity.color}20` }]}
                          textStyle={{ color: activity.color, fontSize: 10 }}
                        >
                          {activity.type}
                        </Chip>
                      </View>
                    </View>
                  </TouchableOpacity>
                  {index < recentActivities.length - 1 && <Divider style={{ marginVertical: 4 }} />}
                </Animatable.View>
              ))
            )}
            <Button
              mode="text"
              onPress={() => navigation.navigate('ActivityHistory')}
              style={{ marginTop: 8 }}
            >
              {t('teacher.dashboard.viewAll')}
            </Button>
          </Card.Content>
        </Card>
      </Animatable.View>

      {/* Bottom spacing */}
      <View style={{ height: 80 }} />
    </ScrollView>
  );

  // Render sidebar navigation
  const renderSidebar = () => (
    drawerOpen && (
      <View style={styles.drawerContainer}>
        <Animated.View style={[styles.drawerContent, { transform: [{ translateX: drawerAnim }] }]}>
          <LinearGradient
            colors={[theme.colors.primary, '#1976D2']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
            style={styles.drawerHeader}
          >
            <Avatar.Image
              size={80}
              source={{ uri: teacherData?.photoURL || 'https://ui-avatars.com/api/?name=T&background=random' }}
              style={styles.drawerAvatar}
            />
            <View style={styles.drawerHeaderText}>
              <Animatable.Text
                animation="fadeIn"
                style={[styles.drawerTitle, { color: 'white' }]}
              >
                {teacherData?.displayName || t('common.loading')}
              </Animatable.Text>
              <Animatable.Text
                animation="fadeIn"
                delay={300}
                style={[styles.drawerSubtitle, { color: 'rgba(255, 255, 255, 0.8)' }]}
              >
                {t('auth.teacher')}
              </Animatable.Text>
            </View>
            <IconButton
              icon="close"
              size={24}
              color="white"
              onPress={toggleDrawer}
              style={styles.drawerCloseButton}
            />
          </LinearGradient>

          <ScrollView style={styles.drawerBody}>
            <Animatable.View animation="fadeInLeft" delay={100} duration={300}>
              <List.Item
                title={t('teacher.dashboard.dashboard')}
                titleStyle={getTextStyle()}
                left={props => <List.Icon {...props} icon="view-dashboard" color={theme.colors.primary} />}
                onPress={() => handleSectionPress('dashboard')}
                style={activeSection === 'dashboard' ? [styles.activeDrawerItem, { borderRightColor: theme.colors.primary }] : null}
              />
            </Animatable.View>

            <Divider style={styles.drawerDivider} />
            <Animatable.Text animation="fadeIn" delay={150} style={styles.drawerSectionTitle}>
              {t('teacher.drawerMenu.academic')}
            </Animatable.Text>

            <Animatable.View animation="fadeInLeft" delay={200} duration={300}>
              <List.Item
                title={t('teacher.attendance.title')}
                titleStyle={getTextStyle()}
                left={props => <List.Icon {...props} icon="account-check" color={theme.colors.info} />}
                onPress={() => navigation.navigate('AttendanceManagement')}
              />
            </Animatable.View>

            <Animatable.View animation="fadeInLeft" delay={250} duration={300}>
              <List.Item
                title={t('teacher.grades.title')}
                titleStyle={getTextStyle()}
                left={props => <List.Icon {...props} icon="school" color={theme.colors.success} />}
                onPress={() => navigation.navigate('GradeManagement')}
              />
            </Animatable.View>

            <Animatable.View animation="fadeInLeft" delay={300} duration={300}>
              <List.Item
                title={t('teacher.homework.title')}
                titleStyle={getTextStyle()}
                left={props => <List.Icon {...props} icon="book-open-variant" color={theme.colors.warning} />}
                onPress={() => navigation.navigate('HomeworkManagement')}
              />
            </Animatable.View>

            <Animatable.View animation="fadeInLeft" delay={350} duration={300}>
              <List.Item
                title={t('teacher.exams.title')}
                titleStyle={getTextStyle()}
                left={props => <List.Icon {...props} icon="clipboard-text" color={theme.colors.error} />}
                onPress={() => navigation.navigate('TeacherExamSchedule')}
              />
            </Animatable.View>

            <Animatable.View animation="fadeInLeft" delay={400} duration={300}>
              <List.Item
                title={t('teacher.classes.title')}
                titleStyle={getTextStyle()}
                left={props => <List.Icon {...props} icon="google-classroom" color="#9C27B0" />}
                onPress={() => navigation.navigate('ClassManagement')}
              />
            </Animatable.View>

            <Animatable.View animation="fadeInLeft" delay={450} duration={300}>
              <List.Item
                title={t('teacher.calendar.title')}
                titleStyle={getTextStyle()}
                left={props => <List.Icon {...props} icon="calendar" color="#00BCD4" />}
                onPress={() => navigation.navigate('CalendarManagement')}
              />
            </Animatable.View>

            <Animatable.View animation="fadeInLeft" delay={500} duration={300}>
              <List.Item
                title={t('teacher.schedule.title') || "Class Schedule"}
                titleStyle={getTextStyle()}
                left={props => <List.Icon {...props} icon="calendar-week" color="#00BCD4" />}
                onPress={() => navigation.navigate('TeacherClassSchedule')}
              />
            </Animatable.View>

            <Animatable.View animation="fadeInLeft" delay={550} duration={300}>
              <List.Item
                title={t('teacher.directory.title') || "Teacher Directory"}
                titleStyle={getTextStyle()}
                left={props => <List.Icon {...props} icon="account-tie" color="#673AB7" />}
                onPress={() => navigation.navigate('TeacherDirectory')}
              />
            </Animatable.View>

            <Divider style={styles.drawerDivider} />
            <Animatable.Text animation="fadeIn" delay={600} style={styles.drawerSectionTitle}>
              {t('teacher.drawerMenu.personal')}
            </Animatable.Text>

            <Animatable.View animation="fadeInLeft" delay={650} duration={300}>
              <List.Item
                title={t('common.settings')}
                titleStyle={getTextStyle()}
                left={props => <List.Icon {...props} icon="cog" color="#607D8B" />}
                onPress={() => navigation.navigate('ProfileManagement')}
              />
            </Animatable.View>

            <Animatable.View animation="fadeInLeft" delay={700} duration={300}>
              <List.Item
                title={t('common.logout')}
                titleStyle={getTextStyle()}
                left={props => <List.Icon {...props} icon="logout" color="#F44336" />}
                onPress={() => setLogoutDialogVisible(true)}
              />
            </Animatable.View>
          </ScrollView>

          <Animatable.View animation="fadeInUp" delay={750} style={styles.drawerFooter}>
            <Text style={styles.drawerVersion}>v1.0.0</Text>
          </Animatable.View>
        </Animated.View>
      </View>
    )
  );

  return (
    <SafeAreaView style={{ flex: 1 }}>
      <PaperProvider theme={paperTheme}>
        <StatusBar
          barStyle="dark-content"
          backgroundColor="#FFFFFF"
        />
        <Portal>
          <Dialog
            visible={logoutDialogVisible}
            onDismiss={() => setLogoutDialogVisible(false)}
            style={styles.dialog}
          >
            <Dialog.Title>{t('common.confirmLogout')}</Dialog.Title>
            <Dialog.Content>
              <Paragraph>{t('common.confirmLogoutMessage')}</Paragraph>
            </Dialog.Content>
            <Dialog.Actions>
              <Button onPress={() => setLogoutDialogVisible(false)}>
                {t('common.cancel')}
              </Button>
              <Button onPress={handleLogout}>
                {t('common.logout')}
              </Button>
            </Dialog.Actions>
          </Dialog>
        </Portal>

        {renderHeader()}

        <View style={styles.container}>
          {isTablet ? (
            // Desktop/Tablet Layout
            <View style={styles.desktopContainer}>
              {renderSidebar()}
              <ScrollView
                style={styles.mainContent}
                contentContainerStyle={styles.mainContentContainer}
                refreshControl={
                  <RefreshControl
                    refreshing={refreshing}
                    onRefresh={onRefresh}
                    colors={[theme.colors.primary]}
                  />
                }
              >
                {renderDashboard()}
              </ScrollView>
            </View>
          ) : (
            // Mobile Layout
            <View style={styles.container}>
              {drawerOpen && (
                <SidebarBackdrop
                  visible={drawerOpen}
                  onClose={() => toggleDrawer()}
                  animation={backdropFadeAnim}
                />
              )}
              <Animated.View
                style={[
                  styles.drawer,
                  {
                    transform: [{ translateX: drawerAnim }],
                  },
                ]}
              >
                {renderSidebar()}
              </Animated.View>
              <ScrollView
                style={styles.mainContent}
                contentContainerStyle={styles.mainContentContainer}
                refreshControl={
                  <RefreshControl
                    refreshing={refreshing}
                    onRefresh={onRefresh}
                    colors={[theme.colors.primary]}
                  />
                }
              >
                {renderDashboard()}
              </ScrollView>
            </View>
          )}
        </View>

        {/* Add FAB for quick actions */}
        <FAB
          style={styles.fab}
          icon="plus"
          color="white"
          onPress={() => {}}
          actions={[
            {
              icon: 'message',
              label: t('teacher.communication.newMessage'),
              onPress: () => navigation.navigate('TeacherMessaging'),
            },
            {
              icon: 'clipboard-check',
              label: t('teacher.attendance.title'),
              onPress: () => navigation.navigate('AttendanceManagement'),
            },
            {
              icon: 'school',
              label: t('teacher.grades.title'),
              onPress: () => navigation.navigate('GradeManagement'),
            }
          ]}
        />

        {/* Snackbar for feedback */}
        <Snackbar
          visible={snackbarVisible}
          onDismiss={() => setSnackbarVisible(false)}
          duration={3000}
          style={styles.snackbar}
          action={{
            label: t('common.dismiss'),
            onPress: () => setSnackbarVisible(false),
          }}
        >
          {snackbarMessage}
        </Snackbar>

        {/* Menu for chart options */}
        <Portal>
          <Menu
            visible={menuVisible}
            onDismiss={() => setMenuVisible(false)}
            anchor={{ x: screenWidth - 40, y: 400 }}
            style={styles.menu}
          >
            <Menu.Item
              onPress={() => {
                setMenuVisible(false);
                setSnackbarMessage(t('teacher.dashboard.viewingClassPerformance'));
                setSnackbarVisible(true);
                navigation.navigate('ClassPerformance');
              }}
              title={t('teacher.dashboard.viewClassPerformance')}
              icon="chart-line"
            />
            <Menu.Item
              onPress={() => {
                setMenuVisible(false);
                setSnackbarMessage(t('teacher.dashboard.viewingStudentPerformance'));
                setSnackbarVisible(true);
                navigation.navigate('StudentPerformance');
              }}
              title={t('teacher.dashboard.viewStudentPerformance')}
              icon="account-details"
            />
            <Menu.Item
              onPress={() => {
                setMenuVisible(false);
                setSnackbarMessage(t('teacher.dashboard.exportingData'));
                setSnackbarVisible(true);
              }}
              title={t('teacher.dashboard.exportData')}
              icon="export"
            />
            <Divider />
            <Menu.Item
              onPress={() => {
                setMenuVisible(false);
              }}
              title={t('common.cancel')}
              icon="close"
            />
          </Menu>
        </Portal>
      </PaperProvider>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  // Main Container
  container: {
    flex: 1,
    padding: 0,
    backgroundColor: theme.colors.background
  },
  scrollView: {
    flex: 1,
    backgroundColor: theme.colors.background
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.background
  },
  loadingCard: {
    borderRadius: 16,
    overflow: 'hidden',
    elevation: 8,
    width: screenWidth * 0.8,
    maxWidth: 320,
  },
  loadingGradient: {
    padding: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    color: 'white',
    fontSize: 20,
    fontWeight: 'bold',
    marginTop: 16,
  },
  loadingSubText: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 14,
    marginTop: 8,
    textAlign: 'center',
  },

  // App Bar
  appHeader: {
    backgroundColor: 'white',
    elevation: 4,
    borderBottomLeftRadius: 0,
    borderBottomRightRadius: 0,
  },
  appbarTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.colors.primary,
  },
  appbarSubtitle: {
    fontSize: 14,
    color: '#666',
    opacity: 0.8,
  },
  appbarActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  menuButton: {
    marginRight: 8,
  },
  notificationButton: {
    marginRight: 8,
  },
  notificationBadge: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: theme.colors.notification,
    zIndex: 1,
  },

  // Section Headers
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 8,
    marginBottom: 8,
  },
  sectionHeaderText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  refreshButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 4,
  },
  refreshButtonText: {
    fontSize: 12,
    color: theme.colors.primary,
    marginLeft: 4,
  },

  // Drawer/Sidebar
  drawerBackdrop: {
    position: 'absolute',
    left: 0,
    top: 0,
    right: 0,
    bottom: 0,
    backgroundColor: theme.colors.backdrop,
  },
  drawerContainer: {
    position: 'absolute',
    left: 0,
    top: 0,
    bottom: 0,
    width: 300,
    zIndex: 1000,
    elevation: 16,
    borderTopRightRadius: 0,
    borderBottomRightRadius: 30,
    overflow: 'hidden',
  },
  drawerContent: {
    flex: 1,
    backgroundColor: 'white',
    borderTopRightRadius: 0,
    borderBottomRightRadius: 30,
    shadowColor: '#000',
    shadowOffset: { width: 2, height: 0 },
    shadowOpacity: 0.3,
    shadowRadius: 5,
    elevation: 16,
  },
  drawerHeader: {
    padding: 16,
    paddingTop: 48,
    paddingBottom: 24,
    alignItems: 'center',
  },
  drawerAvatar: {
    backgroundColor: 'white',
    borderWidth: 3,
    borderColor: 'rgba(255, 255, 255, 0.5)',
  },
  drawerHeaderText: {
    alignItems: 'center',
    marginTop: 16,
    marginBottom: 8,
  },
  drawerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  drawerSubtitle: {
    fontSize: 14,
    textAlign: 'center',
    marginTop: 4,
  },
  drawerCloseButton: {
    position: 'absolute',
    top: 16,
    right: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  drawerBody: {
    flex: 1,
  },
  drawerDivider: {
    height: 1,
    marginVertical: 8,
  },
  drawerSectionTitle: {
    fontSize: 12,
    color: '#757575',
    marginLeft: 16,
    marginTop: 8,
    marginBottom: 8,
    fontWeight: 'bold',
    textTransform: 'uppercase',
  },
  activeDrawerItem: {
    backgroundColor: 'rgba(33, 150, 243, 0.1)',
    borderRightWidth: 4,
  },
  drawerFooter: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
    alignItems: 'center',
  },
  drawerVersion: {
    fontSize: 12,
    color: '#9E9E9E',
  },

  // Welcome Banner
  welcomeBanner: {
    borderRadius: 0,
    overflow: 'hidden',
    marginBottom: 16,
  },
  welcomeBannerImage: {
    borderRadius: 16,
  },
  welcomeGradient: {
    padding: 20,
    borderRadius: 16,
  },
  welcomeContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  welcomeGreeting: {
    fontSize: 14,
    fontWeight: '500',
    color: 'rgba(255, 255, 255, 0.9)',
    marginBottom: 4,
  },
  welcomeName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
  },
  welcomeAvatarContainer: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  welcomeAvatar: {
    backgroundColor: 'white',
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.5)',
  },
  dateContainer: {
    marginTop: 16,
  },
  currentDate: {
    fontSize: 12,
    fontWeight: '500',
    color: 'rgba(255, 255, 255, 0.9)',
    opacity: 0.9,
  },

  // Dashboard Summary
  dashboardSummary: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
    flexWrap: 'wrap',
  },
  summaryCardContainer: {
    width: '32%',
    minWidth: 100,
    marginBottom: 8,
  },
  summaryCard: {
    borderRadius: 12,
    padding: 12,
    elevation: 2,
    height: 100,
    borderLeftWidth: 4,
    justifyContent: 'space-between',
  },
  summaryIconContainer: {
    backgroundColor: 'rgba(33, 150, 243, 0.1)',
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  summaryContent: {
    marginTop: 12,
  },
  summaryValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  summaryLabel: {
    fontSize: 12,
    color: '#757575',
    marginTop: 4,
  },

  // Section Cards
  sectionCard: {
    marginBottom: 16,
    borderRadius: 0,
    overflow: 'hidden',
    elevation: 2,
    marginHorizontal: 0,
  },

  // Quick Actions
  quickActionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flexWrap: 'wrap',
  },
  quickActionItem: {
    width: '23%',
    alignItems: 'center',
    marginBottom: 8,
  },
  quickActionGradient: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 2,
  },
  quickActionText: {
    fontSize: 12,
    textAlign: 'center',
    color: '#333',
  },

  // Quick Stats
  quickStatsContainer: {
    paddingHorizontal: 0,
    paddingVertical: 8,
    marginBottom: 16,
  },
  statCard: {
    width: screenWidth * 0.38,
    height: 140,
    marginRight: 12,
    borderRadius: 12,
    overflow: 'hidden',
    elevation: 2,
  },
  statGradient: {
    flex: 1,
    padding: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
    marginTop: 8,
  },
  statTitle: {
    fontSize: 14,
    color: 'white',
    opacity: 0.9,
    marginTop: 4,
    textAlign: 'center',
  },

  // Chart Cards
  chartCard: {
    marginHorizontal: 0,
    marginBottom: 16,
    borderRadius: 0,
    overflow: 'hidden',
    elevation: 3,
  },
  chartSubtitle: {
    fontSize: 12,
    color: '#666',
  },
  chartContainer: {
    position: 'relative',
    paddingHorizontal: 8,
  },
  chart: {
    marginVertical: 8,
    borderRadius: 16,
  },
  chartLegend: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 8,
    marginBottom: 16,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
  },
  legendColor: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 6,
  },
  legendText: {
    fontSize: 12,
    color: '#666',
  },
  viewDetailsButton: {
    alignSelf: 'center',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    backgroundColor: 'rgba(33, 150, 243, 0.1)',
  },
  viewDetailsText: {
    color: theme.colors.primary,
    fontSize: 14,
  },
  chartLoadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 12,
  },
  statLoadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 12,
  },

  // Progress Section
  progressSection: {
    marginVertical: 12,
  },
  progressCard: {
    marginHorizontal: 0,
    marginBottom: 16,
    borderRadius: 0,
    overflow: 'hidden',
    elevation: 2,
  },
  progressContainer: {
    padding: 16,
  },
  progressTextContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  progressPercentage: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.colors.primary,
  },
  progressLabel: {
    fontSize: 14,
    color: '#757575',
  },
  progressBar: {
    height: 10,
    borderRadius: 5,
  },
  progressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  progressTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  progressDetailChip: {
    height: 24,
    backgroundColor: 'rgba(33, 150, 243, 0.1)',
  },
  progressStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  progressStatItem: {
    alignItems: 'center',
  },
  progressStatValue: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  progressStatLabel: {
    fontSize: 12,
    color: '#757575',
    marginTop: 2,
  },
  progressBarContainer: {
    height: 8,
    borderRadius: 4,
    flexDirection: 'row',
    overflow: 'hidden',
  },
  progressBarSegment: {
    height: '100%',
  },
  progressDivider: {
    marginVertical: 16,
  },
  progressBarWrap: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  linearProgress: {
    height: 8,
    borderRadius: 4,
    flex: 1,
    marginRight: 12,
  },
  progressButton: {
    alignSelf: 'flex-start',
    marginTop: 8,
  },

  // Schedule Card
  scheduleCard: {
    marginHorizontal: 0,
    marginBottom: 16,
    borderRadius: 0,
    overflow: 'hidden',
    elevation: 2,
  },
  activeChip: {
    backgroundColor: theme.colors.primary,
    marginTop: 4,
  },
  scheduleDetails: {
    marginLeft: 12,
    flex: 1,
  },

  // Activity Items
  activityItem: {
    flexDirection: 'row',
    padding: 12,
    borderRadius: 8,
    marginBottom: 12,
    alignItems: 'center',
    elevation: 1,
  },
  activityContent: {
    flex: 1,
    marginLeft: 12,
  },
  activityTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  activityDescription: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  activityMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
    justifyContent: 'space-between',
  },
  activityTime: {
    fontSize: 12,
    color: '#9E9E9E',
  },
  activityTypeChip: {
    height: 22,
    backgroundColor: 'rgba(33, 150, 243, 0.1)',
  },
  activityTypeText: {
    fontSize: 10,
  },

  // Empty State
  emptyStateContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 24,
  },
  emptyStateText: {
    marginTop: 8,
    fontSize: 14,
    color: '#757575',
    textAlign: 'center',
  },

  // Exam Items
  examItem: {
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
    elevation: 1,
  },
  examContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  examTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  examDetails: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  examIcon: {
    marginRight: 4,
  },
  examDate: {
    fontSize: 14,
    color: '#666',
  },
  examClass: {
    fontSize: 14,
    color: '#666',
  },
  examButton: {
    borderRadius: 20,
  },

  // Buttons
  viewAllButton: {
    alignSelf: 'center',
    marginTop: 8,
  },

  // Responsive adjustments for tablets
  ...(isTablet ? {
    dashboardSummary: {
      justifyContent: 'flex-start',
    },
    summaryCardContainer: {
      width: '24%',
      marginRight: 12,
    },
    quickActionItem: {
      width: '22%',
      marginHorizontal: 12,
    },
  } : {}),

  // Responsive adjustments for small phones
  ...(screenWidth < 360 ? {
    summaryCardContainer: {
      width: '100%',
      marginBottom: 12,
    },
    quickActionItem: {
      width: '48%',
      marginBottom: 16,
    },
  } : {}),

  // Daily Schedule styles
  currentClassCard: {
    marginBottom: 16,
    borderRadius: 8,
    overflow: 'hidden',
    position: 'relative',
    padding: 16,
    paddingTop: 24,
  },
  ongoingClassCard: {
    backgroundColor: 'rgba(76, 175, 80, 0.1)',
    borderLeftWidth: 4,
    borderLeftColor: '#4CAF50',
  },
  upcomingClassCard: {
    backgroundColor: 'rgba(255, 152, 0, 0.1)',
    borderLeftWidth: 4,
    borderLeftColor: '#FF9800',
  },
  currentClassBadge: {
    position: 'absolute',
    top: 0,
    left: 0,
    borderTopLeftRadius: 8,
    borderBottomRightRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 4,
  },
  currentClassBadgeContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  currentClassBadgeText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 12,
    marginLeft: 4,
  },
  currentClassHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  currentClassTime: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  currentClassTimeText: {
    marginLeft: 6,
    fontSize: 14,
    fontWeight: '500',
    color: '#555',
  },
  timeRemainingChip: {
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
    height: 26,
  },
  timeRemainingText: {
    fontSize: 12,
  },
  currentClassDetails: {
    marginBottom: 16,
  },
  currentClassSubject: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 6,
    color: '#333',
  },
  currentClassInfo: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  goToClassButton: {
    alignSelf: 'flex-start',
    borderRadius: 20,
  },
  scheduleListContainer: {
    marginTop: 16,
  },
  scheduleSectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 12,
    color: '#333',
  },
  scheduleItem: {
    flexDirection: 'row',
    padding: 12,
    borderRadius: 8,
    marginBottom: 10,
    elevation: 1,
  },
  activeScheduleItem: {
    borderLeftWidth: 4,
    borderLeftColor: theme.colors.primary,
    backgroundColor: 'rgba(33, 150, 243, 0.05)',
  },
  scheduleTimeContainer: {
    width: 85,
    borderRightWidth: 1,
    borderRightColor: '#e0e0e0',
    paddingRight: 8,
  },
  scheduleTime: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#333',
  },
  schedulePeriod: {
    fontSize: 10,
    color: '#666',
    marginTop: 4,
  },
  scheduleInfoContainer: {
    flex: 1,
    paddingHorizontal: 12,
  },
  scheduleSubject: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
  },
  scheduleClass: {
    fontSize: 13,
    color: '#555',
    marginTop: 2,
  },
  scheduleRoom: {
    fontSize: 12,
    color: '#777',
    marginTop: 2,
  },
  scheduleStatus: {
    justifyContent: 'center',
    alignItems: 'flex-end',
    width: 40,
  },
  activeNowBadge: {
    backgroundColor: '#4CAF50',
    fontSize: 10,
  },
  viewScheduleButton: {
    marginTop: 12,
    alignSelf: 'center',
  },
  viewFullScheduleButton: {
    marginTop: 8,
    alignSelf: 'center',
  },
  dialog: {
    backgroundColor: theme.colors.background,
  },
  desktopContainer: {
    flexDirection: 'row',
  },
  mainContent: {
    flex: 1,
  },
  mainContentContainer: {
    paddingHorizontal: 0,
  },
  drawer: {
    position: 'absolute',
    left: 0,
    top: 0,
    bottom: 0,
    width: 300,
    zIndex: 1000,
    elevation: 16,
    borderTopRightRadius: 0,
    borderBottomRightRadius: 30,
    overflow: 'hidden',
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
    backgroundColor: theme.colors.primary,
  },

  // Snackbar and Menu
  snackbar: {
    backgroundColor: '#323232',
    elevation: 6,
    borderRadius: 4,
    margin: 8,
  },
  menu: {
    elevation: 8,
  },
  // Quick Access Menu Styles
  quickAccessCard: {
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 12,
    elevation: 2,
  },
  quickAccessContainer: {
    marginTop: 8,
  },
  quickAccessGrid: {
    flexDirection: 'row',
    flexWrap: 'nowrap',
  },
  quickAccessGridExpanded: {
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  quickAccessItem: {
    width: 130,
    height: 130,
    margin: 8,
    borderRadius: 12,
    overflow: 'hidden',
  },
  quickAccessItemGradient: {
    flex: 1,
    padding: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  quickAccessIconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  quickAccessItemTitle: {
    fontSize: 12,
    fontWeight: 'bold',
    textAlign: 'center',
    marginTop: 4,
  },
  quickAccessLoadingOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(255, 255, 255, 0.7)',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 12,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  sectionHeaderText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  refreshButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  refreshButtonText: {
    fontSize: 12,
    color: theme.colors.primary,
    marginLeft: 4,
  },
});

export default TeacherDashboard;
