import React, { useState, useEffect } from 'react';
import { View, ScrollView, StyleSheet, Linking } from 'react-native';
import { Card, Title, Text, DataTable, Portal, Modal, TextInput, ActivityIndicator, Chip } from 'react-native-paper';
import { db, auth, storage } from '../../config/firebase';
import { collection, query, where, getDocs, updateDoc, doc, getDoc, onSnapshot } from 'firebase/firestore';
import { ref, uploadBytes, getDownloadURL } from 'firebase/storage';
import CustomButton from '../../components/common/CustomButton';
import * as DocumentPicker from 'expo-document-picker';
import EthiopianCalendar from '../../utils/EthiopianCalendar';

const HomeworkView = () => {
  const [homeworks, setHomeworks] = useState([]);
  const [selectedHomework, setSelectedHomework] = useState(null);
  const [modalVisible, setModalVisible] = useState(false);
  const [loading, setLoading] = useState(true);
  const [attachments, setAttachments] = useState([]);
  const [submissionNote, setSubmissionNote] = useState('');
  const [studentClass, setStudentClass] = useState(null);

  useEffect(() => {
    fetchStudentClass();
  }, []);

  useEffect(() => {
    if (studentClass) {
      setupHomeworkListener();
    }
  }, [studentClass]);

  const fetchStudentClass = async () => {
    try {
      const userRef = doc(db, 'users', auth.currentUser.uid);
      const userDoc = await getDoc(userRef);
      if (userDoc.exists()) {
        setStudentClass(userDoc.data().classId);
      } else {
        console.error('Student document not found');
      }
    } catch (error) {
      console.error('Error fetching student class:', error);
    } finally {
      setLoading(false);
    }
  };

  const setupHomeworkListener = () => {
    if (!studentClass) return;

    const homeworkRef = collection(db, 'homework');
    const q = query(
      homeworkRef,
      where('classId', '==', studentClass),
      where('status', '==', 'active')
    );
    
    const unsubscribe = onSnapshot(q, (snapshot) => {
      const homeworkData = [];
      snapshot.forEach((doc) => {
        const data = doc.data();
        homeworkData.push({
          id: doc.id,
          ...data,
          submitted: data.submissions && data.submissions[auth.currentUser.uid],
        });
      });
      setHomeworks(homeworkData);
    });

    return unsubscribe;
  };

  const handleSubmitHomework = async () => {
    try {
      const attachmentUrls = await Promise.all(
        attachments.map(async (file) => {
          const storageRef = ref(storage, `submissions/${Date.now()}_${file.name}`);
          const response = await fetch(file.uri);
          const blob = await response.blob();
          await uploadBytes(storageRef, blob);
          return await getDownloadURL(storageRef);
        })
      );

      const submissionData = {
        attachments: attachmentUrls,
        note: submissionNote,
        submittedAt: new Date().toISOString(),
      };

      const homeworkRef = doc(db, 'homework', selectedHomework.id);
      await updateDoc(homeworkRef, {
        [`submissions.${auth.currentUser.uid}`]: submissionData,
      });

      setModalVisible(false);
      resetForm();
    } catch (error) {
      console.error('Error submitting homework:', error);
    }
  };

  const handleFileSelect = async () => {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: '*/*',
        multiple: true,
      });

      if (!result.canceled) {
        setAttachments([...attachments, ...result.assets]);
      }
    } catch (error) {
      console.error('Error selecting file:', error);
    }
  };

  const resetForm = () => {
    setAttachments([]);
    setSubmissionNote('');
    setSelectedHomework(null);
  };

  const formatDate = (date) => {
    return EthiopianCalendar.formatDate(new Date(date));
  };

  const getDueStatus = (dueDate) => {
    const now = new Date();
    const due = new Date(dueDate);
    
    if (now > due) return 'overdue';
    if (now.getTime() + (24 * 60 * 60 * 1000) > due.getTime()) return 'due-soon';
    return 'upcoming';
  };

  const openAttachment = async (url) => {
    try {
      await Linking.openURL(url);
    } catch (error) {
      console.error('Error opening attachment:', error);
    }
  };

  return (
    <ScrollView style={styles.container}>
      <Card style={styles.card}>
        <Card.Content>
          <Title>My Homework</Title>

          {loading ? (
            <ActivityIndicator style={styles.loader} />
          ) : (
            <View>
              {homeworks.map((homework) => (
                <Card
                  key={homework.id}
                  style={[
                    styles.homeworkCard,
                    homework.submitted ? styles.submittedCard : null
                  ]}
                  onPress={() => {
                    setSelectedHomework(homework);
                    setModalVisible(true);
                  }}
                >
                  <Card.Content>
                    <View style={styles.homeworkHeader}>
                      <Title>{homework.title}</Title>
                      <Chip
                        style={[
                          styles.statusChip,
                          styles[getDueStatus(homework.dueDate)]
                        ]}
                      >
                        {homework.submitted ? 'Submitted' : formatDate(homework.dueDate)}
                      </Chip>
                    </View>

                    <Text style={styles.description}>{homework.description}</Text>

                    <View style={styles.homeworkDetails}>
                      <Text>Type: {homework.type}</Text>
                      <Text>Max Points: {homework.maxPoints}</Text>
                    </View>

                    {homework.attachments && homework.attachments.length > 0 && (
                      <View style={styles.attachmentsContainer}>
                        <Text style={styles.attachmentsTitle}>Attachments:</Text>
                        {homework.attachments.map((url, index) => (
                          <CustomButton
                            key={index}
                            mode="text"
                            onPress={() => openAttachment(url)}
                            style={styles.attachmentButton}
                          >
                            Attachment {index + 1}
                          </CustomButton>
                        ))}
                      </View>
                    )}

                    {homework.submitted && (
                      <View style={styles.submissionDetails}>
                        <Text style={styles.submissionTitle}>Your Submission</Text>
                        <Text>Submitted: {formatDate(homework.submissions[auth.currentUser.uid].submittedAt)}</Text>
                        <Text>Note: {homework.submissions[auth.currentUser.uid].note}</Text>
                        {homework.submissions[auth.currentUser.uid].attachments.map((url, index) => (
                          <CustomButton
                            key={index}
                            mode="text"
                            onPress={() => openAttachment(url)}
                            style={styles.attachmentButton}
                          >
                            Your Attachment {index + 1}
                          </CustomButton>
                        ))}
                      </View>
                    )}
                  </Card.Content>
                </Card>
              ))}
            </View>
          )}
        </Card.Content>
      </Card>

      <Portal>
        <Modal
          visible={modalVisible}
          onDismiss={() => setModalVisible(false)}
          contentContainerStyle={styles.modalContent}
        >
          <ScrollView>
            {selectedHomework && (
              <>
                <Title>{selectedHomework.title}</Title>
                <Text style={styles.modalDescription}>{selectedHomework.description}</Text>

                <View style={styles.modalDetails}>
                  <Text>Due Date: {formatDate(selectedHomework.dueDate)}</Text>
                  <Text>Type: {selectedHomework.type}</Text>
                  <Text>Max Points: {selectedHomework.maxPoints}</Text>
                </View>

                {!selectedHomework.submitted && (
                  <>
                    <TextInput
                      label="Submission Note"
                      value={submissionNote}
                      onChangeText={setSubmissionNote}
                      multiline
                      numberOfLines={3}
                      style={styles.input}
                    />

                    <View style={styles.attachmentsContainer}>
                      <Title style={styles.attachmentsTitle}>Your Attachments</Title>
                      {attachments.map((file, index) => (
                        <Chip
                          key={index}
                          onClose={() => {
                            const newAttachments = [...attachments];
                            newAttachments.splice(index, 1);
                            setAttachments(newAttachments);
                          }}
                          style={styles.attachmentChip}
                        >
                          {file.name}
                        </Chip>
                      ))}
                      <CustomButton
                        mode="outlined"
                        onPress={handleFileSelect}
                        style={styles.attachButton}
                      >
                        Add Attachment
                      </CustomButton>
                    </View>

                    <View style={styles.buttonContainer}>
                      <CustomButton
                        mode="outlined"
                        onPress={() => setModalVisible(false)}
                        style={[styles.button, styles.cancelButton]}
                      >
                        Cancel
                      </CustomButton>
                      <CustomButton
                        mode="contained"
                        onPress={handleSubmitHomework}
                        style={[styles.button, styles.submitButton]}
                      >
                        Submit
                      </CustomButton>
                    </View>
                  </>
                )}
              </>
            )}
          </ScrollView>
        </Modal>
      </Portal>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  card: {
    margin: 16,
  },
  loader: {
    marginVertical: 20,
  },
  homeworkCard: {
    marginVertical: 8,
    borderLeftWidth: 4,
    borderLeftColor: '#2196F3',
  },
  submittedCard: {
    borderLeftColor: '#4CAF50',
  },
  homeworkHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  description: {
    marginVertical: 8,
  },
  homeworkDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  statusChip: {
    alignSelf: 'flex-start',
  },
  'overdue': {
    backgroundColor: '#f44336',
  },
  'due-soon': {
    backgroundColor: '#FF9800',
  },
  'upcoming': {
    backgroundColor: '#4CAF50',
  },
  attachmentsContainer: {
    marginTop: 16,
  },
  attachmentsTitle: {
    fontSize: 14,
    marginBottom: 8,
  },
  attachmentButton: {
    alignSelf: 'flex-start',
    marginVertical: 2,
  },
  submissionDetails: {
    marginTop: 16,
    padding: 12,
    backgroundColor: '#E8F5E9',
    borderRadius: 8,
  },
  submissionTitle: {
    fontWeight: 'bold',
    marginBottom: 8,
  },
  modalContent: {
    backgroundColor: 'white',
    padding: 20,
    margin: 20,
    borderRadius: 8,
    maxHeight: '80%',
  },
  modalDescription: {
    marginVertical: 16,
  },
  modalDetails: {
    backgroundColor: '#E3F2FD',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  input: {
    marginBottom: 16,
  },
  attachmentChip: {
    marginVertical: 4,
  },
  attachButton: {
    marginTop: 8,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: 8,
    marginTop: 16,
  },
  button: {
    minWidth: 100,
  },
  cancelButton: {
    borderColor: '#666',
  },
  submitButton: {
    backgroundColor: '#2196F3',
  },
});

export default HomeworkView;
