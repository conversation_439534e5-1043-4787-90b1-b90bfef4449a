import React, { useState, useEffect } from 'react';
import { View, ScrollView, StyleSheet, Alert } from 'react-native';
import { Card, Title, FAB, Portal, Modal, DataTable, Searchbar } from 'react-native-paper';
import { db } from '../../config/firebase';
import { collection, addDoc, query, getDocs, doc, updateDoc, deleteDoc, where } from 'firebase/firestore';
import CustomButton from '../../components/common/CustomButton';
import CustomInput from '../../components/common/CustomInput';
import ScrollableTable from '../../components/common/ScrollableTable';
import { tableStyles } from '../../styles/tableStyles';

const BookCategories = () => {
  const [categories, setCategories] = useState([]);
  const [visible, setVisible] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(false);

  const [formData, setFormData] = useState({
    name: '',
    code: '',
    description: '',
    shelfLocation: '',
    ageGroup: '',
    maxBorrowDays: '14',
  });

  useEffect(() => {
    fetchCategories();
  }, []);

  const fetchCategories = async () => {
    try {
      const categoriesRef = collection(db, 'bookCategories');
      const q = query(categoriesRef);
      const querySnapshot = await getDocs(q);

      const categoriesData = [];
      querySnapshot.forEach((doc) => {
        categoriesData.push({ id: doc.id, ...doc.data() });
      });

      setCategories(categoriesData);
    } catch (error) {
      console.error('Error fetching categories:', error);
    }
  };

  const handleAddCategory = async () => {
    try {
      setLoading(true);
      const categoriesRef = collection(db, 'bookCategories');
      await addDoc(categoriesRef, {
        ...formData,
        maxBorrowDays: parseInt(formData.maxBorrowDays),
        createdAt: new Date().toISOString(),
      });

      setVisible(false);
      resetForm();
      fetchCategories();
    } catch (error) {
      console.error('Error adding category:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateCategory = async () => {
    try {
      setLoading(true);
      const categoryRef = doc(db, 'bookCategories', selectedCategory.id);
      await updateDoc(categoryRef, {
        ...formData,
        maxBorrowDays: parseInt(formData.maxBorrowDays),
        updatedAt: new Date().toISOString(),
      });

      setVisible(false);
      resetForm();
      fetchCategories();
    } catch (error) {
      console.error('Error updating category:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteCategory = async (categoryId) => {
    try {
      // Check if category has books
      const booksRef = collection(db, 'books');
      const q = query(booksRef, where('category', '==', categoryId));
      const querySnapshot = await getDocs(q);

      if (!querySnapshot.empty) {
        Alert.alert(
          'Cannot Delete Category',
          'This category contains books. Please reassign or delete the books first.'
        );
        return;
      }

      await deleteDoc(doc(db, 'bookCategories', categoryId));
      fetchCategories();
    } catch (error) {
      console.error('Error deleting category:', error);
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      code: '',
      description: '',
      shelfLocation: '',
      ageGroup: '',
      maxBorrowDays: '14',
    });
    setSelectedCategory(null);
  };

  const filteredCategories = categories.filter(category =>
    category.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    category.code.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <View style={styles.container}>
      <Searchbar
        placeholder="Search categories..."
        onChangeText={setSearchQuery}
        value={searchQuery}
        style={styles.searchBar}
      />

      <ScrollableTable
        header={
          <DataTable.Header style={tableStyles.tableHeader}>
            <DataTable.Title style={{ width: 180 }}>Name</DataTable.Title>
            <DataTable.Title style={{ width: 120 }}>Code</DataTable.Title>
            <DataTable.Title style={{ width: 150 }}>Location</DataTable.Title>
            <DataTable.Title numeric style={{ width: 100 }}>Max Days</DataTable.Title>
          </DataTable.Header>
        }
      >
        {filteredCategories.map((category) => (
          <DataTable.Row
            key={category.id}
            style={tableStyles.row}
            onPress={() => {
              setSelectedCategory(category);
              setFormData({
                name: category.name,
                code: category.code,
                description: category.description,
                shelfLocation: category.shelfLocation,
                ageGroup: category.ageGroup,
                maxBorrowDays: category.maxBorrowDays.toString(),
              });
              setVisible(true);
            }}
          >
            <DataTable.Cell style={{ width: 180 }}>{category.name}</DataTable.Cell>
            <DataTable.Cell style={{ width: 120 }}>{category.code}</DataTable.Cell>
            <DataTable.Cell style={{ width: 150 }}>{category.shelfLocation}</DataTable.Cell>
            <DataTable.Cell numeric style={{ width: 100 }}>{category.maxBorrowDays}</DataTable.Cell>
          </DataTable.Row>
        ))}
      </ScrollableTable>

      <Portal>
        <Modal
          visible={visible}
          onDismiss={() => {
            setVisible(false);
            resetForm();
          }}
          contentContainerStyle={styles.modalContent}
        >
          <ScrollView>
            <Title>{selectedCategory ? 'Edit Category' : 'Add New Category'}</Title>

            <CustomInput
              label="Category Name"
              value={formData.name}
              onChangeText={(text) => setFormData({ ...formData, name: text })}
            />

            <CustomInput
              label="Category Code"
              value={formData.code}
              onChangeText={(text) => setFormData({ ...formData, code: text })}
            />

            <CustomInput
              label="Shelf Location"
              value={formData.shelfLocation}
              onChangeText={(text) => setFormData({ ...formData, shelfLocation: text })}
            />

            <CustomInput
              label="Age Group"
              value={formData.ageGroup}
              onChangeText={(text) => setFormData({ ...formData, ageGroup: text })}
            />

            <CustomInput
              label="Maximum Borrow Days"
              value={formData.maxBorrowDays}
              onChangeText={(text) => setFormData({ ...formData, maxBorrowDays: text })}
              keyboardType="numeric"
            />

            <CustomInput
              label="Description"
              value={formData.description}
              onChangeText={(text) => setFormData({ ...formData, description: text })}
              multiline
              numberOfLines={3}
            />

            <View style={styles.modalButtons}>
              <CustomButton
                mode="contained"
                onPress={selectedCategory ? handleUpdateCategory : handleAddCategory}
                loading={loading}
              >
                {selectedCategory ? 'Update' : 'Add'}
              </CustomButton>

              {selectedCategory && (
                <CustomButton
                  mode="outlined"
                  onPress={() => handleDeleteCategory(selectedCategory.id)}
                  style={styles.deleteButton}
                >
                  Delete
                </CustomButton>
              )}

              <CustomButton
                mode="outlined"
                onPress={() => {
                  setVisible(false);
                  resetForm();
                }}
              >
                Cancel
              </CustomButton>
            </View>
          </ScrollView>
        </Modal>
      </Portal>

      <FAB
        style={styles.fab}
        icon="plus"
        onPress={() => setVisible(true)}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  searchBar: {
    margin: 10,
    elevation: 2,
  },
  modalContent: {
    backgroundColor: 'white',
    padding: 20,
    margin: 20,
    borderRadius: 10,
    maxHeight: '80%',
  },
  modalButtons: {
    marginTop: 20,
  },
  deleteButton: {
    marginVertical: 10,
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
  },
});

export default BookCategories;
