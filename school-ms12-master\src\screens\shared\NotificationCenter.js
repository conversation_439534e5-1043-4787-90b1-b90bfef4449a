import React, { useState, useEffect, useCallback } from 'react';
import { View, StyleSheet, ScrollView, RefreshControl, TouchableOpacity, Platform } from 'react-native';
import {
  Text,
  Card,
  Title,
  Paragraph,
  Badge,
  IconButton,
  Chip,
  Button,
  Searchbar,
  Menu,
  Portal,
  Modal,
  Snackbar,
  useTheme,
  Avatar,
  Divider,
  ActivityIndicator
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import * as Animatable from 'react-native-animatable';
import { LinearGradient } from 'expo-linear-gradient';
import { useNotifications } from '../../context/NotificationContext';
import { useLanguage } from '../../context/LanguageContext';
import { useAuth } from '../../context/AuthContext';
import AdminAppHeader from '../../components/common/AdminAppHeader';
import TeacherAppHeader from '../../components/common/TeacherAppHeader';
import StudentAppHeader from '../../components/common/StudentAppHeader';
import ParentAppHeader from '../../components/common/ParentAppHeader';
import { format, formatDistanceToNow } from 'date-fns';
import { am, enUS, om } from 'date-fns/locale';
import { getNotificationIcon } from '../../utils/IconUtils';

const NotificationCenter = () => {
  // No theme needed
  const navigation = useNavigation();
  const { translate, language } = useLanguage();
  const { userRole } = useAuth();
  const {
    notifications,
    unreadCount,
    fetchUserNotifications,
    markAsRead,
    markAllAsRead,
    resetBadgeCount
  } = useNotifications();

  // State variables
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterType, setFilterType] = useState('all'); // 'all', 'unread', 'read', 'type:X'
  const [showFilters, setShowFilters] = useState(true);
  const [selectedNotification, setSelectedNotification] = useState(null);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [menuVisible, setMenuVisible] = useState(false);

  // Fetch notifications on mount
  useEffect(() => {
    loadNotifications();

    // Reset badge count when screen is focused
    resetBadgeCount();
  }, []);

  // Load notifications
  const loadNotifications = async () => {
    setLoading(true);
    try {
      await fetchUserNotifications();
    } catch (error) {
      console.error('Error loading notifications:', error);
      setSnackbarMessage(translate('notifications.loadError') || 'Failed to load notifications');
      setSnackbarVisible(true);
    } finally {
      setLoading(false);
    }
  };

  // Handle refresh
  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      await fetchUserNotifications();
      setSnackbarMessage(translate('notifications.refreshSuccess') || 'Notifications refreshed');
      setSnackbarVisible(true);
    } catch (error) {
      console.error('Error refreshing notifications:', error);
      setSnackbarMessage(translate('notifications.refreshError') || 'Failed to refresh notifications');
      setSnackbarVisible(true);
    } finally {
      setRefreshing(false);
    }
  }, []);

  // Handle notification press
  const handleNotificationPress = async (notification) => {
    // Mark as read if unread
    if (notification.status === 'unread') {
      await markAsRead(notification.id);
    }

    // Show notification details
    setSelectedNotification(notification);
    setDetailModalVisible(true);

    // Handle navigation based on notification data
    if (notification.data?.route) {
      // Close modal first
      setDetailModalVisible(false);
      // Navigate to the specified route
      navigation.navigate(notification.data.route, notification.data.params);
    }
  };

  // Handle mark all as read
  const handleMarkAllAsRead = async () => {
    try {
      await markAllAsRead();
      setSnackbarMessage(translate('notifications.allMarkedRead') || 'All notifications marked as read');
      setSnackbarVisible(true);
    } catch (error) {
      console.error('Error marking all as read:', error);
      setSnackbarMessage(translate('notifications.markAllError') || 'Failed to mark all as read');
      setSnackbarVisible(true);
    }
  };

  // Toggle filters visibility
  const toggleFilters = () => {
    setShowFilters(!showFilters);
  };

  // Get locale for date formatting
  const getLocale = () => {
    switch (language) {
      case 'am':
        return am;
      case 'or':
        return om;
      default:
        return enUS;
    }
  };

  // Format date
  const formatDate = (date) => {
    if (!date) return '';
    return format(new Date(date), 'PPp', { locale: getLocale() });
  };

  // Format relative time
  const formatRelativeTime = (date) => {
    if (!date) return '';
    return formatDistanceToNow(new Date(date), { addSuffix: true, locale: getLocale() });
  };

  // Get notification icon - using the utility function
  const getLocalNotificationIcon = (type, isUnread = false) => {
    // Use the imported utility function
    return getNotificationIcon(type, isUnread);
  };

  // Get notification color
  const getNotificationColor = (type) => {
    switch (type) {
      case 'academic':
        return '#4CAF50'; // Green
      case 'attendance':
        return '#2196F3'; // Blue
      case 'behavioral':
        return '#FF9800'; // Orange
      case 'event':
        return '#9C27B0'; // Purple
      case 'message':
        return '#2196F3'; // Blue
      case 'alert':
        return '#F44336'; // Red
      case 'info':
        return '#03A9F4'; // Light Blue
      case 'success':
        return '#4CAF50'; // Green
      case 'warning':
        return '#FF9800'; // Orange
      default:
        return '#9E9E9E'; // Grey
    }
  };

  // Get priority color
  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'high':
        return '#F44336'; // Red
      case 'urgent':
        return '#D32F2F'; // Dark Red
      case 'normal':
        return '#2196F3'; // Blue
      case 'low':
        return '#4CAF50'; // Green
      default:
        return '#9E9E9E'; // Grey
    }
  };

  // Filter notifications
  const filteredNotifications = notifications.filter(notification => {
    // Apply search filter
    const matchesSearch = searchQuery.trim() === '' ||
      (notification.title && notification.title.toLowerCase().includes(searchQuery.toLowerCase())) ||
      (notification.body && notification.body.toLowerCase().includes(searchQuery.toLowerCase()));

    // Apply type filter
    let matchesType = true;
    if (filterType === 'unread') {
      matchesType = notification.status === 'unread';
    } else if (filterType === 'read') {
      matchesType = notification.status === 'read';
    } else if (filterType.startsWith('type:')) {
      const type = filterType.split(':')[1];
      matchesType = notification.type === type;
    }

    return matchesSearch && matchesType;
  });

  // Get appropriate header based on user role
  const getHeader = () => {
    const title = translate('notifications.title') || 'Notifications';

    switch (userRole) {
      case 'admin':
        return <AdminAppHeader title={title} showNotification={false} />;
      case 'teacher':
        return <TeacherAppHeader title={title} showBackButton={true} />;
      case 'student':
        return <StudentAppHeader title={title} showBackButton={true} />;
      case 'parent':
        return <ParentAppHeader title={title} showBackButton={true} />;
      default:
        return <AdminAppHeader title={title} showNotification={false} />;
    }
  };

  // Render notification item
  const renderNotification = (notification) => (
    <Animatable.View
      key={notification.id}
      animation="fadeIn"
      duration={500}
      delay={200}
    >
      <TouchableOpacity onPress={() => handleNotificationPress(notification)}>
        <Card style={[styles.notificationItem, notification.status === 'unread' && styles.unread]}>
          <Card.Content>
            <View style={styles.notificationHeader}>
              <View style={styles.iconContainer}>
                <Avatar.Icon
                  size={40}
                  icon={getLocalNotificationIcon(notification.type, notification.status === 'unread')}
                  style={[styles.notificationIcon, { backgroundColor: getNotificationColor(notification.type) + '20' }]}
                  color={getNotificationColor(notification.type)}
                />
                {notification.status === 'unread' && (
                  <Badge style={styles.badge} size={8} />
                )}
              </View>
              <View style={styles.titleContainer}>
                <Text style={styles.notificationTitle}>{notification.title}</Text>
                <Text style={styles.timestamp}>{formatRelativeTime(notification.timestamp)}</Text>
              </View>
              <IconButton
                icon="dots-vertical"
                size={20}
                onPress={(e) => {
                  e.stopPropagation();
                  setSelectedNotification(notification);
                  setMenuVisible(true);
                }}
              />
            </View>

            <Text numberOfLines={2} style={styles.notificationMessage}>
              {notification.body}
            </Text>

            <View style={styles.notificationFooter}>
              <Chip
                icon={getLocalNotificationIcon(notification.type, notification.status === 'unread')}
                style={styles.typeChip}
                textStyle={styles.chipText}
              >
                {notification.type.charAt(0).toUpperCase() + notification.type.slice(1)}
              </Chip>

              {notification.priority && notification.priority !== 'normal' && (
                <Chip
                  icon="flag"
                  style={[styles.priorityChip, { borderColor: getPriorityColor(notification.priority) }]}
                  textStyle={[styles.chipText, { color: getPriorityColor(notification.priority) }]}
                >
                  {notification.priority.charAt(0).toUpperCase() + notification.priority.slice(1)}
                </Chip>
              )}
            </View>
          </Card.Content>
        </Card>
      </TouchableOpacity>
    </Animatable.View>
  );

  // Loading state
  if (loading && notifications.length === 0) {
    return (
      <SafeAreaView style={styles.container}>
        {getHeader()}
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={'#1976d2'} />
          <Text style={styles.loadingText}>{translate('common.loading') || 'Loading...'}</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {getHeader()}

      <View style={styles.content}>
        {/* Search Bar */}
        <Animatable.View animation="fadeIn" duration={500}>
          <View style={styles.searchContainer}>
            <Searchbar
              placeholder={translate('notifications.searchPlaceholder') || 'Search notifications...'}
              onChangeText={setSearchQuery}
              value={searchQuery}
              style={styles.searchBar}
              icon="magnify"
              iconColor={'#1976d2'}
            />
            <IconButton
              icon={showFilters ? 'filter-variant' : 'filter-variant-plus'}
              size={24}
              style={styles.filterToggleButton}
              onPress={toggleFilters}
              color={'#1976d2'}
            />
            <IconButton
              icon="cog"
              size={24}
              style={styles.settingsButton}
              onPress={() => navigation.navigate('NotificationSettings')}
              color={'#1976d2'}
            />
          </View>
        </Animatable.View>

        {/* Filter Section */}
        {showFilters && (
          <Animatable.View
            animation={showFilters ? 'fadeIn' : 'fadeOut'}
            duration={300}
            style={styles.filterContainer}
          >
            <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.filterScroll}>
              <Chip
                selected={filterType === 'all'}
                onPress={() => setFilterType('all')}
                style={styles.filterChip}
                icon="filter-variant"
              >
                {translate('notifications.all') || 'All'}
              </Chip>
              <Chip
                selected={filterType === 'unread'}
                onPress={() => setFilterType('unread')}
                style={styles.filterChip}
                icon="email-mark-as-unread"
              >
                {translate('notifications.unread') || 'Unread'}
              </Chip>
              <Chip
                selected={filterType === 'read'}
                onPress={() => setFilterType('read')}
                style={styles.filterChip}
                icon="email-open"
              >
                {translate('notifications.read') || 'Read'}
              </Chip>
              <Chip
                selected={filterType === 'type:academic'}
                onPress={() => setFilterType('type:academic')}
                style={styles.filterChip}
                icon="school"
              >
                {translate('notifications.academic') || 'Academic'}
              </Chip>
              <Chip
                selected={filterType === 'type:attendance'}
                onPress={() => setFilterType('type:attendance')}
                style={styles.filterChip}
                icon="calendar-check"
              >
                {translate('notifications.attendance') || 'Attendance'}
              </Chip>
              <Chip
                selected={filterType === 'type:behavioral'}
                onPress={() => setFilterType('type:behavioral')}
                style={styles.filterChip}
                icon="account-alert"
              >
                {translate('notifications.behavioral') || 'Behavioral'}
              </Chip>
              <Chip
                selected={filterType === 'type:event'}
                onPress={() => setFilterType('type:event')}
                style={styles.filterChip}
                icon="calendar"
              >
                {translate('notifications.events') || 'Events'}
              </Chip>
              <Chip
                selected={filterType === 'type:message'}
                onPress={() => setFilterType('type:message')}
                style={styles.filterChip}
                icon="message-text"
              >
                {translate('notifications.messages') || 'Messages'}
              </Chip>
            </ScrollView>
          </Animatable.View>
        )}

        {/* Stats Bar */}
        <View style={styles.statsContainer}>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{notifications.length}</Text>
            <Text style={styles.statLabel}>{translate('notifications.total') || 'Total'}</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{notifications.filter(n => n.status === 'unread').length}</Text>
            <Text style={styles.statLabel}>{translate('notifications.unread') || 'Unread'}</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{notifications.filter(n => n.status === 'read').length}</Text>
            <Text style={styles.statLabel}>{translate('notifications.read') || 'Read'}</Text>
          </View>
        </View>

        {/* Mark All as Read Button */}
        {unreadCount > 0 && (
          <Button
            mode="outlined"
            onPress={handleMarkAllAsRead}
            style={styles.markAllButton}
            icon="check-all"
          >
            {translate('notifications.markAllRead') || 'Mark All as Read'}
          </Button>
        )}

        {/* Notifications List */}
        <ScrollView
          style={styles.scrollView}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={['#1976d2']}
              tintColor={'#1976d2'}
            />
          }
        >
          {filteredNotifications.length === 0 ? (
            <Animatable.View animation="fadeIn" duration={500} style={styles.emptyContainer}>
              <IconButton icon="bell-off" size={50} color={'#9e9e9e'} />
              <Text style={styles.emptyText}>
                {searchQuery || filterType !== 'all' ?
                  (translate('notifications.noMatchingNotifications') || 'No matching notifications') :
                  (translate('notifications.noNotifications') || 'No notifications yet')}
              </Text>
              <Text style={styles.emptySubtext}>
                {searchQuery || filterType !== 'all' ?
                  (translate('notifications.tryDifferentFilters') || 'Try different search terms or filters') :
                  (translate('notifications.checkBackLater') || 'Check back later for new notifications')}
              </Text>
            </Animatable.View>
          ) : (
            filteredNotifications.map(renderNotification)
          )}

          {/* Add some padding at the bottom */}
          <View style={styles.bottomPadding} />
        </ScrollView>
      </View>

      {/* Notification Detail Modal */}
      <Portal>
        <Modal
          visible={detailModalVisible}
          onDismiss={() => setDetailModalVisible(false)}
          contentContainerStyle={styles.modalContent}
        >
          {selectedNotification && (
            <ScrollView>
              <View style={styles.modalHeader}>
                <Avatar.Icon
                  size={40}
                  icon={getNotificationIcon(selectedNotification.type)}
                  style={[styles.notificationIcon, { backgroundColor: getNotificationColor(selectedNotification.type) + '20' }]}
                  color={getNotificationColor(selectedNotification.type)}
                />
                <Title style={styles.modalTitle}>{selectedNotification.title}</Title>
                <IconButton
                  icon="close"
                  size={24}
                  onPress={() => setDetailModalVisible(false)}
                />
              </View>

              <Divider style={styles.modalDivider} />

              <View style={styles.modalBody}>
                <Paragraph style={styles.modalMessage}>{selectedNotification.body}</Paragraph>

                <View style={styles.modalInfo}>
                  <Chip icon="clock" style={styles.modalChip}>
                    {formatDate(selectedNotification.timestamp)}
                  </Chip>
                  <Chip
                    icon={selectedNotification.status === 'read' ? 'eye' : 'eye-off'}
                    style={styles.modalChip}
                  >
                    {selectedNotification.status === 'read' ?
                      (translate('notifications.read') || 'Read') :
                      (translate('notifications.unread') || 'Unread')}
                  </Chip>
                  <Chip
                    icon={getLocalNotificationIcon(selectedNotification.type, selectedNotification.status === 'unread')}
                    style={styles.modalChip}
                  >
                    {selectedNotification.type.charAt(0).toUpperCase() + selectedNotification.type.slice(1)}
                  </Chip>

                  {selectedNotification.priority && selectedNotification.priority !== 'normal' && (
                    <Chip
                      icon="flag"
                      style={[styles.modalChip, { borderColor: getPriorityColor(selectedNotification.priority) }]}
                      textStyle={{ color: getPriorityColor(selectedNotification.priority) }}
                    >
                      {selectedNotification.priority.charAt(0).toUpperCase() + selectedNotification.priority.slice(1)}
                    </Chip>
                  )}
                </View>

                {selectedNotification.data && Object.keys(selectedNotification.data).length > 0 && (
                  <View style={styles.modalDataSection}>
                    <Title style={styles.modalSectionTitle}>
                      {translate('notifications.additionalInfo') || 'Additional Information'}
                    </Title>
                    <Card style={styles.modalDataCard}>
                      <Card.Content>
                        {Object.entries(selectedNotification.data)
                          .filter(([key]) => key !== 'route' && key !== 'params' && key !== 'channel' && key !== 'notificationId')
                          .map(([key, value]) => (
                            <View key={key} style={styles.modalDataItem}>
                              <Text style={styles.modalDataKey}>{key.charAt(0).toUpperCase() + key.slice(1).replace(/([A-Z])/g, ' $1')}:</Text>
                              <Text style={styles.modalDataValue}>
                                {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                              </Text>
                            </View>
                          ))}
                      </Card.Content>
                    </Card>
                  </View>
                )}

                {selectedNotification.data?.route && (
                  <Button
                    mode="contained"
                    onPress={() => {
                      setDetailModalVisible(false);
                      navigation.navigate(selectedNotification.data.route, selectedNotification.data.params);
                    }}
                    style={styles.modalActionButton}
                    icon="arrow-right"
                  >
                    {translate('notifications.goToRelatedScreen') || 'Go to Related Screen'}
                  </Button>
                )}
              </View>
            </ScrollView>
          )}
        </Modal>
      </Portal>

      {/* Notification Action Menu */}
      <Portal>
        <Menu
          visible={menuVisible}
          onDismiss={() => setMenuVisible(false)}
          anchor={{ x: 0, y: 0 }}
          style={styles.menu}
        >
          <Menu.Item
            icon="eye"
            onPress={() => {
              setMenuVisible(false);
              if (selectedNotification) {
                markAsRead(selectedNotification.id);
              }
            }}
            title={translate('notifications.markAsRead') || 'Mark as Read'}
            disabled={selectedNotification?.status === 'read'}
          />
          <Menu.Item
            icon="information"
            onPress={() => {
              setMenuVisible(false);
              setDetailModalVisible(true);
            }}
            title={translate('notifications.viewDetails') || 'View Details'}
          />
        </Menu>
      </Portal>

      {/* Snackbar */}
      <Snackbar
        visible={snackbarVisible}
        onDismiss={() => setSnackbarVisible(false)}
        duration={3000}
        style={styles.snackbar}
        action={{
          label: translate('common.dismiss') || 'Dismiss',
          onPress: () => setSnackbarVisible(false),
        }}
      >
        {snackbarMessage}
      </Snackbar>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    margin: 16,
    marginBottom: 8,
  },
  searchBar: {
    flex: 1,
    elevation: 4,
    borderRadius: 25,
    height: 50,
  },
  filterToggleButton: {
    marginLeft: 8,
    backgroundColor: '#E3F2FD',
  },
  settingsButton: {
    marginLeft: 8,
    backgroundColor: '#E3F2FD',
  },
  filterContainer: {
    marginHorizontal: 16,
    marginBottom: 8,
  },
  filterScroll: {
    flexDirection: 'row',
  },
  filterChip: {
    marginRight: 8,
    marginBottom: 8,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginHorizontal: 16,
    marginBottom: 16,
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 16,
    elevation: 2,
  },
  statItem: {
    alignItems: 'center',
    flex: 1,
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2196F3',
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
  },
  markAllButton: {
    marginHorizontal: 16,
    marginBottom: 16,
  },
  scrollView: {
    flex: 1,
  },
  notificationItem: {
    marginHorizontal: 16,
    marginVertical: 8,
    borderRadius: 8,
    elevation: 2,
  },
  unread: {
    backgroundColor: '#E3F2FD',
  },
  notificationHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  iconContainer: {
    position: 'relative',
    marginRight: 12,
  },
  notificationIcon: {
    margin: 0,
  },
  badge: {
    position: 'absolute',
    top: 0,
    right: 0,
    backgroundColor: '#f44336',
  },
  titleContainer: {
    flex: 1,
  },
  notificationTitle: {
    fontWeight: 'bold',
    fontSize: 16,
    marginBottom: 4,
  },
  timestamp: {
    fontSize: 12,
    color: '#666',
  },
  notificationMessage: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  notificationFooter: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  typeChip: {
    marginRight: 8,
    marginBottom: 4,
    backgroundColor: '#E0E0E0',
  },
  priorityChip: {
    marginRight: 8,
    marginBottom: 4,
    backgroundColor: 'transparent',
    borderWidth: 1,
  },
  chipText: {
    fontSize: 12,
  },
  emptyContainer: {
    padding: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyText: {
    textAlign: 'center',
    marginTop: 16,
    fontSize: 18,
    fontWeight: 'bold',
    color: '#666',
  },
  emptySubtext: {
    textAlign: 'center',
    marginTop: 8,
    fontSize: 14,
    color: '#888',
  },
  bottomPadding: {
    height: 80,
  },
  modalContent: {
    backgroundColor: 'white',
    margin: 20,
    borderRadius: 8,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#f5f5f5',
  },
  modalTitle: {
    flex: 1,
    marginLeft: 16,
    fontSize: 18,
  },
  modalDivider: {
    height: 1,
  },
  modalBody: {
    padding: 16,
  },
  modalMessage: {
    fontSize: 16,
    lineHeight: 24,
    marginBottom: 16,
  },
  modalInfo: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 16,
  },
  modalChip: {
    marginRight: 8,
    marginBottom: 8,
  },
  modalDataSection: {
    marginTop: 16,
    marginBottom: 16,
  },
  modalSectionTitle: {
    fontSize: 16,
    marginBottom: 8,
  },
  modalDataCard: {
    elevation: 1,
  },
  modalDataItem: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  modalDataKey: {
    fontWeight: 'bold',
    marginRight: 8,
    flex: 1,
  },
  modalDataValue: {
    flex: 2,
  },
  modalActionButton: {
    marginTop: 16,
  },
  menu: {
    elevation: 4,
  },
  snackbar: {
    bottom: 16,
  },
});

export default NotificationCenter;
