import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/user_model.dart';
import '../utils/error_handler.dart';

class FirebaseAuthService {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Get current user
  User? get currentUser => _auth.currentUser;

  // Auth state changes stream
  Stream<User?> get authStateChanges => _auth.authStateChanges();

  // Sign in with email and password
  Future<Map<String, dynamic>> signIn(String email, String password) async {
    try {
      final credential = await _auth.signInWithEmailAndPassword(
        email: email.trim(),
        password: password,
      );

      if (credential.user != null) {
        // Get user data from Firestore
        final userDoc = await _firestore
            .collection('users')
            .doc(credential.user!.uid)
            .get();

        if (userDoc.exists) {
          final userData = UserModel.fromMap(userDoc.data()!);
          return {
            'success': true,
            'user': credential.user,
            'userData': userData,
          };
        } else {
          return {
            'success': false,
            'error': 'User data not found. Please contact administrator.',
          };
        }
      } else {
        return {
          'success': false,
          'error': 'Authentication failed.',
        };
      }
    } on FirebaseAuthException catch (e) {
      return {
        'success': false,
        'error': _getAuthErrorMessage(e.code),
      };
    } catch (e) {
      ErrorHandler.reportError(e, StackTrace.current);
      return {
        'success': false,
        'error': 'An unexpected error occurred. Please try again.',
      };
    }
  }

  // Sign up with email and password
  Future<Map<String, dynamic>> signUp({
    required String email,
    required String password,
    required String displayName,
    required String role,
    Map<String, dynamic>? additionalData,
  }) async {
    try {
      final credential = await _auth.createUserWithEmailAndPassword(
        email: email.trim(),
        password: password,
      );

      if (credential.user != null) {
        // Update user profile
        await credential.user!.updateDisplayName(displayName);

        // Create user document in Firestore
        final userData = UserModel(
          id: credential.user!.uid,
          email: email.trim(),
          displayName: displayName,
          role: role,
          emailVerified: credential.user!.emailVerified,
          status: 'active',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          additionalData: additionalData ?? {},
        );

        await _firestore
            .collection('users')
            .doc(credential.user!.uid)
            .set(userData.toMap());

        // Create role-specific document
        await _createRoleSpecificDocument(credential.user!.uid, role, additionalData);

        // Send email verification
        await credential.user!.sendEmailVerification();

        return {
          'success': true,
          'user': credential.user,
          'userData': userData,
        };
      } else {
        return {
          'success': false,
          'error': 'Account creation failed.',
        };
      }
    } on FirebaseAuthException catch (e) {
      return {
        'success': false,
        'error': _getAuthErrorMessage(e.code),
      };
    } catch (e) {
      ErrorHandler.reportError(e, StackTrace.current);
      return {
        'success': false,
        'error': 'An unexpected error occurred. Please try again.',
      };
    }
  }

  // Sign out
  Future<void> signOut() async {
    try {
      await _auth.signOut();
    } catch (e) {
      ErrorHandler.reportError(e, StackTrace.current);
      rethrow;
    }
  }

  // Reset password
  Future<void> resetPassword(String email) async {
    try {
      await _auth.sendPasswordResetEmail(email: email.trim());
    } on FirebaseAuthException catch (e) {
      throw Exception(_getAuthErrorMessage(e.code));
    } catch (e) {
      ErrorHandler.reportError(e, StackTrace.current);
      throw Exception('An unexpected error occurred. Please try again.');
    }
  }

  // Update profile
  Future<Map<String, dynamic>> updateProfile({
    String? displayName,
    String? photoURL,
    Map<String, dynamic>? additionalData,
  }) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return {
          'success': false,
          'error': 'No user is currently signed in.',
        };
      }

      // Update Firebase Auth profile
      if (displayName != null) {
        await user.updateDisplayName(displayName);
      }
      if (photoURL != null) {
        await user.updatePhotoURL(photoURL);
      }

      // Update Firestore document
      final updateData = <String, dynamic>{
        'updatedAt': Timestamp.fromDate(DateTime.now()),
      };

      if (displayName != null) {
        updateData['displayName'] = displayName;
      }
      if (photoURL != null) {
        updateData['photoURL'] = photoURL;
      }
      if (additionalData != null) {
        updateData['additionalData'] = additionalData;
      }

      await _firestore.collection('users').doc(user.uid).update(updateData);

      return {
        'success': true,
        'message': 'Profile updated successfully.',
      };
    } catch (e) {
      ErrorHandler.reportError(e, StackTrace.current);
      return {
        'success': false,
        'error': 'Failed to update profile. Please try again.',
      };
    }
  }

  // Verify email
  Future<void> sendEmailVerification() async {
    try {
      final user = _auth.currentUser;
      if (user != null && !user.emailVerified) {
        await user.sendEmailVerification();
      }
    } catch (e) {
      ErrorHandler.reportError(e, StackTrace.current);
      rethrow;
    }
  }

  // Change password
  Future<Map<String, dynamic>> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return {
          'success': false,
          'error': 'No user is currently signed in.',
        };
      }

      // Re-authenticate user
      final credential = EmailAuthProvider.credential(
        email: user.email!,
        password: currentPassword,
      );
      await user.reauthenticateWithCredential(credential);

      // Update password
      await user.updatePassword(newPassword);

      return {
        'success': true,
        'message': 'Password changed successfully.',
      };
    } on FirebaseAuthException catch (e) {
      return {
        'success': false,
        'error': _getAuthErrorMessage(e.code),
      };
    } catch (e) {
      ErrorHandler.reportError(e, StackTrace.current);
      return {
        'success': false,
        'error': 'Failed to change password. Please try again.',
      };
    }
  }

  // Create role-specific document
  Future<void> _createRoleSpecificDocument(
    String userId,
    String role,
    Map<String, dynamic>? additionalData,
  ) async {
    final roleData = {
      'userId': userId,
      'createdAt': Timestamp.fromDate(DateTime.now()),
      ...?additionalData,
    };

    switch (role) {
      case 'admin':
        await _firestore.collection('admins').doc(userId).set(roleData);
        break;
      case 'teacher':
        await _firestore.collection('teachers').doc(userId).set(roleData);
        break;
      case 'student':
        await _firestore.collection('students').doc(userId).set(roleData);
        break;
      case 'parent':
        await _firestore.collection('parents').doc(userId).set(roleData);
        break;
    }
  }

  // Get auth error message
  String _getAuthErrorMessage(String errorCode) {
    switch (errorCode) {
      case 'user-not-found':
        return 'No user found with this email address.';
      case 'wrong-password':
        return 'Incorrect password.';
      case 'email-already-in-use':
        return 'An account already exists with this email address.';
      case 'weak-password':
        return 'Password is too weak. Please choose a stronger password.';
      case 'invalid-email':
        return 'Invalid email address.';
      case 'user-disabled':
        return 'This account has been disabled.';
      case 'too-many-requests':
        return 'Too many failed attempts. Please try again later.';
      case 'operation-not-allowed':
        return 'This operation is not allowed.';
      case 'requires-recent-login':
        return 'Please sign in again to complete this action.';
      default:
        return 'Authentication failed. Please try again.';
    }
  }
}
