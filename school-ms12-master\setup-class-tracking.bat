@echo off
echo === Class Tracking Setup Tool ===
echo This batch file will help you set up and test the class tracking features.
echo.

REM Get the current directory with quotes to handle spaces
set "CURRENT_DIR=%~dp0"
echo Current directory: %CURRENT_DIR%

REM Run the setup script with node
echo Running setup script...
node "%CURRENT_DIR%setup-class-tracking.js"

echo.
echo Setup process completed.
echo Press any key to exit...
pause > nul
