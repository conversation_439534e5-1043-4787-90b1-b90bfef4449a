import React, { useState, useEffect } from 'react';
import { View, ScrollView, StyleSheet } from 'react-native';
import { Card, Title, Text, FAB, Portal, Modal, List, Chip } from 'react-native-paper';
import { Calendar } from 'react-native-calendars';
import { db } from '../../config/firebase';
import { collection, addDoc, query, getDocs, doc, updateDoc, deleteDoc, where } from 'firebase/firestore';
import { useAuth } from '../../context/AuthContext';
import { useLanguage } from '../../context/LanguageContext';
import CustomButton from '../../components/common/CustomButton';
import CustomInput from '../../components/common/CustomInput';
import EthiopianCalendar from '../../utils/EthiopianCalendar';
import EthiopianDatePicker from '../../components/common/EthiopianDatePicker';
import EthiopianTimePicker from '../../components/common/EthiopianTimePicker';

const CalendarManagement = () => {
  const { language } = useLanguage();
  const { user } = useAuth();
  const [events, setEvents] = useState([]);
  const [visible, setVisible] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState(null);
  const [selectedDate, setSelectedDate] = useState('');
  const [loading, setLoading] = useState(false);

  const [formData, setFormData] = useState({
    title: '',
    description: '',
    date: new Date().toISOString().split('T')[0],
    startTime: '09:00',
    endTime: '10:00',
    type: 'personal', // personal, class, exam, meeting
    location: '',
    participants: [],
    reminder: '15', // minutes before
  });

  useEffect(() => {
    fetchEvents();
  }, []);

  const fetchEvents = async () => {
    try {
      const eventsRef = collection(db, 'events');
      const q = query(eventsRef, where('userId', '==', user.uid));
      const querySnapshot = await getDocs(q);

      const eventsData = [];
      querySnapshot.forEach((doc) => {
        eventsData.push({ id: doc.id, ...doc.data() });
      });

      setEvents(eventsData);
    } catch (error) {
      console.error('Error fetching events:', error);
    }
  };

  const handleAddEvent = async () => {
    try {
      setLoading(true);
      const eventsRef = collection(db, 'events');
      await addDoc(eventsRef, {
        ...formData,
        userId: user.uid,
        createdAt: new Date().toISOString(),
      });

      setVisible(false);
      resetForm();
      fetchEvents();
    } catch (error) {
      console.error('Error adding event:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateEvent = async () => {
    try {
      setLoading(true);
      const eventRef = doc(db, 'events', selectedEvent.id);
      await updateDoc(eventRef, {
        ...formData,
        updatedAt: new Date().toISOString(),
      });

      setVisible(false);
      resetForm();
      fetchEvents();
    } catch (error) {
      console.error('Error updating event:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteEvent = async (eventId) => {
    try {
      await deleteDoc(doc(db, 'events', eventId));
      fetchEvents();
    } catch (error) {
      console.error('Error deleting event:', error);
    }
  };

  const resetForm = () => {
    setFormData({
      title: '',
      description: '',
      date: new Date().toISOString().split('T')[0],
      startTime: '09:00',
      endTime: '10:00',
      type: 'personal',
      location: '',
      participants: [],
      reminder: '15',
    });
    setSelectedEvent(null);
  };

  const getMarkedDates = () => {
    const marked = {};
    events.forEach(event => {
      marked[event.date] = {
        marked: true,
        dotColor:
          event.type === 'personal' ? '#2196F3' :
          event.type === 'class' ? '#4CAF50' :
          event.type === 'exam' ? '#F44336' :
          '#FFC107',
      };
    });
    return marked;
  };

  const getDayEvents = (date) => {
    return events.filter(event => event.date === date);
  };

  return (
    <View style={styles.container}>
      <Calendar
        onDayPress={day => setSelectedDate(day.dateString)}
        markedDates={{
          ...getMarkedDates(),
          [selectedDate]: {
            ...getMarkedDates()[selectedDate],
            selected: true,
          },
        }}
        theme={{
          selectedDayBackgroundColor: '#2196F3',
          todayTextColor: '#2196F3',
          arrowColor: '#2196F3',
        }}
      />

      {selectedDate && (
        <Card style={styles.eventsCard}>
          <Card.Content>
            <Title>Events for {EthiopianCalendar.formatDate(new Date(selectedDate), language)}</Title>
            {getDayEvents(selectedDate).map((event) => (
              <List.Item
                key={event.id}
                title={event.title}
                description={`${event.startTime} - ${event.endTime}`}
                left={props => (
                  <List.Icon
                    {...props}
                    icon={
                      event.type === 'personal' ? 'account' :
                      event.type === 'class' ? 'school' :
                      event.type === 'exam' ? 'file-document' :
                      'account-group'
                    }
                  />
                )}
                right={() => (
                  <View style={styles.eventActions}>
                    <CustomButton
                      mode="text"
                      onPress={() => {
                        setSelectedEvent(event);
                        setFormData({
                          title: event.title,
                          description: event.description,
                          date: event.date,
                          startTime: event.startTime,
                          endTime: event.endTime,
                          type: event.type,
                          location: event.location,
                          participants: event.participants,
                          reminder: event.reminder,
                        });
                        setVisible(true);
                      }}
                    >
                      Edit
                    </CustomButton>
                    <CustomButton
                      mode="text"
                      onPress={() => handleDeleteEvent(event.id)}
                    >
                      Delete
                    </CustomButton>
                  </View>
                )}
              />
            ))}
          </Card.Content>
        </Card>
      )}

      <Portal>
        <Modal
          visible={visible}
          onDismiss={() => {
            setVisible(false);
            resetForm();
          }}
          contentContainerStyle={styles.modalContent}
        >
          <ScrollView>
            <Title>{selectedEvent ? 'Edit Event' : 'Add New Event'}</Title>

            <CustomInput
              label="Title"
              value={formData.title}
              onChangeText={(text) => setFormData({ ...formData, title: text })}
            />

            <CustomInput
              label="Description"
              value={formData.description}
              onChangeText={(text) => setFormData({ ...formData, description: text })}
              multiline
              numberOfLines={3}
            />

            <View style={styles.datePickerContainer}>
              <Text style={styles.datePickerLabel}>Date</Text>
              <EthiopianDatePicker
                value={formData.date ? new Date(formData.date) : new Date()}
                onChange={(date) => {
                  if (date) {
                    setFormData({ ...formData, date: date.toISOString().split('T')[0] });
                  }
                }}
                label="Select Event Date"
                language={language}
                display="calendar"
                themeType="light"
                showWeekNumbers={true}
                showYearMonthSelector={true}
              />
            </View>

            <View style={styles.timeContainer}>
              <View style={styles.timeInput}>
                <Text style={styles.datePickerLabel}>Start Time</Text>
                <EthiopianTimePicker
                  value={formData.startTime ? new Date(`2023-01-01T${formData.startTime}:00`) : new Date()}
                  onChange={(time) => {
                    if (time) {
                      const hours = time.getHours().toString().padStart(2, '0');
                      const minutes = time.getMinutes().toString().padStart(2, '0');
                      setFormData({ ...formData, startTime: `${hours}:${minutes}` });
                    }
                  }}
                  label="Select Start Time"
                  display="spinner"
                  is24Hour={true}
                  themeType="light"
                />
              </View>
              <View style={styles.timeInput}>
                <Text style={styles.datePickerLabel}>End Time</Text>
                <EthiopianTimePicker
                  value={formData.endTime ? new Date(`2023-01-01T${formData.endTime}:00`) : new Date()}
                  onChange={(time) => {
                    if (time) {
                      const hours = time.getHours().toString().padStart(2, '0');
                      const minutes = time.getMinutes().toString().padStart(2, '0');
                      setFormData({ ...formData, endTime: `${hours}:${minutes}` });
                    }
                  }}
                  label="Select End Time"
                  display="clock"
                  is24Hour={true}
                  themeType="light"
                />
              </View>
            </View>

            <List.Section title="Event Type">
              {['personal', 'class', 'exam', 'meeting'].map((type) => (
                <List.Item
                  key={type}
                  title={type.charAt(0).toUpperCase() + type.slice(1)}
                  left={props => (
                    <List.Icon
                      {...props}
                      icon={
                        type === 'personal' ? 'account' :
                        type === 'class' ? 'school' :
                        type === 'exam' ? 'file-document' :
                        'account-group'
                      }
                    />
                  )}
                  onPress={() => setFormData({ ...formData, type })}
                  style={formData.type === type ? styles.selectedItem : null}
                />
              ))}
            </List.Section>

            <CustomInput
              label="Location"
              value={formData.location}
              onChangeText={(text) => setFormData({ ...formData, location: text })}
            />

            <CustomInput
              label="Reminder (minutes before)"
              value={formData.reminder}
              onChangeText={(text) => setFormData({ ...formData, reminder: text })}
              keyboardType="numeric"
            />

            <View style={styles.modalButtons}>
              <CustomButton
                mode="contained"
                onPress={selectedEvent ? handleUpdateEvent : handleAddEvent}
                loading={loading}
              >
                {selectedEvent ? 'Update' : 'Add'}
              </CustomButton>

              {selectedEvent && (
                <CustomButton
                  mode="outlined"
                  onPress={() => handleDeleteEvent(selectedEvent.id)}
                  style={styles.deleteButton}
                >
                  Delete
                </CustomButton>
              )}

              <CustomButton
                mode="outlined"
                onPress={() => {
                  setVisible(false);
                  resetForm();
                }}
              >
                Cancel
              </CustomButton>
            </View>
          </ScrollView>
        </Modal>
      </Portal>

      <FAB
        style={styles.fab}
        icon="plus"
        onPress={() => {
          setFormData({ ...formData, date: selectedDate || formData.date });
          setVisible(true);
        }}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  eventsCard: {
    margin: 10,
  },
  modalContent: {
    backgroundColor: 'white',
    padding: 20,
    margin: 20,
    borderRadius: 10,
    maxHeight: '80%',
  },
  selectedItem: {
    backgroundColor: '#e8f4f8',
  },
  timeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  timeInput: {
    flex: 1,
    marginHorizontal: 5,
  },
  datePickerContainer: {
    marginBottom: 16,
  },
  datePickerLabel: {
    fontSize: 14,
    marginBottom: 8,
    color: '#666',
  },
  eventActions: {
    flexDirection: 'row',
  },
  modalButtons: {
    marginTop: 20,
  },
  deleteButton: {
    marginVertical: 10,
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
  },
});

export default CalendarManagement;
