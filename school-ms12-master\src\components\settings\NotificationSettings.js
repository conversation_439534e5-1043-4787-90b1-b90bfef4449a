import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView, Alert, Platform } from 'react-native';
import { Card, Title, Switch, Text, Button, Divider, List, useTheme } from 'react-native-paper';
import { useNotifications } from '../../context/NotificationContext';
import { useLanguage } from '../../context/LanguageContext';
import { auth, db } from '../../config/firebase';
import { doc, getDoc, updateDoc } from 'firebase/firestore';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Linking } from 'react-native';

const NotificationSettings = () => {
  const { permissionStatus, requestPermissionAgain, isInitialized } = useNotifications();
  const { translate } = useLanguage();
  // No theme needed

  // Notification preferences state
  const [preferences, setPreferences] = useState({
    academic: true,
    attendance: true,
    behavioral: true,
    event: true,
    message: true,
    general: true,
    emailNotifications: true,
    smsNotifications: false,
    pushNotifications: true,
    notificationSound: true,
    notificationVibration: true,
  });

  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  // Fetch user's notification preferences
  useEffect(() => {
    fetchNotificationPreferences();
  }, []);

  // Fetch notification preferences from Firestore
  const fetchNotificationPreferences = async () => {
    try {
      setLoading(true);

      if (!auth.currentUser) {
        // Use default preferences if not logged in
        setLoading(false);
        return;
      }

      const userRef = doc(db, 'users', auth.currentUser.uid);
      const userDoc = await getDoc(userRef);

      if (userDoc.exists() && userDoc.data().notificationPreferences) {
        setPreferences({
          ...preferences,
          ...userDoc.data().notificationPreferences,
        });
      } else {
        // Save default preferences if none exist
        await updateDoc(userRef, {
          notificationPreferences: preferences,
        });
      }
    } catch (error) {
      console.error('Error fetching notification preferences:', error);
    } finally {
      setLoading(false);
    }
  };

  // Save notification preferences to Firestore
  const saveNotificationPreferences = async () => {
    try {
      setSaving(true);

      if (!auth.currentUser) {
        Alert.alert(
          translate('notifications.error') || 'Error',
          translate('notifications.loginRequired') || 'You must be logged in to save preferences'
        );
        return;
      }

      const userRef = doc(db, 'users', auth.currentUser.uid);
      await updateDoc(userRef, {
        notificationPreferences: preferences,
        updatedAt: new Date().toISOString(),
      });

      // Save to AsyncStorage as well for offline access
      await AsyncStorage.setItem('notificationPreferences', JSON.stringify(preferences));

      Alert.alert(
        translate('notifications.success') || 'Success',
        translate('notifications.preferencesSaved') || 'Notification preferences saved successfully'
      );
    } catch (error) {
      console.error('Error saving notification preferences:', error);
      Alert.alert(
        translate('notifications.error') || 'Error',
        translate('notifications.errorSavingPreferences') || 'Failed to save notification preferences'
      );
    } finally {
      setSaving(false);
    }
  };

  // Toggle a specific preference
  const togglePreference = (key) => {
    setPreferences({
      ...preferences,
      [key]: !preferences[key],
    });
  };

  // Open app settings to enable notifications
  const openAppSettings = async () => {
    try {
      if (Platform.OS === 'ios') {
        await Linking.openURL('app-settings:');
      } else {
        await Linking.openSettings();
      }
    } catch (error) {
      console.error('Error opening settings:', error);
      Alert.alert(
        translate('notifications.error') || 'Error',
        translate('notifications.settingsError') || 'Could not open settings'
      );
    }
  };

  // Render permission status section
  const renderPermissionStatus = () => {
    if (!isInitialized) {
      return (
        <Card style={styles.card}>
          <Card.Content>
            <Title style={styles.title}>
              {translate('notifications.permissionStatus') || 'Notification Permission'}
            </Title>
            <Text style={styles.loadingText}>
              {translate('notifications.initializing') || 'Initializing notifications...'}
            </Text>
          </Card.Content>
        </Card>
      );
    }

    if (permissionStatus === 'granted') {
      return (
        <Card style={styles.card}>
          <Card.Content>
            <Title style={styles.title}>
              {translate('notifications.permissionStatus') || 'Notification Permission'}
            </Title>
            <Text style={styles.permissionGranted}>
              {translate('notifications.permissionGranted') || 'Notifications are enabled'}
            </Text>
          </Card.Content>
        </Card>
      );
    }

    return (
      <Card style={styles.card}>
        <Card.Content>
          <Title style={styles.title}>
            {translate('notifications.permissionStatus') || 'Notification Permission'}
          </Title>
          <Text style={styles.permissionDenied}>
            {translate('notifications.permissionDenied') || 'Notifications are disabled'}
          </Text>
          <Text style={styles.permissionHint}>
            {translate('notifications.permissionHint') || 'Enable notifications to receive important updates'}
          </Text>
          <View style={styles.buttonContainer}>
            <Button
              mode="contained"
              onPress={requestPermissionAgain}
              style={styles.button}
            >
              {translate('notifications.requestPermission') || 'Request Permission'}
            </Button>
            <Button
              mode="outlined"
              onPress={openAppSettings}
              style={styles.button}
            >
              {translate('notifications.openSettings') || 'Open Settings'}
            </Button>
          </View>
        </Card.Content>
      </Card>
    );
  };

  return (
    <ScrollView style={styles.container}>
      {renderPermissionStatus()}

      <Card style={styles.card}>
        <Card.Content>
          <Title style={styles.title}>
            {translate('notifications.notificationTypes') || 'Notification Types'}
          </Title>
          <Text style={styles.subtitle}>
            {translate('notifications.typesDescription') || 'Choose which types of notifications you want to receive'}
          </Text>

          <List.Item
            title={translate('notifications.academic') || 'Academic'}
            description={translate('notifications.academicDescription') || 'Grades, assignments, and academic updates'}
            left={props => <List.Icon {...props} icon="school" />}
            right={props => (
              <Switch
                value={preferences.academic}
                onValueChange={() => togglePreference('academic')}
                color={'#1976d2'}
              />
            )}
          />
          <Divider />

          <List.Item
            title={translate('notifications.attendance') || 'Attendance'}
            description={translate('notifications.attendanceDescription') || 'Attendance records and absences'}
            left={props => <List.Icon {...props} icon="calendar-check" />}
            right={props => (
              <Switch
                value={preferences.attendance}
                onValueChange={() => togglePreference('attendance')}
                color={'#1976d2'}
              />
            )}
          />
          <Divider />

          <List.Item
            title={translate('notifications.behavioral') || 'Behavioral'}
            description={translate('notifications.behavioralDescription') || 'Behavior reports and disciplinary notices'}
            left={props => <List.Icon {...props} icon="account-alert" />}
            right={props => (
              <Switch
                value={preferences.behavioral}
                onValueChange={() => togglePreference('behavioral')}
                color={'#1976d2'}
              />
            )}
          />
          <Divider />

          <List.Item
            title={translate('notifications.event') || 'Events'}
            description={translate('notifications.eventDescription') || 'School events, holidays, and important dates'}
            left={props => <List.Icon {...props} icon="calendar" />}
            right={props => (
              <Switch
                value={preferences.event}
                onValueChange={() => togglePreference('event')}
                color={'#1976d2'}
              />
            )}
          />
          <Divider />

          <List.Item
            title={translate('notifications.message') || 'Messages'}
            description={translate('notifications.messageDescription') || 'Direct messages and communications'}
            left={props => <List.Icon {...props} icon="message" />}
            right={props => (
              <Switch
                value={preferences.message}
                onValueChange={() => togglePreference('message')}
                color={'#1976d2'}
              />
            )}
          />
          <Divider />

          <List.Item
            title={translate('notifications.general') || 'General'}
            description={translate('notifications.generalDescription') || 'General announcements and updates'}
            left={props => <List.Icon {...props} icon="bell" />}
            right={props => (
              <Switch
                value={preferences.general}
                onValueChange={() => togglePreference('general')}
                color={'#1976d2'}
              />
            )}
          />
        </Card.Content>
      </Card>

      <Card style={styles.card}>
        <Card.Content>
          <Title style={styles.title}>
            {translate('notifications.deliveryMethods') || 'Delivery Methods'}
          </Title>
          <Text style={styles.subtitle}>
            {translate('notifications.deliveryDescription') || 'Choose how you want to receive notifications'}
          </Text>

          <List.Item
            title={translate('notifications.pushNotifications') || 'Push Notifications'}
            description={translate('notifications.pushDescription') || 'Receive notifications on your device'}
            left={props => <List.Icon {...props} icon="cellphone" />}
            right={props => (
              <Switch
                value={preferences.pushNotifications}
                onValueChange={() => togglePreference('pushNotifications')}
                color={'#1976d2'}
              />
            )}
          />
          <Divider />

          <List.Item
            title={translate('notifications.emailNotifications') || 'Email Notifications'}
            description={translate('notifications.emailDescription') || 'Receive notifications via email'}
            left={props => <List.Icon {...props} icon="email" />}
            right={props => (
              <Switch
                value={preferences.emailNotifications}
                onValueChange={() => togglePreference('emailNotifications')}
                color={'#1976d2'}
              />
            )}
          />
          <Divider />

          <List.Item
            title={translate('notifications.smsNotifications') || 'SMS Notifications'}
            description={translate('notifications.smsDescription') || 'Receive notifications via SMS'}
            left={props => <List.Icon {...props} icon="message-text" />}
            right={props => (
              <Switch
                value={preferences.smsNotifications}
                onValueChange={() => togglePreference('smsNotifications')}
                color={'#1976d2'}
              />
            )}
          />
        </Card.Content>
      </Card>

      <Card style={styles.card}>
        <Card.Content>
          <Title style={styles.title}>
            {translate('notifications.notificationSettings') || 'Notification Settings'}
          </Title>
          <Text style={styles.subtitle}>
            {translate('notifications.settingsDescription') || 'Customize your notification experience'}
          </Text>

          <List.Item
            title={translate('notifications.sound') || 'Notification Sound'}
            description={translate('notifications.soundDescription') || 'Play sound when notifications arrive'}
            left={props => <List.Icon {...props} icon="volume-high" />}
            right={props => (
              <Switch
                value={preferences.notificationSound}
                onValueChange={() => togglePreference('notificationSound')}
                color={'#1976d2'}
              />
            )}
          />
          <Divider />

          <List.Item
            title={translate('notifications.vibration') || 'Notification Vibration'}
            description={translate('notifications.vibrationDescription') || 'Vibrate when notifications arrive'}
            left={props => <List.Icon {...props} icon="vibrate" />}
            right={props => (
              <Switch
                value={preferences.notificationVibration}
                onValueChange={() => togglePreference('notificationVibration')}
                color={'#1976d2'}
              />
            )}
          />
        </Card.Content>
      </Card>

      <View style={styles.buttonContainer}>
        <Button
          mode="contained"
          onPress={saveNotificationPreferences}
          loading={saving}
          disabled={saving || loading}
          style={styles.saveButton}
        >
          {translate('notifications.savePreferences') || 'Save Preferences'}
        </Button>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  card: {
    marginBottom: 16,
    elevation: 2,
  },
  title: {
    fontSize: 18,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 14,
    color: '#666',
    marginBottom: 16,
  },
  permissionGranted: {
    color: '#4CAF50',
    marginVertical: 8,
  },
  permissionDenied: {
    color: '#F44336',
    marginVertical: 8,
  },
  permissionHint: {
    color: '#666',
    marginBottom: 16,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 16,
    flexWrap: 'wrap',
  },
  button: {
    marginVertical: 8,
    minWidth: 150,
  },
  saveButton: {
    marginVertical: 16,
    paddingVertical: 8,
    width: '100%',
  },
  loadingText: {
    color: '#666',
    marginVertical: 8,
  },
});

export default NotificationSettings;
