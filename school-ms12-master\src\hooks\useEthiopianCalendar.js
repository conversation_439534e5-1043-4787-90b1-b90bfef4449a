import { useLanguage } from '../context/LanguageContext';
import EthiopianCalendar from '../utils/EthiopianCalendar';

/**
 * Hook to provide Ethiopian calendar functionality with language context
 */
export const useEthiopianCalendar = () => {
  const { language } = useLanguage();

  /**
   * Format date using Ethiopian calendar in the current language
   * @param {Date|string} date - Date to format
   * @param {Object} options - Formatting options
   * @returns {string} - Formatted date string
   */
  const formatDate = (date, options = {}) => {
    return EthiopianCalendar.formatDate(date, language, options);
  };

  /**
   * Format date in short format (DD/MM/YYYY)
   * @param {Date|string} date - Date to format
   * @returns {string} - Formatted date string
   */
  const formatShortDate = (date) => {
    return EthiopianCalendar.formatShortDate(date);
  };

  /**
   * Get month name in current language
   * @param {number} month - Month index (0-12)
   * @returns {string} - Month name
   */
  const getMonthName = (month) => {
    return EthiopianCalendar.getMonthName(month, language);
  };

  /**
   * Get weekday name in current language
   * @param {number} day - Day of week (0-6, 0 is Sunday)
   * @returns {string} - Weekday name
   */
  const getWeekdayName = (day) => {
    return EthiopianCalendar.getWeekdayName(day, language);
  };

  /**
   * Convert Gregorian date to Ethiopian date
   * @param {Date} date - JavaScript Date object
   * @returns {Object} - Ethiopian date object
   */
  const toEthiopianDate = (date) => {
    return EthiopianCalendar.fromGregorian(date);
  };

  /**
   * Convert Ethiopian date to Gregorian date
   * @param {number} year - Ethiopian year
   * @param {number} month - Ethiopian month (0-12)
   * @param {number} day - Ethiopian day
   * @returns {Date} - JavaScript Date object
   */
  const toGregorianDate = (year, month, day) => {
    return EthiopianCalendar.toGregorian(year, month, day);
  };

  /**
   * Parse date string in Ethiopian format (DD/MM/YYYY)
   * @param {string} dateStr - Date string in DD/MM/YYYY format
   * @returns {Date} - JavaScript Date object
   */
  const parseDate = (dateStr) => {
    return EthiopianCalendar.parseDate(dateStr);
  };

  return {
    formatDate,
    formatShortDate,
    getMonthName,
    getWeekdayName,
    toEthiopianDate,
    toGregorianDate,
    parseDate,
    isLeapYear: EthiopianCalendar.isLeapYear,
    getAcademicYear: EthiopianCalendar.getAcademicYear,
    getSemester: EthiopianCalendar.getSemester,
    isSchoolDay: EthiopianCalendar.isSchoolDay,
    getWeekNumber: EthiopianCalendar.getWeekNumber,
  };
};
