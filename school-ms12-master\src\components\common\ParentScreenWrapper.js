import React from 'react';
import { View, StyleSheet, SafeAreaView, StatusBar } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import ParentAppHeader from './ParentAppHeader';
import ParentSidebar from './ParentSidebar';
import SidebarBackdrop from './SidebarBackdrop';
import { useParentSidebar } from '../../context/ParentSidebarContext';

/**
 * A wrapper component for parent screens that includes the header and sidebar
 * 
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - The screen content
 * @param {string} props.title - The screen title
 * @param {boolean} props.showBack - Whether to show the back button
 * @param {boolean} props.showNotification - Whether to show the notification icon
 * @param {Array} props.children - List of children for the parent user
 * @param {string} props.selectedChildId - ID of the selected child
 * @param {Function} props.onChildSelect - Function to call when a child is selected
 * @returns {React.ReactElement} The ParentScreenWrapper component
 */
const ParentScreenWrapper = ({ 
  children, 
  title, 
  showBack = false,
  showNotification = true,
  childrenList = [],
  selectedChildId = null,
  onChildSelect = null,
  screenContent
}) => {
  const navigation = useNavigation();
  const { 
    drawerOpen, 
    activeSidebarItem, 
    setActiveSidebarItem, 
    drawerAnim, 
    backdropFadeAnim, 
    toggleDrawer 
  } = useParentSidebar();

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar backgroundColor={'#1976d2'} barStyle="light-content" />
      
      {/* Parent App Header */}
      <ParentAppHeader
        title={title}
        onMenuPress={toggleDrawer}
        showBackButton={showBack}
      />
      
      {/* Parent Sidebar */}
      <ParentSidebar
        visible={drawerOpen}
        drawerAnim={drawerAnim}
        toggleDrawer={toggleDrawer}
        navigation={navigation}
        activeSidebarItem={activeSidebarItem}
        setActiveSidebarItem={setActiveSidebarItem}
        children={childrenList}
        selectedChild={selectedChildId}
        onChildSelect={onChildSelect}
      />
      
      {/* Sidebar Backdrop */}
      <SidebarBackdrop
        visible={drawerOpen}
        onPress={toggleDrawer}
        fadeAnim={backdropFadeAnim}
      />
      
      {/* Screen Content */}
      <View style={styles.content}>
        {screenContent || children}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
    padding: 16,
  },
});

export default ParentScreenWrapper;
