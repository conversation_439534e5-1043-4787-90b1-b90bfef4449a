import React, { useState, useEffect, useRef } from 'react';
import { View, StyleSheet, StatusBar, SafeAreaView, Animated } from 'react-native';
import {
  Card,
  Title,
  Text,
  useTheme,
  Button,
  Paragraph,
  Snackbar
} from 'react-native-paper';
import { useLanguage } from '../../context/LanguageContext';
import * as Animatable from 'react-native-animatable';
import TeacherAppHeader from '../../components/common/TeacherAppHeader';
import TeacherSidebar from '../../components/common/TeacherSidebar';
import SidebarBackdrop from '../../components/common/SidebarBackdrop';

const AssessmentManagement = ({ navigation }) => {
  const { translate, isRTL } = useLanguage();
  // No theme needed
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;
  const backdropFadeAnim = useRef(new Animated.Value(0)).current;
  
  // Sidebar state
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [activeSidebarItem, setActiveSidebarItem] = useState('AssessmentManagement');
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');

  // Start animations when component mounts
  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      })
    ]).start();
  }, []);

  // Toggle sidebar
  const toggleDrawer = () => {
    const newValue = !drawerOpen;
    setDrawerOpen(newValue);
    
    // Animate backdrop
    Animated.timing(backdropFadeAnim, {
      toValue: newValue ? 0.5 : 0,
      duration: 300,
      useNativeDriver: true,
    }).start();
  };

  // Render the component
  return (
    <SafeAreaView style={styles.container}>
      <StatusBar backgroundColor={'#1976d2'} barStyle="light-content" />
      
      {/* Teacher App Header */}
      <TeacherAppHeader
        title={translate('assessmentManagement.title') || "Assessment Management"}
        onMenuPress={toggleDrawer}
        showNotification={true}
      />
      
      {/* Teacher Sidebar */}
      <TeacherSidebar
        visible={drawerOpen}
        onClose={toggleDrawer}
        navigation={navigation}
        activeSidebarItem={activeSidebarItem}
        setActiveSidebarItem={setActiveSidebarItem}
      />
      
      {/* Sidebar Backdrop */}
      <SidebarBackdrop
        visible={drawerOpen}
        onPress={toggleDrawer}
        fadeAnim={backdropFadeAnim}
      />
      
      {/* Main Content */}
      <View style={styles.mainContent}>
        <Animatable.View
          animation="fadeIn"
          duration={500}
          style={styles.contentContainer}
        >
          <Card>
            <Card.Content>
              <Title style={styles.title}>Assessment Management</Title>
              <Paragraph>
                This screen is currently being enhanced. Please check back later.
              </Paragraph>
              <Button 
                mode="contained" 
                onPress={() => navigation.goBack()}
                style={styles.button}
              >
                Go Back
              </Button>
            </Card.Content>
          </Card>
        </Animatable.View>
      </View>
      
      {/* Snackbar for notifications */}
      <Snackbar
        visible={snackbarVisible}
        onDismiss={() => setSnackbarVisible(false)}
        duration={3000}
        style={styles.snackbar}
        action={{
          label: translate('common.dismiss') || "Dismiss",
          onPress: () => setSnackbarVisible(false),
        }}
      >
        {snackbarMessage}
      </Snackbar>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  mainContent: {
    flex: 1,
    padding: 16,
  },
  contentContainer: {
    marginTop: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  button: {
    marginTop: 16,
  },
  snackbar: {
    bottom: 16,
  },
});

export default AssessmentManagement;
