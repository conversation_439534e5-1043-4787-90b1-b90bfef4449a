import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView, Alert } from 'react-native';
import {
  Card,
  Title,
  Text,
  Button,
  TextInput,
  DataTable,
  Chip,
  ActivityIndicator,
  Snackbar,
  Portal,
  Dialog,
  IconButton,
  ProgressBar,
  Divider,
  Menu,
  Tooltip
} from 'react-native-paper';
import { db, auth } from '../../config/firebase';
import {
  collection,
  query,
  where,
  getDocs,
  doc,
  getDoc,
  addDoc,
  updateDoc,
  Timestamp,
  serverTimestamp
} from 'firebase/firestore';
import { useLanguage } from '../../context/LanguageContext';
import * as Animatable from 'react-native-animatable';

const TeacherGradeSubmission = ({ classId, className, sectionName: initialSectionName, subject, onSubmissionComplete }) => {
  // No theme needed
  const { translate } = useLanguage();

  // State variables
  const [loading, setLoading] = useState(true);
  const [students, setStudents] = useState([]);
  const [assessments, setAssessments] = useState([]);
  const [scores, setScores] = useState({});
  const [calculatedScores, setCalculatedScores] = useState({});
  const [confirmDialogVisible, setConfirmDialogVisible] = useState(false);
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [error, setError] = useState(null);
  const [createAssessmentDialogVisible, setCreateAssessmentDialogVisible] = useState(false);
  const [newAssessment, setNewAssessment] = useState({
    title: '',
    type: 'quiz',
    points: '10',
    description: ''
  });
  const [assessmentTypeMenuVisible, setAssessmentTypeMenuVisible] = useState(false);
  const [validationErrors, setValidationErrors] = useState({});
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [showHelp, setShowHelp] = useState(false);

  // Section selection
  const [availableSections, setAvailableSections] = useState([]);
  const [sectionName, setSectionName] = useState(initialSectionName || '');
  const [sectionMenuVisible, setSectionMenuVisible] = useState(false);

  // Fetch available sections on component mount
  useEffect(() => {
    if (classId && className) {
      fetchAvailableSections();
    }
  }, [classId, className]);

  // Fetch students and assessments when section changes
  useEffect(() => {
    if (sectionName) {
      fetchStudentsAndAssessments();
    }
  }, [classId, className, sectionName, subject]);

  // Fetch available sections for the class
  const fetchAvailableSections = async () => {
    try {
      setLoading(true);

      // Query the classes collection to get sections for this class
      const classRef = doc(db, 'classes', classId);
      const classDoc = await getDoc(classRef);

      if (!classDoc.exists()) {
        console.error('Class not found');
        setError('Class not found');
        setLoading(false);
        return;
      }

      const classData = classDoc.data();
      const sections = classData.sections || [];

      // If no sections found in class data, try to query users collection
      if (sections.length === 0) {
        const usersRef = collection(db, 'users');
        const studentsQuery = query(
          usersRef,
          where('role', '==', 'student'),
          where('className', '==', className)
        );

        const studentsSnapshot = await getDocs(studentsQuery);

        // Extract unique section names
        const uniqueSections = new Set();
        studentsSnapshot.forEach(doc => {
          const student = doc.data();
          if (student.sectionName) {
            uniqueSections.add(student.sectionName);
          }
        });

        setAvailableSections(Array.from(uniqueSections).sort());
      } else {
        setAvailableSections(sections.sort());
      }

      // If no section is selected and we have sections, select the first one
      if (!sectionName && sections.length > 0) {
        setSectionName(sections[0]);
      }

    } catch (error) {
      console.error('Error fetching sections:', error);
      setError('Failed to load sections');
    } finally {
      setLoading(false);
    }
  };

  // Create a new assessment
  const createAssessment = async () => {
    try {
      // Validate assessment data
      const errors = {};

      if (!newAssessment.title.trim()) {
        errors.title = 'Title is required';
      }

      if (!newAssessment.type.trim()) {
        errors.type = 'Type is required';
      }

      const points = parseFloat(newAssessment.points);
      if (isNaN(points) || points <= 0 || points > 100) {
        errors.points = 'Points must be a number between 1 and 100';
      }

      // Check if adding this assessment would exceed 100 total points
      const currentTotalPoints = assessments.reduce((total, assessment) => {
        return total + (parseFloat(assessment.points) || 0);
      }, 0);

      const newTotalPoints = currentTotalPoints + points;
      if (newTotalPoints > 100) {
        errors.points = `Total points would exceed 100. Current total: ${currentTotalPoints}. Maximum allowed for new assessment: ${(100 - currentTotalPoints).toFixed(1)}`;
      }

      if (Object.keys(errors).length > 0) {
        setValidationErrors(errors);
        return;
      }

      setValidationErrors({});
      setLoading(true);

      // Create assessment document
      const assessmentRef = collection(db, 'assessments');
      const assessmentData = {
        title: newAssessment.title,
        type: newAssessment.type,
        points: points,
        description: newAssessment.description,
        className,
        sectionName,
        subject,
        teacherId: auth.currentUser?.uid,
        teacherName: auth.currentUser?.displayName || 'Unknown Teacher',
        createdAt: serverTimestamp()
      };

      await addDoc(assessmentRef, assessmentData);

      // Reset form and close dialog
      setNewAssessment({
        title: '',
        type: 'quiz',
        points: '10',
        description: ''
      });

      setCreateAssessmentDialogVisible(false);

      // Refresh assessments
      fetchStudentsAndAssessments();

      setSnackbarMessage('Assessment created successfully');
      setSnackbarVisible(true);
    } catch (error) {
      console.error('Error creating assessment:', error);
      setSnackbarMessage(`Error creating assessment: ${error.message}`);
      setSnackbarVisible(true);
      setLoading(false);
    }
  };

  // Fetch students and assessments
  const fetchStudentsAndAssessments = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch students
      const studentsRef = collection(db, 'users');
      const studentsQuery = query(
        studentsRef,
        where('role', '==', 'student'),
        where('className', '==', className),
        where('sectionName', '==', sectionName)
      );

      const studentsSnapshot = await getDocs(studentsQuery);
      const studentsList = studentsSnapshot.docs.map(doc => ({
        id: doc.id,
        uid: doc.id,
        ...doc.data(),
        scores: {}
      }));

      // Fetch assessments
      const assessmentsRef = collection(db, 'assessments');
      const assessmentsQuery = query(
        assessmentsRef,
        where('className', '==', className),
        where('sectionName', '==', sectionName),
        where('subject', '==', subject)
      );

      const assessmentsSnapshot = await getDocs(assessmentsQuery);
      const assessmentsList = assessmentsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      // Initialize scores object
      const initialScores = {};
      studentsList.forEach(student => {
        initialScores[student.id] = {};
        assessmentsList.forEach(assessment => {
          initialScores[student.id][assessment.id] = {
            points: '',
            maxPoints: assessment.points || 0
          };
        });
      });

      setStudents(studentsList);
      setAssessments(assessmentsList);
      setScores(initialScores);

    } catch (error) {
      console.error('Error fetching data:', error);
      setError('Failed to load students and assessments');
    } finally {
      setLoading(false);
    }
  };

  // Handle score change
  const handleScoreChange = (studentId, assessmentId, value) => {
    const numericValue = value.replace(/[^0-9.]/g, '');

    setScores(prevScores => ({
      ...prevScores,
      [studentId]: {
        ...prevScores[studentId],
        [assessmentId]: {
          ...prevScores[studentId][assessmentId],
          points: numericValue
        }
      }
    }));
  };

  // Validate assessment total points
  const validateAssessmentTotalPoints = () => {
    const totalAssessmentPoints = assessments.reduce((total, assessment) => {
      return total + (parseFloat(assessment.points) || 0);
    }, 0);

    if (Math.abs(totalAssessmentPoints - 100) > 0.01) {
      Alert.alert(
        'Invalid Assessment Points',
        `The total points for all assessments must equal 100. Current total: ${totalAssessmentPoints.toFixed(1)}`,
        [{ text: 'OK' }]
      );
      return false;
    }

    return true;
  };

  // Calculate scores
  const calculateScores = () => {
    try {
      // First validate that assessment points total 100
      if (!validateAssessmentTotalPoints()) {
        return false;
      }

      const calculated = {};
      let hasInvalidScores = false;

      students.forEach(student => {
        let totalPoints = 0;
        let totalMaxPoints = 0;
        const assessmentScores = [];

        assessments.forEach(assessment => {
          const score = scores[student.id][assessment.id];
          const points = parseFloat(score.points) || 0;
          const maxPoints = parseFloat(assessment.points) || 0;

          if (points > maxPoints) {
            hasInvalidScores = true;
            Alert.alert(
              'Invalid Score',
              `Score for ${student.name} on ${assessment.title} exceeds maximum points (${maxPoints}).`
            );
            return;
          }

          totalPoints += points;
          totalMaxPoints += maxPoints;

          assessmentScores.push({
            assessmentId: assessment.id,
            assessmentTitle: assessment.title,
            assessmentType: assessment.type,
            score: points,
            maxPoints: maxPoints
          });
        });

        if (hasInvalidScores) return;

        const percentage = totalMaxPoints > 0 ? (totalPoints / totalMaxPoints) * 100 : 0;

        calculated[student.id] = {
          totalPoints,
          totalMaxPoints,
          percentage,
          assessmentScores
        };
      });

      if (hasInvalidScores) return false;

      setCalculatedScores(calculated);
      setSnackbarMessage('Scores calculated successfully');
      setSnackbarVisible(true);
      return true;

    } catch (error) {
      console.error('Error calculating scores:', error);
      setSnackbarMessage('Error calculating scores');
      setSnackbarVisible(true);
      return false;
    }
  };

  // Submit grades for approval
  const submitGrades = async () => {
    try {
      setLoading(true);

      // Calculate scores first and validate
      const calculationSuccess = calculateScores();
      if (!calculationSuccess) {
        setLoading(false);
        return;
      }

      // Check if we have scores for all students and assessments
      let missingScores = false;
      let missingScoreMessage = '';

      students.forEach(student => {
        assessments.forEach(assessment => {
          const score = scores[student.id]?.[assessment.id]?.points;
          if (!score && score !== '0') {
            missingScores = true;
            missingScoreMessage = `Missing score for ${student.name} on ${assessment.title}`;
          }
        });
      });

      if (missingScores) {
        Alert.alert(
          'Missing Scores',
          `${missingScoreMessage}. Please enter all scores before submitting.`,
          [{ text: 'OK' }]
        );
        setLoading(false);
        return;
      }

      // Prepare student data
      const studentData = students.map(student => {
        const calculatedScore = calculatedScores[student.id];

        if (!calculatedScore) {
          throw new Error(`Missing calculated score for student: ${student.name}`);
        }

        return {
          id: student.id,
          uid: student.uid,
          name: student.name,
          rollNumber: student.rollNumber,
          totalScore: calculatedScore.percentage.toFixed(1),
          assessmentScores: calculatedScore.assessmentScores
        };
      });

      // Prepare assessment data
      const assessmentData = assessments.map(assessment => ({
        id: assessment.id,
        title: assessment.title,
        type: assessment.type,
        points: parseFloat(assessment.points) || 0
      }));

      // Check if there's already a pending submission for this class/section/subject
      const existingSubmissionQuery = query(
        collection(db, 'gradeSubmissions'),
        where('classId', '==', classId),
        where('sectionName', '==', sectionName),
        where('subject', '==', subject),
        where('status', '==', 'pending')
      );

      const existingSubmissionSnapshot = await getDocs(existingSubmissionQuery);

      if (!existingSubmissionSnapshot.empty) {
        Alert.alert(
          'Existing Submission',
          'There is already a pending submission for this class, section, and subject. Please wait for the admin to approve or reject it before submitting again.',
          [{ text: 'OK' }]
        );
        setLoading(false);
        return;
      }

      // Create submission record
      const submissionRef = collection(db, 'gradeSubmissions');
      const submissionData = {
        classId,
        className,
        sectionName,
        subject,
        teacherId: auth.currentUser?.uid,
        teacherName: auth.currentUser?.displayName || 'Unknown Teacher',
        status: 'pending',
        submittedAt: Timestamp.now(),
        assessments: assessmentData,
        students: studentData
      };

      const submissionDoc = await addDoc(submissionRef, submissionData);

      // Send notification to admins
      const usersRef = collection(db, 'users');
      const adminsQuery = query(usersRef, where('role', '==', 'admin'));
      const adminsSnapshot = await getDocs(adminsQuery);

      const notificationsRef = collection(db, 'notifications');
      const notificationPromises = [];

      if (adminsSnapshot.empty) {
        console.warn('No admin users found to notify about grade submission');
      }

      adminsSnapshot.docs.forEach(adminDoc => {
        notificationPromises.push(
          addDoc(notificationsRef, {
            title: 'Grade Submission',
            body: `Teacher ${auth.currentUser?.displayName || 'Unknown'} has submitted grades for ${className}-${sectionName} ${subject} for approval.`,
            type: 'grade_submission',
            role: 'admin',
            userId: adminDoc.id,
            read: false,
            data: {
              submissionId: submissionDoc.id,
              classId,
              className,
              sectionName,
              subject
            },
            createdAt: Timestamp.now()
          })
        );
      });

      await Promise.all(notificationPromises);

      // Save scores to the scores collection for backup
      const scoresRef = collection(db, 'scores');
      const scorePromises = [];

      students.forEach(student => {
        assessments.forEach(assessment => {
          const score = scores[student.id][assessment.id];
          if (score && score.points) {
            scorePromises.push(
              addDoc(scoresRef, {
                studentId: student.id,
                studentName: student.name,
                assessmentId: assessment.id,
                assessmentTitle: assessment.title,
                assessmentType: assessment.type,
                points: parseFloat(score.points) || 0,
                maxPoints: parseFloat(assessment.points) || 0,
                classId,
                className,
                sectionName,
                subject,
                submissionId: submissionDoc.id,
                timestamp: Timestamp.now(),
                status: 'pending'
              })
            );
          }
        });
      });

      await Promise.all(scorePromises);

      setSnackbarMessage('Grades submitted for approval successfully');
      setSnackbarVisible(true);
      setConfirmDialogVisible(false);

      // Call the callback function if provided
      if (onSubmissionComplete) {
        onSubmissionComplete();
      }

    } catch (error) {
      console.error('Error submitting grades:', error);
      setSnackbarMessage(`Error submitting grades: ${error.message}`);
      setSnackbarVisible(true);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={'#1976d2'} />
        <Text style={styles.loadingText}>Loading...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>{error}</Text>
        <Button mode="contained" onPress={fetchStudentsAndAssessments}>
          Retry
        </Button>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Animatable.View animation="fadeIn" duration={500}>
        <Card style={styles.headerCard}>
          <Card.Content>
            <View style={styles.headerRow}>
              <View>
                <Title style={styles.headerTitle}>
                  Grade Submission for {className}-{sectionName} {subject}
                </Title>
                <Text style={styles.headerSubtitle}>
                  Enter points for each student and assessment, then submit for approval.
                </Text>
              </View>
              <IconButton
                icon="help-circle-outline"
                size={24}
                onPress={() => setShowHelp(!showHelp)}
              />
            </View>

            {/* Section Selector */}
            {availableSections.length > 1 && (
              <View style={styles.sectionSelectorContainer}>
                <Text style={styles.sectionSelectorLabel}>Select Section:</Text>
                <Menu
                  visible={sectionMenuVisible}
                  onDismiss={() => setSectionMenuVisible(false)}
                  anchor={
                    <Button
                      mode="outlined"
                      onPress={() => setSectionMenuVisible(true)}
                      style={styles.sectionSelectorButton}
                      icon="class"
                    >
                      {sectionName || 'Select Section'}
                    </Button>
                  }
                >
                  {availableSections.map((section) => (
                    <Menu.Item
                      key={section}
                      onPress={() => {
                        setSectionName(section);
                        setSectionMenuVisible(false);
                      }}
                      title={section}
                    />
                  ))}
                </Menu>
              </View>
            )}

            {showHelp && (
              <Animatable.View animation="fadeIn" duration={300} style={styles.helpContainer}>
                <Text style={styles.helpText}>
                  <Text style={styles.helpBold}>Step 1:</Text> Create assessments (quizzes, tests, etc.) with points that add up to 100.{'\n'}
                  <Text style={styles.helpBold}>Step 2:</Text> Enter scores for each student on each assessment.{'\n'}
                  <Text style={styles.helpBold}>Step 3:</Text> Click "Calculate" to see total scores.{'\n'}
                  <Text style={styles.helpBold}>Step 4:</Text> Submit for admin approval.{'\n\n'}
                  Once approved, students and parents will be notified and can view the results.
                </Text>
              </Animatable.View>
            )}

            {assessments.length > 0 && (
              <View style={styles.assessmentSummary}>
                <Text style={styles.assessmentSummaryText}>
                  {assessments.length} assessment{assessments.length !== 1 ? 's' : ''} |
                  Total points: {assessments.reduce((total, a) => total + parseFloat(a.points || 0), 0).toFixed(1)}/100
                </Text>
                <ProgressBar
                  progress={assessments.reduce((total, a) => total + parseFloat(a.points || 0), 0) / 100}
                  color={assessments.reduce((total, a) => total + parseFloat(a.points || 0), 0) === 100 ? '#4CAF50' : '#2196F3'}
                  style={styles.progressBar}
                />
              </View>
            )}
          </Card.Content>
        </Card>
      </Animatable.View>

      {assessments.length === 0 ? (
        <Animatable.View animation="fadeIn" duration={500} delay={300}>
          <Card style={styles.noDataCard}>
            <Card.Content>
              <View style={styles.noDataContent}>
                <IconButton icon="clipboard-text-outline" size={40} color="#1976d2" />
                <Text style={styles.noDataText}>No assessments found for this class and subject.</Text>
                <Text style={styles.noDataSubtext}>Create assessments first to enter student scores.</Text>
                <Button
                  mode="contained"
                  style={styles.createButton}
                  icon="plus"
                  onPress={() => setCreateAssessmentDialogVisible(true)}
                >
                  Create Assessment
                </Button>
              </View>
            </Card.Content>
          </Card>
        </Animatable.View>
      ) : (
        <ScrollView horizontal>
          <DataTable style={styles.dataTable}>
            <DataTable.Header>
              <DataTable.Title style={styles.nameColumn}>Student Name</DataTable.Title>
              <DataTable.Title style={styles.rollColumn}>Roll No</DataTable.Title>

              {assessments.map(assessment => (
                <DataTable.Title key={assessment.id} numeric style={styles.scoreColumn}>
                  <View style={styles.assessmentHeader}>
                    <Text style={styles.assessmentTitle}>{assessment.title}</Text>
                    <Chip size={20} style={styles.assessmentType}>
                      {assessment.type}
                    </Chip>
                    <Text style={styles.assessmentPoints}>/{assessment.points}</Text>
                  </View>
                </DataTable.Title>
              ))}

              <DataTable.Title numeric style={styles.totalColumn}>Total</DataTable.Title>
            </DataTable.Header>

            {students.map(student => (
              <DataTable.Row key={student.id}>
                <DataTable.Cell style={styles.nameColumn}>{student.name}</DataTable.Cell>
                <DataTable.Cell style={styles.rollColumn}>{student.rollNumber}</DataTable.Cell>

                {assessments.map(assessment => (
                  <DataTable.Cell key={assessment.id} numeric style={styles.scoreColumn}>
                    <TextInput
                      value={scores[student.id]?.[assessment.id]?.points || ''}
                      onChangeText={(text) => handleScoreChange(student.id, assessment.id, text)}
                      keyboardType="numeric"
                      style={styles.scoreInput}
                      maxLength={5}
                    />
                  </DataTable.Cell>
                ))}

                <DataTable.Cell numeric style={styles.totalColumn}>
                  <Text style={styles.totalScore}>
                    {calculatedScores[student.id]?.percentage.toFixed(1) || '-'}%
                  </Text>
                </DataTable.Cell>
              </DataTable.Row>
            ))}
          </DataTable>
        </ScrollView>
      )}

      <Animatable.View animation="fadeIn" duration={500} style={styles.actionContainer}>
        {assessments.length > 0 && (
          <Button
            mode="outlined"
            onPress={() => setCreateAssessmentDialogVisible(true)}
            style={styles.secondaryButton}
            icon="plus"
          >
            Add Assessment
          </Button>
        )}

        <View style={styles.mainActionButtons}>
          <Button
            mode="outlined"
            onPress={calculateScores}
            style={styles.actionButton}
            icon="calculator"
          >
            Calculate
          </Button>

          <Button
            mode="contained"
            onPress={() => {
              // First calculate scores to ensure they're up to date
              const calculationSuccess = calculateScores();
              if (calculationSuccess) {
                setConfirmDialogVisible(true);
              }
            }}
            style={styles.actionButton}
            icon="send"
            disabled={assessments.length === 0 || Object.keys(calculatedScores).length === 0}
          >
            Submit for Approval
          </Button>
        </View>
      </Animatable.View>

      {/* Confirmation Dialog */}
      <Portal>
        <Dialog
          visible={confirmDialogVisible}
          onDismiss={() => setConfirmDialogVisible(false)}
        >
          <Dialog.Title>Confirm Submission</Dialog.Title>
          <Dialog.Content>
            <Text style={styles.dialogText}>
              Are you sure you want to submit these grades for approval? Once submitted, an admin will need to review and approve them before they are visible to students and parents.
            </Text>

            <View style={styles.dialogInfoContainer}>
              <Text style={styles.dialogInfoLabel}>Class:</Text>
              <Text style={styles.dialogInfoValue}>{className}</Text>
            </View>

            <View style={styles.dialogInfoContainer}>
              <Text style={styles.dialogInfoLabel}>Section:</Text>
              <Text style={styles.dialogInfoValue}>{sectionName}</Text>
            </View>

            <View style={styles.dialogInfoContainer}>
              <Text style={styles.dialogInfoLabel}>Subject:</Text>
              <Text style={styles.dialogInfoValue}>{subject}</Text>
            </View>

            <View style={styles.dialogInfoContainer}>
              <Text style={styles.dialogInfoLabel}>Students:</Text>
              <Text style={styles.dialogInfoValue}>{students.length}</Text>
            </View>

            <View style={styles.dialogInfoContainer}>
              <Text style={styles.dialogInfoLabel}>Assessments:</Text>
              <Text style={styles.dialogInfoValue}>{assessments.length}</Text>
            </View>

            <Text style={styles.dialogNote}>
              Note: After submission, you cannot edit these grades until they are approved or rejected by an admin.
            </Text>
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={() => setConfirmDialogVisible(false)}>Cancel</Button>
            <Button onPress={submitGrades} loading={loading} mode="contained">Submit</Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>

      {/* Create Assessment Dialog */}
      <Portal>
        <Dialog
          visible={createAssessmentDialogVisible}
          onDismiss={() => {
            if (!loading) {
              setCreateAssessmentDialogVisible(false);
              setValidationErrors({});
            }
          }}
        >
          <Dialog.Title>Create New Assessment</Dialog.Title>
          <Dialog.Content>
            <TextInput
              label="Assessment Title"
              value={newAssessment.title}
              onChangeText={(text) => setNewAssessment({ ...newAssessment, title: text })}
              style={styles.dialogInput}
              error={!!validationErrors.title}
            />
            {validationErrors.title && (
              <Text style={styles.errorText}>{validationErrors.title}</Text>
            )}

            <View style={styles.assessmentTypeContainer}>
              <Text style={styles.assessmentTypeLabel}>Assessment Type:</Text>
              <Menu
                visible={assessmentTypeMenuVisible}
                onDismiss={() => setAssessmentTypeMenuVisible(false)}
                anchor={
                  <Button
                    mode="outlined"
                    onPress={() => setAssessmentTypeMenuVisible(true)}
                    style={styles.assessmentTypeButton}
                  >
                    {newAssessment.type || 'Select Type'}
                  </Button>
                }
              >
                {['quiz', 'test', 'exam', 'assignment', 'project', 'homework', 'participation'].map((type) => (
                  <Menu.Item
                    key={type}
                    onPress={() => {
                      setNewAssessment({ ...newAssessment, type });
                      setAssessmentTypeMenuVisible(false);
                    }}
                    title={type.charAt(0).toUpperCase() + type.slice(1)}
                  />
                ))}
              </Menu>
            </View>
            {validationErrors.type && (
              <Text style={styles.errorText}>{validationErrors.type}</Text>
            )}

            <TextInput
              label="Points (out of 100 total)"
              value={newAssessment.points}
              onChangeText={(text) => setNewAssessment({ ...newAssessment, points: text })}
              keyboardType="numeric"
              style={styles.dialogInput}
              error={!!validationErrors.points}
            />
            {validationErrors.points && (
              <Text style={styles.errorText}>{validationErrors.points}</Text>
            )}

            <TextInput
              label="Description (optional)"
              value={newAssessment.description}
              onChangeText={(text) => setNewAssessment({ ...newAssessment, description: text })}
              multiline
              numberOfLines={3}
              style={styles.dialogInput}
            />

            <View style={styles.assessmentSummary}>
              <Text style={styles.assessmentSummaryText}>
                Current total: {assessments.reduce((total, a) => total + parseFloat(a.points || 0), 0).toFixed(1)}/100
              </Text>
              <Text style={styles.assessmentSummaryText}>
                After adding: {(assessments.reduce((total, a) => total + parseFloat(a.points || 0), 0) + parseFloat(newAssessment.points || 0)).toFixed(1)}/100
              </Text>
              <ProgressBar
                progress={(assessments.reduce((total, a) => total + parseFloat(a.points || 0), 0) + parseFloat(newAssessment.points || 0)) / 100}
                color={(assessments.reduce((total, a) => total + parseFloat(a.points || 0), 0) + parseFloat(newAssessment.points || 0)) === 100 ? '#4CAF50' : '#2196F3'}
                style={styles.progressBar}
              />
            </View>
          </Dialog.Content>
          <Dialog.Actions>
            <Button
              onPress={() => {
                setCreateAssessmentDialogVisible(false);
                setValidationErrors({});
              }}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button
              onPress={createAssessment}
              loading={loading}
              mode="contained"
              disabled={loading}
            >
              Create
            </Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>

      {/* Snackbar */}
      <Snackbar
        visible={snackbarVisible}
        onDismiss={() => setSnackbarVisible(false)}
        duration={3000}
        style={styles.snackbar}
      >
        {snackbarMessage}
      </Snackbar>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center'
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16
  },
  errorText: {
    color: '#F44336',
    marginBottom: 8,
    fontSize: 12,
    textAlign: 'left'
  },
  headerCard: {
    marginBottom: 16,
    elevation: 2
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start'
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold'
  },
  headerSubtitle: {
    color: '#666',
    marginTop: 4
  },
  sectionSelectorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 16,
    backgroundColor: '#f5f5f5',
    padding: 8,
    borderRadius: 8
  },
  sectionSelectorLabel: {
    marginRight: 8,
    fontSize: 16,
    fontWeight: '500',
    color: '#555'
  },
  sectionSelectorButton: {
    flex: 1,
    borderRadius: 4
  },
  helpContainer: {
    backgroundColor: '#E3F2FD',
    padding: 12,
    borderRadius: 8,
    marginTop: 12
  },
  helpText: {
    lineHeight: 20,
    color: '#333'
  },
  helpBold: {
    fontWeight: 'bold'
  },
  assessmentSummary: {
    marginTop: 12
  },
  assessmentSummaryText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4
  },
  progressBar: {
    height: 6,
    borderRadius: 3
  },
  noDataCard: {
    marginBottom: 16,
    padding: 8,
    elevation: 2
  },
  noDataContent: {
    alignItems: 'center',
    padding: 16
  },
  noDataText: {
    fontSize: 16,
    fontWeight: 'bold',
    marginVertical: 8,
    textAlign: 'center'
  },
  noDataSubtext: {
    color: '#666',
    marginBottom: 16,
    textAlign: 'center'
  },
  createButton: {
    marginTop: 8
  },
  dataTable: {
    marginBottom: 16
  },
  nameColumn: {
    flex: 3
  },
  rollColumn: {
    flex: 1
  },
  scoreColumn: {
    flex: 2,
    justifyContent: 'center',
    alignItems: 'center'
  },
  totalColumn: {
    flex: 2
  },
  assessmentHeader: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 4
  },
  assessmentTitle: {
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 4
  },
  assessmentType: {
    marginBottom: 4
  },
  assessmentPoints: {
    fontSize: 12,
    color: '#666'
  },
  scoreInput: {
    height: 40,
    width: 60,
    textAlign: 'center',
    backgroundColor: '#f5f5f5'
  },
  totalScore: {
    fontWeight: 'bold',
    fontSize: 16
  },
  actionContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
    alignItems: 'center'
  },
  mainActionButtons: {
    flexDirection: 'row',
    justifyContent: 'flex-end'
  },
  actionButton: {
    marginLeft: 16
  },
  secondaryButton: {
    marginRight: 16
  },
  dialogText: {
    marginBottom: 16,
    lineHeight: 20
  },
  dialogInfoContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
    paddingHorizontal: 4,
    paddingVertical: 2,
    backgroundColor: '#f5f5f5',
    borderRadius: 4
  },
  dialogInfoLabel: {
    fontWeight: 'bold',
    color: '#555'
  },
  dialogInfoValue: {
    color: '#1976d2'
  },
  dialogNote: {
    fontStyle: 'italic',
    color: '#F44336',
    marginTop: 16
  },
  dialogInput: {
    marginBottom: 12
  },
  assessmentTypeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12
  },
  assessmentTypeLabel: {
    marginRight: 8,
    fontSize: 16
  },
  assessmentTypeButton: {
    flex: 1
  },
  snackbar: {
    bottom: 16
  }
});

export default TeacherGradeSubmission;
