import { db } from '../config/firebase';
import { collection, addDoc, query, where, getDocs, updateDoc, doc } from 'firebase/firestore';
import { EthiopianCalendar } from '../utils/EthiopianCalendar';

class EthiopianEducationService {
    static instance = null;
    
    constructor() {
        if (EthiopianEducationService.instance) {
            return EthiopianEducationService.instance;
        }
        EthiopianEducationService.instance = this;
        
        this.gradeScale = {
            'A+': { min: 97, max: 100, gpa: 4.0 },
            'A': { min: 93, max: 96, gpa: 4.0 },
            'A-': { min: 90, max: 92, gpa: 3.7 },
            'B+': { min: 87, max: 89, gpa: 3.3 },
            'B': { min: 83, max: 86, gpa: 3.0 },
            'B-': { min: 80, max: 82, gpa: 2.7 },
            'C+': { min: 77, max: 79, gpa: 2.3 },
            'C': { min: 73, max: 76, gpa: 2.0 },
            'C-': { min: 70, max: 72, gpa: 1.7 },
            'D': { min: 60, max: 69, gpa: 1.0 },
            'F': { min: 0, max: 59, gpa: 0.0 }
        };

        this.academicYearStructure = {
            semesters: 2,
            termsPerSemester: 2,
            weeksPerTerm: 8
        };
    }

    // Convert Gregorian to Ethiopian date
    toEthiopianDate(gregorianDate) {
        return EthiopianCalendar.toEthiopian(gregorianDate);
    }

    // Convert Ethiopian to Gregorian date
    toGregorianDate(ethiopianDate) {
        return EthiopianCalendar.toGregorian(ethiopianDate);
    }

    // Calculate GPA according to Ethiopian system
    calculateGPA(grades) {
        let totalPoints = 0;
        let totalCredits = 0;

        grades.forEach(grade => {
            const letterGrade = this.getLetterGrade(grade.score);
            totalPoints += this.gradeScale[letterGrade].gpa * grade.credits;
            totalCredits += grade.credits;
        });

        return totalCredits > 0 ? (totalPoints / totalCredits).toFixed(2) : 0;
    }

    // Get letter grade from numerical score
    getLetterGrade(score) {
        for (const [letter, range] of Object.entries(this.gradeScale)) {
            if (score >= range.min && score <= range.max) {
                return letter;
            }
        }
        return 'F';
    }

    // Generate Ethiopian format transcript
    async generateTranscript(studentId, language = 'amharic') {
        const student = await this.getStudentInfo(studentId);
        const grades = await this.getStudentGrades(studentId);
        const template = await this.getTranscriptTemplate(language);

        return {
            studentInfo: {
                name: student.name,
                id: student.ethiopianId,
                dateOfBirth: this.toEthiopianDate(student.dateOfBirth),
                program: student.program
            },
            academicRecord: this.formatGradesForTranscript(grades),
            gpa: this.calculateGPA(grades),
            certificationDate: this.toEthiopianDate(new Date()),
            template: template
        };
    }

    // Format grades for Ethiopian transcript
    formatGradesForTranscript(grades) {
        return grades.map(grade => ({
            subject: grade.subject,
            amharicName: grade.amharicName,
            score: grade.score,
            letterGrade: this.getLetterGrade(grade.score),
            credits: grade.credits,
            academicYear: this.toEthiopianDate(grade.date).year
        }));
    }

    // Get student information
    async getStudentInfo(studentId) {
        const studentDoc = await getDocs(
            query(collection(db, 'students'), where('id', '==', studentId))
        );
        return studentDoc.docs[0].data();
    }

    // Get student grades
    async getStudentGrades(studentId) {
        const gradesSnapshot = await getDocs(
            query(collection(db, 'grades'), where('studentId', '==', studentId))
        );
        return gradesSnapshot.docs.map(doc => doc.data());
    }

    // Generate Ministry of Education compliant reports
    async generateMOEReport(classId, term) {
        const students = await this.getClassStudents(classId);
        const assessments = await this.getTermAssessments(classId, term);
        
        const report = {
            schoolInfo: await this.getSchoolInfo(),
            classInfo: await this.getClassInfo(classId),
            termInfo: {
                term,
                ethiopianYear: this.toEthiopianDate(new Date()).year
            },
            studentRecords: []
        };

        for (const student of students) {
            const studentRecord = {
                id: student.ethiopianId,
                name: student.name,
                attendance: await this.getStudentAttendance(student.id, term),
                assessments: this.calculateStudentAssessments(student.id, assessments),
                behavior: await this.getStudentBehavior(student.id, term)
            };
            report.studentRecords.push(studentRecord);
        }

        return report;
    }

    // Get class students
    async getClassStudents(classId) {
        const studentsSnapshot = await getDocs(
            query(collection(db, 'students'), where('classId', '==', classId))
        );
        return studentsSnapshot.docs.map(doc => doc.data());
    }

    // Get term assessments
    async getTermAssessments(classId, term) {
        const assessmentsSnapshot = await getDocs(
            query(collection(db, 'assessments'), 
                where('classId', '==', classId),
                where('term', '==', term)
            )
        );
        return assessmentsSnapshot.docs.map(doc => doc.data());
    }

    // Calculate student assessments
    calculateStudentAssessments(studentId, assessments) {
        return assessments.map(assessment => {
            const studentScore = assessment.scores.find(
                score => score.studentId === studentId
            );
            return {
                type: assessment.type,
                score: studentScore ? studentScore.score : 0,
                maxScore: assessment.maxScore,
                weight: assessment.weight
            };
        });
    }

    // Get student attendance
    async getStudentAttendance(studentId, term) {
        const attendanceSnapshot = await getDocs(
            query(collection(db, 'attendance'),
                where('studentId', '==', studentId),
                where('term', '==', term)
            )
        );
        return attendanceSnapshot.docs.map(doc => doc.data());
    }

    // Get student behavior
    async getStudentBehavior(studentId, term) {
        const behaviorSnapshot = await getDocs(
            query(collection(db, 'behavior'),
                where('studentId', '==', studentId),
                where('term', '==', term)
            )
        );
        return behaviorSnapshot.docs.map(doc => doc.data());
    }

    // Get school information
    async getSchoolInfo() {
        const schoolDoc = await getDocs(collection(db, 'school_info'));
        return schoolDoc.docs[0].data();
    }

    // Get class information
    async getClassInfo(classId) {
        const classDoc = await getDocs(
            query(collection(db, 'classes'), where('id', '==', classId))
        );
        return classDoc.docs[0].data();
    }

    // Generate progress report in Amharic
    async generateAmharicProgressReport(studentId, term) {
        const student = await this.getStudentInfo(studentId);
        const grades = await this.getStudentGrades(studentId);
        const attendance = await this.getStudentAttendance(studentId, term);
        const behavior = await this.getStudentBehavior(studentId, term);

        return {
            studentInfo: {
                name: student.amharicName || student.name,
                id: student.ethiopianId,
                grade: student.grade,
                section: student.section
            },
            academicPerformance: this.formatGradesInAmharic(grades),
            attendance: this.formatAttendanceInAmharic(attendance),
            behavior: this.formatBehaviorInAmharic(behavior),
            teacherComments: await this.getTeacherComments(studentId, term, 'amharic'),
            reportDate: this.toEthiopianDate(new Date())
        };
    }

    // Format grades in Amharic
    formatGradesInAmharic(grades) {
        const amharicGrades = {
            'A+': 'A+',
            'A': 'A',
            'A-': 'A-',
            'B+': 'B+',
            'B': 'B',
            'B-': 'B-',
            'C+': 'C+',
            'C': 'C',
            'C-': 'C-',
            'D': 'D',
            'F': 'F'
        };

        return grades.map(grade => ({
            subject: grade.amharicName,
            score: grade.score,
            letterGrade: amharicGrades[this.getLetterGrade(grade.score)],
            teacherName: grade.teacherAmharicName || grade.teacherName
        }));
    }

    // Get teacher comments in Amharic
    async getTeacherComments(studentId, term, language) {
        const commentsSnapshot = await getDocs(
            query(collection(db, 'teacher_comments'),
                where('studentId', '==', studentId),
                where('term', '==', term)
            )
        );
        return commentsSnapshot.docs.map(doc => ({
            subject: language === 'amharic' ? doc.data().subjectAmharic : doc.data().subject,
            comment: language === 'amharic' ? doc.data().commentAmharic : doc.data().comment,
            teacher: language === 'amharic' ? doc.data().teacherAmharicName : doc.data().teacherName
        }));
    }
}

export default new EthiopianEducationService();
