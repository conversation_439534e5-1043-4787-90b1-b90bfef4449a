  // Render filter menu
  const renderFilterMenu = () => (
    <Portal>
      <Modal
        visible={showFilterMenu}
        onDismiss={() => setShowFilterMenu(false)}
        contentContainerStyle={styles.modalContainer}
      >
        <Surface style={styles.modalSurface}>
          <View style={styles.modalHeader}>
            <Title style={styles.modalTitle}>
              {translate('userManagement.filterUsers')}
            </Title>
            <IconButton
              icon="close"
              size={24}
              onPress={() => setShowFilterMenu(false)}
            />
          </View>
          
          <Divider />
          
          <View style={styles.modalContent}>
            <Text style={styles.filterLabel}>{translate('userManagement.filterByRole')}</Text>
            <View style={styles.filterChips}>
              <Chip
                selected={filterRole === 'all'}
                onPress={() => setFilterRole('all')}
                style={styles.filterChip}
                selectedColor={mobileTheme.colors.primary}
              >
                {translate('common.all')}
              </Chip>
              <Chip
                selected={filterRole === 'admin'}
                onPress={() => setFilterRole('admin')}
                style={styles.filterChip}
                selectedColor={mobileTheme.colors.admin}
              >
                {translate('userManagement.roles.admin')}
              </Chip>
              <Chip
                selected={filterRole === 'teacher'}
                onPress={() => setFilterRole('teacher')}
                style={styles.filterChip}
                selectedColor={mobileTheme.colors.teacher}
              >
                {translate('userManagement.roles.teacher')}
              </Chip>
              <Chip
                selected={filterRole === 'student'}
                onPress={() => setFilterRole('student')}
                style={styles.filterChip}
                selectedColor={mobileTheme.colors.student}
              >
                {translate('userManagement.roles.student')}
              </Chip>
              <Chip
                selected={filterRole === 'parent'}
                onPress={() => setFilterRole('parent')}
                style={styles.filterChip}
                selectedColor={mobileTheme.colors.parent}
              >
                {translate('userManagement.roles.parent')}
              </Chip>
            </View>
            
            <Divider style={styles.divider} />
            
            <Text style={styles.filterLabel}>{translate('userManagement.filterByStatus')}</Text>
            <View style={styles.filterChips}>
              <Chip
                selected={filterStatus === 'all'}
                onPress={() => setFilterStatus('all')}
                style={styles.filterChip}
                selectedColor={mobileTheme.colors.primary}
              >
                {translate('common.all')}
              </Chip>
              <Chip
                selected={filterStatus === 'active'}
                onPress={() => setFilterStatus('active')}
                style={styles.filterChip}
                selectedColor={mobileTheme.colors.success}
              >
                {translate('userManagement.status.active')}
              </Chip>
              <Chip
                selected={filterStatus === 'inactive'}
                onPress={() => setFilterStatus('inactive')}
                style={styles.filterChip}
                selectedColor={mobileTheme.colors.error}
              >
                {translate('userManagement.status.inactive')}
              </Chip>
            </View>
          </View>
          
          <Divider />
          
          <View style={styles.modalActions}>
            <Button
              mode="outlined"
              onPress={() => {
                setFilterRole('all');
                setFilterStatus('all');
              }}
              style={styles.resetButton}
            >
              {translate('common.reset')}
            </Button>
            <Button
              mode="contained"
              onPress={() => setShowFilterMenu(false)}
              style={styles.applyButton}
            >
              {translate('common.apply')}
            </Button>
          </View>
        </Surface>
      </Modal>
    </Portal>
  );
  
  // Render sort menu
  const renderSortMenu = () => (
    <Portal>
      <Modal
        visible={showSortMenu}
        onDismiss={() => setShowSortMenu(false)}
        contentContainerStyle={styles.modalContainer}
      >
        <Surface style={styles.modalSurface}>
          <View style={styles.modalHeader}>
            <Title style={styles.modalTitle}>
              {translate('userManagement.sortUsers')}
            </Title>
            <IconButton
              icon="close"
              size={24}
              onPress={() => setShowSortMenu(false)}
            />
          </View>
          
          <Divider />
          
          <View style={styles.modalContent}>
            <Text style={styles.filterLabel}>{translate('userManagement.sortBy')}</Text>
            <View style={styles.filterChips}>
              <Chip
                selected={sortBy === 'name'}
                onPress={() => setSortBy('name')}
                style={styles.filterChip}
                selectedColor={mobileTheme.colors.primary}
              >
                {translate('userManagement.name')}
              </Chip>
              <Chip
                selected={sortBy === 'role'}
                onPress={() => setSortBy('role')}
                style={styles.filterChip}
                selectedColor={mobileTheme.colors.primary}
              >
                {translate('userManagement.role')}
              </Chip>
              <Chip
                selected={sortBy === 'status'}
                onPress={() => setSortBy('status')}
                style={styles.filterChip}
                selectedColor={mobileTheme.colors.primary}
              >
                {translate('userManagement.status.status')}
              </Chip>
              <Chip
                selected={sortBy === 'createdAt'}
                onPress={() => setSortBy('createdAt')}
                style={styles.filterChip}
                selectedColor={mobileTheme.colors.primary}
              >
                {translate('userManagement.createdAt')}
              </Chip>
            </View>
            
            <Divider style={styles.divider} />
            
            <Text style={styles.filterLabel}>{translate('userManagement.sortDirection')}</Text>
            <View style={styles.filterChips}>
              <Chip
                selected={sortDirection === 'asc'}
                onPress={() => setSortDirection('asc')}
                style={styles.filterChip}
                selectedColor={mobileTheme.colors.primary}
                icon="sort-ascending"
              >
                {translate('userManagement.ascending')}
              </Chip>
              <Chip
                selected={sortDirection === 'desc'}
                onPress={() => setSortDirection('desc')}
                style={styles.filterChip}
                selectedColor={mobileTheme.colors.primary}
                icon="sort-descending"
              >
                {translate('userManagement.descending')}
              </Chip>
            </View>
          </View>
          
          <Divider />
          
          <View style={styles.modalActions}>
            <Button
              mode="outlined"
              onPress={() => {
                setSortBy('name');
                setSortDirection('asc');
              }}
              style={styles.resetButton}
            >
              {translate('common.reset')}
            </Button>
            <Button
              mode="contained"
              onPress={() => setShowSortMenu(false)}
              style={styles.applyButton}
            >
              {translate('common.apply')}
            </Button>
          </View>
        </Surface>
      </Modal>
    </Portal>
  );
