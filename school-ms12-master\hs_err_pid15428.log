#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 149946368 bytes. Error detail: G1 virtual space
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (os_windows.cpp:3678), pid=15428, tid=13432
#
# JRE version: OpenJDK Runtime Environment Microsoft-9889599 (17.0.12+7) (build 17.0.12+7-LTS)
# Java VM: OpenJDK 64-Bit Server VM Microsoft-9889599 (17.0.12+7-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: -XX:MaxMetaspaceSize=512m --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED -Xmx2048m -Dfile.encoding=utf8 -Duser.country=US -Duser.language=en -Duser.variant org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.0.1

Host: Intel(R) Core(TM) i5-6200U CPU @ 2.30GHz, 4 cores, 7G,  Windows 10 , 64 bit Build 19041 (10.0.19041.5794)
Time: Sun May 11 02:26:52 2025 Russia TZ 2 Standard Time elapsed time: 459.666133 seconds (0d 0h 7m 39s)

---------------  T H R E A D  ---------------

Current thread (0x0000013c48cad6b0):  VMThread "VM Thread" [stack: 0x00000017ef800000,0x00000017ef900000] [id=13432]

Stack: [0x00000017ef800000,0x00000017ef900000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x686d99]
V  [jvm.dll+0x83daf8]
V  [jvm.dll+0x83f5a3]
V  [jvm.dll+0x83fc13]
V  [jvm.dll+0x2492ef]
V  [jvm.dll+0x683dd9]
V  [jvm.dll+0x6782fa]
V  [jvm.dll+0x30a797]
V  [jvm.dll+0x311d06]
V  [jvm.dll+0x36252e]
V  [jvm.dll+0x36276d]
V  [jvm.dll+0x2e1a9c]
V  [jvm.dll+0x2e4a76]
V  [jvm.dll+0x2ef1ac]
V  [jvm.dll+0x32231d]
V  [jvm.dll+0x84444d]
V  [jvm.dll+0x845183]
V  [jvm.dll+0x8456af]
V  [jvm.dll+0x845a94]
V  [jvm.dll+0x845b5e]
V  [jvm.dll+0x7ed87c]
V  [jvm.dll+0x685c77]
C  [ucrtbase.dll+0x21bb2]
C  [KERNEL32.DLL+0x17374]
C  [ntdll.dll+0x4cc91]

VM_Operation (0x00000017ef4ff4f0): G1PauseRemark, mode: safepoint, requested by thread 0x0000013c2b150450


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x0000013c6edbfdd0, length=90, elements={
0x0000013c2b0e4420, 0x0000013c48cb3800, 0x0000013c48cb4750, 0x0000013c48ccc260,
0x0000013c48ccd0e0, 0x0000013c48cd0c20, 0x0000013c48cd15e0, 0x0000013c48cd2380,
0x0000013c48cd2f00, 0x0000013c48d39090, 0x0000013c680e08c0, 0x0000013c680e0da0,
0x0000013c68f8f190, 0x0000013c69a03460, 0x0000013c68dd4470, 0x0000013c68ae1620,
0x0000013c69862cb0, 0x0000013c69863be0, 0x0000013c698640f0, 0x0000013c69860940,
0x0000013c69860e50, 0x0000013c69861360, 0x0000013c69861870, 0x0000013c69861d80,
0x0000013c6a30c150, 0x0000013c6a30b220, 0x0000013c6a310830, 0x0000013c6a30e4c0,
0x0000013c6a30b730, 0x0000013c6b2088a0, 0x0000013c6a9903d0, 0x0000013c698627a0,
0x0000013c6e5e8d90, 0x0000013c6e5e6a20, 0x0000013c6e5e4bc0, 0x0000013c6e5e2d60,
0x0000013c6e5e8370, 0x0000013c6e5e5af0, 0x0000013c6e5e6510, 0x0000013c6e5e7440,
0x0000013c6e5e97b0, 0x0000013c6e5e8880, 0x0000013c6e5e3c90, 0x0000013c6e5e92a0,
0x0000013c6e5e55e0, 0x0000013c6e5e2340, 0x0000013c6e5e46b0, 0x0000013c6b20bb40,
0x0000013c6b2092c0, 0x0000013c6b2097d0, 0x0000013c6b207970, 0x0000013c6b2050f0,
0x0000013c6b20ac10, 0x0000013c6b20c050, 0x0000013c6b207e80, 0x0000013c6b205600,
0x0000013c6b20b630, 0x0000013c6b209ce0, 0x0000013c6b20c560, 0x0000013c6b20ca70,
0x0000013c6b208390, 0x0000013c6b206020, 0x0000013c6b20a1f0, 0x0000013c6b206530,
0x0000013c6b20a700, 0x0000013c6b20b120, 0x0000013c6b206a40, 0x0000013c6b206f50,
0x0000013c6b207460, 0x0000013c6a311760, 0x0000013c6a30d590, 0x0000013c6a30a800,
0x0000013c6a30fe10, 0x0000013c6a30daa0, 0x0000013c6a30c660, 0x0000013c6a30dfb0,
0x0000013c6a30cb70, 0x0000013c6a30f900, 0x0000013c6a30e9d0, 0x0000013c6a30f3f0,
0x0000013c6a30eee0, 0x0000013c6a311250, 0x0000013c6a30a2f0, 0x0000013c6a311c70,
0x0000013c6a98fec0, 0x0000013c6a991300, 0x0000013c6a98f4a0, 0x0000013c6a98e060,
0x0000013c6a98ef90, 0x0000013c6a98e570
}

Java Threads: ( => current thread )
  0x0000013c2b0e4420 JavaThread "main" [_thread_blocked, id=9484, stack(0x00000017ef200000,0x00000017ef300000)]
  0x0000013c48cb3800 JavaThread "Reference Handler" daemon [_thread_blocked, id=1936, stack(0x00000017ef900000,0x00000017efa00000)]
  0x0000013c48cb4750 JavaThread "Finalizer" daemon [_thread_blocked, id=18236, stack(0x00000017efa00000,0x00000017efb00000)]
  0x0000013c48ccc260 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=13248, stack(0x00000017efb00000,0x00000017efc00000)]
  0x0000013c48ccd0e0 JavaThread "Attach Listener" daemon [_thread_blocked, id=1716, stack(0x00000017efc00000,0x00000017efd00000)]
  0x0000013c48cd0c20 JavaThread "Service Thread" daemon [_thread_blocked, id=11380, stack(0x00000017efd00000,0x00000017efe00000)]
  0x0000013c48cd15e0 JavaThread "Monitor Deflation Thread" daemon [_thread_blocked, id=9508, stack(0x00000017efe00000,0x00000017eff00000)]
  0x0000013c48cd2380 JavaThread "C2 CompilerThread0" daemon [_thread_blocked, id=13504, stack(0x00000017eff00000,0x00000017f0000000)]
  0x0000013c48cd2f00 JavaThread "C1 CompilerThread0" daemon [_thread_blocked, id=6984, stack(0x00000017f0000000,0x00000017f0100000)]
  0x0000013c48d39090 JavaThread "Sweeper thread" daemon [_thread_blocked, id=8828, stack(0x00000017f0100000,0x00000017f0200000)]
  0x0000013c680e08c0 JavaThread "Common-Cleaner" daemon [_thread_blocked, id=6592, stack(0x00000017f0200000,0x00000017f0300000)]
  0x0000013c680e0da0 JavaThread "Notification Thread" daemon [_thread_blocked, id=13528, stack(0x00000017f0300000,0x00000017f0400000)]
  0x0000013c68f8f190 JavaThread "Daemon health stats" [_thread_blocked, id=13024, stack(0x00000017f0800000,0x00000017f0900000)]
  0x0000013c69a03460 JavaThread "Incoming local TCP Connector on port 10529" [_thread_in_native, id=7568, stack(0x00000017f0900000,0x00000017f0a00000)]
  0x0000013c68dd4470 JavaThread "Daemon periodic checks" [_thread_blocked, id=1960, stack(0x00000017f0a00000,0x00000017f0b00000)]
  0x0000013c68ae1620 JavaThread "Daemon" [_thread_blocked, id=4288, stack(0x00000017f0b00000,0x00000017f0c00000)]
  0x0000013c69862cb0 JavaThread "Daemon worker" [_thread_in_native, id=15192, stack(0x00000017f0e00000,0x00000017f0f00000)]
  0x0000013c69863be0 JavaThread "Cache worker for journal cache (C:\Users\<USER>\.gradle\caches\journal-1)" [_thread_blocked, id=8236, stack(0x00000017f1200000,0x00000017f1300000)]
  0x0000013c698640f0 JavaThread "File lock request listener" [_thread_in_native, id=16568, stack(0x00000017f1300000,0x00000017f1400000)]
  0x0000013c69860940 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\.gradle\caches\8.0.1\fileHashes)" [_thread_blocked, id=18220, stack(0x00000017f1400000,0x00000017f1500000)]
  0x0000013c69860e50 JavaThread "File watcher server" daemon [_thread_in_native, id=7980, stack(0x00000017f1500000,0x00000017f1600000)]
  0x0000013c69861360 JavaThread "File watcher consumer" daemon [_thread_blocked, id=17848, stack(0x00000017f1600000,0x00000017f1700000)]
  0x0000013c69861870 JavaThread "jar transforms" [_thread_blocked, id=11132, stack(0x00000017f1700000,0x00000017f1800000)]
  0x0000013c69861d80 JavaThread "jar transforms Thread 2" [_thread_blocked, id=5124, stack(0x00000017f1800000,0x00000017f1900000)]
  0x0000013c6a30c150 JavaThread "jar transforms Thread 3" [_thread_blocked, id=14756, stack(0x00000017f1900000,0x00000017f1a00000)]
  0x0000013c6a30b220 JavaThread "Cache worker for file content cache (C:\Users\<USER>\.gradle\caches\8.0.1\fileContent)" [_thread_blocked, id=12696, stack(0x00000017f1c00000,0x00000017f1d00000)]
  0x0000013c6a310830 JavaThread "Cache worker for execution history cache (C:\Users\<USER>\.gradle\caches\8.0.1\executionHistory)" [_thread_blocked, id=8672, stack(0x00000017f1f00000,0x00000017f2000000)]
  0x0000013c6a30e4c0 JavaThread "jar transforms Thread 4" [_thread_blocked, id=14540, stack(0x00000017f2000000,0x00000017f2100000)]
  0x0000013c6a30b730 JavaThread "Cache worker for kotlin-dsl (C:\Users\<USER>\.gradle\caches\8.0.1\kotlin-dsl)" [_thread_blocked, id=2204, stack(0x00000017eef00000,0x00000017ef000000)]
  0x0000013c6b2088a0 JavaThread "Memory manager" [_thread_blocked, id=12432, stack(0x00000017f4000000,0x00000017f4100000)]
  0x0000013c6a9903d0 JavaThread "ForkJoinPool.commonPool-worker-1" daemon [_thread_blocked, id=14952, stack(0x00000017f4e00000,0x00000017f4f00000)]
  0x0000013c698627a0 JavaThread "pool-3-thread-1" [_thread_blocked, id=1328, stack(0x00000017f5000000,0x00000017f5100000)]
  0x0000013c6e5e8d90 JavaThread "Handler for socket connection from /127.0.0.1:10529 to /127.0.0.1:10825" [_thread_in_native, id=11524, stack(0x00000017f0c00000,0x00000017f0d00000)]
  0x0000013c6e5e6a20 JavaThread "Cancel handler" [_thread_blocked, id=14572, stack(0x00000017f0d00000,0x00000017f0e00000)]
  0x0000013c6e5e4bc0 JavaThread "Asynchronous log dispatcher for DefaultDaemonConnection: socket connection from /127.0.0.1:10529 to /127.0.0.1:10825" [_thread_blocked, id=8368, stack(0x00000017f0f00000,0x00000017f1000000)]
  0x0000013c6e5e2d60 JavaThread "Daemon client event forwarder" [_thread_blocked, id=15820, stack(0x00000017f1100000,0x00000017f1200000)]
  0x0000013c6e5e8370 JavaThread "Cache worker for checksums cache (C:\Users\<USER>\Desktop\New folder (7)\school-ms12-master\android\.gradle\8.0.1\checksums)" [_thread_blocked, id=1208, stack(0x00000017f1a00000,0x00000017f1b00000)]
  0x0000013c6e5e5af0 JavaThread "Cache worker for cache directory md-rule (C:\Users\<USER>\.gradle\caches\8.0.1\md-rule)" [_thread_blocked, id=7112, stack(0x00000017f1b00000,0x00000017f1c00000)]
  0x0000013c6e5e6510 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\Desktop\New folder (7)\school-ms12-master\android\.gradle\8.0.1\fileHashes)" [_thread_blocked, id=4488, stack(0x00000017f1d00000,0x00000017f1e00000)]
  0x0000013c6e5e7440 JavaThread "Cache worker for cache directory md-supplier (C:\Users\<USER>\.gradle\caches\8.0.1\md-supplier)" [_thread_blocked, id=11320, stack(0x00000017f1e00000,0x00000017f1f00000)]
  0x0000013c6e5e97b0 JavaThread "Unconstrained build operations" [_thread_blocked, id=14408, stack(0x00000017f2200000,0x00000017f2300000)]
  0x0000013c6e5e8880 JavaThread "Unconstrained build operations Thread 2" [_thread_blocked, id=12324, stack(0x00000017f2300000,0x00000017f2400000)]
  0x0000013c6e5e3c90 JavaThread "Cache worker for dependencies-accessors (C:\Users\<USER>\Desktop\New folder (7)\school-ms12-master\android\.gradle\8.0.1\dependencies-accessors)" [_thread_blocked, id=8384, stack(0x00000017f2500000,0x00000017f2600000)]
  0x0000013c6e5e92a0 JavaThread "Cache worker for Build Output Cleanup Cache (C:\Users\<USER>\Desktop\New folder (7)\school-ms12-master\node_modules\expo-dev-launcher\expo-dev-launcher-gradle-plugin\.gradle\buildOutputCleanup)" [_thread_blocked, id=5684, stack(0x00000017f2600000,0x00000017f2700000)]
  0x0000013c6e5e55e0 JavaThread "Unconstrained build operations Thread 3" [_thread_blocked, id=4668, stack(0x00000017f2700000,0x00000017f2800000)]
  0x0000013c6e5e2340 JavaThread "Unconstrained build operations Thread 4" [_thread_blocked, id=5680, stack(0x00000017f2800000,0x00000017f2900000)]
  0x0000013c6e5e46b0 JavaThread "Unconstrained build operations Thread 5" [_thread_blocked, id=11644, stack(0x00000017f2900000,0x00000017f2a00000)]
  0x0000013c6b20bb40 JavaThread "Unconstrained build operations Thread 6" [_thread_blocked, id=14656, stack(0x00000017f2a00000,0x00000017f2b00000)]
  0x0000013c6b2092c0 JavaThread "Unconstrained build operations Thread 7" [_thread_blocked, id=14124, stack(0x00000017f2b00000,0x00000017f2c00000)]
  0x0000013c6b2097d0 JavaThread "Unconstrained build operations Thread 8" [_thread_blocked, id=12916, stack(0x00000017f2c00000,0x00000017f2d00000)]
  0x0000013c6b207970 JavaThread "Unconstrained build operations Thread 9" [_thread_blocked, id=5580, stack(0x00000017f2d00000,0x00000017f2e00000)]
  0x0000013c6b2050f0 JavaThread "Unconstrained build operations Thread 10" [_thread_blocked, id=4244, stack(0x00000017f2e00000,0x00000017f2f00000)]
  0x0000013c6b20ac10 JavaThread "Unconstrained build operations Thread 11" [_thread_blocked, id=11628, stack(0x00000017f2f00000,0x00000017f3000000)]
  0x0000013c6b20c050 JavaThread "Unconstrained build operations Thread 12" [_thread_blocked, id=676, stack(0x00000017f3000000,0x00000017f3100000)]
  0x0000013c6b207e80 JavaThread "Unconstrained build operations Thread 13" [_thread_blocked, id=7004, stack(0x00000017f3100000,0x00000017f3200000)]
  0x0000013c6b205600 JavaThread "Unconstrained build operations Thread 14" [_thread_blocked, id=1076, stack(0x00000017f3200000,0x00000017f3300000)]
  0x0000013c6b20b630 JavaThread "Unconstrained build operations Thread 15" [_thread_blocked, id=11136, stack(0x00000017f3300000,0x00000017f3400000)]
  0x0000013c6b209ce0 JavaThread "Unconstrained build operations Thread 16" [_thread_blocked, id=15268, stack(0x00000017f3400000,0x00000017f3500000)]
  0x0000013c6b20c560 JavaThread "Unconstrained build operations Thread 17" [_thread_blocked, id=15316, stack(0x00000017f3500000,0x00000017f3600000)]
  0x0000013c6b20ca70 JavaThread "Unconstrained build operations Thread 18" [_thread_blocked, id=10308, stack(0x00000017f3600000,0x00000017f3700000)]
  0x0000013c6b208390 JavaThread "Unconstrained build operations Thread 19" [_thread_blocked, id=13164, stack(0x00000017f3700000,0x00000017f3800000)]
  0x0000013c6b206020 JavaThread "Unconstrained build operations Thread 20" [_thread_blocked, id=15624, stack(0x00000017f3800000,0x00000017f3900000)]
  0x0000013c6b20a1f0 JavaThread "Unconstrained build operations Thread 21" [_thread_blocked, id=2156, stack(0x00000017f3900000,0x00000017f3a00000)]
  0x0000013c6b206530 JavaThread "Unconstrained build operations Thread 22" [_thread_blocked, id=14180, stack(0x00000017f3a00000,0x00000017f3b00000)]
  0x0000013c6b20a700 JavaThread "Unconstrained build operations Thread 23" [_thread_blocked, id=18208, stack(0x00000017f3b00000,0x00000017f3c00000)]
  0x0000013c6b20b120 JavaThread "Unconstrained build operations Thread 24" [_thread_blocked, id=8904, stack(0x00000017f3c00000,0x00000017f3d00000)]
  0x0000013c6b206a40 JavaThread "Unconstrained build operations Thread 25" [_thread_blocked, id=17360, stack(0x00000017f3d00000,0x00000017f3e00000)]
  0x0000013c6b206f50 JavaThread "Unconstrained build operations Thread 26" [_thread_blocked, id=2712, stack(0x00000017f3e00000,0x00000017f3f00000)]
  0x0000013c6b207460 JavaThread "Unconstrained build operations Thread 27" [_thread_blocked, id=4760, stack(0x00000017f3f00000,0x00000017f4000000)]
  0x0000013c6a311760 JavaThread "Unconstrained build operations Thread 28" [_thread_blocked, id=1792, stack(0x00000017f4100000,0x00000017f4200000)]
  0x0000013c6a30d590 JavaThread "Cache worker for Build Output Cleanup Cache (C:\Users\<USER>\Desktop\New folder (7)\school-ms12-master\node_modules\@react-native\gradle-plugin\.gradle\buildOutputCleanup)" [_thread_blocked, id=10224, stack(0x00000017f4200000,0x00000017f4300000)]
  0x0000013c6a30a800 JavaThread "Unconstrained build operations Thread 29" [_thread_blocked, id=17336, stack(0x00000017f4300000,0x00000017f4400000)]
  0x0000013c6a30fe10 JavaThread "Unconstrained build operations Thread 30" [_thread_blocked, id=6656, stack(0x00000017f4400000,0x00000017f4500000)]
  0x0000013c6a30daa0 JavaThread "Unconstrained build operations Thread 31" [_thread_blocked, id=17424, stack(0x00000017f4500000,0x00000017f4600000)]
  0x0000013c6a30c660 JavaThread "Unconstrained build operations Thread 32" [_thread_blocked, id=4084, stack(0x00000017f4600000,0x00000017f4700000)]
  0x0000013c6a30dfb0 JavaThread "Unconstrained build operations Thread 33" [_thread_blocked, id=7116, stack(0x00000017f4700000,0x00000017f4800000)]
  0x0000013c6a30cb70 JavaThread "Unconstrained build operations Thread 34" [_thread_blocked, id=13932, stack(0x00000017f4800000,0x00000017f4900000)]
  0x0000013c6a30f900 JavaThread "Unconstrained build operations Thread 35" [_thread_blocked, id=15996, stack(0x00000017f4900000,0x00000017f4a00000)]
  0x0000013c6a30e9d0 JavaThread "Unconstrained build operations Thread 36" [_thread_blocked, id=5044, stack(0x00000017f4a00000,0x00000017f4b00000)]
  0x0000013c6a30f3f0 JavaThread "Unconstrained build operations Thread 37" [_thread_blocked, id=12508, stack(0x00000017f4b00000,0x00000017f4c00000)]
  0x0000013c6a30eee0 JavaThread "Unconstrained build operations Thread 38" [_thread_blocked, id=8124, stack(0x00000017f4c00000,0x00000017f4d00000)]
  0x0000013c6a311250 JavaThread "Unconstrained build operations Thread 39" [_thread_blocked, id=3728, stack(0x00000017f4d00000,0x00000017f4e00000)]
  0x0000013c6a30a2f0 JavaThread "Unconstrained build operations Thread 40" [_thread_blocked, id=10384, stack(0x00000017f4f00000,0x00000017f5000000)]
  0x0000013c6a311c70 JavaThread "Cache worker for Build Output Cleanup Cache (C:\Users\<USER>\Desktop\New folder (7)\school-ms12-master\android\.gradle\buildOutputCleanup)" [_thread_blocked, id=8848, stack(0x00000017f5100000,0x00000017f5200000)]
  0x0000013c6a98fec0 JavaThread "ForkJoinPool.commonPool-worker-6" daemon [_thread_blocked, id=14504, stack(0x00000017f5200000,0x00000017f5300000)]
  0x0000013c6a991300 JavaThread "Execution worker" [_thread_blocked, id=5956, stack(0x00000017f5600000,0x00000017f5700000)]
  0x0000013c6a98f4a0 JavaThread "Execution worker Thread 2" [_thread_blocked, id=9436, stack(0x00000017f5700000,0x00000017f5800000)]
  0x0000013c6a98e060 JavaThread "Execution worker Thread 3" [_thread_blocked, id=15024, stack(0x00000017f5800000,0x00000017f5900000)]
  0x0000013c6a98ef90 JavaThread "Cache worker for execution history cache (C:\Users\<USER>\Desktop\New folder (7)\school-ms12-master\node_modules\expo-dev-launcher\expo-dev-launcher-gradle-plugin\.gradle\8.0.1\executionHistory)" [_thread_blocked, id=17800, stack(0x00000017f5900000,0x00000017f5a00000)]
  0x0000013c6a98e570 JavaThread "Cache worker for execution history cache (C:\Users\<USER>\Desktop\New folder (7)\school-ms12-master\node_modules\@react-native\gradle-plugin\.gradle\8.0.1\executionHistory)" [_thread_blocked, id=16756, stack(0x00000017f5a00000,0x00000017f5b00000)]

Other Threads:
=>0x0000013c48cad6b0 VMThread "VM Thread" [stack: 0x00000017ef800000,0x00000017ef900000] [id=13432]
  0x0000013c680e1280 WatcherThread [stack: 0x00000017f0400000,0x00000017f0500000] [id=17856]
  0x0000013c2b13f500 GCTaskThread "GC Thread#0" [stack: 0x00000017ef300000,0x00000017ef400000] [id=18044]
  0x0000013c683bf030 GCTaskThread "GC Thread#1" [stack: 0x00000017f0500000,0x00000017f0600000] [id=11012]
  0x0000013c683ae110 GCTaskThread "GC Thread#2" [stack: 0x00000017f0600000,0x00000017f0700000] [id=13452]
  0x0000013c68b373a0 GCTaskThread "GC Thread#3" [stack: 0x00000017f0700000,0x00000017f0800000] [id=9448]
  0x0000013c2b150450 ConcurrentGCThread "G1 Main Marker" [stack: 0x00000017ef400000,0x00000017ef500000] [id=4556]
  0x0000013c2b150e70 ConcurrentGCThread "G1 Conc#0" [stack: 0x00000017ef500000,0x00000017ef600000] [id=11604]
  0x0000013c2b19db70 ConcurrentGCThread "G1 Refine#0" [stack: 0x00000017ef600000,0x00000017ef700000] [id=16692]
  0x0000013c68b56910 ConcurrentGCThread "G1 Refine#1" [stack: 0x00000017f2100000,0x00000017f2200000] [id=7824]
  0x0000013c2b19e5a0 ConcurrentGCThread "G1 Service" [stack: 0x00000017ef700000,0x00000017ef800000] [id=16716]

Threads with active compile tasks:

VM state: at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x0000013c2b0e12c0] Threads_lock - owner thread: 0x0000013c48cad6b0
[0x0000013c2b0e1470] Heap_lock - owner thread: 0x0000013c2b150450

Heap address: 0x0000000080000000, size: 2048 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x0000013c49000000-0x0000013c49bb0000-0x0000013c49bb0000), size 12255232, SharedBaseAddress: 0x0000013c49000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x0000013c4a000000-0x0000013c64000000, reserved size: 436207616
Narrow klass base: 0x0000013c49000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CPUs: 4 total, 4 available
 Memory: 8071M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Heap Region Size: 1M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 128M
 Heap Max Capacity: 2G
 Pre-touch: Disabled
 Parallel Workers: 4
 Concurrent Workers: 1
 Concurrent Refinement Workers: 4
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 480256K, used 374503K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 8 young (8192K), 3 survivors (3072K)
 Metaspace       used 122327K, committed 124736K, reserved 557056K
  class space    used 16622K, committed 17728K, reserved 425984K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, OA=open archive, CA=closed archive, TAMS=top-at-mark-start (previous, next)
|   0|0x0000000080000000, 0x0000000080100000, 0x0000000080100000|100%| O|  |TAMS 0x0000000080100000, 0x0000000080000000| Untracked 
|   1|0x0000000080100000, 0x0000000080200000, 0x0000000080200000|100%| O|  |TAMS 0x0000000080200000, 0x0000000080100000| Untracked 
|   2|0x0000000080200000, 0x0000000080300000, 0x0000000080300000|100%|HS|  |TAMS 0x0000000080300000, 0x0000000080200000| Complete 
|   3|0x0000000080300000, 0x0000000080400000, 0x0000000080400000|100%|HC|  |TAMS 0x0000000080400000, 0x0000000080300000| Complete 
|   4|0x0000000080400000, 0x0000000080500000, 0x0000000080500000|100%|HC|  |TAMS 0x0000000080500000, 0x0000000080400000| Complete 
|   5|0x0000000080500000, 0x0000000080600000, 0x0000000080600000|100%|HC|  |TAMS 0x0000000080600000, 0x0000000080500000| Complete 
|   6|0x0000000080600000, 0x0000000080700000, 0x0000000080700000|100%| O|  |TAMS 0x0000000080700000, 0x0000000080600000| Untracked 
|   7|0x0000000080700000, 0x0000000080800000, 0x0000000080800000|100%| O|  |TAMS 0x0000000080800000, 0x0000000080700000| Untracked 
|   8|0x0000000080800000, 0x0000000080900000, 0x0000000080900000|100%| O|  |TAMS 0x0000000080900000, 0x0000000080800000| Untracked 
|   9|0x0000000080900000, 0x0000000080a00000, 0x0000000080a00000|100%| O|  |TAMS 0x0000000080a00000, 0x0000000080900000| Untracked 
|  10|0x0000000080a00000, 0x0000000080b00000, 0x0000000080b00000|100%| O|  |TAMS 0x0000000080b00000, 0x0000000080a00000| Untracked 
|  11|0x0000000080b00000, 0x0000000080c00000, 0x0000000080c00000|100%| O|  |TAMS 0x0000000080c00000, 0x0000000080b00000| Updating 
|  12|0x0000000080c00000, 0x0000000080d00000, 0x0000000080d00000|100%| O|  |TAMS 0x0000000080d00000, 0x0000000080c00000| Untracked 
|  13|0x0000000080d00000, 0x0000000080e00000, 0x0000000080e00000|100%| O|  |TAMS 0x0000000080e00000, 0x0000000080d00000| Untracked 
|  14|0x0000000080e00000, 0x0000000080f00000, 0x0000000080f00000|100%| O|  |TAMS 0x0000000080f00000, 0x0000000080e00000| Untracked 
|  15|0x0000000080f00000, 0x0000000081000000, 0x0000000081000000|100%| O|  |TAMS 0x0000000081000000, 0x0000000080f00000| Untracked 
|  16|0x0000000081000000, 0x0000000081100000, 0x0000000081100000|100%| O|  |TAMS 0x0000000081100000, 0x0000000081000000| Untracked 
|  17|0x0000000081100000, 0x0000000081200000, 0x0000000081200000|100%| O|  |TAMS 0x0000000081200000, 0x0000000081100000| Updating 
|  18|0x0000000081200000, 0x0000000081300000, 0x0000000081300000|100%| O|  |TAMS 0x0000000081300000, 0x0000000081200000| Untracked 
|  19|0x0000000081300000, 0x0000000081400000, 0x0000000081400000|100%| O|  |TAMS 0x0000000081400000, 0x0000000081300000| Untracked 
|  20|0x0000000081400000, 0x0000000081500000, 0x0000000081500000|100%| O|  |TAMS 0x0000000081500000, 0x0000000081400000| Untracked 
|  21|0x0000000081500000, 0x0000000081600000, 0x0000000081600000|100%| O|  |TAMS 0x0000000081600000, 0x0000000081500000| Untracked 
|  22|0x0000000081600000, 0x0000000081700000, 0x0000000081700000|100%| O|  |TAMS 0x0000000081700000, 0x0000000081600000| Untracked 
|  23|0x0000000081700000, 0x0000000081800000, 0x0000000081800000|100%| O|  |TAMS 0x0000000081800000, 0x0000000081700000| Untracked 
|  24|0x0000000081800000, 0x0000000081900000, 0x0000000081900000|100%| O|  |TAMS 0x0000000081900000, 0x0000000081800000| Untracked 
|  25|0x0000000081900000, 0x0000000081a00000, 0x0000000081a00000|100%| O|  |TAMS 0x0000000081a00000, 0x0000000081900000| Untracked 
|  26|0x0000000081a00000, 0x0000000081b00000, 0x0000000081b00000|100%| O|  |TAMS 0x0000000081b00000, 0x0000000081a00000| Updating 
|  27|0x0000000081b00000, 0x0000000081c00000, 0x0000000081c00000|100%| O|  |TAMS 0x0000000081c00000, 0x0000000081b00000| Updating 
|  28|0x0000000081c00000, 0x0000000081d00000, 0x0000000081d00000|100%| O|  |TAMS 0x0000000081d00000, 0x0000000081c00000| Untracked 
|  29|0x0000000081d00000, 0x0000000081e00000, 0x0000000081e00000|100%| O|  |TAMS 0x0000000081e00000, 0x0000000081d00000| Untracked 
|  30|0x0000000081e00000, 0x0000000081f00000, 0x0000000081f00000|100%| O|  |TAMS 0x0000000081f00000, 0x0000000081e00000| Updating 
|  31|0x0000000081f00000, 0x0000000082000000, 0x0000000082000000|100%| O|  |TAMS 0x0000000082000000, 0x0000000081f00000| Untracked 
|  32|0x0000000082000000, 0x0000000082100000, 0x0000000082100000|100%| O|  |TAMS 0x0000000082100000, 0x0000000082000000| Updating 
|  33|0x0000000082100000, 0x0000000082200000, 0x0000000082200000|100%| O|  |TAMS 0x0000000082200000, 0x0000000082100000| Updating 
|  34|0x0000000082200000, 0x0000000082300000, 0x0000000082300000|100%| O|  |TAMS 0x0000000082300000, 0x0000000082200000| Updating 
|  35|0x0000000082300000, 0x0000000082400000, 0x0000000082400000|100%| O|  |TAMS 0x0000000082400000, 0x0000000082300000| Updating 
|  36|0x0000000082400000, 0x0000000082500000, 0x0000000082500000|100%| O|  |TAMS 0x0000000082500000, 0x0000000082400000| Updating 
|  37|0x0000000082500000, 0x0000000082600000, 0x0000000082600000|100%| O|  |TAMS 0x0000000082600000, 0x0000000082500000| Updating 
|  38|0x0000000082600000, 0x0000000082700000, 0x0000000082700000|100%| O|  |TAMS 0x0000000082700000, 0x0000000082600000| Untracked 
|  39|0x0000000082700000, 0x0000000082800000, 0x0000000082800000|100%| O|  |TAMS 0x0000000082800000, 0x0000000082700000| Updating 
|  40|0x0000000082800000, 0x0000000082900000, 0x0000000082900000|100%| O|  |TAMS 0x0000000082900000, 0x0000000082800000| Updating 
|  41|0x0000000082900000, 0x0000000082a00000, 0x0000000082a00000|100%| O|  |TAMS 0x0000000082a00000, 0x0000000082900000| Updating 
|  42|0x0000000082a00000, 0x0000000082b00000, 0x0000000082b00000|100%| O|  |TAMS 0x0000000082b00000, 0x0000000082a00000| Updating 
|  43|0x0000000082b00000, 0x0000000082c00000, 0x0000000082c00000|100%| O|  |TAMS 0x0000000082c00000, 0x0000000082b00000| Updating 
|  44|0x0000000082c00000, 0x0000000082d00000, 0x0000000082d00000|100%| O|  |TAMS 0x0000000082d00000, 0x0000000082c00000| Untracked 
|  45|0x0000000082d00000, 0x0000000082e00000, 0x0000000082e00000|100%|HS|  |TAMS 0x0000000082e00000, 0x0000000082d00000| Complete 
|  46|0x0000000082e00000, 0x0000000082f00000, 0x0000000082f00000|100%|HC|  |TAMS 0x0000000082f00000, 0x0000000082e00000| Complete 
|  47|0x0000000082f00000, 0x0000000083000000, 0x0000000083000000|100%|HS|  |TAMS 0x0000000083000000, 0x0000000082f00000| Complete 
|  48|0x0000000083000000, 0x0000000083100000, 0x0000000083100000|100%|HC|  |TAMS 0x0000000083100000, 0x0000000083000000| Complete 
|  49|0x0000000083100000, 0x0000000083200000, 0x0000000083200000|100%|HC|  |TAMS 0x0000000083200000, 0x0000000083100000| Complete 
|  50|0x0000000083200000, 0x0000000083300000, 0x0000000083300000|100%|HC|  |TAMS 0x0000000083300000, 0x0000000083200000| Complete 
|  51|0x0000000083300000, 0x0000000083400000, 0x0000000083400000|100%| O|  |TAMS 0x0000000083400000, 0x0000000083300000| Updating 
|  52|0x0000000083400000, 0x0000000083500000, 0x0000000083500000|100%| O|  |TAMS 0x0000000083500000, 0x0000000083400000| Untracked 
|  53|0x0000000083500000, 0x0000000083600000, 0x0000000083600000|100%| O|  |TAMS 0x0000000083600000, 0x0000000083500000| Untracked 
|  54|0x0000000083600000, 0x0000000083700000, 0x0000000083700000|100%| O|  |TAMS 0x0000000083700000, 0x0000000083600000| Updating 
|  55|0x0000000083700000, 0x0000000083800000, 0x0000000083800000|100%| O|  |TAMS 0x0000000083800000, 0x0000000083700000| Updating 
|  56|0x0000000083800000, 0x0000000083900000, 0x0000000083900000|100%| O|  |TAMS 0x0000000083900000, 0x0000000083800000| Updating 
|  57|0x0000000083900000, 0x0000000083a00000, 0x0000000083a00000|100%| O|  |TAMS 0x0000000083a00000, 0x0000000083900000| Updating 
|  58|0x0000000083a00000, 0x0000000083b00000, 0x0000000083b00000|100%| O|  |TAMS 0x0000000083b00000, 0x0000000083a00000| Updating 
|  59|0x0000000083b00000, 0x0000000083c00000, 0x0000000083c00000|100%| O|  |TAMS 0x0000000083c00000, 0x0000000083b00000| Updating 
|  60|0x0000000083c00000, 0x0000000083d00000, 0x0000000083d00000|100%| O|  |TAMS 0x0000000083d00000, 0x0000000083c00000| Updating 
|  61|0x0000000083d00000, 0x0000000083e00000, 0x0000000083e00000|100%|HS|  |TAMS 0x0000000083e00000, 0x0000000083d00000| Complete 
|  62|0x0000000083e00000, 0x0000000083f00000, 0x0000000083f00000|100%|HC|  |TAMS 0x0000000083f00000, 0x0000000083e00000| Complete 
|  63|0x0000000083f00000, 0x0000000084000000, 0x0000000084000000|100%| O|  |TAMS 0x0000000084000000, 0x0000000083f00000| Updating 
|  64|0x0000000084000000, 0x0000000084100000, 0x0000000084100000|100%| O|  |TAMS 0x0000000084100000, 0x0000000084000000| Updating 
|  65|0x0000000084100000, 0x0000000084200000, 0x0000000084200000|100%| O|  |TAMS 0x0000000084200000, 0x0000000084100000| Updating 
|  66|0x0000000084200000, 0x0000000084300000, 0x0000000084300000|100%| O|  |TAMS 0x0000000084300000, 0x0000000084200000| Updating 
|  67|0x0000000084300000, 0x0000000084400000, 0x0000000084400000|100%|HS|  |TAMS 0x0000000084400000, 0x0000000084300000| Complete 
|  68|0x0000000084400000, 0x0000000084500000, 0x0000000084500000|100%|HC|  |TAMS 0x0000000084500000, 0x0000000084400000| Complete 
|  69|0x0000000084500000, 0x0000000084600000, 0x0000000084600000|100%|HC|  |TAMS 0x0000000084600000, 0x0000000084500000| Complete 
|  70|0x0000000084600000, 0x0000000084700000, 0x0000000084700000|100%|HC|  |TAMS 0x0000000084700000, 0x0000000084600000| Complete 
|  71|0x0000000084700000, 0x0000000084800000, 0x0000000084800000|100%| O|  |TAMS 0x0000000084800000, 0x0000000084700000| Untracked 
|  72|0x0000000084800000, 0x0000000084900000, 0x0000000084900000|100%| O|  |TAMS 0x0000000084900000, 0x0000000084800000| Updating 
|  73|0x0000000084900000, 0x0000000084a00000, 0x0000000084a00000|100%| O|  |TAMS 0x0000000084a00000, 0x0000000084900000| Updating 
|  74|0x0000000084a00000, 0x0000000084a00000, 0x0000000084b00000|  0%| F|  |TAMS 0x0000000084a00000, 0x0000000084a00000| Untracked 
|  75|0x0000000084b00000, 0x0000000084c00000, 0x0000000084c00000|100%| O|  |TAMS 0x0000000084c00000, 0x0000000084b00000| Updating 
|  76|0x0000000084c00000, 0x0000000084d00000, 0x0000000084d00000|100%| O|  |TAMS 0x0000000084d00000, 0x0000000084c00000| Updating 
|  77|0x0000000084d00000, 0x0000000084e00000, 0x0000000084e00000|100%| O|  |TAMS 0x0000000084e00000, 0x0000000084d00000| Updating 
|  78|0x0000000084e00000, 0x0000000084f00000, 0x0000000084f00000|100%| O|  |TAMS 0x0000000084f00000, 0x0000000084e00000| Untracked 
|  79|0x0000000084f00000, 0x0000000085000000, 0x0000000085000000|100%| O|  |TAMS 0x0000000085000000, 0x0000000084f00000| Updating 
|  80|0x0000000085000000, 0x0000000085100000, 0x0000000085100000|100%| O|  |TAMS 0x0000000085100000, 0x0000000085000000| Updating 
|  81|0x0000000085100000, 0x0000000085200000, 0x0000000085200000|100%|HS|  |TAMS 0x0000000085200000, 0x0000000085100000| Complete 
|  82|0x0000000085200000, 0x0000000085300000, 0x0000000085300000|100%|HS|  |TAMS 0x0000000085300000, 0x0000000085200000| Complete 
|  83|0x0000000085300000, 0x0000000085400000, 0x0000000085400000|100%|HS|  |TAMS 0x0000000085400000, 0x0000000085300000| Complete 
|  84|0x0000000085400000, 0x0000000085400000, 0x0000000085500000|  0%| F|  |TAMS 0x0000000085400000, 0x0000000085400000| Untracked 
|  85|0x0000000085500000, 0x0000000085600000, 0x0000000085600000|100%| O|  |TAMS 0x0000000085600000, 0x0000000085500000| Updating 
|  86|0x0000000085600000, 0x0000000085700000, 0x0000000085700000|100%| O|  |TAMS 0x0000000085700000, 0x0000000085600000| Untracked 
|  87|0x0000000085700000, 0x0000000085800000, 0x0000000085800000|100%| O|  |TAMS 0x0000000085800000, 0x0000000085700000| Updating 
|  88|0x0000000085800000, 0x0000000085900000, 0x0000000085900000|100%| O|  |TAMS 0x0000000085900000, 0x0000000085800000| Updating 
|  89|0x0000000085900000, 0x0000000085a00000, 0x0000000085a00000|100%| O|  |TAMS 0x0000000085a00000, 0x0000000085900000| Updating 
|  90|0x0000000085a00000, 0x0000000085b00000, 0x0000000085b00000|100%| O|  |TAMS 0x0000000085b00000, 0x0000000085a00000| Untracked 
|  91|0x0000000085b00000, 0x0000000085c00000, 0x0000000085c00000|100%| O|  |TAMS 0x0000000085c00000, 0x0000000085b00000| Untracked 
|  92|0x0000000085c00000, 0x0000000085d00000, 0x0000000085d00000|100%| O|  |TAMS 0x0000000085d00000, 0x0000000085c00000| Updating 
|  93|0x0000000085d00000, 0x0000000085e00000, 0x0000000085e00000|100%| O|  |TAMS 0x0000000085e00000, 0x0000000085d00000| Updating 
|  94|0x0000000085e00000, 0x0000000085f00000, 0x0000000085f00000|100%| O|  |TAMS 0x0000000085f00000, 0x0000000085e00000| Updating 
|  95|0x0000000085f00000, 0x0000000086000000, 0x0000000086000000|100%| O|  |TAMS 0x0000000086000000, 0x0000000085f00000| Updating 
|  96|0x0000000086000000, 0x0000000086100000, 0x0000000086100000|100%| O|  |TAMS 0x0000000086100000, 0x0000000086000000| Updating 
|  97|0x0000000086100000, 0x0000000086200000, 0x0000000086200000|100%| O|  |TAMS 0x0000000086200000, 0x0000000086100000| Untracked 
|  98|0x0000000086200000, 0x0000000086300000, 0x0000000086300000|100%| O|  |TAMS 0x0000000086300000, 0x0000000086200000| Untracked 
|  99|0x0000000086300000, 0x0000000086400000, 0x0000000086400000|100%| O|  |TAMS 0x0000000086400000, 0x0000000086300000| Untracked 
| 100|0x0000000086400000, 0x0000000086500000, 0x0000000086500000|100%| O|  |TAMS 0x0000000086500000, 0x0000000086400000| Untracked 
| 101|0x0000000086500000, 0x0000000086600000, 0x0000000086600000|100%| O|  |TAMS 0x0000000086600000, 0x0000000086500000| Updating 
| 102|0x0000000086600000, 0x0000000086700000, 0x0000000086700000|100%| O|  |TAMS 0x0000000086700000, 0x0000000086600000| Updating 
| 103|0x0000000086700000, 0x0000000086800000, 0x0000000086800000|100%| O|  |TAMS 0x0000000086800000, 0x0000000086700000| Untracked 
| 104|0x0000000086800000, 0x0000000086900000, 0x0000000086900000|100%| O|  |TAMS 0x0000000086900000, 0x0000000086800000| Untracked 
| 105|0x0000000086900000, 0x0000000086a00000, 0x0000000086a00000|100%| O|  |TAMS 0x0000000086a00000, 0x0000000086900000| Updating 
| 106|0x0000000086a00000, 0x0000000086b00000, 0x0000000086b00000|100%| O|  |TAMS 0x0000000086b00000, 0x0000000086a00000| Updating 
| 107|0x0000000086b00000, 0x0000000086c00000, 0x0000000086c00000|100%| O|  |TAMS 0x0000000086c00000, 0x0000000086b00000| Updating 
| 108|0x0000000086c00000, 0x0000000086d00000, 0x0000000086d00000|100%| O|  |TAMS 0x0000000086d00000, 0x0000000086c00000| Updating 
| 109|0x0000000086d00000, 0x0000000086e00000, 0x0000000086e00000|100%| O|  |TAMS 0x0000000086e00000, 0x0000000086d00000| Updating 
| 110|0x0000000086e00000, 0x0000000086f00000, 0x0000000086f00000|100%| O|  |TAMS 0x0000000086f00000, 0x0000000086e00000| Updating 
| 111|0x0000000086f00000, 0x0000000087000000, 0x0000000087000000|100%| O|  |TAMS 0x0000000087000000, 0x0000000086f00000| Updating 
| 112|0x0000000087000000, 0x0000000087100000, 0x0000000087100000|100%| O|  |TAMS 0x0000000087100000, 0x0000000087000000| Updating 
| 113|0x0000000087100000, 0x0000000087200000, 0x0000000087200000|100%| O|  |TAMS 0x0000000087200000, 0x0000000087100000| Updating 
| 114|0x0000000087200000, 0x0000000087300000, 0x0000000087300000|100%| O|  |TAMS 0x0000000087300000, 0x0000000087200000| Updating 
| 115|0x0000000087300000, 0x0000000087400000, 0x0000000087400000|100%| O|  |TAMS 0x0000000087400000, 0x0000000087300000| Updating 
| 116|0x0000000087400000, 0x0000000087500000, 0x0000000087500000|100%| O|  |TAMS 0x0000000087500000, 0x0000000087400000| Updating 
| 117|0x0000000087500000, 0x0000000087600000, 0x0000000087600000|100%| O|  |TAMS 0x0000000087600000, 0x0000000087500000| Updating 
| 118|0x0000000087600000, 0x0000000087700000, 0x0000000087700000|100%| O|  |TAMS 0x0000000087700000, 0x0000000087600000| Updating 
| 119|0x0000000087700000, 0x0000000087800000, 0x0000000087800000|100%| O|  |TAMS 0x0000000087800000, 0x0000000087700000| Updating 
| 120|0x0000000087800000, 0x0000000087900000, 0x0000000087900000|100%| O|  |TAMS 0x0000000087900000, 0x0000000087800000| Updating 
| 121|0x0000000087900000, 0x0000000087a00000, 0x0000000087a00000|100%| O|  |TAMS 0x0000000087a00000, 0x0000000087900000| Updating 
| 122|0x0000000087a00000, 0x0000000087b00000, 0x0000000087b00000|100%| O|  |TAMS 0x0000000087b00000, 0x0000000087a00000| Updating 
| 123|0x0000000087b00000, 0x0000000087c00000, 0x0000000087c00000|100%| O|  |TAMS 0x0000000087c00000, 0x0000000087b00000| Updating 
| 124|0x0000000087c00000, 0x0000000087d00000, 0x0000000087d00000|100%| O|  |TAMS 0x0000000087d00000, 0x0000000087c00000| Updating 
| 125|0x0000000087d00000, 0x0000000087e00000, 0x0000000087e00000|100%| O|  |TAMS 0x0000000087e00000, 0x0000000087d00000| Updating 
| 126|0x0000000087e00000, 0x0000000087f00000, 0x0000000087f00000|100%| O|  |TAMS 0x0000000087f00000, 0x0000000087e00000| Updating 
| 127|0x0000000087f00000, 0x0000000088000000, 0x0000000088000000|100%| O|  |TAMS 0x0000000088000000, 0x0000000087f00000| Updating 
| 128|0x0000000088000000, 0x0000000088100000, 0x0000000088100000|100%| O|  |TAMS 0x0000000088100000, 0x0000000088000000| Updating 
| 129|0x0000000088100000, 0x0000000088200000, 0x0000000088200000|100%| O|  |TAMS 0x0000000088200000, 0x0000000088100000| Updating 
| 130|0x0000000088200000, 0x0000000088300000, 0x0000000088300000|100%| O|  |TAMS 0x0000000088300000, 0x0000000088200000| Updating 
| 131|0x0000000088300000, 0x0000000088400000, 0x0000000088400000|100%| O|  |TAMS 0x0000000088400000, 0x0000000088300000| Updating 
| 132|0x0000000088400000, 0x0000000088500000, 0x0000000088500000|100%| O|  |TAMS 0x0000000088500000, 0x0000000088400000| Updating 
| 133|0x0000000088500000, 0x0000000088600000, 0x0000000088600000|100%| O|  |TAMS 0x0000000088600000, 0x0000000088500000| Updating 
| 134|0x0000000088600000, 0x0000000088700000, 0x0000000088700000|100%| O|  |TAMS 0x0000000088700000, 0x0000000088600000| Updating 
| 135|0x0000000088700000, 0x0000000088800000, 0x0000000088800000|100%| O|  |TAMS 0x0000000088800000, 0x0000000088700000| Updating 
| 136|0x0000000088800000, 0x0000000088900000, 0x0000000088900000|100%| O|  |TAMS 0x0000000088900000, 0x0000000088800000| Updating 
| 137|0x0000000088900000, 0x0000000088a00000, 0x0000000088a00000|100%| O|  |TAMS 0x0000000088a00000, 0x0000000088900000| Updating 
| 138|0x0000000088a00000, 0x0000000088b00000, 0x0000000088b00000|100%| O|  |TAMS 0x0000000088b00000, 0x0000000088a00000| Updating 
| 139|0x0000000088b00000, 0x0000000088c00000, 0x0000000088c00000|100%| O|  |TAMS 0x0000000088c00000, 0x0000000088b00000| Updating 
| 140|0x0000000088c00000, 0x0000000088d00000, 0x0000000088d00000|100%| O|  |TAMS 0x0000000088d00000, 0x0000000088c00000| Updating 
| 141|0x0000000088d00000, 0x0000000088e00000, 0x0000000088e00000|100%| O|  |TAMS 0x0000000088e00000, 0x0000000088d00000| Updating 
| 142|0x0000000088e00000, 0x0000000088f00000, 0x0000000088f00000|100%| O|  |TAMS 0x0000000088f00000, 0x0000000088e00000| Updating 
| 143|0x0000000088f00000, 0x0000000089000000, 0x0000000089000000|100%| O|  |TAMS 0x0000000089000000, 0x0000000088f00000| Updating 
| 144|0x0000000089000000, 0x0000000089100000, 0x0000000089100000|100%| O|  |TAMS 0x0000000089100000, 0x0000000089000000| Updating 
| 145|0x0000000089100000, 0x0000000089200000, 0x0000000089200000|100%| O|  |TAMS 0x0000000089200000, 0x0000000089100000| Updating 
| 146|0x0000000089200000, 0x0000000089300000, 0x0000000089300000|100%| O|  |TAMS 0x0000000089300000, 0x0000000089200000| Updating 
| 147|0x0000000089300000, 0x0000000089400000, 0x0000000089400000|100%| O|  |TAMS 0x0000000089400000, 0x0000000089300000| Updating 
| 148|0x0000000089400000, 0x0000000089500000, 0x0000000089500000|100%| O|  |TAMS 0x0000000089500000, 0x0000000089400000| Updating 
| 149|0x0000000089500000, 0x0000000089600000, 0x0000000089600000|100%| O|  |TAMS 0x0000000089600000, 0x0000000089500000| Updating 
| 150|0x0000000089600000, 0x0000000089600000, 0x0000000089700000|  0%| F|  |TAMS 0x0000000089600000, 0x0000000089600000| Untracked 
| 151|0x0000000089700000, 0x0000000089700000, 0x0000000089800000|  0%| F|  |TAMS 0x0000000089700000, 0x0000000089700000| Untracked 
| 152|0x0000000089800000, 0x0000000089900000, 0x0000000089900000|100%| O|  |TAMS 0x0000000089900000, 0x0000000089800000| Updating 
| 153|0x0000000089900000, 0x0000000089a00000, 0x0000000089a00000|100%| O|  |TAMS 0x0000000089a00000, 0x0000000089900000| Updating 
| 154|0x0000000089a00000, 0x0000000089a00000, 0x0000000089b00000|  0%| F|  |TAMS 0x0000000089a00000, 0x0000000089a00000| Untracked 
| 155|0x0000000089b00000, 0x0000000089c00000, 0x0000000089c00000|100%| O|  |TAMS 0x0000000089c00000, 0x0000000089b00000| Updating 
| 156|0x0000000089c00000, 0x0000000089d00000, 0x0000000089d00000|100%| O|  |TAMS 0x0000000089d00000, 0x0000000089c00000| Updating 
| 157|0x0000000089d00000, 0x0000000089e00000, 0x0000000089e00000|100%| O|  |TAMS 0x0000000089e00000, 0x0000000089d00000| Updating 
| 158|0x0000000089e00000, 0x0000000089f00000, 0x0000000089f00000|100%| O|  |TAMS 0x0000000089f00000, 0x0000000089e00000| Updating 
| 159|0x0000000089f00000, 0x000000008a000000, 0x000000008a000000|100%| O|  |TAMS 0x000000008a000000, 0x0000000089f00000| Updating 
| 160|0x000000008a000000, 0x000000008a100000, 0x000000008a100000|100%| O|  |TAMS 0x000000008a100000, 0x000000008a000000| Updating 
| 161|0x000000008a100000, 0x000000008a100000, 0x000000008a200000|  0%| F|  |TAMS 0x000000008a100000, 0x000000008a100000| Untracked 
| 162|0x000000008a200000, 0x000000008a200000, 0x000000008a300000|  0%| F|  |TAMS 0x000000008a200000, 0x000000008a200000| Untracked 
| 163|0x000000008a300000, 0x000000008a300000, 0x000000008a400000|  0%| F|  |TAMS 0x000000008a300000, 0x000000008a300000| Untracked 
| 164|0x000000008a400000, 0x000000008a400000, 0x000000008a500000|  0%| F|  |TAMS 0x000000008a400000, 0x000000008a400000| Untracked 
| 165|0x000000008a500000, 0x000000008a600000, 0x000000008a600000|100%| O|  |TAMS 0x000000008a600000, 0x000000008a500000| Updating 
| 166|0x000000008a600000, 0x000000008a600000, 0x000000008a700000|  0%| F|  |TAMS 0x000000008a600000, 0x000000008a600000| Untracked 
| 167|0x000000008a700000, 0x000000008a700000, 0x000000008a800000|  0%| F|  |TAMS 0x000000008a700000, 0x000000008a700000| Untracked 
| 168|0x000000008a800000, 0x000000008a900000, 0x000000008a900000|100%| O|  |TAMS 0x000000008a900000, 0x000000008a800000| Updating 
| 169|0x000000008a900000, 0x000000008aa00000, 0x000000008aa00000|100%| O|  |TAMS 0x000000008aa00000, 0x000000008a900000| Updating 
| 170|0x000000008aa00000, 0x000000008aa00000, 0x000000008ab00000|  0%| F|  |TAMS 0x000000008aa00000, 0x000000008aa00000| Untracked 
| 171|0x000000008ab00000, 0x000000008ac00000, 0x000000008ac00000|100%| O|  |TAMS 0x000000008ac00000, 0x000000008ab00000| Updating 
| 172|0x000000008ac00000, 0x000000008ad00000, 0x000000008ad00000|100%| O|  |TAMS 0x000000008ad00000, 0x000000008ac00000| Updating 
| 173|0x000000008ad00000, 0x000000008ae00000, 0x000000008ae00000|100%| O|  |TAMS 0x000000008ae00000, 0x000000008ad00000| Updating 
| 174|0x000000008ae00000, 0x000000008af00000, 0x000000008af00000|100%| O|  |TAMS 0x000000008af00000, 0x000000008ae00000| Updating 
| 175|0x000000008af00000, 0x000000008b000000, 0x000000008b000000|100%| O|  |TAMS 0x000000008b000000, 0x000000008af00000| Updating 
| 176|0x000000008b000000, 0x000000008b100000, 0x000000008b100000|100%|HS|  |TAMS 0x000000008b100000, 0x000000008b000000| Complete 
| 177|0x000000008b100000, 0x000000008b200000, 0x000000008b200000|100%|HS|  |TAMS 0x000000008b200000, 0x000000008b100000| Complete 
| 178|0x000000008b200000, 0x000000008b300000, 0x000000008b300000|100%|HS|  |TAMS 0x000000008b300000, 0x000000008b200000| Complete 
| 179|0x000000008b300000, 0x000000008b400000, 0x000000008b400000|100%|HC|  |TAMS 0x000000008b400000, 0x000000008b300000| Complete 
| 180|0x000000008b400000, 0x000000008b500000, 0x000000008b500000|100%| O|  |TAMS 0x000000008b500000, 0x000000008b400000| Untracked 
| 181|0x000000008b500000, 0x000000008b600000, 0x000000008b600000|100%| O|  |TAMS 0x000000008b600000, 0x000000008b500000| Updating 
| 182|0x000000008b600000, 0x000000008b700000, 0x000000008b700000|100%| O|  |TAMS 0x000000008b700000, 0x000000008b600000| Updating 
| 183|0x000000008b700000, 0x000000008b800000, 0x000000008b800000|100%| O|  |TAMS 0x000000008b800000, 0x000000008b700000| Updating 
| 184|0x000000008b800000, 0x000000008b900000, 0x000000008b900000|100%| O|  |TAMS 0x000000008b900000, 0x000000008b800000| Updating 
| 185|0x000000008b900000, 0x000000008ba00000, 0x000000008ba00000|100%| O|  |TAMS 0x000000008ba00000, 0x000000008b900000| Updating 
| 186|0x000000008ba00000, 0x000000008bb00000, 0x000000008bb00000|100%| O|  |TAMS 0x000000008bb00000, 0x000000008ba00000| Updating 
| 187|0x000000008bb00000, 0x000000008bb00000, 0x000000008bc00000|  0%| F|  |TAMS 0x000000008bb00000, 0x000000008bb00000| Untracked 
| 188|0x000000008bc00000, 0x000000008bc00000, 0x000000008bd00000|  0%| F|  |TAMS 0x000000008bc00000, 0x000000008bc00000| Untracked 
| 189|0x000000008bd00000, 0x000000008be00000, 0x000000008be00000|100%| O|  |TAMS 0x000000008be00000, 0x000000008bd00000| Updating 
| 190|0x000000008be00000, 0x000000008bf00000, 0x000000008bf00000|100%| O|  |TAMS 0x000000008bf00000, 0x000000008be00000| Updating 
| 191|0x000000008bf00000, 0x000000008bf00000, 0x000000008c000000|  0%| F|  |TAMS 0x000000008bf00000, 0x000000008bf00000| Untracked 
| 192|0x000000008c000000, 0x000000008c100000, 0x000000008c100000|100%| O|  |TAMS 0x000000008c100000, 0x000000008c000000| Updating 
| 193|0x000000008c100000, 0x000000008c200000, 0x000000008c200000|100%| O|  |TAMS 0x000000008c200000, 0x000000008c100000| Updating 
| 194|0x000000008c200000, 0x000000008c300000, 0x000000008c300000|100%| O|  |TAMS 0x000000008c300000, 0x000000008c200000| Updating 
| 195|0x000000008c300000, 0x000000008c400000, 0x000000008c400000|100%| O|  |TAMS 0x000000008c400000, 0x000000008c300000| Updating 
| 196|0x000000008c400000, 0x000000008c400000, 0x000000008c500000|  0%| F|  |TAMS 0x000000008c400000, 0x000000008c400000| Untracked 
| 197|0x000000008c500000, 0x000000008c600000, 0x000000008c600000|100%| O|  |TAMS 0x000000008c600000, 0x000000008c500000| Updating 
| 198|0x000000008c600000, 0x000000008c700000, 0x000000008c700000|100%| O|  |TAMS 0x000000008c700000, 0x000000008c600000| Updating 
| 199|0x000000008c700000, 0x000000008c800000, 0x000000008c800000|100%| O|  |TAMS 0x000000008c800000, 0x000000008c700000| Updating 
| 200|0x000000008c800000, 0x000000008c800000, 0x000000008c900000|  0%| F|  |TAMS 0x000000008c800000, 0x000000008c800000| Untracked 
| 201|0x000000008c900000, 0x000000008c900000, 0x000000008ca00000|  0%| F|  |TAMS 0x000000008c900000, 0x000000008c900000| Untracked 
| 202|0x000000008ca00000, 0x000000008ca00000, 0x000000008cb00000|  0%| F|  |TAMS 0x000000008ca00000, 0x000000008ca00000| Untracked 
| 203|0x000000008cb00000, 0x000000008cb00000, 0x000000008cc00000|  0%| F|  |TAMS 0x000000008cb00000, 0x000000008cb00000| Untracked 
| 204|0x000000008cc00000, 0x000000008cd00000, 0x000000008cd00000|100%|HS|  |TAMS 0x000000008cd00000, 0x000000008cc00000| Complete 
| 205|0x000000008cd00000, 0x000000008ce00000, 0x000000008ce00000|100%|HS|  |TAMS 0x000000008ce00000, 0x000000008cd00000| Complete 
| 206|0x000000008ce00000, 0x000000008cf00000, 0x000000008cf00000|100%|HS|  |TAMS 0x000000008cf00000, 0x000000008ce00000| Complete 
| 207|0x000000008cf00000, 0x000000008d000000, 0x000000008d000000|100%| O|  |TAMS 0x000000008d000000, 0x000000008cf00000| Untracked 
| 208|0x000000008d000000, 0x000000008d100000, 0x000000008d100000|100%| O|  |TAMS 0x000000008d100000, 0x000000008d000000| Updating 
| 209|0x000000008d100000, 0x000000008d200000, 0x000000008d200000|100%| O|  |TAMS 0x000000008d200000, 0x000000008d100000| Updating 
| 210|0x000000008d200000, 0x000000008d300000, 0x000000008d300000|100%| O|  |TAMS 0x000000008d300000, 0x000000008d200000| Untracked 
| 211|0x000000008d300000, 0x000000008d400000, 0x000000008d400000|100%| O|  |TAMS 0x000000008d400000, 0x000000008d300000| Updating 
| 212|0x000000008d400000, 0x000000008d500000, 0x000000008d500000|100%| O|  |TAMS 0x000000008d500000, 0x000000008d400000| Untracked 
| 213|0x000000008d500000, 0x000000008d600000, 0x000000008d600000|100%| O|  |TAMS 0x000000008d600000, 0x000000008d500000| Untracked 
| 214|0x000000008d600000, 0x000000008d700000, 0x000000008d700000|100%| O|  |TAMS 0x000000008d700000, 0x000000008d600000| Untracked 
| 215|0x000000008d700000, 0x000000008d800000, 0x000000008d800000|100%| O|  |TAMS 0x000000008d800000, 0x000000008d700000| Updating 
| 216|0x000000008d800000, 0x000000008d900000, 0x000000008d900000|100%| O|  |TAMS 0x000000008d900000, 0x000000008d800000| Untracked 
| 217|0x000000008d900000, 0x000000008da00000, 0x000000008da00000|100%| O|  |TAMS 0x000000008da00000, 0x000000008d900000| Untracked 
| 218|0x000000008da00000, 0x000000008db00000, 0x000000008db00000|100%| O|  |TAMS 0x000000008db00000, 0x000000008da00000| Updating 
| 219|0x000000008db00000, 0x000000008dc00000, 0x000000008dc00000|100%| O|  |TAMS 0x000000008dc00000, 0x000000008db00000| Updating 
| 220|0x000000008dc00000, 0x000000008dd00000, 0x000000008dd00000|100%| O|  |TAMS 0x000000008dd00000, 0x000000008dc00000| Updating 
| 221|0x000000008dd00000, 0x000000008de00000, 0x000000008de00000|100%| O|  |TAMS 0x000000008de00000, 0x000000008dd00000| Updating 
| 222|0x000000008de00000, 0x000000008de00000, 0x000000008df00000|  0%| F|  |TAMS 0x000000008de00000, 0x000000008de00000| Untracked 
| 223|0x000000008df00000, 0x000000008e000000, 0x000000008e000000|100%| O|  |TAMS 0x000000008e000000, 0x000000008df00000| Updating 
| 224|0x000000008e000000, 0x000000008e100000, 0x000000008e100000|100%| O|  |TAMS 0x000000008e100000, 0x000000008e000000| Updating 
| 225|0x000000008e100000, 0x000000008e200000, 0x000000008e200000|100%| O|  |TAMS 0x000000008e200000, 0x000000008e100000| Updating 
| 226|0x000000008e200000, 0x000000008e200000, 0x000000008e300000|  0%| F|  |TAMS 0x000000008e200000, 0x000000008e200000| Untracked 
| 227|0x000000008e300000, 0x000000008e400000, 0x000000008e400000|100%| O|  |TAMS 0x000000008e400000, 0x000000008e300000| Updating 
| 228|0x000000008e400000, 0x000000008e500000, 0x000000008e500000|100%| O|  |TAMS 0x000000008e500000, 0x000000008e400000| Updating 
| 229|0x000000008e500000, 0x000000008e600000, 0x000000008e600000|100%| O|  |TAMS 0x000000008e600000, 0x000000008e500000| Updating 
| 230|0x000000008e600000, 0x000000008e700000, 0x000000008e700000|100%| O|  |TAMS 0x000000008e700000, 0x000000008e600000| Untracked 
| 231|0x000000008e700000, 0x000000008e800000, 0x000000008e800000|100%| O|  |TAMS 0x000000008e800000, 0x000000008e700000| Untracked 
| 232|0x000000008e800000, 0x000000008e900000, 0x000000008e900000|100%| O|  |TAMS 0x000000008e900000, 0x000000008e800000| Updating 
| 233|0x000000008e900000, 0x000000008ea00000, 0x000000008ea00000|100%| O|  |TAMS 0x000000008ea00000, 0x000000008e900000| Untracked 
| 234|0x000000008ea00000, 0x000000008eb00000, 0x000000008eb00000|100%| O|  |TAMS 0x000000008eb00000, 0x000000008ea00000| Untracked 
| 235|0x000000008eb00000, 0x000000008ec00000, 0x000000008ec00000|100%| O|  |TAMS 0x000000008ec00000, 0x000000008eb00000| Updating 
| 236|0x000000008ec00000, 0x000000008ed00000, 0x000000008ed00000|100%| O|  |TAMS 0x000000008ed00000, 0x000000008ec00000| Updating 
| 237|0x000000008ed00000, 0x000000008ee00000, 0x000000008ee00000|100%| O|  |TAMS 0x000000008ee00000, 0x000000008ed00000| Updating 
| 238|0x000000008ee00000, 0x000000008ef00000, 0x000000008ef00000|100%| O|  |TAMS 0x000000008ef00000, 0x000000008ee00000| Updating 
| 239|0x000000008ef00000, 0x000000008f000000, 0x000000008f000000|100%| O|  |TAMS 0x000000008f000000, 0x000000008ef00000| Updating 
| 240|0x000000008f000000, 0x000000008f100000, 0x000000008f100000|100%| O|  |TAMS 0x000000008f100000, 0x000000008f000000| Updating 
| 241|0x000000008f100000, 0x000000008f200000, 0x000000008f200000|100%| O|  |TAMS 0x000000008f200000, 0x000000008f100000| Updating 
| 242|0x000000008f200000, 0x000000008f300000, 0x000000008f300000|100%| O|  |TAMS 0x000000008f300000, 0x000000008f200000| Updating 
| 243|0x000000008f300000, 0x000000008f400000, 0x000000008f400000|100%| O|  |TAMS 0x000000008f400000, 0x000000008f300000| Updating 
| 244|0x000000008f400000, 0x000000008f500000, 0x000000008f500000|100%| O|  |TAMS 0x000000008f500000, 0x000000008f400000| Updating 
| 245|0x000000008f500000, 0x000000008f600000, 0x000000008f600000|100%| O|  |TAMS 0x000000008f600000, 0x000000008f500000| Updating 
| 246|0x000000008f600000, 0x000000008f700000, 0x000000008f700000|100%|HS|  |TAMS 0x000000008f700000, 0x000000008f600000| Complete 
| 247|0x000000008f700000, 0x000000008f800000, 0x000000008f800000|100%| O|  |TAMS 0x000000008f800000, 0x000000008f700000| Untracked 
| 248|0x000000008f800000, 0x000000008f900000, 0x000000008f900000|100%| O|  |TAMS 0x000000008f900000, 0x000000008f800000| Updating 
| 249|0x000000008f900000, 0x000000008fa00000, 0x000000008fa00000|100%| O|  |TAMS 0x000000008fa00000, 0x000000008f900000| Updating 
| 250|0x000000008fa00000, 0x000000008fb00000, 0x000000008fb00000|100%| O|  |TAMS 0x000000008fb00000, 0x000000008fa00000| Updating 
| 251|0x000000008fb00000, 0x000000008fc00000, 0x000000008fc00000|100%| O|  |TAMS 0x000000008fc00000, 0x000000008fb00000| Untracked 
| 252|0x000000008fc00000, 0x000000008fd00000, 0x000000008fd00000|100%| O|  |TAMS 0x000000008fd00000, 0x000000008fc00000| Untracked 
| 253|0x000000008fd00000, 0x000000008fe00000, 0x000000008fe00000|100%| O|  |TAMS 0x000000008fe00000, 0x000000008fd00000| Untracked 
| 254|0x000000008fe00000, 0x000000008ff00000, 0x000000008ff00000|100%| O|  |TAMS 0x000000008ff00000, 0x000000008fe00000| Untracked 
| 255|0x000000008ff00000, 0x0000000090000000, 0x0000000090000000|100%| O|  |TAMS 0x0000000090000000, 0x000000008ff00000| Untracked 
| 256|0x0000000090000000, 0x0000000090100000, 0x0000000090100000|100%| O|  |TAMS 0x0000000090100000, 0x0000000090000000| Updating 
| 257|0x0000000090100000, 0x0000000090200000, 0x0000000090200000|100%| O|  |TAMS 0x0000000090200000, 0x0000000090100000| Updating 
| 258|0x0000000090200000, 0x0000000090300000, 0x0000000090300000|100%| O|  |TAMS 0x0000000090300000, 0x0000000090200000| Updating 
| 259|0x0000000090300000, 0x0000000090400000, 0x0000000090400000|100%| O|  |TAMS 0x0000000090400000, 0x0000000090300000| Updating 
| 260|0x0000000090400000, 0x0000000090500000, 0x0000000090500000|100%| O|  |TAMS 0x0000000090500000, 0x0000000090400000| Untracked 
| 261|0x0000000090500000, 0x0000000090600000, 0x0000000090600000|100%| O|  |TAMS 0x0000000090600000, 0x0000000090500000| Untracked 
| 262|0x0000000090600000, 0x0000000090700000, 0x0000000090700000|100%| O|  |TAMS 0x0000000090700000, 0x0000000090600000| Updating 
| 263|0x0000000090700000, 0x0000000090800000, 0x0000000090800000|100%| O|  |TAMS 0x0000000090800000, 0x0000000090700000| Untracked 
| 264|0x0000000090800000, 0x0000000090900000, 0x0000000090900000|100%| O|  |TAMS 0x0000000090900000, 0x0000000090800000| Untracked 
| 265|0x0000000090900000, 0x0000000090a00000, 0x0000000090a00000|100%| O|  |TAMS 0x0000000090a00000, 0x0000000090900000| Updating 
| 266|0x0000000090a00000, 0x0000000090b00000, 0x0000000090b00000|100%| O|  |TAMS 0x0000000090b00000, 0x0000000090a00000| Untracked 
| 267|0x0000000090b00000, 0x0000000090c00000, 0x0000000090c00000|100%| O|  |TAMS 0x0000000090c00000, 0x0000000090b00000| Untracked 
| 268|0x0000000090c00000, 0x0000000090d00000, 0x0000000090d00000|100%| O|  |TAMS 0x0000000090d00000, 0x0000000090c00000| Untracked 
| 269|0x0000000090d00000, 0x0000000090e00000, 0x0000000090e00000|100%| O|  |TAMS 0x0000000090e00000, 0x0000000090d00000| Untracked 
| 270|0x0000000090e00000, 0x0000000090f00000, 0x0000000090f00000|100%| O|  |TAMS 0x0000000090f00000, 0x0000000090e00000| Updating 
| 271|0x0000000090f00000, 0x0000000091000000, 0x0000000091000000|100%| O|  |TAMS 0x0000000091000000, 0x0000000090f00000| Updating 
| 272|0x0000000091000000, 0x0000000091000000, 0x0000000091100000|  0%| F|  |TAMS 0x0000000091000000, 0x0000000091000000| Untracked 
| 273|0x0000000091100000, 0x0000000091200000, 0x0000000091200000|100%| O|  |TAMS 0x0000000091200000, 0x0000000091100000| Updating 
| 274|0x0000000091200000, 0x0000000091300000, 0x0000000091300000|100%| O|  |TAMS 0x0000000091300000, 0x0000000091200000| Updating 
| 275|0x0000000091300000, 0x0000000091300000, 0x0000000091400000|  0%| F|  |TAMS 0x0000000091300000, 0x0000000091300000| Untracked 
| 276|0x0000000091400000, 0x0000000091500000, 0x0000000091500000|100%| O|  |TAMS 0x0000000091500000, 0x0000000091400000| Updating 
| 277|0x0000000091500000, 0x0000000091500000, 0x0000000091600000|  0%| F|  |TAMS 0x0000000091500000, 0x0000000091500000| Untracked 
| 278|0x0000000091600000, 0x0000000091700000, 0x0000000091700000|100%| O|  |TAMS 0x0000000091700000, 0x0000000091600000| Updating 
| 279|0x0000000091700000, 0x0000000091800000, 0x0000000091800000|100%| O|  |TAMS 0x0000000091800000, 0x0000000091700000| Updating 
| 280|0x0000000091800000, 0x0000000091900000, 0x0000000091900000|100%| O|  |TAMS 0x0000000091900000, 0x0000000091800000| Untracked 
| 281|0x0000000091900000, 0x0000000091a00000, 0x0000000091a00000|100%| O|  |TAMS 0x0000000091a00000, 0x0000000091900000| Updating 
| 282|0x0000000091a00000, 0x0000000091b00000, 0x0000000091b00000|100%| O|  |TAMS 0x0000000091b00000, 0x0000000091a00000| Updating 
| 283|0x0000000091b00000, 0x0000000091c00000, 0x0000000091c00000|100%| O|  |TAMS 0x0000000091c00000, 0x0000000091b00000| Updating 
| 284|0x0000000091c00000, 0x0000000091d00000, 0x0000000091d00000|100%| O|  |TAMS 0x0000000091d00000, 0x0000000091c00000| Untracked 
| 285|0x0000000091d00000, 0x0000000091e00000, 0x0000000091e00000|100%| O|  |TAMS 0x0000000091e00000, 0x0000000091d00000| Updating 
| 286|0x0000000091e00000, 0x0000000091f00000, 0x0000000091f00000|100%| O|  |TAMS 0x0000000091f00000, 0x0000000091e00000| Updating 
| 287|0x0000000091f00000, 0x0000000092000000, 0x0000000092000000|100%| O|  |TAMS 0x0000000092000000, 0x0000000091f00000| Updating 
| 288|0x0000000092000000, 0x0000000092100000, 0x0000000092100000|100%| O|  |TAMS 0x0000000092100000, 0x0000000092000000| Updating 
| 289|0x0000000092100000, 0x0000000092200000, 0x0000000092200000|100%| O|  |TAMS 0x0000000092200000, 0x0000000092100000| Updating 
| 290|0x0000000092200000, 0x0000000092300000, 0x0000000092300000|100%| O|  |TAMS 0x0000000092300000, 0x0000000092200000| Updating 
| 291|0x0000000092300000, 0x0000000092400000, 0x0000000092400000|100%| O|  |TAMS 0x0000000092400000, 0x0000000092300000| Updating 
| 292|0x0000000092400000, 0x0000000092500000, 0x0000000092500000|100%| O|  |TAMS 0x0000000092500000, 0x0000000092400000| Updating 
| 293|0x0000000092500000, 0x0000000092600000, 0x0000000092600000|100%| O|  |TAMS 0x0000000092600000, 0x0000000092500000| Updating 
| 294|0x0000000092600000, 0x0000000092700000, 0x0000000092700000|100%| O|  |TAMS 0x0000000092700000, 0x0000000092600000| Untracked 
| 295|0x0000000092700000, 0x0000000092800000, 0x0000000092800000|100%| O|  |TAMS 0x0000000092800000, 0x0000000092700000| Updating 
| 296|0x0000000092800000, 0x0000000092900000, 0x0000000092900000|100%| O|  |TAMS 0x0000000092900000, 0x0000000092800000| Updating 
| 297|0x0000000092900000, 0x0000000092a00000, 0x0000000092a00000|100%| O|  |TAMS 0x0000000092a00000, 0x0000000092900000| Updating 
| 298|0x0000000092a00000, 0x0000000092b00000, 0x0000000092b00000|100%| O|  |TAMS 0x0000000092b00000, 0x0000000092a00000| Updating 
| 299|0x0000000092b00000, 0x0000000092c00000, 0x0000000092c00000|100%| O|  |TAMS 0x0000000092c00000, 0x0000000092b00000| Updating 
| 300|0x0000000092c00000, 0x0000000092d00000, 0x0000000092d00000|100%| O|  |TAMS 0x0000000092d00000, 0x0000000092c00000| Updating 
| 301|0x0000000092d00000, 0x0000000092e00000, 0x0000000092e00000|100%| O|  |TAMS 0x0000000092e00000, 0x0000000092d00000| Updating 
| 302|0x0000000092e00000, 0x0000000092f00000, 0x0000000092f00000|100%| O|  |TAMS 0x0000000092f00000, 0x0000000092e00000| Updating 
| 303|0x0000000092f00000, 0x0000000093000000, 0x0000000093000000|100%| O|  |TAMS 0x0000000093000000, 0x0000000092f00000| Updating 
| 304|0x0000000093000000, 0x0000000093100000, 0x0000000093100000|100%| O|  |TAMS 0x0000000093100000, 0x0000000093000000| Updating 
| 305|0x0000000093100000, 0x0000000093200000, 0x0000000093200000|100%| O|  |TAMS 0x0000000093200000, 0x0000000093100000| Updating 
| 306|0x0000000093200000, 0x0000000093300000, 0x0000000093300000|100%| O|  |TAMS 0x0000000093300000, 0x0000000093200000| Updating 
| 307|0x0000000093300000, 0x0000000093400000, 0x0000000093400000|100%| O|  |TAMS 0x0000000093400000, 0x0000000093300000| Updating 
| 308|0x0000000093400000, 0x0000000093500000, 0x0000000093500000|100%| O|  |TAMS 0x0000000093500000, 0x0000000093400000| Updating 
| 309|0x0000000093500000, 0x0000000093600000, 0x0000000093600000|100%| O|  |TAMS 0x0000000093600000, 0x0000000093500000| Updating 
| 310|0x0000000093600000, 0x0000000093700000, 0x0000000093700000|100%| O|  |TAMS 0x0000000093700000, 0x0000000093600000| Updating 
| 311|0x0000000093700000, 0x0000000093700000, 0x0000000093800000|  0%| F|  |TAMS 0x0000000093700000, 0x0000000093700000| Untracked 
| 312|0x0000000093800000, 0x0000000093900000, 0x0000000093900000|100%| O|  |TAMS 0x0000000093900000, 0x0000000093800000| Updating 
| 313|0x0000000093900000, 0x0000000093900000, 0x0000000093a00000|  0%| F|  |TAMS 0x0000000093900000, 0x0000000093900000| Untracked 
| 314|0x0000000093a00000, 0x0000000093b00000, 0x0000000093b00000|100%| O|  |TAMS 0x0000000093b00000, 0x0000000093a00000| Updating 
| 315|0x0000000093b00000, 0x0000000093b00000, 0x0000000093c00000|  0%| F|  |TAMS 0x0000000093b00000, 0x0000000093b00000| Untracked 
| 316|0x0000000093c00000, 0x0000000093d00000, 0x0000000093d00000|100%| O|  |TAMS 0x0000000093d00000, 0x0000000093c00000| Updating 
| 317|0x0000000093d00000, 0x0000000093e00000, 0x0000000093e00000|100%| O|  |TAMS 0x0000000093e00000, 0x0000000093d00000| Updating 
| 318|0x0000000093e00000, 0x0000000093e00000, 0x0000000093f00000|  0%| F|  |TAMS 0x0000000093e00000, 0x0000000093e00000| Untracked 
| 319|0x0000000093f00000, 0x0000000094000000, 0x0000000094000000|100%| O|  |TAMS 0x0000000094000000, 0x0000000093f00000| Updating 
| 320|0x0000000094000000, 0x0000000094000000, 0x0000000094100000|  0%| F|  |TAMS 0x0000000094000000, 0x0000000094000000| Untracked 
| 321|0x0000000094100000, 0x0000000094200000, 0x0000000094200000|100%| O|  |TAMS 0x0000000094200000, 0x0000000094100000| Updating 
| 322|0x0000000094200000, 0x0000000094300000, 0x0000000094300000|100%| O|  |TAMS 0x0000000094300000, 0x0000000094200000| Updating 
| 323|0x0000000094300000, 0x0000000094400000, 0x0000000094400000|100%| O|  |TAMS 0x0000000094400000, 0x0000000094300000| Updating 
| 324|0x0000000094400000, 0x0000000094500000, 0x0000000094500000|100%| O|  |TAMS 0x0000000094500000, 0x0000000094400000| Updating 
| 325|0x0000000094500000, 0x0000000094600000, 0x0000000094600000|100%| O|  |TAMS 0x0000000094600000, 0x0000000094500000| Updating 
| 326|0x0000000094600000, 0x0000000094700000, 0x0000000094700000|100%| O|  |TAMS 0x0000000094700000, 0x0000000094600000| Updating 
| 327|0x0000000094700000, 0x0000000094800000, 0x0000000094800000|100%| O|  |TAMS 0x0000000094800000, 0x0000000094700000| Updating 
| 328|0x0000000094800000, 0x0000000094800000, 0x0000000094900000|  0%| F|  |TAMS 0x0000000094800000, 0x0000000094800000| Untracked 
| 329|0x0000000094900000, 0x0000000094900000, 0x0000000094a00000|  0%| F|  |TAMS 0x0000000094900000, 0x0000000094900000| Untracked 
| 330|0x0000000094a00000, 0x0000000094a00000, 0x0000000094b00000|  0%| F|  |TAMS 0x0000000094a00000, 0x0000000094a00000| Untracked 
| 331|0x0000000094b00000, 0x0000000094b00000, 0x0000000094c00000|  0%| F|  |TAMS 0x0000000094b00000, 0x0000000094b00000| Untracked 
| 332|0x0000000094c00000, 0x0000000094c00000, 0x0000000094d00000|  0%| F|  |TAMS 0x0000000094c00000, 0x0000000094c00000| Untracked 
| 333|0x0000000094d00000, 0x0000000094d00000, 0x0000000094e00000|  0%| F|  |TAMS 0x0000000094d00000, 0x0000000094d00000| Untracked 
| 334|0x0000000094e00000, 0x0000000094f00000, 0x0000000094f00000|100%| O|  |TAMS 0x0000000094f00000, 0x0000000094e00000| Updating 
| 335|0x0000000094f00000, 0x0000000094f00000, 0x0000000095000000|  0%| F|  |TAMS 0x0000000094f00000, 0x0000000094f00000| Untracked 
| 336|0x0000000095000000, 0x0000000095100000, 0x0000000095100000|100%| O|  |TAMS 0x0000000095100000, 0x0000000095000000| Updating 
| 337|0x0000000095100000, 0x0000000095100000, 0x0000000095200000|  0%| F|  |TAMS 0x0000000095100000, 0x0000000095100000| Untracked 
| 338|0x0000000095200000, 0x0000000095300000, 0x0000000095300000|100%| O|  |TAMS 0x0000000095300000, 0x0000000095200000| Updating 
| 339|0x0000000095300000, 0x0000000095400000, 0x0000000095400000|100%| O|  |TAMS 0x0000000095400000, 0x0000000095300000| Updating 
| 340|0x0000000095400000, 0x0000000095500000, 0x0000000095500000|100%| O|  |TAMS 0x0000000095500000, 0x0000000095400000| Updating 
| 341|0x0000000095500000, 0x0000000095600000, 0x0000000095600000|100%| O|  |TAMS 0x0000000095600000, 0x0000000095500000| Updating 
| 342|0x0000000095600000, 0x0000000095700000, 0x0000000095700000|100%| O|  |TAMS 0x0000000095700000, 0x0000000095600000| Updating 
| 343|0x0000000095700000, 0x0000000095800000, 0x0000000095800000|100%| O|  |TAMS 0x0000000095800000, 0x0000000095700000| Updating 
| 344|0x0000000095800000, 0x0000000095900000, 0x0000000095900000|100%| O|  |TAMS 0x0000000095900000, 0x0000000095800000| Updating 
| 345|0x0000000095900000, 0x0000000095900000, 0x0000000095a00000|  0%| F|  |TAMS 0x0000000095900000, 0x0000000095900000| Untracked 
| 346|0x0000000095a00000, 0x0000000095b00000, 0x0000000095b00000|100%| O|  |TAMS 0x0000000095b00000, 0x0000000095a00000| Updating 
| 347|0x0000000095b00000, 0x0000000095b00000, 0x0000000095c00000|  0%| F|  |TAMS 0x0000000095b00000, 0x0000000095b00000| Untracked 
| 348|0x0000000095c00000, 0x0000000095d00000, 0x0000000095d00000|100%| O|  |TAMS 0x0000000095d00000, 0x0000000095c00000| Updating 
| 349|0x0000000095d00000, 0x0000000095e00000, 0x0000000095e00000|100%| O|  |TAMS 0x0000000095e00000, 0x0000000095d00000| Updating 
| 350|0x0000000095e00000, 0x0000000095e00000, 0x0000000095f00000|  0%| F|  |TAMS 0x0000000095e00000, 0x0000000095e00000| Untracked 
| 351|0x0000000095f00000, 0x0000000096000000, 0x0000000096000000|100%| O|  |TAMS 0x0000000096000000, 0x0000000095f00000| Updating 
| 352|0x0000000096000000, 0x0000000096100000, 0x0000000096100000|100%| O|  |TAMS 0x0000000096100000, 0x0000000096000000| Updating 
| 353|0x0000000096100000, 0x0000000096200000, 0x0000000096200000|100%| O|  |TAMS 0x0000000096200000, 0x0000000096100000| Updating 
| 354|0x0000000096200000, 0x0000000096300000, 0x0000000096300000|100%| O|  |TAMS 0x0000000096300000, 0x0000000096200000| Updating 
| 355|0x0000000096300000, 0x0000000096400000, 0x0000000096400000|100%| O|  |TAMS 0x0000000096400000, 0x0000000096300000| Updating 
| 356|0x0000000096400000, 0x0000000096400000, 0x0000000096500000|  0%| F|  |TAMS 0x0000000096400000, 0x0000000096400000| Untracked 
| 357|0x0000000096500000, 0x0000000096600000, 0x0000000096600000|100%| O|  |TAMS 0x0000000096600000, 0x0000000096500000| Updating 
| 358|0x0000000096600000, 0x0000000096600000, 0x0000000096700000|  0%| F|  |TAMS 0x0000000096600000, 0x0000000096600000| Untracked 
| 359|0x0000000096700000, 0x0000000096700000, 0x0000000096800000|  0%| F|  |TAMS 0x0000000096700000, 0x0000000096700000| Untracked 
| 360|0x0000000096800000, 0x0000000096800000, 0x0000000096900000|  0%| F|  |TAMS 0x0000000096800000, 0x0000000096800000| Untracked 
| 361|0x0000000096900000, 0x0000000096a00000, 0x0000000096a00000|100%| O|  |TAMS 0x0000000096a00000, 0x0000000096900000| Updating 
| 362|0x0000000096a00000, 0x0000000096a00000, 0x0000000096b00000|  0%| F|  |TAMS 0x0000000096a00000, 0x0000000096a00000| Untracked 
| 363|0x0000000096b00000, 0x0000000096c00000, 0x0000000096c00000|100%| O|  |TAMS 0x0000000096c00000, 0x0000000096b00000| Updating 
| 364|0x0000000096c00000, 0x0000000096d00000, 0x0000000096d00000|100%| O|  |TAMS 0x0000000096d00000, 0x0000000096c00000| Updating 
| 365|0x0000000096d00000, 0x0000000096d00000, 0x0000000096e00000|  0%| F|  |TAMS 0x0000000096d00000, 0x0000000096d00000| Untracked 
| 366|0x0000000096e00000, 0x0000000096f00000, 0x0000000096f00000|100%| O|  |TAMS 0x0000000096f00000, 0x0000000096e00000| Updating 
| 367|0x0000000096f00000, 0x0000000096f00000, 0x0000000097000000|  0%| F|  |TAMS 0x0000000096f00000, 0x0000000096f00000| Untracked 
| 368|0x0000000097000000, 0x0000000097000000, 0x0000000097100000|  0%| F|  |TAMS 0x0000000097000000, 0x0000000097000000| Untracked 
| 369|0x0000000097100000, 0x0000000097200000, 0x0000000097200000|100%| O|  |TAMS 0x0000000097200000, 0x0000000097100000| Updating 
| 370|0x0000000097200000, 0x0000000097300000, 0x0000000097300000|100%| O|  |TAMS 0x0000000097300000, 0x0000000097200000| Updating 
| 371|0x0000000097300000, 0x0000000097400000, 0x0000000097400000|100%| O|  |TAMS 0x0000000097400000, 0x0000000097300000| Updating 
| 372|0x0000000097400000, 0x0000000097400000, 0x0000000097500000|  0%| F|  |TAMS 0x0000000097400000, 0x0000000097400000| Untracked 
| 373|0x0000000097500000, 0x0000000097500000, 0x0000000097600000|  0%| F|  |TAMS 0x0000000097500000, 0x0000000097500000| Untracked 
| 374|0x0000000097600000, 0x0000000097600000, 0x0000000097700000|  0%| F|  |TAMS 0x0000000097600000, 0x0000000097600000| Untracked 
| 375|0x0000000097700000, 0x0000000097800000, 0x0000000097800000|100%| O|  |TAMS 0x0000000097800000, 0x0000000097700000| Updating 
| 376|0x0000000097800000, 0x0000000097800000, 0x0000000097900000|  0%| F|  |TAMS 0x0000000097800000, 0x0000000097800000| Untracked 
| 377|0x0000000097900000, 0x0000000097a00000, 0x0000000097a00000|100%| O|  |TAMS 0x0000000097a00000, 0x0000000097900000| Updating 
| 378|0x0000000097a00000, 0x0000000097b00000, 0x0000000097b00000|100%| O|  |TAMS 0x0000000097b00000, 0x0000000097a00000| Updating 
| 379|0x0000000097b00000, 0x0000000097b00000, 0x0000000097c00000|  0%| F|  |TAMS 0x0000000097b00000, 0x0000000097b00000| Untracked 
| 380|0x0000000097c00000, 0x0000000097d00000, 0x0000000097d00000|100%| O|  |TAMS 0x0000000097d00000, 0x0000000097c00000| Updating 
| 381|0x0000000097d00000, 0x0000000097e00000, 0x0000000097e00000|100%| O|  |TAMS 0x0000000097e00000, 0x0000000097d00000| Updating 
| 382|0x0000000097e00000, 0x0000000097f00000, 0x0000000097f00000|100%| O|  |TAMS 0x0000000097f00000, 0x0000000097e00000| Updating 
| 383|0x0000000097f00000, 0x0000000098000000, 0x0000000098000000|100%| O|  |TAMS 0x0000000098000000, 0x0000000097f00000| Updating 
| 384|0x0000000098000000, 0x0000000098100000, 0x0000000098100000|100%| O|  |TAMS 0x0000000098100000, 0x0000000098000000| Updating 
| 385|0x0000000098100000, 0x0000000098200000, 0x0000000098200000|100%| O|  |TAMS 0x0000000098200000, 0x0000000098100000| Updating 
| 386|0x0000000098200000, 0x0000000098300000, 0x0000000098300000|100%| O|  |TAMS 0x0000000098300000, 0x0000000098200000| Untracked 
| 387|0x0000000098300000, 0x0000000098400000, 0x0000000098400000|100%| O|  |TAMS 0x0000000098400000, 0x0000000098300000| Untracked 
| 388|0x0000000098400000, 0x0000000098500000, 0x0000000098500000|100%| O|  |TAMS 0x0000000098500000, 0x0000000098400000| Untracked 
| 389|0x0000000098500000, 0x0000000098600000, 0x0000000098600000|100%| O|  |TAMS 0x0000000098600000, 0x0000000098500000| Updating 
| 390|0x0000000098600000, 0x0000000098700000, 0x0000000098700000|100%| O|  |TAMS 0x0000000098700000, 0x0000000098600000| Untracked 
| 391|0x0000000098700000, 0x0000000098800000, 0x0000000098800000|100%| O|  |TAMS 0x0000000098800000, 0x0000000098700000| Untracked 
| 392|0x0000000098800000, 0x0000000098900000, 0x0000000098900000|100%| O|  |TAMS 0x0000000098900000, 0x0000000098800000| Updating 
| 393|0x0000000098900000, 0x0000000098a00000, 0x0000000098a00000|100%| O|  |TAMS 0x0000000098a00000, 0x0000000098900000| Updating 
| 394|0x0000000098a00000, 0x0000000098b00000, 0x0000000098b00000|100%| O|  |TAMS 0x0000000098b00000, 0x0000000098a00000| Updating 
| 395|0x0000000098b00000, 0x0000000098c00000, 0x0000000098c00000|100%| O|  |TAMS 0x0000000098c00000, 0x0000000098b00000| Updating 
| 396|0x0000000098c00000, 0x0000000098d00000, 0x0000000098d00000|100%| O|  |TAMS 0x0000000098d00000, 0x0000000098c00000| Updating 
| 397|0x0000000098d00000, 0x0000000098e00000, 0x0000000098e00000|100%| O|  |TAMS 0x0000000098e00000, 0x0000000098d00000| Untracked 
| 398|0x0000000098e00000, 0x0000000098f00000, 0x0000000098f00000|100%| O|  |TAMS 0x0000000098f00000, 0x0000000098e00000| Updating 
| 399|0x0000000098f00000, 0x0000000099000000, 0x0000000099000000|100%| O|  |TAMS 0x0000000099000000, 0x0000000098f00000| Untracked 
| 400|0x0000000099000000, 0x0000000099100000, 0x0000000099100000|100%| O|  |TAMS 0x0000000099100000, 0x0000000099000000| Untracked 
| 401|0x0000000099100000, 0x0000000099200000, 0x0000000099200000|100%| O|  |TAMS 0x0000000099200000, 0x0000000099100000| Untracked 
| 402|0x0000000099200000, 0x0000000099300000, 0x0000000099300000|100%| O|  |TAMS 0x0000000099300000, 0x0000000099200000| Untracked 
| 403|0x0000000099300000, 0x0000000099400000, 0x0000000099400000|100%| O|  |TAMS 0x00000000993ff200, 0x0000000099300000| Updating 
| 404|0x0000000099400000, 0x0000000099500000, 0x0000000099500000|100%| O|  |TAMS 0x0000000099400000, 0x0000000099400000| Untracked 
| 405|0x0000000099500000, 0x0000000099600000, 0x0000000099600000|100%| O|  |TAMS 0x0000000099500000, 0x0000000099500000| Untracked 
| 406|0x0000000099600000, 0x0000000099700000, 0x0000000099700000|100%| O|  |TAMS 0x0000000099600000, 0x0000000099600000| Untracked 
| 407|0x0000000099700000, 0x0000000099800000, 0x0000000099800000|100%| O|  |TAMS 0x0000000099700000, 0x0000000099700000| Untracked 
| 408|0x0000000099800000, 0x0000000099900000, 0x0000000099900000|100%| O|  |TAMS 0x0000000099800000, 0x0000000099800000| Untracked 
| 409|0x0000000099900000, 0x0000000099a00000, 0x0000000099a00000|100%| O|  |TAMS 0x0000000099900000, 0x0000000099900000| Untracked 
| 410|0x0000000099a00000, 0x0000000099b00000, 0x0000000099b00000|100%| O|  |TAMS 0x0000000099a00000, 0x0000000099a00000| Untracked 
| 411|0x0000000099b00000, 0x0000000099c00000, 0x0000000099c00000|100%| O|  |TAMS 0x0000000099b00000, 0x0000000099b00000| Untracked 
| 412|0x0000000099c00000, 0x0000000099cb9e00, 0x0000000099d00000| 72%| O|  |TAMS 0x0000000099c00000, 0x0000000099c00000| Updating 
| 413|0x0000000099d00000, 0x0000000099d00000, 0x0000000099e00000|  0%| F|  |TAMS 0x0000000099d00000, 0x0000000099d00000| Untracked 
| 414|0x0000000099e00000, 0x0000000099e00000, 0x0000000099f00000|  0%| F|  |TAMS 0x0000000099e00000, 0x0000000099e00000| Untracked 
| 415|0x0000000099f00000, 0x0000000099f00000, 0x000000009a000000|  0%| F|  |TAMS 0x0000000099f00000, 0x0000000099f00000| Untracked 
| 416|0x000000009a000000, 0x000000009a000000, 0x000000009a100000|  0%| F|  |TAMS 0x000000009a000000, 0x000000009a000000| Untracked 
| 417|0x000000009a100000, 0x000000009a100000, 0x000000009a200000|  0%| F|  |TAMS 0x000000009a100000, 0x000000009a100000| Untracked 
| 418|0x000000009a200000, 0x000000009a200000, 0x000000009a300000|  0%| F|  |TAMS 0x000000009a200000, 0x000000009a200000| Untracked 
| 419|0x000000009a300000, 0x000000009a300000, 0x000000009a400000|  0%| F|  |TAMS 0x000000009a300000, 0x000000009a300000| Untracked 
| 420|0x000000009a400000, 0x000000009a400000, 0x000000009a500000|  0%| F|  |TAMS 0x000000009a400000, 0x000000009a400000| Untracked 
| 421|0x000000009a500000, 0x000000009a500000, 0x000000009a600000|  0%| F|  |TAMS 0x000000009a500000, 0x000000009a500000| Untracked 
| 422|0x000000009a600000, 0x000000009a600000, 0x000000009a700000|  0%| F|  |TAMS 0x000000009a600000, 0x000000009a600000| Untracked 
| 423|0x000000009a700000, 0x000000009a700000, 0x000000009a800000|  0%| F|  |TAMS 0x000000009a700000, 0x000000009a700000| Untracked 
| 424|0x000000009a800000, 0x000000009a800000, 0x000000009a900000|  0%| F|  |TAMS 0x000000009a800000, 0x000000009a800000| Untracked 
| 425|0x000000009a900000, 0x000000009a900000, 0x000000009aa00000|  0%| F|  |TAMS 0x000000009a900000, 0x000000009a900000| Untracked 
| 426|0x000000009aa00000, 0x000000009aa00000, 0x000000009ab00000|  0%| F|  |TAMS 0x000000009aa00000, 0x000000009aa00000| Untracked 
| 427|0x000000009ab00000, 0x000000009ab00000, 0x000000009ac00000|  0%| F|  |TAMS 0x000000009ab00000, 0x000000009ab00000| Untracked 
| 428|0x000000009ac00000, 0x000000009ac00000, 0x000000009ad00000|  0%| F|  |TAMS 0x000000009ac00000, 0x000000009ac00000| Untracked 
| 429|0x000000009ad00000, 0x000000009ad00000, 0x000000009ae00000|  0%| F|  |TAMS 0x000000009ad00000, 0x000000009ad00000| Untracked 
| 430|0x000000009ae00000, 0x000000009ae00000, 0x000000009af00000|  0%| F|  |TAMS 0x000000009ae00000, 0x000000009ae00000| Untracked 
| 431|0x000000009af00000, 0x000000009af00000, 0x000000009b000000|  0%| F|  |TAMS 0x000000009af00000, 0x000000009af00000| Untracked 
| 432|0x000000009b000000, 0x000000009b000000, 0x000000009b100000|  0%| F|  |TAMS 0x000000009b000000, 0x000000009b000000| Untracked 
| 433|0x000000009b100000, 0x000000009b100000, 0x000000009b200000|  0%| F|  |TAMS 0x000000009b100000, 0x000000009b100000| Untracked 
| 434|0x000000009b200000, 0x000000009b200000, 0x000000009b300000|  0%| F|  |TAMS 0x000000009b200000, 0x000000009b200000| Untracked 
| 435|0x000000009b300000, 0x000000009b300000, 0x000000009b400000|  0%| F|  |TAMS 0x000000009b300000, 0x000000009b300000| Untracked 
| 436|0x000000009b400000, 0x000000009b400000, 0x000000009b500000|  0%| F|  |TAMS 0x000000009b400000, 0x000000009b400000| Untracked 
| 437|0x000000009b500000, 0x000000009b500000, 0x000000009b600000|  0%| F|  |TAMS 0x000000009b500000, 0x000000009b500000| Untracked 
| 438|0x000000009b600000, 0x000000009b600000, 0x000000009b700000|  0%| F|  |TAMS 0x000000009b600000, 0x000000009b600000| Untracked 
| 439|0x000000009b700000, 0x000000009b700000, 0x000000009b800000|  0%| F|  |TAMS 0x000000009b700000, 0x000000009b700000| Untracked 
| 440|0x000000009b800000, 0x000000009b800000, 0x000000009b900000|  0%| F|  |TAMS 0x000000009b800000, 0x000000009b800000| Untracked 
| 441|0x000000009b900000, 0x000000009b900000, 0x000000009ba00000|  0%| F|  |TAMS 0x000000009b900000, 0x000000009b900000| Untracked 
| 442|0x000000009ba00000, 0x000000009ba00000, 0x000000009bb00000|  0%| F|  |TAMS 0x000000009ba00000, 0x000000009ba00000| Untracked 
| 443|0x000000009bb00000, 0x000000009bb00000, 0x000000009bc00000|  0%| F|  |TAMS 0x000000009bb00000, 0x000000009bb00000| Untracked 
| 444|0x000000009bc00000, 0x000000009bd00000, 0x000000009bd00000|100%| S|CS|TAMS 0x000000009bc00000, 0x000000009bc00000| Complete 
| 445|0x000000009bd00000, 0x000000009be00000, 0x000000009be00000|100%| S|CS|TAMS 0x000000009bd00000, 0x000000009bd00000| Complete 
| 446|0x000000009be00000, 0x000000009be00000, 0x000000009bf00000|  0%| F|  |TAMS 0x000000009be00000, 0x000000009be00000| Untracked 
| 447|0x000000009bf00000, 0x000000009bf00000, 0x000000009c000000|  0%| F|  |TAMS 0x000000009bf00000, 0x000000009bf00000| Untracked 
| 448|0x000000009c000000, 0x000000009c100000, 0x000000009c100000|100%| S|CS|TAMS 0x000000009c000000, 0x000000009c000000| Complete 
| 449|0x000000009c100000, 0x000000009c100000, 0x000000009c200000|  0%| F|  |TAMS 0x000000009c100000, 0x000000009c100000| Untracked 
| 450|0x000000009c200000, 0x000000009c200000, 0x000000009c300000|  0%| F|  |TAMS 0x000000009c200000, 0x000000009c200000| Untracked 
| 451|0x000000009c300000, 0x000000009c300000, 0x000000009c400000|  0%| F|  |TAMS 0x000000009c300000, 0x000000009c300000| Untracked 
| 452|0x000000009c400000, 0x000000009c400000, 0x000000009c500000|  0%| F|  |TAMS 0x000000009c400000, 0x000000009c400000| Untracked 
| 453|0x000000009c500000, 0x000000009c500000, 0x000000009c600000|  0%| F|  |TAMS 0x000000009c500000, 0x000000009c500000| Untracked 
| 454|0x000000009c600000, 0x000000009c600000, 0x000000009c700000|  0%| F|  |TAMS 0x000000009c600000, 0x000000009c600000| Untracked 
| 455|0x000000009c700000, 0x000000009c700000, 0x000000009c800000|  0%| F|  |TAMS 0x000000009c700000, 0x000000009c700000| Untracked 
| 456|0x000000009c800000, 0x000000009c800000, 0x000000009c900000|  0%| F|  |TAMS 0x000000009c800000, 0x000000009c800000| Untracked 
| 457|0x000000009c900000, 0x000000009c900000, 0x000000009ca00000|  0%| F|  |TAMS 0x000000009c900000, 0x000000009c900000| Untracked 
| 458|0x000000009ca00000, 0x000000009ca00000, 0x000000009cb00000|  0%| F|  |TAMS 0x000000009ca00000, 0x000000009ca00000| Untracked 
| 459|0x000000009cb00000, 0x000000009cb00000, 0x000000009cc00000|  0%| F|  |TAMS 0x000000009cb00000, 0x000000009cb00000| Untracked 
| 460|0x000000009cc00000, 0x000000009cc00000, 0x000000009cd00000|  0%| F|  |TAMS 0x000000009cc00000, 0x000000009cc00000| Untracked 
| 461|0x000000009cd00000, 0x000000009cd00000, 0x000000009ce00000|  0%| F|  |TAMS 0x000000009cd00000, 0x000000009cd00000| Untracked 
| 462|0x000000009ce00000, 0x000000009ce00000, 0x000000009cf00000|  0%| F|  |TAMS 0x000000009ce00000, 0x000000009ce00000| Untracked 
| 463|0x000000009cf00000, 0x000000009cf00000, 0x000000009d000000|  0%| F|  |TAMS 0x000000009cf00000, 0x000000009cf00000| Untracked 
| 464|0x000000009d000000, 0x000000009d019d48, 0x000000009d100000| 10%| E|  |TAMS 0x000000009d000000, 0x000000009d000000| Complete 
| 465|0x000000009d100000, 0x000000009d200000, 0x000000009d200000|100%| E|CS|TAMS 0x000000009d100000, 0x000000009d100000| Complete 
| 466|0x000000009d200000, 0x000000009d300000, 0x000000009d300000|100%| E|CS|TAMS 0x000000009d200000, 0x000000009d200000| Complete 
| 467|0x000000009d300000, 0x000000009d400000, 0x000000009d400000|100%| E|CS|TAMS 0x000000009d300000, 0x000000009d300000| Complete 
| 468|0x000000009d400000, 0x000000009d500000, 0x000000009d500000|100%| E|CS|TAMS 0x000000009d400000, 0x000000009d400000| Complete 

Card table byte_map: [0x0000013c42010000,0x0000013c42410000] _byte_map_base: 0x0000013c41c10000

Marking Bits (Prev, Next): (CMBitMap*) 0x0000013c2b13fb20, (CMBitMap*) 0x0000013c2b13fb60
 Prev Bits: [0x0000013c42810000, 0x0000013c44810000)
 Next Bits: [0x0000013c44810000, 0x0000013c46810000)

Polling page: 0x0000013c2a8a0000

Metaspace:

Usage:
  Non-class:    103.23 MB used.
      Class:     16.23 MB used.
       Both:    119.46 MB used.

Virtual space:
  Non-class space:      128.00 MB reserved,     104.50 MB ( 82%) committed,  2 nodes.
      Class space:      416.00 MB reserved,      17.31 MB (  4%) committed,  1 nodes.
             Both:      544.00 MB reserved,     121.81 MB ( 22%) committed. 

Chunk freelists:
   Non-Class:  7.47 MB
       Class:  14.68 MB
        Both:  22.15 MB

MaxMetaspaceSize: 512.00 MB
CompressedClassSpaceSize: 416.00 MB
Initial GC threshold: 21.00 MB
Current GC threshold: 159.94 MB
CDS: on
MetaspaceReclaimPolicy: balanced
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - new_chunks_are_fully_committed: 0.
 - uncommit_free_chunks: 1.
 - use_allocation_guard: 0.
 - handle_deallocations: 1.


Internal statistics:

num_allocs_failed_limit: 6.
num_arena_births: 2782.
num_arena_deaths: 10.
num_vsnodes_births: 3.
num_vsnodes_deaths: 0.
num_space_committed: 1947.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 20.
num_chunks_taken_from_freelist: 8964.
num_chunk_merges: 3.
num_chunk_splits: 5513.
num_chunks_enlarged: 3095.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=15476Kb max_used=15476Kb free=104524Kb
 bounds [0x0000013c3a6e0000, 0x0000013c3b600000, 0x0000013c41c10000]
CodeHeap 'profiled nmethods': size=120000Kb used=34716Kb max_used=34716Kb free=85283Kb
 bounds [0x0000013c32c10000, 0x0000013c34e00000, 0x0000013c3a140000]
CodeHeap 'non-nmethods': size=5760Kb used=2425Kb max_used=2525Kb free=3334Kb
 bounds [0x0000013c3a140000, 0x0000013c3a3f0000, 0x0000013c3a6e0000]
 total_blobs=18900 nmethods=17934 adapters=876
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 459.480 Thread 0x0000013c48cd2f00 nmethod 21645 0x0000013c34df4190 code [0x0000013c34df43a0, 0x0000013c34df4a78]
Event: 459.514 Thread 0x0000013c48cd2380 nmethod 21632 0x0000013c3b5f9b90 code [0x0000013c3b5f9e40, 0x0000013c3b5fae60]
Event: 459.515 Thread 0x0000013c48cd2380 21628       4       org.gradle.api.plugins.BasePlugin$$Lambda$845/0x0000013c4a726b38::execute (16 bytes)
Event: 459.521 Thread 0x0000013c48cd2380 nmethod 21628 0x0000013c3b5fbe90 code [0x0000013c3b5fc040, 0x0000013c3b5fc248]
Event: 459.523 Thread 0x0000013c48cd2380 21646       4       org.gradle.api.internal.project.DefaultProject_Decorated::getPluginManager (32 bytes)
Event: 459.527 Thread 0x0000013c48cd2380 nmethod 21646 0x0000013c3b5fc510 code [0x0000013c3b5fc6a0, 0x0000013c3b5fc8a8]
Event: 459.538 Thread 0x0000013c48cd2f00 21647       3       org.gradle.api.internal.provider.sources.SystemPropertyValueSource$Inject::getParameters (31 bytes)
Event: 459.539 Thread 0x0000013c48cd2f00 nmethod 21647 0x0000013c34df4d10 code [0x0000013c34df4f00, 0x0000013c34df5558]
Event: 459.546 Thread 0x0000013c48cd2f00 21648       3       org.gradle.internal.extensibility.DefaultConvention$ExtensionsDynamicObject::<init> (6 bytes)
Event: 459.546 Thread 0x0000013c48cd2f00 nmethod 21648 0x0000013c34df5710 code [0x0000013c34df58c0, 0x0000013c34df5b38]
Event: 459.546 Thread 0x0000013c48cd2f00 21649       3       org.gradle.internal.extensibility.DefaultConvention$ExtensionsDynamicObject::<init> (10 bytes)
Event: 459.547 Thread 0x0000013c48cd2f00 nmethod 21649 0x0000013c34df5c90 code [0x0000013c34df5e40, 0x0000013c34df6058]
Event: 459.563 Thread 0x0000013c48cd2f00 21650       3       org.gradle.api.internal.DefaultNamedDomainObjectSet_Decorated::$gradleInit (15 bytes)
Event: 459.563 Thread 0x0000013c48cd2f00 nmethod 21650 0x0000013c34df6190 code [0x0000013c34df6380, 0x0000013c34df69d8]
Event: 459.569 Thread 0x0000013c48cd2f00 21651       1       org.gradle.internal.resource.UriTextResource::access$200 (5 bytes)
Event: 459.569 Thread 0x0000013c48cd2f00 nmethod 21651 0x0000013c3b5fca10 code [0x0000013c3b5fcba0, 0x0000013c3b5fcc58]
Event: 459.569 Thread 0x0000013c48cd2f00 21652       3       java.math.BigInteger::<init> (94 bytes)
Event: 459.570 Thread 0x0000013c48cd2f00 nmethod 21652 0x0000013c34df6b90 code [0x0000013c34df6de0, 0x0000013c34df7768]
Event: 459.590 Thread 0x0000013c48cd2380 21653       4       com.google.common.base.CharMatcher$InRange::matches (22 bytes)
Event: 459.591 Thread 0x0000013c48cd2380 nmethod 21653 0x0000013c3b5fcd10 code [0x0000013c3b5fce80, 0x0000013c3b5fcf38]

GC Heap History (20 events):
Event: 299.927 GC heap before
{Heap before GC invocations=89 (full 0):
 garbage-first heap   total 480256K, used 425186K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 32 young (32768K), 2 survivors (2048K)
 Metaspace       used 121547K, committed 123712K, reserved 557056K
  class space    used 16537K, committed 17536K, reserved 425984K
}
Event: 299.930 GC heap after
{Heap after GC invocations=90 (full 0):
 garbage-first heap   total 480256K, used 394446K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 2 young (2048K), 2 survivors (2048K)
 Metaspace       used 121547K, committed 123712K, reserved 557056K
  class space    used 16537K, committed 17536K, reserved 425984K
}
Event: 383.944 GC heap before
{Heap before GC invocations=90 (full 0):
 garbage-first heap   total 480256K, used 426190K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 33 young (33792K), 2 survivors (2048K)
 Metaspace       used 121663K, committed 123840K, reserved 557056K
  class space    used 16549K, committed 17536K, reserved 425984K
}
Event: 383.952 GC heap after
{Heap after GC invocations=91 (full 0):
 garbage-first heap   total 480256K, used 397100K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 5 young (5120K), 5 survivors (5120K)
 Metaspace       used 121663K, committed 123840K, reserved 557056K
  class space    used 16549K, committed 17536K, reserved 425984K
}
Event: 385.259 GC heap before
{Heap before GC invocations=91 (full 0):
 garbage-first heap   total 480256K, used 423724K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 32 young (32768K), 5 survivors (5120K)
 Metaspace       used 121816K, committed 124032K, reserved 557056K
  class space    used 16565K, committed 17600K, reserved 425984K
}
Event: 385.271 GC heap after
{Heap after GC invocations=92 (full 0):
 garbage-first heap   total 480256K, used 400266K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 4 young (4096K), 4 survivors (4096K)
 Metaspace       used 121816K, committed 124032K, reserved 557056K
  class space    used 16565K, committed 17600K, reserved 425984K
}
Event: 386.673 GC heap before
{Heap before GC invocations=92 (full 0):
 garbage-first heap   total 480256K, used 424842K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 28 young (28672K), 4 survivors (4096K)
 Metaspace       used 121951K, committed 124160K, reserved 557056K
  class space    used 16577K, committed 17600K, reserved 425984K
}
Event: 386.684 GC heap after
{Heap after GC invocations=93 (full 0):
 garbage-first heap   total 480256K, used 403453K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 4 young (4096K), 4 survivors (4096K)
 Metaspace       used 121951K, committed 124160K, reserved 557056K
  class space    used 16577K, committed 17600K, reserved 425984K
}
Event: 388.719 GC heap before
{Heap before GC invocations=93 (full 0):
 garbage-first heap   total 480256K, used 422909K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 24 young (24576K), 4 survivors (4096K)
 Metaspace       used 122054K, committed 124288K, reserved 557056K
  class space    used 16583K, committed 17600K, reserved 425984K
}
Event: 388.732 GC heap after
{Heap after GC invocations=94 (full 0):
 garbage-first heap   total 480256K, used 406214K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 3 young (3072K), 3 survivors (3072K)
 Metaspace       used 122054K, committed 124288K, reserved 557056K
  class space    used 16583K, committed 17600K, reserved 425984K
}
Event: 456.080 GC heap before
{Heap before GC invocations=94 (full 0):
 garbage-first heap   total 480256K, used 426694K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 23 young (23552K), 3 survivors (3072K)
 Metaspace       used 122231K, committed 124672K, reserved 557056K
  class space    used 16622K, committed 17728K, reserved 425984K
}
Event: 456.090 GC heap after
{Heap after GC invocations=95 (full 0):
 garbage-first heap   total 480256K, used 408266K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 2 young (2048K), 2 survivors (2048K)
 Metaspace       used 122231K, committed 124672K, reserved 557056K
  class space    used 16622K, committed 17728K, reserved 425984K
}
Event: 457.543 GC heap before
{Heap before GC invocations=95 (full 0):
 garbage-first heap   total 480256K, used 429770K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 23 young (23552K), 2 survivors (2048K)
 Metaspace       used 122255K, committed 124672K, reserved 557056K
  class space    used 16622K, committed 17728K, reserved 425984K
}
Event: 457.553 GC heap after
{Heap after GC invocations=96 (full 0):
 garbage-first heap   total 480256K, used 411460K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 3 young (3072K), 3 survivors (3072K)
 Metaspace       used 122255K, committed 124672K, reserved 557056K
  class space    used 16622K, committed 17728K, reserved 425984K
}
Event: 457.973 GC heap before
{Heap before GC invocations=96 (full 0):
 garbage-first heap   total 480256K, used 431940K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 23 young (23552K), 3 survivors (3072K)
 Metaspace       used 122291K, committed 124672K, reserved 557056K
  class space    used 16622K, committed 17728K, reserved 425984K
}
Event: 457.991 GC heap after
{Heap after GC invocations=97 (full 0):
 garbage-first heap   total 480256K, used 416764K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 3 young (3072K), 3 survivors (3072K)
 Metaspace       used 122291K, committed 124672K, reserved 557056K
  class space    used 16622K, committed 17728K, reserved 425984K
}
Event: 458.836 GC heap before
{Heap before GC invocations=97 (full 0):
 garbage-first heap   total 480256K, used 437244K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 23 young (23552K), 3 survivors (3072K)
 Metaspace       used 122306K, committed 124736K, reserved 557056K
  class space    used 16622K, committed 17728K, reserved 425984K
}
Event: 458.850 GC heap after
{Heap after GC invocations=98 (full 0):
 garbage-first heap   total 480256K, used 420722K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 3 young (3072K), 3 survivors (3072K)
 Metaspace       used 122306K, committed 124736K, reserved 557056K
  class space    used 16622K, committed 17728K, reserved 425984K
}
Event: 459.491 GC heap before
{Heap before GC invocations=98 (full 0):
 garbage-first heap   total 480256K, used 441202K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 23 young (23552K), 3 survivors (3072K)
 Metaspace       used 122326K, committed 124736K, reserved 557056K
  class space    used 16622K, committed 17728K, reserved 425984K
}
Event: 459.508 GC heap after
{Heap after GC invocations=99 (full 0):
 garbage-first heap   total 480256K, used 425703K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 3 young (3072K), 3 survivors (3072K)
 Metaspace       used 122326K, committed 124736K, reserved 557056K
  class space    used 16622K, committed 17728K, reserved 425984K
}

Dll operation events (13 events):
Event: 0.084 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\java.dll
Event: 0.320 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\jsvml.dll
Event: 0.493 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\net.dll
Event: 0.523 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\nio.dll
Event: 0.539 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\zip.dll
Event: 1.299 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\jimage.dll
Event: 1.529 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\verify.dll
Event: 2.266 Loaded shared library C:\Users\<USER>\.gradle\native\68d5fa5c4cc2d200863cafc0d521ce42e7d3e7ee720ec0a83991735586a16f82\windows-amd64\native-platform.dll
Event: 2.397 Loaded shared library C:\Users\<USER>\.gradle\native\e376f236ea51e6404a007f0833ffe2c6e607c4080706a723a18a27aeea778392\windows-amd64\native-platform-file-events.dll
Event: 6.179 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\management.dll
Event: 6.190 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\management_ext.dll
Event: 7.153 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\extnet.dll
Event: 9.429 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\sunmscapi.dll

Deoptimization events (20 events):
Event: 457.660 Thread 0x0000013c69862cb0 Uncommon trap: trap_request=0xffffffc6 fr.pc=0x0000013c3b4c9280 relative=0x0000000000001080
Event: 457.660 Thread 0x0000013c69862cb0 Uncommon trap: reason=bimorphic_or_optimized_type_check action=maybe_recompile pc=0x0000013c3b4c9280 method=org.gradle.internal.event.BroadcastDispatch.add(Ljava/lang/String;Lorg/gradle/api/Action;)Lorg/gradle/internal/event/BroadcastDispatch; @ 22 c2
Event: 457.660 Thread 0x0000013c69862cb0 DEOPT PACKING pc=0x0000013c3b4c9280 sp=0x00000017f0ef7130
Event: 457.660 Thread 0x0000013c69862cb0 DEOPT UNPACKING pc=0x0000013c3a1969a3 sp=0x00000017f0ef7108 mode 2
Event: 457.955 Thread 0x0000013c69862cb0 Uncommon trap: trap_request=0xffffffc6 fr.pc=0x0000013c3b4c9280 relative=0x0000000000001080
Event: 457.955 Thread 0x0000013c69862cb0 Uncommon trap: reason=bimorphic_or_optimized_type_check action=maybe_recompile pc=0x0000013c3b4c9280 method=org.gradle.internal.event.BroadcastDispatch.add(Ljava/lang/String;Lorg/gradle/api/Action;)Lorg/gradle/internal/event/BroadcastDispatch; @ 22 c2
Event: 457.955 Thread 0x0000013c69862cb0 DEOPT PACKING pc=0x0000013c3b4c9280 sp=0x00000017f0ef7130
Event: 457.955 Thread 0x0000013c69862cb0 DEOPT UNPACKING pc=0x0000013c3a1969a3 sp=0x00000017f0ef7108 mode 2
Event: 458.787 Thread 0x0000013c69862cb0 Uncommon trap: trap_request=0xffffffc6 fr.pc=0x0000013c3b4c9280 relative=0x0000000000001080
Event: 458.787 Thread 0x0000013c69862cb0 Uncommon trap: reason=bimorphic_or_optimized_type_check action=maybe_recompile pc=0x0000013c3b4c9280 method=org.gradle.internal.event.BroadcastDispatch.add(Ljava/lang/String;Lorg/gradle/api/Action;)Lorg/gradle/internal/event/BroadcastDispatch; @ 22 c2
Event: 458.787 Thread 0x0000013c69862cb0 DEOPT PACKING pc=0x0000013c3b4c9280 sp=0x00000017f0ef7130
Event: 458.787 Thread 0x0000013c69862cb0 DEOPT UNPACKING pc=0x0000013c3a1969a3 sp=0x00000017f0ef7108 mode 2
Event: 458.858 Thread 0x0000013c69862cb0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000013c3b404648 relative=0x0000000000003f08
Event: 458.858 Thread 0x0000013c69862cb0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000013c3b404648 method=org.gradle.api.internal.project.DefaultProject.getTaskDependencyFactory()Lorg/gradle/api/internal/tasks/TaskDependencyFactory; @ 4 c2
Event: 458.858 Thread 0x0000013c69862cb0 DEOPT PACKING pc=0x0000013c3b404648 sp=0x00000017f0ef6590
Event: 458.858 Thread 0x0000013c69862cb0 DEOPT UNPACKING pc=0x0000013c3a1969a3 sp=0x00000017f0ef6548 mode 2
Event: 459.221 Thread 0x0000013c69862cb0 Uncommon trap: trap_request=0xffffffc6 fr.pc=0x0000013c3b4c9280 relative=0x0000000000001080
Event: 459.221 Thread 0x0000013c69862cb0 Uncommon trap: reason=bimorphic_or_optimized_type_check action=maybe_recompile pc=0x0000013c3b4c9280 method=org.gradle.internal.event.BroadcastDispatch.add(Ljava/lang/String;Lorg/gradle/api/Action;)Lorg/gradle/internal/event/BroadcastDispatch; @ 22 c2
Event: 459.221 Thread 0x0000013c69862cb0 DEOPT PACKING pc=0x0000013c3b4c9280 sp=0x00000017f0ef7130
Event: 459.221 Thread 0x0000013c69862cb0 DEOPT UNPACKING pc=0x0000013c3a1969a3 sp=0x00000017f0ef7108 mode 2

Classes loaded (20 events):
Event: 264.200 Loading class javax/crypto/JceSecurity$2
Event: 264.200 Loading class javax/crypto/JceSecurity$2 done
Event: 264.200 Loading class javax/crypto/JceSecurity$3
Event: 264.200 Loading class javax/crypto/JceSecurity$3 done
Event: 264.200 Loading class javax/crypto/ProviderVerifier
Event: 264.200 Loading class javax/crypto/ProviderVerifier done
Event: 275.144 Loading class java/text/MessageFormat
Event: 275.145 Loading class java/text/MessageFormat done
Event: 275.145 Loading class java/text/MessageFormat$Field
Event: 275.145 Loading class java/text/MessageFormat$Field done
Event: 300.887 Loading class java/util/Locale$1
Event: 300.887 Loading class java/util/Locale$1 done
Event: 342.199 Loading class jdk/internal/math/FormattedFloatingDecimal$Form
Event: 342.199 Loading class jdk/internal/math/FormattedFloatingDecimal$Form done
Event: 342.199 Loading class jdk/internal/math/FormattedFloatingDecimal
Event: 342.200 Loading class jdk/internal/math/FormattedFloatingDecimal done
Event: 342.200 Loading class jdk/internal/math/FormattedFloatingDecimal$1
Event: 342.200 Loading class jdk/internal/math/FormattedFloatingDecimal$1 done
Event: 342.200 Loading class jdk/internal/math/FormattedFloatingDecimal$2
Event: 342.200 Loading class jdk/internal/math/FormattedFloatingDecimal$2 done

Classes unloaded (5 events):
Event: 157.325 Thread 0x0000013c48cad6b0 Unloading class 0x0000013c4aa11c00 'java/lang/invoke/LambdaForm$DMH+0x0000013c4aa11c00'
Event: 157.325 Thread 0x0000013c48cad6b0 Unloading class 0x0000013c4a9f9400 'java/lang/invoke/LambdaForm$DMH+0x0000013c4a9f9400'
Event: 157.325 Thread 0x0000013c48cad6b0 Unloading class 0x0000013c4a9ed400 'java/lang/invoke/LambdaForm$MH+0x0000013c4a9ed400'
Event: 157.325 Thread 0x0000013c48cad6b0 Unloading class 0x0000013c4a9ecc00 'java/lang/invoke/LambdaForm$DMH+0x0000013c4a9ecc00'
Event: 157.325 Thread 0x0000013c48cad6b0 Unloading class 0x0000013c4a9ec400 'java/lang/invoke/LambdaForm$DMH+0x0000013c4a9ec400'

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 457.519 Thread 0x0000013c69862cb0 Exception <a 'java/lang/NullPointerException'{0x000000009c1771d0}> (0x000000009c1771d0) 
thrown [s\src\hotspot\share\runtime\sharedRuntime.cpp, line 631]
Event: 457.519 Thread 0x0000013c69862cb0 Implicit null exception at 0x0000013c341f5249 to 0x0000013c341f58d9
Event: 457.519 Thread 0x0000013c69862cb0 Exception <a 'java/lang/NullPointerException'{0x000000009c1787a8}> (0x000000009c1787a8) 
thrown [s\src\hotspot\share\runtime\sharedRuntime.cpp, line 631]
Event: 457.519 Thread 0x0000013c69862cb0 Implicit null exception at 0x0000013c341f5249 to 0x0000013c341f58d9
Event: 457.519 Thread 0x0000013c69862cb0 Exception <a 'java/lang/NullPointerException'{0x000000009c179d18}> (0x000000009c179d18) 
thrown [s\src\hotspot\share\runtime\sharedRuntime.cpp, line 631]
Event: 457.520 Thread 0x0000013c69862cb0 Implicit null exception at 0x0000013c341f5249 to 0x0000013c341f58d9
Event: 457.520 Thread 0x0000013c69862cb0 Exception <a 'java/lang/NullPointerException'{0x000000009c17b288}> (0x000000009c17b288) 
thrown [s\src\hotspot\share\runtime\sharedRuntime.cpp, line 631]
Event: 457.520 Thread 0x0000013c69862cb0 Implicit null exception at 0x0000013c341f5249 to 0x0000013c341f58d9
Event: 457.520 Thread 0x0000013c69862cb0 Exception <a 'java/lang/NullPointerException'{0x000000009c17c7f8}> (0x000000009c17c7f8) 
thrown [s\src\hotspot\share\runtime\sharedRuntime.cpp, line 631]
Event: 457.520 Thread 0x0000013c69862cb0 Implicit null exception at 0x0000013c341f5249 to 0x0000013c341f58d9
Event: 457.521 Thread 0x0000013c69862cb0 Exception <a 'java/lang/NullPointerException'{0x000000009c17de30}> (0x000000009c17de30) 
thrown [s\src\hotspot\share\runtime\sharedRuntime.cpp, line 631]
Event: 457.521 Thread 0x0000013c69862cb0 Implicit null exception at 0x0000013c341f5249 to 0x0000013c341f58d9
Event: 457.521 Thread 0x0000013c69862cb0 Exception <a 'java/lang/NullPointerException'{0x000000009c17f3a0}> (0x000000009c17f3a0) 
thrown [s\src\hotspot\share\runtime\sharedRuntime.cpp, line 631]
Event: 457.521 Thread 0x0000013c69862cb0 Implicit null exception at 0x0000013c341f5249 to 0x0000013c341f58d9
Event: 457.521 Thread 0x0000013c69862cb0 Exception <a 'java/lang/NullPointerException'{0x000000009c180910}> (0x000000009c180910) 
thrown [s\src\hotspot\share\runtime\sharedRuntime.cpp, line 631]
Event: 457.521 Thread 0x0000013c69862cb0 Implicit null exception at 0x0000013c341f5249 to 0x0000013c341f58d9
Event: 457.522 Thread 0x0000013c69862cb0 Exception <a 'java/lang/NullPointerException'{0x000000009c181f18}> (0x000000009c181f18) 
thrown [s\src\hotspot\share\runtime\sharedRuntime.cpp, line 631]
Event: 457.522 Thread 0x0000013c69862cb0 Implicit null exception at 0x0000013c341f5249 to 0x0000013c341f58d9
Event: 457.522 Thread 0x0000013c69862cb0 Exception <a 'java/lang/NullPointerException'{0x000000009c183488}> (0x000000009c183488) 
thrown [s\src\hotspot\share\runtime\sharedRuntime.cpp, line 631]
Event: 458.858 Thread 0x0000013c69862cb0 Implicit null exception at 0x0000013c3b4009b4 to 0x0000013c3b404638

VM Operations (20 events):
Event: 427.991 Executing VM operation: Cleanup done
Event: 434.038 Executing VM operation: Cleanup
Event: 434.039 Executing VM operation: Cleanup done
Event: 435.053 Executing VM operation: Cleanup
Event: 435.053 Executing VM operation: Cleanup done
Event: 437.069 Executing VM operation: Cleanup
Event: 437.069 Executing VM operation: Cleanup done
Event: 456.080 Executing VM operation: G1CollectForAllocation
Event: 456.090 Executing VM operation: G1CollectForAllocation done
Event: 457.092 Executing VM operation: Cleanup
Event: 457.092 Executing VM operation: Cleanup done
Event: 457.543 Executing VM operation: G1CollectForAllocation
Event: 457.554 Executing VM operation: G1CollectForAllocation done
Event: 457.973 Executing VM operation: G1CollectForAllocation
Event: 457.994 Executing VM operation: G1CollectForAllocation done
Event: 458.820 Executing VM operation: G1CollectForAllocation
Event: 458.853 Executing VM operation: G1CollectForAllocation done
Event: 459.485 Executing VM operation: G1CollectForAllocation
Event: 459.513 Executing VM operation: G1CollectForAllocation done
Event: 459.643 Executing VM operation: G1PauseRemark

Events (20 events):
Event: 387.739 Thread 0x0000013c6a98fec0 Thread added: 0x0000013c6a98fec0
Event: 387.739 Thread 0x0000013c6a991810 Thread added: 0x0000013c6a991810
Event: 388.051 Thread 0x0000013c6a9908e0 Thread added: 0x0000013c6a9908e0
Event: 388.051 Thread 0x0000013c6a990df0 Thread added: 0x0000013c6a990df0
Event: 388.121 Thread 0x0000013c6a991300 Thread added: 0x0000013c6a991300
Event: 388.121 Thread 0x0000013c6a98f4a0 Thread added: 0x0000013c6a98f4a0
Event: 388.121 Thread 0x0000013c6a98e060 Thread added: 0x0000013c6a98e060
Event: 388.126 Thread 0x0000013c6a98ef90 Thread added: 0x0000013c6a98ef90
Event: 388.143 Thread 0x0000013c6a98e570 Thread added: 0x0000013c6a98e570
Event: 389.414 Thread 0x0000013c6cad1d50 Thread exited: 0x0000013c6cad1d50
Event: 402.618 Thread 0x0000013c6e5e6f30 Thread exited: 0x0000013c6e5e6f30
Event: 434.653 Thread 0x0000013c6cad5d10 Thread added: 0x0000013c6cad5d10
Event: 435.632 Thread 0x0000013c6cad5d10 Thread exited: 0x0000013c6cad5d10
Event: 447.855 Thread 0x0000013c6a991810 Thread exited: 0x0000013c6a991810
Event: 448.646 Thread 0x0000013c6a990df0 Thread exited: 0x0000013c6a990df0
Event: 448.656 Thread 0x0000013c6a9908e0 Thread exited: 0x0000013c6a9908e0
Event: 456.169 Thread 0x0000013c6cad27f0 Thread added: 0x0000013c6cad27f0
Event: 457.508 Thread 0x0000013c6cad27f0 Thread exited: 0x0000013c6cad27f0
Event: 457.628 Thread 0x0000013c6cad8240 Thread added: 0x0000013c6cad8240
Event: 458.744 Thread 0x0000013c6cad8240 Thread exited: 0x0000013c6cad8240


Dynamic libraries:
0x00007ff75eed0000 - 0x00007ff75eede000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\java.exe
0x00007ff87c1f0000 - 0x00007ff87c3e8000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ff84d8a0000 - 0x00007ff84d8bb000 	C:\Program Files\Avast Software\Avast\aswhook.dll
0x00007ff87bfc0000 - 0x00007ff87c082000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ff8799a0000 - 0x00007ff879c96000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ff879ee0000 - 0x00007ff879fe0000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ff86ec90000 - 0x00007ff86eca7000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\jli.dll
0x00007ff87aaa0000 - 0x00007ff87ac3d000 	C:\WINDOWS\System32\USER32.dll
0x00007ff879970000 - 0x00007ff879992000 	C:\WINDOWS\System32\win32u.dll
0x00007ff87afc0000 - 0x00007ff87afeb000 	C:\WINDOWS\System32\GDI32.dll
0x00007ff87a060000 - 0x00007ff87a17a000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ff87a180000 - 0x00007ff87a21d000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ff86e730000 - 0x00007ff86e74d000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\VCRUNTIME140.dll
0x00007ff8631d0000 - 0x00007ff86346a000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5794_none_60bcd33171f2783c\COMCTL32.dll
0x00007ff87b790000 - 0x00007ff87b82e000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ff87bf70000 - 0x00007ff87bf9f000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ff86f650000 - 0x00007ff86f65c000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\vcruntime140_1.dll
0x00007ff82a470000 - 0x00007ff82a4fd000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\msvcp140.dll
0x00007ff801f10000 - 0x00007ff802b80000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\server\jvm.dll
0x00007ff87b8a0000 - 0x00007ff87b94f000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ff87b6d0000 - 0x00007ff87b76f000 	C:\WINDOWS\System32\sechost.dll
0x00007ff87b050000 - 0x00007ff87b173000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ff879d50000 - 0x00007ff879d77000 	C:\WINDOWS\System32\bcrypt.dll
0x00007ff87b830000 - 0x00007ff87b89b000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ff8796f0000 - 0x00007ff87973b000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ff874230000 - 0x00007ff87423a000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ff849f10000 - 0x00007ff849f37000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ff8796d0000 - 0x00007ff8796e2000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ff878050000 - 0x00007ff878062000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ff86f1c0000 - 0x00007ff86f1ca000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\jimage.dll
0x00007ff877e40000 - 0x00007ff878024000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ff86aa30000 - 0x00007ff86aa64000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ff8798e0000 - 0x00007ff879962000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ff861510000 - 0x00007ff861535000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\java.dll
0x00007ff821770000 - 0x00007ff821847000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\jsvml.dll
0x00007ff87a330000 - 0x00007ff87aa9e000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ff874480000 - 0x00007ff874c23000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ff87bc10000 - 0x00007ff87bf63000 	C:\WINDOWS\System32\combase.dll
0x00007ff879170000 - 0x00007ff87919b000 	C:\WINDOWS\SYSTEM32\Wldp.dll
0x00007ff87ac40000 - 0x00007ff87ad0d000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ff87ba30000 - 0x00007ff87badd000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ff87aff0000 - 0x00007ff87b045000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ff8797c0000 - 0x00007ff8797e5000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ff86d080000 - 0x00007ff86d09a000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\net.dll
0x00007ff86bec0000 - 0x00007ff86bfca000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ff878f50000 - 0x00007ff878fbc000 	C:\WINDOWS\system32\mswsock.dll
0x00007ff863150000 - 0x00007ff863166000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\nio.dll
0x00007ff863010000 - 0x00007ff863028000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\zip.dll
0x00007ff86f070000 - 0x00007ff86f080000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\verify.dll
0x00007ff86f1d0000 - 0x00007ff86f1f7000 	C:\Users\<USER>\.gradle\native\68d5fa5c4cc2d200863cafc0d521ce42e7d3e7ee720ec0a83991735586a16f82\windows-amd64\native-platform.dll
0x00007ff8207a0000 - 0x00007ff8208e4000 	C:\Users\<USER>\.gradle\native\e376f236ea51e6404a007f0833ffe2c6e607c4080706a723a18a27aeea778392\windows-amd64\native-platform-file-events.dll
0x00007ff86f020000 - 0x00007ff86f02a000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\management.dll
0x00007ff86e980000 - 0x00007ff86e98b000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\management_ext.dll
0x00007ff87b770000 - 0x00007ff87b778000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ff8791b0000 - 0x00007ff8791c8000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ff878830000 - 0x00007ff878868000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ff879780000 - 0x00007ff8797ae000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ff8790e0000 - 0x00007ff8790ec000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ff878c50000 - 0x00007ff878c8b000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ff87ad70000 - 0x00007ff87ad78000 	C:\WINDOWS\System32\NSI.dll
0x00007ff86d900000 - 0x00007ff86d917000 	C:\WINDOWS\SYSTEM32\dhcpcsvc6.DLL
0x00007ff86d8c0000 - 0x00007ff86d8dd000 	C:\WINDOWS\SYSTEM32\dhcpcsvc.DLL
0x00007ff878c90000 - 0x00007ff878d5a000 	C:\WINDOWS\SYSTEM32\DNSAPI.dll
0x00007ff86e6e0000 - 0x00007ff86e6e9000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\extnet.dll
0x00007ff86e630000 - 0x00007ff86e63e000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\sunmscapi.dll
0x00007ff879d80000 - 0x00007ff879edd000 	C:\WINDOWS\System32\CRYPT32.dll
0x00007ff8792c0000 - 0x00007ff8792e7000 	C:\WINDOWS\SYSTEM32\ncrypt.dll
0x00007ff879280000 - 0x00007ff8792bb000 	C:\WINDOWS\SYSTEM32\NTASN1.dll
0x00007ff86b340000 - 0x00007ff86b347000 	C:\WINDOWS\system32\wshunix.dll
0x00007ff857e60000 - 0x00007ff857e77000 	C:\WINDOWS\system32\napinsp.dll
0x00007ff857e40000 - 0x00007ff857e5b000 	C:\WINDOWS\system32\pnrpnsp.dll
0x00007ff857e20000 - 0x00007ff857e35000 	C:\WINDOWS\system32\wshbth.dll
0x00007ff871590000 - 0x00007ff8715ad000 	C:\WINDOWS\system32\NLAapi.dll
0x00007ff857e00000 - 0x00007ff857e12000 	C:\WINDOWS\System32\winrnr.dll
0x00007ff862360000 - 0x00007ff86236a000 	C:\Windows\System32\rasadhlp.dll
0x00007ff86bbf0000 - 0x00007ff86bc70000 	C:\WINDOWS\System32\fwpuclnt.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Microsoft\jdk-*********-hotspot\bin;C:\WINDOWS\SYSTEM32;C:\Program Files\Avast Software\Avast;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5794_none_60bcd33171f2783c;C:\Program Files\Microsoft\jdk-*********-hotspot\bin\server;C:\Users\<USER>\.gradle\native\68d5fa5c4cc2d200863cafc0d521ce42e7d3e7ee720ec0a83991735586a16f82\windows-amd64;C:\Users\<USER>\.gradle\native\e376f236ea51e6404a007f0833ffe2c6e607c4080706a723a18a27aeea778392\windows-amd64

VM Arguments:
jvm_args: -XX:MaxMetaspaceSize=512m --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED -Xmx2048m -Dfile.encoding=utf8 -Duser.country=US -Duser.language=en -Duser.variant 
java_command: org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.0.1
java_class_path (initial): C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.0.1-all\aro4hu1c3oeioove7l0i4i14o\gradle-8.0.1\lib\gradle-launcher-8.0.1.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 3                                         {product} {ergonomic}
   size_t CompressedClassSpaceSize                 = 436207616                                 {product} {ergonomic}
     uint ConcGCThreads                            = 1                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 4                                         {product} {ergonomic}
   size_t G1HeapRegionSize                         = 1048576                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 134217728                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 2147483648                                {product} {command line}
   size_t MaxMetaspaceSize                         = 536870912                                 {product} {command line}
   size_t MaxNewSize                               = 1287651328                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 1048576                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5832780                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122912730                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122912730                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 2147483648                             {manageable} {ergonomic}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=C:\Program Files\Microsoft\jdk-*********-hotspot\
PATH=C:\Program Files\Microsoft\jdk-*********-hotspot\bin;C:\Python312\Scripts\;C:\Python312\;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Program Files\OpenLogic\jre-********-hotspot\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\ProgramData\chocolatey\bin;C:\Program Files\dotnet\;C:\Program Files\Graphviz\bin;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;D:\Android\Sdk\platform-tools;D:\Android\Sdk\tools;D:\Android\Sdk\tools\bin;D:\Android\Sdk\emulator;D:\Android\Sdk\platform-tools;D:\Android\Sdk\tools;D:\Android\Sdk\tools\bin;D:\Android\Sdk\cmdline-tools\latest\bin;D:\apktool;C:\Program Files\Pandoc\;C:\Program Files\OpenSSL-Win64\bin;C:\HaxeToolkit\haxe;C:\HaxeToolkit\neko;C:\Program Files\nodejs\;C:\Program Files\Git\cmd;C:\Program Files\Microsoft\jdk-*********-hotspot\bin;C:\Python312\Scripts\;C:\Python312\;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Program Files\OpenLogic\jre-********-hotspot\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\ProgramData\chocolatey\bin;C:\Program Files\dotnet\;C:\Program Files\Graphviz\bin;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;D:\Android\Sdk\platform-tools;D:\Android\Sdk\tools;D:\Android\Sdk\tools\bin;D:\Android\Sdk\emulator;D:\Android\Sdk\platform-tools;D:\Android\Sdk\tools;D:\Android\Sdk\tools\bin;D:\Android\Sdk\cmdline-tools\latest\bin;D:\apktool;C:\Program Files\Pandoc\;C:\Program Files\OpenSSL-Win64\bin;C:\HaxeToolkit\haxe;C:\HaxeToolkit\neko;C:\Program Files\nodejs\;C:\Program Files\Git\cmd;C:\HaxeToolkit\haxe;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\ProgramData\PC\GitHubDesktop\bin;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin
USERNAME=PC
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 78 Stepping 3, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp



Periodic native trim disabled


---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.5794)
OS uptime: 0 days 11:49 hours
Hyper-V role detected

CPU: total 4 (initial active 4) (2 cores per cpu, 2 threads per core) family 6 model 78 stepping 3 microcode 0xf0, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt, hv
Processor Information for the first 4 processors :
  Max Mhz: 2400, Current Mhz: 2300, Mhz Limit: 2280

Memory: 4k page, system-wide physical 8071M (427M free)
TotalPageFile size 20359M (AvailPageFile size 117M)
current process WorkingSet (physical memory assigned to process): 736M, peak: 736M
current process commit charge ("private bytes"): 860M, peak: 1003M

vm_info: OpenJDK 64-Bit Server VM (17.0.12+7-LTS) for windows-amd64 JRE (17.0.12+7-LTS), built on Jul 16 2024 18:11:44 by "MicrosoftCorporation" with unknown MS VC++:1939

END.
