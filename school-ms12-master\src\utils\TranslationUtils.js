import { useLanguage } from '../context/LanguageContext';

/**
 * Utility function to translate text with fallback
 * @param {string} key - Translation key
 * @param {string} fallback - Fallback text if translation is not found
 * @returns {string} Translated text or fallback
 */
export const translate = (key, fallback = '') => {
  try {
    // Try to get the current language context
    const { translate } = useLanguage();
    if (typeof translate === 'function') {
      return translate(key) || fallback;
    }
    return fallback;
  } catch (error) {
    // If outside of context (e.g., in a utility function), return fallback
    console.warn('Error accessing language context for key: ' + key, error);
    return fallback;
  }
};

/**
 * Translate activity details using templates from translations
 * @param {object} activity - Activity object to translate
 * @param {function} t - Translation function
 * @returns {object} Translated activity
 */
export const translateActivity = (activity, t) => {
  if (!activity) return null;
  if (!t || typeof t !== 'function') {
    console.warn('Translation function not provided for activity translation');
    return activity;
  }

  // Clone the activity to avoid mutating the original
  const translatedActivity = { ...activity };
  
  try {
    // Get the type and action from metadata
    const type = activity.type || 'general';
    const action = activity.metadata?.action || '';
    const entityType = activity.metadata?.entityType || '';
    
    // Try to find a matching template
    let templateKey = '';
    
    // First try to find an exact template match based on type and action
    if (type && action) {
      templateKey = `activities.templates.${type}${action.charAt(0).toUpperCase() + action.slice(1)}`;
      // Check if template exists
      const template = t(templateKey);
      if (!template || template === templateKey) {
        // If not found, try more generic patterns
        templateKey = '';
      }
    }
    
    // If no specific template was found, try the general patterns
    if (!templateKey) {
      switch (type) {
        case 'login':
          templateKey = 'activities.templates.login';
          break;
        case 'logout':
          templateKey = 'activities.templates.logout';
          break;
        case 'user':
          if (action === 'created') {
            templateKey = `activities.templates.create${entityType.charAt(0).toUpperCase() + entityType.slice(1)}`;
          } else if (action === 'updated') {
            templateKey = `activities.templates.update${entityType.charAt(0).toUpperCase() + entityType.slice(1)}`;
          } else if (action === 'deleted') {
            templateKey = `activities.templates.delete${entityType.charAt(0).toUpperCase() + entityType.slice(1)}`;
          }
          break;
        case 'class':
          if (action === 'created') {
            templateKey = 'activities.templates.createClass';
          } else if (action === 'updated') {
            templateKey = 'activities.templates.updateClass';
          } else if (action === 'deleted') {
            templateKey = 'activities.templates.deleteClass';
          }
          break;
        case 'grade':
          if (action === 'submitted') {
            templateKey = 'activities.templates.submitGrades';
          } else if (action === 'approved') {
            templateKey = 'activities.templates.approveGrades';
          } else if (action === 'rejected') {
            templateKey = 'activities.templates.rejectGrades';
          }
          break;
        case 'attendance':
          if (action === 'submitted') {
            templateKey = 'activities.templates.submitAttendance';
          } else if (action === 'approved') {
            templateKey = 'activities.templates.approveAttendance';
          } else if (action === 'rejected') {
            templateKey = 'activities.templates.rejectAttendance';
          }
          break;
        case 'announcement':
          if (action === 'created') {
            templateKey = 'activities.templates.createAnnouncement';
          } else if (action === 'updated') {
            templateKey = 'activities.templates.updateAnnouncement';
          } else if (action === 'deleted') {
            templateKey = 'activities.templates.deleteAnnouncement';
          }
          break;
        case 'system':
          if (action === 'updated') {
            templateKey = 'activities.templates.systemUpdate';
          } else if (action === 'settings') {
            templateKey = 'activities.templates.settingsChanged';
          }
          break;
        case 'profile':
          if (action === 'updated') {
            templateKey = 'activities.templates.profileUpdated';
          } else if (action === 'password') {
            templateKey = 'activities.templates.passwordChanged';
          }
          break;
        case 'document':
          if (action === 'uploaded') {
            templateKey = 'activities.templates.documentUploaded';
          } else if (action === 'downloaded') {
            templateKey = 'activities.templates.documentDownloaded';
          }
          break;
        default:
          // No matching template, keep original description
          break;
      }
    }

    // If we found a template, translate with it
    if (templateKey) {
      const template = t(templateKey);
      if (template && template !== templateKey) {
        // Replace variables in the template
        let description = template;
        
        // Extract all metadata fields for replacement
        const metadata = activity.metadata || {};
        
        // Replace all metadata variables
        Object.keys(metadata).forEach(key => {
          const value = metadata[key];
          if (value && typeof value === 'string') {
            description = description.replace(`{{${key}}}`, value);
          }
        });
        
        // Special cases and common replacements
        if (metadata.entityName) {
          description = description.replace('{{name}}', metadata.entityName);
        }
        if (metadata.className) {
          description = description.replace('{{class}}', metadata.className);
        }
        if (metadata.subject) {
          description = description.replace('{{subject}}', metadata.subject);
        }
        if (metadata.date) {
          description = description.replace('{{date}}', metadata.date);
        }
        if (metadata.title) {
          description = description.replace('{{title}}', metadata.title);
        }
        if (metadata.version) {
          description = description.replace('{{version}}', metadata.version);
        }
        
        // Replace user variable
        const userName = metadata.entityName || metadata.userName || activity.userId || 'System';
        description = description.replace('{{user}}', userName);
        
        translatedActivity.description = description;
      }
    }

    // Translate the activity type
    const translatedType = t(`activities.activityTypes.${type}`, type);
    translatedActivity.typeLabel = translatedType;

    // Translate the action if available
    if (action) {
      const translatedAction = t(`activities.actions.${action}`, action);
      translatedActivity.actionLabel = translatedAction;
    }

    // Translate entity type if available
    if (entityType) {
      const translatedEntityType = t(`activities.entityTypes.${entityType}`, entityType);
      translatedActivity.entityTypeLabel = translatedEntityType;
    }

    return translatedActivity;
  } catch (error) {
    console.error('Error translating activity:', error);
    return activity;
  }
};

export default {
  translate,
  translateActivity
}; 