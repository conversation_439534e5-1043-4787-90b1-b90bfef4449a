import React, { useEffect, useRef } from 'react';
import { View, StyleSheet, Animated, TouchableOpacity, Dimensions } from 'react-native';
import { Text, Surface, Avatar } from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import * as Animatable from 'react-native-animatable';
import { useNavigation } from '@react-navigation/native';

/**
 * A toast notification that appears at the top of the screen
 * 
 * @param {Object} props - Component props
 * @param {boolean} props.visible - Whether the notification is visible
 * @param {Function} props.onDismiss - Function to call when the notification is dismissed
 * @param {string} props.title - Notification title
 * @param {string} props.message - Notification message
 * @param {string} props.type - Notification type (success, error, info, warning)
 * @param {number} props.duration - Duration in ms before auto-dismissing (default: 3000, 0 to disable)
 * @param {string} props.navigateTo - Screen to navigate to when notification is pressed
 * @param {Object} props.navigationParams - Parameters to pass to the navigation
 */
const ToastNotification = ({
  visible,
  onDismiss,
  title,
  message,
  type = 'info',
  duration = 3000,
  navigateTo = null,
  navigationParams = {}
}) => {
  const navigation = useNavigation();
  const translateY = useRef(new Animated.Value(-100)).current;
  const timeoutRef = useRef(null);
  
  // Get notification color and icon based on type
  const getNotificationColor = () => {
    switch (type) {
      case 'success':
        return '#4CAF50';
      case 'error':
        return '#F44336';
      case 'warning':
        return '#FF9800';
      case 'info':
      default:
        return '#2196F3';
    }
  };
  
  const getNotificationIcon = () => {
    switch (type) {
      case 'success':
        return 'check-circle';
      case 'error':
        return 'alert-circle';
      case 'warning':
        return 'alert';
      case 'info':
      default:
        return 'information';
    }
  };
  
  // Show/hide animation
  useEffect(() => {
    if (visible) {
      // Clear any existing timeout
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      
      // Show animation
      Animated.spring(translateY, {
        toValue: 0,
        tension: 80,
        friction: 9,
        useNativeDriver: true
      }).start();
      
      // Auto-dismiss after duration (if not 0)
      if (duration > 0) {
        timeoutRef.current = setTimeout(() => {
          handleDismiss();
        }, duration);
      }
    } else {
      // Hide animation
      Animated.timing(translateY, {
        toValue: -100,
        duration: 300,
        useNativeDriver: true
      }).start();
    }
    
    // Cleanup timeout on unmount
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [visible, duration]);
  
  // Handle notification press
  const handlePress = () => {
    if (navigateTo) {
      navigation.navigate(navigateTo, navigationParams);
    }
    handleDismiss();
  };
  
  // Handle dismiss
  const handleDismiss = () => {
    Animated.timing(translateY, {
      toValue: -100,
      duration: 300,
      useNativeDriver: true
    }).start(() => {
      if (onDismiss) {
        onDismiss();
      }
    });
  };
  
  if (!visible) return null;
  
  return (
    <Animated.View 
      style={[
        styles.container, 
        { transform: [{ translateY }] }
      ]}
    >
      <TouchableOpacity 
        activeOpacity={0.9}
        onPress={handlePress}
        style={styles.touchable}
      >
        <Animatable.View 
          animation="fadeIn" 
          duration={300}
          style={styles.animatedContainer}
        >
          <Surface style={[styles.surface, { borderLeftColor: getNotificationColor() }]}>
            <Avatar.Icon 
              size={40} 
              icon={getNotificationIcon()} 
              style={[styles.icon, { backgroundColor: getNotificationColor() + '20' }]}
              color={getNotificationColor()}
            />
            
            <View style={styles.textContainer}>
              <Text style={styles.title} numberOfLines={1}>
                {title}
              </Text>
              
              <Text style={styles.message} numberOfLines={2}>
                {message}
              </Text>
            </View>
            
            <TouchableOpacity 
              onPress={handleDismiss}
              style={styles.closeButton}
            >
              <MaterialCommunityIcons 
                name="close" 
                size={20} 
                color="#757575" 
              />
            </TouchableOpacity>
          </Surface>
        </Animatable.View>
      </TouchableOpacity>
    </Animated.View>
  );
};

const { width } = Dimensions.get('window');

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 9999,
    paddingHorizontal: 16,
    paddingTop: 8,
    alignItems: 'center',
  },
  touchable: {
    width: '100%',
    maxWidth: 500,
  },
  animatedContainer: {
    width: '100%',
  },
  surface: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    elevation: 4,
    backgroundColor: '#FFFFFF',
    borderLeftWidth: 4,
  },
  icon: {
    marginRight: 12,
  },
  textContainer: {
    flex: 1,
    marginRight: 8,
  },
  title: {
    fontWeight: 'bold',
    fontSize: 16,
    marginBottom: 2,
  },
  message: {
    fontSize: 14,
    color: '#666666',
  },
  closeButton: {
    padding: 4,
  }
});

export default ToastNotification;
