@echo off
echo === Update Timetable Data Tool ===
echo This batch file will help you update timetable data.
echo.

REM Get the current directory with quotes to handle spaces
set "CURRENT_DIR=%~dp0"
echo Current directory: %CURRENT_DIR%

REM Run the update script with node
echo Running update script...
node "%CURRENT_DIR%update-timetable-data.js"

echo.
echo Update process completed.
echo Press any key to exit...
pause > nul
