import React, { useState, useRef, useEffect } from 'react';
import { View, StyleSheet, ActivityIndicator, TouchableOpacity, Text } from 'react-native';
import { Video } from 'expo-av';
import { MaterialIcons } from '@expo/vector-icons';
import CloudinaryService from '../../services/CloudinaryService';

/**
 * CloudinaryVideo component for displaying videos from Cloudinary
 * This component supports both Cloudinary public IDs and Firebase Storage URLs
 * 
 * @param {Object} props - Component props
 * @param {string} props.source - Cloudinary public ID or Firebase Storage URL
 * @param {string} props.fallbackSource - Fallback video source to display if the main video fails to load
 * @param {Object} props.style - Style object for the video
 * @param {number} props.width - Width of the video
 * @param {number} props.height - Height of the video
 * @param {string} props.resizeMode - Resize mode for the video (cover, contain, stretch, etc.)
 * @param {number} props.borderRadius - Border radius for the video
 * @param {boolean} props.showControls - Whether to show video controls
 * @param {boolean} props.autoPlay - Whether to auto-play the video
 * @param {boolean} props.loop - Whether to loop the video
 * @param {boolean} props.muted - Whether to mute the video
 * @param {boolean} props.showLoadingIndicator - Whether to show a loading indicator while the video is loading
 * @param {Function} props.onLoad - Callback function to call when the video is loaded
 * @param {Function} props.onError - Callback function to call when the video fails to load
 * @param {Function} props.onPlaybackStatusUpdate - Callback function to call when the playback status updates
 */
const CloudinaryVideo = ({
  source,
  fallbackSource,
  style = {},
  width,
  height,
  resizeMode = 'cover',
  borderRadius = 0,
  showControls = true,
  autoPlay = false,
  loop = false,
  muted = false,
  showLoadingIndicator = true,
  onLoad,
  onError,
  onPlaybackStatusUpdate,
  ...props
}) => {
  const videoRef = useRef(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);
  const [videoUrl, setVideoUrl] = useState(null);
  const [status, setStatus] = useState({});
  const [showPlayButton, setShowPlayButton] = useState(!autoPlay);

  useEffect(() => {
    if (!source) {
      setError(true);
      setLoading(false);
      return;
    }

    try {
      // Generate Cloudinary URL with transformations
      const options = {
        width: width || style.width,
        height: height || style.height,
        quality: 'auto',
        format: 'mp4'
      };

      // Get the URL using CloudinaryService
      let url;
      if (typeof source === 'string' && 
          (source.includes('firebasestorage.googleapis.com') || 
           source.includes('storage.googleapis.com'))) {
        // For Firebase Storage URLs, use the fetch capability
        url = CloudinaryService.getCloudinaryFromFirebase(source, options);
      } else {
        // For Cloudinary public IDs
        url = CloudinaryService.getUrl(source, {
          ...options,
          resourceType: 'video'
        });
      }
      
      setVideoUrl(url);
    } catch (err) {
      console.error('Error generating Cloudinary video URL:', err);
      setError(true);
      if (onError) onError(err);
    }
  }, [source, width, height, style.width, style.height]);

  const handleLoad = () => {
    setLoading(false);
    if (onLoad) onLoad();
  };

  const handleError = (err) => {
    console.error('Error loading Cloudinary video:', err);
    setError(true);
    setLoading(false);
    if (onError) onError(err);
  };

  const handlePlaybackStatusUpdate = (status) => {
    setStatus(status);
    if (onPlaybackStatusUpdate) onPlaybackStatusUpdate(status);
  };

  const togglePlayPause = async () => {
    if (videoRef.current) {
      if (status.isPlaying) {
        await videoRef.current.pauseAsync();
      } else {
        setShowPlayButton(false);
        await videoRef.current.playAsync();
      }
    }
  };

  // Calculate container style
  const containerStyle = {
    ...style,
    width: width || style.width,
    height: height || style.height,
    borderRadius: borderRadius || style.borderRadius || 0,
    overflow: 'hidden',
    justifyContent: 'center',
    alignItems: 'center',
  };

  // If there's an error and a fallback source is provided, show the fallback video
  if (error && fallbackSource) {
    return (
      <Video
        source={typeof fallbackSource === 'string' ? { uri: fallbackSource } : fallbackSource}
        style={containerStyle}
        resizeMode={resizeMode}
        useNativeControls={showControls}
        isLooping={loop}
        isMuted={muted}
        shouldPlay={autoPlay}
        onPlaybackStatusUpdate={handlePlaybackStatusUpdate}
        {...props}
      />
    );
  }

  return (
    <View style={containerStyle}>
      {videoUrl && (
        <Video
          ref={videoRef}
          source={{ uri: videoUrl }}
          style={styles.video}
          resizeMode={resizeMode}
          useNativeControls={showControls}
          isLooping={loop}
          isMuted={muted}
          shouldPlay={autoPlay}
          onPlaybackStatusUpdate={handlePlaybackStatusUpdate}
          onLoad={handleLoad}
          onError={handleError}
          {...props}
        />
      )}
      
      {loading && showLoadingIndicator && (
        <ActivityIndicator
          size="large"
          color="#fff"
          style={styles.loadingIndicator}
        />
      )}
      
      {!loading && !status.isPlaying && showPlayButton && (
        <TouchableOpacity
          style={styles.playButton}
          onPress={togglePlayPause}
          activeOpacity={0.8}
        >
          <MaterialIcons name="play-arrow" size={50} color="#fff" />
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  video: {
    width: '100%',
    height: '100%',
  },
  loadingIndicator: {
    position: 'absolute',
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    borderRadius: 8,
    padding: 8,
  },
  playButton: {
    position: 'absolute',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    borderRadius: 50,
    width: 80,
    height: 80,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default CloudinaryVideo;
