@echo off
echo ===== Deploying Firebase Rules =====

echo Step 1: Checking Firebase CLI installation...
where firebase >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
  echo Firebase CLI not found. Installing...
  npm install -g firebase-tools
) else (
  echo Firebase CLI is already installed.
)

echo Step 2: Logging in to Firebase...
firebase login

echo Step 3: Deploying Firestore rules...
firebase deploy --only firestore:rules

echo Step 4: Deploying Storage rules...
firebase deploy --only storage:rules

echo ===== Deployment completed =====
pause
