import React, { useEffect, useState, useRef } from 'react';
import { StyleSheet, TouchableOpacity, Animated } from 'react-native';
import { Badge, IconButton, useTheme } from 'react-native-paper';
import { useNavigation } from '@react-navigation/native';
import { db } from '../../config/firebase';
import { collection, query, where, getDocs } from 'firebase/firestore';
import * as Animatable from 'react-native-animatable';

const GradeApprovalBadge = ({
  size = 24,
  color,
  badgeSize = 16,
  style,
  onPress,
  containerStyle,
  animated = true,
  badgeColor
}) => {
  // No theme needed
  const navigation = useNavigation();
  const [pendingCount, setPendingCount] = useState(0);
  const [prevCount, setPrevCount] = useState(0);
  const pulseAnim = useRef(new Animated.Value(1)).current;
  const badgeRef = useRef(null);

  // Fetch pending grade submissions when component mounts
  useEffect(() => {
    fetchPendingSubmissions();

    // Set up a refresh interval (every 5 minutes)
    const intervalId = setInterval(fetchPendingSubmissions, 5 * 60 * 1000);

    return () => clearInterval(intervalId);
  }, []);

  // Animate badge when count changes
  useEffect(() => {
    if (animated && pendingCount > 0 && pendingCount !== prevCount) {
      // Pulse animation
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.3,
          duration: 200,
          useNativeDriver: true
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 200,
          useNativeDriver: true
        })
      ]).start();

      // Bounce animation using Animatable
      if (badgeRef.current) {
        badgeRef.current.bounce(800);
      }

      setPrevCount(pendingCount);
    }
  }, [pendingCount, animated, prevCount]);

  // Fetch pending grade submissions
  const fetchPendingSubmissions = async () => {
    try {
      const submissionsRef = collection(db, 'gradeSubmissions');
      const q = query(submissionsRef, where('status', '==', 'pending'));
      const querySnapshot = await getDocs(q);

      setPendingCount(querySnapshot.size);
      console.log(`Found ${querySnapshot.size} pending grade submissions`);
    } catch (error) {
      console.error('Error fetching pending grade submissions:', error);
    }
  };

  // Handle badge press
  const handlePress = () => {
    if (onPress) {
      onPress();
    } else {
      // Navigate to grade approval screen
      navigation.navigate('AdminGradeApproval');
    }
  };

  // Format badge text (99+ for large numbers)
  const getBadgeText = () => {
    if (pendingCount > 99) return '99+';
    return pendingCount.toString();
  };

  // Don't render anything if there are no pending submissions
  if (pendingCount === 0) return null;

  return (
    <TouchableOpacity
      onPress={handlePress}
      style={[styles.container, containerStyle]}
    >
      <IconButton
        icon="clipboard-check"
        size={size}
        color={color || '#1976d2'}
        style={[styles.icon, style]}
      />
      <Animated.View
        style={[
          styles.badgeContainer,
          { transform: [{ scale: pulseAnim }] }
        ]}
      >
        <Animatable.View ref={badgeRef}>
          <Badge
            size={badgeSize}
            style={[
              styles.badge,
              { backgroundColor: badgeColor || '#B00020' }
            ]}
          >
            {getBadgeText()}
          </Badge>
        </Animatable.View>
      </Animated.View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
  },
  icon: {
    margin: 0,
  },
  badgeContainer: {
    position: 'absolute',
    top: -4,
    right: -4,
    zIndex: 1
  },
  badge: {
    position: 'relative',
    fontWeight: 'bold',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.3,
    shadowRadius: 2,
  },
});

export default GradeApprovalBadge;
