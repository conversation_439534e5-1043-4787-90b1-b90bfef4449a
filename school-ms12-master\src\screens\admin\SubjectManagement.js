import React, { useState, useEffect, useRef } from 'react';
import { View, ScrollView, StyleSheet, Animated, TouchableOpacity, Alert } from 'react-native';
import { Card, Title, FAB, Portal, Modal, DataTable, Searchbar, List, useTheme, Text, IconButton, Surface, Divider, Avatar, Chip, Badge } from 'react-native-paper';
import { db } from '../../config/firebase';
import { collection, addDoc, query, getDocs, doc, updateDoc, deleteDoc, where } from 'firebase/firestore';
import CustomButton from '../../components/common/CustomButton';
import CustomInput from '../../components/common/CustomInput';
import { useLanguage } from '../../context/LanguageContext';
import { LinearGradient } from 'expo-linear-gradient';
import * as Animatable from 'react-native-animatable';
import { Ionicons, MaterialCommunityIcons } from '@expo/vector-icons';
import ActivityService from '../../services/ActivityService';
import AdminAppHeader from '../../components/common/AdminAppHeader';
import AdminSidebar from '../../components/common/AdminSidebar';
import SidebarBackdrop from '../../components/common/SidebarBackdrop';
import { useNavigation } from '@react-navigation/native';
import FileUploader from '../../components/common/FileUploader';
import CloudinaryService from '../../services/CloudinaryService';

const SubjectManagement = () => {
  const { translate } = useLanguage();
  // No theme needed
  const navigation = useNavigation();
  const [subjects, setSubjects] = useState([]);
  const [classes, setClasses] = useState([]);
  const [teachers, setTeachers] = useState([]);
  const [visible, setVisible] = useState(false);
  const [selectedSubject, setSelectedSubject] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(false);
  const [fadeAnim] = useState(new Animated.Value(0));
  const [slideAnim] = useState(new Animated.Value(50));
  const [formErrors, setFormErrors] = useState({});

  // Sidebar state
  const [drawerOpen, setDrawerOpen] = useState(false);
  const drawerAnim = useRef(new Animated.Value(-300)).current;
  const backdropFadeAnim = useRef(new Animated.Value(0)).current;
  const [activeSidebarItem, setActiveSidebarItem] = useState('SubjectManagement');

  const [formData, setFormData] = useState({
    name: '',
    code: '',
    description: '',
    classId: '',
    sectionId: '',
    teacherId: '',
    passingScore: '40',
    maxScore: '100',
    weightage: {
      assignments: '20',
      tests: '30',
      finalExam: '50',
    },
    files: [],
  });

  const [sections, setSections] = useState([]);
  const [selectedClass, setSelectedClass] = useState(null);

  useEffect(() => {
    fetchSubjects();
    fetchClasses();
    fetchTeachers();
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      })
    ]).start();

    // Hide the default header
    navigation.setOptions({
      headerShown: false,
    });
  }, [navigation]);

  const toggleDrawer = () => {
    if (drawerOpen) {
      // Close drawer
      Animated.parallel([
        Animated.timing(drawerAnim, {
          toValue: -300,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(backdropFadeAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        })
      ]).start(() => setDrawerOpen(false));
    } else {
      // Open drawer
      setDrawerOpen(true);
      Animated.parallel([
        Animated.timing(drawerAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(backdropFadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        })
      ]).start();
    }
  };

  const fetchSubjects = async () => {
    try {
      const subjectsRef = collection(db, 'subjects');
      const q = query(subjectsRef);
      const querySnapshot = await getDocs(q);

      const subjectsData = [];
      querySnapshot.forEach((doc) => {
        subjectsData.push({ id: doc.id, ...doc.data() });
      });

      setSubjects(subjectsData);
    } catch (error) {
      console.error('Error fetching subjects:', error);
    }
  };

  const fetchClasses = async () => {
    try {
      const classesRef = collection(db, 'classes');
      const q = query(classesRef);
      const querySnapshot = await getDocs(q);

      const classesData = [];
      querySnapshot.forEach((doc) => {
        const classData = doc.data();
        classesData.push({
          id: doc.id,
          ...classData,
          sections: classData.sections || []
        });
      });

      setClasses(classesData);
    } catch (error) {
      console.error('Error fetching classes:', error);
    }
  };

  const handleClassSelect = (classItem) => {
    setSelectedClass(classItem);
    setFormData({ ...formData, classId: classItem.id, sectionId: '' });
    setFormErrors({ ...formErrors, classId: '' });

    // Extract sections from the selected class
    if (classItem.sections && classItem.sections.length > 0) {
      const sectionsData = classItem.sections.map(section => ({
        id: `${classItem.id}_${section.name}`,
        name: section.name,
        capacity: section.capacity || 30,
        classId: classItem.id,
        className: classItem.name
      }));
      setSections(sectionsData);
    } else {
      setSections([]);
    }
  };

  const fetchTeachers = async () => {
    try {
      const teachersRef = collection(db, 'users');
      const q = query(teachersRef, where('role', '==', 'teacher'));
      const querySnapshot = await getDocs(q);

      const teachersData = [];
      querySnapshot.forEach((doc) => {
        teachersData.push({ id: doc.id, ...doc.data() });
      });

      setTeachers(teachersData);
    } catch (error) {
      console.error('Error fetching teachers:', error);
    }
  };

  const validateForm = () => {
    const errors = {};
    if (!formData.name.trim()) {
      errors.name = translate('errors.required');
    }
    if (!formData.code.trim()) {
      errors.code = translate('errors.required');
    }
    if (!formData.classId) {
      errors.classId = translate('errors.required');
    }
    if (!formData.sectionId && sections.length > 0) {
      errors.sectionId = translate('errors.required');
    }
    if (!formData.teacherId) {
      errors.teacherId = translate('errors.required');
    }
    if (!formData.passingScore || isNaN(formData.passingScore) || Number(formData.passingScore) <= 0) {
      errors.passingScore = translate('errors.invalidScore');
    }
    if (!formData.maxScore || isNaN(formData.maxScore) || Number(formData.maxScore) <= 0) {
      errors.maxScore = translate('errors.invalidScore');
    }
    if (Number(formData.passingScore) >= Number(formData.maxScore)) {
      errors.passingScore = translate('errors.passingScoreGreater');
    }
    const totalWeightage = Number(formData.weightage.assignments) +
                          Number(formData.weightage.tests) +
                          Number(formData.weightage.finalExam);
    if (totalWeightage !== 100) {
      errors.weightage = translate('errors.invalidWeightage');
    }
    return errors;
  };

  const handleAddSubject = async () => {
    const errors = validateForm();
    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }

    try {
      setLoading(true);

      // Get section details
      const selectedSection = sections.find(section => section.id === formData.sectionId);
      const sectionName = selectedSection ? selectedSection.name : '';

      // Process files if any
      const fileData = formData.files.map(file => ({
        name: file.name,
        type: file.type,
        size: file.size,
        downloadUrl: file.downloadUrl,
        publicId: file.publicId,
        uploadedAt: new Date().toISOString()
      }));

      const subjectsRef = collection(db, 'subjects');
      const newSubject = {
        ...formData,
        sectionName: sectionName,
        passingScore: parseInt(formData.passingScore),
        maxScore: parseInt(formData.maxScore),
        weightage: {
          assignments: parseInt(formData.weightage.assignments),
          tests: parseInt(formData.weightage.tests),
          finalExam: parseInt(formData.weightage.finalExam),
        },
        files: fileData,
        hasAttachments: fileData.length > 0,
        createdAt: new Date().toISOString(),
      };

      const docRef = await addDoc(subjectsRef, newSubject);

      // Log activity
      await ActivityService.logActivity(
        'admin',
        'academic',
        translate('activities.newSubject'),
        translate('activities.newSubjectDesc', { subject: formData.name })
      );

      setVisible(false);
      resetForm();
      fetchSubjects();

      // Show success message
      Alert.alert(
        translate('success.title'),
        translate('success.subjectAdded'),
        [{ text: translate('common.ok') }]
      );
    } catch (error) {
      console.error('Error adding subject:', error);
      Alert.alert(
        translate('errors.title'),
        translate('errors.addSubject'),
        [{ text: translate('common.ok') }]
      );
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateSubject = async () => {
    const errors = validateForm();
    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }

    try {
      setLoading(true);

      // Get section details
      const selectedSection = sections.find(section => section.id === formData.sectionId);
      const sectionName = selectedSection ? selectedSection.name : '';

      // Process files if any
      const fileData = formData.files.map(file => ({
        name: file.name,
        type: file.type,
        size: file.size,
        downloadUrl: file.downloadUrl,
        publicId: file.publicId,
        uploadedAt: file.uploadedAt || new Date().toISOString()
      }));

      const subjectRef = doc(db, 'subjects', selectedSubject.id);
      const updatedSubject = {
        ...formData,
        sectionName: sectionName,
        passingScore: parseInt(formData.passingScore),
        maxScore: parseInt(formData.maxScore),
        weightage: {
          assignments: parseInt(formData.weightage.assignments),
          tests: parseInt(formData.weightage.tests),
          finalExam: parseInt(formData.weightage.finalExam),
        },
        files: fileData,
        hasAttachments: fileData.length > 0,
        updatedAt: new Date().toISOString(),
      };

      await updateDoc(subjectRef, updatedSubject);

      // Log activity
      await ActivityService.logActivity(
        'admin',
        'academic',
        translate('activities.updateSubject'),
        translate('activities.updateSubjectDesc', { subject: formData.name })
      );

      setVisible(false);
      resetForm();
      fetchSubjects();

      // Show success message
      Alert.alert(
        translate('success.title'),
        translate('success.subjectUpdated'),
        [{ text: translate('common.ok') }]
      );
    } catch (error) {
      console.error('Error updating subject:', error);
      Alert.alert(
        translate('errors.title'),
        translate('errors.updateSubject'),
        [{ text: translate('common.ok') }]
      );
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteSubject = async (subjectId) => {
    try {
      await deleteDoc(doc(db, 'subjects', subjectId));

      // Log activity
      await ActivityService.logActivity(
        'admin',
        'academic',
        translate('activities.deleteSubject'),
        translate('activities.deleteSubjectDesc', { subject: selectedSubject.name })
      );

      fetchSubjects();
    } catch (error) {
      console.error('Error deleting subject:', error);
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      code: '',
      description: '',
      classId: '',
      sectionId: '',
      teacherId: '',
      passingScore: '40',
      maxScore: '100',
      weightage: {
        assignments: '20',
        tests: '30',
        finalExam: '50',
      },
      files: [],
    });
    setSelectedSubject(null);
    setSelectedClass(null);
    setSections([]);
    setFormErrors({});
  };

  const handleFileUploaded = (file) => {
    setFormData(prevData => ({
      ...prevData,
      files: [...prevData.files, file]
    }));
  };

  const filteredSubjects = subjects.filter(subject =>
    subject.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    subject.code.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <View style={styles.mainContainer}>
      {/* Sidebar */}
      <AdminSidebar
        drawerAnim={drawerAnim}
        activeSidebarItem={activeSidebarItem}
        setActiveSidebarItem={setActiveSidebarItem}
        toggleDrawer={toggleDrawer}
      />

      {/* Backdrop */}
      <SidebarBackdrop
        visible={drawerOpen}
        onPress={toggleDrawer}
        fadeAnim={backdropFadeAnim}
      />

      {/* Admin App Header */}
      <AdminAppHeader
        title={translate('subject.title')}
        onMenuPress={toggleDrawer}
      />

      <Animated.View
        style={[
          styles.container,
          {
            opacity: fadeAnim,
            transform: [{ translateY: slideAnim }]
          }
        ]}
      >
        <Surface style={styles.header}>
          <LinearGradient
            colors={['#1976d2', '#005cb2']}
            style={styles.headerGradient}
          >
            <Searchbar
              placeholder={translate('subject.search')}
              onChangeText={setSearchQuery}
              value={searchQuery}
              style={styles.searchBar}
              iconColor={'#ffffff'}
              inputStyle={{ color: '#ffffff' }}
              placeholderTextColor={'#ffffff' + '80'}
            />
          </LinearGradient>
        </Surface>

      <ScrollView style={styles.content}>
        <DataTable style={styles.dataTable}>
          <DataTable.Header style={styles.tableHeader}>
            <DataTable.Title textStyle={styles.headerText}>{translate('subject.name')}</DataTable.Title>
            <DataTable.Title textStyle={styles.headerText}>{translate('subject.code')}</DataTable.Title>
            <DataTable.Title textStyle={styles.headerText}>{translate('subject.class')}</DataTable.Title>
            <DataTable.Title textStyle={styles.headerText}>{translate('subject.section')}</DataTable.Title>
            <DataTable.Title textStyle={styles.headerText}>{translate('subject.teacher')}</DataTable.Title>
            <DataTable.Title textStyle={styles.headerText}>{translate('subject.files')}</DataTable.Title>
            <DataTable.Title textStyle={styles.headerText}>{translate('actions')}</DataTable.Title>
          </DataTable.Header>

          {filteredSubjects.map((subject, index) => (
            <Animatable.View
              key={subject.id}
              animation="fadeInUp"
              duration={500}
              delay={index * 100}
            >
              <DataTable.Row
                style={styles.tableRow}
                onPress={() => {
                  setSelectedSubject(subject);
                  // Get the class to load sections
                  const classItem = classes.find(c => c.id === subject.classId);
                  if (classItem) {
                    handleClassSelect(classItem);
                  }

                  setFormData({
                    name: subject.name,
                    code: subject.code,
                    description: subject.description,
                    classId: subject.classId,
                    sectionId: subject.sectionId || '',
                    teacherId: subject.teacherId,
                    passingScore: subject.passingScore.toString(),
                    maxScore: subject.maxScore.toString(),
                    weightage: {
                      assignments: subject.weightage.assignments.toString(),
                      tests: subject.weightage.tests.toString(),
                      finalExam: subject.weightage.finalExam.toString(),
                    },
                    files: subject.files || [],
                  });
                  setVisible(true);
                }}
              >
                <DataTable.Cell textStyle={styles.cellText}>{subject.name}</DataTable.Cell>
                <DataTable.Cell textStyle={styles.cellText}>{subject.code}</DataTable.Cell>
                <DataTable.Cell textStyle={styles.cellText}>
                  {classes.find(c => c.id === subject.classId)?.name || 'N/A'}
                </DataTable.Cell>
                <DataTable.Cell textStyle={styles.cellText}>
                  {subject.sectionName || 'N/A'}
                </DataTable.Cell>
                <DataTable.Cell textStyle={styles.cellText}>
                  {teachers.find(t => t.id === subject.teacherId)?.displayName || 'N/A'}
                </DataTable.Cell>
                <DataTable.Cell>
                  {subject.hasAttachments ? (
                    <Badge size={24} style={styles.fileBadge}>
                      {subject.files?.length || 0}
                    </Badge>
                  ) : (
                    <Text style={styles.noFileText}>0</Text>
                  )}
                </DataTable.Cell>
                <DataTable.Cell>
                  <IconButton
                    icon="pencil"
                    size={20}
                    onPress={() => {
                      setSelectedSubject(subject);
                      // Get the class to load sections
                      const classItem = classes.find(c => c.id === subject.classId);
                      if (classItem) {
                        handleClassSelect(classItem);
                      }

                      setFormData({
                        name: subject.name,
                        code: subject.code,
                        description: subject.description,
                        classId: subject.classId,
                        sectionId: subject.sectionId || '',
                        teacherId: subject.teacherId,
                        passingScore: subject.passingScore.toString(),
                        maxScore: subject.maxScore.toString(),
                        weightage: {
                          assignments: subject.weightage.assignments.toString(),
                          tests: subject.weightage.tests.toString(),
                          finalExam: subject.weightage.finalExam.toString(),
                        },
                        files: subject.files || [],
                      });
                      setVisible(true);
                    }}
                  />
                </DataTable.Cell>
              </DataTable.Row>
            </Animatable.View>
          ))}
        </DataTable>
      </ScrollView>

      <Portal>
        <Modal
          visible={visible}
          onDismiss={() => {
            setVisible(false);
            resetForm();
          }}
          contentContainerStyle={styles.modalContent}
        >
          <ScrollView>
            <LinearGradient
              colors={['#1976d2', '#005cb2']}
              style={styles.modalHeader}
            >
              <Title style={styles.modalTitle}>
                {selectedSubject ? translate('subject.editSubject') : translate('subject.addSubject')}
              </Title>
            </LinearGradient>

            <View style={styles.formContainer}>
              <CustomInput
                label={translate('subject.name')}
                value={formData.name}
                onChangeText={(text) => {
                  setFormData({ ...formData, name: text });
                  setFormErrors({ ...formErrors, name: '' });
                }}
                error={formErrors.name}
              />

              <CustomInput
                label={translate('subject.code')}
                value={formData.code}
                onChangeText={(text) => {
                  setFormData({ ...formData, code: text });
                  setFormErrors({ ...formErrors, code: '' });
                }}
                error={formErrors.code}
              />

              <List.Section title={translate('subject.class')}>
                {classes.map((classItem) => (
                  <Animatable.View
                    key={classItem.id}
                    animation="fadeInRight"
                    duration={500}
                  >
                    <List.Item
                      title={classItem.name}
                      left={props => <List.Icon {...props} icon="school" />}
                      onPress={() => handleClassSelect(classItem)}
                      style={[
                        styles.listItem,
                        formData.classId === classItem.id && styles.selectedItem
                      ]}
                    />
                  </Animatable.View>
                ))}
                {formErrors.classId && (
                  <Text style={styles.errorText}>{formErrors.classId}</Text>
                )}
              </List.Section>

              {sections.length > 0 && (
                <List.Section title={translate('subject.section')}>
                  {sections.map((section) => (
                    <Animatable.View
                      key={section.id}
                      animation="fadeInRight"
                      duration={500}
                    >
                      <List.Item
                        title={section.name}
                        description={`${translate('subject.capacity')}: ${section.capacity}`}
                        left={props => <List.Icon {...props} icon="account-group" />}
                        onPress={() => {
                          setFormData({ ...formData, sectionId: section.id });
                          setFormErrors({ ...formErrors, sectionId: '' });
                        }}
                        style={[
                          styles.listItem,
                          formData.sectionId === section.id && styles.selectedItem
                        ]}
                      />
                    </Animatable.View>
                  ))}
                  {formErrors.sectionId && (
                    <Text style={styles.errorText}>{formErrors.sectionId}</Text>
                  )}
                </List.Section>
              )}

              <List.Section title={translate('subject.teacher')}>
                {teachers.map((teacher) => (
                  <Animatable.View
                    key={teacher.id}
                    animation="fadeInRight"
                    duration={500}
                  >
                    <List.Item
                      title={teacher.displayName || translate('common.unknown')}
                      description={teacher.email || ''}
                      left={props => (
                        <Avatar.Text
                          size={40}
                          label={
                            teacher.displayName
                              ? teacher.displayName.split(' ').map(n => n[0]).join('')
                              : '?'
                          }
                        />
                      )}
                      onPress={() => {
                        setFormData({ ...formData, teacherId: teacher.id });
                        setFormErrors({ ...formErrors, teacherId: '' });
                      }}
                      style={[
                        styles.listItem,
                        formData.teacherId === teacher.id && styles.selectedItem
                      ]}
                    />
                  </Animatable.View>
                ))}
                {formErrors.teacherId && (
                  <Text style={styles.errorText}>{formErrors.teacherId}</Text>
                )}
              </List.Section>

              <CustomInput
                label={translate('subject.description')}
                value={formData.description}
                onChangeText={(text) => setFormData({ ...formData, description: text })}
                multiline
                numberOfLines={3}
              />

              <Title style={styles.sectionTitle}>{translate('subject.passingScore')}</Title>

              <CustomInput
                label={translate('subject.passingScore')}
                value={formData.passingScore}
                onChangeText={(text) => {
                  setFormData({ ...formData, passingScore: text });
                  setFormErrors({ ...formErrors, passingScore: '' });
                }}
                keyboardType="numeric"
                error={formErrors.passingScore}
              />

              <CustomInput
                label={translate('subject.maxScore')}
                value={formData.maxScore}
                onChangeText={(text) => {
                  setFormData({ ...formData, maxScore: text });
                  setFormErrors({ ...formErrors, maxScore: '' });
                }}
                keyboardType="numeric"
                error={formErrors.maxScore}
              />

              <Title style={styles.sectionTitle}>{translate('subject.weightage')}</Title>

              <View style={styles.weightageContainer}>
                <CustomInput
                  label={translate('subject.assignments')}
                  value={formData.weightage.assignments}
                  onChangeText={(text) => {
                    setFormData({
                      ...formData,
                      weightage: { ...formData.weightage, assignments: text },
                    });
                    setFormErrors({ ...formErrors, weightage: '' });
                  }}
                  keyboardType="numeric"
                  style={styles.weightageInput}
                />

                <CustomInput
                  label={translate('subject.tests')}
                  value={formData.weightage.tests}
                  onChangeText={(text) => {
                    setFormData({
                      ...formData,
                      weightage: { ...formData.weightage, tests: text },
                    });
                    setFormErrors({ ...formErrors, weightage: '' });
                  }}
                  keyboardType="numeric"
                  style={styles.weightageInput}
                />

                <CustomInput
                  label={translate('subject.finalExam')}
                  value={formData.weightage.finalExam}
                  onChangeText={(text) => {
                    setFormData({
                      ...formData,
                      weightage: { ...formData.weightage, finalExam: text },
                    });
                    setFormErrors({ ...formErrors, weightage: '' });
                  }}
                  keyboardType="numeric"
                  style={styles.weightageInput}
                />
              </View>
              {formErrors.weightage && (
                <Text style={styles.errorText}>{formErrors.weightage}</Text>
              )}

              <Title style={styles.sectionTitle}>{translate('subject.files')}</Title>
              <Text style={styles.helperText}>{translate('subject.filesHelper')}</Text>

              <FileUploader
                onFileUploaded={handleFileUploaded}
                onError={(error) => console.error('File upload error:', error)}
                multiple={true}
                maxFiles={5}
                storagePath="subjects"
                showPreview={true}
                previewType="list"
                buttonLabel="subject.uploadFiles"
                buttonIcon="file-upload"
                initialFiles={formData.files}
                style={styles.fileUploader}
              />

              <View style={styles.modalButtons}>
                <CustomButton
                  mode="contained"
                  onPress={selectedSubject ? handleUpdateSubject : handleAddSubject}
                  loading={loading}
                  style={styles.saveButton}
                >
                  {selectedSubject ? translate('actions.update') : translate('actions.add')}
                </CustomButton>

                {selectedSubject && (
                  <CustomButton
                    mode="outlined"
                    onPress={() => handleDeleteSubject(selectedSubject.id)}
                    style={styles.deleteButton}
                  >
                    {translate('actions.delete')}
                  </CustomButton>
                )}

                <CustomButton
                  mode="outlined"
                  onPress={() => {
                    setVisible(false);
                    resetForm();
                  }}
                >
                  {translate('actions.cancel')}
                </CustomButton>
              </View>
            </View>
          </ScrollView>
        </Modal>
      </Portal>

      <FAB
        style={[styles.fab, { backgroundColor: '#1976d2' }]}
        icon="plus"
        onPress={() => setVisible(true)}
        color={'#ffffff'}
      />
    </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    elevation: 4,
  },
  headerGradient: {
    padding: 20,
    paddingTop: 40,
  },
  headerTitle: {
    color: '#fff',
    fontSize: 24,
    marginBottom: 16,
  },
  searchBar: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    elevation: 0,
  },
  content: {
    flex: 1,
  },
  dataTable: {
    margin: 16,
    borderRadius: 8,
    elevation: 4,
    backgroundColor: '#fff',
  },
  tableHeader: {
    backgroundColor: '#f5f5f5',
  },
  headerText: {
    fontWeight: 'bold',
  },
  tableRow: {
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  cellText: {
    color: '#333',
  },
  modalContent: {
    backgroundColor: 'white',
    margin: 20,
    borderRadius: 10,
    maxHeight: '80%',
  },
  modalHeader: {
    padding: 20,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
  },
  modalTitle: {
    color: '#fff',
    fontSize: 20,
  },
  formContainer: {
    padding: 20,
  },
  listItem: {
    marginVertical: 4,
    borderRadius: 8,
  },
  selectedItem: {
    backgroundColor: '#e8f4f8',
  },
  sectionTitle: {
    fontSize: 18,
    marginTop: 20,
    marginBottom: 10,
    color: '#333',
  },
  helperText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 10,
  },
  weightageContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  weightageInput: {
    flex: 1,
    marginHorizontal: 4,
  },
  modalButtons: {
    marginTop: 20,
  },
  saveButton: {
    marginBottom: 10,
  },
  deleteButton: {
    marginBottom: 10,
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
  },
  errorText: {
    color: '#f44336',
    fontSize: 12,
    marginTop: 4,
    marginLeft: 16,
  },
  fileUploader: {
    marginTop: 10,
    marginBottom: 20,
  },
  fileBadge: {
    backgroundColor: '#1976d2',
  },
  noFileText: {
    color: '#999',
    fontSize: 14,
  },
  filePreview: {
    width: 40,
    height: 40,
    borderRadius: 4,
    marginRight: 8,
  },
  fileIcon: {
    width: 40,
    height: 40,
    borderRadius: 4,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default SubjectManagement;
