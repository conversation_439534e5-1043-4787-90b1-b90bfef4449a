import React, { useState, useEffect, useRef } from 'react';
import { View, StyleSheet, Animated } from 'react-native';
import { Surface, Text, Badge, Chip, IconButton, ProgressBar, useTheme } from 'react-native-paper';
import { useLanguage } from '../../context/LanguageContext';
import * as Animatable from 'react-native-animatable';
import { LinearGradient } from 'expo-linear-gradient';
import { db, auth } from '../../config/firebase';
import { collection, query, where, onSnapshot } from 'firebase/firestore';
import NotificationService from '../../services/NotificationService';

const OngoingClassTracker = ({
  schedule,
  role,
  classId,
  section,
  teacherId,
  onPressViewSchedule,
  style
}) => {
  // No theme needed
  const { translate } = useLanguage();
  const [currentPeriod, setCurrentPeriod] = useState(null);
  const [nextPeriod, setNextPeriod] = useState(null);
  const [timeRemaining, setTimeRemaining] = useState(null);
  const [nextClassNotified, setNextClassNotified] = useState(false);
  const timerRef = useRef(null);
  const pulseAnim = useRef(new Animated.Value(1)).current;

  // Start pulse animation for the "Now" badge
  useEffect(() => {
    const startPulseAnimation = () => {
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.2,
          duration: 800,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
        })
      ]).start(() => {
        if (currentPeriod) {
          startPulseAnimation();
        }
      });
    };

    if (currentPeriod) {
      startPulseAnimation();
    } else {
      // Reset animation when no current period
      pulseAnim.setValue(1);
    }

    return () => {
      // Stop animation on cleanup
      pulseAnim.stopAnimation();
    };
  }, [currentPeriod, pulseAnim]);

  // Update current and next periods every minute
  useEffect(() => {
    const updatePeriods = () => {
      try {
        if (!schedule || schedule.length === 0) return;

        const now = new Date();
        const currentDay = now.getDay();
        const daysMap = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
        const currentDayName = daysMap[currentDay];
        const currentTime = now.toLocaleTimeString('en-US', { hour12: false });

        // Find current period
        const current = schedule.find(item =>
          item.day === currentDayName &&
          item.startTime <= currentTime &&
          item.endTime >= currentTime
        );

        setCurrentPeriod(current);

        // Find next period
        // Get all periods for today that start after current time
        const todayUpcoming = schedule
          .filter(item =>
            item.day === currentDayName &&
            item.startTime > currentTime
          )
          .sort((a, b) => a.startTime.localeCompare(b.startTime));

        // Get the first upcoming period if available
        if (todayUpcoming.length > 0) {
          setNextPeriod(todayUpcoming[0]);

          // Calculate time remaining until next period
          const [nextHours, nextMinutes] = todayUpcoming[0].startTime.split(':').map(Number);
          const nextTime = new Date();
          nextTime.setHours(nextHours, nextMinutes, 0);

          const diffMs = nextTime - now;
          const diffMins = Math.floor(diffMs / 60000);

          setTimeRemaining(diffMins);

          // Send notification if next class is starting in 5 minutes and we haven't notified yet
          if (diffMins <= 5 && diffMins > 0 && !nextClassNotified) {
            sendNextClassNotification(todayUpcoming[0]);
            setNextClassNotified(true);
          } else if (diffMins > 5) {
            // Reset notification flag when we're more than 5 minutes away
            setNextClassNotified(false);
          }
        } else {
          // If no more periods today, find the first period of the next day
          const nextDayIndex = (currentDay + 1) % 7;
          const nextDayName = daysMap[nextDayIndex];

          const nextDayPeriods = schedule
            .filter(item => item.day === nextDayName)
            .sort((a, b) => a.startTime.localeCompare(b.startTime));

          if (nextDayPeriods.length > 0) {
            setNextPeriod({
              ...nextDayPeriods[0],
              isNextDay: true
            });
            setTimeRemaining(null); // Don't show countdown for next day
          } else {
            setNextPeriod(null);
            setTimeRemaining(null);
          }
        }
      } catch (error) {
        console.error('Error updating periods:', error);
      }
    };

    // Initial update
    updatePeriods();

    // Set up interval to update every minute
    timerRef.current = setInterval(updatePeriods, 60000);

    // Clean up interval on unmount
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [schedule, nextClassNotified]);

  // Send notification for upcoming class
  const sendNextClassNotification = async (periodData) => {
    if (!periodData) return;

    try {
      // Get current user ID
      const userId = auth.currentUser?.uid;
      if (!userId) {
        console.error('No authenticated user found');

        // Fallback to local notification if no user ID
        let title, body;

        switch (role) {
          case 'teacher':
            title = translate('notifications.upcomingClass') || 'Upcoming Class';
            body = `${translate('notifications.youHaveClass') || 'You have a class'} ${periodData.subjectName || periodData.subjectId} ${translate('notifications.withClass') || 'with'} ${periodData.className || periodData.classId} ${translate('notifications.inRoom') || 'in room'} ${periodData.roomNumber || 'N/A'} ${translate('notifications.in5Minutes') || 'in 5 minutes'}`;
            break;
          case 'student':
            title = translate('notifications.upcomingClass') || 'Upcoming Class';
            body = `${translate('notifications.youHaveClass') || 'You have a class'} ${periodData.subjectName || periodData.subjectId} ${translate('notifications.withTeacher') || 'with'} ${periodData.teacherName || periodData.teacherId} ${translate('notifications.inRoom') || 'in room'} ${periodData.roomNumber || 'N/A'} ${translate('notifications.in5Minutes') || 'in 5 minutes'}`;
            break;
          case 'parent':
            title = translate('notifications.childUpcomingClass') || 'Child\'s Upcoming Class';
            body = `${translate('notifications.yourChildHasClass') || 'Your child has a class'} ${periodData.subjectName || periodData.subjectId} ${translate('notifications.withTeacher') || 'with'} ${periodData.teacherName || periodData.teacherId} ${translate('notifications.inRoom') || 'in room'} ${periodData.roomNumber || 'N/A'} ${translate('notifications.in5Minutes') || 'in 5 minutes'}`;
            break;
          default:
            title = translate('notifications.upcomingClass') || 'Upcoming Class';
            body = `${translate('notifications.upcomingClassIn5Minutes') || 'Upcoming class in 5 minutes'}`;
        }

        NotificationService.scheduleLocalNotification(
          title,
          body,
          {
            type: 'class_schedule',
            periodId: periodData.id,
            role: role
          },
          1 // 1 second delay (almost immediate)
        );
        return;
      }

      // Use the NotificationService to send the notification
      await NotificationService.sendUpcomingClassNotification(userId, role, periodData);

      console.log('Upcoming class notification sent successfully');
    } catch (error) {
      console.error('Error sending upcoming class notification:', error);

      // Fallback to local notification if the service fails
      let title, body;

      switch (role) {
        case 'teacher':
          title = translate('notifications.upcomingClass') || 'Upcoming Class';
          body = `${translate('notifications.youHaveClass') || 'You have a class'} ${periodData.subjectName || periodData.subjectId} ${translate('notifications.withClass') || 'with'} ${periodData.className || periodData.classId} ${translate('notifications.inRoom') || 'in room'} ${periodData.roomNumber || 'N/A'} ${translate('notifications.in5Minutes') || 'in 5 minutes'}`;
          break;
        case 'student':
          title = translate('notifications.upcomingClass') || 'Upcoming Class';
          body = `${translate('notifications.youHaveClass') || 'You have a class'} ${periodData.subjectName || periodData.subjectId} ${translate('notifications.withTeacher') || 'with'} ${periodData.teacherName || periodData.teacherId} ${translate('notifications.inRoom') || 'in room'} ${periodData.roomNumber || 'N/A'} ${translate('notifications.in5Minutes') || 'in 5 minutes'}`;
          break;
        case 'parent':
          title = translate('notifications.childUpcomingClass') || 'Child\'s Upcoming Class';
          body = `${translate('notifications.yourChildHasClass') || 'Your child has a class'} ${periodData.subjectName || periodData.subjectId} ${translate('notifications.withTeacher') || 'with'} ${periodData.teacherName || periodData.teacherId} ${translate('notifications.inRoom') || 'in room'} ${periodData.roomNumber || 'N/A'} ${translate('notifications.in5Minutes') || 'in 5 minutes'}`;
          break;
        default:
          title = translate('notifications.upcomingClass') || 'Upcoming Class';
          body = `${translate('notifications.upcomingClassIn5Minutes') || 'Upcoming class in 5 minutes'}`;
      }

      // Send local notification as fallback
      NotificationService.scheduleLocalNotification(
        title,
        body,
        {
          type: 'class_schedule',
          periodId: periodData.id,
          role: role
        },
        1 // 1 second delay (almost immediate)
      );
    }
  };

  // Format time for display
  const formatTime = (time) => {
    if (!time) return '';
    const [hours, minutes] = time.split(':');
    const date = new Date();
    date.setHours(parseInt(hours), parseInt(minutes));
    return date.toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit' });
  };

  // Get subject name from the period data
  const getSubjectName = (periodData) => {
    if (!periodData) return 'Unknown Subject';

    // First try to use subjectName if available
    if (periodData.subjectName) {
      return periodData.subjectName;
    }

    // Fall back to subjectId
    return periodData.subjectId || 'Unknown Subject';
  };

  // Get teacher name from the period data
  const getTeacherName = (periodData) => {
    if (!periodData) return 'Unknown Teacher';

    // First try to use teacherName if available
    if (periodData.teacherName) {
      return periodData.teacherName;
    }

    // Fall back to teacherId
    return periodData.teacherId || 'Unknown Teacher';
  };

  // Get class name from the period data
  const getClassName = (periodData) => {
    if (!periodData) return 'Unknown Class';

    // First try to use className if available
    if (periodData.className) {
      return `${periodData.className} - ${periodData.sectionName || ''}`;
    }

    // Fall back to classId
    return `${periodData.classId || 'Unknown Class'} - ${periodData.sectionName || ''}`;
  };

  // Render free period state
  const renderFreePeriod = () => (
    <Surface style={styles.freePeriodContainer}>
      <IconButton icon="clock-outline" size={30} color="#9e9e9e" />
      <Text style={styles.freePeriodText}>
        {translate('schedule.noOngoingClass') || 'No ongoing class'}
      </Text>
      {nextPeriod && (
        <View style={styles.nextClassInfo}>
          <Text style={styles.nextClassText}>
            {nextPeriod.isNextDay
              ? `${translate('schedule.nextClassTomorrow') || 'Next class tomorrow'} ${formatTime(nextPeriod.startTime)}`
              : timeRemaining
                ? `${translate('schedule.nextClassIn') || 'Next class in'} ${timeRemaining} ${translate('schedule.minutes') || 'minutes'}`
                : `${translate('schedule.nextClass') || 'Next class'} ${formatTime(nextPeriod.startTime)}`
            }
          </Text>
          <Chip
            mode="outlined"
            style={styles.nextClassChip}
            onPress={onPressViewSchedule}
          >
            {translate('schedule.viewSchedule') || 'View Schedule'}
          </Chip>
        </View>
      )}
    </Surface>
  );

  // Render current period for teacher
  const renderTeacherCurrentPeriod = () => (
    <Surface style={styles.currentPeriodContainer}>
      <LinearGradient
        colors={['#E3F2FD', '#BBDEFB']}
        style={styles.gradientBackground}
      >
        <View style={styles.currentPeriodHeader}>
          <Animated.View style={{ transform: [{ scale: pulseAnim }] }}>
            <Badge style={styles.nowBadge}>
              {translate('schedule.now') || 'Now'}
            </Badge>
          </Animated.View>
          <Text style={styles.currentTimeText}>
            {formatTime(currentPeriod.startTime)} - {formatTime(currentPeriod.endTime)}
          </Text>
        </View>

        <Text style={styles.currentSubjectText}>
          {getSubjectName(currentPeriod)}
        </Text>

        <Text style={styles.currentClassText}>
          {getClassName(currentPeriod)}
        </Text>

        <Text style={styles.currentRoomText}>
          {translate('schedule.room') || 'Room'}: {currentPeriod.roomNumber || 'N/A'}
        </Text>

        <View style={styles.periodInfo}>
          <Chip
            mode="outlined"
            style={[styles.periodChip, { backgroundColor: '#E3F2FD' }]}
            icon={currentPeriod.session === 'morning' ? 'weather-sunny' : 'weather-night'}
          >
            {currentPeriod.session === 'morning' ?
              (translate('schedule.morningSession') || 'Morning Session') :
              (translate('schedule.afternoonSession') || 'Afternoon Session')}
          </Chip>

          <Chip
            mode="outlined"
            style={styles.periodChip}
          >
            {translate('schedule.period') || 'Period'} {currentPeriod.periodNumber || '?'}
          </Chip>
        </View>

        <View style={styles.actionsContainer}>
          <Chip
            mode="outlined"
            style={styles.actionChip}
            icon="calendar-clock"
            onPress={onPressViewSchedule}
          >
            {translate('schedule.viewSchedule') || 'View Schedule'}
          </Chip>
        </View>
      </LinearGradient>
    </Surface>
  );

  // Render current period for student
  const renderStudentCurrentPeriod = () => (
    <Surface style={styles.currentPeriodContainer}>
      <LinearGradient
        colors={['#E3F2FD', '#BBDEFB']}
        style={styles.gradientBackground}
      >
        <View style={styles.currentPeriodHeader}>
          <Animated.View style={{ transform: [{ scale: pulseAnim }] }}>
            <Badge style={styles.nowBadge}>
              {translate('schedule.now') || 'Now'}
            </Badge>
          </Animated.View>
          <Text style={styles.currentTimeText}>
            {formatTime(currentPeriod.startTime)} - {formatTime(currentPeriod.endTime)}
          </Text>
        </View>

        <Text style={styles.currentSubjectText}>
          {getSubjectName(currentPeriod)}
        </Text>

        <Text style={styles.currentTeacherText}>
          {translate('schedule.teacher') || 'Teacher'}: {getTeacherName(currentPeriod)}
        </Text>

        <Text style={styles.currentRoomText}>
          {translate('schedule.room') || 'Room'}: {currentPeriod.roomNumber || 'N/A'}
        </Text>

        <View style={styles.periodInfo}>
          <Chip
            mode="outlined"
            style={[styles.periodChip, { backgroundColor: '#E3F2FD' }]}
            icon={currentPeriod.session === 'morning' ? 'weather-sunny' : 'weather-night'}
          >
            {currentPeriod.session === 'morning' ?
              (translate('schedule.morningSession') || 'Morning Session') :
              (translate('schedule.afternoonSession') || 'Afternoon Session')}
          </Chip>

          <Chip
            mode="outlined"
            style={styles.periodChip}
          >
            {translate('schedule.period') || 'Period'} {currentPeriod.periodNumber || '?'}
          </Chip>
        </View>

        <View style={styles.actionsContainer}>
          <Chip
            mode="outlined"
            style={styles.actionChip}
            icon="calendar-clock"
            onPress={onPressViewSchedule}
          >
            {translate('schedule.viewSchedule') || 'View Schedule'}
          </Chip>
        </View>
      </LinearGradient>
    </Surface>
  );

  // Render current period for parent
  const renderParentCurrentPeriod = () => (
    <Surface style={styles.currentPeriodContainer}>
      <LinearGradient
        colors={['#E3F2FD', '#BBDEFB']}
        style={styles.gradientBackground}
      >
        <View style={styles.currentPeriodHeader}>
          <Animated.View style={{ transform: [{ scale: pulseAnim }] }}>
            <Badge style={styles.nowBadge}>
              {translate('schedule.now') || 'Now'}
            </Badge>
          </Animated.View>
          <Text style={styles.currentTimeText}>
            {formatTime(currentPeriod.startTime)} - {formatTime(currentPeriod.endTime)}
          </Text>
        </View>

        <Text style={styles.currentSubjectText}>
          {getSubjectName(currentPeriod)}
        </Text>

        <Text style={styles.currentTeacherText}>
          {translate('schedule.teacher') || 'Teacher'}: {getTeacherName(currentPeriod)}
        </Text>

        <Text style={styles.currentRoomText}>
          {translate('schedule.room') || 'Room'}: {currentPeriod.roomNumber || 'N/A'}
        </Text>

        <View style={styles.periodInfo}>
          <Chip
            mode="outlined"
            style={[styles.periodChip, { backgroundColor: '#E3F2FD' }]}
            icon={currentPeriod.session === 'morning' ? 'weather-sunny' : 'weather-night'}
          >
            {currentPeriod.session === 'morning' ?
              (translate('schedule.morningSession') || 'Morning Session') :
              (translate('schedule.afternoonSession') || 'Afternoon Session')}
          </Chip>

          <Chip
            mode="outlined"
            style={styles.periodChip}
          >
            {translate('schedule.period') || 'Period'} {currentPeriod.periodNumber || '?'}
          </Chip>
        </View>

        <View style={styles.actionsContainer}>
          <Chip
            mode="outlined"
            style={styles.actionChip}
            icon="calendar-clock"
            onPress={onPressViewSchedule}
          >
            {translate('schedule.viewChildSchedule') || 'View Child\'s Schedule'}
          </Chip>
        </View>
      </LinearGradient>
    </Surface>
  );

  // Render next period
  const renderNextPeriod = () => (
    <Surface style={styles.nextPeriodContainer}>
      <View style={styles.nextPeriodHeader}>
        <Badge style={styles.upNextBadge}>
          {nextPeriod.isNextDay
            ? (translate('schedule.tomorrow') || 'Tomorrow')
            : (translate('schedule.upNext') || 'Up Next')}
        </Badge>
        <Text style={styles.nextTimeText}>
          {formatTime(nextPeriod.startTime)} - {formatTime(nextPeriod.endTime)}
        </Text>
      </View>

      <Text style={styles.nextSubjectText}>
        {getSubjectName(nextPeriod)}
      </Text>

      {role === 'teacher' ? (
        <Text style={styles.nextClassText}>
          {getClassName(nextPeriod)}
        </Text>
      ) : (
        <Text style={styles.nextTeacherText}>
          {translate('schedule.teacher') || 'Teacher'}: {getTeacherName(nextPeriod)}
        </Text>
      )}

      <Text style={styles.nextRoomText}>
        {translate('schedule.room') || 'Room'}: {nextPeriod.roomNumber || 'N/A'}
      </Text>

      <View style={styles.periodInfo}>
        <Chip
          mode="outlined"
          style={[styles.periodChip, { backgroundColor: '#F5F5F5' }]}
          icon={nextPeriod.session === 'morning' ? 'weather-sunny' : 'weather-night'}
        >
          {nextPeriod.session === 'morning' ?
            (translate('schedule.morningSession') || 'Morning Session') :
            (translate('schedule.afternoonSession') || 'Afternoon Session')}
        </Chip>

        <Chip
          mode="outlined"
          style={styles.periodChip}
        >
          {translate('schedule.period') || 'Period'} {nextPeriod.periodNumber || '?'}
        </Chip>
      </View>

      {!nextPeriod.isNextDay && timeRemaining !== null && (
        <View style={styles.countdownContainer}>
          <Text style={styles.countdownText}>
            {translate('schedule.starting') || 'Starting in'}: {timeRemaining} {translate('schedule.minutes') || 'minutes'}
          </Text>
          {timeRemaining <= 10 && (
            <ProgressBar
              progress={1 - (timeRemaining / 10)}
              color="#2196F3"
              style={styles.countdownProgress}
            />
          )}
        </View>
      )}
    </Surface>
  );

  // Main render
  return (
    <Animatable.View
      animation="fadeIn"
      duration={800}
      style={[styles.container, style]}
    >
      {!currentPeriod ? (
        renderFreePeriod()
      ) : role === 'teacher' ? (
        renderTeacherCurrentPeriod()
      ) : role === 'student' ? (
        renderStudentCurrentPeriod()
      ) : (
        renderParentCurrentPeriod()
      )}

      {nextPeriod && (
        <Animatable.View animation="fadeInUp" duration={800} delay={300}>
          {renderNextPeriod()}
        </Animatable.View>
      )}
    </Animatable.View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  freePeriodContainer: {
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 2,
    minHeight: 120,
  },
  freePeriodText: {
    fontSize: 16,
    color: '#757575',
    marginTop: 8,
  },
  nextClassInfo: {
    marginTop: 16,
    alignItems: 'center',
  },
  nextClassText: {
    fontSize: 14,
    color: '#616161',
    marginBottom: 8,
  },
  nextClassChip: {
    marginTop: 8,
  },
  currentPeriodContainer: {
    borderRadius: 8,
    overflow: 'hidden',
    elevation: 3,
    marginBottom: 16,
  },
  nextPeriodContainer: {
    padding: 16,
    borderRadius: 8,
    backgroundColor: '#FFFFFF',
    elevation: 2,
  },
  gradientBackground: {
    padding: 16,
    borderRadius: 8,
  },
  currentPeriodHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  nextPeriodHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  nowBadge: {
    backgroundColor: '#4CAF50',
    color: 'white',
    marginRight: 8,
  },
  upNextBadge: {
    backgroundColor: '#FF9800',
    color: 'white',
    marginRight: 8,
  },
  currentTimeText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  nextTimeText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  currentSubjectText: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
    color: '#333',
  },
  nextSubjectText: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
    color: '#333',
  },
  currentTeacherText: {
    fontSize: 14,
    marginBottom: 4,
    color: '#555',
  },
  nextTeacherText: {
    fontSize: 14,
    marginBottom: 4,
    color: '#555',
  },
  currentClassText: {
    fontSize: 14,
    marginBottom: 4,
    color: '#555',
  },
  nextClassText: {
    fontSize: 14,
    marginBottom: 4,
    color: '#555',
  },
  currentRoomText: {
    fontSize: 14,
    color: '#555',
    marginBottom: 12,
  },
  nextRoomText: {
    fontSize: 14,
    color: '#555',
    marginBottom: 12,
  },
  periodInfo: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 8,
  },
  periodChip: {
    marginRight: 8,
    marginBottom: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.7)',
  },
  countdownContainer: {
    marginTop: 8,
    padding: 8,
    backgroundColor: '#F5F5F5',
    borderRadius: 4,
  },
  countdownText: {
    fontSize: 14,
    color: '#333',
    marginBottom: 4,
    textAlign: 'center',
  },
  countdownProgress: {
    height: 6,
    borderRadius: 3,
  },
  actionsContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 8,
  },
  actionChip: {
    backgroundColor: 'rgba(255, 255, 255, 0.7)',
  },
});

export default OngoingClassTracker;
