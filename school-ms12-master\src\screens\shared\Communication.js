import React, { useState, useEffect } from 'react';
import { View, ScrollView, StyleSheet } from 'react-native';
import { Card, Title, Text, Portal, Modal, TextInput, ActivityIndicator, Chip, IconButton, Avatar, useTheme } from 'react-native-paper';
import { db, auth, storage } from '../../config/firebase';
import { collection, query, where, getDocs, addDoc, doc, getDoc, updateDoc, orderBy, limit } from 'firebase/firestore';
import { ref, uploadBytes, getDownloadURL } from 'firebase/storage';
import CustomButton from '../../components/common/CustomButton';
import * as DocumentPicker from 'expo-document-picker';
import EthiopianCalendar from '../../utils/EthiopianCalendar';

const Communication = ({ isTeacher = false }) => {
  const [messages, setMessages] = useState([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [loading, setLoading] = useState(true);
  const [students, setStudents] = useState([]);
  const [selectedStudent, setSelectedStudent] = useState(null);
  const [messageForm, setMessageForm] = useState({
    title: '',
    content: '',
    attachments: [],
    priority: 'normal', // normal, important, urgent
    category: 'general', // general, academic, behavioral, attendance
    status: 'unread',
  });
  // No theme needed

  useEffect(() => {
    if (isTeacher) {
      fetchStudents();
    }
    fetchMessages();
  }, [isTeacher]);

  const fetchStudents = async () => {
    try {
      const userDoc = await getDoc(doc(db, 'users', auth.currentUser.uid));
      const assignedClasses = userDoc.data().assignedClasses || [];
      
      const studentsRef = collection(db, 'users');
      const studentsData = [];
      
      for (const classId of assignedClasses) {
        const q = query(studentsRef, where('role', '==', 'student'), where('classId', '==', classId));
        const querySnapshot = await getDocs(q);
        querySnapshot.forEach((doc) => {
          studentsData.push({ id: doc.id, ...doc.data() });
        });
      }
      
      setStudents(studentsData.sort((a, b) => a.displayName.localeCompare(b.displayName)));
    } catch (error) {
      console.error('Error fetching students:', error);
    }
  };

  const fetchMessages = async () => {
    try {
      setLoading(true);
      
      const messagesRef = collection(db, 'messages');
      let q;
      
      if (isTeacher) {
        q = query(
          messagesRef,
          where('senderId', '==', auth.currentUser.uid),
          orderBy('timestamp', 'desc'),
          limit(50)
        );
      } else {
        q = query(
          messagesRef,
          where('recipientId', '==', auth.currentUser.uid),
          orderBy('timestamp', 'desc'),
          limit(50)
        );
      }
      
      const querySnapshot = await getDocs(q);
      const messageData = [];
      
      for (const doc of querySnapshot.docs) {
        const message = { id: doc.id, ...doc.data() };
        
        // Fetch sender and recipient details
        const [senderDoc, recipientDoc] = await Promise.all([
          getDoc(doc(db, 'users', message.senderId)),
          getDoc(doc(db, 'users', message.recipientId))
        ]);
        
        message.sender = senderDoc.data();
        message.recipient = recipientDoc.data();
        messageData.push(message);
      }
      
      setMessages(messageData);
      setLoading(false);
    } catch (error) {
      console.error('Error fetching messages:', error);
      setLoading(false);
    }
  };

  const handleSendMessage = async () => {
    try {
      setLoading(true);
      
      // Upload attachments
      const attachmentUrls = await Promise.all(
        messageForm.attachments.map(async (file) => {
          const response = await fetch(file.uri);
          const blob = await response.blob();
          const storageRef = ref(storage, `messages/${auth.currentUser.uid}/${Date.now()}_${file.name}`);
          await uploadBytes(storageRef, blob);
          return await getDownloadURL(storageRef);
        })
      );

      const newMessage = {
        ...messageForm,
        attachments: attachmentUrls,
        senderId: auth.currentUser.uid,
        recipientId: selectedStudent.id,
        timestamp: new Date().toISOString(),
        status: 'unread',
      };

      await addDoc(collection(db, 'messages'), newMessage);
      
      // Update local state
      const senderDoc = await getDoc(doc(db, 'users', auth.currentUser.uid));
      newMessage.sender = senderDoc.data();
      newMessage.recipient = selectedStudent;
      
      setMessages([newMessage, ...messages]);
      setModalVisible(false);
      resetForm();
    } catch (error) {
      console.error('Error sending message:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleFileSelect = async () => {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: '*/*',
        copyToCacheDirectory: false,
      });

      if (result.type === 'success') {
        setMessageForm({
          ...messageForm,
          attachments: [...messageForm.attachments, result],
        });
      }
    } catch (error) {
      console.error('Error selecting file:', error);
    }
  };

  const resetForm = () => {
    setMessageForm({
      title: '',
      content: '',
      attachments: [],
      priority: 'normal',
      category: 'general',
      status: 'unread',
    });
    setSelectedStudent(null);
  };

  const formatDate = (date) => {
    return EthiopianCalendar.formatDate(new Date(date));
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'urgent': return '#f44336';
      case 'important': return '#FF9800';
      default: return '#2196F3';
    }
  };

  const getCategoryIcon = (category) => {
    switch (category) {
      case 'academic': return 'school';
      case 'behavioral': return 'account-alert';
      case 'attendance': return 'calendar-check';
      default: return 'information';
    }
  };

  const renderMessage = (message) => (
    <Card 
      key={message.id}
      style={[styles.messageCard, { borderLeftColor: getPriorityColor(message.priority) }]}
    >
      <Card.Content>
        <View style={styles.messageHeader}>
          <View style={styles.senderInfo}>
            <Avatar.Text 
              size={40}
              label={message.sender.displayName.split(' ').map(n => n[0]).join('')}
            />
            <View style={styles.senderDetails}>
              <Title style={styles.senderName}>{message.sender.displayName}</Title>
              <Text style={styles.timestamp}>{formatDate(message.timestamp)}</Text>
            </View>
          </View>
          <IconButton
            icon={getCategoryIcon(message.category)}
            size={20}
            color={'#1976d2'}
          />
        </View>

        <Title style={styles.messageTitle}>{message.title}</Title>
        <Text style={styles.messageContent}>{message.content}</Text>

        {message.attachments?.length > 0 && (
          <View style={styles.attachments}>
            <Text style={styles.attachmentTitle}>Attachments:</Text>
            <View style={styles.chipContainer}>
              {message.attachments.map((url, index) => (
                <Chip
                  key={index}
                  icon="file"
                  onPress={() => {/* Handle file open */}}
                  style={styles.attachmentChip}
                >
                  Attachment {index + 1}
                </Chip>
              ))}
            </View>
          </View>
        )}

        <View style={styles.recipientInfo}>
          <Text style={styles.recipientLabel}>
            {isTeacher ? 'To: ' : 'From: '}
          </Text>
          <Chip icon="account">
            {isTeacher ? message.recipient.displayName : message.sender.displayName}
          </Chip>
        </View>
      </Card.Content>
    </Card>
  );

  return (
    <View style={styles.container}>
      {loading ? (
        <ActivityIndicator style={styles.loader} />
      ) : (
        <>
          {isTeacher && (
            <CustomButton
              mode="contained"
              onPress={() => setModalVisible(true)}
              style={styles.sendButton}
            >
              Send Message
            </CustomButton>
          )}

          <ScrollView>
            {messages.length > 0 ? (
              messages.map(renderMessage)
            ) : (
              <Text style={styles.emptyText}>No messages found</Text>
            )}
          </ScrollView>

          <Portal>
            <Modal
              visible={modalVisible}
              onDismiss={() => {
                setModalVisible(false);
                resetForm();
              }}
              contentContainerStyle={styles.modalContent}
            >
              <ScrollView>
                <Title>Send Message</Title>

                <View style={styles.studentSelect}>
                  <Text style={styles.label}>Select Student:</Text>
                  <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                    {students.map((student) => (
                      <Chip
                        key={student.id}
                        selected={selectedStudent?.id === student.id}
                        onPress={() => setSelectedStudent(student)}
                        style={styles.studentChip}
                      >
                        {student.displayName}
                      </Chip>
                    ))}
                  </ScrollView>
                </View>

                <TextInput
                  label="Title"
                  value={messageForm.title}
                  onChangeText={(text) => setMessageForm({ ...messageForm, title: text })}
                  style={styles.input}
                />

                <TextInput
                  label="Message"
                  value={messageForm.content}
                  onChangeText={(text) => setMessageForm({ ...messageForm, content: text })}
                  multiline
                  numberOfLines={4}
                  style={styles.input}
                />

                <View style={styles.chipRow}>
                  <Text style={styles.label}>Priority:</Text>
                  {['normal', 'important', 'urgent'].map((priority) => (
                    <Chip
                      key={priority}
                      selected={messageForm.priority === priority}
                      onPress={() => setMessageForm({ ...messageForm, priority })}
                      style={[
                        styles.priorityChip,
                        { backgroundColor: getPriorityColor(priority) }
                      ]}
                    >
                      {priority.charAt(0).toUpperCase() + priority.slice(1)}
                    </Chip>
                  ))}
                </View>

                <View style={styles.chipRow}>
                  <Text style={styles.label}>Category:</Text>
                  {['general', 'academic', 'behavioral', 'attendance'].map((category) => (
                    <Chip
                      key={category}
                      selected={messageForm.category === category}
                      onPress={() => setMessageForm({ ...messageForm, category })}
                      icon={getCategoryIcon(category)}
                      style={styles.categoryChip}
                    >
                      {category.charAt(0).toUpperCase() + category.slice(1)}
                    </Chip>
                  ))}
                </View>

                <View style={styles.attachmentSection}>
                  <Text style={styles.label}>Attachments:</Text>
                  <CustomButton
                    mode="outlined"
                    onPress={handleFileSelect}
                    style={styles.attachButton}
                  >
                    Add Attachment
                  </CustomButton>
                  {messageForm.attachments.map((file, index) => (
                    <Chip
                      key={index}
                      onClose={() => {
                        const newAttachments = [...messageForm.attachments];
                        newAttachments.splice(index, 1);
                        setMessageForm({ ...messageForm, attachments: newAttachments });
                      }}
                      style={styles.attachmentChip}
                    >
                      {file.name}
                    </Chip>
                  ))}
                </View>

                <View style={styles.buttonContainer}>
                  <CustomButton
                    mode="outlined"
                    onPress={() => {
                      setModalVisible(false);
                      resetForm();
                    }}
                    style={[styles.button, styles.cancelButton]}
                  >
                    Cancel
                  </CustomButton>
                  <CustomButton
                    mode="contained"
                    onPress={handleSendMessage}
                    style={[styles.button, styles.sendButton]}
                    loading={loading}
                    disabled={!selectedStudent}
                  >
                    Send
                  </CustomButton>
                </View>
              </ScrollView>
            </Modal>
          </Portal>
        </>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  sendButton: {
    margin: 16,
  },
  messageCard: {
    margin: 16,
    borderLeftWidth: 4,
  },
  messageHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  senderInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  senderDetails: {
    marginLeft: 12,
  },
  senderName: {
    fontSize: 16,
  },
  timestamp: {
    fontSize: 12,
    color: '#666',
  },
  messageTitle: {
    fontSize: 18,
    marginBottom: 8,
  },
  messageContent: {
    fontSize: 14,
    color: '#444',
    marginBottom: 12,
  },
  attachments: {
    marginTop: 12,
  },
  attachmentTitle: {
    fontSize: 14,
    marginBottom: 8,
  },
  chipContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  attachmentChip: {
    marginVertical: 4,
  },
  recipientInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 12,
  },
  recipientLabel: {
    marginRight: 8,
    color: '#666',
  },
  modalContent: {
    backgroundColor: 'white',
    padding: 20,
    margin: 20,
    borderRadius: 8,
    maxHeight: '80%',
  },
  studentSelect: {
    marginBottom: 16,
  },
  studentChip: {
    marginRight: 8,
  },
  input: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  chipRow: {
    marginBottom: 16,
  },
  priorityChip: {
    marginRight: 8,
  },
  categoryChip: {
    marginRight: 8,
  },
  attachmentSection: {
    marginBottom: 16,
  },
  attachButton: {
    marginBottom: 8,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: 8,
  },
  button: {
    minWidth: 100,
  },
  cancelButton: {
    borderColor: '#666',
  },
  emptyText: {
    textAlign: 'center',
    marginTop: 20,
    color: '#666',
    fontStyle: 'italic',
  },
  loader: {
    marginVertical: 20,
  },
});

export default Communication;
