import { db, auth } from '../config/firebase';
import {
  collection,
  addDoc,
  query,
  where,
  getDocs,
  updateDoc,
  doc,
  orderBy,
  limit,
  onSnapshot,
  serverTimestamp,
  getDoc,
  arrayUnion,
  arrayRemove
} from '@firebase/firestore';
import NotificationService from '../services/NotificationService';

class MessagingService {
  static chatListeners = new Map();

  static async createChat(participants, type = 'private') {
    try {
      const chatRef = await addDoc(collection(db, 'chats'), {
        participants,
        type, // private, group, announcement
        createdAt: serverTimestamp(),
        lastMessage: null,
        lastMessageAt: null,
        unreadCount: {},
      });

      // Initialize unread counts for all participants
      const unreadCounts = {};
      participants.forEach(userId => {
        unreadCounts[userId] = 0;
      });

      await updateDoc(chatRef, { unreadCount: unreadCounts });

      return chatRef.id;
    } catch (error) {
      console.error('Error creating chat:', error);
      throw error;
    }
  }

  static async sendMessage(chatId, content, type = 'text') {
    try {
      const sender = auth.currentUser;
      if (!sender) throw new Error('User not authenticated');

      const chatRef = doc(db, 'chats', chatId);
      const chatDoc = await getDoc(chatRef);

      if (!chatDoc.exists()) throw new Error('Chat not found');

      const chatData = chatDoc.data();
      const messageData = {
        chatId,
        senderId: sender.uid,
        content,
        type, // text, image, file, system
        timestamp: serverTimestamp(),
        readBy: [sender.uid],
        reactions: {},
        attachments: [],
      };

      // Add message to messages collection
      const messageRef = await addDoc(collection(db, 'messages'), messageData);

      // Update chat's last message
      const updateData = {
        lastMessage: content,
        lastMessageAt: serverTimestamp(),
      };

      // Update unread counts for all participants except sender
      const unreadCount = { ...chatData.unreadCount };
      chatData.participants.forEach(userId => {
        if (userId !== sender.uid) {
          unreadCount[userId] = (unreadCount[userId] || 0) + 1;
        }
      });
      updateData.unreadCount = unreadCount;

      await updateDoc(chatRef, updateData);

      // Send notifications to other participants
      const otherParticipants = chatData.participants.filter(id => id !== sender.uid);
      if (otherParticipants.length > 0) {
        const senderDoc = await getDoc(doc(db, 'users', sender.uid));
        const senderName = senderDoc.data().displayName || 'Someone';

        otherParticipants.forEach(async (userId) => {
          await NotificationService.sendNotification(userId, {
            title: 'New Message',
            body: `${senderName}: ${content.length > 50 ? content.substring(0, 47) + '...' : content}`,
            data: {
              type: 'message',
              chatId,
              messageId: messageRef.id,
            },
          });
        });
      }

      return messageRef.id;
    } catch (error) {
      console.error('Error sending message:', error);
      throw error;
    }
  }

  static subscribeToChat(chatId, callback) {
    try {
      // Unsubscribe from existing listener if any
      this.unsubscribeFromChat(chatId);

      // Subscribe to messages
      const messagesQuery = query(
        collection(db, 'messages'),
        where('chatId', '==', chatId),
        orderBy('timestamp', 'desc'),
        limit(50)
      );

      const unsubscribe = onSnapshot(messagesQuery, async (snapshot) => {
        const messages = [];
        for (const change of snapshot.docChanges()) {
          const messageData = { id: change.doc.id, ...change.doc.data() };

          // Convert server timestamp to Date
          if (messageData.timestamp) {
            messageData.timestamp = messageData.timestamp.toDate();
          }

          if (change.type === 'added') {
            messages.push(messageData);
          }
        }

        callback(messages.reverse());
      });

      this.chatListeners.set(chatId, unsubscribe);
      return unsubscribe;
    } catch (error) {
      console.error('Error subscribing to chat:', error);
      throw error;
    }
  }

  static unsubscribeFromChat(chatId) {
    const unsubscribe = this.chatListeners.get(chatId);
    if (unsubscribe) {
      unsubscribe();
      this.chatListeners.delete(chatId);
    }
  }

  static async markMessageAsRead(messageId) {
    try {
      const messageRef = doc(db, 'messages', messageId);
      const messageDoc = await getDoc(messageRef);

      if (!messageDoc.exists()) throw new Error('Message not found');

      const messageData = messageDoc.data();
      const currentUser = auth.currentUser;

      if (!messageData.readBy.includes(currentUser.uid)) {
        await updateDoc(messageRef, {
          readBy: arrayUnion(currentUser.uid),
        });

        // Update unread count in chat
        const chatRef = doc(db, 'chats', messageData.chatId);
        const chatDoc = await getDoc(chatRef);
        const chatData = chatDoc.data();

        if (chatData.unreadCount[currentUser.uid] > 0) {
          const unreadCount = { ...chatData.unreadCount };
          unreadCount[currentUser.uid]--;
          await updateDoc(chatRef, { unreadCount });
        }
      }
    } catch (error) {
      console.error('Error marking message as read:', error);
      throw error;
    }
  }

  static async getUserChats() {
    try {
      const currentUser = auth.currentUser;
      if (!currentUser) throw new Error('User not authenticated');

      const chatsQuery = query(
        collection(db, 'chats'),
        where('participants', 'array-contains', currentUser.uid),
        orderBy('lastMessageAt', 'desc')
      );

      const snapshot = await getDocs(chatsQuery);
      const chats = [];

      for (const doc of snapshot.docs) {
        const chatData = { id: doc.id, ...doc.data() };

        // Get other participants' details
        const otherParticipants = chatData.participants.filter(
          id => id !== currentUser.uid
        );

        const participantDetails = await Promise.all(
          otherParticipants.map(async (userId) => {
            const userDoc = await getDoc(doc(db, 'users', userId));
            return userDoc.data();
          })
        );

        chatData.participantDetails = participantDetails;
        chats.push(chatData);
      }

      return chats;
    } catch (error) {
      console.error('Error getting user chats:', error);
      throw error;
    }
  }

  static async addReaction(messageId, reaction) {
    try {
      const messageRef = doc(db, 'messages', messageId);
      const messageDoc = await getDoc(messageRef);

      if (!messageDoc.exists()) throw new Error('Message not found');

      const currentUser = auth.currentUser;
      const reactions = messageDoc.data().reactions || {};

      // Remove existing reaction from this user if any
      Object.keys(reactions).forEach(emoji => {
        if (reactions[emoji].includes(currentUser.uid)) {
          reactions[emoji] = reactions[emoji].filter(id => id !== currentUser.uid);
          if (reactions[emoji].length === 0) {
            delete reactions[emoji];
          }
        }
      });

      // Add new reaction
      if (!reactions[reaction]) {
        reactions[reaction] = [];
      }
      reactions[reaction].push(currentUser.uid);

      await updateDoc(messageRef, { reactions });
    } catch (error) {
      console.error('Error adding reaction:', error);
      throw error;
    }
  }

  static async deleteMessage(messageId) {
    try {
      const messageRef = doc(db, 'messages', messageId);
      const messageDoc = await getDoc(messageRef);

      if (!messageDoc.exists()) throw new Error('Message not found');

      const messageData = messageDoc.data();
      const currentUser = auth.currentUser;

      // Only allow deletion by message sender
      if (messageData.senderId !== currentUser.uid) {
        throw new Error('Unauthorized to delete this message');
      }

      await updateDoc(messageRef, {
        content: 'This message was deleted',
        type: 'deleted',
        attachments: [],
        reactions: {},
      });
    } catch (error) {
      console.error('Error deleting message:', error);
      throw error;
    }
  }
}

export default MessagingService;
