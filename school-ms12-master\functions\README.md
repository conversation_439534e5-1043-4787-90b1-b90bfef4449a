# Email Sending Cloud Functions

This directory contains Firebase Cloud Functions for sending emails and handling email verification.

## Setup Instructions

1. Install Firebase CLI:
   ```
   npm install -g firebase-tools
   ```

2. Initialize Firebase Functions in this directory:
   ```
   firebase init functions
   ```

3. Install required dependencies:
   ```
   cd functions
   npm install nodemailer
   ```

4. Update the email configuration in `index.js`:
   - Replace `'<EMAIL>'` with your actual email
   - Replace `'your-app-password'` with your app password (for Gmail, you need to generate an app password)

5. Deploy the functions:
   ```
   firebase deploy --only functions
   ```

## Using Gmail for Sending Emails

If you're using Gmail, you need to:

1. Enable 2-Step Verification for your Google account
2. Generate an App Password:
   - Go to your Google Account settings
   - Select Security
   - Under "Signing in to Google," select App Passwords
   - Generate a new app password for "Mail" and "Other (Custom name)"
   - Use this password in the Cloud Function configuration

## Alternative Email Services

You can also use other email services like Send<PERSON>rid, Mailgun, etc. Update the transporter configuration accordingly.

For SendGrid example:
```javascript
const transporter = nodemailer.createTransport({
  host: 'smtp.sendgrid.net',
  port: 587,
  auth: {
    user: 'apikey',
    pass: 'YOUR_SENDGRID_API_KEY'
  }
});
```

## Testing

To test the email sending functionality:
1. Add a document to the `emails` collection with the following fields:
   - `to`: Recipient email address
   - `subject`: Email subject
   - `html`: Email content in HTML format
   - `from`: (Optional) Sender email address

2. The Cloud Function will automatically trigger and send the email.

## Troubleshooting

If emails are not being sent:
1. Check the Firebase Functions logs for errors
2. Verify your email service credentials
3. Make sure your email service allows sending from your application
4. Check if your email service has sending limits or restrictions
