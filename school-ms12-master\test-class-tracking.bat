@echo off
echo === Test Class Tracking Tool ===
echo This batch file will help you test the class tracking features.
echo.

REM Get the current directory with quotes to handle spaces
set "CURRENT_DIR=%~dp0"
echo Current directory: %CURRENT_DIR%

REM Ask for user role
set /p ROLE="Which user role do you want to test? (teacher/student/parent): "

REM Run the test script with node
echo Running test script for %ROLE% role...
node "%CURRENT_DIR%test-class-tracking.js" %ROLE%

echo.
echo Testing process completed.
echo Press any key to exit...
pause > nul
