import { db } from '../config/firebase';
import { collection, query, where, getDocs, writeBatch, doc } from 'firebase/firestore';

export const assignRollNumbers = async (classId, sectionName) => {
  try {
    // Get all students in the section
    const studentsRef = collection(db, 'users');
    const q = query(
      studentsRef,
      where('role', '==', 'student'),
      where('classId', '==', classId),
      where('sectionName', '==', sectionName)
    );

    const querySnapshot = await getDocs(q);
    const students = querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));

    // Sort students by first name and last name
    const sortedStudents = students.sort((a, b) => {
      const nameA = `${a.firstName} ${a.lastName}`.toLowerCase();
      const nameB = `${b.firstName} ${b.lastName}`.toLowerCase();
      return nameA.localeCompare(nameB);
    });

    // Assign roll numbers using batch write
    const batch = writeBatch(db);

    sortedStudents.forEach((student, index) => {
      const rollNumber = (index + 1).toString().padStart(2, '0'); // 01, 02, etc.
      const studentRef = doc(db, 'users', student.id);
      batch.update(studentRef, { rollNumber });
    });

    await batch.commit();

    return sortedStudents.length;
  } catch (error) {
    console.error('Error assigning roll numbers:', error);
    throw error;
  }
};
