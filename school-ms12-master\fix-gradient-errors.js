const fs = require('fs');
const path = require('path');

// Function to recursively find all JS files in a directory
function findJsFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);

  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);

    if (stat.isDirectory() && file !== 'node_modules' && file !== '.git') {
      findJsFiles(filePath, fileList);
    } else if (stat.isFile() && (file.endsWith('.js') || file.endsWith('.jsx'))) {
      fileList.push(filePath);
    }
  });

  return fileList;
}

// Function to fix gradient color errors in a file
function fixGradientErrors(filePath) {
  console.log(`Checking ${filePath}...`);
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;

  // Fix incorrect gradient color syntax
  const gradientRegex = /colors={\[['"]#[0-9a-fA-F]+['"],\s*['"]#[0-9a-fA-F]+'[^}]*Dark\s*\|\|\s*['"]#[0-9a-fA-F]+['"]\]}/g;
  if (content.match(gradientRegex)) {
    console.log(`Found potential gradient error in ${filePath}`);
    
    // Replace with fixed version
    content = content.replace(gradientRegex, (match) => {
      console.log(`  Original: ${match}`);
      // Extract the first color and the fallback color
      const firstColorMatch = match.match(/['"]#[0-9a-fA-F]+['"]/);
      const fallbackColorMatch = match.match(/\|\|\s*(['"]#[0-9a-fA-F]+['"])/);
      
      if (firstColorMatch && fallbackColorMatch) {
        const firstColor = firstColorMatch[0];
        const fallbackColor = fallbackColorMatch[1];
        const replacement = `colors={[${firstColor}, ${fallbackColor}]}`;
        console.log(`  Fixed: ${replacement}`);
        return replacement;
      }
      
      return match;
    });
    
    modified = true;
  }

  // Save the file if modified
  if (modified) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`Fixed gradient errors in ${filePath}`);
    return true;
  }

  return false;
}

// Main function
function main() {
  const srcDir = path.join(__dirname, 'src');
  const jsFiles = findJsFiles(srcDir);
  let fixedCount = 0;

  jsFiles.forEach(file => {
    if (fixGradientErrors(file)) {
      fixedCount++;
    }
  });

  console.log(`\nFixed gradient errors in ${fixedCount} files.`);
}

main();
