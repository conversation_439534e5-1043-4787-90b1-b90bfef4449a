const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Get the current directory
const currentDir = __dirname;
console.log(`Current directory: ${currentDir}`);

// Function to run a script
const runScript = (scriptPath) => {
  console.log(`Running script: ${scriptPath}`);
  try {
    // Use path.resolve to get the absolute path
    const fullPath = path.resolve(currentDir, scriptPath);
    console.log(`Full path: ${fullPath}`);
    execSync(`node "${fullPath}"`, { stdio: 'inherit' });
    console.log(`Successfully executed: ${scriptPath}`);
    return true;
  } catch (error) {
    console.error(`Failed to execute ${scriptPath}:`, error.message);
    return false;
  }
};

// Check if scripts exist
const updateTimetablesPath = path.join(__dirname, 'src', 'scripts', 'updateTimetables.js');
const generateSampleTimetablesPath = path.join(__dirname, 'src', 'scripts', 'generateSampleTimetables.js');

const updateTimetablesExists = fs.existsSync(updateTimetablesPath);
const generateSampleTimetablesExists = fs.existsSync(generateSampleTimetablesPath);

if (!updateTimetablesExists) {
  console.error(`Error: ${updateTimetablesPath} does not exist.`);
}

if (!generateSampleTimetablesExists) {
  console.error(`Error: ${generateSampleTimetablesPath} does not exist.`);
}

if (!updateTimetablesExists || !generateSampleTimetablesExists) {
  console.error('Please ensure both scripts exist in the correct location.');
  process.exit(1);
}

// Main function
const main = async () => {
  console.log('=== Timetable Data Update Tool ===');
  console.log('This tool will help you update and generate timetable data.');
  console.log('');

  // Ask if user wants to update existing timetables
  rl.question('Do you want to update existing timetable entries? (y/n): ', (updateAnswer) => {
    if (updateAnswer.toLowerCase() === 'y') {
      const updateSuccess = runScript(updateTimetablesPath);
      if (!updateSuccess) {
        console.error('Failed to update existing timetable entries.');
      }
    }

    // Ask if user wants to generate sample timetables
    rl.question('Do you want to generate sample timetable data? (y/n): ', (generateAnswer) => {
      if (generateAnswer.toLowerCase() === 'y') {
        const generateSuccess = runScript(generateSampleTimetablesPath);
        if (!generateSuccess) {
          console.error('Failed to generate sample timetable data.');
        }
      }

      console.log('');
      console.log('Timetable data update process completed.');
      rl.close();
    });
  });
};

// Run the main function
main().catch(error => {
  console.error('An error occurred:', error);
  process.exit(1);
});
