import React, { useState } from 'react';
import { View, StyleSheet, TouchableOpacity, Text, ActivityIndicator, Image, Alert } from 'react-native';
import * as ImagePicker from 'expo-image-picker';
import { MaterialIcons } from '@expo/vector-icons';
import CloudinaryService from '../../services/CloudinaryService';
import CloudinaryImage from './CloudinaryImage';
import { useLanguage } from '../../context/LanguageContext';

/**
 * CloudinaryUploader component for uploading images to Cloudinary
 * 
 * @param {Object} props - Component props
 * @param {string} props.value - Current image source (Cloudinary public ID or Firebase URL)
 * @param {Function} props.onChange - Callback function to call when the image is uploaded or removed
 * @param {string} props.folder - Cloudinary folder to upload to
 * @param {Array<string>} props.tags - Tags to assign to the uploaded image
 * @param {string} props.placeholder - Placeholder text to display when no image is selected
 * @param {Object} props.style - Style object for the component
 * @param {number} props.width - Width of the component
 * @param {number} props.height - Height of the component
 * @param {boolean} props.circle - Whether to display the image as a circle
 * @param {number} props.borderRadius - Border radius for the component
 * @param {boolean} props.disabled - Whether the component is disabled
 */
const CloudinaryUploader = ({
  value,
  onChange,
  folder = 'school_app',
  tags = [],
  placeholder = 'Upload Image',
  style = {},
  width = 150,
  height = 150,
  circle = false,
  borderRadius = 8,
  disabled = false,
}) => {
  const [loading, setLoading] = useState(false);
  const { translate } = useLanguage();

  // Request permission to access the camera roll
  const requestPermission = async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert(
        translate('permissions.mediaLibraryTitle') || 'Permission Required',
        translate('permissions.mediaLibraryMessage') || 'Please grant permission to access your photos',
        [{ text: translate('common.ok') || 'OK' }]
      );
      return false;
    }
    return true;
  };

  // Pick an image from the camera roll
  const pickImage = async () => {
    if (disabled) return;
    
    const hasPermission = await requestPermission();
    if (!hasPermission) return;

    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        const selectedImage = result.assets[0];
        uploadImage(selectedImage.uri);
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert(
        translate('errors.imagePickerTitle') || 'Error',
        translate('errors.imagePickerMessage') || 'Failed to pick image. Please try again.',
        [{ text: translate('common.ok') || 'OK' }]
      );
    }
  };

  // Upload the selected image to Cloudinary
  const uploadImage = async (uri) => {
    try {
      setLoading(true);

      // Upload the image to Cloudinary
      const result = await CloudinaryService.uploadFromUri(uri, {
        folder,
        tags,
        optimize: true,
        maxWidth: 1200,
        maxHeight: 1200,
        quality: 80,
      });

      // Call the onChange callback with the public ID
      if (onChange) {
        onChange(result.publicId);
      }
    } catch (error) {
      console.error('Error uploading image:', error);
      Alert.alert(
        translate('errors.uploadTitle') || 'Upload Failed',
        translate('errors.uploadMessage') || 'Failed to upload image. Please try again.',
        [{ text: translate('common.ok') || 'OK' }]
      );
    } finally {
      setLoading(false);
    }
  };

  // Remove the current image
  const removeImage = () => {
    if (disabled) return;
    
    Alert.alert(
      translate('confirm.removeImageTitle') || 'Remove Image',
      translate('confirm.removeImageMessage') || 'Are you sure you want to remove this image?',
      [
        {
          text: translate('common.cancel') || 'Cancel',
          style: 'cancel',
        },
        {
          text: translate('common.remove') || 'Remove',
          style: 'destructive',
          onPress: () => {
            if (onChange) {
              onChange(null);
            }
          },
        },
      ]
    );
  };

  // Calculate container style
  const containerStyle = {
    width,
    height,
    borderRadius: circle ? width / 2 : borderRadius,
    ...style,
  };

  return (
    <View style={[styles.container, containerStyle, disabled && styles.disabled]}>
      {value ? (
        // Display the current image
        <View style={styles.imageContainer}>
          <CloudinaryImage
            source={value}
            style={styles.image}
            width={width}
            height={height}
            circle={circle}
            borderRadius={borderRadius}
            resizeMode="cover"
          />
          
          {!disabled && (
            <TouchableOpacity
              style={styles.removeButton}
              onPress={removeImage}
              activeOpacity={0.8}
            >
              <MaterialIcons name="close" size={20} color="#fff" />
            </TouchableOpacity>
          )}
        </View>
      ) : (
        // Display the upload button
        <TouchableOpacity
          style={styles.uploadButton}
          onPress={pickImage}
          activeOpacity={0.8}
          disabled={disabled || loading}
        >
          {loading ? (
            <ActivityIndicator size="small" color="#fff" />
          ) : (
            <>
              <MaterialIcons name="cloud-upload" size={32} color="#fff" />
              <Text style={styles.uploadText}>{placeholder}</Text>
            </>
          )}
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    overflow: 'hidden',
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
  },
  disabled: {
    opacity: 0.6,
  },
  imageContainer: {
    width: '100%',
    height: '100%',
    position: 'relative',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  uploadButton: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
  },
  uploadText: {
    marginTop: 8,
    color: '#555',
    fontSize: 14,
    textAlign: 'center',
  },
  removeButton: {
    position: 'absolute',
    top: 8,
    right: 8,
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default CloudinaryUploader;
