import React, { useState, useEffect } from 'react';
import { View, ScrollView, StyleSheet } from 'react-native';
import { Card, Title, Paragraph, FAB, Portal, Modal, List, Chip, Searchbar, IconButton, Text, DataTable } from 'react-native-paper';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'react-native-chart-kit';
import { db } from '../../config/firebase';
import { collection, query, getDocs, where, orderBy } from 'firebase/firestore';
import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';
import CustomButton from '../../components/common/CustomButton';
import { sanitizeChartData, sanitizeChartDatasets } from '../../utils/ChartUtils';

const ReportsAnalytics = () => {
  const [reportData, setReportData] = useState({
    academic: {},
    attendance: {},
    financial: {},
    library: {},
    events: {},
  });
  const [selectedReport, setSelectedReport] = useState('academic');
  const [dateRange, setDateRange] = useState('month'); // day, week, month, year
  const [loading, setLoading] = useState(false);
  const [exportVisible, setExportVisible] = useState(false);

  useEffect(() => {
    fetchReportData();
  }, [selectedReport, dateRange]);

  const fetchReportData = async () => {
    try {
      setLoading(true);
      switch (selectedReport) {
        case 'academic':
          await fetchAcademicData();
          break;
        case 'attendance':
          await fetchAttendanceData();
          break;
        case 'financial':
          await fetchFinancialData();
          break;
        case 'library':
          await fetchLibraryData();
          break;
        case 'events':
          await fetchEventsData();
          break;
      }
    } catch (error) {
      console.error('Error fetching report data:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchAcademicData = async () => {
    const examResults = await getDocs(collection(db, 'examResults'));
    const assignments = await getDocs(collection(db, 'assignments'));
    
    // Process exam results
    const results = {};
    examResults.forEach(doc => {
      const data = doc.data();
      if (!results[data.subject]) {
        results[data.subject] = {
          totalMarks: 0,
          count: 0,
        };
      }
      results[data.subject].totalMarks += data.marks;
      results[data.subject].count += 1;
    });

    // Calculate averages
    Object.keys(results).forEach(subject => {
      results[subject].average = results[subject].totalMarks / results[subject].count;
    });

    setReportData(prev => ({
      ...prev,
      academic: {
        results,
        assignments: assignments.docs.map(doc => doc.data()),
      },
    }));
  };

  const fetchAttendanceData = async () => {
    const attendance = await getDocs(collection(db, 'attendance'));
    const attendanceData = attendance.docs.map(doc => doc.data());
    
    // Process attendance data
    const stats = {
      present: 0,
      absent: 0,
      late: 0,
      total: attendanceData.length,
    };

    attendanceData.forEach(record => {
      stats[record.status.toLowerCase()]++;
    });

    setReportData(prev => ({
      ...prev,
      attendance: stats,
    }));
  };

  const fetchFinancialData = async () => {
    const transactions = await getDocs(collection(db, 'transactions'));
    const transactionData = transactions.docs.map(doc => doc.data());
    
    // Process financial data
    const summary = {
      income: 0,
      expenses: 0,
      pending: 0,
      categories: {},
    };

    transactionData.forEach(transaction => {
      if (transaction.type === 'income') {
        summary.income += transaction.amount;
      } else {
        summary.expenses += transaction.amount;
      }

      if (transaction.status === 'pending') {
        summary.pending += transaction.amount;
      }

      if (!summary.categories[transaction.category]) {
        summary.categories[transaction.category] = 0;
      }
      summary.categories[transaction.category] += transaction.amount;
    });

    setReportData(prev => ({
      ...prev,
      financial: summary,
    }));
  };

  const fetchLibraryData = async () => {
    const books = await getDocs(collection(db, 'books'));
    const loans = await getDocs(collection(db, 'bookLoans'));
    
    // Process library data
    const stats = {
      totalBooks: books.size,
      availableBooks: 0,
      loanedBooks: 0,
      overdue: 0,
      popularBooks: {},
    };

    books.forEach(doc => {
      const book = doc.data();
      if (book.status === 'available') {
        stats.availableBooks++;
      }
    });

    loans.forEach(doc => {
      const loan = doc.data();
      stats.loanedBooks++;
      
      if (loan.status === 'overdue') {
        stats.overdue++;
      }

      if (!stats.popularBooks[loan.bookId]) {
        stats.popularBooks[loan.bookId] = 0;
      }
      stats.popularBooks[loan.bookId]++;
    });

    setReportData(prev => ({
      ...prev,
      library: stats,
    }));
  };

  const fetchEventsData = async () => {
    const events = await getDocs(collection(db, 'events'));
    const eventData = events.docs.map(doc => doc.data());
    
    // Process events data
    const stats = {
      total: eventData.length,
      byCategory: {},
      byStatus: {},
      upcoming: 0,
    };

    const now = new Date();
    eventData.forEach(event => {
      // Count by category
      if (!stats.byCategory[event.category]) {
        stats.byCategory[event.category] = 0;
      }
      stats.byCategory[event.category]++;

      // Count by status
      if (!stats.byStatus[event.status]) {
        stats.byStatus[event.status] = 0;
      }
      stats.byStatus[event.status]++;

      // Count upcoming events
      if (new Date(event.startDate) > now) {
        stats.upcoming++;
      }
    });

    setReportData(prev => ({
      ...prev,
      events: stats,
    }));
  };

  const handleExport = async (format) => {
    try {
      setLoading(true);
      const data = reportData[selectedReport];
      let content = '';

      // Generate CSV or PDF content based on format
      if (format === 'csv') {
        content = generateCSV(data);
        const fileUri = FileSystem.documentDirectory + `${selectedReport}_report.csv`;
        await FileSystem.writeAsStringAsync(fileUri, content);
        await Sharing.shareAsync(fileUri);
      } else {
        // TODO: Implement PDF export
        console.log('PDF export not implemented yet');
      }

      setExportVisible(false);
    } catch (error) {
      console.error('Error exporting report:', error);
    } finally {
      setLoading(false);
    }
  };

  const generateCSV = (data) => {
    // Convert data object to CSV format
    let csv = 'Category,Value\n';
    Object.entries(data).forEach(([key, value]) => {
      if (typeof value === 'object') {
        Object.entries(value).forEach(([subKey, subValue]) => {
          csv += `${key}_${subKey},${subValue}\n`;
        });
      } else {
        csv += `${key},${value}\n`;
      }
    });
    return csv;
  };

  const renderAcademicReport = () => (
    <ScrollView>
      <Card style={styles.card}>
        <Card.Content>
          <Title>Subject Performance</Title>
          <BarChart
            data={sanitizeChartDatasets({
              labels: Object.keys(reportData.academic.results || {)}),
              datasets: [{
                data: sanitizeChartData(Object.values(reportData.academic.results || {}).map(r => r.average)),
              }],
            }}
            width={350}
            height={220}
            chartConfig={{
              backgroundColor: '#ffffff',
              backgroundGradientFrom: '#ffffff',
              backgroundGradientTo: '#ffffff',
              decimalPlaces: 2,
              color: (opacity = 1) => `rgba(33, 150, 243, ${opacity})`,
            }}
          />
        </Card.Content>
      </Card>

      <Card style={styles.card}>
        <Card.Content>
          <Title>Assignment Completion Rate</Title>
          <PieChart
            data={[
              {
                name: 'Completed',
                population: reportData.academic.assignments?.filter(a => a.status === 'completed').length || 0,
                color: '#4CAF50',
                legendFontColor: '#7F7F7F',
              .map(item => ({
        ...item,
        population: isNaN(item.population) || item.population === undefined || 
                    item.population === null || item.population === Infinity || 
                    item.population === -Infinity ? 0 : item.population
      }))},
              {
                name: 'Pending',
                population: reportData.academic.assignments?.filter(a => a.status === 'pending').length || 0,
                color: '#FFC107',
                legendFontColor: '#7F7F7F',
              },
              {
                name: 'Overdue',
                population: reportData.academic.assignments?.filter(a => a.status === 'overdue').length || 0,
                color: '#F44336',
                legendFontColor: '#7F7F7F',
              },
            ]}
            width={350}
            height={220}
            chartConfig={{
              backgroundColor: '#ffffff',
              backgroundGradientFrom: '#ffffff',
              backgroundGradientTo: '#ffffff',
              decimalPlaces: 2,
              color: (opacity = 1) => `rgba(0, 0, 0, ${opacity})`,
            }}
            accessor="population"
            backgroundColor="transparent"
            paddingLeft="15"
          />
        </Card.Content>
      </Card>
    </ScrollView>
  );

  const renderAttendanceReport = () => (
    <ScrollView>
      <Card style={styles.card}>
        <Card.Content>
          <Title>Attendance Overview</Title>
          <PieChart
            data={[
              {
                name: 'Present',
                population: reportData.attendance.present || 0,
                color: '#4CAF50',
                legendFontColor: '#7F7F7F',
              .map(item => ({
        ...item,
        population: isNaN(item.population) || item.population === undefined || 
                    item.population === null || item.population === Infinity || 
                    item.population === -Infinity ? 0 : item.population
      }))},
              {
                name: 'Absent',
                population: reportData.attendance.absent || 0,
                color: '#F44336',
                legendFontColor: '#7F7F7F',
              },
              {
                name: 'Late',
                population: reportData.attendance.late || 0,
                color: '#FFC107',
                legendFontColor: '#7F7F7F',
              },
            ]}
            width={350}
            height={220}
            chartConfig={{
              backgroundColor: '#ffffff',
              backgroundGradientFrom: '#ffffff',
              backgroundGradientTo: '#ffffff',
              decimalPlaces: 2,
              color: (opacity = 1) => `rgba(0, 0, 0, ${opacity})`,
            }}
            accessor="population"
            backgroundColor="transparent"
            paddingLeft="15"
          />
        </Card.Content>
      </Card>

      <Card style={styles.card}>
        <Card.Content>
          <Title>Attendance Statistics</Title>
          <DataTable>
            <DataTable.Header>
              <DataTable.Title>Category</DataTable.Title>
              <DataTable.Title numeric>Count</DataTable.Title>
              <DataTable.Title numeric>Percentage</DataTable.Title>
            </DataTable.Header>

            {Object.entries(reportData.attendance).map(([key, value]) => (
              key !== 'total' && (
                <DataTable.Row key={key}>
                  <DataTable.Cell>{key.charAt(0).toUpperCase() + key.slice(1)}</DataTable.Cell>
                  <DataTable.Cell numeric>{value}</DataTable.Cell>
                  <DataTable.Cell numeric>
                    {((value / reportData.attendance.total) * 100).toFixed(1)}%
                  </DataTable.Cell>
                </DataTable.Row>
              )
            ))}
          </DataTable>
        </Card.Content>
      </Card>
    </ScrollView>
  );

  const renderFinancialReport = () => (
    <ScrollView>
      <Card style={styles.card}>
        <Card.Content>
          <Title>Financial Summary</Title>
          <View style={styles.statsContainer}>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>${reportData.financial.income?.toFixed(2)}</Text>
              <Text style={styles.statLabel}>Income</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>${reportData.financial.expenses?.toFixed(2)}</Text>
              <Text style={styles.statLabel}>Expenses</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>${reportData.financial.pending?.toFixed(2)}</Text>
              <Text style={styles.statLabel}>Pending</Text>
            </View>
          </View>
        </Card.Content>
      </Card>

      <Card style={styles.card}>
        <Card.Content>
          <Title>Expenses by Category</Title>
          <PieChart
            data={Object.entries(reportData.financial.categories || {.map(item => ({
        ...item,
        population: isNaN(item.population) || item.population === undefined || 
                    item.population === null || item.population === Infinity || 
                    item.population === -Infinity ? 0 : item.population
      }))}).map(([category, amount]) => ({
              name: category,
              population: amount,
              color: `#${Math.floor(Math.random()*16777215).toString(16)}`,
              legendFontColor: '#7F7F7F',
            }))}
            width={350}
            height={220}
            chartConfig={{
              backgroundColor: '#ffffff',
              backgroundGradientFrom: '#ffffff',
              backgroundGradientTo: '#ffffff',
              decimalPlaces: 2,
              color: (opacity = 1) => `rgba(0, 0, 0, ${opacity})`,
            }}
            accessor="population"
            backgroundColor="transparent"
            paddingLeft="15"
          />
        </Card.Content>
      </Card>
    </ScrollView>
  );

  const renderLibraryReport = () => (
    <ScrollView>
      <Card style={styles.card}>
        <Card.Content>
          <Title>Library Overview</Title>
          <View style={styles.statsContainer}>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{reportData.library.totalBooks}</Text>
              <Text style={styles.statLabel}>Total Books</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{reportData.library.loanedBooks}</Text>
              <Text style={styles.statLabel}>Loaned</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{reportData.library.overdue}</Text>
              <Text style={styles.statLabel}>Overdue</Text>
            </View>
          </View>
        </Card.Content>
      </Card>

      <Card style={styles.card}>
        <Card.Content>
          <Title>Book Status Distribution</Title>
          <PieChart
            data={[
              {
                name: 'Available',
                population: reportData.library.availableBooks || 0,
                color: '#4CAF50',
                legendFontColor: '#7F7F7F',
              .map(item => ({
        ...item,
        population: isNaN(item.population) || item.population === undefined || 
                    item.population === null || item.population === Infinity || 
                    item.population === -Infinity ? 0 : item.population
      }))},
              {
                name: 'Loaned',
                population: reportData.library.loanedBooks || 0,
                color: '#2196F3',
                legendFontColor: '#7F7F7F',
              },
              {
                name: 'Overdue',
                population: reportData.library.overdue || 0,
                color: '#F44336',
                legendFontColor: '#7F7F7F',
              },
            ]}
            width={350}
            height={220}
            chartConfig={{
              backgroundColor: '#ffffff',
              backgroundGradientFrom: '#ffffff',
              backgroundGradientTo: '#ffffff',
              decimalPlaces: 2,
              color: (opacity = 1) => `rgba(0, 0, 0, ${opacity})`,
            }}
            accessor="population"
            backgroundColor="transparent"
            paddingLeft="15"
          />
        </Card.Content>
      </Card>
    </ScrollView>
  );

  const renderEventsReport = () => (
    <ScrollView>
      <Card style={styles.card}>
        <Card.Content>
          <Title>Events Overview</Title>
          <View style={styles.statsContainer}>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{reportData.events.total}</Text>
              <Text style={styles.statLabel}>Total Events</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{reportData.events.upcoming}</Text>
              <Text style={styles.statLabel}>Upcoming</Text>
            </View>
          </View>
        </Card.Content>
      </Card>

      <Card style={styles.card}>
        <Card.Content>
          <Title>Events by Category</Title>
          <PieChart
            data={Object.entries(reportData.events.byCategory || {.map(item => ({
        ...item,
        population: isNaN(item.population) || item.population === undefined || 
                    item.population === null || item.population === Infinity || 
                    item.population === -Infinity ? 0 : item.population
      }))}).map(([category, count]) => ({
              name: category,
              population: count,
              color: `#${Math.floor(Math.random()*16777215).toString(16)}`,
              legendFontColor: '#7F7F7F',
            }))}
            width={350}
            height={220}
            chartConfig={{
              backgroundColor: '#ffffff',
              backgroundGradientFrom: '#ffffff',
              backgroundGradientTo: '#ffffff',
              decimalPlaces: 2,
              color: (opacity = 1) => `rgba(0, 0, 0, ${opacity})`,
            }}
            accessor="population"
            backgroundColor="transparent"
            paddingLeft="15"
          />
        </Card.Content>
      </Card>

      <Card style={styles.card}>
        <Card.Content>
          <Title>Events by Status</Title>
          <DataTable>
            <DataTable.Header>
              <DataTable.Title>Status</DataTable.Title>
              <DataTable.Title numeric>Count</DataTable.Title>
              <DataTable.Title numeric>Percentage</DataTable.Title>
            </DataTable.Header>

            {Object.entries(reportData.events.byStatus || {}).map(([status, count]) => (
              <DataTable.Row key={status}>
                <DataTable.Cell>{status.charAt(0).toUpperCase() + status.slice(1)}</DataTable.Cell>
                <DataTable.Cell numeric>{count}</DataTable.Cell>
                <DataTable.Cell numeric>
                  {((count / reportData.events.total) * 100).toFixed(1)}%
                </DataTable.Cell>
              </DataTable.Row>
            ))}
          </DataTable>
        </Card.Content>
      </Card>
    </ScrollView>
  );

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {['academic', 'attendance', 'financial', 'library', 'events'].map((report) => (
            <Chip
              key={report}
              selected={selectedReport === report}
              onPress={() => setSelectedReport(report)}
              style={styles.chip}
            >
              {report.charAt(0).toUpperCase() + report.slice(1)}
            </Chip>
          ))}
        </ScrollView>

        <IconButton
          icon="export"
          onPress={() => setExportVisible(true)}
        />
      </View>

      <View style={styles.dateRangeContainer}>
        {['day', 'week', 'month', 'year'].map((range) => (
          <Chip
            key={range}
            selected={dateRange === range}
            onPress={() => setDateRange(range)}
            style={styles.chip}
          >
            {range.charAt(0).toUpperCase() + range.slice(1)}
          </Chip>
        ))}
      </View>

      <View style={styles.content}>
        {selectedReport === 'academic' && renderAcademicReport()}
        {selectedReport === 'attendance' && renderAttendanceReport()}
        {selectedReport === 'financial' && renderFinancialReport()}
        {selectedReport === 'library' && renderLibraryReport()}
        {selectedReport === 'events' && renderEventsReport()}
      </View>

      <Portal>
        <Modal
          visible={exportVisible}
          onDismiss={() => setExportVisible(false)}
          contentContainerStyle={styles.modalContent}
        >
          <Title>Export Report</Title>
          
          <List.Item
            title="Export as CSV"
            onPress={() => handleExport('csv')}
            left={props => <List.Icon {...props} icon="file-delimited" />}
          />
          
          <List.Item
            title="Export as PDF"
            onPress={() => handleExport('pdf')}
            left={props => <List.Icon {...props} icon="file-pdf-box" />}
          />
        </Modal>
      </Portal>

      {loading && (
        <View style={styles.loadingContainer}>
          <Text>Loading...</Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 10,
    backgroundColor: 'white',
    elevation: 2,
  },
  dateRangeContainer: {
    flexDirection: 'row',
    padding: 10,
    backgroundColor: 'white',
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  content: {
    flex: 1,
  },
  card: {
    margin: 10,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 10,
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  statLabel: {
    color: '#666',
    marginTop: 5,
  },
  chip: {
    margin: 4,
  },
  modalContent: {
    backgroundColor: 'white',
    padding: 20,
    margin: 20,
    borderRadius: 10,
  },
  loadingContainer: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default ReportsAnalytics;
