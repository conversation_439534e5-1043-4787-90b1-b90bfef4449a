import React, { useState } from 'react';
import { TextField, InputAdornment, IconButton, Tooltip } from '@mui/material';
import { Language as LanguageIcon } from '@mui/icons-material';
import { useTranslation } from '../hooks/useTranslation';

const MultilingualInput = ({
    name,
    label,
    value = { en: '', am: '', or: '' },
    onChange,
    required = false,
    error = false,
    helperText = '',
    fullWidth = false,
}) => {
    const { language, t } = useTranslation();
    const [currentLang, setCurrentLang] = useState(language);

    // Validation patterns for different languages
    const patterns = {
        am: /^[\u1200-\u137F\s]*$/,  // Ethiopic Unicode range
        or: /^[a-zA-Z\s']*$/,        // Latin with apostrophe for Afaan Oromoo
        en: /^[a-zA-Z\s]*$/,         // Basic Latin
    };

    const validateInput = (text, lang) => {
        if (!text) return true;
        return patterns[lang].test(text);
    };

    const handleChange = (e) => {
        const newValue = e.target.value;
        if (validateInput(newValue, currentLang)) {
            onChange({
                ...value,
                [currentLang]: newValue,
            });
        }
    };

    const cycleLanguage = () => {
        const langs = ['en', 'am', 'or'];
        const currentIndex = langs.indexOf(currentLang);
        const nextIndex = (currentIndex + 1) % langs.length;
        setCurrentLang(langs[nextIndex]);
    };

    const getLanguageLabel = (code) => {
        const labels = {
            en: 'English',
            am: 'አማርኛ',
            or: 'Afaan Oromoo'
        };
        return labels[code];
    };

    const getPlaceholder = () => {
        switch (currentLang) {
            case 'am':
                return 'በአማርኛ ይጻፉ...';
            case 'or':
                return 'Afaan Oromootiin barreessi...';
            default:
                return 'Type in English...';
        }
    };

    const getHelperText = () => {
        if (error && helperText) return helperText;
        return t('type_in_language', { language: getLanguageLabel(currentLang) });
    };

    const getInputDirection = () => {
        return currentLang === 'am' ? 'rtl' : 'ltr';
    };

    return (
        <TextField
            name={name}
            label={label}
            value={value[currentLang] || ''}
            onChange={handleChange}
            required={required}
            error={error}
            helperText={getHelperText()}
            fullWidth={fullWidth}
            placeholder={getPlaceholder()}
            InputProps={{
                style: { direction: getInputDirection() },
                endAdornment: (
                    <InputAdornment position="end">
                        <Tooltip title={`Switch to ${getLanguageLabel(currentLang)}`}>
                            <IconButton onClick={cycleLanguage} edge="end">
                                <LanguageIcon />
                            </IconButton>
                        </Tooltip>
                    </InputAdornment>
                ),
            }}
            sx={{
                '& .MuiInputBase-input': {
                    fontFamily: currentLang === 'am' 
                        ? '"Noto Sans Ethiopic"'
                        : 'inherit',
                },
            }}
        />
    );
};

export default MultilingualInput;
