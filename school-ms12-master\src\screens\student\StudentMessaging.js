import React, { useState } from 'react';
import { StyleSheet, StatusBar } from 'react-native';
// Theme import removed
import { useAuth } from '../../context/AuthContext';
import { useLanguage } from '../../context/LanguageContext';
import MessagingComponent from '../../components/messaging/MessagingComponent';
import StudentAppHeader from '../../components/common/StudentAppHeader';
import StudentSidebar from '../../components/common/StudentSidebar';

const StudentMessaging = ({ navigation }) => {
  // No theme needed
  const { user } = useAuth();
  const { translate } = useLanguage();
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [activeSidebarItem, setActiveSidebarItem] = useState('messaging');

  const toggleDrawer = () => {
    setDrawerOpen(!drawerOpen);
  };

  // Custom header for student dashboard
  const customHeader = (
    <StudentAppHeader
      title={translate('communication.studentTitle') || 'Student Communication Center'}
      onMenuPress={toggleDrawer}
    />
  );

  // Custom sidebar for student dashboard
  const customSidebar = (
    <StudentSidebar
      visible={drawerOpen}
      onClose={toggleDrawer}
      navigation={navigation}
      activeSidebarItem={activeSidebarItem}
      setActiveSidebarItem={setActiveSidebarItem}
    />
  );

  return (
    <MessagingComponent
      customHeader={customHeader}
      customSidebar={customSidebar}
      userRole="student"
    />
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

export default StudentMessaging;
