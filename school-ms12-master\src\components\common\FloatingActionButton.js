import React, { useState, useRef } from 'react';
import { View, StyleSheet, TouchableOpacity, Animated, Text, Platform } from 'react-native';
import { Portal, Surface, useTheme } from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import * as Animatable from 'react-native-animatable';
import { useLanguage } from '../../context/LanguageContext';

const FloatingActionButton = ({ 
  actions = [], 
  icon = 'plus',
  color = '#1976d2',
  onPress,
  position = 'bottomRight',
  visible = true,
  label,
  testID
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const animation = useRef(new Animated.Value(0)).current;
  const theme = useTheme();
  const { translate, isRTL } = useLanguage();

  // Handle FAB press
  const handlePress = () => {
    if (actions.length === 0 && onPress) {
      onPress();
      return;
    }
    
    setIsOpen(!isOpen);
    Animated.timing(animation, {
      toValue: isOpen ? 0 : 1,
      duration: 300,
      useNativeDriver: true,
    }).start();
  };

  // Handle action press
  const handleActionPress = (action) => {
    setIsOpen(false);
    Animated.timing(animation, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true,
    }).start();
    
    if (action.onPress) {
      action.onPress();
    }
  };

  // Get position styles based on position prop
  const getPositionStyles = () => {
    switch (position) {
      case 'bottomRight':
        return {
          bottom: 16,
          right: 16,
        };
      case 'bottomLeft':
        return {
          bottom: 16,
          left: 16,
        };
      case 'topRight':
        return {
          top: 16,
          right: 16,
        };
      case 'topLeft':
        return {
          top: 16,
          left: 16,
        };
      case 'center':
        return {
          bottom: 16,
          alignSelf: 'center',
        };
      default:
        return {
          bottom: 16,
          right: 16,
        };
    }
  };

  // Don't render if not visible
  if (!visible) return null;

  return (
    <Portal>
      <View style={[styles.container, getPositionStyles()]}>
        {/* Actions */}
        {isOpen && actions.length > 0 && (
          <Animatable.View 
            animation="fadeIn" 
            duration={300} 
            style={styles.actionsContainer}
          >
            {actions.map((action, index) => (
              <Animatable.View
                key={index}
                animation="slideInUp"
                duration={300}
                delay={index * 50}
                style={styles.actionWrapper}
              >
                {action.label && (
                  <Surface style={styles.actionLabelContainer}>
                    <Text style={styles.actionLabel}>{action.label}</Text>
                  </Surface>
                )}
                <TouchableOpacity
                  style={[
                    styles.actionButton,
                    { backgroundColor: action.color || theme.colors.primary }
                  ]}
                  onPress={() => handleActionPress(action)}
                  testID={`fab-action-${index}`}
                >
                  <MaterialCommunityIcons
                    name={action.icon}
                    color="white"
                    size={20}
                  />
                </TouchableOpacity>
              </Animatable.View>
            ))}
          </Animatable.View>
        )}

        {/* Main FAB */}
        <Animated.View
          style={[
            styles.fabContainer,
            {
              transform: [
                {
                  rotate: animation.interpolate({
                    inputRange: [0, 1],
                    outputRange: ['0deg', '45deg'],
                  }),
                },
              ],
            },
          ]}
        >
          <TouchableOpacity
            style={[styles.fab, { backgroundColor: color }]}
            onPress={handlePress}
            testID={testID || 'main-fab'}
          >
            <MaterialCommunityIcons name={icon} color="white" size={24} />
          </TouchableOpacity>
          {label && (
            <Surface style={styles.labelContainer}>
              <Text style={styles.label}>{label}</Text>
            </Surface>
          )}
        </Animated.View>
      </View>
    </Portal>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    alignItems: 'center',
    zIndex: 999,
  },
  fabContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  fab: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 6,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 3,
    zIndex: 1,
  },
  actionsContainer: {
    marginBottom: 16,
    alignItems: 'center',
  },
  actionWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  actionButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  actionLabelContainer: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 4,
    marginRight: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1,
  },
  actionLabel: {
    fontSize: 14,
    color: '#424242',
  },
  labelContainer: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 4,
    marginRight: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1,
  },
  label: {
    fontSize: 14,
    color: '#424242',
  },
});

export default FloatingActionButton;
