import { db } from '../config/firebase';
import { collection, addDoc, serverTimestamp } from 'firebase/firestore';
import axios from 'axios';
import { Platform } from 'react-native';

class EmailNotificationService {
  static async sendVerificationEmail(email, token, type = 'user') {
    try {
      // Create verification link with a fixed URL for React Native
      // This avoids the window.location.origin error
      const baseUrl = 'https://school-ms.example.com'; // Fixed URL for all environments

      // Log platform information for debugging
      console.log(`Platform: ${Platform.OS}, Version: ${Platform.Version}`);

      const verificationLink = `${baseUrl}/verify-email?email=${encodeURIComponent(email)}&token=${token}&type=${type}`;

      // Prepare email content
      let subject;
      switch (type) {
        case 'student':
          subject = 'Verify Your Student Account';
          break;
        case 'parent':
          subject = 'Verify Your Parent Account';
          break;
        case 'teacher':
          subject = 'Verify Your Teacher Account';
          break;
        case 'admin':
          subject = 'Verify Your Administrator Account';
          break;
        default:
          subject = 'Verify Your Account';
      }

      const content = this.getVerificationEmailTemplate(email, verificationLink, type);

      // Send email
      await this.sendEmail(email, subject, content);

      // Log the notification
      await this.logEmailNotification(email, 'verification', {
        type,
        token
      });

      return true;
    } catch (error) {
      console.error('Error sending verification email:', error);
      throw error;
    }
  }

  static async sendPasswordResetEmail(email, resetLink) {
    try {
      // Prepare email content
      const subject = 'Reset Your Password';
      const content = this.getPasswordResetEmailTemplate(email, resetLink);

      // Send email
      await this.sendEmail(email, subject, content);

      // Log the notification
      await this.logEmailNotification(email, 'password_reset', {
        resetLink
      });

      return true;
    } catch (error) {
      console.error('Error sending password reset email:', error);
      throw error;
    }
  }

  static async sendPasswordResetConfirmationEmail(email) {
    try {
      // Prepare email content
      const subject = 'Your Password Has Been Reset';
      const content = this.getPasswordResetConfirmationEmailTemplate(email);

      // Send email
      await this.sendEmail(email, subject, content);

      // Log the notification
      await this.logEmailNotification(email, 'password_reset_confirmation', {});

      return true;
    } catch (error) {
      console.error('Error sending password reset confirmation email:', error);
      throw error;
    }
  }

  static async sendCredentialsEmail(email, password, userDetails, type = 'student') {
    try {
      // Prepare email content
      let userTypeTitle;
      switch (type) {
        case 'student':
          userTypeTitle = 'Student';
          break;
        case 'parent':
          userTypeTitle = 'Parent';
          break;
        case 'teacher':
          userTypeTitle = 'Teacher';
          break;
        case 'admin':
          userTypeTitle = 'Administrator';
          break;
        default:
          userTypeTitle = 'User';
      }

      const subject = `Your ${userTypeTitle} Account Credentials`;
      const content = this.getCredentialsEmailTemplate(email, password, userDetails, type);

      // Send email
      await this.sendEmail(email, subject, content);

      // Log the notification
      await this.logEmailNotification(email, 'credentials', {
        type
      });

      return true;
    } catch (error) {
      console.error('Error sending credentials email:', error);
      throw error;
    }
  }

  static async sendEmail(to, subject, htmlContent) {
    try {
      // Log the email for debugging
      console.log(`Sending email to ${to} with subject: ${subject}`);

      // Store the email in Firestore for sending via Firebase Cloud Functions
      const emailData = {
        to,
        subject,
        html: htmlContent,
        from: '<EMAIL>',
        createdAt: new Date(),
        status: 'pending'
      };

      // Add to the 'emails' collection - this will trigger a Cloud Function to send the email
      const emailRef = await addDoc(collection(db, 'emails'), emailData);
      console.log(`Email queued with ID: ${emailRef.id}`);

      // For development/testing, also save the full email content to a separate collection
      // This helps with debugging when Cloud Functions aren't set up
      await addDoc(collection(db, 'email_contents'), {
        emailId: emailRef.id,
        to,
        subject,
        content: htmlContent,
        createdAt: new Date()
      });

      // Create a verification URL that works in the app
      if (subject.includes('Verify')) {
        // Extract the token from the HTML content
        const tokenMatch = htmlContent.match(/token=([^&"]+)/);
        const emailMatch = htmlContent.match(/email=([^&"]+)/);
        const typeMatch = htmlContent.match(/type=([^&"]+)/);

        if (tokenMatch && emailMatch && typeMatch) {
          const token = tokenMatch[1];
          const email = decodeURIComponent(emailMatch[1]);
          const type = typeMatch[1];

          // Store the verification info in a collection that the app can check
          await addDoc(collection(db, 'app_verifications'), {
            email,
            token,
            type,
            createdAt: new Date(),
            expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
            verified: false
          });

          console.log(`Verification info stored for ${email} with token ${token}`);
        }
      }

      return true;
    } catch (error) {
      console.error('Error sending email:', error);
      throw error;
    }
  }

  static async logEmailNotification(recipient, type, data) {
    try {
      await addDoc(collection(db, 'email_logs'), {
        recipient,
        type,
        data,
        timestamp: serverTimestamp(),
        status: 'sent'
      });
    } catch (error) {
      console.error('Error logging email notification:', error);
    }
  }

  static getVerificationEmailTemplate(email, verificationLink, type) {
    let userType;
    switch (type) {
      case 'student':
        userType = 'Student';
        break;
      case 'parent':
        userType = 'Parent';
        break;
      case 'teacher':
        userType = 'Teacher';
        break;
      case 'admin':
        userType = 'Administrator';
        break;
      default:
        userType = 'User';
    }

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background-color: #4CAF50; color: white; padding: 10px; text-align: center; }
          .content { padding: 20px; border: 1px solid #ddd; }
          .button { display: inline-block; background-color: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; }
          .footer { margin-top: 20px; font-size: 12px; color: #777; text-align: center; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h2>Email Verification</h2>
          </div>
          <div class="content">
            <p>Hello,</p>
            <p>Thank you for registering as a ${userType.toLowerCase()} with our school management system. Please verify your email address by clicking the button below:</p>
            <p style="text-align: center;">
              <a href="${verificationLink}" class="button">Verify Email</a>
            </p>
            <p>If the button doesn't work, you can copy and paste the following link into your browser:</p>
            <p>${verificationLink}</p>
            <p>This link will expire in 24 hours.</p>
            <p>If you didn't request this verification, please ignore this email.</p>
          </div>
          <div class="footer">
            <p>This is an automated message. Please do not reply to this email.</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  static getPasswordResetEmailTemplate(email, resetLink) {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background-color: #2196F3; color: white; padding: 10px; text-align: center; }
          .content { padding: 20px; border: 1px solid #ddd; }
          .button { display: inline-block; background-color: #2196F3; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; }
          .warning { color: #f44336; font-weight: bold; }
          .footer { margin-top: 20px; font-size: 12px; color: #777; text-align: center; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h2>Password Reset Request</h2>
          </div>
          <div class="content">
            <p>Hello,</p>
            <p>We received a request to reset the password for your account with the email address: <strong>${email}</strong></p>
            <p>To reset your password, click on the button below:</p>
            <p style="text-align: center;">
              <a href="${resetLink}" class="button">Reset Password</a>
            </p>
            <p>If the button doesn't work, you can copy and paste the following link into your browser:</p>
            <p>${resetLink}</p>
            <p>This link will expire in 1 hour for security reasons.</p>
            <p class="warning">If you did not request a password reset, please ignore this email or contact support if you have concerns.</p>
          </div>
          <div class="footer">
            <p>This is an automated message. Please do not reply to this email.</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  static getPasswordResetConfirmationEmailTemplate(email) {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background-color: #4CAF50; color: white; padding: 10px; text-align: center; }
          .content { padding: 20px; border: 1px solid #ddd; }
          .button { display: inline-block; background-color: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; }
          .security-tips { background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin-top: 20px; }
          .footer { margin-top: 20px; font-size: 12px; color: #777; text-align: center; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h2>Password Reset Successful</h2>
          </div>
          <div class="content">
            <p>Hello,</p>
            <p>Your password for the account with email address <strong>${email}</strong> has been successfully reset.</p>
            <p>You can now log in with your new password.</p>
            <p style="text-align: center;">
              <a href="https://school-ms.example.com/login" class="button">Go to Login</a>
            </p>

            <div class="security-tips">
              <h3>Security Tips:</h3>
              <ul>
                <li>Never share your password with anyone</li>
                <li>Use a strong, unique password</li>
                <li>Consider changing your password regularly</li>
                <li>Make sure your device is secure before logging in</li>
              </ul>
            </div>

            <p>If you did not reset your password, please contact our support team immediately.</p>
          </div>
          <div class="footer">
            <p>This is an automated message. Please do not reply to this email.</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  static getCredentialsEmailTemplate(email, password, userDetails, type) {
    let userType;
    switch (type) {
      case 'student':
        userType = 'Student';
        break;
      case 'parent':
        userType = 'Parent';
        break;
      case 'teacher':
        userType = 'Teacher';
        break;
      case 'admin':
        userType = 'Administrator';
        break;
      default:
        userType = 'User';
    }

    const name = `${userDetails.firstName} ${userDetails.lastName}`;

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background-color: #4CAF50; color: white; padding: 10px; text-align: center; }
          .content { padding: 20px; border: 1px solid #ddd; }
          .credentials { background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin: 15px 0; }
          .footer { margin-top: 20px; font-size: 12px; color: #777; text-align: center; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h2>${userType} Account Created</h2>
          </div>
          <div class="content">
            <p>Hello ${name},</p>
            <p>Your ${userType.toLowerCase()} account has been successfully created in our school management system. Below are your login credentials:</p>

            <div class="credentials">
              <p><strong>Email:</strong> ${email}</p>
              <p><strong>Password:</strong> ${password}</p>
            </div>

            <p>Please keep these credentials safe and do not share them with anyone. We recommend changing your password after your first login.</p>

            <h3>Your Information:</h3>
            <ul>
              <li><strong>Name:</strong> ${name}</li>
              ${type === 'student' ? `<li><strong>Class:</strong> ${userDetails.className || 'N/A'}</li>` : ''}
              ${type === 'student' ? `<li><strong>Section:</strong> ${userDetails.sectionName || 'N/A'}</li>` : ''}
              ${type === 'student' ? `<li><strong>Admission Number:</strong> ${userDetails.admissionNumber || 'N/A'}</li>` : ''}
              ${type === 'teacher' ? `<li><strong>Qualification:</strong> ${userDetails.qualification || 'N/A'}</li>` : ''}
              ${type === 'teacher' ? `<li><strong>Specialization:</strong> ${userDetails.specialization || 'N/A'}</li>` : ''}
              ${type === 'teacher' ? `<li><strong>Joining Date:</strong> ${userDetails.joiningDate || 'N/A'}</li>` : ''}
            </ul>

            <p>You can log in to the system using the link below:</p>
            <p style="text-align: center;">
              <a href="https://school-ms.example.com/login" style="display: inline-block; background-color: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Login to System</a>
            </p>
          </div>
          <div class="footer">
            <p>This is an automated message. Please do not reply to this email.</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }
}

export default EmailNotificationService;
