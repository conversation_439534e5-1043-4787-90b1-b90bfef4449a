const { initializeApp } = require('firebase/app');
const { getAuth, createUserWithEmailAndPassword } = require('firebase/auth');
const { getFirestore, doc, setDoc } = require('firebase/firestore');

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyBHkGAhm80xBIECyaHbZ_DbeNzu3jnVuDg",
  authDomain: "schoolmn-16cbc.firebaseapp.com",
  projectId: "schoolmn-16cbc",
  storageBucket: "schoolmn-16cbc.appspot.com",
  messagingSenderId: "999485613068",
  appId: "1:999485613068:web:e9c0c3e0a4c7f4e4c8b8b8"
};
// > node src/scripts/recreateAdminUser.js
// Initialize Firebase
const app = initializeApp(firebaseConfig);
const auth = getAuth(app);
const db = getFirestore(app);

const createAdminUser = async () => {
  try {
    console.log('Creating admin user...');
    
    // Check if user already exists
    try {
      // Create auth user
      const userCredential = await createUserWithEmailAndPassword(auth, '<EMAIL>', '123qwe');
      console.log('Auth user created successfully');
      
      // Create admin document
      const userDocRef = doc(db, 'users', userCredential.user.uid);
      await setDoc(userDocRef, {
        uid: userCredential.user.uid,
        email: '<EMAIL>',
        firstName: 'Admin',
        lastName: 'User',
        role: 'admin',
        status: 'active',
        createdAt: new Date().toISOString(),
      });
      
      console.log('Admin document created successfully');
      console.log('Admin user created with UID:', userCredential.user.uid);
    } catch (error) {
      if (error.code === 'auth/email-already-in-use') {
        console.log('Admin user already exists');
      } else {
        throw error;
      }
    }
    
    process.exit(0);
  } catch (error) {
    console.error('Error creating admin user:', error);
    process.exit(1);
  }
};

createAdminUser();
