import React, { createContext, useState, useContext, useRef } from 'react';
import { Animated } from 'react-native';

// Create the context
const ParentSidebarContext = createContext();

/**
 * Provider component for the ParentSidebar context
 * 
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components
 * @returns {React.ReactElement} The ParentSidebarProvider component
 */
export const ParentSidebarProvider = ({ children }) => {
  // State for drawer open/close
  const [drawerOpen, setDrawerOpen] = useState(false);
  
  // State for active sidebar item
  const [activeSidebarItem, setActiveSidebarItem] = useState('dashboard');
  
  // Animated values for drawer and backdrop
  const drawerAnim = useRef(new Animated.Value(-300)).current;
  const backdropFadeAnim = useRef(new Animated.Value(0)).current;
  
  // Toggle drawer open/close
  const toggleDrawer = () => {
    if (drawerOpen) {
      // Close drawer
      Animated.parallel([
        Animated.timing(drawerAnim, {
          toValue: -300,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(backdropFadeAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start(() => {
        setDrawerOpen(false);
      });
    } else {
      // Open drawer
      setDrawerOpen(true);
      Animated.parallel([
        Animated.timing(drawerAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(backdropFadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    }
  };
  
  // Context value
  const contextValue = {
    drawerOpen,
    setDrawerOpen,
    activeSidebarItem,
    setActiveSidebarItem,
    drawerAnim,
    backdropFadeAnim,
    toggleDrawer,
  };
  
  return (
    <ParentSidebarContext.Provider value={contextValue}>
      {children}
    </ParentSidebarContext.Provider>
  );
};

/**
 * Hook to use the ParentSidebar context
 * 
 * @returns {Object} The ParentSidebar context value
 */
export const useParentSidebar = () => {
  const context = useContext(ParentSidebarContext);
  
  if (!context) {
    throw new Error('useParentSidebar must be used within a ParentSidebarProvider');
  }
  
  return context;
};
