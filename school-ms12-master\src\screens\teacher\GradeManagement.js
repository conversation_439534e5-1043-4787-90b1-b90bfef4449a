import React, { useState, useEffect, useRef } from 'react';
import { View, ScrollView, StyleSheet, StatusBar, SafeAreaView, Animated } from 'react-native';
import {
  DataTable,
  Card,
  Title,
  Button,
  IconButton,
  Text,
  ActivityIndicator,
  Snackbar,
  Portal,
  Dialog,
  TextInput,
  Paragraph,
  FAB,
  useTheme,
  SegmentedButtons
} from 'react-native-paper';
import { db } from '../../config/firebase';
import {
  collection,
  query,
  where,
  getDocs,
  addDoc,
  updateDoc,
  orderBy,
  Timestamp,
  doc,
  getDoc,
  deleteDoc
} from 'firebase/firestore';
import { auth } from '../../config/firebase';
import { useLanguage } from '../../context/LanguageContext';
import * as Animatable from 'react-native-animatable';
import TeacherAppHeader from '../../components/common/TeacherAppHeader';
import TeacherSidebar from '../../components/common/TeacherSidebar';
import SidebarBackdrop from '../../components/common/SidebarBackdrop';
import TeacherGradeSubmission from '../../components/teacher/TeacherGradeSubmission';
import TeacherResultsView from '../../components/teacher/TeacherResultsView';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5'
  },
  safeArea: {
    flex: 1,
  },
  mainContent: {
    flex: 1,
    padding: 16,
  },
  headerCard: {
    margin: 16,
    elevation: 4
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start'
  },
  headerLeft: {
    flex: 1
  },
  examControls: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8
  },
  examTypeButton: {
    marginRight: 16
  },
  assessmentCard: {
    marginBottom: 16,
    elevation: 2
  },
  assessmentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8
  },
  assessmentTitle: {
    fontSize: 16,
    fontWeight: 'bold'
  },
  assessmentType: {
    fontSize: 12,
    color: '#757575',
    marginTop: 2
  },
  assessmentPoints: {
    fontSize: 14,
    color: '#666'
  },
  assessmentActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 8
  },
  // Table styles
  tableWrapper: {
    marginTop: 16,
    marginBottom: 16,
    borderRadius: 8,
    overflow: 'hidden',
    elevation: 2,
    backgroundColor: '#ffffff',
  },
  tableContainer: {
    minWidth: '100%',
  },
  dataTable: {
    backgroundColor: '#ffffff',
  },
  tableHeader: {
    backgroundColor: '#f0f0f0',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    elevation: 2,
  },
  tableBody: {
    maxHeight: 400, // Set a max height to enable vertical scrolling
  },
  // Column styles
  rollColumn: {
    width: 100,
    justifyContent: 'center',
  },
  nameColumn: {
    width: 180,
    justifyContent: 'center',
  },
  assessmentColumn: {
    width: 120,
    justifyContent: 'center',
  },
  totalColumn: {
    width: 100,
    justifyContent: 'center',
  },
  // Assessment header cell
  assessmentHeaderCell: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 4,
  },
  assessmentColumnTitle: {
    fontSize: 12,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  assessmentColumnPoints: {
    fontSize: 10,
    color: '#666',
    textAlign: 'center',
  },
  // Input styles
  pointsInput: {
    backgroundColor: 'transparent',
    textAlign: 'center',
    width: 60,
    height: 40,
  },
  unsavedInput: {
    backgroundColor: '#FFF9C4',
    borderWidth: 1,
    borderColor: '#FBC02D'
  },
  percentageText: {
    fontWeight: 'bold',
  },
  totalPointsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#E3F2FD',
    borderRadius: 8,
    marginBottom: 16
  },
  totalPointsText: {
    fontSize: 16,
    fontWeight: 'bold'
  },
  remainingPointsText: {
    fontSize: 16,
    color: '#666'
  },
  filterContainer: {
    marginBottom: 16,
    flexDirection: 'row',
  },
  filterButton: {
    marginRight: 8,
    borderRadius: 20,
  },
  filterButtonLabel: {
    fontSize: 12,
    marginHorizontal: 4,
  },
  loader: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center'
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16
  },
  errorText: {
    color: 'red',
    marginBottom: 16,
    textAlign: 'center'
  },
  remarksInput: {
    flex: 1,
    backgroundColor: 'transparent'
  },
  statusChip: {
    alignSelf: 'center'
  },
  publishedChip: {
    backgroundColor: '#C8E6C9'
  },
  pendingChip: {
    backgroundColor: '#FFE0B2'
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
  },
  dialogContent: {
    paddingTop: 8,
    paddingBottom: 16
  },
  dialogInput: {
    marginBottom: 16
  },
  inputLabel: {
    fontSize: 12,
    color: '#666',
    marginBottom: 8,
    marginLeft: 4
  },
  typeSelector: {
    flexDirection: 'row',
    marginBottom: 8
  },
  typeButton: {
    marginRight: 8,
    borderRadius: 20,
  },
  typeButtonLabel: {
    fontSize: 12,
    marginHorizontal: 4,
  },
  studentRow: {
    backgroundColor: '#fff',
    marginBottom: 2,
    minHeight: 60,
  },
  studentRowAlternate: {
    backgroundColor: '#F5F5F5'
  },
  pointsCell: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end'
  },
  actionButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 24,
    marginBottom: 16,
    paddingHorizontal: 16
  },
  actionButton: {
    flex: 1,
    marginHorizontal: 8,
    paddingVertical: 8
  },
  saveButton: {
    backgroundColor: '#2196F3'
  },
  submitButton: {
    backgroundColor: '#4CAF50'
  },
  contentContainer: {
    marginTop: 16,
  },
  snackbar: {
    bottom: 16,
  }
});

const GradeManagement = ({ route, navigation }) => {
  const { translate } = useLanguage();
  // No theme needed
  const backdropFadeAnim = useRef(new Animated.Value(0)).current;

  // Add default values and error handling for route params
  const params = route?.params || {};
  const classId = params.classId;
  const className = params.className;
  const sectionName = params.sectionName;
  const subject = params.subject || 'Mathematics';

  // Redirect to ClassManagement if required params are missing
  useEffect(() => {
    if (!classId || !className || !sectionName) {
      navigation.replace('ClassManagement');
      return;
    }
  }, [classId, className, sectionName]);

  // Sidebar state
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [activeSidebarItem, setActiveSidebarItem] = useState('GradeManagement');

  // View mode state - use route param if provided, otherwise default to 'assessments'
  const [viewMode, setViewMode] = useState(params.viewMode || 'assessments'); // 'assessments', 'submission', 'results'

  // Assessment and student data
  const [students, setStudents] = useState([]);
  const [assessments, setAssessments] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [addAssessmentDialogVisible, setAddAssessmentDialogVisible] = useState(false);
  const [editAssessmentDialogVisible, setEditAssessmentDialogVisible] = useState(false);
  const [selectedAssessment, setSelectedAssessment] = useState(null);
  const [totalPoints, setTotalPoints] = useState(0);
  const [remainingPoints, setRemainingPoints] = useState(100);
  const [calculatedScores, setCalculatedScores] = useState({});
  const [submitDialogVisible, setSubmitDialogVisible] = useState(false);

  // New assessment form data
  const [assessmentFormData, setAssessmentFormData] = useState({
    title: '',
    description: '',
    points: '',
    type: 'assignment', // Default type
    dueDate: new Date().toISOString()
  });

  // Assessment type filter
  const [selectedFilter, setSelectedFilter] = useState('all');
  const assessmentTypes = ['all', 'assignment', 'quiz', 'test', 'mid', 'final', 'project'];

  // Toggle sidebar
  const toggleDrawer = () => {
    const newValue = !drawerOpen;
    setDrawerOpen(newValue);

    // Animate backdrop
    Animated.timing(backdropFadeAnim, {
      toValue: newValue ? 0.5 : 0,
      duration: 300,
      useNativeDriver: true,
    }).start();
  };

  useEffect(() => {
    if (classId && className && sectionName) {
      fetchStudentsAndAssessments();
    }
  }, [classId, className, sectionName, selectedFilter]);

  const fetchStudentsAndAssessments = async () => {
    try {
      setLoading(true);
      setError(null);

      // Get the class document to get the section's student IDs
      const classRef = doc(db, 'classes', classId);
      const classDoc = await getDoc(classRef);

      if (!classDoc.exists()) {
        throw new Error('Class not found');
      }

      const classData = classDoc.data();
      const section = classData.sections.find(s => s.name === sectionName);

      if (!section || !section.students) {
        setStudents([]);
        return;
      }

      // Fetch all students in the section
      const usersRef = collection(db, 'users');
      const studentPromises = section.students.map(async studentId => {
        const studentDoc = await getDoc(doc(usersRef, studentId));
        if (studentDoc.exists()) {
          const studentData = studentDoc.data();
          return {
            id: studentDoc.id,
            uid: studentData.uid,
            name: studentData.firstName && studentData.lastName
              ? `${studentData.firstName} ${studentData.lastName}`
              : studentData.displayName || studentData.username || 'No Name',
            rollNumber: studentData.rollNumber || 'N/A'
          };
        }
        return null;
      });

      const studentsData = (await Promise.all(studentPromises))
        .filter(student => student !== null)
        .sort((a, b) => (a.rollNumber !== 'N/A' && b.rollNumber !== 'N/A')
          ? a.rollNumber.localeCompare(b.rollNumber)
          : a.name.localeCompare(b.name));

      // Fetch assessments for this class and section
      const assessmentsRef = collection(db, 'assessments');
      let queryConstraints = [
        where('classId', '==', classId),
        where('sectionName', '==', sectionName),
        where('subject', '==', subject)
      ];

      // Add type filter if not 'all'
      if (selectedFilter !== 'all') {
        queryConstraints.push(where('type', '==', selectedFilter));
      }

      const assessmentsQuery = query(assessmentsRef, ...queryConstraints);
      const assessmentsSnapshot = await getDocs(assessmentsQuery);
      const assessmentsData = [];
      let totalAssessmentPoints = 0;

      assessmentsSnapshot.docs.forEach(doc => {
        const assessmentData = doc.data();
        assessmentsData.push({
          id: doc.id,
          ...assessmentData,
          // Ensure type exists, default to 'assignment' if not specified
          type: assessmentData.type || 'assignment'
        });

        // Calculate total points
        if (assessmentData.points) {
          totalAssessmentPoints += parseFloat(assessmentData.points);
        }
      });

      // Fetch student scores for each assessment
      const scoresRef = collection(db, 'scores');
      const scoresPromises = studentsData.map(async student => {
        const studentScores = {};

        for (const assessment of assessmentsData) {
          const scoreQuery = query(
            scoresRef,
            where('studentId', '==', student.uid || student.id),
            where('assessmentId', '==', assessment.id)
          );

          const scoreSnapshot = await getDocs(scoreQuery);
          if (!scoreSnapshot.empty) {
            const scoreData = scoreSnapshot.docs[0].data();
            studentScores[assessment.id] = {
              id: scoreSnapshot.docs[0].id,
              points: scoreData.points || '',
              remarks: scoreData.remarks || ''
            };
          } else {
            studentScores[assessment.id] = {
              id: null,
              points: '',
              remarks: ''
            };
          }
        }

        return {
          ...student,
          scores: studentScores
        };
      });

      const studentsWithScores = await Promise.all(scoresPromises);

      setStudents(studentsWithScores);
      setAssessments(assessmentsData);
      setTotalPoints(totalAssessmentPoints);
      setRemainingPoints(100 - totalAssessmentPoints);

    } catch (err) {
      console.error('Error fetching data:', err);
      setError('Failed to fetch students and assessments');
      setSnackbarMessage('Error loading data. Please try again.');
      setSnackbarVisible(true);
    } finally {
      setLoading(false);
    }
  };



  // Calculate all student scores
  const calculateAllScores = () => {
    setLoading(true);

    try {
      // Check if we have assessments and students
      if (!assessments.length) {
        setSnackbarMessage('No assessments available to calculate scores');
        setSnackbarVisible(true);
        setLoading(false);
        return;
      }

      if (!students.length) {
        setSnackbarMessage('No students available to calculate scores');
        setSnackbarVisible(true);
        setLoading(false);
        return;
      }

      const newCalculatedScores = {};

      // Process each student
      students.forEach(student => {
        if (!student) return;

        const studentId = student.id || '';
        if (!studentId) return;

        // Initialize scores object if it doesn't exist
        const studentScores = student.scores || {};

        let studentTotalScore = 0;
        const assessmentScores = {};

        // Calculate score for each assessment
        assessments.forEach(assessment => {
          if (!assessment || !assessment.id) return;

          const assessmentId = assessment.id;
          const score = studentScores[assessmentId] || {};

          // Skip if no points entered or assessment has no points
          if (!score.points || !assessment.points) {
            assessmentScores[assessmentId] = {
              points: 0,
              maxPoints: parseFloat(assessment.points) || 0,
              percentage: '0.0'
            };
            return;
          }

          try {
            const points = parseFloat(score.points) || 0;
            const maxPoints = parseFloat(assessment.points) || 0;

            // Avoid division by zero
            if (maxPoints <= 0) {
              assessmentScores[assessmentId] = {
                points,
                maxPoints,
                percentage: '0.0'
              };
              return;
            }

            const percentage = (points / maxPoints) * 100;

            assessmentScores[assessmentId] = {
              points,
              maxPoints,
              percentage: percentage.toFixed(1)
            };

            // Calculate weighted score based on assessment points out of 100
            const weightedScore = (points / maxPoints) * maxPoints;
            studentTotalScore += weightedScore;
          } catch (err) {
            console.error(`Error calculating score for student ${studentId}, assessment ${assessmentId}:`, err);
            // Continue with other assessments
          }
        });

        // Store the calculated scores
        newCalculatedScores[studentId] = {
          assessmentScores,
          totalScore: studentTotalScore.toFixed(1)
        };
      });

      // Update state with calculated scores
      setCalculatedScores(newCalculatedScores);

      // Show success message
      setSnackbarMessage('Scores calculated successfully');
      setSnackbarVisible(true);
    } catch (error) {
      console.error('Error calculating scores:', error);
      setSnackbarMessage(`Error calculating scores: ${error.message}`);
      setSnackbarVisible(true);
    } finally {
      setLoading(false);
    }
  };

  // Submit grades for approval
  const submitGradesForApproval = async () => {
    try {
      setLoading(true);

      // Make sure all scores are saved first
      await saveAllScores();

      // Make sure scores are calculated before submitting
      await new Promise(resolve => {
        calculateAllScores();
        // Give time for the calculation to complete and update state
        setTimeout(resolve, 500);
      });

      // Validate that we have calculated scores for all students
      if (Object.keys(calculatedScores).length === 0) {
        throw new Error('No calculated scores available. Please calculate scores first.');
      }

      // Create a submission record
      const submissionRef = collection(db, 'gradeSubmissions');

      // Prepare student data with proper validation
      const studentData = students.map(s => {
        // Ensure we have valid data for each student
        const studentId = s.id || '';
        const studentUid = s.uid || studentId; // Fallback to id if uid is missing
        const studentName = s.name || 'Unknown Student';
        const score = calculatedScores[studentId]?.totalScore || '0';

        return {
          id: studentId,
          uid: studentUid,
          name: studentName,
          totalScore: score
        };
      });

      // Prepare assessment data with proper validation
      const assessmentData = assessments.map(a => {
        return {
          id: a.id || '',
          title: a.title || 'Untitled Assessment',
          type: a.type || 'assignment',
          points: parseFloat(a.points) || 0
        };
      });

      const submissionData = {
        classId: classId || '',
        className: className || '',
        sectionName: sectionName || '',
        subject: subject || '',
        teacherId: auth.currentUser?.uid || '',
        teacherName: auth.currentUser?.displayName || auth.currentUser?.email || 'Unknown Teacher',
        status: 'pending',
        submittedAt: Timestamp.now(),
        assessments: assessmentData,
        students: studentData
      };

      // Log the data being submitted for debugging
      console.log('Submitting grade data:', JSON.stringify(submissionData));

      const submissionDocRef = await addDoc(submissionRef, submissionData);

      // Send notification to admins
      const adminNotificationRef = collection(db, 'notifications');
      await addDoc(adminNotificationRef, {
        title: 'Grade Submission',
        body: `Teacher ${auth.currentUser?.displayName || auth.currentUser?.email || 'Unknown'} has submitted grades for ${className}-${sectionName} ${subject}`,
        type: 'grade_submission',
        role: 'admin',
        read: false,
        data: {
          submissionId: submissionDocRef.id,
          classId: classId || '',
          className: className || '',
          sectionName: sectionName || '',
          subject: subject || ''
        },
        createdAt: Timestamp.now()
      });

      setSubmitDialogVisible(false);
      setSnackbarMessage('Grades submitted for approval');
      setSnackbarVisible(true);
    } catch (error) {
      console.error('Error submitting grades:', error);
      setSnackbarMessage(`Error submitting grades: ${error.message}`);
      setSnackbarVisible(true);
    } finally {
      setLoading(false);
    }
  };

  // Handle adding a new assessment
  const handleAddAssessment = async () => {
    try {
      if (!assessmentFormData.title) {
        setSnackbarMessage('Please enter an assessment title');
        setSnackbarVisible(true);
        return;
      }

      if (!assessmentFormData.points) {
        setSnackbarMessage('Please enter points for the assessment');
        setSnackbarVisible(true);
        return;
      }

      const points = parseFloat(assessmentFormData.points);
      if (isNaN(points) || points <= 0) {
        setSnackbarMessage('Points must be a positive number');
        setSnackbarVisible(true);
        return;
      }

      // Check if total would exceed 100 points
      let currentTotal = 0;
      assessments.forEach(assessment => {
        if (assessment.points) {
          currentTotal += parseFloat(assessment.points);
        }
      });

      if (currentTotal + points > 100) {
        setSnackbarMessage(`Total points cannot exceed 100. Current total: ${currentTotal}, Remaining: ${100 - currentTotal}`);
        setSnackbarVisible(true);
        return;
      }

      setLoading(true);
      const assessmentsRef = collection(db, 'assessments');

      const newAssessment = {
        title: assessmentFormData.title,
        description: assessmentFormData.description,
        points: points,
        type: assessmentFormData.type,
        classId,
        className,
        sectionName,
        subject,
        dueDate: assessmentFormData.dueDate,
        createdAt: Timestamp.now(),
        createdBy: auth.currentUser.uid
      };

      await addDoc(assessmentsRef, newAssessment);

      // Reset form data
      setAssessmentFormData({
        title: '',
        description: '',
        points: '',
        type: 'assignment',
        dueDate: new Date().toISOString()
      });

      setAddAssessmentDialogVisible(false);
      await fetchStudentsAndAssessments();

      setSnackbarMessage('Assessment added successfully');
      setSnackbarVisible(true);
    } catch (err) {
      console.error('Error adding assessment:', err);
      setSnackbarMessage('Failed to add assessment');
      setSnackbarVisible(true);
    } finally {
      setLoading(false);
    }
  };

  // Handle editing an assessment
  const handleEditAssessment = async () => {
    try {
      if (!selectedAssessment) {
        return;
      }

      if (!assessmentFormData.title) {
        setSnackbarMessage('Please enter an assessment title');
        setSnackbarVisible(true);
        return;
      }

      if (!assessmentFormData.points) {
        setSnackbarMessage('Please enter points for the assessment');
        setSnackbarVisible(true);
        return;
      }

      const points = parseFloat(assessmentFormData.points);
      if (isNaN(points) || points <= 0) {
        setSnackbarMessage('Points must be a positive number');
        setSnackbarVisible(true);
        return;
      }

      // Calculate what the total would be with the new points

      // Calculate current total excluding the assessment being edited
      let currentTotal = 0;
      assessments.forEach(assessment => {
        if (assessment.id !== selectedAssessment.id && assessment.points) {
          currentTotal += parseFloat(assessment.points);
        }
      });

      if (currentTotal + points > 100) {
        setSnackbarMessage(`Total points cannot exceed 100. Current total (excluding this assessment): ${currentTotal}, Remaining: ${100 - currentTotal}`);
        setSnackbarVisible(true);
        return;
      }

      setLoading(true);
      const assessmentRef = doc(db, 'assessments', selectedAssessment.id);

      await updateDoc(assessmentRef, {
        title: assessmentFormData.title,
        description: assessmentFormData.description,
        points: points,
        type: assessmentFormData.type,
        dueDate: assessmentFormData.dueDate,
        updatedAt: Timestamp.now(),
        updatedBy: auth.currentUser.uid
      });

      setEditAssessmentDialogVisible(false);
      await fetchStudentsAndAssessments();

      setSnackbarMessage('Assessment updated successfully');
      setSnackbarVisible(true);
    } catch (err) {
      console.error('Error updating assessment:', err);
      setSnackbarMessage('Failed to update assessment');
      setSnackbarVisible(true);
    } finally {
      setLoading(false);
    }
  };

  // Handle deleting an assessment
  const handleDeleteAssessment = async (assessmentId) => {
    try {
      setLoading(true);

      // Delete the assessment
      const assessmentRef = doc(db, 'assessments', assessmentId);
      await deleteDoc(assessmentRef);

      // Delete all scores for this assessment
      const scoresRef = collection(db, 'scores');
      const scoresQuery = query(
        scoresRef,
        where('assessmentId', '==', assessmentId)
      );

      const scoresSnapshot = await getDocs(scoresQuery);
      const deletePromises = scoresSnapshot.docs.map(doc =>
        deleteDoc(doc.ref)
      );

      await Promise.all(deletePromises);
      await fetchStudentsAndAssessments();

      setSnackbarMessage('Assessment deleted successfully');
      setSnackbarVisible(true);
    } catch (err) {
      console.error('Error deleting assessment:', err);
      setSnackbarMessage('Failed to delete assessment');
      setSnackbarVisible(true);
    } finally {
      setLoading(false);
    }
  };

  // Handle updating a student's score in local state (without saving to database)
  const handleScoreChange = (studentId, assessmentId, points) => {
    // Update the score in local state only
    setStudents(prevStudents => {
      return prevStudents.map(student => {
        if (student.id === studentId || student.uid === studentId) {
          // Create a new scores object with the updated score
          const updatedScores = {
            ...student.scores,
            [assessmentId]: {
              ...student.scores[assessmentId],
              points: points,
              unsaved: true // Mark as unsaved to highlight in UI
            }
          };

          // Return updated student object
          return {
            ...student,
            scores: updatedScores
          };
        }
        return student;
      });
    });
  };

  // Save all scores to the database
  const saveAllScores = async () => {
    try {
      setLoading(true);
      const scoresRef = collection(db, 'scores');
      const savePromises = [];

      // Find all unsaved scores and save them
      students.forEach(student => {
        if (!student.scores) return;

        Object.entries(student.scores).forEach(([assessmentId, score]) => {
          if (score.unsaved) {
            const assessment = assessments.find(a => a.id === assessmentId);
            if (!assessment) return;

            const scoreData = {
              studentId: student.uid || student.id,
              studentName: student.name,
              assessmentId,
              assessmentTitle: assessment.title,
              points: score.points,
              maxPoints: parseFloat(assessment.points),
              classId,
              className,
              sectionName,
              subject,
              timestamp: Timestamp.now(),
              lastModifiedBy: auth.currentUser.uid
            };

            if (score.id) {
              // Update existing score
              savePromises.push(updateDoc(doc(scoresRef, score.id), scoreData));
            } else {
              // Add new score
              savePromises.push(
                addDoc(scoresRef, scoreData).then(docRef => {
                  // Update the local state with the new ID
                  setStudents(prevStudents => {
                    return prevStudents.map(s => {
                      if (s.id === student.id) {
                        const updatedScores = {
                          ...s.scores,
                          [assessmentId]: {
                            ...s.scores[assessmentId],
                            id: docRef.id,
                            unsaved: false
                          }
                        };
                        return { ...s, scores: updatedScores };
                      }
                      return s;
                    });
                  });
                })
              );
            }
          }
        });
      });

      if (savePromises.length > 0) {
        await Promise.all(savePromises);

        // Mark all scores as saved
        setStudents(prevStudents => {
          return prevStudents.map(student => {
            if (!student.scores) return student;

            const updatedScores = {};
            Object.entries(student.scores).forEach(([assessmentId, score]) => {
              updatedScores[assessmentId] = {
                ...score,
                unsaved: false
              };
            });

            return {
              ...student,
              scores: updatedScores
            };
          });
        });

        setSnackbarMessage('All scores saved successfully');
      } else {
        setSnackbarMessage('No changes to save');
      }

      setSnackbarVisible(true);
    } catch (err) {
      console.error('Error saving scores:', err);
      setSnackbarMessage('Failed to save scores');
      setSnackbarVisible(true);
    } finally {
      setLoading(false);
    }
  };



  // Open add assessment dialog
  const openAddAssessmentDialog = () => {
    setAssessmentFormData({
      title: '',
      description: '',
      points: '',
      type: 'assignment',
      dueDate: new Date().toISOString()
    });
    setAddAssessmentDialogVisible(true);
  };

  // Open edit assessment dialog
  const openEditAssessmentDialog = (assessment) => {
    setSelectedAssessment(assessment);
    setAssessmentFormData({
      title: assessment.title || '',
      description: assessment.description || '',
      points: assessment.points ? assessment.points.toString() : '',
      type: assessment.type || 'assignment',
      dueDate: assessment.dueDate || new Date().toISOString()
    });
    setEditAssessmentDialogVisible(true);
  };

  // Handle filter change
  const handleFilterChange = (filter) => {
    setSelectedFilter(filter);
    fetchStudentsAndAssessments();
  };

  // Render assessment list
  const renderAssessmentList = () => (
    <View>
      <View style={styles.totalPointsContainer}>
        <Text style={styles.totalPointsText}>
          Total Points: {totalPoints}/100
        </Text>
        <Text style={styles.remainingPointsText}>
          Remaining: {remainingPoints} points
        </Text>
      </View>

      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        style={styles.filterContainer}
      >
        {assessmentTypes.map((type) => (
          <Button
            key={type}
            mode={selectedFilter === type ? "contained" : "outlined"}
            onPress={() => handleFilterChange(type)}
            style={styles.filterButton}
            labelStyle={styles.filterButtonLabel}
          >
            {type === 'all' ? 'All Types' : type.charAt(0).toUpperCase() + type.slice(1)}
          </Button>
        ))}
      </ScrollView>

      {assessments.map((assessment) => (
        <Card key={assessment.id} style={styles.assessmentCard}>
          <Card.Content>
            <View style={styles.assessmentHeader}>
              <View>
                <Title style={styles.assessmentTitle}>{assessment.title}</Title>
                <Text style={styles.assessmentType}>
                  {assessment.type.charAt(0).toUpperCase() + assessment.type.slice(1)}
                </Text>
              </View>
              <Text style={styles.assessmentPoints}>{assessment.points} points</Text>
            </View>
            {assessment.description && (
              <Paragraph>{assessment.description}</Paragraph>
            )}
            <View style={styles.assessmentActions}>
              <Button
                mode="outlined"
                onPress={() => openEditAssessmentDialog(assessment)}
                compact
                style={{ marginRight: 8 }}
              >
                Edit
              </Button>
              <Button
                mode="outlined"
                onPress={() => handleDeleteAssessment(assessment.id)}
                compact
                buttonColor="#ffebee"
                textColor="#d32f2f"
              >
                Delete
              </Button>
            </View>
          </Card.Content>
        </Card>
      ))}
    </View>
  );

  // Render student scores table
  const renderStudentScores = () => (
    <View>
      <View style={styles.tableWrapper}>
        <ScrollView horizontal showsHorizontalScrollIndicator={true}>
          <View style={styles.tableContainer}>
            <DataTable style={styles.dataTable}>
              <DataTable.Header style={styles.tableHeader}>
                <DataTable.Title style={styles.rollColumn}>{translate('common.roll') || 'Roll No'}</DataTable.Title>
                <DataTable.Title style={styles.nameColumn}>{translate('common.name') || 'Name'}</DataTable.Title>
                {assessments.map((assessment) => (
                  <DataTable.Title key={assessment.id} numeric style={styles.assessmentColumn}>
                    <View style={styles.assessmentHeaderCell}>
                      <Text style={styles.assessmentColumnTitle} numberOfLines={2}>
                        {assessment.title}
                      </Text>
                      <Text style={styles.assessmentColumnPoints}>
                        ({assessment.points}pts)
                      </Text>
                    </View>
                  </DataTable.Title>
                ))}
                <DataTable.Title numeric style={styles.totalColumn}>{translate('common.total') || 'Total'}</DataTable.Title>
              </DataTable.Header>

              <ScrollView style={styles.tableBody}>
                {students.map((student, index) => (
                  <DataTable.Row
                    key={student.id}
                    style={[
                      styles.studentRow,
                      index % 2 === 1 && styles.studentRowAlternate
                    ]}
                  >
                    <DataTable.Cell style={styles.rollColumn}>{student.rollNumber}</DataTable.Cell>
                    <DataTable.Cell style={styles.nameColumn}>{student.name}</DataTable.Cell>

                    {assessments.map((assessment) => (
                      <DataTable.Cell key={assessment.id} numeric style={styles.assessmentColumn}>
                        <View style={styles.pointsCell}>
                          <TextInput
                            value={student.scores[assessment.id]?.points?.toString() || ''}
                            onChangeText={(text) => handleScoreChange(student.id, assessment.id, text)}
                            keyboardType="numeric"
                            style={[
                              styles.pointsInput,
                              student.scores[assessment.id]?.unsaved && styles.unsavedInput
                            ]}
                            placeholder="0"
                          />
                        </View>
                      </DataTable.Cell>
                    ))}

                    <DataTable.Cell numeric style={styles.totalColumn}>
                      <Text style={styles.percentageText}>
                        {calculatedScores[student.id]?.totalScore ||
                         (Object.keys(student.scores || {}).some(id => student.scores[id]?.unsaved) ?
                          '* Unsaved *' : '-')}
                      </Text>
                    </DataTable.Cell>
                  </DataTable.Row>
                ))}
              </ScrollView>
            </DataTable>
          </View>
        </ScrollView>
      </View>

      <View style={styles.actionButtonsContainer}>
        <Button
          mode="contained"
          onPress={saveAllScores}
          style={[styles.actionButton, styles.saveButton]}
          loading={loading}
          disabled={loading}
          icon="content-save"
        >
          Save Scores
        </Button>

        <Button
          mode="contained"
          onPress={calculateAllScores}
          style={styles.actionButton}
          loading={loading}
          disabled={loading}
          icon="calculator"
        >
          Calculate
        </Button>

        <Button
          mode="contained"
          onPress={() => setSubmitDialogVisible(true)}
          style={[styles.actionButton, styles.submitButton]}
          loading={loading}
          disabled={loading || !Object.keys(calculatedScores).length}
          icon="send-check"
        >
          Submit
        </Button>
      </View>
    </View>
  );

  if (!classId || !className || !sectionName) {
    return null;
  }

  return (
    <SafeAreaView style={styles.safeArea}>
      <StatusBar backgroundColor="#1976d2" barStyle="light-content" />

      {/* Teacher App Header */}
      <TeacherAppHeader
        title={translate('gradeManagement.title') || "Grade Management"}
        subtitle={`${className} - ${sectionName} - ${subject}`}
        onMenuPress={toggleDrawer}
        showBackButton={true}
        onBackPress={() => navigation.goBack()}
        showNotification={true}
      />

      {/* Teacher Sidebar */}
      <TeacherSidebar
        visible={drawerOpen}
        onClose={toggleDrawer}
        navigation={navigation}
        activeSidebarItem={activeSidebarItem}
        setActiveSidebarItem={setActiveSidebarItem}
      />

      {/* Sidebar Backdrop */}
      <SidebarBackdrop
        visible={drawerOpen}
        onPress={toggleDrawer}
        fadeAnim={backdropFadeAnim}
      />

      {/* Main Content */}
      <View style={styles.mainContent}>
        <Card style={styles.headerCard}>
          <Card.Content>
            <View style={styles.headerContent}>
              <View style={styles.headerLeft}>
                <Title>Class {className} - Section {sectionName} - {subject}</Title>
                <Paragraph>
                  Enter points for each assessment. Total points for all assessments must equal 100.
                </Paragraph>
              </View>
            </View>

            <SegmentedButtons
              value={viewMode}
              onValueChange={setViewMode}
              buttons={[
                {
                  value: 'assessments',
                  label: 'Assessments',
                  icon: 'clipboard-list'
                },
                {
                  value: 'results',
                  label: 'View Results',
                  icon: 'chart-bar'
                }
              ]}
              style={{ marginTop: 16 }}
            />
          </Card.Content>
        </Card>

        {loading ? (
          <ActivityIndicator style={styles.loader} />
        ) : error ? (
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>{error}</Text>
            <Button mode="contained" onPress={fetchStudentsAndAssessments}>
              Retry
            </Button>
          </View>
        ) : viewMode === 'assessments' ? (
          <ScrollView>
            {renderAssessmentList()}
            {assessments.length > 0 && renderStudentScores()}
          </ScrollView>
        ) : viewMode === 'submission' ? (
          <TeacherGradeSubmission
            classId={classId}
            className={className}
            sectionName={sectionName}
            subject={subject}
            onSubmissionComplete={() => {
              setViewMode('results');
              setSnackbarMessage('Grades submitted successfully. You can now view the results.');
              setSnackbarVisible(true);
            }}
          />
        ) : (
          <TeacherResultsView
            classId={classId}
            className={className}
            sectionName={sectionName}
            subject={subject}
          />
        )}
      </View>

      {/* Add Assessment FAB - only show in assessments mode */}
      {viewMode === 'assessments' && (
        <FAB
          style={styles.fab}
          icon="plus"
          onPress={openAddAssessmentDialog}
          disabled={remainingPoints <= 0}
        />
      )}

      {/* Add Assessment Dialog */}
      <Portal>
        <Dialog
          visible={addAssessmentDialogVisible}
          onDismiss={() => setAddAssessmentDialogVisible(false)}
        >
          <Dialog.Title>Add New Assessment</Dialog.Title>
          <Dialog.Content style={styles.dialogContent}>
            <TextInput
              label="Assessment Title"
              value={assessmentFormData.title}
              onChangeText={(text) => setAssessmentFormData({...assessmentFormData, title: text})}
              mode="outlined"
              style={styles.dialogInput}
            />
            <TextInput
              label="Description (Optional)"
              value={assessmentFormData.description}
              onChangeText={(text) => setAssessmentFormData({...assessmentFormData, description: text})}
              mode="outlined"
              multiline
              numberOfLines={2}
              style={styles.dialogInput}
            />
            <View style={styles.dialogInput}>
              <Text style={styles.inputLabel}>Assessment Type</Text>
              <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.typeSelector}>
                {assessmentTypes.slice(1).map((type) => (
                  <Button
                    key={type}
                    mode={assessmentFormData.type === type ? "contained" : "outlined"}
                    onPress={() => setAssessmentFormData({...assessmentFormData, type})}
                    style={styles.typeButton}
                    labelStyle={styles.typeButtonLabel}
                  >
                    {type.charAt(0).toUpperCase() + type.slice(1)}
                  </Button>
                ))}
              </ScrollView>
            </View>
            <TextInput
              label={`Points (Max: ${remainingPoints})`}
              value={assessmentFormData.points}
              onChangeText={(text) => setAssessmentFormData({...assessmentFormData, points: text})}
              keyboardType="numeric"
              mode="outlined"
              style={styles.dialogInput}
            />
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={() => setAddAssessmentDialogVisible(false)}>Cancel</Button>
            <Button mode="contained" onPress={handleAddAssessment} loading={loading}>
              Add
            </Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>

      {/* Edit Assessment Dialog */}
      <Portal>
        <Dialog
          visible={editAssessmentDialogVisible}
          onDismiss={() => setEditAssessmentDialogVisible(false)}
        >
          <Dialog.Title>Edit Assessment</Dialog.Title>
          <Dialog.Content style={styles.dialogContent}>
            <TextInput
              label="Assessment Title"
              value={assessmentFormData.title}
              onChangeText={(text) => setAssessmentFormData({...assessmentFormData, title: text})}
              mode="outlined"
              style={styles.dialogInput}
            />
            <TextInput
              label="Description (Optional)"
              value={assessmentFormData.description}
              onChangeText={(text) => setAssessmentFormData({...assessmentFormData, description: text})}
              mode="outlined"
              multiline
              numberOfLines={2}
              style={styles.dialogInput}
            />
            <View style={styles.dialogInput}>
              <Text style={styles.inputLabel}>Assessment Type</Text>
              <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.typeSelector}>
                {assessmentTypes.slice(1).map((type) => (
                  <Button
                    key={type}
                    mode={assessmentFormData.type === type ? "contained" : "outlined"}
                    onPress={() => setAssessmentFormData({...assessmentFormData, type})}
                    style={styles.typeButton}
                    labelStyle={styles.typeButtonLabel}
                  >
                    {type.charAt(0).toUpperCase() + type.slice(1)}
                  </Button>
                ))}
              </ScrollView>
            </View>
            <TextInput
              label="Points"
              value={assessmentFormData.points}
              onChangeText={(text) => setAssessmentFormData({...assessmentFormData, points: text})}
              keyboardType="numeric"
              mode="outlined"
              style={styles.dialogInput}
            />
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={() => setEditAssessmentDialogVisible(false)}>Cancel</Button>
            <Button mode="contained" onPress={handleEditAssessment} loading={loading}>
              Update
            </Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>

      {/* Submit Dialog */}
      <Portal>
        <Dialog
          visible={submitDialogVisible}
          onDismiss={() => setSubmitDialogVisible(false)}
        >
          <Dialog.Title>Submit Grades for Approval</Dialog.Title>
          <Dialog.Content>
            <Paragraph>
              Are you sure you want to submit these grades for approval? Once submitted, the admin will review and approve the grades.
              After approval, students and parents will receive notifications with the grade details.
            </Paragraph>
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={() => setSubmitDialogVisible(false)}>Cancel</Button>
            <Button
              mode="contained"
              onPress={submitGradesForApproval}
              loading={loading}
              disabled={loading}
            >
              Submit
            </Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>

      {/* Snackbar for notifications */}
      <Snackbar
        visible={snackbarVisible}
        onDismiss={() => setSnackbarVisible(false)}
        duration={3000}
        style={styles.snackbar}
      >
        {snackbarMessage}
      </Snackbar>
    </SafeAreaView>
  );
};

export default GradeManagement;