import React, { useState, useEffect } from 'react';
import { View, ScrollView, StyleSheet } from 'react-native';
import { Card, Title, FAB, Portal, Modal, List, Chip, Searchbar, IconButton, Text, Switch } from 'react-native-paper';
import { Calendar as RNCalendar } from 'react-native-calendars';
import { db } from '../../config/firebase';
import { collection, addDoc, query, getDocs, doc, updateDoc, deleteDoc, where, orderBy } from 'firebase/firestore';
import CustomButton from '../../components/common/CustomButton';
import CustomInput from '../../components/common/CustomInput';
import EthiopianDatePicker from '../../components/common/EthiopianDatePicker';
import { useLanguage } from '../../context/LanguageContext';
import { useEthiopianCalendar } from '../../hooks/useEthiopianCalendar';
import EthiopianCalendar from '../../utils/EthiopianCalendar';

const CalendarSystem = () => {
  const { language } = useLanguage();
  const { formatDate, formatShortDate } = useEthiopianCalendar();
  const [events, setEvents] = useState([]);
  const [selectedDate, setSelectedDate] = useState('');
  const [visible, setVisible] = useState(false);
  const [viewMode, setViewMode] = useState('month'); // month, week, day
  const [loading, setLoading] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState(null);

  const [formData, setFormData] = useState({
    title: '',
    description: '',
    type: 'event', // event, exam, holiday, meeting
    startDate: new Date().toISOString(),
    endDate: new Date().toISOString(),
    allDay: false,
    location: '',
    organizer: '',
    participants: [],
    recurring: {
      isRecurring: false,
      frequency: 'none', // none, daily, weekly, monthly
      endDate: '',
    },
    reminder: {
      enabled: false,
      timing: '30', // minutes before event
    },
    category: 'general', // general, academic, sports, cultural
    visibility: 'public', // public, private, specific roles
    attachments: [],
    status: 'scheduled', // scheduled, ongoing, completed, cancelled
  });

  useEffect(() => {
    fetchEvents();
  }, []);

  const fetchEvents = async () => {
    try {
      const eventsRef = collection(db, 'events');
      const querySnapshot = await getDocs(eventsRef);

      const eventsData = [];
      querySnapshot.forEach((doc) => {
        eventsData.push({ id: doc.id, ...doc.data() });
      });

      setEvents(eventsData);
    } catch (error) {
      console.error('Error fetching events:', error);
    }
  };

  const handleAddEvent = async () => {
    try {
      setLoading(true);
      const eventsRef = collection(db, 'events');

      // Create event
      const event = {
        ...formData,
        createdAt: new Date().toISOString(),
        createdBy: 'admin', // TODO: Get from auth context
      };

      await addDoc(eventsRef, event);

      // Create notifications if reminder is enabled
      if (formData.reminder.enabled) {
        const notificationsRef = collection(db, 'notifications');
        await addDoc(notificationsRef, {
          title: `Reminder: ${formData.title}`,
          message: formData.description,
          type: 'reminder',
          priority: 'normal',
          schedule: {
            immediate: false,
            scheduledDate: new Date(new Date(formData.startDate).getTime() - formData.reminder.timing * 60000).toISOString(),
          },
          targetAudience: formData.participants,
          channels: {
            inApp: true,
            push: true,
          },
          createdAt: new Date().toISOString(),
          createdBy: 'admin',
        });
      }

      setVisible(false);
      resetForm();
      fetchEvents();
    } catch (error) {
      console.error('Error creating event:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateEvent = async () => {
    try {
      setLoading(true);
      const eventRef = doc(db, 'events', selectedEvent.id);
      await updateDoc(eventRef, {
        ...formData,
        updatedAt: new Date().toISOString(),
      });

      setVisible(false);
      resetForm();
      fetchEvents();
    } catch (error) {
      console.error('Error updating event:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteEvent = async (eventId) => {
    try {
      await deleteDoc(doc(db, 'events', eventId));
      fetchEvents();
    } catch (error) {
      console.error('Error deleting event:', error);
    }
  };

  const resetForm = () => {
    setFormData({
      title: '',
      description: '',
      type: 'event',
      startDate: new Date().toISOString(),
      endDate: new Date().toISOString(),
      allDay: false,
      location: '',
      organizer: '',
      participants: [],
      recurring: {
        isRecurring: false,
        frequency: 'none',
        endDate: '',
      },
      reminder: {
        enabled: false,
        timing: '30',
      },
      category: 'general',
      visibility: 'public',
      attachments: [],
      status: 'scheduled',
    });
    setSelectedEvent(null);
  };

  const toggleParticipant = (userId) => {
    const participants = formData.participants.includes(userId)
      ? formData.participants.filter(id => id !== userId)
      : [...formData.participants, userId];
    setFormData({ ...formData, participants });
  };

  const getMarkedDates = () => {
    const marked = {};
    events.forEach(event => {
      const startDate = event.startDate.split('T')[0];
      const endDate = event.endDate.split('T')[0];

      marked[startDate] = {
        marked: true,
        dotColor: getCategoryColor(event.category),
      };

      // Mark dates between start and end
      if (startDate !== endDate) {
        const start = new Date(startDate);
        const end = new Date(endDate);
        for (let d = start; d <= end; d.setDate(d.getDate() + 1)) {
          const dateString = d.toISOString().split('T')[0];
          marked[dateString] = {
            marked: true,
            dotColor: getCategoryColor(event.category),
          };
        }
      }
    });
    return marked;
  };

  const getCategoryColor = (category) => {
    switch (category) {
      case 'academic': return '#4CAF50';
      case 'sports': return '#2196F3';
      case 'cultural': return '#FF6B6B';
      default: return '#9C27B0';
    }
  };

  const getEventTypeIcon = (type) => {
    switch (type) {
      case 'event': return 'calendar';
      case 'exam': return 'pencil';
      case 'holiday': return 'beach';
      case 'meeting': return 'account-group';
      default: return 'calendar';
    }
  };

  // Format date using Ethiopian calendar
  const formatDateTime = (dateString) => {
    const date = new Date(dateString);
    return formatDate(date) + ' ' + date.toLocaleTimeString();
  };

  const getDayEvents = (date) => {
    return events.filter(event => {
      const eventStart = new Date(event.startDate);
      const eventEnd = new Date(event.endDate);
      const selectedDate = new Date(date);
      return selectedDate >= eventStart && selectedDate <= eventEnd;
    });
  };

  return (
    <View style={styles.container}>
      <View style={styles.calendarContainer}>
        <RNCalendar
          onDayPress={(day) => setSelectedDate(day.dateString)}
          markedDates={{
            ...getMarkedDates(),
            [selectedDate]: {
              selected: true,
              marked: getMarkedDates()[selectedDate]?.marked,
              dotColor: getMarkedDates()[selectedDate]?.dotColor,
            },
          }}
          theme={{
            selectedDayBackgroundColor: '#2196F3',
            todayTextColor: '#2196F3',
            arrowColor: '#2196F3',
          }}
        />

        <View style={styles.viewModeContainer}>
          {['month', 'week', 'day'].map((mode) => (
            <Chip
              key={mode}
              selected={viewMode === mode}
              onPress={() => setViewMode(mode)}
              style={styles.chip}
            >
              {mode.charAt(0).toUpperCase() + mode.slice(1)}
            </Chip>
          ))}
        </View>
      </View>

      <View style={styles.eventsContainer}>
        <Title>Events for {selectedDate || 'Today'}</Title>
        <ScrollView>
          {getDayEvents(selectedDate || new Date().toISOString()).map((event) => (
            <Card
              key={event.id}
              style={[styles.eventCard, { borderLeftColor: getCategoryColor(event.category) }]}
              onPress={() => {
                setSelectedEvent(event);
                setFormData({
                  title: event.title,
                  description: event.description,
                  type: event.type,
                  startDate: event.startDate,
                  endDate: event.endDate,
                  allDay: event.allDay,
                  location: event.location,
                  organizer: event.organizer,
                  participants: event.participants,
                  recurring: event.recurring,
                  reminder: event.reminder,
                  category: event.category,
                  visibility: event.visibility,
                  attachments: event.attachments,
                  status: event.status,
                });
                setVisible(true);
              }}
            >
              <Card.Content>
                <View style={styles.eventHeader}>
                  <View style={styles.eventTitleContainer}>
                    <IconButton
                      icon={getEventTypeIcon(event.type)}
                      size={20}
                      color={getCategoryColor(event.category)}
                    />
                    <Title>{event.title}</Title>
                  </View>
                  <Chip mode="outlined">{event.status}</Chip>
                </View>

                <Text style={styles.eventDescription}>{event.description}</Text>

                <View style={styles.eventDetails}>
                  <Text>
                    {event.allDay
                      ? 'All Day'
                      : `${formatDateTime(event.startDate)} - ${formatDateTime(event.endDate)}`}
                  </Text>
                  {event.location && (
                    <Text style={styles.eventLocation}>
                      Location: {event.location}
                    </Text>
                  )}
                </View>

                {event.recurring.isRecurring && (
                  <Chip icon="repeat" style={styles.chip}>
                    {event.recurring.frequency}
                  </Chip>
                )}

                {event.reminder.enabled && (
                  <Chip icon="bell" style={styles.chip}>
                    Reminder: {event.reminder.timing} min before
                  </Chip>
                )}
              </Card.Content>
            </Card>
          ))}
        </ScrollView>
      </View>

      <Portal>
        <Modal
          visible={visible}
          onDismiss={() => {
            setVisible(false);
            resetForm();
          }}
          contentContainerStyle={styles.modalContent}
        >
          <ScrollView>
            <Title>{selectedEvent ? 'Edit Event' : 'Create Event'}</Title>

            <CustomInput
              label="Title"
              value={formData.title}
              onChangeText={(text) => setFormData({ ...formData, title: text })}
            />

            <CustomInput
              label="Description"
              value={formData.description}
              onChangeText={(text) => setFormData({ ...formData, description: text })}
              multiline
              numberOfLines={3}
            />

            <List.Section title="Event Type">
              {['event', 'exam', 'holiday', 'meeting'].map((type) => (
                <List.Item
                  key={type}
                  title={type.charAt(0).toUpperCase() + type.slice(1)}
                  onPress={() => setFormData({ ...formData, type })}
                  style={formData.type === type ? styles.selectedItem : null}
                  left={props => <List.Icon {...props} icon={getEventTypeIcon(type)} />}
                />
              ))}
            </List.Section>

            <View style={styles.datePickerContainer}>
              <Text style={styles.datePickerLabel}>Start Date</Text>
              <EthiopianDatePicker
                value={formData.startDate ? new Date(formData.startDate) : new Date()}
                onChange={(date) => setFormData({ ...formData, startDate: date.toISOString() })}
                label="Select Start Date"
                language={language}
              />
            </View>

            <View style={styles.datePickerContainer}>
              <Text style={styles.datePickerLabel}>End Date</Text>
              <EthiopianDatePicker
                value={formData.endDate ? new Date(formData.endDate) : new Date()}
                onChange={(date) => setFormData({ ...formData, endDate: date.toISOString() })}
                label="Select End Date"
                minDate={formData.startDate ? new Date(formData.startDate) : null}
                language={language}
              />
            </View>

            <List.Item
              title="All Day Event"
              onPress={() => setFormData({ ...formData, allDay: !formData.allDay })}
              left={props => (
                <List.Icon
                  {...props}
                  icon={formData.allDay ? 'checkbox-marked' : 'checkbox-blank-outline'}
                />
              )}
            />

            <CustomInput
              label="Location"
              value={formData.location}
              onChangeText={(text) => setFormData({ ...formData, location: text })}
            />

            <CustomInput
              label="Organizer"
              value={formData.organizer}
              onChangeText={(text) => setFormData({ ...formData, organizer: text })}
            />

            <Title style={styles.sectionTitle}>Recurring Event</Title>
            <List.Item
              title="Is Recurring"
              onPress={() => setFormData({
                ...formData,
                recurring: {
                  ...formData.recurring,
                  isRecurring: !formData.recurring.isRecurring,
                },
              })}
              left={props => (
                <List.Icon
                  {...props}
                  icon={formData.recurring.isRecurring ? 'checkbox-marked' : 'checkbox-blank-outline'}
                />
              )}
            />

            {formData.recurring.isRecurring && (
              <>
                <List.Section title="Frequency">
                  {['daily', 'weekly', 'monthly'].map((frequency) => (
                    <List.Item
                      key={frequency}
                      title={frequency.charAt(0).toUpperCase() + frequency.slice(1)}
                      onPress={() => setFormData({
                        ...formData,
                        recurring: { ...formData.recurring, frequency },
                      })}
                      style={formData.recurring.frequency === frequency ? styles.selectedItem : null}
                      left={props => <List.Icon {...props} icon="calendar-repeat" />}
                    />
                  ))}
                </List.Section>

                <CustomInput
                  label="Recurring End Date"
                  value={formData.recurring.endDate}
                  onChangeText={(text) => setFormData({
                    ...formData,
                    recurring: { ...formData.recurring, endDate: text },
                  })}
                />
              </>
            )}

            <Title style={styles.sectionTitle}>Reminder</Title>
            <List.Item
              title="Enable Reminder"
              onPress={() => setFormData({
                ...formData,
                reminder: {
                  ...formData.reminder,
                  enabled: !formData.reminder.enabled,
                },
              })}
              left={props => (
                <List.Icon
                  {...props}
                  icon={formData.reminder.enabled ? 'checkbox-marked' : 'checkbox-blank-outline'}
                />
              )}
            />

            {formData.reminder.enabled && (
              <CustomInput
                label="Minutes Before Event"
                value={formData.reminder.timing}
                onChangeText={(text) => setFormData({
                  ...formData,
                  reminder: { ...formData.reminder, timing: text },
                })}
                keyboardType="numeric"
              />
            )}

            <List.Section title="Category">
              {['general', 'academic', 'sports', 'cultural'].map((category) => (
                <List.Item
                  key={category}
                  title={category.charAt(0).toUpperCase() + category.slice(1)}
                  onPress={() => setFormData({ ...formData, category })}
                  style={formData.category === category ? styles.selectedItem : null}
                  left={props => (
                    <List.Icon
                      {...props}
                      icon="tag"
                      color={getCategoryColor(category)}
                    />
                  )}
                />
              ))}
            </List.Section>

            <List.Section title="Visibility">
              {['public', 'private', 'specific roles'].map((visibility) => (
                <List.Item
                  key={visibility}
                  title={visibility.charAt(0).toUpperCase() + visibility.slice(1)}
                  onPress={() => setFormData({ ...formData, visibility })}
                  style={formData.visibility === visibility ? styles.selectedItem : null}
                  left={props => <List.Icon {...props} icon="eye" />}
                />
              ))}
            </List.Section>

            <List.Section title="Status">
              {['scheduled', 'ongoing', 'completed', 'cancelled'].map((status) => (
                <List.Item
                  key={status}
                  title={status.charAt(0).toUpperCase() + status.slice(1)}
                  onPress={() => setFormData({ ...formData, status })}
                  style={formData.status === status ? styles.selectedItem : null}
                  left={props => <List.Icon {...props} icon="calendar-check" />}
                />
              ))}
            </List.Section>

            <View style={styles.modalButtons}>
              <CustomButton
                mode="contained"
                onPress={selectedEvent ? handleUpdateEvent : handleAddEvent}
                loading={loading}
              >
                {selectedEvent ? 'Update' : 'Create'}
              </CustomButton>

              {selectedEvent && (
                <CustomButton
                  mode="outlined"
                  onPress={() => handleDeleteEvent(selectedEvent.id)}
                  style={styles.deleteButton}
                >
                  Delete
                </CustomButton>
              )}

              <CustomButton
                mode="outlined"
                onPress={() => {
                  setVisible(false);
                  resetForm();
                }}
              >
                Cancel
              </CustomButton>
            </View>
          </ScrollView>
        </Modal>
      </Portal>

      <FAB
        style={styles.fab}
        icon="plus"
        onPress={() => setVisible(true)}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  calendarContainer: {
    backgroundColor: 'white',
    padding: 10,
    elevation: 2,
  },
  viewModeContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 10,
  },
  eventsContainer: {
    flex: 1,
    padding: 10,
  },
  eventCard: {
    marginVertical: 5,
    borderLeftWidth: 5,
  },
  eventHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  eventTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  eventDescription: {
    marginVertical: 10,
  },
  eventDetails: {
    marginTop: 10,
  },
  eventLocation: {
    marginTop: 5,
    color: '#666',
  },
  modalContent: {
    backgroundColor: 'white',
    padding: 20,
    margin: 20,
    borderRadius: 10,
    maxHeight: '80%',
  },
  selectedItem: {
    backgroundColor: '#e8f4f8',
  },
  sectionTitle: {
    fontSize: 18,
    marginTop: 20,
    marginBottom: 10,
  },
  chip: {
    margin: 4,
  },
  modalButtons: {
    marginTop: 20,
  },
  deleteButton: {
    marginVertical: 10,
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
  },
  datePickerContainer: {
    marginBottom: 16,
  },
  datePickerLabel: {
    fontSize: 14,
    marginBottom: 8,
    color: '#666',
  },
});

export default CalendarSystem;
