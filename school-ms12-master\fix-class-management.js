const fs = require('fs');
const path = require('path');

// Path to the ClassManagement component
const classManagementPath = path.join(__dirname, 'src', 'screens', 'teacher', 'ClassManagement.js');

// Function to fix the ClassManagement component
function fixClassManagement() {
  console.log(`Processing ${classManagementPath}...`);
  
  try {
    let content = fs.readFileSync(classManagementPath, 'utf8');
    
    // Add useLanguage import if not already present
    if (!content.includes('import { useLanguage }')) {
      content = content.replace(
        'import { db, auth } from \'../../config/firebase\';',
        'import { db, auth } from \'../../config/firebase\';\nimport { useLanguage } from \'../../context/LanguageContext\';'
      );
    }
    
    // Add useLanguage hook in the component
    if (!content.includes('const { translate }')) {
      content = content.replace(
        'const ClassManagement = ({ navigation }) => {',
        'const ClassManagement = ({ navigation }) => {\n  const { translate } = useLanguage();'
      );
    }
    
    // Save the modified file
    fs.writeFileSync(classManagementPath, content, 'utf8');
    console.log(`Fixed ClassManagement component at ${classManagementPath}`);
    return true;
  } catch (error) {
    console.error(`Error fixing ClassManagement component: ${error}`);
    return false;
  }
}

// Execute the function
fixClassManagement();
