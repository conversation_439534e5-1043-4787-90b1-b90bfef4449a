const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Get the current directory
const currentDir = __dirname;
console.log(`Current directory: ${currentDir}`);

// Check if firebase-tools is installed
try {
  console.log('Checking for Firebase CLI...');
  execSync('firebase --version', { stdio: 'inherit' });
} catch (error) {
  console.error('Firebase CLI not found. Installing...');
  try {
    execSync('npm install -g firebase-tools', { stdio: 'inherit' });
  } catch (installError) {
    console.error('Failed to install Firebase CLI. Please install it manually with:');
    console.error('npm install -g firebase-tools');
    process.exit(1);
  }
}

// Check if user is logged in to Firebase
try {
  console.log('Checking Firebase login status...');
  execSync('firebase projects:list', { stdio: 'pipe' });
  console.log('Already logged in to Firebase.');
} catch (error) {
  console.log('Not logged in to Firebase. Please log in:');
  try {
    execSync('firebase login', { stdio: 'inherit' });
  } catch (loginError) {
    console.error('Failed to log in to Firebase. Please try again manually.');
    process.exit(1);
  }
}

// Check if firebase.json exists, create if not
const firebaseConfigPath = path.join(__dirname, 'firebase.json');
if (!fs.existsSync(firebaseConfigPath)) {
  console.log('Creating firebase.json configuration file...');
  const firebaseConfig = {
    "firestore": {
      "rules": "firestore.rules",
      "indexes": "firestore.indexes.json"
    }
  };

  fs.writeFileSync(firebaseConfigPath, JSON.stringify(firebaseConfig, null, 2));
  console.log('Created firebase.json configuration file.');
}

// Check if firestore.indexes.json exists, create if not
const firestoreIndexesPath = path.join(__dirname, 'firestore.indexes.json');
if (!fs.existsSync(firestoreIndexesPath)) {
  console.log('Creating firestore.indexes.json file...');
  const firestoreIndexes = {
    "indexes": [],
    "fieldOverrides": []
  };

  fs.writeFileSync(firestoreIndexesPath, JSON.stringify(firestoreIndexes, null, 2));
  console.log('Created firestore.indexes.json file.');
}

// Deploy Firestore security rules
console.log('Deploying Firestore security rules...');
try {
  execSync('firebase deploy --only firestore:rules', { stdio: 'inherit' });
  console.log('Successfully deployed Firestore security rules!');
} catch (error) {
  console.error('Failed to deploy Firestore security rules:', error.message);
  process.exit(1);
}
