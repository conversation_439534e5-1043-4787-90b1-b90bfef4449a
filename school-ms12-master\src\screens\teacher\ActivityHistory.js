import React, { useState, useEffect, useRef } from 'react';
import { View, StyleSheet, FlatList, TouchableOpacity, Alert, ScrollView, Linking, StatusBar, SafeAreaView, Animated } from 'react-native';
import {
  Surface,
  List,
  Divider,
  Text,
  Searchbar,
  Chip,
  ActivityIndicator,
  useTheme,
  Button,
  IconButton,
  Card,
  Title,
  Paragraph,
  Portal,
  Dialog,
  Snackbar
} from 'react-native-paper';
import { useNavigation, useRoute } from '@react-navigation/native';
import { auth, db } from '../../config/firebase';
import { collection, query, where, getDocs, orderBy, limit, Timestamp } from 'firebase/firestore';
import { useLanguage } from '../../context/LanguageContext';
import * as Animatable from 'react-native-animatable';
import { LinearGradient } from 'expo-linear-gradient';
import TeacherAppHeader from '../../components/common/TeacherAppHeader';
import TeacherSidebar from '../../components/common/TeacherSidebar';
import SidebarBackdrop from '../../components/common/SidebarBackdrop';

const ActivityHistory = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { translate, language, isRTL } = useLanguage();
  // No theme needed
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;

  // Sidebar state
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [activeSidebarItem, setActiveSidebarItem] = useState('ActivityHistory');
  const backdropFadeAnim = useRef(new Animated.Value(0)).current;

  // Get activities passed from dashboard if available
  const initialActivities = route.params?.activities || [];

  const [activities, setActivities] = useState(initialActivities);
  const [filteredActivities, setFilteredActivities] = useState(initialActivities);
  const [loading, setLoading] = useState(initialActivities.length === 0);
  const [searchQuery, setSearchQuery] = useState('');
  const [activeFilter, setActiveFilter] = useState('all');

  useEffect(() => {
    if (initialActivities.length === 0) {
      fetchAllActivities();
    }
  }, []);

  const fetchAllActivities = async () => {
    try {
      setLoading(true);

      // Get current teacher ID
      const teacherId = auth.currentUser.uid;

      // Create a timestamp for 30 days ago
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      // Convert to Firestore timestamp
      const thirtyDaysAgoTimestamp = Timestamp.fromDate(thirtyDaysAgo);

      // Initialize snapshots
      let attendanceSnapshot = { docs: [] };
      let assignmentsSnapshot = { docs: [] };
      let examsSnapshot = { docs: [] };
      let messagesSnapshot = { docs: [] };

      // Use try-catch for each query to handle missing indexes
      try {
        // Attendance activities
        attendanceSnapshot = await getDocs(
          query(
            collection(db, 'attendance'),
            where('teacherId', '==', teacherId),
            orderBy('timestamp', 'desc'),
            limit(50)
          )
        );
      } catch (error) {
        console.log('Error fetching attendance activities:', error);
        // Check if it's an index error and show a message to the admin
        if (error.message && error.message.includes('requires an index')) {
          const indexUrl = error.message.match(/https:\/\/console\.firebase\.google\.com[^\s]*/)?.[0];
          if (indexUrl) {
            Alert.alert(
              translate('common.indexNeeded'),
              translate('common.createIndexMessage'),
              [
                {
                  text: translate('common.cancel'),
                  style: 'cancel',
                },
                {
                  text: translate('common.createIndex'),
                  onPress: () => {
                    // Open the URL in a browser
                    Linking.openURL(indexUrl);
                  },
                },
              ]
            );
          }
        }
        // Fallback to simpler query without ordering
        try {
          attendanceSnapshot = await getDocs(
            query(
              collection(db, 'attendance'),
              where('teacherId', '==', teacherId),
              limit(50)
            )
          );
        } catch (fallbackError) {
          console.log('Fallback attendance query failed:', fallbackError);
        }
      }

      try {
        // Assignment activities
        assignmentsSnapshot = await getDocs(
          query(
            collection(db, 'assignments'),
            where('teacherId', '==', teacherId),
            orderBy('timestamp', 'desc'),
            limit(50)
          )
        );
      } catch (error) {
        console.log('Error fetching assignment activities:', error);
        // Check if it's an index error and show a message to the admin
        if (error.message && error.message.includes('requires an index')) {
          const indexUrl = error.message.match(/https:\/\/console\.firebase\.google\.com[^\s]*/)?.[0];
          if (indexUrl) {
            Alert.alert(
              translate('common.indexNeeded'),
              translate('common.createIndexMessage'),
              [
                {
                  text: translate('common.cancel'),
                  style: 'cancel',
                },
                {
                  text: translate('common.createIndex'),
                  onPress: () => {
                    // Open the URL in a browser
                    Linking.openURL(indexUrl);
                  },
                },
              ]
            );
          }
        }
        // Fallback to simpler query without ordering
        try {
          assignmentsSnapshot = await getDocs(
            query(
              collection(db, 'assignments'),
              where('teacherId', '==', teacherId),
              limit(50)
            )
          );
        } catch (fallbackError) {
          console.log('Fallback assignments query failed:', fallbackError);
        }
      }

      try {
        // Exam activities
        examsSnapshot = await getDocs(
          query(
            collection(db, 'exams'),
            where('teacherId', '==', teacherId),
            orderBy('timestamp', 'desc'),
            limit(50)
          )
        );
      } catch (error) {
        console.log('Error fetching exam activities:', error);
        // Check if it's an index error and show a message to the admin
        if (error.message && error.message.includes('requires an index')) {
          const indexUrl = error.message.match(/https:\/\/console\.firebase\.google\.com[^\s]*/)?.[0];
          if (indexUrl) {
            Alert.alert(
              translate('common.indexNeeded'),
              translate('common.createIndexMessage'),
              [
                {
                  text: translate('common.cancel'),
                  style: 'cancel',
                },
                {
                  text: translate('common.createIndex'),
                  onPress: () => {
                    // Open the URL in a browser
                    Linking.openURL(indexUrl);
                  },
                },
              ]
            );
          }
        }
        // Fallback to simpler query without ordering
        try {
          examsSnapshot = await getDocs(
            query(
              collection(db, 'exams'),
              where('teacherId', '==', teacherId),
              limit(50)
            )
          );
        } catch (fallbackError) {
          console.log('Fallback exams query failed:', fallbackError);
        }
      }

      try {
        // Message activities
        messagesSnapshot = await getDocs(
          query(
            collection(db, 'messages'),
            where('teacherId', '==', teacherId),
            orderBy('timestamp', 'desc'),
            limit(50)
          )
        );
      } catch (error) {
        console.log('Error fetching message activities:', error);
        // Check if it's an index error and show a message to the admin
        if (error.message && error.message.includes('requires an index')) {
          const indexUrl = error.message.match(/https:\/\/console\.firebase\.google\.com[^\s]*/)?.[0];
          if (indexUrl) {
            Alert.alert(
              translate('common.indexNeeded'),
              translate('common.createIndexMessage'),
              [
                {
                  text: translate('common.cancel'),
                  style: 'cancel',
                },
                {
                  text: translate('common.createIndex'),
                  onPress: () => {
                    // Open the URL in a browser
                    Linking.openURL(indexUrl);
                  },
                },
              ]
            );
          }
        }
        // Fallback to simpler query without ordering
        try {
          messagesSnapshot = await getDocs(
            query(
              collection(db, 'messages'),
              where('teacherId', '==', teacherId),
              limit(50)
            )
          );
        } catch (fallbackError) {
          console.log('Fallback messages query failed:', fallbackError);
        }
      }

      // Format date based on current language and with Ethiopian calendar support
      const formatDate = (date) => {
        if (!date) return '';

        const now = new Date();
        const yesterday = new Date(now);
        yesterday.setDate(yesterday.getDate() - 1);
        const tomorrow = new Date(now);
        tomorrow.setDate(tomorrow.getDate() + 1);

        // Get locale based on language
        const locale = language === 'en' ? 'en-US' : language === 'am' ? 'am-ET' : 'om-ET';

        // Time formatting options
        const timeOptions = { hour: '2-digit', minute: '2-digit' };

        // Format time string
        const timeString = date.toLocaleTimeString(locale, timeOptions);

        // Check if date is today, yesterday, or tomorrow
        if (date.toDateString() === now.toDateString()) {
          return `${translate('common.today')}, ${timeString}`;
        } else if (date.toDateString() === yesterday.toDateString()) {
          return `${translate('common.yesterday')}, ${timeString}`;
        } else if (date.toDateString() === tomorrow.toDateString()) {
          return `${translate('common.tomorrow')}, ${timeString}`;
        } else {
          // For Ethiopian calendar, we would use a custom formatter here
          // This is a placeholder for Ethiopian calendar implementation
          // TODO: Implement proper Ethiopian calendar conversion
          return date.toLocaleDateString(locale, {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
          });
        }
      };

      // Process attendance activities
      const attendanceActivities = attendanceSnapshot.docs.map(doc => {
        const data = doc.data();
        const timestamp = data.timestamp?.toDate() || new Date();
        return {
          id: doc.id,
          type: 'attendance',
          title: `${data.className || translate('teacherDashboard.class')} ${translate('teacherDashboard.attendance')}`,
          description: formatDate(timestamp),
          icon: 'clipboard-check',
          color: '#4CAF50',
          data: data,
          timestamp: timestamp
        };
      });

      // Process assignment activities
      const assignmentActivities = assignmentsSnapshot.docs.map(doc => {
        const data = doc.data();
        const timestamp = data.timestamp?.toDate() || new Date();
        return {
          id: doc.id,
          type: 'assignment',
          title: data.title || translate('teacherDashboard.assignment'),
          description: formatDate(timestamp),
          icon: 'file-document-edit',
          color: '#FF9800',
          data: data,
          timestamp: timestamp
        };
      });

      // Process exam activities
      const examActivities = examsSnapshot.docs.map(doc => {
        const data = doc.data();
        const timestamp = data.timestamp?.toDate() || new Date();
        return {
          id: doc.id,
          type: 'exam',
          title: `${data.title || translate('teacherDashboard.exam')} ${translate('teacherDashboard.results')}`,
          description: formatDate(timestamp),
          icon: 'file-document',
          color: '#9C27B0',
          data: data,
          timestamp: timestamp
        };
      });

      // Process message activities
      const messageActivities = messagesSnapshot.docs.map(doc => {
        const data = doc.data();
        const timestamp = data.timestamp?.toDate() || new Date();
        const recipientType = data.recipientType === 'parent'
          ? translate('teacherDashboard.toParent')
          : translate('teacherDashboard.toStudent');

        return {
          id: doc.id,
          type: 'message',
          title: `${data.subject || translate('teacherDashboard.message')} ${recipientType}`,
          description: formatDate(timestamp),
          icon: 'message-text',
          color: '#2196F3',
          data: data,
          timestamp: timestamp
        };
      });

      // Combine all activities and sort by timestamp (newest first)
      const allActivities = [
        ...attendanceActivities,
        ...assignmentActivities,
        ...examActivities,
        ...messageActivities
      ].sort((a, b) => b.timestamp - a.timestamp);

      setActivities(allActivities);
      setFilteredActivities(allActivities);
    } catch (error) {
      console.error('Error fetching activities:', error);
      Alert.alert(translate('common.error'), translate('teacherDashboard.failedToLoadActivities'));

      // Set empty arrays if there's an error
      setActivities([]);
      setFilteredActivities([]);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (query) => {
    setSearchQuery(query);

    if (!query.trim()) {
      filterActivitiesByType(activeFilter);
      return;
    }

    const lowercaseQuery = query.toLowerCase();
    const filtered = activities.filter(activity =>
      activity.title.toLowerCase().includes(lowercaseQuery) ||
      activity.description.toLowerCase().includes(lowercaseQuery)
    );

    setFilteredActivities(filtered);
  };

  const filterActivitiesByType = (type) => {
    setActiveFilter(type);

    if (type === 'all') {
      setFilteredActivities(activities);
      return;
    }

    const filtered = activities.filter(activity => activity.type === type);
    setFilteredActivities(filtered);
  };

  const handleActivityPress = (activity) => {
    // Navigate to different screens based on activity type
    switch (activity.type) {
      case 'attendance':
        navigation.navigate('AttendanceManagement', {
          screen: 'AttendanceDetails',
          params: { attendanceId: activity.id, attendanceData: activity.data }
        });
        break;
      case 'assignment':
        navigation.navigate('AssessmentManagement', {
          screen: 'AssignmentDetails',
          params: { assignmentId: activity.id, assignmentData: activity.data }
        });
        break;
      case 'exam':
        navigation.navigate('GradeManagement', {
          screen: 'ExamResults',
          params: { examId: activity.id, examData: activity.data }
        });
        break;
      case 'message':
        navigation.navigate('Communication', {
          screen: 'MessageDetails',
          params: { messageId: activity.id, messageData: activity.data }
        });
        break;
      default:
        // If type is unknown, just show an alert
        Alert.alert('Activity Details', `You selected: ${activity.title}`);
    }
  };

  const renderActivityItem = ({ item, index }) => (
    <Animatable.View
      animation="fadeInUp"
      duration={300}
      delay={index * 50}
    >
      <TouchableOpacity
        activeOpacity={0.7}
        onPress={() => showActivityDetails(item)}
      >
        <Surface style={styles.activityItem}>
          <LinearGradient
            colors={[item.color + '10', '#ffffff']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
            style={{ borderRadius: 12 }}
          >
            <List.Item
              title={item.title}
              titleStyle={[
                { fontWeight: '600', color: '#333' },
                isRTL && { textAlign: 'right' }
              ]}
              description={item.description}
              descriptionStyle={isRTL && { textAlign: 'right' }}
              left={props => (
                <View style={{
                  backgroundColor: item.color + '20',
                  borderRadius: 30,
                  margin: 8,
                  padding: 4,
                  justifyContent: 'center',
                  alignItems: 'center'
                }}>
                  <List.Icon {...props} icon={item.icon} color={item.color} />
                </View>
              )}
              right={props => (
                <List.Icon
                  {...props}
                  icon={isRTL ? "chevron-left" : "chevron-right"}
                  color="#757575"
                />
              )}
            />
          </LinearGradient>
        </Surface>
      </TouchableOpacity>
    </Animatable.View>
  );

  const renderEmptyList = () => (
    <View style={styles.emptyContainer}>
      <Text style={styles.emptyText}>
        {searchQuery
          ? translate('common.noSearchResults')
          : translate('teacherDashboard.noRecentActivities')}
      </Text>
    </View>
  );

  // Toggle sidebar
  const toggleDrawer = () => {
    const newValue = !drawerOpen;
    setDrawerOpen(newValue);

    // Animate backdrop
    Animated.timing(backdropFadeAnim, {
      toValue: newValue ? 0.5 : 0,
      duration: 300,
      useNativeDriver: true,
    }).start();
  };

  // Start animations when component mounts
  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      })
    ]).start();
  }, []);

  // Activity detail dialog
  const [selectedActivity, setSelectedActivity] = useState(null);
  const [detailDialogVisible, setDetailDialogVisible] = useState(false);
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');

  const showActivityDetails = (activity) => {
    setSelectedActivity(activity);
    setDetailDialogVisible(true);
  };

  const renderActivityDetailDialog = () => {
    if (!selectedActivity) return null;

    return (
      <Dialog
        visible={detailDialogVisible}
        onDismiss={() => setDetailDialogVisible(false)}
        style={styles.dialog}
      >
        <LinearGradient
          colors={[selectedActivity.color + '80', selectedActivity.color + '30']}
          style={styles.dialogHeader}
        >
          <Dialog.Title style={styles.dialogTitle}>
            {selectedActivity.title}
          </Dialog.Title>
        </LinearGradient>
        <Dialog.Content style={styles.dialogContent}>
          <Animatable.View animation="fadeIn" duration={300}>
            <Paragraph style={styles.dialogDate}>
              {selectedActivity.description}
            </Paragraph>

            <View style={styles.dialogDetails}>
              <Title style={styles.dialogSubtitle}>
                {translate('activities.details')}
              </Title>

              {selectedActivity.data && Object.entries(selectedActivity.data)
                .filter(([key]) => !['id', 'timestamp'].includes(key))
                .map(([key, value]) => {
                  // Skip complex objects and arrays
                  if (typeof value === 'object') return null;

                  return (
                    <View key={key} style={styles.detailRow}>
                      <Text style={styles.detailLabel}>
                        {key.charAt(0).toUpperCase() + key.slice(1)}:
                      </Text>
                      <Text style={styles.detailValue}>
                        {String(value)}
                      </Text>
                    </View>
                  );
                })}
            </View>
          </Animatable.View>
        </Dialog.Content>
        <Dialog.Actions style={styles.dialogActions}>
          <Button
            mode="outlined"
            onPress={() => setDetailDialogVisible(false)}
            style={styles.dialogButton}
          >
            {translate('common.close')}
          </Button>
          <Button
            mode="contained"
            onPress={() => {
              setDetailDialogVisible(false);
              handleActivityPress(selectedActivity);
            }}
            style={[styles.dialogButton, {backgroundColor: selectedActivity.color}]}
          >
            {translate('activities.goToRelated')}
          </Button>
        </Dialog.Actions>
      </Dialog>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar backgroundColor={'#1976d2'} barStyle="light-content" />

      {/* Teacher App Header */}
      <TeacherAppHeader
        title={translate('teacherDashboard.activityHistory')}
        onMenuPress={toggleDrawer}
        showBackButton={true}
        showNotification={true}
      />

      {/* Teacher Sidebar */}
      <TeacherSidebar
        visible={drawerOpen}
        onClose={toggleDrawer}
        navigation={navigation}
        activeSidebarItem={activeSidebarItem}
        setActiveSidebarItem={setActiveSidebarItem}
      />

      {/* Sidebar Backdrop */}
      <SidebarBackdrop
        visible={drawerOpen}
        onPress={toggleDrawer}
        fadeAnim={backdropFadeAnim}
      />

      {/* Main Content */}
      <View style={[styles.mainContent, isRTL && styles.mainContentRTL]}>
        <Animatable.View
          animation="fadeIn"
          duration={500}
          style={styles.headerCard}
        >
          <Card>
            <Card.Content>
              <View style={styles.headerContent}>
                <View>
                  <Title style={[styles.headerTitle, isRTL && styles.textRTL]}>
                    {translate('teacherDashboard.activityHistory')}
                  </Title>
                  <Paragraph style={[styles.headerSubtitle, isRTL && styles.textRTL]}>
                    {translate('teacherDashboard.recentActivitiesDescription')}
                  </Paragraph>
                </View>
                <IconButton
                  icon="refresh"
                  size={24}
                  color={'#1976d2'}
                  onPress={fetchAllActivities}
                  style={styles.refreshButton}
                />
              </View>
            </Card.Content>
          </Card>
        </Animatable.View>

        <Animatable.View
          animation="fadeInUp"
          duration={500}
          delay={100}
          style={styles.searchContainer}
        >
          <Searchbar
            placeholder={translate('common.search')}
            onChangeText={handleSearch}
            value={searchQuery}
            style={[styles.searchbar, isRTL && styles.searchbarRTL]}
            iconColor={'#1976d2'}
            placeholderTextColor="#888"
          />
        </Animatable.View>

        <Animatable.View
          animation="fadeInUp"
          duration={500}
          delay={200}
          style={styles.filterContainer}
        >
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={[styles.filterScroll, isRTL && styles.filterScrollRTL]}
          >
            <Chip
              selected={activeFilter === 'all'}
              onPress={() => filterActivitiesByType('all')}
              style={styles.filterChip}
              selectedColor={'#1976d2'}
              mode={activeFilter === 'all' ? 'flat' : 'outlined'}
            >
              {translate('common.all')}
            </Chip>
            <Chip
              selected={activeFilter === 'attendance'}
              onPress={() => filterActivitiesByType('attendance')}
              style={styles.filterChip}
              selectedColor="#4CAF50"
              mode={activeFilter === 'attendance' ? 'flat' : 'outlined'}
              icon="clipboard-check"
            >
              {translate('teacherDashboard.attendance')}
            </Chip>
            <Chip
              selected={activeFilter === 'assignment'}
              onPress={() => filterActivitiesByType('assignment')}
              style={styles.filterChip}
              selectedColor="#FF9800"
              mode={activeFilter === 'assignment' ? 'flat' : 'outlined'}
              icon="file-document-edit"
            >
              {translate('teacherDashboard.assignments')}
            </Chip>
            <Chip
              selected={activeFilter === 'exam'}
              onPress={() => filterActivitiesByType('exam')}
              style={styles.filterChip}
              selectedColor="#9C27B0"
              mode={activeFilter === 'exam' ? 'flat' : 'outlined'}
              icon="file-document"
            >
              {translate('teacherDashboard.examinations')}
            </Chip>
            <Chip
              selected={activeFilter === 'message'}
              onPress={() => filterActivitiesByType('message')}
              style={styles.filterChip}
              selectedColor="#2196F3"
              mode={activeFilter === 'message' ? 'flat' : 'outlined'}
              icon="message-text"
            >
              {translate('teacherDashboard.messages')}
            </Chip>
          </ScrollView>
        </Animatable.View>

        {loading ? (
          <Animatable.View
            animation="fadeIn"
            duration={500}
            style={styles.loadingContainer}
          >
            <Surface style={styles.loadingCard}>
              <LinearGradient
                colors={['#1976d2' + '20', '#1976d2' + '10']}
                style={styles.loadingGradient}
              >
                <ActivityIndicator size="large" color={'#1976d2'} />
                <Text style={styles.loadingText}>{translate('common.loading')}</Text>
              </LinearGradient>
            </Surface>
          </Animatable.View>
        ) : (
          <FlatList
            data={filteredActivities}
            renderItem={renderActivityItem}
            keyExtractor={item => item.id}
            contentContainerStyle={styles.listContent}
            ItemSeparatorComponent={() => <View style={styles.separator} />}
            ListEmptyComponent={renderEmptyList}
            showsVerticalScrollIndicator={false}
          />
        )}
      </View>

      {/* Activity Detail Dialog */}
      <Portal>
        {renderActivityDetailDialog()}
      </Portal>

      {/* Snackbar for notifications */}
      <Snackbar
        visible={snackbarVisible}
        onDismiss={() => setSnackbarVisible(false)}
        duration={3000}
        style={styles.snackbar}
        action={{
          label: translate('common.dismiss'),
          onPress: () => setSnackbarVisible(false),
        }}
      >
        {snackbarMessage}
      </Snackbar>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  mainContent: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: 8,
  },
  mainContentRTL: {
    alignItems: 'flex-end',
  },
  headerCard: {
    marginBottom: 16,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  headerSubtitle: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  textRTL: {
    textAlign: 'right',
  },
  refreshButton: {
    margin: 0,
  },
  searchContainer: {
    marginBottom: 12,
  },
  searchbar: {
    elevation: 2,
    borderRadius: 12,
    height: 50,
  },
  searchbarRTL: {
    textAlign: 'right',
  },
  filterContainer: {
    marginBottom: 16,
  },
  filterScroll: {
    paddingVertical: 8,
  },
  filterScrollRTL: {
    flexDirection: 'row-reverse',
  },
  filterChip: {
    marginRight: 8,
    marginBottom: 4,
    paddingHorizontal: 4,
    height: 36,
  },
  listContent: {
    paddingBottom: 16,
  },
  activityItem: {
    borderRadius: 12,
    marginBottom: 8,
    elevation: 2,
    overflow: 'hidden',
  },
  separator: {
    height: 8,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingCard: {
    borderRadius: 16,
    overflow: 'hidden',
    elevation: 4,
    width: '80%',
  },
  loadingGradient: {
    padding: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    marginTop: 16,
    color: '#555',
    fontSize: 16,
    fontWeight: '500',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 40,
  },
  emptyText: {
    color: '#757575',
    fontSize: 16,
    textAlign: 'center',
  },
  dialog: {
    borderRadius: 16,
    backgroundColor: 'white',
    overflow: 'hidden',
  },
  dialogHeader: {
    padding: 16,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
  },
  dialogTitle: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  dialogContent: {
    padding: 16,
  },
  dialogDate: {
    fontSize: 14,
    color: '#666',
    marginBottom: 16,
  },
  dialogDetails: {
    marginTop: 8,
  },
  dialogSubtitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
    color: '#333',
  },
  detailRow: {
    flexDirection: 'row',
    marginBottom: 8,
    flexWrap: 'wrap',
  },
  detailLabel: {
    fontWeight: 'bold',
    color: '#555',
    marginRight: 8,
    width: '40%',
  },
  detailValue: {
    color: '#333',
    flex: 1,
  },
  dialogActions: {
    padding: 8,
    justifyContent: 'flex-end',
  },
  dialogButton: {
    marginHorizontal: 8,
  },
  snackbar: {
    bottom: 16,
  },
});

export default ActivityHistory;
