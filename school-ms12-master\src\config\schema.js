// Define proper schema for admin-related collections
const adminSchemas = {
  schoolConfig: {
    settings: {
      schoolName: String,
      address: Object,
      contactInfo: Object,
      academicYear: {
        startDate: Date,
        endDate: Date
      },
      gradeSettings: Array,
      workingHours: Object
    },
    classes: {
      name: String,
      sections: Array,
      capacity: Number,
      teachers: Array
    },
    subjects: {
      name: String,
      code: String,
      gradeLevel: Number,
      teachers: Array
    }
  }
};

// Export the schemas
module.exports = {
  adminSchemas
};