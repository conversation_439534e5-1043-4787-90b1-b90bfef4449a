import React from 'react';
import {
    Paper,
    Typography,
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableRow,
    Box,
    Grid,
    Rating
} from '@mui/material';
import { useTranslation } from '../../hooks/useTranslation';
import { styled } from '@mui/material/styles';

const StyledPaper = styled(Paper)(({ theme }) => ({
    padding: theme.spacing(3),
    margin: theme.spacing(2),
    '& .MuiRating-root': {
        color: theme.palette.primary.main
    }
}));

const GradeBox = styled(Box)(({ theme, grade }) => {
    const colors = {
        'A': '#4CAF50',
        'B': '#8BC34A',
        'C': '#FFC107',
        'D': '#FF9800',
        'F': '#F44336'
    };

    return {
        padding: theme.spacing(1),
        borderRadius: theme.shape.borderRadius,
        backgroundColor: colors[grade.charAt(0)] || '#grey',
        color: 'white',
        display: 'inline-block',
        minWidth: '2.5em',
        textAlign: 'center'
    };
});

const EthiopianGradeReport = ({
    studentInfo,
    gradeInfo,
    schoolInfo,
    semester,
    teacherComments
}) => {
    const {
        t,
        language,
        formatDate,
        formatNumber,
        getGradeName,
        getSubjectName
    } = useTranslation();

    const calculateAttendancePercentage = (attendance) => {
        return (attendance.present / attendance.total) * 100;
    };

    const renderHeader = () => (
        <Box textAlign="center" mb={3}>
            <Typography variant="h5" gutterBottom>
                {schoolInfo.name[language]}
            </Typography>
            <Typography variant="h6" gutterBottom>
                {t('semester_report_card')} - {t(`academic.terms.${semester}`)}
            </Typography>
            <Typography variant="body1">
                {t('academic_year')}: {gradeInfo.academicYear}
            </Typography>
        </Box>
    );

    const renderStudentInfo = () => (
        <Grid container spacing={2} mb={3}>
            <Grid item xs={12} md={6}>
                <Table size="small">
                    <TableBody>
                        <TableRow>
                            <TableCell>{t('student_name')}:</TableCell>
                            <TableCell>{studentInfo.name[language]}</TableCell>
                        </TableRow>
                        <TableRow>
                            <TableCell>{t('grade_section')}:</TableCell>
                            <TableCell>
                                {getGradeName(gradeInfo.grade)} - {gradeInfo.section}
                            </TableCell>
                        </TableRow>
                        <TableRow>
                            <TableCell>{t('roll_number')}:</TableCell>
                            <TableCell>{studentInfo.rollNumber}</TableCell>
                        </TableRow>
                    </TableBody>
                </Table>
            </Grid>
            <Grid item xs={12} md={6}>
                <Table size="small">
                    <TableBody>
                        <TableRow>
                            <TableCell>{t('class_teacher')}:</TableCell>
                            <TableCell>{gradeInfo.classTeacher[language]}</TableCell>
                        </TableRow>
                        <TableRow>
                            <TableCell>{t('report_date')}:</TableCell>
                            <TableCell>{formatDate(gradeInfo.reportDate)}</TableCell>
                        </TableRow>
                    </TableBody>
                </Table>
            </Grid>
        </Grid>
    );

    const renderAcademicPerformance = () => (
        <Box mb={3}>
            <Typography variant="h6" gutterBottom>
                {t('academic_performance')}
            </Typography>
            <Table>
                <TableHead>
                    <TableRow>
                        <TableCell>{t('subject')}</TableCell>
                        <TableCell align="center">{t('assessment')} (40%)</TableCell>
                        <TableCell align="center">{t('exam')} (60%)</TableCell>
                        <TableCell align="center">{t('total')} (100%)</TableCell>
                        <TableCell align="center">{t('grade')}</TableCell>
                        <TableCell>{t('teacher_remark')}</TableCell>
                    </TableRow>
                </TableHead>
                <TableBody>
                    {gradeInfo.subjects.map((subject, index) => (
                        <TableRow key={index}>
                            <TableCell>{getSubjectName(subject.name)}</TableCell>
                            <TableCell align="center">
                                {formatNumber(subject.assessment)}
                            </TableCell>
                            <TableCell align="center">
                                {formatNumber(subject.exam)}
                            </TableCell>
                            <TableCell align="center">
                                {formatNumber(subject.total)}
                            </TableCell>
                            <TableCell align="center">
                                <GradeBox grade={subject.grade}>
                                    {subject.grade}
                                </GradeBox>
                            </TableCell>
                            <TableCell>
                                {t(subject.remark)}
                            </TableCell>
                        </TableRow>
                    ))}
                </TableBody>
            </Table>
        </Box>
    );

    const renderAttendance = () => (
        <Box mb={3}>
            <Typography variant="h6" gutterBottom>
                {t('attendance_record')}
            </Typography>
            <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                    <Table size="small">
                        <TableBody>
                            <TableRow>
                                <TableCell>{t('school_days')}:</TableCell>
                                <TableCell>
                                    {formatNumber(gradeInfo.attendance.total)}
                                </TableCell>
                            </TableRow>
                            <TableRow>
                                <TableCell>{t('days_present')}:</TableCell>
                                <TableCell>
                                    {formatNumber(gradeInfo.attendance.present)}
                                </TableCell>
                            </TableRow>
                            <TableRow>
                                <TableCell>{t('days_absent')}:</TableCell>
                                <TableCell>
                                    {formatNumber(gradeInfo.attendance.absent)}
                                </TableCell>
                            </TableRow>
                        </TableBody>
                    </Table>
                </Grid>
                <Grid item xs={12} md={6}>
                    <Box textAlign="center">
                        <Typography variant="body1" gutterBottom>
                            {t('attendance_percentage')}
                        </Typography>
                        <Typography variant="h4" color="primary">
                            {formatNumber(calculateAttendancePercentage(gradeInfo.attendance))}%
                        </Typography>
                    </Box>
                </Grid>
            </Grid>
        </Box>
    );

    const renderConductAndEffort = () => (
        <Box mb={3}>
            <Typography variant="h6" gutterBottom>
                {t('conduct_and_effort')}
            </Typography>
            <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                    <Typography variant="subtitle1" gutterBottom>
                        {t('conduct_rating')}
                    </Typography>
                    <Rating 
                        value={gradeInfo.conduct} 
                        readOnly 
                        max={5}
                    />
                    <Typography variant="body2" color="textSecondary">
                        {t(`conduct_${gradeInfo.conduct}`)}
                    </Typography>
                </Grid>
                <Grid item xs={12} md={6}>
                    <Typography variant="subtitle1" gutterBottom>
                        {t('effort_rating')}
                    </Typography>
                    <Rating 
                        value={gradeInfo.effort} 
                        readOnly 
                        max={5}
                    />
                    <Typography variant="body2" color="textSecondary">
                        {t(`effort_${gradeInfo.effort}`)}
                    </Typography>
                </Grid>
            </Grid>
        </Box>
    );

    const renderTeacherComments = () => (
        <Box mb={3}>
            <Typography variant="h6" gutterBottom>
                {t('teacher_comments')}
            </Typography>
            <Paper variant="outlined" sx={{ p: 2 }}>
                <Typography variant="body1">
                    {teacherComments[language]}
                </Typography>
            </Paper>
        </Box>
    );

    const renderSignatures = () => (
        <Grid container spacing={4} mt={4}>
            <Grid item xs={12} md={4}>
                <Typography variant="body2" gutterBottom>
                    {t('class_teacher')}
                </Typography>
                <Box mt={4} borderTop={1} width={200} />
                <Typography variant="body2" mt={1}>
                    {formatDate(new Date())}
                </Typography>
            </Grid>
            <Grid item xs={12} md={4}>
                <Typography variant="body2" gutterBottom>
                    {t('parent_guardian')}
                </Typography>
                <Box mt={4} borderTop={1} width={200} />
                <Typography variant="body2" mt={1}>
                    {t('signature_date')}
                </Typography>
            </Grid>
            <Grid item xs={12} md={4}>
                <Typography variant="body2" gutterBottom>
                    {t('principal')}
                </Typography>
                <Box mt={4} borderTop={1} width={200} />
                <Typography variant="body2" mt={1}>
                    {t('school_seal')}
                </Typography>
            </Grid>
        </Grid>
    );

    return (
        <StyledPaper>
            {renderHeader()}
            {renderStudentInfo()}
            {renderAcademicPerformance()}
            {renderAttendance()}
            {renderConductAndEffort()}
            {renderTeacherComments()}
            {renderSignatures()}
        </StyledPaper>
    );
};

export default EthiopianGradeReport;
