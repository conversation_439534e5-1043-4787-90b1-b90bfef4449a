import React, { useState, useEffect } from 'react';
import { View, ScrollView, StyleSheet } from 'react-native';
import { Card, Title, Text, List, Portal, Modal, ActivityIndicator } from 'react-native-paper';
import { db } from '../../config/firebase';
import { collection, query, where, getDocs } from 'firebase/firestore';
import { useAuth } from '../../context/AuthContext';
import CustomButton from '../../components/common/CustomButton';

const ReportCenter = () => {
  const { user } = useAuth();
  const [classes, setClasses] = useState([]);
  const [selectedClass, setSelectedClass] = useState(null);
  const [students, setStudents] = useState([]);
  const [studentReports, setStudentReports] = useState({});
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [selectedStudent, setSelectedStudent] = useState(null);

  useEffect(() => {
    fetchTeacherClasses();
  }, []);

  const fetchTeacherClasses = async () => {
    try {
      setLoading(true);
      const classesRef = collection(db, 'classes');
      const q = query(classesRef, where('teacherId', '==', user.uid));
      const querySnapshot = await getDocs(q);
      
      const classesData = [];
      querySnapshot.forEach((doc) => {
        classesData.push({ id: doc.id, ...doc.data() });
      });
      
      setClasses(classesData);
    } catch (error) {
      console.error('Error fetching classes:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchStudents = async (classId) => {
    try {
      setLoading(true);
      const studentsRef = collection(db, 'users');
      const q = query(studentsRef, where('classId', '==', classId), where('role', '==', 'student'));
      const querySnapshot = await getDocs(q);
      
      const studentsData = [];
      querySnapshot.forEach((doc) => {
        studentsData.push({ id: doc.id, ...doc.data() });
      });
      
      setStudents(studentsData);
      await fetchStudentReports(classId, studentsData);
    } catch (error) {
      console.error('Error fetching students:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchStudentReports = async (classId, studentsData) => {
    try {
      const gradesRef = collection(db, 'grades');
      const q = query(gradesRef, where('classId', '==', classId));
      const gradesSnapshot = await getDocs(q);
      
      const attendanceRef = collection(db, 'attendance');
      const attQuery = query(attendanceRef, where('classId', '==', classId));
      const attendanceSnapshot = await getDocs(attQuery);

      const reports = {};
      
      // Process for each student
      studentsData.forEach(student => {
        const studentReport = {
          grades: [],
          attendance: {
            present: 0,
            total: 0,
            percentage: 0
          },
          averageScore: 0
        };

        // Process grades
        gradesSnapshot.forEach(doc => {
          const gradeData = doc.data();
          const studentGrade = gradeData.grades.find(g => g.studentId === student.id);
          if (studentGrade) {
            studentReport.grades.push({
              assessmentTitle: gradeData.assessmentTitle,
              score: studentGrade.score,
              maxScore: gradeData.maxScore
            });
          }
        });

        // Calculate average score
        if (studentReport.grades.length > 0) {
          const totalPercentage = studentReport.grades.reduce((acc, grade) => {
            return acc + (grade.score / grade.maxScore) * 100;
          }, 0);
          studentReport.averageScore = totalPercentage / studentReport.grades.length;
        }

        // Process attendance
        attendanceSnapshot.forEach(doc => {
          const attendanceData = doc.data();
          const studentAttendance = attendanceData.records.find(r => r.studentId === student.id);
          if (studentAttendance) {
            studentReport.attendance.total++;
            if (studentAttendance.present) {
              studentReport.attendance.present++;
            }
          }
        });

        // Calculate attendance percentage
        if (studentReport.attendance.total > 0) {
          studentReport.attendance.percentage = 
            (studentReport.attendance.present / studentReport.attendance.total) * 100;
        }

        reports[student.id] = studentReport;
      });

      setStudentReports(reports);
    } catch (error) {
      console.error('Error fetching student reports:', error);
    }
  };

  const handleClassSelect = async (classItem) => {
    setSelectedClass(classItem);
    await fetchStudents(classItem.id);
  };

  const handleStudentSelect = (student) => {
    setSelectedStudent(student);
    setVisible(true);
  };

  const ReportModal = () => {
    if (!selectedStudent || !studentReports[selectedStudent.id]) return null;
    
    const report = studentReports[selectedStudent.id];
    
    return (
      <Portal>
        <Modal
          visible={visible}
          onDismiss={() => setVisible(false)}
          contentContainerStyle={styles.modalContent}
        >
          <ScrollView>
            <Title style={styles.modalTitle}>Student Report Card</Title>
            <Text style={styles.studentName}>{selectedStudent.displayName}</Text>
            <Text style={styles.className}>{selectedClass?.name || 'Class N/A'}</Text>

            <Card style={styles.sectionCard}>
              <Card.Content>
                <Title>Attendance Summary</Title>
                <Text>Present: {report.attendance.present} days</Text>
                <Text>Total Days: {report.attendance.total} days</Text>
                <Text>Attendance Rate: {report.attendance.percentage.toFixed(1)}%</Text>
              </Card.Content>
            </Card>

            <Card style={styles.sectionCard}>
              <Card.Content>
                <Title>Academic Performance</Title>
                <Text>Average Score: {report.averageScore.toFixed(1)}%</Text>
                
                <Title style={styles.gradesTitle}>Assessment Grades</Title>
                {report.grades.map((grade, index) => (
                  <View key={index} style={styles.gradeItem}>
                    <Text>{grade.assessmentTitle}</Text>
                    <Text>Score: {grade.score}/{grade.maxScore} ({((grade.score/grade.maxScore)*100).toFixed(1)}%)</Text>
                  </View>
                ))}
              </Card.Content>
            </Card>
          </ScrollView>

          <CustomButton
            mode="outlined"
            onPress={() => setVisible(false)}
            style={styles.closeButton}
          >
            Close
          </CustomButton>
        </Modal>
      </Portal>
    );
  };

  return (
    <View style={styles.container}>
      <ScrollView>
        <Card style={styles.card}>
          <Card.Content>
            <Title>Report Center</Title>
            {loading ? (
              <ActivityIndicator animating={true} />
            ) : classes.length === 0 ? (
              <Text>No classes assigned to you</Text>
            ) : !selectedClass ? (
              <List.Section>
                <Title>Select a Class</Title>
                {classes.map((classItem) => (
                  <List.Item
                    key={classItem.id}
                    title={classItem.name || 'Unnamed Class'}
                    description={`Grade: ${classItem.grade || 'N/A'} • Subject: ${classItem.subject || 'N/A'}`}
                    onPress={() => handleClassSelect(classItem)}
                    left={props => <List.Icon {...props} icon="book-education" />}
                  />
                ))}
              </List.Section>
            ) : (
              <List.Section>
                <Title>{selectedClass.name} - Student Reports</Title>
                {students.map((student) => (
                  <List.Item
                    key={student.id}
                    title={student.displayName}
                    description={
                      studentReports[student.id] 
                        ? `Average: ${studentReports[student.id].averageScore.toFixed(1)}% • Attendance: ${studentReports[student.id].attendance.percentage.toFixed(1)}%`
                        : 'Loading...'
                    }
                    onPress={() => handleStudentSelect(student)}
                    left={props => <List.Icon {...props} icon="account" />}
                  />
                ))}
                <CustomButton
                  mode="outlined"
                  onPress={() => setSelectedClass(null)}
                  style={styles.backButton}
                >
                  Back to Classes
                </CustomButton>
              </List.Section>
            )}
          </Card.Content>
        </Card>
      </ScrollView>

      <ReportModal />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  card: {
    margin: 10,
    elevation: 2,
  },
  modalContent: {
    backgroundColor: 'white',
    margin: 20,
    padding: 20,
    borderRadius: 8,
    maxHeight: '80%',
  },
  modalTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  studentName: {
    fontSize: 18,
    marginBottom: 5,
  },
  className: {
    fontSize: 16,
    color: '#666',
    marginBottom: 20,
  },
  sectionCard: {
    marginVertical: 10,
  },
  gradesTitle: {
    fontSize: 18,
    marginTop: 15,
    marginBottom: 10,
  },
  gradeItem: {
    marginVertical: 5,
    padding: 10,
    backgroundColor: '#f5f5f5',
    borderRadius: 5,
  },
  closeButton: {
    marginTop: 20,
  },
  backButton: {
    marginTop: 10,
  },
});

export default ReportCenter;
