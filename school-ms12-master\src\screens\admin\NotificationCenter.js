import React, { useState, useEffect, useRef, useCallback } from 'react';
import { View, ScrollView, StyleSheet, TouchableOpacity, RefreshControl, Animated, Platform } from 'react-native';
import { List, Divider, Text, IconButton, Badge, Surface, ActivityIndicator, FAB, Card, Title, Paragraph, Chip, Button, Searchbar, Menu, Portal, Modal, Snackbar, useTheme, Avatar, ProgressBar } from 'react-native-paper';
import NotificationService from '../../services/NotificationService';
import { useLanguage } from '../../context/LanguageContext';
import { format, formatDistanceToNow } from 'date-fns';
import { am, enUS, om } from 'date-fns/locale';
import { SafeAreaView } from 'react-native-safe-area-context';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import * as Animatable from 'react-native-animatable';
import { LinearGradient } from 'expo-linear-gradient';
import AdminAppHeader from '../../components/common/AdminAppHeader';
import AdminSidebar from '../../components/common/AdminSidebar';
import SidebarBackdrop from '../../components/common/SidebarBackdrop';
import ActivityService from '../../services/ActivityService';

const NotificationCenter = ({ navigation }) => {
  // No theme needed
  const { translate, language } = useLanguage();

  // Data state
  const [notifications, setNotifications] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterType, setFilterType] = useState('all'); // 'all', 'unread', 'alert', 'message', 'info'

  // UI state
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [showFilters, setShowFilters] = useState(true);

  // Sidebar state
  const [sidebarVisible, setSidebarVisible] = useState(false);
  const [activeSidebarItem, setActiveSidebarItem] = useState('NotificationCenter');
  const drawerAnim = useRef(new Animated.Value(-300)).current;

  // Detail view state
  const [selectedNotification, setSelectedNotification] = useState(null);
  const [detailModalVisible, setDetailModalVisible] = useState(false);

  // Toggle sidebar
  const toggleSidebar = () => {
    const toValue = sidebarVisible ? -300 : 0;
    Animated.timing(drawerAnim, {
      toValue,
      duration: 250,
      useNativeDriver: true,
    }).start();
    setSidebarVisible(!sidebarVisible);
  };

  // Toggle filter visibility
  const toggleFilters = () => {
    setShowFilters(!showFilters);
  };

  // Refresh function for pull-to-refresh
  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      await fetchNotifications();
      setSnackbarMessage(translate('notifications.refreshSuccess') || 'Notifications refreshed');
      setSnackbarVisible(true);
    } catch (error) {
      console.error('Error refreshing notifications:', error);
      setSnackbarMessage(translate('notifications.refreshError') || 'Failed to refresh notifications');
      setSnackbarVisible(true);
    } finally {
      setRefreshing(false);
    }
  }, []);

  // View notification details
  const viewNotificationDetails = (notification) => {
    setSelectedNotification(notification);
    setDetailModalVisible(true);

    // Mark as read if unread
    if (!notification.read) {
      markAsRead(notification.id);
    }
  };

  // Mark all as read
  const markAllAsRead = async () => {
    try {
      setLoading(true);
      const unreadNotifications = notifications.filter(n => !n.read);

      if (unreadNotifications.length === 0) {
        setSnackbarMessage(translate('notifications.allRead') || 'All notifications are already read');
        setSnackbarVisible(true);
        setLoading(false);
        return;
      }

      await Promise.all(unreadNotifications.map(n => NotificationService.markAsRead(n.id)));

      // Update local state
      setNotifications(notifications.map(notification => ({ ...notification, read: true })));

      // Log activity
      await ActivityService.logActivity(
        'admin',
        'notification',
        translate('activities.markAllRead') || 'Marked all notifications as read',
        translate('activities.markAllReadDesc') || `Marked ${unreadNotifications.length} notifications as read`
      );

      setSnackbarMessage(translate('notifications.markedAllRead') || 'All notifications marked as read');
      setSnackbarVisible(true);
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      setSnackbarMessage(translate('notifications.markAllReadError') || 'Failed to mark all notifications as read');
      setSnackbarVisible(true);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // Hide header in this screen as we're using our own
    navigation.setOptions({
      headerShown: false,
    });

    // Set active sidebar item
    setActiveSidebarItem('NotificationCenter');

    // Fetch initial data
    fetchNotifications();
  }, [navigation]);

  const fetchNotifications = async () => {
    try {
      setLoading(true);
      const data = await NotificationService.getAdminNotifications();
      setNotifications(data);
      return data;
    } catch (error) {
      console.error('Error fetching notifications:', error);
      setSnackbarMessage(translate('notifications.fetchError') || 'Failed to load notifications');
      setSnackbarVisible(true);
      return [];
    } finally {
      setLoading(false);
    }
  };

  const markAsRead = async (notificationId) => {
    try {
      await NotificationService.markAsRead(notificationId);

      // Update local state
      setNotifications(notifications.map(notification =>
        notification.id === notificationId
          ? { ...notification, read: true }
          : notification
      ));

      // Log activity
      await ActivityService.logActivity(
        'admin',
        'notification',
        translate('activities.markAsRead') || 'Marked notification as read',
        translate('activities.markAsReadDesc') || `Marked notification as read`
      );

      return true;
    } catch (error) {
      console.error('Error marking notification as read:', error);
      setSnackbarMessage(translate('notifications.markAsReadError') || 'Failed to mark notification as read');
      setSnackbarVisible(true);
      return false;
    }
  };

  // Delete notification
  const deleteNotification = async (notificationId) => {
    try {
      setLoading(true);
      await NotificationService.deleteNotification(notificationId);

      // Update local state
      setNotifications(notifications.filter(notification => notification.id !== notificationId));

      // Close detail modal if open
      if (selectedNotification && selectedNotification.id === notificationId) {
        setDetailModalVisible(false);
        setSelectedNotification(null);
      }

      // Log activity
      await ActivityService.logActivity(
        'admin',
        'notification',
        translate('activities.deleteNotification') || 'Deleted notification',
        translate('activities.deleteNotificationDesc') || `Deleted notification`
      );

      setSnackbarMessage(translate('notifications.deleted') || 'Notification deleted');
      setSnackbarVisible(true);
      return true;
    } catch (error) {
      console.error('Error deleting notification:', error);
      setSnackbarMessage(translate('notifications.deleteError') || 'Failed to delete notification');
      setSnackbarVisible(true);
      return false;
    } finally {
      setLoading(false);
    }
  };

  // Filter notifications based on search query and filter type
  const filteredNotifications = notifications.filter(notification => {
    // Apply search filter
    const matchesSearch = searchQuery.trim() === '' ||
      notification.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      notification.message.toLowerCase().includes(searchQuery.toLowerCase());

    // Apply type filter
    const matchesType =
      filterType === 'all' ||
      (filterType === 'unread' && !notification.read) ||
      (filterType === notification.type);

    return matchesSearch && matchesType;
  });

  const getLocale = () => {
    switch (language) {
      case 'am':
        return am;
      case 'or':
        return om;
      default:
        return enUS;
    }
  };

  const formatDate = (date) => {
    if (!date) return '';
    return format(new Date(date), 'PPp', { locale: getLocale() });
  };

  const formatRelativeTime = (date) => {
    if (!date) return '';
    return formatDistanceToNow(new Date(date), { addSuffix: true, locale: getLocale() });
  };

  const getNotificationIcon = (type) => {
    switch (type) {
      case 'alert':
        return 'alert-circle';
      case 'message':
        return 'message-text';
      case 'info':
        return 'information';
      case 'success':
        return 'check-circle';
      case 'warning':
        return 'alert';
      default:
        return 'bell';
    }
  };

  const getNotificationColor = (type) => {
    switch (type) {
      case 'alert':
        return '#f44336'; // Red
      case 'message':
        return '#2196f3'; // Blue
      case 'info':
        return '#03a9f4'; // Light Blue
      case 'success':
        return '#4caf50'; // Green
      case 'warning':
        return '#ff9800'; // Orange
      default:
        return '#9e9e9e'; // Grey
    }
  };

  const renderNotification = (notification) => (
    <Animatable.View
      key={notification.id}
      animation="fadeIn"
      duration={500}
      delay={200}
    >
      <TouchableOpacity onPress={() => viewNotificationDetails(notification)}>
        <Surface style={[styles.notificationItem, !notification.read && styles.unread]}>
          <List.Item
            title={<Text style={styles.notificationTitle}>{notification.title}</Text>}
            description={<Text numberOfLines={2} style={styles.notificationMessage}>{notification.message}</Text>}
            left={props => (
              <View style={styles.iconContainer}>
                <Avatar.Icon
                  size={40}
                  icon={getNotificationIcon(notification.type)}
                  style={[styles.notificationIcon, { backgroundColor: getNotificationColor(notification.type) + '20' }]}
                  color={getNotificationColor(notification.type)}
                />
                {!notification.read && (
                  <Badge style={styles.badge} size={8} />
                )}
              </View>
            )}
            right={props => (
              <View style={styles.rightContainer}>
                <Text style={styles.timestamp}>{formatRelativeTime(notification.createdAt)}</Text>
                <View style={styles.actionButtons}>
                  {!notification.read && (
                    <IconButton
                      icon="check-circle"
                      size={20}
                      onPress={(e) => {
                        e.stopPropagation();
                        markAsRead(notification.id);
                      }}
                      style={styles.actionButton}
                      color={'#1976d2'}
                    />
                  )}
                  <IconButton
                    icon="delete"
                    size={20}
                    onPress={(e) => {
                      e.stopPropagation();
                      deleteNotification(notification.id);
                    }}
                    style={styles.actionButton}
                    color={'#B00020'}
                  />
                </View>
              </View>
            )}
          />
        </Surface>
      </TouchableOpacity>
    </Animatable.View>
  );

  if (loading && notifications.length === 0) {
    return (
      <SafeAreaView style={styles.container}>
        <AdminAppHeader
          title={translate('notifications.title') || "Notification Center"}
          onMenuPress={toggleSidebar}
        />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={'#1976d2'} />
          <Text style={styles.loadingText}>{translate('common.loading') || "Loading..."}</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Admin App Header */}
      <AdminAppHeader
        title={translate('notifications.title') || "Notification Center"}
        onMenuPress={toggleSidebar}
      />

      {/* Sidebar Backdrop - only visible when sidebar is open */}
      {sidebarVisible && (
        <SidebarBackdrop onPress={toggleSidebar} />
      )}

      {/* Admin Sidebar */}
      <AdminSidebar
        drawerAnim={drawerAnim}
        activeSidebarItem={activeSidebarItem}
        setActiveSidebarItem={setActiveSidebarItem}
        toggleDrawer={toggleSidebar}
      />

      {/* Main Content */}
      <View style={styles.content}>
        {/* Search Bar */}
        <Animatable.View animation="fadeIn" duration={500}>
          <View style={styles.searchContainer}>
            <Searchbar
              placeholder={translate('notifications.searchPlaceholder') || "Search notifications..."}
              onChangeText={query => setSearchQuery(query)}
              value={searchQuery}
              style={styles.searchBar}
              icon="magnify"
              iconColor={'#1976d2'}
            />
            <IconButton
              icon={showFilters ? "filter-variant" : "filter-variant-plus"}
              size={24}
              style={styles.filterToggleButton}
              onPress={toggleFilters}
              color={'#1976d2'}
            />
          </View>
        </Animatable.View>

        {/* Filter Section */}
        {showFilters && (
          <Animatable.View
            animation={showFilters ? "fadeIn" : "fadeOut"}
            duration={300}
            style={styles.filterContainer}
          >
            <View style={styles.filterHeader}>
              <Text style={styles.filterTitle}>{translate('notifications.filters') || "Filters"}</Text>
              <IconButton
                icon="chevron-up"
                size={24}
                onPress={toggleFilters}
                color={'#1976d2'}
              />
            </View>

            <View style={styles.filterChips}>
              <Chip
                selected={filterType === 'all'}
                onPress={() => setFilterType('all')}
                style={styles.filterChip}
                icon="filter-variant"
              >
                {translate('notifications.all') || "All"}
              </Chip>
              <Chip
                selected={filterType === 'unread'}
                onPress={() => setFilterType('unread')}
                style={styles.filterChip}
                icon="email-unread"
              >
                {translate('notifications.unread') || "Unread"}
              </Chip>
              <Chip
                selected={filterType === 'alert'}
                onPress={() => setFilterType('alert')}
                style={styles.filterChip}
                icon="alert-circle"
              >
                {translate('notifications.alerts') || "Alerts"}
              </Chip>
              <Chip
                selected={filterType === 'message'}
                onPress={() => setFilterType('message')}
                style={styles.filterChip}
                icon="message-text"
              >
                {translate('notifications.messages') || "Messages"}
              </Chip>
              <Chip
                selected={filterType === 'info'}
                onPress={() => setFilterType('info')}
                style={styles.filterChip}
                icon="information"
              >
                {translate('notifications.info') || "Info"}
              </Chip>
            </View>
          </Animatable.View>
        )}

        {/* Notification Stats */}
        <View style={styles.statsContainer}>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{notifications.length}</Text>
            <Text style={styles.statLabel}>{translate('notifications.total') || "Total"}</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{notifications.filter(n => !n.read).length}</Text>
            <Text style={styles.statLabel}>{translate('notifications.unread') || "Unread"}</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{notifications.filter(n => n.type === 'alert').length}</Text>
            <Text style={styles.statLabel}>{translate('notifications.alerts') || "Alerts"}</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{notifications.filter(n => n.read).length}</Text>
            <Text style={styles.statLabel}>{translate('notifications.read') || "Read"}</Text>
          </View>
        </View>

        {/* Mark All as Read Button */}
        {notifications.filter(n => !n.read).length > 0 && (
          <Button
            mode="outlined"
            onPress={markAllAsRead}
            style={styles.markAllButton}
            icon="check-all"
            loading={loading}
          >
            {translate('notifications.markAllRead') || "Mark All as Read"}
          </Button>
        )}

        <ScrollView
          style={styles.scrollView}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={['#1976d2']}
              tintColor={'#1976d2'}
            />
          }
        >
          <View style={styles.notificationList}>
            {filteredNotifications.length === 0 ? (
              <Animatable.View animation="fadeIn" duration={500} style={styles.emptyContainer}>
                <IconButton icon="bell-off" size={50} color={'#9e9e9e'} />
                <Text style={styles.emptyText}>
                  {searchQuery || filterType !== 'all' ?
                    (translate('notifications.noMatchingNotifications') || "No matching notifications") :
                    (translate('notifications.noNotifications') || "No notifications yet")}
                </Text>
                <Text style={styles.emptySubtext}>
                  {searchQuery || filterType !== 'all' ?
                    (translate('notifications.tryDifferentFilters') || "Try different search terms or filters") :
                    (translate('notifications.checkBackLater') || "Check back later for new notifications")}
                </Text>
              </Animatable.View>
            ) : (
              filteredNotifications.map(renderNotification)
            )}
          </View>
        </ScrollView>
      </View>

      {/* Floating Action Button */}
      <FAB
        style={styles.fab}
        icon="refresh"
        onPress={fetchNotifications}
        loading={loading}
        color="white"
      />

      {/* Notification Detail Modal */}
      <Portal>
        <Modal
          visible={detailModalVisible}
          onDismiss={() => setDetailModalVisible(false)}
          contentContainerStyle={styles.modalContent}
        >
          {selectedNotification && (
            <ScrollView>
              <View style={styles.modalHeader}>
                <Avatar.Icon
                  size={40}
                  icon={getNotificationIcon(selectedNotification.type)}
                  style={[styles.notificationIcon, { backgroundColor: getNotificationColor(selectedNotification.type) + '20' }]}
                  color={getNotificationColor(selectedNotification.type)}
                />
                <Title style={styles.modalTitle}>{selectedNotification.title}</Title>
              </View>

              <Divider style={styles.modalDivider} />

              <View style={styles.modalBody}>
                <Paragraph style={styles.modalMessage}>{selectedNotification.message}</Paragraph>

                <View style={styles.modalInfo}>
                  <Chip icon="clock" style={styles.modalChip}>
                    {formatDate(selectedNotification.createdAt)}
                  </Chip>
                  <Chip
                    icon={selectedNotification.read ? "eye" : "eye-off"}
                    style={styles.modalChip}
                  >
                    {selectedNotification.read ?
                      (translate('notifications.read') || "Read") :
                      (translate('notifications.unread') || "Unread")}
                  </Chip>
                  <Chip
                    icon={getNotificationIcon(selectedNotification.type)}
                    style={styles.modalChip}
                  >
                    {selectedNotification.type.charAt(0).toUpperCase() + selectedNotification.type.slice(1)}
                  </Chip>
                </View>

                <View style={styles.modalActions}>
                  <Button
                    mode="outlined"
                    onPress={() => setDetailModalVisible(false)}
                    style={styles.modalButton}
                    icon="close"
                  >
                    {translate('common.close') || "Close"}
                  </Button>
                  <Button
                    mode="contained"
                    onPress={() => {
                      deleteNotification(selectedNotification.id);
                    }}
                    style={styles.modalButton}
                    icon="delete"
                    color={'#B00020'}
                  >
                    {translate('common.delete') || "Delete"}
                  </Button>
                </View>
              </View>
            </ScrollView>
          )}
        </Modal>
      </Portal>

      {/* Snackbar for notifications */}
      <Snackbar
        visible={snackbarVisible}
        onDismiss={() => setSnackbarVisible(false)}
        duration={3000}
        action={{
          label: translate('common.dismiss') || 'Dismiss',
          onPress: () => setSnackbarVisible(false),
        }}
        style={styles.snackbar}
      >
        {snackbarMessage}
      </Snackbar>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
    paddingTop: 8,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    margin: 16,
    marginBottom: 8,
  },
  searchBar: {
    flex: 1,
    elevation: 4,
    borderRadius: 25,
    height: 50,
  },
  filterToggleButton: {
    marginLeft: 8,
    backgroundColor: '#E3F2FD',
  },
  filterContainer: {
    marginHorizontal: 16,
    marginBottom: 8,
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 16,
    elevation: 2,
  },
  filterHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  filterTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  filterChips: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 8,
  },
  filterChip: {
    marginRight: 8,
    marginBottom: 8,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginHorizontal: 16,
    marginBottom: 16,
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 16,
    elevation: 2,
  },
  statItem: {
    alignItems: 'center',
    flex: 1,
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2196F3',
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
  },
  markAllButton: {
    marginHorizontal: 16,
    marginBottom: 16,
  },
  scrollView: {
    flex: 1,
  },
  notificationList: {
    padding: 8,
  },
  notificationItem: {
    marginHorizontal: 8,
    marginVertical: 4,
    borderRadius: 8,
    elevation: 2,
    overflow: 'hidden',
  },
  unread: {
    backgroundColor: '#E3F2FD',
  },
  notificationTitle: {
    fontWeight: 'bold',
    fontSize: 16,
  },
  notificationMessage: {
    fontSize: 14,
    color: '#666',
  },
  iconContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
    position: 'relative',
  },
  notificationIcon: {
    margin: 8,
  },
  badge: {
    position: 'absolute',
    top: 0,
    right: 0,
    backgroundColor: '#f44336',
  },
  rightContainer: {
    alignItems: 'flex-end',
    justifyContent: 'space-between',
    height: '100%',
  },
  timestamp: {
    fontSize: 12,
    color: '#666',
    marginRight: 8,
  },
  actionButtons: {
    flexDirection: 'row',
  },
  actionButton: {
    margin: 0,
  },
  markReadButton: {
    margin: 0,
  },
  emptyContainer: {
    padding: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyText: {
    textAlign: 'center',
    marginTop: 16,
    fontSize: 18,
    fontWeight: 'bold',
    color: '#666',
  },
  emptySubtext: {
    textAlign: 'center',
    marginTop: 8,
    fontSize: 14,
    color: '#888',
  },
  fab: {
    position: 'absolute',
    right: 16,
    bottom: 16,
    backgroundColor: '#2196F3',
  },
  modalContent: {
    backgroundColor: 'white',
    margin: 20,
    borderRadius: 8,
    padding: 0,
    maxHeight: '80%',
    overflow: 'hidden',
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#f5f5f5',
  },
  modalTitle: {
    marginLeft: 16,
    flex: 1,
    fontSize: 18,
  },
  modalDivider: {
    height: 1,
  },
  modalBody: {
    padding: 16,
  },
  modalMessage: {
    fontSize: 16,
    lineHeight: 24,
    marginBottom: 16,
  },
  modalInfo: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 16,
  },
  modalChip: {
    marginRight: 8,
    marginBottom: 8,
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  modalButton: {
    marginLeft: 8,
  },
  snackbar: {
    bottom: 16,
  },
});

export default NotificationCenter;
