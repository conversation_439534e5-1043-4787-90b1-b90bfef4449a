import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView, TouchableOpacity, Text, Dimensions } from 'react-native';
import { DataTable as PaperDataTable, Searchbar, Menu, Button, IconButton, Divider, Checkbox, Surface, ActivityIndicator } from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import * as Animatable from 'react-native-animatable';
import { useLanguage } from '../../context/LanguageContext';

const DataTable = ({
  data = [],
  columns = [],
  title,
  loading = false,
  onRowPress,
  onSort,
  onFilter,
  onSearch,
  onSelectionChange,
  onActionPress,
  actions = [],
  selectable = false,
  searchable = true,
  filterable = true,
  sortable = true,
  pagination = true,
  itemsPerPageOptions = [5, 10, 15, 20],
  defaultItemsPerPage = 10,
  emptyStateText = 'No data available',
  emptyStateIcon = 'database-off',
  containerStyle,
  headerStyle,
  rowStyle,
  cellStyle,
}) => {
  const { translate, isRTL } = useLanguage();
  const [searchQuery, setSearchQuery] = useState('');
  const [sortedColumn, setSortedColumn] = useState(null);
  const [sortDirection, setSortDirection] = useState('asc');
  const [page, setPage] = useState(0);
  const [itemsPerPage, setItemsPerPage] = useState(defaultItemsPerPage);
  const [selectedItems, setSelectedItems] = useState([]);
  const [allSelected, setAllSelected] = useState(false);
  const [visibleColumns, setVisibleColumns] = useState(columns.map(col => col.field));
  const [columnMenuVisible, setColumnMenuVisible] = useState(false);
  const [filterMenuVisible, setFilterMenuVisible] = useState(false);
  const [filters, setFilters] = useState({});
  const [filteredData, setFilteredData] = useState(data);
  const screenWidth = Dimensions.get('window').width;
  const isTablet = screenWidth >= 768;

  // Update filtered data when data changes
  useEffect(() => {
    let result = [...data];
    
    // Apply search
    if (searchQuery) {
      result = result.filter(item => {
        return columns.some(column => {
          const value = item[column.field];
          if (value === null || value === undefined) return false;
          return String(value).toLowerCase().includes(searchQuery.toLowerCase());
        });
      });
    }
    
    // Apply filters
    Object.entries(filters).forEach(([field, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        result = result.filter(item => {
          if (typeof value === 'function') {
            return value(item[field]);
          }
          return String(item[field]).toLowerCase().includes(String(value).toLowerCase());
        });
      }
    });
    
    // Apply sorting
    if (sortedColumn) {
      result.sort((a, b) => {
        const aValue = a[sortedColumn];
        const bValue = b[sortedColumn];
        
        if (aValue === bValue) return 0;
        if (aValue === null || aValue === undefined) return 1;
        if (bValue === null || bValue === undefined) return -1;
        
        const comparison = String(aValue).localeCompare(String(bValue));
        return sortDirection === 'asc' ? comparison : -comparison;
      });
    }
    
    setFilteredData(result);
    
    // Reset selection when data changes
    setSelectedItems([]);
    setAllSelected(false);
    
    // Reset to first page when data changes
    setPage(0);
  }, [data, searchQuery, sortedColumn, sortDirection, filters]);

  // Handle search
  const handleSearch = (query) => {
    setSearchQuery(query);
    if (onSearch) {
      onSearch(query);
    }
  };

  // Handle sort
  const handleSort = (field) => {
    if (!sortable) return;
    
    if (sortedColumn === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortedColumn(field);
      setSortDirection('asc');
    }
    
    if (onSort) {
      onSort(field, sortDirection === 'asc' ? 'desc' : 'asc');
    }
  };

  // Handle filter
  const handleFilter = (field, value) => {
    const newFilters = { ...filters, [field]: value };
    setFilters(newFilters);
    
    if (onFilter) {
      onFilter(newFilters);
    }
  };

  // Handle row selection
  const handleRowSelect = (id) => {
    if (!selectable) return;
    
    let newSelectedItems;
    if (selectedItems.includes(id)) {
      newSelectedItems = selectedItems.filter(item => item !== id);
    } else {
      newSelectedItems = [...selectedItems, id];
    }
    
    setSelectedItems(newSelectedItems);
    setAllSelected(newSelectedItems.length === filteredData.length);
    
    if (onSelectionChange) {
      onSelectionChange(newSelectedItems);
    }
  };

  // Handle select all
  const handleSelectAll = () => {
    if (!selectable) return;
    
    if (allSelected) {
      setSelectedItems([]);
      setAllSelected(false);
    } else {
      const allIds = filteredData.map(item => item.id);
      setSelectedItems(allIds);
      setAllSelected(true);
    }
    
    if (onSelectionChange) {
      onSelectionChange(allSelected ? [] : filteredData.map(item => item.id));
    }
  };

  // Handle column visibility toggle
  const handleColumnToggle = (field) => {
    let newVisibleColumns;
    if (visibleColumns.includes(field)) {
      // Don't allow hiding all columns
      if (visibleColumns.length === 1) return;
      newVisibleColumns = visibleColumns.filter(col => col !== field);
    } else {
      newVisibleColumns = [...visibleColumns, field];
    }
    setVisibleColumns(newVisibleColumns);
  };

  // Get visible columns
  const getVisibleColumns = () => {
    return columns.filter(col => visibleColumns.includes(col.field));
  };

  // Get from and to for pagination
  const from = page * itemsPerPage;
  const to = Math.min((page + 1) * itemsPerPage, filteredData.length);

  // Get paginated data
  const paginatedData = filteredData.slice(from, to);

  // Render empty state
  const renderEmptyState = () => (
    <Animatable.View 
      animation="fadeIn" 
      duration={500} 
      style={styles.emptyState}
    >
      <MaterialCommunityIcons 
        name={emptyStateIcon} 
        size={48} 
        color="#BDBDBD" 
      />
      <Text style={styles.emptyStateText}>
        {translate(emptyStateText) || emptyStateText}
      </Text>
    </Animatable.View>
  );

  // Render loading state
  const renderLoadingState = () => (
    <View style={styles.loadingContainer}>
      <ActivityIndicator size="large" />
      <Text style={styles.loadingText}>
        {translate('common.loading') || 'Loading...'}
      </Text>
    </View>
  );

  // Render cell content
  const renderCellContent = (item, column) => {
    if (column.render) {
      return column.render(item);
    }
    
    const value = item[column.field];
    if (value === null || value === undefined) {
      return '-';
    }
    
    return String(value);
  };

  return (
    <Surface style={[styles.container, containerStyle]}>
      {title && (
        <View style={styles.titleContainer}>
          <Text style={styles.title}>{title}</Text>
        </View>
      )}
      
      <View style={styles.toolbarContainer}>
        {searchable && (
          <View style={styles.searchContainer}>
            <Searchbar
              placeholder={translate('common.search') || 'Search'}
              onChangeText={handleSearch}
              value={searchQuery}
              style={styles.searchbar}
              iconColor="#757575"
            />
          </View>
        )}
        
        <View style={styles.actionsContainer}>
          {filterable && (
            <Menu
              visible={filterMenuVisible}
              onDismiss={() => setFilterMenuVisible(false)}
              anchor={
                <IconButton
                  icon="filter-variant"
                  size={20}
                  onPress={() => setFilterMenuVisible(true)}
                  style={styles.actionButton}
                />
              }
            >
              <Menu.Item 
                title={translate('common.clearFilters') || 'Clear Filters'} 
                onPress={() => {
                  setFilters({});
                  setFilterMenuVisible(false);
                }}
              />
              <Divider />
              {columns.map(column => (
                <View key={column.field}>
                  <Menu.Item
                    title={column.title}
                    disabled
                    style={styles.filterMenuItem}
                  />
                  <Searchbar
                    placeholder={translate('common.filterBy', { field: column.title }) || `Filter by ${column.title}`}
                    value={filters[column.field] || ''}
                    onChangeText={(text) => handleFilter(column.field, text)}
                    style={styles.filterSearchbar}
                  />
                </View>
              ))}
            </Menu>
          )}
          
          <Menu
            visible={columnMenuVisible}
            onDismiss={() => setColumnMenuVisible(false)}
            anchor={
              <IconButton
                icon="eye-settings"
                size={20}
                onPress={() => setColumnMenuVisible(true)}
                style={styles.actionButton}
              />
            }
          >
            <Menu.Item 
              title={translate('common.showAllColumns') || 'Show All Columns'} 
              onPress={() => {
                setVisibleColumns(columns.map(col => col.field));
                setColumnMenuVisible(false);
              }}
            />
            <Divider />
            {columns.map(column => (
              <Menu.Item
                key={column.field}
                title={column.title}
                onPress={() => handleColumnToggle(column.field)}
                style={styles.columnMenuItem}
                leadingIcon={() => (
                  <Checkbox
                    status={visibleColumns.includes(column.field) ? 'checked' : 'unchecked'}
                    onPress={() => handleColumnToggle(column.field)}
                  />
                )}
              />
            ))}
          </Menu>
          
          {actions.map((action, index) => (
            <IconButton
              key={index}
              icon={action.icon}
              size={20}
              onPress={() => onActionPress(action.key, selectedItems)}
              style={styles.actionButton}
              disabled={action.requireSelection && selectedItems.length === 0}
            />
          ))}
        </View>
      </View>
      
      <ScrollView horizontal showsHorizontalScrollIndicator={true}>
        <View style={styles.tableContainer}>
          <PaperDataTable>
            <PaperDataTable.Header style={[styles.tableHeader, headerStyle]}>
              {selectable && (
                <PaperDataTable.Title style={[styles.checkboxCell, isRTL && styles.rtlCell]}>
                  <Checkbox
                    status={allSelected ? 'checked' : selectedItems.length > 0 ? 'indeterminate' : 'unchecked'}
                    onPress={handleSelectAll}
                  />
                </PaperDataTable.Title>
              )}
              
              {getVisibleColumns().map(column => (
                <PaperDataTable.Title
                  key={column.field}
                  sortDirection={sortedColumn === column.field ? sortDirection : 'none'}
                  onPress={() => handleSort(column.field)}
                  style={[
                    styles.headerCell, 
                    column.width && { width: column.width },
                    isRTL && styles.rtlCell
                  ]}
                >
                  <View style={styles.headerCellContent}>
                    <Text style={styles.headerCellText}>{column.title}</Text>
                    {sortable && sortedColumn === column.field && (
                      <MaterialCommunityIcons
                        name={sortDirection === 'asc' ? 'arrow-up' : 'arrow-down'}
                        size={16}
                        color="#757575"
                        style={styles.sortIcon}
                      />
                    )}
                  </View>
                </PaperDataTable.Title>
              ))}
            </PaperDataTable.Header>

            {loading ? (
              renderLoadingState()
            ) : paginatedData.length === 0 ? (
              renderEmptyState()
            ) : (
              paginatedData.map((item, index) => (
                <Animatable.View
                  key={item.id || index}
                  animation="fadeIn"
                  duration={300}
                  delay={index * 50}
                >
                  <PaperDataTable.Row
                    onPress={() => onRowPress && onRowPress(item)}
                    style={[
                      styles.tableRow,
                      selectedItems.includes(item.id) && styles.selectedRow,
                      index % 2 === 0 && styles.evenRow,
                      rowStyle
                    ]}
                  >
                    {selectable && (
                      <PaperDataTable.Cell style={[styles.checkboxCell, isRTL && styles.rtlCell]}>
                        <Checkbox
                          status={selectedItems.includes(item.id) ? 'checked' : 'unchecked'}
                          onPress={() => handleRowSelect(item.id)}
                        />
                      </PaperDataTable.Cell>
                    )}
                    
                    {getVisibleColumns().map(column => (
                      <PaperDataTable.Cell
                        key={column.field}
                        style={[
                          styles.cell, 
                          column.width && { width: column.width },
                          column.align && { justifyContent: `flex-${column.align}` },
                          isRTL && styles.rtlCell,
                          cellStyle
                        ]}
                      >
                        {renderCellContent(item, column)}
                      </PaperDataTable.Cell>
                    ))}
                  </PaperDataTable.Row>
                </Animatable.View>
              ))
            )}

            {pagination && filteredData.length > 0 && (
              <PaperDataTable.Pagination
                page={page}
                numberOfPages={Math.ceil(filteredData.length / itemsPerPage)}
                onPageChange={page => setPage(page)}
                label={`${from + 1}-${to} of ${filteredData.length}`}
                showFastPaginationControls
                numberOfItemsPerPageList={itemsPerPageOptions}
                numberOfItemsPerPage={itemsPerPage}
                onItemsPerPageChange={setItemsPerPage}
                selectPageDropdownLabel={'Rows per page'}
              />
            )}
          </PaperDataTable>
        </View>
      </ScrollView>
    </Surface>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 8,
    overflow: 'hidden',
    elevation: 2,
    marginVertical: 8,
  },
  titleContainer: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  toolbarContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  searchContainer: {
    flex: 1,
    marginRight: 8,
  },
  searchbar: {
    height: 40,
    elevation: 0,
    backgroundColor: '#f5f5f5',
  },
  actionsContainer: {
    flexDirection: 'row',
  },
  actionButton: {
    margin: 0,
  },
  tableContainer: {
    minWidth: '100%',
  },
  tableHeader: {
    backgroundColor: '#f5f5f5',
  },
  headerCell: {
    justifyContent: 'center',
  },
  headerCellContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerCellText: {
    fontWeight: 'bold',
  },
  sortIcon: {
    marginLeft: 4,
  },
  tableRow: {
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  selectedRow: {
    backgroundColor: 'rgba(33, 150, 243, 0.05)',
  },
  evenRow: {
    backgroundColor: '#fafafa',
  },
  cell: {
    justifyContent: 'center',
  },
  checkboxCell: {
    width: 48,
    justifyContent: 'center',
  },
  rtlCell: {
    justifyContent: 'flex-end',
  },
  emptyState: {
    padding: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyStateText: {
    marginTop: 16,
    color: '#757575',
    textAlign: 'center',
  },
  loadingContainer: {
    padding: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    marginTop: 16,
    color: '#757575',
  },
  columnMenuItem: {
    height: 48,
  },
  filterMenuItem: {
    backgroundColor: '#f5f5f5',
  },
  filterSearchbar: {
    margin: 8,
    height: 40,
    elevation: 0,
    backgroundColor: '#f5f5f5',
  },
});

export default DataTable;
