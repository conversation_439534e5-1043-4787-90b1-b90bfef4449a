import React, { useState, useEffect, useRef } from 'react';
import { View, ScrollView, StyleSheet, RefreshControl, Animated, Share, Platform } from 'react-native';
import {
  DataTable,
  Card,
  Title,
  Button,
  Menu,
  Divider,
  Searchbar,
  Text,
  Portal,
  Modal,
  ActivityIndicator,
  List,
  Chip,
  Surface,
  Badge,
  useTheme,
  IconButton,
  Avatar,
  ProgressBar,
  Snackbar,
  FAB
} from 'react-native-paper';
import {
  collection,
  query,
  where,
  getDocs
} from 'firebase/firestore';
import { db, auth } from '../../config/firebase';
import * as Animatable from 'react-native-animatable';
import { LinearGradient } from 'expo-linear-gradient';
import { useLanguage } from '../../context/LanguageContext';
import { <PERSON><PERSON><PERSON>, Bar<PERSON>hart } from 'react-native-chart-kit';
import { useNavigation } from '@react-navigation/native';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';
import * as XLSX from 'xlsx';
import AdminAppHeader from '../../components/common/AdminAppHeader';
import AdminSidebar from '../../components/common/AdminSidebar';
import SidebarBackdrop from '../../components/common/SidebarBackdrop';
import SaveResultsButton from '../../components/admin/SaveResultsButton';
import ResultApprovalButton from '../../components/admin/ResultApprovalButton';
import ActivityService from '../../services/ActivityService';
import { sanitizeChartData, sanitizeChartDatasets } from '../../utils/ChartUtils';

const ResultsManagement = () => {
  // No theme needed
  const navigation = useNavigation();
  const { translate, getTextStyle, isRTL } = useLanguage();

  // Results state
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [students, setStudents] = useState([]);
  const [filteredStudents, setFilteredStudents] = useState([]);
  const [classes, setClasses] = useState([]);
  const [sections, setSections] = useState([]);
  const [selectedClass, setSelectedClass] = useState(null);
  const [selectedSection, setSelectedSection] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [classMenuVisible, setClassMenuVisible] = useState(false);
  const [sectionMenuVisible, setSectionMenuVisible] = useState(false);
  const [selectedStudent, setSelectedStudent] = useState(null);
  const [detailsModalVisible, setDetailsModalVisible] = useState(false);
  const [error, setError] = useState(null);
  const [currentPage, setCurrentPage] = useState(0);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [activeTab, setActiveTab] = useState('table'); // 'table', 'charts', 'summary'
  const [sortColumn, setSortColumn] = useState('schoolRank');
  const [sortDirection, setSortDirection] = useState('asc');
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [exportLoading, setExportLoading] = useState(false);
  const [fabOpen, setFabOpen] = useState(false);

  // Animation states
  const [fadeAnim] = useState(new Animated.Value(0));
  const [slideAnim] = useState(new Animated.Value(50));

  // Sidebar state
  const [drawerOpen, setDrawerOpen] = useState(false);
  const drawerAnim = useRef(new Animated.Value(-300)).current;
  const backdropFadeAnim = useRef(new Animated.Value(0)).current;
  const [activeSidebarItem, setActiveSidebarItem] = useState('ResultsManagement');

  // Toggle drawer function
  const toggleDrawer = () => {
    if (drawerOpen) {
      // Close drawer
      Animated.parallel([
        Animated.timing(drawerAnim, {
          toValue: -300,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(backdropFadeAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      // Open drawer
      Animated.parallel([
        Animated.timing(drawerAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(backdropFadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    }
    setDrawerOpen(!drawerOpen);
  };

  // Animation on mount
  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 500,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 500,
        useNativeDriver: true,
      }),
    ]).start();

    // Hide the default header
    navigation.setOptions({
      headerShown: false,
    });
  }, [navigation, fadeAnim, slideAnim]);

  useEffect(() => {
    fetchClasses();
  }, []);

  useEffect(() => {
    if (selectedClass) {
      fetchSections(selectedClass);
      setSections([]); // Clear sections when class changes
      setSelectedSection(null);
    }
  }, [selectedClass]);

  useEffect(() => {
    if (selectedClass) {
      fetchResults();
    }
  }, [selectedClass, selectedSection]);

  const fetchClasses = async () => {
    try {
      setError(null);
      const classesRef = collection(db, 'classes');
      const snapshot = await getDocs(classesRef);
      const classList = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      setClasses(classList);
    } catch (error) {
      console.error('Error fetching classes:', error);
      setError(translate('results.management.errors.fetchClasses'));
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    try {
      await fetchClasses();
      if (selectedClass) {
        await fetchResults();
      }
    } catch (error) {
      console.error('Error refreshing data:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const fetchSections = async (className) => {
    try {
      setError(null);
      const sectionsRef = collection(db, 'sections');
      const q = query(sectionsRef, where('className', '==', className));
      const snapshot = await getDocs(q);
      const sectionList = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      setSections(sectionList);
    } catch (error) {
      console.error('Error fetching sections:', error);
      setError(translate('results.management.errors.fetchSections'));
    }
  };

  const calculateRanks = (students) => {
    if (!students || students.length === 0) return [];

    // Calculate overall school rank - sort by totalPercentage in descending order
    const schoolRanked = [...students].sort((a, b) => b.totalPercentage - a.totalPercentage);
    schoolRanked.forEach((student, index) => {
      student.schoolRank = index + 1;
    });

    // Calculate class rank (across all sections of the same class)
    const classGroups = {};
    students.forEach(student => {
      if (!classGroups[student.className]) {
        classGroups[student.className] = [];
      }
      classGroups[student.className].push(student);
    });

    Object.values(classGroups).forEach(classStudents => {
      const ranked = [...classStudents].sort((a, b) => b.totalPercentage - a.totalPercentage);
      ranked.forEach((student, index) => {
        const studentInMain = schoolRanked.find(s => s.id === student.id);
        if (studentInMain) {
          studentInMain.classRank = index + 1;
        }
      });
    });

    // Calculate section rank
    const sectionGroups = {};
    students.forEach(student => {
      const key = `${student.className}-${student.sectionName}`;
      if (!sectionGroups[key]) {
        sectionGroups[key] = [];
      }
      sectionGroups[key].push(student);
    });

    Object.values(sectionGroups).forEach(sectionStudents => {
      const ranked = [...sectionStudents].sort((a, b) => b.totalPercentage - a.totalPercentage);
      ranked.forEach((student, index) => {
        const studentInMain = schoolRanked.find(s => s.id === student.id);
        if (studentInMain) {
          studentInMain.sectionRank = index + 1;
        }
      });
    });

    // Return students sorted by rank (already sorted in schoolRanked)
    return schoolRanked;
  };

  const fetchResults = async () => {
    try {
      setError(null);
      setLoading(true);
      const usersRef = collection(db, 'users');

      // Build query with filters
      let queryConstraints = [
        where('role', '==', 'student'),
        where('className', '==', selectedClass)
      ];

      if (selectedSection) {
        queryConstraints.push(where('sectionName', '==', selectedSection));
      }

      const q = query(usersRef, ...queryConstraints);
      const snapshot = await getDocs(q);

      if (snapshot.empty) {
        setSnackbarMessage(translate('results.management.messages.noStudentsFound') || 'No students found for the selected class/section');
        setSnackbarVisible(true);
        setStudents([]);
        setFilteredStudents([]);
        setLoading(false);
        return;
      }

      const studentList = await Promise.all(
        snapshot.docs.map(async (doc) => {
          const student = { id: doc.id, ...doc.data() };

          // Fetch all grades for the student
          const gradesRef = collection(db, 'grades');
          const gradesQuery = query(
            gradesRef,
            where('studentId', '==', student.uid),
            where('status', '==', 'published')
          );
          const gradesSnapshot = await getDocs(gradesQuery);

          // Group grades by exam type
          const grades = {
            assignment: [],
            quiz: [],
            midterm: [],
            final: []
          };

          // Group grades by subject
          const subjectGrades = {};

          let totalPoints = 0;
          let totalMaxPoints = 0;

          // Track exam type averages for charts
          const examTypeAverages = {
            assignment: { total: 0, count: 0 },
            quiz: { total: 0, count: 0 },
            midterm: { total: 0, count: 0 },
            final: { total: 0, count: 0 }
          };

          // Track subject averages
          const subjectAverages = {};

          gradesSnapshot.docs.forEach(gradeDoc => {
            const grade = gradeDoc.data();
            if (grade.examType && grades.hasOwnProperty(grade.examType)) {
              const points = Number(grade.points) || 0;
              const maxPoints = Number(grade.maxPoints) || 0;
              const percentage = maxPoints > 0 ? (points / maxPoints) * 100 : 0;
              const subject = grade.subject || 'Unknown Subject';

              // Add to exam type grades
              grades[grade.examType].push({
                id: gradeDoc.id,
                points: points,
                maxPoints: maxPoints,
                percentage: percentage,
                remarks: grade.remarks || '',
                date: grade.timestamp ? new Date(grade.timestamp.toDate()).toLocaleDateString() : 'N/A',
                subject: subject
              });

              // Add to subject grades
              if (!subjectGrades[subject]) {
                subjectGrades[subject] = [];
              }

              subjectGrades[subject].push({
                id: gradeDoc.id,
                examType: grade.examType,
                points: points,
                maxPoints: maxPoints,
                percentage: percentage,
                remarks: grade.remarks || '',
                date: grade.timestamp ? new Date(grade.timestamp.toDate()).toLocaleDateString() : 'N/A'
              });

              // Update subject averages
              if (!subjectAverages[subject]) {
                subjectAverages[subject] = { total: 0, count: 0, totalPoints: 0, totalMaxPoints: 0 };
              }
              subjectAverages[subject].total += percentage;
              subjectAverages[subject].count += 1;
              subjectAverages[subject].totalPoints += points;
              subjectAverages[subject].totalMaxPoints += maxPoints;

              totalPoints += points;
              totalMaxPoints += maxPoints;

              // Update exam type averages
              examTypeAverages[grade.examType].total += percentage;
              examTypeAverages[grade.examType].count += 1;
            }
          });

          // Calculate final subject averages
          const finalSubjectAverages = {};
          Object.keys(subjectAverages).forEach(subject => {
            const avg = subjectAverages[subject];
            if (avg.count > 0) {
              finalSubjectAverages[subject] = {
                average: avg.total / avg.count,
                totalPoints: avg.totalPoints,
                totalMaxPoints: avg.totalMaxPoints,
                percentage: avg.totalMaxPoints > 0 ? (avg.totalPoints / avg.totalMaxPoints) * 100 : 0
              };
            }
          });

          // Calculate grade letter based on percentage
          const totalPercentage = totalMaxPoints > 0 ? (totalPoints / totalMaxPoints) * 100 : 0;
          let gradeLetter = '';

          if (totalPercentage >= 90) gradeLetter = 'A+';
          else if (totalPercentage >= 85) gradeLetter = 'A';
          else if (totalPercentage >= 80) gradeLetter = 'A-';
          else if (totalPercentage >= 75) gradeLetter = 'B+';
          else if (totalPercentage >= 70) gradeLetter = 'B';
          else if (totalPercentage >= 65) gradeLetter = 'B-';
          else if (totalPercentage >= 60) gradeLetter = 'C+';
          else if (totalPercentage >= 55) gradeLetter = 'C';
          else if (totalPercentage >= 50) gradeLetter = 'C-';
          else if (totalPercentage >= 45) gradeLetter = 'D+';
          else if (totalPercentage >= 40) gradeLetter = 'D';
          else gradeLetter = 'F';

          // Calculate exam type averages for charts
          const examTypeAveragesResult = {};
          Object.keys(examTypeAverages).forEach(type => {
            examTypeAveragesResult[type] = examTypeAverages[type].count > 0
              ? examTypeAverages[type].total / examTypeAverages[type].count
              : 0;
          });

          return {
            ...student,
            grades,
            subjectGrades,
            finalSubjectAverages,
            totalPoints,
            totalMaxPoints,
            totalPercentage,
            gradeLetter,
            examTypeAverages: examTypeAveragesResult
          };
        })
      );

      const rankedStudents = calculateRanks(studentList);
      setStudents(rankedStudents);
      setFilteredStudents(sortStudents(rankedStudents, sortColumn, sortDirection));

      // Log activity
      await ActivityService.logActivity({
        type: 'RESULTS_VIEW',
        userId: auth.currentUser?.uid,
        details: {
          className: selectedClass,
          sectionName: selectedSection || 'All Sections',
          studentCount: rankedStudents.length,
          timestamp: new Date().toISOString()
        }
      });

      setSnackbarMessage(
        translate('results.management.messages.resultsLoaded', { count: rankedStudents.length }) ||
        `Loaded results for ${rankedStudents.length} students`
      );
      setSnackbarVisible(true);
    } catch (error) {
      console.error('Error fetching results:', error);
      setError(translate('results.management.errors.fetchResults') || 'Failed to fetch results');
      setSnackbarMessage(translate('messages.errors.network') || 'Network error occurred');
      setSnackbarVisible(true);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (query) => {
    setSearchQuery(query);
    if (!query.trim()) {
      setFilteredStudents(sortStudents(students, sortColumn, sortDirection));
      return;
    }
    const filtered = students.filter(student =>
      student.name?.toLowerCase().includes(query.toLowerCase()) ||
      student.rollNumber?.toLowerCase().includes(query.toLowerCase())
    );
    setFilteredStudents(sortStudents(filtered, sortColumn, sortDirection));
  };

  const sortStudents = (studentsToSort, column, direction) => {
    if (!studentsToSort || studentsToSort.length === 0) return [];

    const sortedStudents = [...studentsToSort].sort((a, b) => {
      let valueA, valueB;

      // Determine the values to compare based on the column
      switch (column) {
        case 'name':
          valueA = a.name?.toLowerCase() || '';
          valueB = b.name?.toLowerCase() || '';
          return direction === 'asc' ? valueA.localeCompare(valueB) : valueB.localeCompare(valueA);

        case 'rollNumber':
          valueA = a.rollNumber?.toLowerCase() || '';
          valueB = b.rollNumber?.toLowerCase() || '';
          return direction === 'asc' ? valueA.localeCompare(valueB) : valueB.localeCompare(valueA);

        case 'className':
          valueA = a.className?.toLowerCase() || '';
          valueB = b.className?.toLowerCase() || '';
          return direction === 'asc' ? valueA.localeCompare(valueB) : valueB.localeCompare(valueA);

        case 'sectionName':
          valueA = a.sectionName?.toLowerCase() || '';
          valueB = b.sectionName?.toLowerCase() || '';
          return direction === 'asc' ? valueA.localeCompare(valueB) : valueB.localeCompare(valueA);

        case 'totalPercentage':
          valueA = a.totalPercentage || 0;
          valueB = b.totalPercentage || 0;
          return direction === 'asc' ? valueA - valueB : valueB - valueA;

        case 'schoolRank':
          valueA = a.schoolRank || 0;
          valueB = b.schoolRank || 0;
          return direction === 'asc' ? valueA - valueB : valueB - valueA;

        case 'classRank':
          valueA = a.classRank || 0;
          valueB = b.classRank || 0;
          return direction === 'asc' ? valueA - valueB : valueB - valueA;

        default:
          return 0;
      }
    });

    return sortedStudents;
  };

  const handleSort = (column) => {
    // If clicking the same column, toggle direction
    if (sortColumn === column) {
      const newDirection = sortDirection === 'asc' ? 'desc' : 'asc';
      setSortDirection(newDirection);
      setFilteredStudents(sortStudents(filteredStudents, column, newDirection));
    } else {
      // If clicking a new column, sort ascending by that column
      setSortColumn(column);
      setSortDirection('asc');
      setFilteredStudents(sortStudents(filteredStudents, column, 'asc'));
    }
  };

  const getSortIcon = (column) => {
    if (sortColumn !== column) return null;
    return sortDirection === 'asc' ? 'arrow-up' : 'arrow-down';
  };

  const calculateAverageForExamType = (grades) => {
    if (!grades || grades.length === 0) return '-';
    const total = grades.reduce((sum, grade) => sum + grade.percentage, 0);
    return (total / grades.length).toFixed(2) + '%';
  };

  const calculateAveragePercentage = (grades) => {
    if (!grades || grades.length === 0) return 0;
    const total = grades.reduce((sum, grade) => sum + grade.percentage, 0);
    return total / grades.length;
  };

  const getProgressColor = (percentage) => {
    if (percentage >= 80) return '#4CAF50';
    if (percentage >= 70) return '#8BC34A';
    if (percentage >= 60) return '#FFC107';
    if (percentage >= 50) return '#FF9800';
    return '#F44336';
  };

  const getRankBadgeStyle = (rank) => {
    if (rank === 1) return styles.firstRankBadge;
    if (rank === 2) return styles.secondRankBadge;
    if (rank === 3) return styles.thirdRankBadge;
    if (rank <= 10) return styles.topTenBadge;
    return styles.normalRankBadge;
  };

  const getPerformanceChipStyle = (percentage) => {
    if (percentage >= 80) return styles.excellentChip;
    if (percentage >= 70) return styles.goodChip;
    if (percentage >= 60) return styles.averageChip;
    if (percentage >= 50) return styles.passChip;
    return styles.failChip;
  };

  // Export results to Excel
  const exportToExcel = async () => {
    try {
      setExportLoading(true);

      if (!filteredStudents || filteredStudents.length === 0) {
        setSnackbarMessage(translate('results.management.messages.noDataToExport') || 'No data to export');
        setSnackbarVisible(true);
        setExportLoading(false);
        return;
      }

      // Create worksheet data
      const wsData = [
        // Header row
        [
          translate('results.management.table.studentName') || 'Student Name',
          translate('results.management.table.rollNumber') || 'Roll Number',
          translate('results.management.table.class') || 'Class',
          translate('results.management.table.section') || 'Section',
          translate('results.management.table.totalMarks') || 'Total Marks',
          translate('results.management.table.percentage') || 'Percentage',
          translate('results.management.table.grade') || 'Grade',
          translate('results.management.rankings.schoolRank') || 'School Rank',
          translate('results.management.rankings.classRank') || 'Class Rank',
          translate('results.management.rankings.sectionRank') || 'Section Rank'
        ]
      ];

      // Add student data rows
      filteredStudents.forEach(student => {
        wsData.push([
          student.name || '',
          student.rollNumber || '',
          student.className || '',
          student.sectionName || '',
          `${student.totalPoints}/${student.totalMaxPoints}`,
          `${student.totalPercentage.toFixed(2)}%`,
          student.gradeLetter || '',
          student.schoolRank || '',
          student.classRank || '',
          student.sectionRank || ''
        ]);
      });

      // Create workbook and worksheet
      const ws = XLSX.utils.aoa_to_sheet(wsData);
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, 'Results');

      // Generate Excel file
      const wbout = XLSX.write(wb, { type: 'base64', bookType: 'xlsx' });

      // Create file name with class and section info
      const fileName = `Results_${selectedClass}${selectedSection ? '_' + selectedSection : ''}_${new Date().toISOString().split('T')[0]}.xlsx`;

      // Save file
      const fileUri = FileSystem.documentDirectory + fileName;
      await FileSystem.writeAsStringAsync(fileUri, wbout, {
        encoding: FileSystem.EncodingType.Base64
      });

      // Share file
      if (Platform.OS === 'ios') {
        await Sharing.shareAsync(fileUri);
      } else {
        await Share.share({
          title: translate('results.management.export.title') || 'Student Results',
          message: translate('results.management.export.message') || 'Student Results Export',
          url: 'file://' + fileUri
        });
      }

      // Log activity
      await ActivityService.logActivity({
        type: 'RESULTS_EXPORT',
        userId: auth.currentUser?.uid,
        details: {
          className: selectedClass,
          sectionName: selectedSection || 'All Sections',
          studentCount: filteredStudents.length,
          timestamp: new Date().toISOString()
        }
      });

      setSnackbarMessage(translate('results.management.messages.exportSuccess') || 'Results exported successfully');
      setSnackbarVisible(true);
    } catch (error) {
      console.error('Error exporting results:', error);
      setSnackbarMessage(translate('results.management.messages.exportError') || 'Failed to export results');
      setSnackbarVisible(true);
    } finally {
      setExportLoading(false);
    }
  };

  const renderStudentDetails = () => {
    if (!selectedStudent) return null;

    return (
      <ScrollView>
        <Animatable.View animation="fadeIn" duration={500}>
          <View style={styles.studentHeaderContainer}>
            <Avatar.Text
              size={80}
              label={selectedStudent.name.substring(0, 2).toUpperCase()}
              style={styles.studentAvatar}
            />
            <View style={styles.studentHeaderInfo}>
              <Title style={getTextStyle(styles.modalTitle)}>{selectedStudent.name}</Title>
              <View style={styles.studentBasicInfo}>
                <Chip icon="card-account-details" style={styles.infoChip}>{translate('results.management.details.rollNumber')}: {selectedStudent.rollNumber}</Chip>
                <Chip icon="school" style={styles.infoChip}>{translate('results.management.details.class')}: {selectedStudent.className}</Chip>
                <Chip icon="account-group" style={styles.infoChip}>{translate('results.management.details.section')}: {selectedStudent.sectionName}</Chip>
              </View>
            </View>
          </View>
        </Animatable.View>

        <Divider style={styles.divider} />

        <Animatable.View animation="fadeInUp" duration={600} delay={100}>
          <Surface style={styles.rankSurface}>
            <LinearGradient
              colors={['#f5f7fa', '#e4e8eb']}
              style={styles.rankGradient}
            >
              <Title style={getTextStyle(styles.rankTitle)}>{translate('results.management.details.ranking')}</Title>
              <View style={styles.rankContainer}>
                <View style={styles.rankItem}>
                  <Badge size={40} style={getRankBadgeStyle(selectedStudent.schoolRank)}>{selectedStudent.schoolRank}</Badge>
                  <Text style={styles.rankLabel}>{translate('results.management.rankings.schoolRank')}</Text>
                </View>
                <View style={styles.rankItem}>
                  <Badge size={40} style={getRankBadgeStyle(selectedStudent.classRank)}>{selectedStudent.classRank}</Badge>
                  <Text style={styles.rankLabel}>{translate('results.management.rankings.classRank')}</Text>
                </View>
                <View style={styles.rankItem}>
                  <Badge size={40} style={getRankBadgeStyle(selectedStudent.sectionRank)}>{selectedStudent.sectionRank}</Badge>
                  <Text style={styles.rankLabel}>{translate('results.management.rankings.sectionRank')}</Text>
                </View>
              </View>
            </LinearGradient>
          </Surface>
        </Animatable.View>

        <Divider style={styles.divider} />

        <Animatable.View animation="fadeInUp" duration={700} delay={200}>
          <Title style={getTextStyle(styles.subtitle)}>{translate('results.management.details.subjectResults') || 'Subject Results'}</Title>

          <List.Section>
            {Object.keys(selectedStudent.subjectGrades || {}).length === 0 ? (
              <View style={styles.noDataContainer}>
                <Text style={styles.noDataText}>{translate('common.noData') || 'No data available'}</Text>
              </View>
            ) : (
              Object.keys(selectedStudent.subjectGrades).map((subject, subjectIndex) => (
                <Animatable.View key={subject} animation="fadeIn" duration={300} delay={subjectIndex * 100}>
                  <List.Accordion
                    title={subject}
                    left={props => <List.Icon {...props} icon="book-open-variant" color="#2196F3" />}
                    titleStyle={[styles.accordionTitle, styles.subjectTitle, getTextStyle()]}
                    style={styles.subjectAccordion}
                    description={
                      selectedStudent.finalSubjectAverages[subject] ?
                      `${translate('results.management.details.average') || 'Average'}: ${selectedStudent.finalSubjectAverages[subject].percentage.toFixed(2)}%` :
                      ''
                    }
                  >
                    {selectedStudent.subjectGrades[subject].map((grade, gradeIndex) => (
                      <Animatable.View key={grade.id} animation="fadeIn" duration={300} delay={gradeIndex * 50}>
                        <List.Item
                          title={getExamTypeTitle(grade.examType)}
                          titleStyle={getTextStyle()}
                          description={
                            <View>
                              <Text style={styles.gradePoints}>
                                {grade.points}/{grade.maxPoints} ({grade.percentage.toFixed(2)}%)
                              </Text>
                              <View style={styles.gradeDetailsRow}>
                                <Text style={styles.gradeDate}>{translate('common.date') || 'Date'}: {grade.date}</Text>
                                {grade.remarks && <Text style={styles.gradeRemarks}>{translate('common.remarks') || 'Remarks'}: {grade.remarks}</Text>}
                              </View>
                              <ProgressBar
                                progress={grade.percentage / 100}
                                color={getProgressColor(grade.percentage)}
                                style={styles.progressBar}
                              />
                            </View>
                          }
                          right={() => (
                            <View style={styles.gradeTypeContainer}>
                              <Badge style={getExamTypeBadgeStyle(grade.examType)}>{getExamTypeShort(grade.examType)}</Badge>
                              <Chip
                                mode="outlined"
                                style={getPerformanceChipStyle(grade.percentage)}
                                labelStyle={{fontSize: 12}}
                              >
                                {grade.percentage.toFixed(1)}%
                              </Chip>
                            </View>
                          )}
                          style={styles.gradeItem}
                        />
                      </Animatable.View>
                    ))}

                    {/* Subject Summary */}
                    {selectedStudent.finalSubjectAverages[subject] && (
                      <Surface style={styles.subjectSummary}>
                        <View style={styles.subjectSummaryContent}>
                          <View style={styles.subjectSummaryRow}>
                            <Text style={[styles.subjectSummaryLabel, getTextStyle()]}>
                              {translate('results.management.details.totalPoints') || 'Total Points'}:
                            </Text>
                            <Text style={styles.subjectSummaryValue}>
                              {selectedStudent.finalSubjectAverages[subject].totalPoints}/
                              {selectedStudent.finalSubjectAverages[subject].totalMaxPoints}
                            </Text>
                          </View>
                          <View style={styles.subjectSummaryRow}>
                            <Text style={[styles.subjectSummaryLabel, getTextStyle()]}>
                              {translate('results.management.details.percentage') || 'Percentage'}:
                            </Text>
                            <Chip
                              mode="outlined"
                              style={getPerformanceChipStyle(selectedStudent.finalSubjectAverages[subject].percentage)}
                            >
                              {selectedStudent.finalSubjectAverages[subject].percentage.toFixed(2)}%
                            </Chip>
                          </View>
                          <ProgressBar
                            progress={selectedStudent.finalSubjectAverages[subject].percentage / 100}
                            color={getProgressColor(selectedStudent.finalSubjectAverages[subject].percentage)}
                            style={styles.subjectProgressBar}
                          />
                        </View>
                      </Surface>
                    )}
                  </List.Accordion>
                </Animatable.View>
              ))
            )}
          </List.Section>
        </Animatable.View>

        <Animatable.View animation="fadeInUp" duration={700} delay={300}>
          <Title style={getTextStyle(styles.subtitle)}>{translate('results.management.details.examTypeResults') || 'Results by Exam Type'}</Title>

          <List.Section>
            <List.Accordion
              title={translate('results.management.examTypes.assignment') || 'Assignments'}
              left={props => <List.Icon {...props} icon="book" color="#2196F3" />}
              titleStyle={styles.accordionTitle}
              style={styles.accordion}
            >
              {selectedStudent.grades.assignment.length === 0 ? (
                <List.Item
                  title={translate('common.noData') || 'No data available'}
                  titleStyle={styles.noDataText}
                />
              ) : (
                selectedStudent.grades.assignment.map((grade, index) => (
                  <Animatable.View key={index} animation="fadeIn" duration={300} delay={index * 100}>
                    <List.Item
                      title={`${grade.subject} - ${translate('results.management.examTypes.assignment') || 'Assignment'}`}
                      description={
                        <View>
                          <Text style={styles.gradePoints}>{grade.points}/{grade.maxPoints} ({grade.percentage.toFixed(2)}%)</Text>
                          <Text style={styles.gradeDate}>{translate('common.date') || 'Date'}: {grade.date}</Text>
                          <ProgressBar progress={grade.percentage / 100} color={getProgressColor(grade.percentage)} style={styles.progressBar} />
                        </View>
                      }
                      right={() => <Chip mode="outlined" style={getPerformanceChipStyle(grade.percentage)}>{grade.remarks || '-'}</Chip>}
                      style={styles.gradeItem}
                    />
                  </Animatable.View>
                ))
              )}
              <List.Item
                title={translate('results.management.details.average') || 'Average'}
                right={() => (
                  <Chip mode="outlined" style={getPerformanceChipStyle(calculateAveragePercentage(selectedStudent.grades.assignment))}>
                    {calculateAverageForExamType(selectedStudent.grades.assignment)}
                  </Chip>
                )}
                style={styles.averageItem}
              />
            </List.Accordion>

            <List.Accordion
              title={translate('results.management.examTypes.quiz') || 'Quizzes'}
              left={props => <List.Icon {...props} icon="pencil" color="#FF9800" />}
              titleStyle={styles.accordionTitle}
              style={styles.accordion}
            >
              {selectedStudent.grades.quiz.length === 0 ? (
                <List.Item
                  title={translate('common.noData') || 'No data available'}
                  titleStyle={styles.noDataText}
                />
              ) : (
                selectedStudent.grades.quiz.map((grade, index) => (
                  <Animatable.View key={index} animation="fadeIn" duration={300} delay={index * 100}>
                    <List.Item
                      title={`${grade.subject} - ${translate('results.management.examTypes.quiz') || 'Quiz'}`}
                      description={
                        <View>
                          <Text style={styles.gradePoints}>{grade.points}/{grade.maxPoints} ({grade.percentage.toFixed(2)}%)</Text>
                          <Text style={styles.gradeDate}>{translate('common.date') || 'Date'}: {grade.date}</Text>
                          <ProgressBar progress={grade.percentage / 100} color={getProgressColor(grade.percentage)} style={styles.progressBar} />
                        </View>
                      }
                      right={() => <Chip mode="outlined" style={getPerformanceChipStyle(grade.percentage)}>{grade.remarks || '-'}</Chip>}
                      style={styles.gradeItem}
                    />
                  </Animatable.View>
                ))
              )}
              <List.Item
                title={translate('results.management.details.average') || 'Average'}
                right={() => (
                  <Chip mode="outlined" style={getPerformanceChipStyle(calculateAveragePercentage(selectedStudent.grades.quiz))}>
                    {calculateAverageForExamType(selectedStudent.grades.quiz)}
                  </Chip>
                )}
                style={styles.averageItem}
              />
            </List.Accordion>

            <List.Accordion
              title={translate('results.management.examTypes.midterm') || 'Midterm Exams'}
              left={props => <List.Icon {...props} icon="file-document" color="#9C27B0" />}
              titleStyle={styles.accordionTitle}
              style={styles.accordion}
            >
              {selectedStudent.grades.midterm.length === 0 ? (
                <List.Item
                  title={translate('common.noData') || 'No data available'}
                  titleStyle={styles.noDataText}
                />
              ) : (
                selectedStudent.grades.midterm.map((grade, index) => (
                  <Animatable.View key={index} animation="fadeIn" duration={300} delay={index * 100}>
                    <List.Item
                      title={`${grade.subject} - ${translate('results.management.examTypes.midterm') || 'Midterm'}`}
                      description={
                        <View>
                          <Text style={styles.gradePoints}>{grade.points}/{grade.maxPoints} ({grade.percentage.toFixed(2)}%)</Text>
                          <Text style={styles.gradeDate}>{translate('common.date') || 'Date'}: {grade.date}</Text>
                          <ProgressBar progress={grade.percentage / 100} color={getProgressColor(grade.percentage)} style={styles.progressBar} />
                        </View>
                      }
                      right={() => <Chip mode="outlined" style={getPerformanceChipStyle(grade.percentage)}>{grade.remarks || '-'}</Chip>}
                      style={styles.gradeItem}
                    />
                  </Animatable.View>
                ))
              )}
              <List.Item
                title={translate('results.management.details.average') || 'Average'}
                right={() => (
                  <Chip mode="outlined" style={getPerformanceChipStyle(calculateAveragePercentage(selectedStudent.grades.midterm))}>
                    {calculateAverageForExamType(selectedStudent.grades.midterm)}
                  </Chip>
                )}
                style={styles.averageItem}
              />
            </List.Accordion>

            <List.Accordion
              title={translate('results.management.examTypes.final') || 'Final Exams'}
              left={props => <List.Icon {...props} icon="file-certificate" color="#4CAF50" />}
              titleStyle={styles.accordionTitle}
              style={styles.accordion}
            >
              {selectedStudent.grades.final.length === 0 ? (
                <List.Item
                  title={translate('common.noData') || 'No data available'}
                  titleStyle={styles.noDataText}
                />
              ) : (
                selectedStudent.grades.final.map((grade, index) => (
                  <Animatable.View key={index} animation="fadeIn" duration={300} delay={index * 100}>
                    <List.Item
                      title={`${grade.subject} - ${translate('results.management.examTypes.final') || 'Final'}`}
                      description={
                        <View>
                          <Text style={styles.gradePoints}>{grade.points}/{grade.maxPoints} ({grade.percentage.toFixed(2)}%)</Text>
                          <Text style={styles.gradeDate}>{translate('common.date') || 'Date'}: {grade.date}</Text>
                          <ProgressBar progress={grade.percentage / 100} color={getProgressColor(grade.percentage)} style={styles.progressBar} />
                        </View>
                      }
                      right={() => <Chip mode="outlined" style={getPerformanceChipStyle(grade.percentage)}>{grade.remarks || '-'}</Chip>}
                      style={styles.gradeItem}
                    />
                  </Animatable.View>
                ))
              )}
              <List.Item
                title={translate('results.management.details.average') || 'Average'}
                right={() => (
                  <Chip mode="outlined" style={getPerformanceChipStyle(calculateAveragePercentage(selectedStudent.grades.final))}>
                    {calculateAverageForExamType(selectedStudent.grades.final)}
                  </Chip>
                )}
                style={styles.averageItem}
              />
            </List.Accordion>
          </List.Section>
        </Animatable.View>

        <Animatable.View animation="fadeInUp" duration={800} delay={300}>
          <Surface style={styles.totalCard}>
            <LinearGradient
              colors={['#e3f2fd', '#bbdefb']}
              style={styles.totalCardGradient}
            >
              <Card.Content>
                <Title style={getTextStyle(styles.totalCardTitle)}>{translate('results.management.details.overallPerformance')}</Title>
                <View style={styles.totalPointsContainer}>
                  <Text style={styles.totalPointsLabel}>{translate('results.management.details.totalPoints')}:</Text>
                  <Text style={styles.totalPointsValue}>{selectedStudent.totalPoints}/{selectedStudent.totalMaxPoints}</Text>
                </View>
                <View style={styles.overallContainer}>
                  <Text style={styles.overallLabel}>{translate('results.management.details.overallPercentage')}:</Text>
                  <Chip mode="outlined" style={getPerformanceChipStyle(selectedStudent.totalPercentage)}>
                    {selectedStudent.totalPercentage.toFixed(2)}%
                  </Chip>
                </View>
                <ProgressBar
                  progress={selectedStudent.totalPercentage / 100}
                  color={getProgressColor(selectedStudent.totalPercentage)}
                  style={styles.overallProgressBar}
                />
              </Card.Content>
            </LinearGradient>
          </Surface>
        </Animatable.View>
      </ScrollView>
    );
  };

  // const calculateAveragePercentage = (grades) => {
  //   if (!grades || grades.length === 0) return 0;
  //   const total = grades.reduce((sum, grade) => sum + grade.percentage, 0);
  //   return total / grades.length;
  // };

  // const getProgressColor = (percentage) => {
  //   if (percentage >= 90) return '#4CAF50';
  //   if (percentage >= 75) return '#2196F3';
  //   if (percentage >= 60) return '#FFC107';
  //   if (percentage >= 40) return '#FF9800';
  //   return '#F44336';
  // };

  // Render tab navigation
  const renderTabNavigation = () => (
    <View style={styles.tabContainer}>
      <Animatable.View animation="fadeIn" duration={500}>
        <View style={styles.tabRow}>
          <Button
            mode={activeTab === 'table' ? 'contained' : 'outlined'}
            onPress={() => setActiveTab('table')}
            style={styles.tabButton}
          >
            {translate('results.management.tabs.table')}
          </Button>
          <Button
            mode={activeTab === 'charts' ? 'contained' : 'outlined'}
            onPress={() => setActiveTab('charts')}
            style={styles.tabButton}
          >
            {translate('results.management.tabs.charts')}
          </Button>
          <Button
            mode={activeTab === 'summary' ? 'contained' : 'outlined'}
            onPress={() => setActiveTab('summary')}
            style={styles.tabButton}
          >
            {translate('results.management.tabs.summary')}
          </Button>
        </View>
      </Animatable.View>
    </View>
  );

  return (
    <View style={styles.mainContainer}>
      {/* Sidebar */}
      <AdminSidebar
        drawerAnim={drawerAnim}
        activeSidebarItem={activeSidebarItem}
        setActiveSidebarItem={setActiveSidebarItem}
        toggleDrawer={toggleDrawer}
      />

      {/* Backdrop */}
      <SidebarBackdrop
        visible={drawerOpen}
        onPress={toggleDrawer}
        fadeAnim={backdropFadeAnim}
      />

      {/* Admin App Header */}
      <AdminAppHeader
        title={translate('results.management.title') || "Results Management"}
        onMenuPress={toggleDrawer}
      />

      <Animated.View
        style={[
          styles.contentContainer,
          { opacity: fadeAnim, transform: [{ translateY: slideAnim }] },
          isRTL && styles.rtlContainer
        ]}
      >
        <Animatable.View animation="fadeInDown" duration={800}>
          <Surface style={styles.filterCard} elevation={4}>
            <LinearGradient
              colors={['#f5f7fa', '#e4e8eb']}
              style={styles.cardGradient}
            >
              <Card.Content>
                <Title style={[styles.cardTitle, getTextStyle()]}>{translate('results.management.title') || 'Results Management'}</Title>
                <View style={[styles.filterRow, isRTL && { flexDirection: 'row-reverse' }]}>
                  <Menu
                    visible={classMenuVisible}
                    onDismiss={() => setClassMenuVisible(false)}
                    anchor={
                      <Button
                        mode="outlined"
                        onPress={() => setClassMenuVisible(true)}
                        style={styles.filterButton}
                        icon="school"
                        labelStyle={getTextStyle()}
                      >
                        {selectedClass || translate('results.management.filters.selectClass') || 'Select Class'}
                      </Button>
                    }
                  >
                    {classes.map(cls => (
                      <Menu.Item
                        key={cls.id}
                        onPress={() => {
                          setSelectedClass(cls.name);
                          setSelectedSection(null);
                          setClassMenuVisible(false);
                        }}
                        title={cls.name}
                      />
                    ))}
                  </Menu>

                  <Menu
                    visible={sectionMenuVisible}
                    onDismiss={() => setSectionMenuVisible(false)}
                    anchor={
                      <Button
                        mode="outlined"
                        onPress={() => setSectionMenuVisible(true)}
                        style={styles.filterButton}
                        disabled={!selectedClass}
                        icon="account-group"
                        labelStyle={getTextStyle()}
                      >
                        {selectedSection || translate('results.management.filters.selectSection') || 'Select Section'}
                      </Button>
                    }
                  >
                    <Menu.Item
                      onPress={() => {
                        setSelectedSection(null);
                        setSectionMenuVisible(false);
                      }}
                      title={translate('results.management.filters.allSections') || 'All Sections'}
                    />
                    {sections.map(section => (
                      <Menu.Item
                        key={section.id}
                        onPress={() => {
                          setSelectedSection(section.name);
                          setSectionMenuVisible(false);
                        }}
                        title={section.name}
                      />
                    ))}
                  </Menu>
                </View>

                <Searchbar
                  placeholder={translate('results.management.filters.searchPlaceholder') || 'Search by name or roll number...'}
                  onChangeText={handleSearch}
                  value={searchQuery}
                  style={[styles.searchBar, isRTL && { textAlign: 'right' }]}
                  icon="magnify"
                />

                {selectedClass && (
                  <View style={styles.actionButtonsContainer}>
                    <Button
                      mode="contained"
                      icon="file-export"
                      style={styles.actionButton}
                      onPress={exportToExcel}
                      loading={exportLoading}
                      disabled={exportLoading || filteredStudents.length === 0}
                      labelStyle={getTextStyle()}
                    >
                      {translate('results.management.export.button') || 'Export Results'}
                    </Button>

                    {filteredStudents.length > 0 && (
                      <View style={styles.resultsActionButtons}>
                        <SaveResultsButton
                          classId={classId}
                          className={selectedClass}
                          sectionName={selectedSection}
                          students={filteredStudents}
                          onSuccess={() => {
                            setSnackbarMessage(translate('results.management.messages.resultsSaved') || 'Results saved successfully');
                            setSnackbarVisible(true);
                            // Refresh the data after saving
                            fetchResults();
                          }}
                        />

                        <ResultApprovalButton
                          classId={classId}
                          className={selectedClass}
                          sectionName={selectedSection}
                          students={filteredStudents}
                          onSuccess={() => {
                            setSnackbarMessage(translate('results.management.messages.resultsApproved') || 'Results approved and notifications sent');
                            setSnackbarVisible(true);
                            // Refresh the data after approval
                            fetchResults();
                          }}
                        />
                      </View>
                    )}
                  </View>
                )}
              </Card.Content>
            </LinearGradient>
          </Surface>
        </Animatable.View>

      {renderTabNavigation()}

      {loading ? (
        <Animatable.View animation="fadeIn" duration={500} style={styles.loaderContainer}>
          <ActivityIndicator size="large" color="#2196F3" style={styles.loader} />
          <Text style={styles.loadingText}>{translate('common.loading')}</Text>
        </Animatable.View>
      ) : activeTab === 'table' ? (
        <Animatable.View animation="fadeIn" duration={800}>
          <ScrollView
            refreshControl={
              <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
            }
          >
            <DataTable style={styles.dataTable}>
              <DataTable.Header style={styles.tableHeader}>
                <DataTable.Title
                  style={getTextStyle()}
                  onPress={() => handleSort('rollNumber')}
                  sortDirection={sortColumn === 'rollNumber' ? sortDirection : undefined}
                >
                  <View style={styles.headerCell}>
                    <Text>{translate('results.management.table.rollNumber')}</Text>
                    {getSortIcon('rollNumber') && <IconButton icon={getSortIcon('rollNumber')} size={16} />}
                  </View>
                </DataTable.Title>
                <DataTable.Title
                  style={getTextStyle()}
                  onPress={() => handleSort('name')}
                  sortDirection={sortColumn === 'name' ? sortDirection : undefined}
                >
                  <View style={styles.headerCell}>
                    <Text>{translate('results.management.table.studentName')}</Text>
                    {getSortIcon('name') && <IconButton icon={getSortIcon('name')} size={16} />}
                  </View>
                </DataTable.Title>
                <DataTable.Title
                  style={getTextStyle()}
                  onPress={() => handleSort('className')}
                  sortDirection={sortColumn === 'className' ? sortDirection : undefined}
                >
                  <View style={styles.headerCell}>
                    <Text>{translate('results.management.table.class')}</Text>
                    {getSortIcon('className') && <IconButton icon={getSortIcon('className')} size={16} />}
                  </View>
                </DataTable.Title>
                <DataTable.Title
                  style={getTextStyle()}
                  onPress={() => handleSort('sectionName')}
                  sortDirection={sortColumn === 'sectionName' ? sortDirection : undefined}
                >
                  <View style={styles.headerCell}>
                    <Text>{translate('results.management.table.section')}</Text>
                    {getSortIcon('sectionName') && <IconButton icon={getSortIcon('sectionName')} size={16} />}
                  </View>
                </DataTable.Title>
                <DataTable.Title
                  numeric
                  style={getTextStyle()}
                  onPress={() => handleSort('totalPercentage')}
                  sortDirection={sortColumn === 'totalPercentage' ? sortDirection : undefined}
                >
                  <View style={styles.headerCell}>
                    <Text>{translate('results.management.table.percentage')}</Text>
                    {getSortIcon('totalPercentage') && <IconButton icon={getSortIcon('totalPercentage')} size={16} />}
                  </View>
                </DataTable.Title>
                <DataTable.Title
                  numeric
                  style={getTextStyle()}
                  onPress={() => handleSort('schoolRank')}
                  sortDirection={sortColumn === 'schoolRank' ? sortDirection : undefined}
                >
                  <View style={styles.headerCell}>
                    <Text>{translate('results.management.table.schoolRank')}</Text>
                    {getSortIcon('schoolRank') && <IconButton icon={getSortIcon('schoolRank')} size={16} />}
                  </View>
                </DataTable.Title>
                <DataTable.Title
                  numeric
                  style={getTextStyle()}
                  onPress={() => handleSort('classRank')}
                  sortDirection={sortColumn === 'classRank' ? sortDirection : undefined}
                >
                  <View style={styles.headerCell}>
                    <Text>{translate('results.management.table.classRank')}</Text>
                    {getSortIcon('classRank') && <IconButton icon={getSortIcon('classRank')} size={16} />}
                  </View>
                </DataTable.Title>
                <DataTable.Title style={getTextStyle()}>
                  {translate('results.management.table.actions')}
                </DataTable.Title>
              </DataTable.Header>

              {filteredStudents.length === 0 ? (
                <View style={styles.noDataContainer}>
                  <Text style={styles.noDataText}>{translate('common.noData')}</Text>
                </View>
              ) : (
                filteredStudents.map((student, index) => (
                  <Animatable.View
                    key={student.id}
                    animation="fadeInUp"
                    duration={300}
                    delay={index * 50}
                  >
                    <DataTable.Row style={index % 2 === 0 ? styles.evenRow : styles.oddRow}>
                      <DataTable.Cell style={getTextStyle()}>{student.rollNumber}</DataTable.Cell>
                      <DataTable.Cell style={getTextStyle()}>{student.name}</DataTable.Cell>
                      <DataTable.Cell style={getTextStyle()}>{student.className}</DataTable.Cell>
                      <DataTable.Cell style={getTextStyle()}>{student.sectionName}</DataTable.Cell>
                      <DataTable.Cell numeric style={getTextStyle()}>
                        <Chip mode="outlined" style={getPerformanceChipStyle(student.totalPercentage)}>
                          {student.totalPercentage.toFixed(2)}%
                        </Chip>
                      </DataTable.Cell>
                      <DataTable.Cell numeric style={getTextStyle()}>
                        <Badge size={24} style={getRankBadgeStyle(student.schoolRank)}>
                          {student.schoolRank}
                        </Badge>
                      </DataTable.Cell>
                      <DataTable.Cell numeric style={getTextStyle()}>
                        <Badge size={24} style={getRankBadgeStyle(student.classRank)}>
                          {student.classRank}
                        </Badge>
                      </DataTable.Cell>
                      <DataTable.Cell>
                        <Button
                          mode="contained"
                          icon="information"
                          onPress={() => {
                            setSelectedStudent(student);
                            setDetailsModalVisible(true);
                          }}
                          style={styles.detailsButton}
                        >
                          {translate('results.management.table.details')}
                        </Button>
                      </DataTable.Cell>
                    </DataTable.Row>
                  </Animatable.View>
                ))
              )}

              <DataTable.Pagination
                page={currentPage}
                numberOfPages={Math.ceil(filteredStudents.length / itemsPerPage)}
                onPageChange={page => setCurrentPage(page)}
                label={`${currentPage * itemsPerPage + 1}-${Math.min((currentPage + 1) * itemsPerPage, filteredStudents.length)} of ${filteredStudents.length}`}
                showFastPaginationControls
                numberOfItemsPerPageList={[5, 10, 20, 50]}
                numberOfItemsPerPage={itemsPerPage}
                onItemsPerPageChange={setItemsPerPage}
                selectPageDropdownLabel={translate('common.rowsPerPage')}
              />
            </DataTable>
          </ScrollView>
        </Animatable.View>
      ) : activeTab === 'charts' ? (
        <Animatable.View animation="fadeIn" duration={800}>
          <ScrollView
            refreshControl={
              <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
            }
          >
            <Card style={styles.chartCard}>
              <Card.Content>
                <Title style={getTextStyle(styles.chartTitle)}>{translate('results.management.charts.performanceDistribution')}</Title>
                {renderPerformanceDistributionChart()}
              </Card.Content>
            </Card>

            <Card style={styles.chartCard}>
              <Card.Content>
                <Title style={getTextStyle(styles.chartTitle)}>{translate('results.management.charts.examTypeComparison')}</Title>
                {renderExamTypeComparisonChart()}
              </Card.Content>
            </Card>
          </ScrollView>
        </Animatable.View>
      ) : (
        <Animatable.View animation="fadeIn" duration={800}>
          <ScrollView
            refreshControl={
              <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
            }
          >
            <Card style={styles.summaryCard}>
              <Card.Content>
                <Title style={getTextStyle(styles.summaryTitle)}>{translate('results.management.summary.classPerformance')}</Title>
                {renderClassPerformanceSummary()}
              </Card.Content>
            </Card>

            <Card style={styles.summaryCard}>
              <Card.Content>
                <Title style={getTextStyle(styles.summaryTitle)}>{translate('results.management.summary.topPerformers')}</Title>
                {renderTopPerformersList()}
              </Card.Content>
            </Card>
          </ScrollView>
        </Animatable.View>
      )}

      <Portal>
        <Modal
          visible={detailsModalVisible}
          onDismiss={() => setDetailsModalVisible(false)}
          contentContainerStyle={styles.modalContent}
        >
          {renderStudentDetails()}
          <Button
            mode="contained"
            onPress={() => setDetailsModalVisible(false)}
            style={styles.closeButton}
            icon="close"
          >
            {translate('common.close')}
          </Button>
        </Modal>
      </Portal>
      {error && (
        <Animatable.View animation="shake" duration={500} style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
          <Button
            mode="contained"
            onPress={() => setError(null)}
            style={styles.dismissButton}
            icon="close-circle"
            labelStyle={getTextStyle()}
          >
            {translate('common.dismiss') || 'Dismiss'}
          </Button>
        </Animatable.View>
      )}

      {/* FAB for quick actions */}
      <FAB.Group
        open={fabOpen}
        icon={fabOpen ? 'close' : 'plus'}
        actions={[
          {
            icon: 'refresh',
            label: translate('common.refresh') || 'Refresh',
            onPress: onRefresh,
          },
          {
            icon: 'file-export',
            label: translate('results.management.export.button') || 'Export Results',
            onPress: exportToExcel,
            disabled: !selectedClass || filteredStudents.length === 0 || exportLoading
          },
        ]}
        onStateChange={({ open }) => setFabOpen(open)}
        fabStyle={{ backgroundColor: '#1976d2' }}
      />

      {/* Snackbar for notifications */}
      <Snackbar
        visible={snackbarVisible}
        onDismiss={() => setSnackbarVisible(false)}
        duration={3000}
        style={{ bottom: 16 }}
        action={{
          label: 'OK',
          onPress: () => setSnackbarVisible(false),
        }}
      >
        {snackbarMessage}
      </Snackbar>
      </Animated.View>
    </View>
  );
};

// Helper functions for styling and charts
const getPerformanceChipStyle = (percentage) => {
  if (percentage >= 90) {
    return { backgroundColor: 'rgba(76, 175, 80, 0.1)', borderColor: '#4CAF50' };
  } else if (percentage >= 75) {
    return { backgroundColor: 'rgba(33, 150, 243, 0.1)', borderColor: '#2196F3' };
  } else if (percentage >= 60) {
    return { backgroundColor: 'rgba(255, 193, 7, 0.1)', borderColor: '#FFC107' };
  } else if (percentage >= 40) {
    return { backgroundColor: 'rgba(255, 152, 0, 0.1)', borderColor: '#FF9800' };
  } else {
    return { backgroundColor: 'rgba(244, 67, 54, 0.1)', borderColor: '#F44336' };
  }
};

const getRankBadgeStyle = (rank) => {
  if (rank === 1) {
    return { backgroundColor: '#FFD700' }; // Gold
  } else if (rank === 2) {
    return { backgroundColor: '#C0C0C0' }; // Silver
  } else if (rank === 3) {
    return { backgroundColor: '#CD7F32' }; // Bronze
  } else if (rank <= 10) {
    return { backgroundColor: '#2196F3' }; // Blue
  } else {
    return { backgroundColor: '#757575' }; // Gray
  }
};

const renderPerformanceDistributionChart = () => {
  // Calculate performance distribution from actual student data
  const performanceRanges = [
    { name: translate('results.management.charts.excellent') || '90-100%', min: 90, max: 100, color: '#4CAF50', count: 0 },
    { name: translate('results.management.charts.veryGood') || '80-89%', min: 80, max: 89.99, color: '#2196F3', count: 0 },
    { name: translate('results.management.charts.good') || '70-79%', min: 70, max: 79.99, color: '#8BC34A', count: 0 },
    { name: translate('results.management.charts.average') || '60-69%', min: 60, max: 69.99, color: '#FFC107', count: 0 },
    { name: translate('results.management.charts.pass') || '40-59%', min: 40, max: 59.99, color: '#FF9800', count: 0 },
    { name: translate('results.management.charts.fail') || '0-39%', min: 0, max: 39.99, color: '#F44336', count: 0 },
  ];

  // Count students in each performance range
  students.forEach(student => {
    const percentage = student.totalPercentage;
    for (const range of performanceRanges) {
      if (percentage >= range.min && percentage <= range.max) {
        range.count++;
        break;
      }
    }
  });

  // Format data for chart
  const chartData = performanceRanges
    .filter(range => range.count > 0) // Only include ranges with students
    .map(range => ({
      name: range.name,
      population: range.count,
      color: range.color,
      legendFontColor: '#7F7F7F',
      legendFontSize: 12
    }));

  // If no data, show a message
  if (chartData.length === 0) {
    return (
      <View style={styles.noDataContainer}>
        <MaterialCommunityIcons name="chart-pie" size={50} color="#BDBDBD" />
        <Text style={styles.noDataText}>
          {translate('results.management.charts.noData') || 'No data available for chart'}
        </Text>
      </View>
    );
  }

  return (
    <View style={styles.chartContainer}>
      <PieChart
        data={chartData.map(item => ({
        ...item,
        population: isNaN(item.population) || item.population === undefined ||
                    item.population === null || item.population === Infinity ||
                    item.population === -Infinity ? 0 : item.population
      }))}
        width={300}
        height={220}
        chartConfig={{
          backgroundColor: '#ffffff',
          backgroundGradientFrom: '#ffffff',
          backgroundGradientTo: '#ffffff',
          color: (opacity = 1) => `rgba(0, 0, 0, ${opacity})`,
          labelColor: (opacity = 1) => `rgba(0, 0, 0, ${opacity})`,
        }}
        accessor="population"
        backgroundColor="transparent"
        paddingLeft="15"
        absolute
        hasLegend={true}
        center={[50, 0]}
      />

      <View style={styles.chartLegendContainer}>
        {chartData.map((item, index) => (
          <View key={index} style={styles.chartLegendItem}>
            <View style={[styles.chartLegendColor, { backgroundColor: item.color }]} />
            <Text style={styles.chartLegendText}>
              {item.name}: {item.population} {translate('results.management.charts.students') || 'students'}
            </Text>
          </View>
        ))}
      </View>
    </View>
  );
};

const renderExamTypeComparisonChart = () => {
  // Calculate average performance for each exam type from actual student data
  const examTypes = {
    assignment: {
      label: translate('results.management.examTypes.assignment') || 'Assignments',
      total: 0,
      count: 0
    },
    quiz: {
      label: translate('results.management.examTypes.quiz') || 'Quizzes',
      total: 0,
      count: 0
    },
    midterm: {
      label: translate('results.management.examTypes.midterm') || 'Midterm',
      total: 0,
      count: 0
    },
    final: {
      label: translate('results.management.examTypes.final') || 'Final',
      total: 0,
      count: 0
    }
  };

  // Calculate averages from all students
  students.forEach(student => {
    Object.keys(student.grades).forEach(type => {
      student.grades[type].forEach(grade => {
        examTypes[type].total += grade.percentage;
        examTypes[type].count += 1;
      });
    });
  });

  // Calculate averages and prepare data for chart
  const labels = [];
  const values = [];

  Object.keys(examTypes).forEach(type => {
    if (examTypes[type].count > 0) {
      labels.push(examTypes[type].label);
      values.push(examTypes[type].total / examTypes[type].count);
    }
  });

  // If no data, show a message
  if (values.length === 0) {
    return (
      <View style={styles.noDataContainer}>
        <MaterialCommunityIcons name="chart-bar" size={50} color="#BDBDBD" />
        <Text style={styles.noDataText}>
          {translate('results.management.charts.noData') || 'No data available for chart'}
        </Text>
      </View>
    );
  }

  const data = {
    labels,
    datasets: [
      {
        data: sanitizeChartData(values),
        color: (opacity = 1) => `rgba(33, 150, 243, ${opacity})`,
        strokeWidth: 2
      }
    ]
  };

  const chartConfig = {
    backgroundGradientFrom: '#ffffff',
    backgroundGradientTo: '#ffffff',
    color: (opacity = 1) => `rgba(0, 0, 0, ${opacity})`,
    strokeWidth: 2,
    barPercentage: 0.7,
    decimalPlaces: 1,
    propsForLabels: {
      fontSize: 10,
      fontWeight: 'bold'
    }
  };

  return (
    <View style={styles.chartContainer}>
      <BarChart
        data={sanitizeChartDatasets(data)}
        width={300}
        height={220}
        chartConfig={chartConfig}
        verticalLabelRotation={30}
        fromZero
        yAxisSuffix="%"
        yAxisLabel=""
        showValuesOnTopOfBars={true}
      />

      <View style={styles.chartExplanation}>
        <Text style={styles.chartExplanationText}>
          {translate('results.management.charts.examTypeExplanation') ||
           'This chart shows the average performance percentage for each exam type.'}
        </Text>
      </View>
    </View>
  );
};

const renderClassPerformanceSummary = () => {
  // Calculate class performance metrics from actual student data
  if (!students || students.length === 0) {
    return (
      <View style={styles.noDataContainer}>
        <MaterialCommunityIcons name="chart-line" size={50} color="#BDBDBD" />
        <Text style={styles.noDataText}>
          {translate('results.management.summary.noData') || 'No data available for summary'}
        </Text>
      </View>
    );
  }

  // Calculate class average
  const totalPercentages = students.reduce((sum, student) => sum + student.totalPercentage, 0);
  const classAverage = (totalPercentages / students.length).toFixed(2);

  // Find highest and lowest scores
  const highestScore = Math.max(...students.map(student => student.totalPercentage)).toFixed(2);
  const lowestScore = Math.min(...students.map(student => student.totalPercentage)).toFixed(2);

  // Calculate pass rate (students with percentage >= 40%)
  const passingStudents = students.filter(student => student.totalPercentage >= 40).length;
  const passRate = ((passingStudents / students.length) * 100).toFixed(2);

  // Calculate grade distribution
  const gradeDistribution = {
    'A+': 0, 'A': 0, 'A-': 0,
    'B+': 0, 'B': 0, 'B-': 0,
    'C+': 0, 'C': 0, 'C-': 0,
    'D+': 0, 'D': 0, 'F': 0
  };

  students.forEach(student => {
    if (student.gradeLetter) {
      gradeDistribution[student.gradeLetter]++;
    }
  });

  return (
    <View style={styles.summaryContainer}>
      <Animatable.View animation="fadeIn" duration={500}>
        <Surface style={styles.summaryCard} elevation={2}>
          <List.Item
            title={translate('results.management.summary.classAverage') || "Class Average"}
            description={`${classAverage}%`}
            left={props => <List.Icon {...props} icon="chart-line" color="#2196F3" />}
            titleStyle={getTextStyle()}
            descriptionStyle={{fontSize: 16, fontWeight: 'bold'}}
          />
        </Surface>
      </Animatable.View>

      <Animatable.View animation="fadeIn" duration={500} delay={100}>
        <Surface style={styles.summaryCard} elevation={2}>
          <List.Item
            title={translate('results.management.summary.highestScore') || "Highest Score"}
            description={`${highestScore}%`}
            left={props => <List.Icon {...props} icon="arrow-up-bold" color="#4CAF50" />}
            titleStyle={getTextStyle()}
            descriptionStyle={{fontSize: 16, fontWeight: 'bold'}}
          />
        </Surface>
      </Animatable.View>

      <Animatable.View animation="fadeIn" duration={500} delay={200}>
        <Surface style={styles.summaryCard} elevation={2}>
          <List.Item
            title={translate('results.management.summary.lowestScore') || "Lowest Score"}
            description={`${lowestScore}%`}
            left={props => <List.Icon {...props} icon="arrow-down-bold" color="#F44336" />}
            titleStyle={getTextStyle()}
            descriptionStyle={{fontSize: 16, fontWeight: 'bold'}}
          />
        </Surface>
      </Animatable.View>

      <Animatable.View animation="fadeIn" duration={500} delay={300}>
        <Surface style={styles.summaryCard} elevation={2}>
          <List.Item
            title={translate('results.management.summary.passRate') || "Pass Rate"}
            description={`${passRate}%`}
            left={props => <List.Icon {...props} icon="check-circle" color="#4CAF50" />}
            titleStyle={getTextStyle()}
            descriptionStyle={{fontSize: 16, fontWeight: 'bold'}}
          />
        </Surface>
      </Animatable.View>

      <Animatable.View animation="fadeIn" duration={500} delay={400}>
        <Surface style={styles.summaryCard} elevation={2}>
          <List.Item
            title={translate('results.management.summary.totalStudents') || "Total Students"}
            description={students.length.toString()}
            left={props => <List.Icon {...props} icon="account-group" color="#9C27B0" />}
            titleStyle={getTextStyle()}
            descriptionStyle={{fontSize: 16, fontWeight: 'bold'}}
          />
        </Surface>
      </Animatable.View>

      <Animatable.View animation="fadeIn" duration={500} delay={500}>
        <Surface style={styles.gradeDistributionCard} elevation={2}>
          <Title style={[styles.gradeDistributionTitle, getTextStyle()]}>
            {translate('results.management.summary.gradeDistribution') || "Grade Distribution"}
          </Title>
          <View style={styles.gradeDistributionContainer}>
            {Object.entries(gradeDistribution).map(([grade, count]) => (
              count > 0 && (
                <View key={grade} style={styles.gradeItem}>
                  <Badge size={28} style={getGradeBadgeStyle(grade)}>{grade}</Badge>
                  <Text style={styles.gradeCount}>{count}</Text>
                </View>
              )
            ))}
          </View>
        </Surface>
      </Animatable.View>
    </View>
  );
};

const renderTopPerformersList = () => {
  // Get top 5 performers from actual student data
  if (!students || students.length === 0) {
    return (
      <View style={styles.noDataContainer}>
        <MaterialCommunityIcons name="trophy" size={50} color="#BDBDBD" />
        <Text style={styles.noDataText}>
          {translate('results.management.summary.noData') || 'No data available for top performers'}
        </Text>
      </View>
    );
  }

  // Sort students by percentage and take top 5
  const topPerformers = [...students]
    .sort((a, b) => b.totalPercentage - a.totalPercentage)
    .slice(0, 5);

  return (
    <View style={styles.topPerformersContainer}>
      {topPerformers.map((student, index) => (
        <Animatable.View
          key={student.id}
          animation="fadeInRight"
          duration={500}
          delay={index * 100}
        >
          <Surface style={styles.topPerformerCard} elevation={3}>
            <View style={styles.topPerformerContent}>
              <View style={styles.rankBadgeContainer}>
                <Badge size={32} style={getRankBadgeStyle(index + 1)}>{index + 1}</Badge>
              </View>
              <View style={styles.topPerformerInfo}>
                <Text style={[styles.topPerformerName, getTextStyle()]}>{student.name}</Text>
                <Text style={styles.topPerformerClass}>
                  {translate('results.management.table.class') || 'Class'} {student.className}-{student.sectionName}
                </Text>
              </View>
              <Chip mode="outlined" style={getPerformanceChipStyle(student.totalPercentage)}>
                {student.totalPercentage.toFixed(1)}%
              </Chip>
            </View>
            <Button
              mode="text"
              icon="information-outline"
              onPress={() => {
                setSelectedStudent(student);
                setDetailsModalVisible(true);
              }}
              style={styles.detailsButton}
            >
              {translate('results.management.table.details') || 'Details'}
            </Button>
          </Surface>
        </Animatable.View>
      ))}
    </View>
  );
};

const getGradeBadgeStyle = (grade) => {
  if (grade === 'A+' || grade === 'A' || grade === 'A-') {
    return styles.excellentGradeBadge;
  } else if (grade === 'B+' || grade === 'B' || grade === 'B-') {
    return styles.goodGradeBadge;
  } else if (grade === 'C+' || grade === 'C' || grade === 'C-') {
    return styles.averageGradeBadge;
  } else if (grade === 'D+' || grade === 'D') {
    return styles.passGradeBadge;
  } else {
    return styles.failGradeBadge;
  }
};

const getExamTypeTitle = (examType) => {
  switch (examType) {
    case 'assignment':
      return translate('results.management.examTypes.assignment') || 'Assignment';
    case 'quiz':
      return translate('results.management.examTypes.quiz') || 'Quiz';
    case 'midterm':
      return translate('results.management.examTypes.midterm') || 'Midterm Exam';
    case 'final':
      return translate('results.management.examTypes.final') || 'Final Exam';
    default:
      return examType || 'Unknown';
  }
};

const getExamTypeShort = (examType) => {
  switch (examType) {
    case 'assignment':
      return 'ASG';
    case 'quiz':
      return 'QZ';
    case 'midterm':
      return 'MID';
    case 'final':
      return 'FNL';
    default:
      return 'UNK';
  }
};

const getExamTypeBadgeStyle = (examType) => {
  switch (examType) {
    case 'assignment':
      return styles.assignmentBadge;
    case 'quiz':
      return styles.quizBadge;
    case 'midterm':
      return styles.midtermBadge;
    case 'final':
      return styles.finalBadge;
    default:
      return {};
  }
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5'
  },
  filterCard: {
    margin: 16,
    elevation: 4,
    borderRadius: 12,
    overflow: 'hidden'
  },
  cardGradient: {
    padding: 16,
    borderRadius: 12
  },
  cardTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: 16,
    color: '#333'
  },
  filterRow: {
    flexDirection: 'row',
    marginBottom: 16
  },
  filterButton: {
    flex: 1,
    marginRight: 8,
    borderRadius: 8
  },
  searchBar: {
    marginTop: 8,
    borderRadius: 8,
    elevation: 2
  },
  loaderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20
  },
  loader: {
    marginBottom: 16
  },
  loadingText: {
    fontSize: 16,
    color: '#666'
  },
  tabContainer: {
    marginHorizontal: 16,
    marginTop: 8,
    marginBottom: 16
  },
  tabRow: {
    flexDirection: 'row',
    justifyContent: 'space-between'
  },
  tabButton: {
    flex: 1,
    marginHorizontal: 4,
    borderRadius: 8
  },
  dataTable: {
    marginHorizontal: 16,
    marginBottom: 16,
    backgroundColor: 'white',
    borderRadius: 12,
    overflow: 'hidden',
    elevation: 4
  },
  tableHeader: {
    backgroundColor: '#e3f2fd'
  },
  evenRow: {
    backgroundColor: '#ffffff'
  },
  oddRow: {
    backgroundColor: '#f5f5f5'
  },
  detailsButton: {
    borderRadius: 8,
    marginVertical: 4
  },
  noDataContainer: {
    padding: 20,
    alignItems: 'center'
  },
  noDataText: {
    fontSize: 16,
    color: '#666'
  },
  chartCard: {
    margin: 16,
    borderRadius: 12,
    elevation: 4
  },
  chartTitle: {
    fontSize: 18,
    marginBottom: 16
  },
  chartContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16
  },
  summaryCard: {
    margin: 16,
    borderRadius: 12,
    elevation: 4
  },
  summaryTitle: {
    fontSize: 18,
    marginBottom: 16
  },
  summaryContainer: {
    marginTop: 8
  },
  topPerformersContainer: {
    marginTop: 8
  },
  topPerformerCard: {
    marginVertical: 8,
    borderRadius: 8,
    elevation: 2
  },
  topPerformerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16
  },
  rankBadgeContainer: {
    marginRight: 16
  },
  topPerformerInfo: {
    flex: 1
  },
  topPerformerName: {
    fontSize: 16,
    fontWeight: 'bold'
  },
  topPerformerClass: {
    fontSize: 14,
    color: '#666'
  },
  modalContent: {
    backgroundColor: 'white',
    padding: 20,
    margin: 20,
    borderRadius: 12,
    maxHeight: '80%',
    elevation: 8
  },
  modalTitle: {
    fontSize: 24,
    marginBottom: 8,
    fontWeight: 'bold',
    color: '#333'
  },
  subtitle: {
    fontSize: 20,
    marginVertical: 16,
    fontWeight: '600',
    color: '#444'
  },
  divider: {
    marginVertical: 16
  },
  studentHeaderContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16
  },
  studentAvatar: {
    backgroundColor: '#2196F3',
    marginRight: 16
  },
  studentHeaderInfo: {
    flex: 1
  },
  studentBasicInfo: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 8
  },
  infoChip: {
    marginRight: 8,
    marginBottom: 8,
    backgroundColor: 'rgba(33, 150, 243, 0.1)'
  },
  rankSurface: {
    marginHorizontal: 16,
    borderRadius: 8,
    overflow: 'hidden',
    elevation: 4
  },
  rankGradient: {
    padding: 16,
    borderRadius: 8
  },
  rankTitle: {
    fontSize: 18,
    marginBottom: 16,
    textAlign: 'center',
    color: '#333'
  },
  rankContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginVertical: 8,
    padding: 8,
    borderRadius: 8
  },
  rankItem: {
    alignItems: 'center'
  },
  rankLabel: {
    fontSize: 14,
    color: '#666',
    marginTop: 8
  },
  accordion: {
    backgroundColor: '#f9f9f9',
    marginBottom: 8,
    borderRadius: 8
  },
  accordionTitle: {
    fontSize: 16,
    fontWeight: '600'
  },
  gradeItem: {
    borderLeftWidth: 4,
    borderLeftColor: '#2196F3',
    backgroundColor: '#ffffff'
  },
  gradePoints: {
    fontSize: 14,
    marginBottom: 4
  },
  progressBar: {
    height: 6,
    borderRadius: 3
  },
  averageItem: {
    backgroundColor: '#e3f2fd',
    borderBottomLeftRadius: 8,
    borderBottomRightRadius: 8
  },
  totalCard: {
    marginHorizontal: 16,
    marginTop: 16,
    marginBottom: 24,
    elevation: 4,
    borderRadius: 8,
    overflow: 'hidden'
  },
  totalCardGradient: {
    borderRadius: 8
  },
  totalCardTitle: {
    fontSize: 18,
    marginBottom: 16,
    color: '#333'
  },
  totalPointsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8
  },
  totalPointsLabel: {
    fontSize: 16,
    color: '#555'
  },
  totalPointsValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333'
  },
  overallContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12
  },
  overallLabel: {
    fontSize: 16,
    color: '#555'
  },
  overallProgressBar: {
    height: 8,
    borderRadius: 4
  },
  closeButton: {
    marginTop: 16,
    borderRadius: 8
  },
  errorContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    zIndex: 1000
  },
  errorText: {
    color: 'white',
    fontSize: 18,
    marginBottom: 16,
    textAlign: 'center',
    paddingHorizontal: 32
  },
  dismissButton: {
    marginTop: 16,
    borderRadius: 8
  },
  // New styles for enhanced UI
  mainContainer: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  contentContainer: {
    flex: 1,
    backgroundColor: '#f5f5f5',
    padding: 16,
  },
  rtlContainer: {
    flexDirection: 'row-reverse',
  },
  actionButtonsContainer: {
    marginTop: 16,
  },
  actionButton: {
    marginBottom: 8,
  },
  resultsActionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  // Grade badge styles
  excellentGradeBadge: {
    backgroundColor: '#4CAF50',
    color: 'white',
  },
  goodGradeBadge: {
    backgroundColor: '#8BC34A',
    color: 'white',
  },
  averageGradeBadge: {
    backgroundColor: '#FFC107',
    color: 'black',
  },
  passGradeBadge: {
    backgroundColor: '#FF9800',
    color: 'white',
  },
  failGradeBadge: {
    backgroundColor: '#F44336',
    color: 'white',
  },
  // Rank badge styles
  firstRankBadge: {
    backgroundColor: '#FFD700',
    color: '#333',
  },
  secondRankBadge: {
    backgroundColor: '#C0C0C0',
    color: '#333',
  },
  thirdRankBadge: {
    backgroundColor: '#CD7F32',
    color: 'white',
  },
  topTenBadge: {
    backgroundColor: '#2196F3',
    color: 'white',
  },
  normalRankBadge: {
    backgroundColor: '#9E9E9E',
    color: 'white',
  },
  // Performance chip styles
  excellentChip: {
    backgroundColor: '#E8F5E9',
    borderColor: '#4CAF50',
  },
  goodChip: {
    backgroundColor: '#F1F8E9',
    borderColor: '#8BC34A',
  },
  averageChip: {
    backgroundColor: '#FFF8E1',
    borderColor: '#FFC107',
  },
  passChip: {
    backgroundColor: '#FFF3E0',
    borderColor: '#FF9800',
  },
  failChip: {
    backgroundColor: '#FFEBEE',
    borderColor: '#F44336',
  },
  // Chart styles
  chartLegendContainer: {
    marginTop: 16,
    alignItems: 'flex-start',
  },
  chartLegendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  chartLegendColor: {
    width: 16,
    height: 16,
    borderRadius: 8,
    marginRight: 8,
  },
  chartLegendText: {
    fontSize: 12,
  },
  chartExplanation: {
    marginTop: 16,
    padding: 8,
    backgroundColor: '#E3F2FD',
    borderRadius: 4,
  },
  chartExplanationText: {
    fontSize: 12,
    color: '#333',
    textAlign: 'center',
  },
  // Grade distribution styles
  gradeDistributionCard: {
    margin: 16,
    padding: 16,
    borderRadius: 12,
  },
  gradeDistributionTitle: {
    fontSize: 16,
    marginBottom: 16,
    textAlign: 'center',
  },
  gradeDistributionContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
  },
  gradeCount: {
    fontSize: 12,
    marginTop: 4,
  },
  // Top performer styles
  topPerformerStats: {
    alignItems: 'center',
  },
  gradeLetterBadge: {
    marginTop: 8,
  },
  topPerformerDetailsButton: {
    marginTop: 0,
    marginBottom: 8,
  },

  // Subject styles
  subjectTitle: {
    fontWeight: 'bold',
    fontSize: 16,
  },
  subjectAccordion: {
    backgroundColor: '#f5f7fa',
    marginBottom: 8,
    borderRadius: 8,
    overflow: 'hidden',
  },
  gradeDetailsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 4,
    flexWrap: 'wrap',
  },
  gradeDate: {
    fontSize: 12,
    color: '#666',
    marginRight: 8,
  },
  gradeRemarks: {
    fontSize: 12,
    color: '#666',
    fontStyle: 'italic',
  },
  gradeTypeContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 8,
  },
  subjectSummary: {
    margin: 16,
    padding: 16,
    borderRadius: 8,
    backgroundColor: '#f0f8ff',
  },
  subjectSummaryContent: {
    gap: 8,
  },
  subjectSummaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  subjectSummaryLabel: {
    fontWeight: 'bold',
    color: '#333',
  },
  subjectSummaryValue: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  subjectProgressBar: {
    height: 8,
    borderRadius: 4,
    marginTop: 8,
  },

  // Exam type badge styles
  assignmentBadge: {
    backgroundColor: '#2196F3',
    color: 'white',
    marginBottom: 8,
  },
  quizBadge: {
    backgroundColor: '#FF9800',
    color: 'white',
    marginBottom: 8,
  },
  midtermBadge: {
    backgroundColor: '#9C27B0',
    color: 'white',
    marginBottom: 8,
  },
  finalBadge: {
    backgroundColor: '#4CAF50',
    color: 'white',
    marginBottom: 8,
  }
});

export default ResultsManagement;
