// Simple custom event emitter implementation
class EventEmitter {
  constructor() {
    this.events = {};
  }

  addListener(eventName, listener) {
    if (!this.events[eventName]) {
      this.events[eventName] = [];
    }
    this.events[eventName].push(listener);

    // Return an object with a remove method
    return {
      remove: () => this.removeListener(eventName, listener)
    };
  }

  removeListener(eventName, listenerToRemove) {
    if (!this.events[eventName]) {
      return;
    }

    this.events[eventName] = this.events[eventName].filter(
      listener => listener !== listenerToRemove
    );
  }

  emit(eventName, data) {
    if (!this.events[eventName]) {
      return;
    }

    this.events[eventName].forEach(listener => {
      listener(data);
    });
  }
}

// Create a singleton event emitter
class EventService {
  constructor() {
    this.emitter = new EventEmitter();
  }

  // Add event listener
  addListener(eventName, listener) {
    return this.emitter.addListener(eventName, listener);
  }

  // Remove event listener
  removeListener(eventName, listener) {
    this.emitter.removeListener(eventName, listener);
  }

  // Emit event
  emit(eventName, data) {
    this.emitter.emit(eventName, data);
  }
}

// Export a singleton instance
export default new EventService();

// Define event names as constants for consistency
export const EVENTS = {
  PARENT_ADDED: 'PARENT_ADDED',
  STUDENT_ADDED: 'STUDENT_ADDED',
  TEACHER_ADDED: 'TEACHER_ADDED',
  CLASS_ADDED: 'CLASS_ADDED',
};
