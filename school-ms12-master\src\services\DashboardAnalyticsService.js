import { db } from '../config/firebase';
import { 
  collection, 
  query, 
  where, 
  getDocs, 
  getDoc, 
  doc, 
  orderBy, 
  limit, 
  startAfter,
  Timestamp,
  addDoc,
  updateDoc,
  deleteDoc,
  onSnapshot
} from 'firebase/firestore';

class DashboardAnalyticsService {
  /**
   * Get school statistics
   * @returns {Promise<Object>} School statistics
   */
  async getSchoolStatistics() {
    try {
      // Get total students
      const studentsQuery = query(collection(db, 'users'), where('role', '==', 'student'));
      const studentsSnapshot = await getDocs(studentsQuery);
      const totalStudents = studentsSnapshot.size;

      // Get total teachers
      const teachersQuery = query(collection(db, 'users'), where('role', '==', 'teacher'));
      const teachersSnapshot = await getDocs(teachersQuery);
      const totalTeachers = teachersSnapshot.size;

      // Get total parents
      const parentsQuery = query(collection(db, 'users'), where('role', '==', 'parent'));
      const parentsSnapshot = await getDocs(parentsQuery);
      const totalParents = parentsSnapshot.size;

      // Get total classes
      const classesQuery = query(collection(db, 'classes'));
      const classesSnapshot = await getDocs(classesQuery);
      const totalClasses = classesSnapshot.size;

      // Get active users (users who logged in within the last 7 days)
      const sevenDaysAgo = new Date();
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
      
      const activeUsersQuery = query(
        collection(db, 'users'),
        where('lastLoginAt', '>=', Timestamp.fromDate(sevenDaysAgo))
      );
      const activeUsersSnapshot = await getDocs(activeUsersQuery);
      const activeUsers = activeUsersSnapshot.size;

      return {
        totalStudents,
        totalTeachers,
        totalParents,
        totalClasses,
        activeUsers
      };
    } catch (error) {
      console.error('Error getting school statistics:', error);
      throw error;
    }
  }

  /**
   * Get enrollment statistics by month
   * @param {number} year - Year to get statistics for
   * @returns {Promise<Array>} Enrollment statistics by month
   */
  async getEnrollmentStatsByMonth(year = new Date().getFullYear()) {
    try {
      const enrollmentStats = [];
      
      // Get all students
      const studentsQuery = query(collection(db, 'users'), where('role', '==', 'student'));
      const studentsSnapshot = await getDocs(studentsQuery);
      
      // Group students by enrollment month
      const studentsByMonth = Array(12).fill(0);
      
      studentsSnapshot.forEach(doc => {
        const student = doc.data();
        if (student.createdAt) {
          const enrollmentDate = student.createdAt.toDate();
          const enrollmentYear = enrollmentDate.getFullYear();
          
          if (enrollmentYear === year) {
            const month = enrollmentDate.getMonth();
            studentsByMonth[month]++;
          }
        }
      });
      
      // Create enrollment stats array
      const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
      
      for (let i = 0; i < 12; i++) {
        enrollmentStats.push({
          month: months[i],
          students: studentsByMonth[i]
        });
      }
      
      return enrollmentStats;
    } catch (error) {
      console.error('Error getting enrollment statistics:', error);
      throw error;
    }
  }

  /**
   * Get attendance statistics
   * @param {string} classId - Class ID to get statistics for (optional)
   * @returns {Promise<Object>} Attendance statistics
   */
  async getAttendanceStatistics(classId = null) {
    try {
      let attendanceQuery;
      
      if (classId) {
        attendanceQuery = query(
          collection(db, 'attendance'),
          where('classId', '==', classId)
        );
      } else {
        attendanceQuery = query(collection(db, 'attendance'));
      }
      
      const attendanceSnapshot = await getDocs(attendanceQuery);
      
      let totalAttendance = 0;
      let presentCount = 0;
      let absentCount = 0;
      let lateCount = 0;
      let excusedCount = 0;
      
      attendanceSnapshot.forEach(doc => {
        const attendance = doc.data();
        
        if (attendance.students) {
          Object.values(attendance.students).forEach(status => {
            totalAttendance++;
            
            switch (status) {
              case 'present':
                presentCount++;
                break;
              case 'absent':
                absentCount++;
                break;
              case 'late':
                lateCount++;
                break;
              case 'excused':
                excusedCount++;
                break;
              default:
                break;
            }
          });
        }
      });
      
      const presentPercentage = totalAttendance > 0 ? (presentCount / totalAttendance) * 100 : 0;
      const absentPercentage = totalAttendance > 0 ? (absentCount / totalAttendance) * 100 : 0;
      const latePercentage = totalAttendance > 0 ? (lateCount / totalAttendance) * 100 : 0;
      const excusedPercentage = totalAttendance > 0 ? (excusedCount / totalAttendance) * 100 : 0;
      
      return {
        totalAttendance,
        presentCount,
        absentCount,
        lateCount,
        excusedCount,
        presentPercentage,
        absentPercentage,
        latePercentage,
        excusedPercentage
      };
    } catch (error) {
      console.error('Error getting attendance statistics:', error);
      throw error;
    }
  }

  /**
   * Get grade statistics
   * @param {string} classId - Class ID to get statistics for (optional)
   * @returns {Promise<Object>} Grade statistics
   */
  async getGradeStatistics(classId = null) {
    try {
      let gradesQuery;
      
      if (classId) {
        gradesQuery = query(
          collection(db, 'grades'),
          where('classId', '==', classId)
        );
      } else {
        gradesQuery = query(collection(db, 'grades'));
      }
      
      const gradesSnapshot = await getDocs(gradesQuery);
      
      let totalGrades = 0;
      let gradeSum = 0;
      let gradeDistribution = {
        A: 0, // 90-100
        B: 0, // 80-89
        C: 0, // 70-79
        D: 0, // 60-69
        F: 0  // 0-59
      };
      
      gradesSnapshot.forEach(doc => {
        const grade = doc.data();
        
        if (grade.score !== undefined) {
          totalGrades++;
          gradeSum += grade.score;
          
          // Update grade distribution
          if (grade.score >= 90) {
            gradeDistribution.A++;
          } else if (grade.score >= 80) {
            gradeDistribution.B++;
          } else if (grade.score >= 70) {
            gradeDistribution.C++;
          } else if (grade.score >= 60) {
            gradeDistribution.D++;
          } else {
            gradeDistribution.F++;
          }
        }
      });
      
      const averageGrade = totalGrades > 0 ? gradeSum / totalGrades : 0;
      
      // Calculate percentages
      const gradePercentages = {};
      Object.keys(gradeDistribution).forEach(grade => {
        gradePercentages[grade] = totalGrades > 0 ? (gradeDistribution[grade] / totalGrades) * 100 : 0;
      });
      
      return {
        totalGrades,
        averageGrade,
        gradeDistribution,
        gradePercentages
      };
    } catch (error) {
      console.error('Error getting grade statistics:', error);
      throw error;
    }
  }

  /**
   * Get recent activities
   * @param {number} limit - Number of activities to get
   * @returns {Promise<Array>} Recent activities
   */
  async getRecentActivities(limitCount = 10) {
    try {
      const activitiesQuery = query(
        collection(db, 'activities'),
        orderBy('timestamp', 'desc'),
        limit(limitCount)
      );
      
      const activitiesSnapshot = await getDocs(activitiesQuery);
      
      const activities = [];
      activitiesSnapshot.forEach(doc => {
        activities.push({
          id: doc.id,
          ...doc.data()
        });
      });
      
      return activities;
    } catch (error) {
      console.error('Error getting recent activities:', error);
      throw error;
    }
  }

  /**
   * Get resource usage statistics
   * @returns {Promise<Object>} Resource usage statistics
   */
  async getResourceUsageStatistics() {
    try {
      // Get library resources
      const libraryQuery = query(collection(db, 'library'));
      const librarySnapshot = await getDocs(libraryQuery);
      const totalLibraryResources = librarySnapshot.size;
      
      // Get digital resources
      const resourcesQuery = query(collection(db, 'resources'));
      const resourcesSnapshot = await getDocs(resourcesQuery);
      const totalDigitalResources = resourcesSnapshot.size;
      
      // Get resource usage
      const usageQuery = query(collection(db, 'resourceUsage'));
      const usageSnapshot = await getDocs(usageQuery);
      
      let totalUsage = 0;
      let usageByType = {
        book: 0,
        document: 0,
        video: 0,
        audio: 0,
        other: 0
      };
      
      usageSnapshot.forEach(doc => {
        const usage = doc.data();
        totalUsage++;
        
        if (usage.resourceType) {
          if (usageByType[usage.resourceType] !== undefined) {
            usageByType[usage.resourceType]++;
          } else {
            usageByType.other++;
          }
        }
      });
      
      // Calculate percentages
      const usagePercentages = {};
      Object.keys(usageByType).forEach(type => {
        usagePercentages[type] = totalUsage > 0 ? (usageByType[type] / totalUsage) * 100 : 0;
      });
      
      return {
        totalLibraryResources,
        totalDigitalResources,
        totalUsage,
        usageByType,
        usagePercentages
      };
    } catch (error) {
      console.error('Error getting resource usage statistics:', error);
      throw error;
    }
  }

  /**
   * Get user activity statistics
   * @returns {Promise<Object>} User activity statistics
   */
  async getUserActivityStatistics() {
    try {
      // Get all users
      const usersQuery = query(collection(db, 'users'));
      const usersSnapshot = await getDocs(usersQuery);
      const totalUsers = usersSnapshot.size;
      
      // Calculate active users in different time periods
      const now = new Date();
      const oneDayAgo = new Date(now);
      oneDayAgo.setDate(now.getDate() - 1);
      
      const sevenDaysAgo = new Date(now);
      sevenDaysAgo.setDate(now.getDate() - 7);
      
      const thirtyDaysAgo = new Date(now);
      thirtyDaysAgo.setDate(now.getDate() - 30);
      
      let activeToday = 0;
      let activeThisWeek = 0;
      let activeThisMonth = 0;
      
      usersSnapshot.forEach(doc => {
        const user = doc.data();
        
        if (user.lastLoginAt) {
          const lastLogin = user.lastLoginAt.toDate();
          
          if (lastLogin >= oneDayAgo) {
            activeToday++;
          }
          
          if (lastLogin >= sevenDaysAgo) {
            activeThisWeek++;
          }
          
          if (lastLogin >= thirtyDaysAgo) {
            activeThisMonth++;
          }
        }
      });
      
      // Calculate percentages
      const activeTodayPercentage = totalUsers > 0 ? (activeToday / totalUsers) * 100 : 0;
      const activeThisWeekPercentage = totalUsers > 0 ? (activeThisWeek / totalUsers) * 100 : 0;
      const activeThisMonthPercentage = totalUsers > 0 ? (activeThisMonth / totalUsers) * 100 : 0;
      
      return {
        totalUsers,
        activeToday,
        activeThisWeek,
        activeThisMonth,
        activeTodayPercentage,
        activeThisWeekPercentage,
        activeThisMonthPercentage
      };
    } catch (error) {
      console.error('Error getting user activity statistics:', error);
      throw error;
    }
  }

  /**
   * Get communication statistics
   * @returns {Promise<Object>} Communication statistics
   */
  async getCommunicationStatistics() {
    try {
      // Get messages
      const messagesQuery = query(collection(db, 'messages'));
      const messagesSnapshot = await getDocs(messagesQuery);
      const totalMessages = messagesSnapshot.size;
      
      // Get announcements
      const announcementsQuery = query(collection(db, 'announcements'));
      const announcementsSnapshot = await getDocs(announcementsQuery);
      const totalAnnouncements = announcementsSnapshot.size;
      
      // Calculate messages by role
      let messagesByRole = {
        admin: 0,
        teacher: 0,
        student: 0,
        parent: 0
      };
      
      messagesSnapshot.forEach(doc => {
        const message = doc.data();
        
        if (message.senderRole && messagesByRole[message.senderRole] !== undefined) {
          messagesByRole[message.senderRole]++;
        }
      });
      
      // Calculate percentages
      const messagePercentages = {};
      Object.keys(messagesByRole).forEach(role => {
        messagePercentages[role] = totalMessages > 0 ? (messagesByRole[role] / totalMessages) * 100 : 0;
      });
      
      return {
        totalMessages,
        totalAnnouncements,
        messagesByRole,
        messagePercentages
      };
    } catch (error) {
      console.error('Error getting communication statistics:', error);
      throw error;
    }
  }

  /**
   * Log activity
   * @param {string} userId - User ID
   * @param {string} action - Action performed
   * @param {string} details - Activity details
   * @param {string} targetId - Target ID (optional)
   * @param {string} targetType - Target type (optional)
   * @returns {Promise<string>} Activity ID
   */
  async logActivity(userId, action, details, targetId = null, targetType = null) {
    try {
      const activityData = {
        userId,
        action,
        details,
        targetId,
        targetType,
        timestamp: Timestamp.now()
      };
      
      const activityRef = await addDoc(collection(db, 'activities'), activityData);
      return activityRef.id;
    } catch (error) {
      console.error('Error logging activity:', error);
      throw error;
    }
  }

  /**
   * Set up real-time listeners for dashboard data
   * @param {Function} callback - Callback function to handle updates
   * @returns {Function} Unsubscribe function
   */
  setupDashboardListeners(callback) {
    try {
      // Listen for user changes
      const usersUnsubscribe = onSnapshot(
        query(collection(db, 'users')),
        (snapshot) => {
          callback('users', snapshot.size);
        }
      );
      
      // Listen for attendance changes
      const attendanceUnsubscribe = onSnapshot(
        query(collection(db, 'attendance')),
        (snapshot) => {
          callback('attendance', snapshot.size);
        }
      );
      
      // Listen for grade changes
      const gradesUnsubscribe = onSnapshot(
        query(collection(db, 'grades')),
        (snapshot) => {
          callback('grades', snapshot.size);
        }
      );
      
      // Listen for activity changes
      const activitiesUnsubscribe = onSnapshot(
        query(
          collection(db, 'activities'),
          orderBy('timestamp', 'desc'),
          limit(10)
        ),
        (snapshot) => {
          const activities = [];
          snapshot.forEach(doc => {
            activities.push({
              id: doc.id,
              ...doc.data()
            });
          });
          callback('activities', activities);
        }
      );
      
      // Return unsubscribe function
      return () => {
        usersUnsubscribe();
        attendanceUnsubscribe();
        gradesUnsubscribe();
        activitiesUnsubscribe();
      };
    } catch (error) {
      console.error('Error setting up dashboard listeners:', error);
      throw error;
    }
  }
}

export default new DashboardAnalyticsService();
