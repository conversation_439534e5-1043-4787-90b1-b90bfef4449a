import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  TouchableWithoutFeedback,
  Keyboard,
  StatusBar,
  Image,
  Dimensions,
  Animated,
  KeyboardAvoidingView,
  Platform
} from 'react-native';
import {
  Surface,
  Text,
  Button,
  Title,
  Menu,
  useTheme,
  IconButton
} from 'react-native-paper';
import FixedTextInput from '../../components/common/FixedTextInput';
import { useAuth } from '../../context/AuthContext';
import { useLanguage } from '../../context/LanguageContext';
import { MaterialIcons, MaterialCommunityIcons, FontAwesome5 } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import * as Animatable from 'react-native-animatable';

const { width, height } = Dimensions.get('window');

const Login = ({ navigation }) => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [menuVisible, setMenuVisible] = useState(false);
  const [hidePassword, setHidePassword] = useState(true);
  const [emailFocused, setEmailFocused] = useState(false);
  const [passwordFocused, setPasswordFocused] = useState(false);
  const [isKeyboardVisible, setKeyboardVisible] = useState(false);

  const { login } = useAuth();
  const { translate, language, setLanguage, supportedLanguages } = useLanguage();
  // No theme needed

  // Animation values
  const [fadeAnim] = useState(new Animated.Value(0));
  const [slideAnim] = useState(new Animated.Value(50));
  const [formSlideAnim] = useState(new Animated.Value(100));
  const [logoScale] = useState(new Animated.Value(1));
  const [buttonAnim] = useState(new Animated.Value(0));
  const [pulseAnim] = useState(new Animated.Value(1));

  useEffect(() => {
    // Start entrance animations in sequence
    Animated.sequence([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 500,
        useNativeDriver: true,
      }),
      Animated.timing(formSlideAnim, {
        toValue: 0,
        duration: 500,
        useNativeDriver: true,
      }),
      Animated.timing(buttonAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      })
    ]).start();

    // Start pulsing animation for logo
    Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
      ])
    ).start();

    // Keyboard listeners
    const keyboardDidShowListener = Keyboard.addListener(
      'keyboardDidShow',
      () => {
        setKeyboardVisible(true);
        Animated.timing(logoScale, {
          toValue: 0.8,
          duration: 300,
          useNativeDriver: true,
        }).start();
      }
    );

    const keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      () => {
        setKeyboardVisible(false);
        Animated.timing(logoScale, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }).start();
      }
    );

    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, [fadeAnim, slideAnim, formSlideAnim, logoScale, buttonAnim, pulseAnim]);

  // School Logo Component
  const SchoolLogo = () => {
    const logoOuterStyle = {
      width: 120,
      height: 120,
      borderRadius: 60,
      backgroundColor: '#4A6FFF',
      alignItems: 'center',
      justifyContent: 'center',
      elevation: 8,
      shadowColor: "#000",
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.3,
      shadowRadius: 4.65,
    };

    return (
      <View style={styles.logoInnerContainer}>
        <Animated.View
          style={[
            logoOuterStyle,
            { transform: [{ scale: pulseAnim }] }
          ]}
        >
          <View style={styles.logoInner}>
            <Text style={styles.logoInitials}>
              ASS
            </Text>
            <FontAwesome5 name="book-open" size={24} color="#fff" style={styles.logoIcon} />
          </View>
        </Animated.View>
      </View>
    );
  };

  const handleLogin = async () => {
    console.log('Login attempt with:', { email });

    if (!email.trim() || !password.trim()) {
      setError(translate('auth.errors.enterCredentials'));
      shakeForm();
      return;
    }

    // Button press animation
    Animated.sequence([
      Animated.timing(buttonAnim, {
        toValue: 0.9,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(buttonAnim, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      })
    ]).start();

    try {
      setError('');
      setLoading(true);
      console.log('Calling login function...');

      const result = await login(email.trim(), password);
      console.log('Login result:', result);

      if (!result.success) {
        console.log('Login failed:', result.error);
        setError(translate(`auth.errors.${result.error}`));
        shakeForm();
        return;
      }

      console.log('Login successful, user role:', result.role);
    } catch (err) {
      console.error('Login error:', err);
      setError(translate('auth.errors.unexpected'));
      shakeForm();
    } finally {
      setLoading(false);
    }
  };

  const shakeForm = () => {
    Animated.sequence([
      Animated.timing(slideAnim, { toValue: 10, duration: 80, useNativeDriver: true }),
      Animated.timing(slideAnim, { toValue: -10, duration: 80, useNativeDriver: true }),
      Animated.timing(slideAnim, { toValue: 10, duration: 80, useNativeDriver: true }),
      Animated.timing(slideAnim, { toValue: 0, duration: 80, useNativeDriver: true })
    ]).start();
  };

  const handleLanguageChange = async (langCode) => {
    try {
      await setLanguage(langCode);
      setMenuVisible(false);
    } catch (error) {
      console.error('Failed to change language:', error);
    }
  };

  return (
    <View style={styles.container}>
      <StatusBar translucent backgroundColor="transparent" />
      <LinearGradient
        colors={['#4A6FFF', '#6A5ACD', '#8A2BE2']}
        style={styles.gradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <View style={styles.gradientPattern}>
          {/* Pattern overlay created with small dots */}
          {Array.from({ length: 20 }).map((_, i) => (
            <View
              key={i}
              style={[
                styles.patternDot,
                {
                  left: `${Math.random() * 100}%`,
                  top: `${Math.random() * 100}%`,
                  width: Math.random() * 4 + 1,
                  height: Math.random() * 4 + 1,
                  opacity: Math.random() * 0.3 + 0.1
                }
              ]}
            />
          ))}
        </View>
        <TouchableWithoutFeedback onPress={Keyboard.dismiss} accessible={false}>
          <KeyboardAvoidingView
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            style={styles.keyboardAvoidingView}
          >
            <Menu
              visible={menuVisible}
              onDismiss={() => setMenuVisible(false)}
              anchor={
                <Animated.View style={{ opacity: fadeAnim, position: 'absolute', top: 50, right: 20 }}>

                </Animated.View>
              }
            >
              {Object.entries(supportedLanguages).map(([code, name]) => (
                <Menu.Item
                  key={code}
                  onPress={() => handleLanguageChange(code)}
                  title={name}
                  disabled={code === language}
                />
              ))}
            </Menu>

            {/* Logo and school name */}
            <Animated.View
              style={[
                styles.logoContainer,
                {
                  opacity: fadeAnim,
                  transform: [
                    { translateY: slideAnim },
                    { scale: logoScale }
                  ]
                }
              ]}
            >
              <SchoolLogo />
              <Animatable.Text
                animation="fadeIn"
                duration={1500}
                style={styles.logoText}
              >
                {translate('auth.landing.schoolNameFull')}
              </Animatable.Text>
            </Animated.View>

            {/* Login form */}
            <Animated.View
              style={[
                styles.formContainer,
                {
                  opacity: fadeAnim,
                  transform: [{ translateY: formSlideAnim }]
                }
              ]}
            >
              {error ? (
                <Animatable.View animation="fadeIn" duration={300}>
                  <View style={styles.errorContainer}>
                    <MaterialCommunityIcons name="alert-circle" size={18} color="#FFFFFF" />
                    <Text style={styles.errorText}>{error}</Text>
                  </View>
                </Animatable.View>
              ) : null}

              <View style={[styles.inputContainer, emailFocused && styles.inputContainerFocused]}>
                <MaterialCommunityIcons
                  name="email"
                  size={24}
                  color={emailFocused ? '#FFFFFF' : 'rgba(255, 255, 255, 0.7)'}
                  style={styles.inputIcon}
                />
                <FixedTextInput
                  label={translate('auth.login.email')}
                  value={email}
                  onChangeText={setEmail}
                  keyboardType="email-address"
                  autoCapitalize="none"
                  style={styles.input}
                  mode="flat"
                  underlineColor="rgba(255, 255, 255, 0.3)"
                  activeUnderlineColor="#FFFFFF"
                  onFocus={() => setEmailFocused(true)}
                  onBlur={() => setEmailFocused(false)}
                  theme={{
                    colors: {
                      background: 'transparent',
                      text: '#FFFFFF',
                      placeholder: 'rgba(255, 255, 255, 0.7)',
                      primary: '#FFFFFF'
                    }
                  }}
                  textColor="#FFFFFF"
                  placeholderTextColor="rgba(255, 255, 255, 0.7)"
                />
              </View>

              <View style={[styles.inputContainer, passwordFocused && styles.inputContainerFocused]}>
                <MaterialCommunityIcons
                  name="lock"
                  size={24}
                  color={passwordFocused ? '#FFFFFF' : 'rgba(255, 255, 255, 0.7)'}
                  style={styles.inputIcon}
                />
                <FixedTextInput
                  label={translate('auth.login.password')}
                  value={password}
                  onChangeText={setPassword}
                  secureTextEntry={hidePassword}
                  autoCapitalize="none"
                  style={styles.input}
                  mode="flat"
                  underlineColor="rgba(255, 255, 255, 0.3)"
                  activeUnderlineColor="#FFFFFF"
                  onFocus={() => setPasswordFocused(true)}
                  onBlur={() => setPasswordFocused(false)}
                  theme={{
                    colors: {
                      background: 'transparent',
                      text: '#FFFFFF',
                      placeholder: 'rgba(255, 255, 255, 0.7)',
                      primary: '#FFFFFF'
                    }
                  }}
                  textColor="#FFFFFF"
                  placeholderTextColor="rgba(255, 255, 255, 0.7)"
                  right={
                    <FixedTextInput.Icon
                      icon={hidePassword ? "eye" : "eye-off"}
                      onPress={() => setHidePassword(!hidePassword)}
                      color="rgba(255, 255, 255, 0.8)"
                      forceTextInputFocus={false}
                    />
                  }
                />
              </View>

              <Animatable.View animation="fadeIn" delay={800}>
                <TouchableWithoutFeedback onPress={() => navigation.navigate('ForgotPassword')}>
                  <Text style={styles.forgotPassword}>{translate('auth.login.forgotPassword')}</Text>
                </TouchableWithoutFeedback>
              </Animatable.View>

              <Animated.View
                style={{
                  transform: [{ scale: buttonAnim }],
                  opacity: fadeAnim
                }}
              >
                <Animatable.View animation="fadeIn" delay={1000}>
                  <Button
                    mode="contained"
                    onPress={handleLogin}
                    loading={loading}
                    disabled={loading}
                    style={styles.loginButton}
                    contentStyle={styles.buttonContent}
                    labelStyle={styles.buttonLabel}
                  >
                    {translate('auth.login.submit')}
                  </Button>
                </Animatable.View>
              </Animated.View>

              <Animatable.View animation="fadeIn" delay={1200}>
                <Button
                  mode="outlined"
                  onPress={() => navigation.navigate('Landing')}
                  style={styles.link}
                  textColor="#FFFFFF"
                >
                  {translate('auth.login.back')}
                </Button>
              </Animatable.View>
            </Animated.View>

            <Animatable.View animation="fadeIn" delay={1500} style={styles.footer}>
              <Text style={styles.footerText}>
                {translate('auth.landing.footer.copyright')}
              </Text>
            </Animatable.View>
          </KeyboardAvoidingView>
        </TouchableWithoutFeedback>
      </LinearGradient>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gradient: {
    flex: 1,
    width: '100%',
    height: '100%',
    position: 'relative',
  },
  gradientPattern: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1,
  },
  patternDot: {
    position: 'absolute',
    borderRadius: 50,
    backgroundColor: '#FFFFFF',
  },
  keyboardAvoidingView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    position: 'relative',
    zIndex: 2,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 20,
  },
  logoInnerContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 15,
  },
  logoInner: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  logoInitials: {
    fontSize: 36,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 5,
  },
  logoIcon: {
    marginTop: 5,
  },
  logoSurface: {
    borderRadius: 75,
    padding: 5,
    backgroundColor: 'white',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 15,
  },
  logo: {
    width: 120,
    height: 120,
    borderRadius: 60,
  },
  logoText: {
    fontSize: 22,
    fontWeight: 'bold',
    color: 'white',
    textAlign: 'center',
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  formContainer: {
    width: '100%',
    maxWidth: 400,
    paddingHorizontal: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
    color: '#FFFFFF',
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(220, 53, 69, 0.7)',
    padding: 12,
    borderRadius: 8,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  errorText: {
    marginLeft: 8,
    fontSize: 14,
    flex: 1,
    color: '#FFFFFF',
    fontWeight: '500',
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    borderRadius: 12,
    marginBottom: 20,
    paddingHorizontal: 15,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    shadowColor: 'rgba(0, 0, 0, 0.3)',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 5,
    elevation: 3,
  },
  inputContainerFocused: {
    backgroundColor: 'rgba(255, 255, 255, 0.25)',
    borderColor: 'rgba(255, 255, 255, 0.5)',
  },
  inputIcon: {
    marginRight: 12,
  },
  input: {
    flex: 1,
    backgroundColor: 'transparent',
    color: '#FFFFFF',
    height: 60,
  },
  forgotPassword: {
    alignSelf: 'flex-end',
    marginBottom: 25,
    color: '#FFFFFF',
    fontWeight: '500',
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  loginButton: {
    borderRadius: 12,
    marginTop: 5,
    elevation: 5,
    backgroundColor: '#FFFFFF',
  },
  buttonContent: {
    height: 56,
    backgroundColor: '#FFFFFF',
  },
  buttonLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#4A6FFF',
  },
  link: {
    marginTop: 20,
    borderColor: 'rgba(255, 255, 255, 0.5)',
    borderRadius: 12,
  },
  languageButton: {
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    borderRadius: 20,
  },
  footer: {
    marginTop: 30,
  },
  footerText: {
    color: 'rgba(255, 255, 255, 0.7)',
    textAlign: 'center',
  }
});

export default Login;