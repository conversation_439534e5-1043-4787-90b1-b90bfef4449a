import React, { useState, useEffect } from 'react';
import { View, ScrollView, StyleSheet, Animated, Dimensions } from 'react-native';
import { Avatar, Title, Paragraph, Divider, List, useTheme } from 'react-native-paper';
import { LinearGradient } from 'expo-linear-gradient';
import { useLanguage } from '../../context/LanguageContext';
import { useAuth } from '../../context/AuthContext';
import LanguageSelector from './LanguageSelector';
import usePermissions from '../../hooks/usePermissions';

const TeacherSidebar = ({ visible, onClose, navigation, activeSidebarItem, setActiveSidebarItem }) => {
  // No theme needed
  const { translate } = useLanguage();
  const { user, logout } = useAuth();
  const { permissions, loading } = usePermissions();

  const drawerAnim = React.useRef(new Animated.Value(visible ? 0 : -300)).current;

  React.useEffect(() => {
    Animated.timing(drawerAnim, {
      toValue: visible ? 0 : -300,
      duration: 250,
      useNativeDriver: true,
    }).start();
  }, [visible, drawerAnim]);

  const handleNavigation = (screen) => {
    if (screen === 'logout') {
      logout();
      return;
    }

    setActiveSidebarItem(screen);
    navigation.navigate(screen);

    if (Dimensions.get('window').width < 768) {
      onClose();
    }
  };

  return (
    <Animated.View
      style={[styles.sidebar, { transform: [{ translateX: drawerAnim }] }]}
    >
      <LinearGradient
        colors={['#1976d2', '#1565C0']}
        style={styles.sidebarHeader}
      >
        <View style={styles.sidebarProfile}>
          <Avatar.Icon
            size={80}
            icon="account-tie"
            style={styles.sidebarAvatar}
            color="white"
            backgroundColor="rgba(255, 255, 255, 0.2)"
          />
          <Title style={styles.sidebarName}>{user?.displayName || translate('roles.teacher')}</Title>
          <Paragraph style={styles.sidebarRole}>{translate('roles.teacher')}</Paragraph>
        </View>
      </LinearGradient>

      <ScrollView style={styles.sidebarMenu}>
        <List.Section>
          <List.Item
            title={translate('teacherDashboard.dashboard')}
            left={props => <List.Icon {...props} icon="view-dashboard" color={activeSidebarItem === 'TeacherDashboard' ? '#1976d2' : '#757575'} />}
            onPress={() => handleNavigation('TeacherDashboard')}
            style={[styles.sidebarItem, activeSidebarItem === 'TeacherDashboard' && styles.sidebarItemActive]}
            titleStyle={[styles.sidebarItemText, activeSidebarItem === 'TeacherDashboard' && styles.sidebarItemTextActive]}
          />

          <List.Item
            title={translate('teacherDashboard.classManagement')}
            left={props => <List.Icon {...props} icon="account-group" color={activeSidebarItem === 'ClassManagement' ? '#1976d2' : '#757575'} />}
            onPress={() => handleNavigation('ClassManagement')}
            style={[styles.sidebarItem, activeSidebarItem === 'ClassManagement' && styles.sidebarItemActive]}
            titleStyle={[styles.sidebarItemText, activeSidebarItem === 'ClassManagement' && styles.sidebarItemTextActive]}
          />

          <List.Item
            title={translate('teacherDashboard.attendance')}
            left={props => <List.Icon {...props} icon="calendar-check" color={activeSidebarItem === 'AttendanceManagement' ? '#1976d2' : '#757575'} />}
            onPress={() => handleNavigation('AttendanceManagement')}
            style={[styles.sidebarItem, activeSidebarItem === 'AttendanceManagement' && styles.sidebarItemActive]}
            titleStyle={[styles.sidebarItemText, activeSidebarItem === 'AttendanceManagement' && styles.sidebarItemTextActive]}
          />

          <List.Item
            title={translate('teacherDashboard.gradeBook')}
            left={props => <List.Icon {...props} icon="book-open-variant" color={activeSidebarItem === 'GradeBook' ? '#1976d2' : '#757575'} />}
            onPress={() => handleNavigation('GradeBook')}
            style={[styles.sidebarItem, activeSidebarItem === 'GradeBook' && styles.sidebarItemActive]}
            titleStyle={[styles.sidebarItemText, activeSidebarItem === 'GradeBook' && styles.sidebarItemTextActive]}
          />

          <List.Item
            title={translate('teacherDashboard.assignments')}
            left={props => <List.Icon {...props} icon="file-document-edit" color={activeSidebarItem === 'AssignmentManagement' ? '#1976d2' : '#757575'} />}
            onPress={() => handleNavigation('AssignmentManagement')}
            style={[styles.sidebarItem, activeSidebarItem === 'AssignmentManagement' && styles.sidebarItemActive]}
            titleStyle={[styles.sidebarItemText, activeSidebarItem === 'AssignmentManagement' && styles.sidebarItemTextActive]}
          />

          <List.Item
            title={translate('teacherDashboard.calendar')}
            left={props => <List.Icon {...props} icon="calendar-month" color={activeSidebarItem === 'CalendarManagement' ? '#1976d2' : '#757575'} />}
            onPress={() => handleNavigation('CalendarManagement')}
            style={[styles.sidebarItem, activeSidebarItem === 'CalendarManagement' && styles.sidebarItemActive]}
            titleStyle={[styles.sidebarItemText, activeSidebarItem === 'CalendarManagement' && styles.sidebarItemTextActive]}
          />

          <List.Item
            title={translate('schedule.title') || "Class Schedule"}
            left={props => <List.Icon {...props} icon="calendar-week" color={activeSidebarItem === 'TeacherClassSchedule' ? '#1976d2' : '#757575'} />}
            onPress={() => handleNavigation('TeacherClassSchedule')}
            style={[styles.sidebarItem, activeSidebarItem === 'TeacherClassSchedule' && styles.sidebarItemActive]}
            titleStyle={[styles.sidebarItemText, activeSidebarItem === 'TeacherClassSchedule' && styles.sidebarItemTextActive]}
          />

          <List.Item
            title={translate('teacherDashboard.messaging')}
            left={props => <List.Icon {...props} icon="message-text" color={activeSidebarItem === 'TeacherMessaging' ? '#1976d2' : '#757575'} />}
            onPress={() => handleNavigation('TeacherMessaging')}
            style={[styles.sidebarItem, activeSidebarItem === 'TeacherMessaging' && styles.sidebarItemActive]}
            titleStyle={[styles.sidebarItemText, activeSidebarItem === 'TeacherMessaging' && styles.sidebarItemTextActive]}
          />

          {permissions.registration && (
            <>
              <Divider style={styles.sidebarDivider} />
              <List.Subheader style={styles.sidebarSubheader}>Registration</List.Subheader>

              <List.Item
                title="Student Registration"
                left={props => <List.Icon {...props} icon="account-plus" color={activeSidebarItem === 'StudentRegistration' ? '#1976d2' : '#757575'} />}
                onPress={() => handleNavigation('StudentRegistration')}
                style={[styles.sidebarItem, activeSidebarItem === 'StudentRegistration' && styles.sidebarItemActive]}
                titleStyle={[styles.sidebarItemText, activeSidebarItem === 'StudentRegistration' && styles.sidebarItemTextActive]}
              />

              <List.Item
                title="Parent Registration"
                left={props => <List.Icon {...props} icon="account-child" color={activeSidebarItem === 'ParentRegistration' ? '#1976d2' : '#757575'} />}
                onPress={() => handleNavigation('ParentRegistration')}
                style={[styles.sidebarItem, activeSidebarItem === 'ParentRegistration' && styles.sidebarItemActive]}
                titleStyle={[styles.sidebarItemText, activeSidebarItem === 'ParentRegistration' && styles.sidebarItemTextActive]}
              />
            </>
          )}
        </List.Section>

        <Divider style={styles.sidebarDivider} />

        <List.Item
          title={translate('teacherDashboard.profile')}
          left={props => <List.Icon {...props} icon="account" color={activeSidebarItem === 'ProfileManagement' ? '#1976d2' : '#757575'} />}
          onPress={() => handleNavigation('ProfileManagement')}
          style={[styles.sidebarItem, activeSidebarItem === 'ProfileManagement' && styles.sidebarItemActive]}
          titleStyle={[styles.sidebarItemText, activeSidebarItem === 'ProfileManagement' && styles.sidebarItemTextActive]}
        />

        <List.Item
          title={translate('teacherDashboard.logout')}
          left={props => <List.Icon {...props} icon="logout" color="#F44336" />}
          onPress={() => handleNavigation('logout')}
          style={styles.sidebarItem}
          titleStyle={[styles.sidebarItemText, { color: '#F44336' }]}
        />

        <View style={styles.sidebarFooter}>
          <LanguageSelector />
        </View>
      </ScrollView>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  sidebar: {
    position: 'absolute',
    left: 0,
    top: 0,
    bottom: 0,
    width: 300,
    backgroundColor: 'white',
    zIndex: 1000,
    elevation: 16,
    borderTopRightRadius: 0,
    borderBottomRightRadius: 30,
    overflow: 'hidden',
  },
  sidebarHeader: {
    padding: 16,
    paddingTop: 40,
    paddingBottom: 24,
  },
  sidebarProfile: {
    alignItems: 'center',
    marginBottom: 8,
  },
  sidebarAvatar: {
    marginBottom: 12,
    borderWidth: 3,
    borderColor: 'rgba(255, 255, 255, 0.5)',
  },
  sidebarName: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  sidebarRole: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 14,
    textAlign: 'center',
    marginTop: 4,
  },
  sidebarMenu: {
    flex: 1,
    paddingBottom: 20,
  },
  sidebarItem: {
    marginVertical: 0,
    borderRadius: 0,
    paddingVertical: 4,
  },
  sidebarItemActive: {
    backgroundColor: 'rgba(33, 150, 243, 0.1)',
    borderLeftWidth: 4,
    borderLeftColor: '#2196F3',
  },
  sidebarItemText: {
    fontSize: 14,
    color: '#424242',
  },
  sidebarItemTextActive: {
    color: '#2196F3',
    fontWeight: 'bold',
  },
  sidebarDivider: {
    marginVertical: 12,
  },
  sidebarFooter: {
    padding: 16,
    alignItems: 'center',
  },
  sidebarSubheader: {
    color: '#1976d2',
    fontWeight: 'bold',
    fontSize: 14,
    marginTop: 8,
    marginBottom: 0,
  },
});

export default TeacherSidebar;
