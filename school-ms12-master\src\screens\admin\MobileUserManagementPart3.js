  // Main render function
  return (
    <MobileScreenWrapper
      title={translate('userManagement.title')}
      subtitle={translate('userManagement.subtitle')}
      showBackArrow={true}
      bottomNavItems={bottomNavItems}
      fabActions={fabActions}
      isRTL={isRTL}
    >
      <View style={styles.container}>
        {/* Search bar */}
        <Animatable.View 
          animation="fadeInDown" 
          duration={500}
          style={styles.searchContainer}
        >
          <Searchbar
            placeholder={translate('userManagement.searchUsers')}
            onChangeText={setSearchQuery}
            value={searchQuery}
            style={styles.searchBar}
            icon="magnify"
            clearIcon="close"
            onClearIconPress={() => setSearchQuery('')}
          />
        </Animatable.View>
        
        {/* User list with swipe actions */}
        <SwipeListView
          data={filteredUsers}
          keyExtractor={item => item.id}
          refreshing={refreshing}
          onRefresh={onRefresh}
          renderItem={({ item }) => {
            const initials = `${item.firstName?.[0] || ''}${item.lastName?.[0] || ''}`.toUpperCase();
            const fullName = `${item.firstName || ''} ${item.lastName || ''}`;
            
            // Get role-specific color
            const getRoleColor = (role) => {
              switch(role) {
                case 'admin': return mobileTheme.colors.admin;
                case 'teacher': return mobileTheme.colors.teacher;
                case 'student': return mobileTheme.colors.student;
                case 'parent': return mobileTheme.colors.parent;
                default: return mobileTheme.colors.primary;
              }
            };
            
            return (
              <Animatable.View 
                animation="fadeIn" 
                duration={500}
              >
                <Surface style={styles.userCard}>
                  <TouchableOpacity 
                    style={styles.userCardContent}
                    onPress={() => handleUserPress(item)}
                  >
                    <View style={styles.userInfo}>
                      {item.photoURL ? (
                        <Avatar.Image 
                          source={{ uri: item.photoURL }} 
                          size={50} 
                          style={styles.avatar}
                        />
                      ) : (
                        <Avatar.Text 
                          label={initials} 
                          size={50} 
                          style={[styles.avatar, { backgroundColor: getRoleColor(item.role) }]}
                        />
                      )}
                      
                      <View style={styles.userDetails}>
                        <Text style={styles.userName}>{fullName}</Text>
                        <Text style={styles.userEmail}>{item.email}</Text>
                        
                        <View style={styles.userChips}>
                          <Chip 
                            mode="outlined" 
                            style={[styles.roleChip, { borderColor: getRoleColor(item.role) }]}
                            textStyle={{ color: getRoleColor(item.role) }}
                          >
                            {translate(`userManagement.roles.${item.role}`) || item.role}
                          </Chip>
                          
                          <Chip 
                            mode="outlined" 
                            style={[
                              styles.statusChip, 
                              { 
                                borderColor: item.status === 'active' ? mobileTheme.colors.success : mobileTheme.colors.error,
                                backgroundColor: item.status === 'active' ? mobileTheme.colors.success + '20' : mobileTheme.colors.error + '20'
                              }
                            ]}
                            textStyle={{ 
                              color: item.status === 'active' ? mobileTheme.colors.success : mobileTheme.colors.error 
                            }}
                          >
                            {translate(`userManagement.status.${item.status}`) || item.status}
                          </Chip>
                        </View>
                      </View>
                    </View>
                  </TouchableOpacity>
                </Surface>
              </Animatable.View>
            );
          }}
          renderHiddenItem={({ item }) => (
            <View style={styles.rowBack}>
              {/* Left swipe action - Edit */}
              <TouchableOpacity
                style={[styles.backLeftBtn, styles.backLeftBtnLeft]}
                onPress={() => navigation.navigate('UserEdit', { userId: item.id })}
              >
                <MaterialCommunityIcons name="pencil" size={24} color="white" />
                <Text style={styles.backTextWhite}>{translate('common.edit')}</Text>
              </TouchableOpacity>
              
              {/* Right swipe action - Status change */}
              <TouchableOpacity
                style={[styles.backRightBtn, styles.backRightBtnLeft]}
                onPress={() => {
                  setSelectedUser(item);
                  if (item.status === 'active') {
                    setShowDeactivateConfirmDialog(true);
                  } else {
                    setShowActivateConfirmDialog(true);
                  }
                }}
              >
                <MaterialCommunityIcons 
                  name={item.status === 'active' ? 'account-off' : 'account-check'} 
                  size={24} 
                  color="white" 
                />
                <Text style={styles.backTextWhite}>
                  {item.status === 'active' 
                    ? translate('userManagement.deactivate') 
                    : translate('userManagement.activate')}
                </Text>
              </TouchableOpacity>
              
              {/* Right swipe action - Delete */}
              <TouchableOpacity
                style={[styles.backRightBtn, styles.backRightBtnRight]}
                onPress={() => {
                  setSelectedUser(item);
                  setShowDeleteConfirmDialog(true);
                }}
              >
                <MaterialCommunityIcons name="delete" size={24} color="white" />
                <Text style={styles.backTextWhite}>{translate('common.delete')}</Text>
              </TouchableOpacity>
            </View>
          )}
          leftOpenValue={75}
          rightOpenValue={-150}
          previewRowKey={'0'}
          previewOpenValue={-40}
          previewOpenDelay={3000}
          ListEmptyComponent={() => (
            <View style={styles.noResultsContainer}>
              <MaterialCommunityIcons name="account-question" size={48} color="#9e9e9e" />
              <Text style={styles.noResultsText}>
                {searchQuery 
                  ? translate('userManagement.noSearchResults') 
                  : translate('userManagement.noUsersFound')}
              </Text>
              {!searchQuery ? (
                <Button
                  mode="contained"
                  onPress={() => navigation.navigate('UserRegistration')}
                  style={styles.addUserButton}
                  icon="account-plus"
                >
                  {translate('userManagement.addFirstUser')}
                </Button>
              ) : (
                <Button
                  mode="outlined"
                  onPress={() => setSearchQuery('')}
                  style={styles.clearSearchButton}
                >
                  {translate('userManagement.clearSearch')}
                </Button>
              )}
            </View>
          )}
          ListHeaderComponent={() => (
            <View style={styles.statsContainer}>
              <Animatable.View 
                animation="fadeInUp" 
                duration={500} 
                delay={100}
                style={styles.statCardContainer}
              >
                <Surface style={styles.statCard}>
                  <MaterialCommunityIcons name="account-group" size={24} color={mobileTheme.colors.admin} />
                  <Text style={styles.statValue}>{userStats.total}</Text>
                  <Text style={styles.statLabel}>{translate('userManagement.totalUsers')}</Text>
                </Surface>
              </Animatable.View>
              
              <Animatable.View 
                animation="fadeInUp" 
                duration={500} 
                delay={200}
                style={styles.statCardContainer}
              >
                <Surface style={styles.statCard}>
                  <MaterialCommunityIcons name="account-check" size={24} color={mobileTheme.colors.success} />
                  <Text style={styles.statValue}>{userStats.active}</Text>
                  <Text style={styles.statLabel}>{translate('userManagement.activeUsers')}</Text>
                </Surface>
              </Animatable.View>
              
              <Animatable.View 
                animation="fadeInUp" 
                duration={500} 
                delay={300}
                style={styles.statCardContainer}
              >
                <Surface style={styles.statCard}>
                  <MaterialCommunityIcons name="account-off" size={24} color={mobileTheme.colors.error} />
                  <Text style={styles.statValue}>{userStats.inactive}</Text>
                  <Text style={styles.statLabel}>{translate('userManagement.inactiveUsers')}</Text>
                </Surface>
              </Animatable.View>
              
              <Animatable.View 
                animation="fadeInUp" 
                duration={500} 
                delay={400}
                style={styles.statCardContainer}
              >
                <Surface style={styles.statCard}>
                  <MaterialCommunityIcons name="account-plus" size={24} color={mobileTheme.colors.info} />
                  <Text style={styles.statValue}>{userStats.new}</Text>
                  <Text style={styles.statLabel}>{translate('userManagement.newUsers')}</Text>
                </Surface>
              </Animatable.View>
            </View>
          )}
        />
