import { db } from '../config/firebase';
import { collection, getDocs, writeBatch, query, where } from 'firebase/firestore';
import * as XLSX from 'xlsx';
import { saveAs } from 'file-saver';
import Papa from 'papaparse';

class DataPortabilityService {
    static instance = null;
    
    constructor() {
        if (DataPortabilityService.instance) {
            return DataPortabilityService.instance;
        }
        DataPortabilityService.instance = this;
        
        this.supportedFormats = ['xlsx', 'csv', 'json'];
        this.maxBatchSize = 500;
    }

    // Export data to various formats
    async exportData(collections, format = 'xlsx', filters = {}) {
        try {
            const data = {};
            
            // Gather data from specified collections
            for (const collectionName of collections) {
                let queryRef = collection(db, collectionName);
                
                // Apply filters if any
                if (filters[collectionName]) {
                    queryRef = query(queryRef, ...this.buildFilters(filters[collectionName]));
                }
                
                const snapshot = await getDocs(queryRef);
                data[collectionName] = snapshot.docs.map(doc => ({
                    id: doc.id,
                    ...doc.data()
                }));
            }

            // Export in specified format
            switch (format.toLowerCase()) {
                case 'xlsx':
                    return await this.exportToExcel(data);
                case 'csv':
                    return await this.exportToCSV(data);
                case 'json':
                    return await this.exportToJSON(data);
                default:
                    throw new Error('Unsupported export format');
            }
        } catch (error) {
            console.error('Export failed:', error);
            throw error;
        }
    }

    // Export to Excel
    async exportToExcel(data) {
        const workbook = XLSX.utils.book_new();

        // Create worksheet for each collection
        Object.entries(data).forEach(([collectionName, documents]) => {
            const worksheet = XLSX.utils.json_to_sheet(documents);
            XLSX.utils.book_append_sheet(workbook, worksheet, collectionName);
        });

        // Generate Excel file
        const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
        const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
        
        return {
            blob,
            filename: `school_data_export_${new Date().toISOString()}.xlsx`
        };
    }

    // Export to CSV
    async exportToCSV(data) {
        const files = [];

        // Create CSV for each collection
        Object.entries(data).forEach(([collectionName, documents]) => {
            const csv = Papa.unparse(documents);
            const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
            
            files.push({
                blob,
                filename: `${collectionName}_${new Date().toISOString()}.csv`
            });
        });

        return files;
    }

    // Export to JSON
    async exportToJSON(data) {
        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        
        return {
            blob,
            filename: `school_data_export_${new Date().toISOString()}.json`
        };
    }

    // Import data from file
    async importData(file, format, options = {}) {
        try {
            let data;

            // Parse file based on format
            switch (format.toLowerCase()) {
                case 'xlsx':
                    data = await this.parseExcel(file);
                    break;
                case 'csv':
                    data = await this.parseCSV(file);
                    break;
                case 'json':
                    data = await this.parseJSON(file);
                    break;
                default:
                    throw new Error('Unsupported import format');
            }

            // Validate data
            await this.validateImportData(data);

            // Import data in batches
            return await this.importBatch(data, options);
        } catch (error) {
            console.error('Import failed:', error);
            throw error;
        }
    }

    // Parse Excel file
    async parseExcel(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            
            reader.onload = (e) => {
                try {
                    const workbook = XLSX.read(e.target.result, { type: 'array' });
                    const data = {};

                    workbook.SheetNames.forEach(sheetName => {
                        data[sheetName] = XLSX.utils.sheet_to_json(workbook.Sheets[sheetName]);
                    });

                    resolve(data);
                } catch (error) {
                    reject(error);
                }
            };

            reader.readAsArrayBuffer(file);
        });
    }

    // Parse CSV file
    async parseCSV(file) {
        return new Promise((resolve, reject) => {
            Papa.parse(file, {
                complete: (results) => {
                    resolve({ [file.name.split('_')[0]]: results.data });
                },
                error: (error) => {
                    reject(error);
                },
                header: true
            });
        });
    }

    // Parse JSON file
    async parseJSON(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            
            reader.onload = (e) => {
                try {
                    resolve(JSON.parse(e.target.result));
                } catch (error) {
                    reject(error);
                }
            };

            reader.readAsText(file);
        });
    }

    // Validate import data
    async validateImportData(data) {
        const errors = [];

        Object.entries(data).forEach(([collection, documents]) => {
            documents.forEach((doc, index) => {
                // Check required fields
                const missingFields = this.checkRequiredFields(collection, doc);
                if (missingFields.length > 0) {
                    errors.push(`Row ${index + 1} in ${collection} is missing required fields: ${missingFields.join(', ')}`);
                }

                // Validate data types
                const invalidTypes = this.validateDataTypes(collection, doc);
                if (invalidTypes.length > 0) {
                    errors.push(`Row ${index + 1} in ${collection} has invalid data types: ${invalidTypes.join(', ')}`);
                }
            });
        });

        if (errors.length > 0) {
            throw new Error('Validation failed:\n' + errors.join('\n'));
        }
    }

    // Import data in batches
    async importBatch(data, options) {
        const results = {
            successful: 0,
            failed: 0,
            errors: []
        };

        const batch = writeBatch(db);
        let batchCount = 0;

        for (const [collection, documents] of Object.entries(data)) {
            for (const doc of documents) {
                try {
                    if (batchCount >= this.maxBatchSize) {
                        await batch.commit();
                        batch = writeBatch(db);
                        batchCount = 0;
                    }

                    const docRef = doc.id ? 
                        doc(db, collection, doc.id) : 
                        doc(collection);

                    if (options.mode === 'update' && doc.id) {
                        batch.update(docRef, doc);
                    } else {
                        batch.set(docRef, doc);
                    }

                    batchCount++;
                    results.successful++;
                } catch (error) {
                    results.failed++;
                    results.errors.push({
                        collection,
                        doc: doc.id,
                        error: error.message
                    });
                }
            }
        }

        if (batchCount > 0) {
            await batch.commit();
        }

        return results;
    }

    // Build Firestore filters
    buildFilters(filterConfig) {
        return Object.entries(filterConfig).map(([field, value]) => {
            if (typeof value === 'object') {
                return where(field, value.operator, value.value);
            }
            return where(field, '==', value);
        });
    }

    // Check required fields
    checkRequiredFields(collection, doc) {
        const requiredFields = {
            students: ['name', 'grade', 'section'],
            teachers: ['name', 'subjects'],
            classes: ['name', 'grade', 'section'],
            grades: ['studentId', 'subject', 'score']
        };

        return requiredFields[collection]?.filter(field => !doc[field]) || [];
    }

    // Validate data types
    validateDataTypes(collection, doc) {
        const typeValidations = {
            students: {
                grade: 'number',
                dateOfBirth: 'date'
            },
            grades: {
                score: 'number'
            }
        };

        const errors = [];
        const validations = typeValidations[collection];

        if (validations) {
            Object.entries(validations).forEach(([field, type]) => {
                if (doc[field] && !this.validateType(doc[field], type)) {
                    errors.push(`${field} should be of type ${type}`);
                }
            });
        }

        return errors;
    }

    // Validate data type
    validateType(value, type) {
        switch (type) {
            case 'number':
                return typeof value === 'number' && !isNaN(value);
            case 'date':
                return !isNaN(Date.parse(value));
            default:
                return true;
        }
    }
}

export default new DataPortabilityService();
