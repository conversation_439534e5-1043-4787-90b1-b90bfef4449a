  // Render confirmation dialogs
  const renderConfirmationDialogs = () => {
    return (
      <>
        {/* Delete confirmation dialog */}
        <Portal>
          <Dialog
            visible={showDeleteConfirmDialog}
            onDismiss={() => setShowDeleteConfirmDialog(false)}
            style={styles.dialog}
          >
            <Dialog.Title>{translate('userManagement.confirmDelete')}</Dialog.Title>
            <Dialog.Content>
              <Paragraph>
                {translate('userManagement.deleteWarning')}
              </Paragraph>
              <Paragraph style={styles.warningText}>
                {translate('userManagement.deleteWarningDetails')}
              </Paragraph>
              {selectedUser && (
                <Surface style={styles.userSummary}>
                  <Text style={styles.userSummaryName}>
                    {`${selectedUser.firstName || ''} ${selectedUser.lastName || ''}`}
                  </Text>
                  <Text style={styles.userSummaryEmail}>{selectedUser.email}</Text>
                  <Text style={styles.userSummaryRole}>
                    {translate(`userManagement.roles.${selectedUser.role}`) || selectedUser.role}
                  </Text>
                </Surface>
              )}
            </Dialog.Content>
            <Dialog.Actions>
              <Button
                onPress={() => setShowDeleteConfirmDialog(false)}
                style={styles.cancelButton}
              >
                {translate('common.cancel')}
              </Button>
              <Button
                mode="contained"
                onPress={() => selectedUser && handleDeleteUser(selectedUser.id)}
                style={styles.deleteButton}
                loading={loading}
                disabled={loading}
              >
                {translate('common.delete')}
              </Button>
            </Dialog.Actions>
          </Dialog>
        </Portal>
        
        {/* Activate confirmation dialog */}
        <Portal>
          <Dialog
            visible={showActivateConfirmDialog}
            onDismiss={() => setShowActivateConfirmDialog(false)}
            style={styles.dialog}
          >
            <Dialog.Title>{translate('userManagement.confirmActivate')}</Dialog.Title>
            <Dialog.Content>
              <Paragraph>
                {translate('userManagement.activateWarning')}
              </Paragraph>
              {selectedUser && (
                <Surface style={styles.userSummary}>
                  <Text style={styles.userSummaryName}>
                    {`${selectedUser.firstName || ''} ${selectedUser.lastName || ''}`}
                  </Text>
                  <Text style={styles.userSummaryEmail}>{selectedUser.email}</Text>
                  <Text style={styles.userSummaryRole}>
                    {translate(`userManagement.roles.${selectedUser.role}`) || selectedUser.role}
                  </Text>
                </Surface>
              )}
            </Dialog.Content>
            <Dialog.Actions>
              <Button
                onPress={() => setShowActivateConfirmDialog(false)}
                style={styles.cancelButton}
              >
                {translate('common.cancel')}
              </Button>
              <Button
                mode="contained"
                onPress={() => {
                  selectedUser && handleStatusChange(selectedUser.id, 'active');
                  setShowActivateConfirmDialog(false);
                }}
                style={styles.activateButton}
                loading={loading}
                disabled={loading}
              >
                {translate('userManagement.activate')}
              </Button>
            </Dialog.Actions>
          </Dialog>
        </Portal>
        
        {/* Deactivate confirmation dialog */}
        <Portal>
          <Dialog
            visible={showDeactivateConfirmDialog}
            onDismiss={() => setShowDeactivateConfirmDialog(false)}
            style={styles.dialog}
          >
            <Dialog.Title>{translate('userManagement.confirmDeactivate')}</Dialog.Title>
            <Dialog.Content>
              <Paragraph>
                {translate('userManagement.deactivateWarning')}
              </Paragraph>
              {selectedUser && (
                <Surface style={styles.userSummary}>
                  <Text style={styles.userSummaryName}>
                    {`${selectedUser.firstName || ''} ${selectedUser.lastName || ''}`}
                  </Text>
                  <Text style={styles.userSummaryEmail}>{selectedUser.email}</Text>
                  <Text style={styles.userSummaryRole}>
                    {translate(`userManagement.roles.${selectedUser.role}`) || selectedUser.role}
                  </Text>
                </Surface>
              )}
            </Dialog.Content>
            <Dialog.Actions>
              <Button
                onPress={() => setShowDeactivateConfirmDialog(false)}
                style={styles.cancelButton}
              >
                {translate('common.cancel')}
              </Button>
              <Button
                mode="contained"
                onPress={() => {
                  selectedUser && handleStatusChange(selectedUser.id, 'inactive');
                  setShowDeactivateConfirmDialog(false);
                }}
                style={styles.deactivateButton}
                loading={loading}
                disabled={loading}
              >
                {translate('userManagement.deactivate')}
              </Button>
            </Dialog.Actions>
          </Dialog>
        </Portal>
        
        {/* Password reset dialog */}
        <Portal>
          <Dialog
            visible={showPasswordResetDialog}
            onDismiss={() => setShowPasswordResetDialog(false)}
            style={styles.dialog}
          >
            <Dialog.Title>{translate('userManagement.resetPassword')}</Dialog.Title>
            <Dialog.Content>
              <Paragraph>
                {translate('userManagement.resetPasswordWarning')}
              </Paragraph>
              {selectedUser && (
                <Surface style={styles.userSummary}>
                  <Text style={styles.userSummaryName}>
                    {`${selectedUser.firstName || ''} ${selectedUser.lastName || ''}`}
                  </Text>
                  <Text style={styles.userSummaryEmail}>{selectedUser.email}</Text>
                </Surface>
              )}
            </Dialog.Content>
            <Dialog.Actions>
              <Button
                onPress={() => setShowPasswordResetDialog(false)}
                style={styles.cancelButton}
              >
                {translate('common.cancel')}
              </Button>
              <Button
                mode="contained"
                onPress={() => selectedUser && handlePasswordReset(selectedUser.email)}
                style={styles.resetButton}
                loading={passwordResetLoading}
                disabled={passwordResetLoading}
              >
                {translate('userManagement.sendResetLink')}
              </Button>
            </Dialog.Actions>
          </Dialog>
        </Portal>
      </>
    );
  };
