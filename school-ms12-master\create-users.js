// Script to create users with different roles
const { initializeApp } = require('firebase/app');
const {
  getAuth,
  createUserWithEmailAndPassword,
  signOut,
  updateProfile
} = require('firebase/auth');
const {
  getFirestore,
  doc,
  setDoc,
  serverTimestamp
} = require('firebase/firestore');

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyBHkGAhm80xBIECyaHbZ_DbeNzu3jnVuDg",
  authDomain: "schoolmn-16cbc.firebaseapp.com",
  projectId: "schoolmn-16cbc",
  storageBucket: "schoolmn-16cbc.appspot.com",
  messagingSenderId: "999485613068",
  appId: "1:999485613068:web:e9c0c3e0a4c7f4e4c8b8b8"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const auth = getAuth(app);
const db = getFirestore(app);

// User data
const users = [
  {
    email: '<EMAIL>',
    password: '1234qwer',
    role: 'admin',
    firstName: 'Admin',
    lastName: 'User',
    displayName: 'Admin User',
    phone: '1234567890',
    address: '123 Admin St',
    status: 'active',
    permissions: ['all']
  },
  {
    email: '<EMAIL>',
    password: '1234qwer',
    role: 'teacher',
    firstName: 'Teacher',
    lastName: 'User',
    displayName: 'Teacher User',
    phone: '2345678901',
    address: '456 Teacher Ave',
    status: 'active',
    subject: 'Mathematics',
    qualification: 'Masters in Education',
    experience: '5 years'
  },
  {
    email: '<EMAIL>',
    password: '1234qwer',
    role: 'student',
    firstName: 'Student',
    lastName: 'User',
    displayName: 'Student User',
    phone: '3456789012',
    address: '789 Student Blvd',
    status: 'active',
    grade: '10',
    section: 'A',
    rollNumber: 'S12345'
  },
  {
    email: '<EMAIL>',
    password: '1234qwer',
    role: 'parent',
    firstName: 'Parent',
    lastName: 'User',
    displayName: 'Parent User',
    phone: '4567890123',
    address: '101 Parent Rd',
    status: 'active',
    occupation: 'Engineer',
    relation: 'Father'
  }
];

// Function to create a user
async function createUser(userData) {
  try {
    console.log(`Creating ${userData.role} user: ${userData.email}`);

    // Create user with Firebase Auth
    const userCredential = await createUserWithEmailAndPassword(
      auth,
      userData.email,
      userData.password
    );

    const user = userCredential.user;

    // Update user profile
    await updateProfile(user, {
      displayName: userData.displayName
    });

    console.log(`Created auth user with UID: ${user.uid}`);

    // Create user document in Firestore
    await setDoc(doc(db, 'users', user.uid), {
      uid: user.uid,
      email: userData.email,
      firstName: userData.firstName,
      lastName: userData.lastName,
      displayName: userData.displayName,
      phone: userData.phone,
      address: userData.address,
      role: userData.role,
      status: userData.status,
      emailVerified: true, // Set to true to avoid verification
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    });

    console.log(`Created user document in 'users' collection`);

    // Create role-specific document
    const roleData = { ...userData };
    delete roleData.password; // Remove password from data

    await setDoc(doc(db, `${userData.role}s`, user.uid), {
      userId: user.uid,
      ...roleData,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    });

    console.log(`Created document in '${userData.role}s' collection`);

    // Sign out after creating the user
    await signOut(auth);

    return user.uid;
  } catch (error) {
    console.error(`Error creating ${userData.role} user:`, error.message);
    if (error.code === 'auth/email-already-in-use') {
      console.log(`User with email ${userData.email} already exists`);
    }
    return null;
  }
}

// Create all users
async function createAllUsers() {
  console.log('Starting user creation process...');

  for (const userData of users) {
    try {
      const uid = await createUser(userData);
      if (uid) {
        console.log(`Successfully created ${userData.role} user with UID: ${uid}`);
      }
    } catch (error) {
      console.error(`Failed to create ${userData.role} user:`, error);
    }

    // Add a small delay between user creations
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  console.log('User creation process completed');
  process.exit(0);
}

// Run the script
createAllUsers().catch(error => {
  console.error('Error in script execution:', error);
  process.exit(1);
});
