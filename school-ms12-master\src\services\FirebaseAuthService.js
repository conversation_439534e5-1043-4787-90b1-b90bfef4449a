import { auth, db } from '../config/firebase';
import {
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  signOut,
  sendPasswordResetEmail,
  updatePassword,
  updateProfile,
  sendEmailVerification,
  applyActionCode,
  verifyBeforeUpdateEmail,
  EmailAuthProvider,
  reauthenticateWithCredential
} from 'firebase/auth';
import { doc, setDoc, getDoc, collection, updateDoc, serverTimestamp } from 'firebase/firestore';

class FirebaseAuthService {
  // User Authentication
  static async signIn(email, password) {
    try {
      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      const userDoc = await getDoc(doc(db, 'users', userCredential.user.uid));
      return { user: userCredential.user, userData: userDoc.data() };
    } catch (error) {
      throw new Error('Authentication failed: ' + error.message);
    }
  }

  static async signUp(email, password, userData, role, sendVerification = true) {
    try {
      const userCredential = await createUserWithEmailAndPassword(auth, email, password);
      const { user } = userCredential;

      // Create user profile
      await updateProfile(user, {
        displayName: userData.displayName,
        photoURL: userData.photoURL || null,
      });

      // Store additional user data in Firestore
      await setDoc(doc(db, 'users', user.uid), {
        ...userData,
        role,
        email,
        emailVerified: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      });

      // Create role-specific document
      await setDoc(doc(db, `${role}s`, user.uid), {
        userId: user.uid,
        ...userData,
        createdAt: new Date().toISOString(),
      });

      // Send verification email if requested
      if (sendVerification) {
        await sendEmailVerification(user);
      }

      return { user, userData: { ...userData, role } };
    } catch (error) {
      throw new Error('Sign up failed: ' + error.message);
    }
  }

  // Send email verification
  static async sendEmailVerification(user) {
    try {
      await sendEmailVerification(user);
      return true;
    } catch (error) {
      throw new Error('Failed to send verification email: ' + error.message);
    }
  }

  // Verify email with action code
  static async verifyEmail(actionCode) {
    try {
      await applyActionCode(auth, actionCode);

      // Update user document in Firestore if user is logged in
      const user = auth.currentUser;
      if (user) {
        const userDoc = doc(db, 'users', user.uid);
        await updateDoc(userDoc, {
          emailVerified: true,
          updatedAt: new Date().toISOString()
        });
      }

      return true;
    } catch (error) {
      throw new Error('Email verification failed: ' + error.message);
    }
  }

  static async signOut() {
    try {
      await signOut(auth);
    } catch (error) {
      throw new Error('Sign out failed: ' + error.message);
    }
  }

  static async resetPassword(email) {
    try {
      await sendPasswordResetEmail(auth, email);

      // Log the password reset request
      await setDoc(doc(collection(db, 'password_reset_logs')), {
        email,
        timestamp: new Date().toISOString(),
        status: 'requested'
      });

      return true;
    } catch (error) {
      throw new Error('Password reset failed: ' + error.message);
    }
  }

  static async updateUserPassword(newPassword, currentPassword = null) {
    try {
      const user = auth.currentUser;
      if (!user) throw new Error('No user is signed in');

      // If current password is provided, reauthenticate first
      if (currentPassword) {
        const credential = EmailAuthProvider.credential(user.email, currentPassword);
        await reauthenticateWithCredential(user, credential);
      }

      await updatePassword(user, newPassword);

      // Log the password update
      await setDoc(doc(collection(db, 'password_change_logs')), {
        userId: user.uid,
        email: user.email,
        timestamp: new Date().toISOString(),
        status: 'completed'
      });

      // Update user document
      const userDoc = doc(db, 'users', user.uid);
      await updateDoc(userDoc, {
        passwordUpdatedAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      });

      return true;
    } catch (error) {
      let errorMessage = 'Password update failed';

      if (error.code === 'auth/requires-recent-login') {
        errorMessage = 'Please log in again before changing your password';
      } else if (error.code === 'auth/weak-password') {
        errorMessage = 'Password is too weak';
      } else if (error.code === 'auth/wrong-password') {
        errorMessage = 'Current password is incorrect';
      }

      throw new Error(errorMessage + ': ' + error.message);
    }
  }

  // Update user email
  static async updateUserEmail(newEmail, password) {
    try {
      const user = auth.currentUser;
      if (!user) throw new Error('No user is signed in');

      // Reauthenticate user first
      const credential = EmailAuthProvider.credential(user.email, password);
      await reauthenticateWithCredential(user, credential);

      // Update email with verification
      await verifyBeforeUpdateEmail(user, newEmail);

      // Log the email update request
      await setDoc(doc(collection(db, 'email_change_logs')), {
        userId: user.uid,
        oldEmail: user.email,
        newEmail: newEmail,
        timestamp: new Date().toISOString(),
        status: 'requested'
      });

      return {
        success: true,
        message: 'Verification email sent to new address. Please check your email to complete the change.'
      };
    } catch (error) {
      let errorMessage = 'Email update failed';

      if (error.code === 'auth/requires-recent-login') {
        errorMessage = 'Please log in again before changing your email';
      } else if (error.code === 'auth/invalid-email') {
        errorMessage = 'New email address is invalid';
      } else if (error.code === 'auth/email-already-in-use') {
        errorMessage = 'Email address is already in use';
      } else if (error.code === 'auth/wrong-password') {
        errorMessage = 'Password is incorrect';
      }

      throw new Error(errorMessage + ': ' + error.message);
    }
  }

  static async updateUserProfile(profileData) {
    try {
      const user = auth.currentUser;
      if (!user) throw new Error('No user is signed in');

      // Update auth profile
      await updateProfile(user, {
        displayName: profileData.displayName,
        photoURL: profileData.photoURL,
      });

      // Update Firestore user document
      await setDoc(doc(db, 'users', user.uid), profileData, { merge: true });

      // Get user role and update role-specific document
      const userDoc = await getDoc(doc(db, 'users', user.uid));
      const { role } = userDoc.data();
      await setDoc(doc(db, `${role}s`, user.uid), profileData, { merge: true });

      return { user, profileData };
    } catch (error) {
      throw new Error('Profile update failed: ' + error.message);
    }
  }

  static async getUserData(userId) {
    try {
      const userDoc = await getDoc(doc(db, 'users', userId));
      if (!userDoc.exists()) throw new Error('User not found');
      return userDoc.data();
    } catch (error) {
      throw new Error('Failed to get user data: ' + error.message);
    }
  }

  static async getRoleData(userId, role) {
    try {
      const roleDoc = await getDoc(doc(db, `${role}s`, userId));
      if (!roleDoc.exists()) throw new Error('Role data not found');
      return roleDoc.data();
    } catch (error) {
      throw new Error('Failed to get role data: ' + error.message);
    }
  }
}

export default FirebaseAuthService;
