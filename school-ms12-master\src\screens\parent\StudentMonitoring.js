import React, { useState, useEffect } from 'react';
import { View, ScrollView, StyleSheet } from 'react-native';
import { Card, Title, Text, DataTable, List, Chip, Portal, Modal } from 'react-native-paper';
import { db } from '../../config/firebase';
import { collection, query, getDocs, where } from 'firebase/firestore';
import { useAuth } from '../../context/AuthContext';
import CustomButton from '../../components/common/CustomButton';

const StudentMonitoring = () => {
  const { user } = useAuth();
  const [student, setStudent] = useState(null);
  const [attendance, setAttendance] = useState([]);
  const [results, setResults] = useState([]);
  const [selectedResult, setSelectedResult] = useState(null);
  const [visible, setVisible] = useState(false);

  useEffect(() => {
    fetchStudentInfo();
  }, []);

  useEffect(() => {
    if (student) {
      fetchAttendance();
      fetchResults();
    }
  }, [student]);

  const fetchStudentInfo = async () => {
    try {
      const parentsRef = collection(db, 'parents');
      const parentDoc = await getDocs(query(parentsRef, where('userId', '==', user.uid)));
      
      if (!parentDoc.empty) {
        const parentData = parentDoc.docs[0].data();
        const studentsRef = collection(db, 'students');
        const studentDoc = await getDocs(query(studentsRef, where('id', '==', parentData.studentId)));
        
        if (!studentDoc.empty) {
          setStudent(studentDoc.docs[0].data());
        }
      }
    } catch (error) {
      console.error('Error fetching student info:', error);
    }
  };

  const fetchAttendance = async () => {
    try {
      const attendanceRef = collection(db, 'attendance');
      const q = query(attendanceRef, where('studentId', '==', student.id));
      const querySnapshot = await getDocs(q);
      
      const attendanceData = [];
      querySnapshot.forEach((doc) => {
        attendanceData.push({ id: doc.id, ...doc.data() });
      });
      
      setAttendance(attendanceData);
    } catch (error) {
      console.error('Error fetching attendance:', error);
    }
  };

  const fetchResults = async () => {
    try {
      const resultsRef = collection(db, 'results');
      const q = query(
        resultsRef,
        where('studentId', '==', student.id),
        where('status', '==', 'published')
      );
      const querySnapshot = await getDocs(q);
      
      const resultsData = [];
      querySnapshot.forEach((doc) => {
        resultsData.push({ id: doc.id, ...doc.data() });
      });
      
      setResults(resultsData);
    } catch (error) {
      console.error('Error fetching results:', error);
    }
  };

  const calculateAttendancePercentage = () => {
    if (!attendance.length) return 0;
    const present = attendance.filter(a => a.status === 'present').length;
    return ((present / attendance.length) * 100).toFixed(1);
  };

  const getAttendanceColor = (percentage) => {
    if (percentage >= 90) return '#4CAF50';
    if (percentage >= 80) return '#8BC34A';
    if (percentage >= 70) return '#FFC107';
    if (percentage >= 60) return '#FF9800';
    return '#F44336';
  };

  return (
    <ScrollView style={styles.container}>
      {student && (
        <Card style={styles.studentCard}>
          <Card.Content>
            <Title>{student.name}</Title>
            <Text>Class: {student.className} - Section {student.section}</Text>
            <Text>Roll No: {student.rollNo}</Text>
            <Text>Admission No: {student.admissionNumber}</Text>
          </Card.Content>
        </Card>
      )}

      <Card style={styles.attendanceCard}>
        <Card.Content>
          <Title>Attendance Overview</Title>
          
          <View style={styles.attendanceStats}>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>
                {calculateAttendancePercentage()}%
              </Text>
              <Text style={styles.statLabel}>Overall Attendance</Text>
            </View>
            
            <View style={styles.statItem}>
              <Text style={styles.statValue}>
                {attendance.filter(a => a.status === 'present').length}
              </Text>
              <Text style={styles.statLabel}>Days Present</Text>
            </View>
            
            <View style={styles.statItem}>
              <Text style={styles.statValue}>
                {attendance.filter(a => a.status === 'absent').length}
              </Text>
              <Text style={styles.statLabel}>Days Absent</Text>
            </View>
          </View>

          <DataTable>
            <DataTable.Header>
              <DataTable.Title>Date</DataTable.Title>
              <DataTable.Title>Status</DataTable.Title>
              <DataTable.Title>Remarks</DataTable.Title>
            </DataTable.Header>

            {attendance.slice(0, 5).map((record) => (
              <DataTable.Row key={record.id}>
                <DataTable.Cell>
                  {new Date(record.date).toLocaleDateString()}
                </DataTable.Cell>
                <DataTable.Cell>
                  <Chip
                    mode="outlined"
                    selectedColor={record.status === 'present' ? '#4CAF50' : '#F44336'}
                  >
                    {record.status}
                  </Chip>
                </DataTable.Cell>
                <DataTable.Cell>{record.remarks || '-'}</DataTable.Cell>
              </DataTable.Row>
            ))}
          </DataTable>
        </Card.Content>
      </Card>

      <Card style={styles.resultsCard}>
        <Card.Content>
          <Title>Academic Performance</Title>

          {results.map((result) => (
            <List.Item
              key={result.id}
              title={result.examName}
              description={`Term: ${result.term} | Year: ${result.year}`}
              onPress={() => {
                setSelectedResult(result);
                setVisible(true);
              }}
              right={props => (
                <Chip
                  mode="outlined"
                  selectedColor={getAttendanceColor(result.percentage)}
                >
                  {result.percentage}%
                </Chip>
              )}
            />
          ))}
        </Card.Content>
      </Card>

      <Portal>
        <Modal
          visible={visible}
          onDismiss={() => setVisible(false)}
          contentContainerStyle={styles.modalContent}
        >
          {selectedResult && (
            <ScrollView>
              <Title>{selectedResult.examName}</Title>
              <Text style={styles.modalSubtitle}>
                Term: {selectedResult.term} | Year: {selectedResult.year}
              </Text>

              <DataTable>
                <DataTable.Header>
                  <DataTable.Title>Subject</DataTable.Title>
                  <DataTable.Title numeric>Marks</DataTable.Title>
                  <DataTable.Title numeric>Grade</DataTable.Title>
                  <DataTable.Title>Remarks</DataTable.Title>
                </DataTable.Header>

                {selectedResult.subjects.map((subject) => (
                  <DataTable.Row key={subject.name}>
                    <DataTable.Cell>{subject.name}</DataTable.Cell>
                    <DataTable.Cell numeric>{subject.marks}</DataTable.Cell>
                    <DataTable.Cell numeric>{subject.grade}</DataTable.Cell>
                    <DataTable.Cell>{subject.remarks}</DataTable.Cell>
                  </DataTable.Row>
                ))}
              </DataTable>

              {selectedResult.teacherComments && (
                <Card style={styles.commentsCard}>
                  <Card.Content>
                    <Title>Teacher's Comments</Title>
                    <Text>{selectedResult.teacherComments}</Text>
                  </Card.Content>
                </Card>
              )}

              <CustomButton
                mode="outlined"
                onPress={() => setVisible(false)}
                style={styles.closeButton}
              >
                Close
              </CustomButton>
            </ScrollView>
          )}
        </Modal>
      </Portal>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  studentCard: {
    margin: 10,
  },
  attendanceCard: {
    margin: 10,
  },
  resultsCard: {
    margin: 10,
  },
  attendanceStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginVertical: 20,
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  statLabel: {
    color: '#666',
    marginTop: 5,
  },
  modalContent: {
    backgroundColor: 'white',
    padding: 20,
    margin: 20,
    borderRadius: 10,
    maxHeight: '80%',
  },
  modalSubtitle: {
    color: '#666',
    marginBottom: 20,
  },
  commentsCard: {
    marginTop: 20,
    backgroundColor: '#f9f9f9',
  },
  closeButton: {
    marginTop: 20,
  },
});

export default StudentMonitoring;
