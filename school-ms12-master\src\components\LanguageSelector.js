import React from 'react';
import { useTranslation } from '../hooks/useTranslation';
import { Select, FormControl, InputLabel, MenuItem } from '@mui/material';
import { styled } from '@mui/material/styles';

const StyledFormControl = styled(FormControl)(({ theme }) => ({
    margin: theme.spacing(1),
    minWidth: 120,
    '& .MuiSelect-select': {
        paddingRight: theme.spacing(4),
    },
}));

const LanguageSelector = () => {
    const { language, changeLanguage, t } = useTranslation();

    const languages = [
        { code: 'en', name: 'English', flag: '🇬🇧' },
        { code: 'am', name: 'አማርኛ', flag: '🇪🇹' },
        { code: 'om', name: '<PERSON><PERSON><PERSON>', flag: '🇪🇹' },
        { code: 'ctu', name: 'Ctu', flag: '🌐' }
    ];

    const handleChange = (event) => {
        changeLanguage(event.target.value);
    };

    return (
        <StyledFormControl>
            <InputLabel id="language-select-label">
                {t('language')}
            </InputLabel>
            <Select
                labelId="language-select-label"
                id="language-select"
                value={language}
                onChange={handleChange}
                label={t('language')}
            >
                {languages.map((lang) => (
                    <MenuItem key={lang.code} value={lang.code}>
                        <span style={{ marginRight: '8px' }}>{lang.flag}</span>
                        {lang.name}
                    </MenuItem>
                ))}
            </Select>
        </StyledFormControl>
    );
};

export default LanguageSelector;
