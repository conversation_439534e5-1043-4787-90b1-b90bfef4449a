rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // Helper functions
    function isAdmin() {
      return request.auth != null &&
        firestore.exists(/databases/(default)/documents/users/$(request.auth.uid)) &&
        firestore.get(/databases/(default)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }

    function isTeacher() {
      return request.auth != null &&
        firestore.exists(/databases/(default)/documents/users/$(request.auth.uid)) &&
        firestore.get(/databases/(default)/documents/users/$(request.auth.uid)).data.role == 'teacher';
    }

    function isStudent() {
      return request.auth != null &&
        firestore.exists(/databases/(default)/documents/users/$(request.auth.uid)) &&
        firestore.get(/databases/(default)/documents/users/$(request.auth.uid)).data.role == 'student';
    }

    function isParent() {
      return request.auth != null &&
        firestore.exists(/databases/(default)/documents/users/$(request.auth.uid)) &&
        firestore.get(/databases/(default)/documents/users/$(request.auth.uid)).data.role == 'parent';
    }

    function isAuthenticated() {
      return request.auth != null;
    }

    // Allow admins to read and write all files
    match /{allPaths=**} {
      allow read, write: if isAdmin();
    }

    // Allow users to read and write their own profile images
    match /profileImages/{userId}/{fileName} {
      allow read: if isAuthenticated();
      allow write: if request.auth.uid == userId;
    }

    // Allow teachers to read and write their own files
    match /teachers/{teacherId}/{allPaths=**} {
      allow read: if isAuthenticated();
      allow write: if request.auth.uid == teacherId || isAdmin();
    }

    // Allow students to read and write their own files
    match /students/{studentId}/{allPaths=**} {
      allow read: if isAuthenticated();
      allow write: if request.auth.uid == studentId || isAdmin();
    }

    // Allow parents to read and write their own files
    match /parents/{parentId}/{allPaths=**} {
      allow read: if isAuthenticated();
      allow write: if request.auth.uid == parentId || isAdmin();
    }

    // Allow access to public resources
    match /resources/{resourceId} {
      allow read: if isAuthenticated();
      allow write: if isAdmin() || isTeacher();
    }

    // Allow access to shared files
    match /shared/{fileId} {
      allow read: if isAuthenticated();
      allow write: if isAdmin() || isTeacher();
    }
  }
}
