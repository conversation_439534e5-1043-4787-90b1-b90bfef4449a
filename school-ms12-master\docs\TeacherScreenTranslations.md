# Teacher Screen Translations Guide

This document provides a guide for adding translations to teacher screens in the School Management System. It includes mappings of screens to their corresponding translation keys.

## Table of Contents
- [General Structure](#general-structure)
- [How to Implement](#how-to-implement)
- [Teacher Dashboard](#teacher-dashboard)
- [Attendance Management](#attendance-management)
- [Grade Management](#grade-management)
- [Homework Management](#homework-management)
- [Exam Schedule](#exam-schedule)
- [Class Management](#class-management)
- [Calendar Management](#calendar-management)

## General Structure

The translation keys for teacher screens follow a common pattern:

```
teacher.<feature>.<key>
```

For example, `teacher.dashboard.title` would contain the title for the Teacher Dashboard screen.

## How to Implement

1. In your component, import the useLanguage hook:
   ```javascript
   import { useLanguage } from '../../context/LanguageContext';
   ```

2. Use the translate function:
   ```javascript
   const { translate, getTextStyle } = useLanguage();
   
   // In your JSX:
   <Text style={getTextStyle()}>{translate('teacher.dashboard.title')}</Text>
   ```

3. For text with dynamic content, use parameters:
   ```javascript
   <Text>{translate('teacher.dashboard.welcome', { name: teacher<PERSON><PERSON> })}</Text>
   ```

## Teacher Dashboard

The Teacher Dashboard screen uses keys under the `teacher.dashboard` namespace:

| UI Element | Translation Key | Description |
|------------|-----------------|-------------|
| Screen title | `teacher.dashboard.title` | "Teacher Dashboard" |
| Welcome message | `teacher.dashboard.welcome` | "Welcome, {name}" |
| Overview section | `teacher.dashboard.overview` | "Overview" |
| Quick actions | `teacher.dashboard.quickActions` | "Quick Actions" |
| Recent activity | `teacher.dashboard.recentActivity` | "Recent Activity" |
| Statistics | `teacher.dashboard.statistics` | "Statistics" |
| Total students | `teacher.dashboard.totalStudents` | "Total Students" |
| Total classes | `teacher.dashboard.totalClasses` | "Total Classes" |
| Upcoming events | `teacher.dashboard.upcomingEvents` | "Upcoming Events" |
| Attendance | `teacher.dashboard.attendance` | "Student Attendance" |
| Today's schedule | `teacher.dashboard.todaySchedule` | "Today's Schedule" |
| View all | `teacher.dashboard.viewAll` | "View All" |

## Attendance Management

The Attendance Management screen uses keys under the `teacher.attendance` namespace:

| UI Element | Translation Key | Description |
|------------|-----------------|-------------|
| Screen title | `teacher.attendance.title` | "Attendance Management" |
| Take attendance | `teacher.attendance.takeAttendance` | "Take Attendance" |
| View attendance | `teacher.attendance.viewAttendance` | "View Attendance" |
| Attendance history | `teacher.attendance.attendanceHistory` | "Attendance History" |
| Mark present | `teacher.attendance.markPresent` | "Mark Present" |
| Mark absent | `teacher.attendance.markAbsent` | "Mark Absent" |
| Mark late | `teacher.attendance.markLate` | "Mark Late" |
| Student list | `teacher.attendance.studentList` | "Student List" |
| Status | `teacher.attendance.status` | "Status" |
| Submit attendance | `teacher.attendance.submitAttendance` | "Submit Attendance" |

Additional error and notification messages are found under `attendanceManagement` namespace.

## Grade Management

The Grade Management screen uses keys under the `teacher.grades` namespace:

| UI Element | Translation Key | Description |
|------------|-----------------|-------------|
| Screen title | `teacher.grades.title` | "Grade Management" |
| Enter grades | `teacher.grades.enterGrades` | "Enter Grades" |
| View grades | `teacher.grades.viewGrades` | "View Grades" |
| Grade history | `teacher.grades.gradeHistory` | "Grade History" |
| Assignment | `teacher.grades.assignment` | "Assignment" |
| Exam | `teacher.grades.exam` | "Exam" |
| Quiz | `teacher.grades.quiz` | "Quiz" |
| Project | `teacher.grades.project` | "Project" |
| Midterm | `teacher.grades.midterm` | "Midterm Exam" |
| Final | `teacher.grades.final` | "Final Exam" |
| Grade type | `teacher.grades.gradeType` | "Grade Type" |
| Max points | `teacher.grades.maxPoints` | "Maximum Points" |
| Class average | `teacher.grades.classAverage` | "Class Average" |

## Homework Management

The Homework Management screen uses keys under the `teacher.homework` namespace:

| UI Element | Translation Key | Description |
|------------|-----------------|-------------|
| Screen title | `teacher.homework.title` | "Homework Management" |
| Create homework | `teacher.homework.createHomework` | "Create Homework" |
| View homework | `teacher.homework.viewHomework` | "View Homework" |
| Homework history | `teacher.homework.homeworkHistory` | "Homework History" |
| Assign to | `teacher.homework.assignTo` | "Assign To" |
| Due date | `teacher.homework.dueDate` | "Due Date" |
| Instructions | `teacher.homework.instructions` | "Instructions" |
| Attachments | `teacher.homework.attachments` | "Attachments" |
| Submission status | `teacher.homework.submissionStatus` | "Submission Status" |

## Exam Schedule

The Exam Schedule screen uses keys under the `teacher.exams` namespace:

| UI Element | Translation Key | Description |
|------------|-----------------|-------------|
| Screen title | `teacher.exams.title` | "Exam Schedule" |
| Create exam | `teacher.exams.createExam` | "Create Exam" |
| View exams | `teacher.exams.viewExams` | "View Exams" |
| Exam history | `teacher.exams.examHistory` | "Exam History" |
| Exam date | `teacher.exams.examDate` | "Exam Date" |
| Exam duration | `teacher.exams.examDuration` | "Exam Duration" |
| Exam type | `teacher.exams.examType` | "Exam Type" |
| Upcoming exams | `teacher.exams.upcomingExams` | "Upcoming Exams" |
| Past exams | `teacher.exams.pastExams` | "Past Exams" |

## Class Management

The Class Management screen uses keys under the `teacher.classes` namespace:

| UI Element | Translation Key | Description |
|------------|-----------------|-------------|
| Screen title | `teacher.classes.title` | "Class Management" |
| Create class | `teacher.classes.createClass` | "Create Class" |
| View classes | `teacher.classes.viewClasses` | "View Classes" |
| Class name | `teacher.classes.className` | "Class Name" |
| Class section | `teacher.classes.classSection` | "Class Section" |
| Class schedule | `teacher.classes.classSchedule` | "Class Schedule" |
| Student list | `teacher.classes.studentList` | "Student List" |
| Class performance | `teacher.classes.classPerformance` | "Class Performance" |
| Attendance overview | `teacher.classes.attendanceOverview` | "Attendance Overview" |

## Calendar Management

The Calendar Management screen uses keys under the `teacher.calendar` namespace:

| UI Element | Translation Key | Description |
|------------|-----------------|-------------|
| Screen title | `teacher.calendar.title` | "Calendar Management" |
| Create event | `teacher.calendar.createEvent` | "Create Event" |
| View calendar | `teacher.calendar.viewCalendar` | "View Calendar" |
| Event date | `teacher.calendar.eventDate` | "Event Date" |
| Event time | `teacher.calendar.eventTime` | "Event Time" |
| Event description | `teacher.calendar.eventDescription` | "Event Description" |
| Upcoming events | `teacher.calendar.upcomingEvents` | "Upcoming Events" |
| Today | `teacher.calendar.today` | "Today" |
| Week | `teacher.calendar.week` | "Week" |
| Month | `teacher.calendar.month` | "Month" |
| Agenda | `teacher.calendar.agenda` | "Agenda" | 