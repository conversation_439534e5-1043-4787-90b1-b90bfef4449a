import React, { useState } from 'react';
import { View, StyleSheet, TouchableOpacity, StatusBar, Platform } from 'react-native';
import { Surface, Text, IconButton, Avatar, Badge } from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { useLanguage } from '../../context/LanguageContext';
import { useAuth } from '../../context/AuthContext';
import * as Animatable from 'react-native-animatable';
import { LinearGradient } from 'expo-linear-gradient';
import mobileTheme from '../../theme/mobileTheme';
import MobileNotificationBadge from './MobileNotificationBadge';

/**
 * Mobile-optimized header component
 * @param {Object} props - Component props
 * @param {string} props.title - Header title
 * @param {boolean} props.showBack - Whether to show back button
 * @param {Function} props.onMenuPress - Function to call when menu button is pressed
 * @param {boolean} props.showNotification - Whether to show notification icon
 * @param {boolean} props.showProfile - Whether to show profile icon
 * @param {boolean} props.showLanguage - Whether to show language selector
 * @param {boolean} props.transparent - Whether header should be transparent
 * @param {string} props.userRole - User role (admin, teacher, student, parent)
 */
const MobileHeader = ({
  title,
  showBack = false,
  onMenuPress,
  showNotification = true,
  showProfile = true,
  showLanguage = true,
  transparent = false,
  userRole = 'admin'
}) => {
  const navigation = useNavigation();
  const { translate, getTextStyle, isRTL } = useLanguage();
  const { user } = useAuth();
  const [menuVisible, setMenuVisible] = useState(false);

  // Get role-specific gradient colors
  const getGradientColors = () => {
    switch(userRole) {
      case 'admin': return ['#3949ab', '#1976d2'];
      case 'teacher': return ['#00897b', '#26a69a'];
      case 'student': return ['#d32f2f', '#ef5350'];
      case 'parent': return ['#f57c00', '#ffa726'];
      default: return ['#1976d2', '#42a5f5'];
    }
  };

  const handleBack = () => {
    navigation.goBack();
  };

  const handleNotification = () => {
    navigation.navigate('NotificationCenter');
  };

  const handleProfile = () => {
    navigation.navigate('ProfileManagement');
  };

  const handleLanguage = () => {
    // Show language selector
    // This will be implemented separately
  };

  const getInitials = () => {
    if (!user) return '?';
    const firstName = user.firstName || '';
    const lastName = user.lastName || '';
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  };

  return (
    <>
      <StatusBar
        backgroundColor={transparent ? 'transparent' : getGradientColors()[0]}
        barStyle="light-content"
        translucent={transparent}
      />

      <Surface style={[
        styles.container,
        transparent && styles.transparentContainer
      ]}>
        <LinearGradient
          colors={getGradientColors()}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={styles.gradient}
        >
          <View style={[styles.content, isRTL && styles.contentRTL]}>
            {/* Left section */}
            <View style={styles.leftSection}>
              {showBack ? (
                <IconButton
                  icon={isRTL ? "arrow-right" : "arrow-left"}
                  color="white"
                  size={24}
                  onPress={handleBack}
                  style={styles.backButton}
                />
              ) : (
                <IconButton
                  icon="menu"
                  color="white"
                  size={24}
                  onPress={onMenuPress}
                  style={styles.menuButton}
                />
              )}

              <Animatable.Text
                animation="fadeIn"
                duration={500}
                style={[styles.title, getTextStyle({ fontSize: 18 })]}
                numberOfLines={1}
              >
                {translate(title) || title}
              </Animatable.Text>
            </View>

            {/* Right section */}
            <View style={[styles.rightSection, isRTL && styles.rightSectionRTL]}>
              {showLanguage && (
                <IconButton
                  icon="translate"
                  color="white"
                  size={22}
                  onPress={handleLanguage}
                  style={styles.actionButton}
                />
              )}

              {showNotification && (
                <View style={styles.notificationContainer}>
                  <IconButton
                    icon="bell"
                    color="white"
                    size={22}
                    onPress={handleNotification}
                    style={styles.actionButton}
                  />
                  <MobileNotificationBadge style={styles.notificationBadge} />
                </View>
              )}

              {showProfile && (
                <TouchableOpacity
                  onPress={handleProfile}
                  style={styles.profileButton}
                >
                  <Avatar.Text
                    size={32}
                    label={getInitials()}
                    style={styles.avatar}
                    labelStyle={styles.avatarLabel}
                  />
                </TouchableOpacity>
              )}
            </View>
          </View>
        </LinearGradient>
      </Surface>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    height: mobileTheme.mobile.headerHeight + (Platform.OS === 'android' ? StatusBar.currentHeight : 0),
    elevation: 4,
    zIndex: 100,
  },
  transparentContainer: {
    backgroundColor: 'transparent',
    elevation: 0,
  },
  gradient: {
    flex: 1,
    paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0,
  },
  content: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 4,
  },
  contentRTL: {
    flexDirection: 'row-reverse',
  },
  leftSection: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  rightSection: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rightSectionRTL: {
    flexDirection: 'row-reverse',
  },
  title: {
    color: 'white',
    fontSize: 18,
    fontWeight: '500',
    marginLeft: 4,
    flex: 1,
  },
  backButton: {
    marginRight: 0,
  },
  menuButton: {
    marginRight: 0,
  },
  actionButton: {
    marginHorizontal: 0,
  },
  notificationContainer: {
    position: 'relative',
  },
  notificationBadge: {
    position: 'absolute',
    top: 6,
    right: 6,
  },
  profileButton: {
    marginLeft: 4,
    marginRight: 8,
  },
  avatar: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  avatarLabel: {
    fontSize: 14,
    color: 'white',
  },
});

export default MobileHeader;
