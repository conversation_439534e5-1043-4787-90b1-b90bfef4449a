import { auth, db } from '../config/firebase';
import { doc, setDoc, getDoc, updateDoc } from 'firebase/firestore';
import { signInWithEmailAndPassword, sendPasswordResetEmail } from 'firebase/auth';
import { encrypt, decrypt } from '../utils/encryption';

class SecurityService {
    static instance = null;
    
    constructor() {
        if (SecurityService.instance) {
            return SecurityService.instance;
        }
        SecurityService.instance = this;
        this.loginAttempts = new Map();
        this.activeSessions = new Map();
    }

    // Session Management
    async createSession(userId, role) {
        const sessionToken = encrypt(JSON.stringify({
            userId,
            role,
            createdAt: Date.now(),
            expiresAt: Date.now() + (30 * 60 * 1000) // 30 minutes
        }));

        await setDoc(doc(db, 'sessions', userId), {
            token: sessionToken,
            lastActivity: Date.now()
        });

        this.activeSessions.set(userId, sessionToken);
        return sessionToken;
    }

    async validateSession(userId, token) {
        const sessionDoc = await getDoc(doc(db, 'sessions', userId));
        if (!sessionDoc.exists()) return false;

        const session = decrypt(sessionDoc.data().token);
        const sessionData = JSON.parse(session);

        if (Date.now() > sessionData.expiresAt) {
            await this.invalidateSession(userId);
            return false;
        }

        // Update last activity
        await updateDoc(doc(db, 'sessions', userId), {
            lastActivity: Date.now()
        });

        return true;
    }

    async invalidateSession(userId) {
        await setDoc(doc(db, 'sessions', userId), {
            token: null,
            lastActivity: null
        });
        this.activeSessions.delete(userId);
    }

    // Login Security
    async handleLoginAttempt(email, password) {
        const attempts = this.loginAttempts.get(email) || { count: 0, lastAttempt: 0 };
        
        // Check if account is locked
        if (attempts.count >= 5 && Date.now() - attempts.lastAttempt < 15 * 60 * 1000) {
            throw new Error('Account is temporarily locked. Please try again later.');
        }

        try {
            const userCredential = await signInWithEmailAndPassword(auth, email, password);
            // Reset attempts on successful login
            this.loginAttempts.delete(email);
            return userCredential;
        } catch (error) {
            // Update failed attempts
            attempts.count += 1;
            attempts.lastAttempt = Date.now();
            this.loginAttempts.set(email, attempts);

            if (attempts.count >= 5) {
                // Send notification to admin
                await this.notifyAdmin('security', {
                    type: 'login_attempts_exceeded',
                    email,
                    timestamp: Date.now()
                });
            }
            throw error;
        }
    }

    // Password Security
    validatePassword(password) {
        const minLength = 8;
        const hasUpperCase = /[A-Z]/.test(password);
        const hasLowerCase = /[a-z]/.test(password);
        const hasNumbers = /\d/.test(password);
        const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);

        if (password.length < minLength) {
            throw new Error('Password must be at least 8 characters long');
        }
        if (!hasUpperCase || !hasLowerCase) {
            throw new Error('Password must contain both uppercase and lowercase letters');
        }
        if (!hasNumbers) {
            throw new Error('Password must contain at least one number');
        }
        if (!hasSpecialChar) {
            throw new Error('Password must contain at least one special character');
        }

        return true;
    }

    // Security Notifications
    async notifyAdmin(type, data) {
        await setDoc(doc(db, 'security_alerts', Date.now().toString()), {
            type,
            data,
            timestamp: Date.now(),
            status: 'unread'
        });
    }

    // Activity Logging
    async logActivity(userId, action, details) {
        await setDoc(doc(db, 'activity_logs', Date.now().toString()), {
            userId,
            action,
            details,
            timestamp: Date.now(),
            ipAddress: await this.getClientIP(),
            userAgent: navigator.userAgent
        });
    }

    // IP Address Management
    async getClientIP() {
        try {
            const response = await fetch('https://api.ipify.org?format=json');
            const data = await response.json();
            return data.ip;
        } catch (error) {
            console.error('Error getting IP:', error);
            return 'unknown';
        }
    }

    // Ethiopian-specific security validations
    validateEthiopianStudentId(studentId) {
        const pattern = /^ETH-[0-9]{8}$/;
        return pattern.test(studentId);
    }

    validateEthiopianPhoneNumber(phone) {
        const pattern = /^(\+251|0)[0-9]{9}$/;
        return pattern.test(phone);
    }
}

export default new SecurityService();
