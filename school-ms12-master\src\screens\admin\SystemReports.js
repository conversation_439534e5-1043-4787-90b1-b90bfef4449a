import React, { useState, useEffect } from 'react';
import { View, ScrollView, StyleSheet, Dimensions } from 'react-native';
import { Card, Title, Text, Portal, Modal, DataTable, List, ActivityIndicator } from 'react-native-paper';
import { db } from '../../config/firebase';
import { collection, query, getDocs, where, doc, getDoc } from 'firebase/firestore';
import { LineChart, PieChart } from 'react-native-chart-kit';
import CustomButton from '../../components/common/CustomButton';
import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';
import { sanitizeChartData, sanitizeChartDatasets } from '../../utils/ChartUtils';

const screenWidth = Dimensions.get('window').width;

const chartConfig = {
  backgroundColor: '#ffffff',
  backgroundGradientFrom: '#ffffff',
  backgroundGradientTo: '#ffffff',
  decimalPlaces: 0,
  color: (opacity = 1) => `rgba(0, 0, 0, ${opacity})`,
  style: {
    borderRadius: 16,
  },
  propsForDots: {
    r: '6',
    strokeWidth: '2',
    stroke: '#ffa726',
  },
};

const SystemReports = () => {
  const [userStats, setUserStats] = useState({
    total: 0,
    teachers: 0,
    students: 0,
    parents: 0,
  });

  const [activityStats, setActivityStats] = useState({
    totalLogins: 0,
    activeUsers: 0,
    lastWeekActivities: [0, 0, 0, 0, 0, 0, 0],
  });

  const [libraryStats, setLibraryStats] = useState({
    totalBooks: 0,
    borrowedBooks: 0,
    overdueBooks: 0,
    popularBooks: [],
  });

  const [examStats, setExamStats] = useState({
    totalExams: 0,
    ongoingExams: 0,
    completedExams: 0,
    averageScores: [],
  });

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [selectedReport, setSelectedReport] = useState(null);
  const [visible, setVisible] = useState(false);

  useEffect(() => {
    fetchAllStats();
  }, []);

  const fetchAllStats = async () => {
    try {
      setLoading(true);
      setError('');
      await Promise.all([
        fetchUserStats(),
        fetchActivityStats(),
        fetchLibraryStats(),
        fetchExamStats(),
      ]);
    } catch (error) {
      console.error('Error fetching stats:', error);
      setError('Failed to load statistics. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const fetchUserStats = async () => {
    try {
      const usersRef = collection(db, 'users');

      const teacherQuery = query(usersRef, where('role', '==', 'teacher'));
      const studentQuery = query(usersRef, where('role', '==', 'student'));
      const parentQuery = query(usersRef, where('role', '==', 'parent'));

      const [teacherSnap, studentSnap, parentSnap] = await Promise.all([
        getDocs(teacherQuery),
        getDocs(studentQuery),
        getDocs(parentQuery),
      ]);

      setUserStats({
        teachers: teacherSnap.size,
        students: studentSnap.size,
        parents: parentSnap.size,
        total: teacherSnap.size + studentSnap.size + parentSnap.size,
      });
    } catch (error) {
      console.error('Error fetching user stats:', error);
      throw error;
    }
  };

  const fetchActivityStats = async () => {
    try {
      const activitiesRef = collection(db, 'userActivities');
      const lastWeek = new Date();
      lastWeek.setDate(lastWeek.getDate() - 7);

      const activityQuery = query(
        activitiesRef,
        where('timestamp', '>=', lastWeek)
      );

      const querySnapshot = await getDocs(activityQuery);
      const activities = [];
      querySnapshot.forEach((doc) => {
        activities.push({ id: doc.id, ...doc.data() });
      });

      const dailyCounts = new Array(7).fill(0);
      const uniqueUsers = new Set();

      activities.forEach((activity) => {
        if (activity.timestamp) {
          const activityDate = activity.timestamp.toDate ?
            activity.timestamp.toDate() :
            new Date(activity.timestamp);
          const dayIndex = 6 - Math.floor((new Date() - activityDate) / (1000 * 60 * 60 * 24));
          if (dayIndex >= 0 && dayIndex < 7) {
            dailyCounts[dayIndex]++;
            uniqueUsers.add(activity.userId);
          }
        }
      });

      setActivityStats({
        totalLogins: activities.length,
        activeUsers: uniqueUsers.size,
        lastWeekActivities: dailyCounts,
      });
    } catch (error) {
      console.error('Error fetching activity stats:', error);
      throw error;
    }
  };

  const fetchLibraryStats = async () => {
    try {
      const booksRef = collection(db, 'books');
      const borrowingsRef = collection(db, 'bookBorrowings');

      const [booksSnap, borrowingsSnap] = await Promise.all([
        getDocs(booksRef),
        getDocs(query(borrowingsRef, where('status', '==', 'borrowed'))),
      ]);

      const now = new Date();
      const overdue = borrowingsSnap.docs.filter((doc) => {
        const data = doc.data();
        if (!data.dueDate) return false;
        const dueDate = data.dueDate.toDate ?
          data.dueDate.toDate() :
          new Date(data.dueDate);
        return dueDate < now;
      });

      const bookBorrowCounts = {};
      borrowingsSnap.forEach((doc) => {
        const data = doc.data();
        if (data.bookId) {
          bookBorrowCounts[data.bookId] = (bookBorrowCounts[data.bookId] || 0) + 1;
        }
      });

      const popularBooks = await Promise.all(
        Object.entries(bookBorrowCounts)
          .sort(([, a], [, b]) => b - a)
          .slice(0, 5)
          .map(async ([bookId, count]) => {
            try {
              const bookDoc = await getDoc(doc(db, 'books', bookId));
              return {
                id: bookId,
                title: bookDoc.exists() ? bookDoc.data().title : 'Unknown Book',
                borrowCount: count,
              };
            } catch (error) {
              console.error(`Error fetching book ${bookId}:`, error);
              return {
                id: bookId,
                title: 'Error Loading Book',
                borrowCount: count,
              };
            }
          })
      );

      setLibraryStats({
        totalBooks: booksSnap.size,
        borrowedBooks: borrowingsSnap.size,
        overdueBooks: overdue.length,
        popularBooks,
      });
    } catch (error) {
      console.error('Error fetching library stats:', error);
      throw error;
    }
  };

  const fetchExamStats = async () => {
    try {
      const examsRef = collection(db, 'exams');
      const resultsRef = collection(db, 'examResults');

      const [examsSnap, resultsSnap] = await Promise.all([
        getDocs(examsRef),
        getDocs(resultsRef),
      ]);

      const now = new Date();
      const exams = examsSnap.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
      }));

      const ongoing = exams.filter(exam => {
        if (!exam.startDate || !exam.endDate) return false;
        const startDate = exam.startDate.toDate ?
          exam.startDate.toDate() :
          new Date(exam.startDate);
        const endDate = exam.endDate.toDate ?
          exam.endDate.toDate() :
          new Date(exam.endDate);
        return startDate <= now && endDate >= now;
      });

      const completed = exams.filter(exam => {
        if (!exam.endDate) return false;
        const endDate = exam.endDate.toDate ?
          exam.endDate.toDate() :
          new Date(exam.endDate);
        return endDate < now;
      });

      const results = resultsSnap.docs.map(doc => doc.data());
      const averageScores = [];

      completed.forEach(exam => {
        const examResults = results.filter(result => result.examId === exam.id);
        if (examResults.length > 0) {
          const average = examResults.reduce((sum, result) => sum + (result.score || 0), 0) / examResults.length;
          averageScores.push({
            examName: exam.name || 'Unnamed Exam',
            average: Math.round(average),
          });
        }
      });

      setExamStats({
        totalExams: exams.length,
        ongoingExams: ongoing.length,
        completedExams: completed.length,
        averageScores: averageScores.slice(0, 5),
      });
    } catch (error) {
      console.error('Error fetching exam stats:', error);
      throw error;
    }
  };

  const generateCSVReport = async (type) => {
    try {
      let csvContent = '';
      let fileName = '';

      switch (type) {
        case 'users':
          csvContent = 'Role,Count\n' +
            `Teachers,${userStats.teachers}\n` +
            `Students,${userStats.students}\n` +
            `Parents,${userStats.parents}\n` +
            `Total,${userStats.total}`;
          fileName = 'user_stats.csv';
          break;

        case 'activities':
          const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
          const today = new Date().getDay();
          csvContent = 'Day,Activities\n' +
            activityStats.lastWeekActivities.map((count, index) => {
              const dayIndex = (today - 6 + index + 7) % 7;
              return `${days[dayIndex]},${count}`;
            }).join('\n');
          fileName = 'activity_stats.csv';
          break;

        case 'library':
          csvContent = 'Metric,Count\n' +
            `Total Books,${libraryStats.totalBooks}\n` +
            `Borrowed Books,${libraryStats.borrowedBooks}\n` +
            `Overdue Books,${libraryStats.overdueBooks}\n\n` +
            'Popular Books\nTitle,Borrow Count\n' +
            libraryStats.popularBooks.map(book =>
              `${book.title},${book.borrowCount}`
            ).join('\n');
          fileName = 'library_stats.csv';
          break;

        case 'exams':
          csvContent = 'Metric,Count\n' +
            `Total Exams,${examStats.totalExams}\n` +
            `Ongoing Exams,${examStats.ongoingExams}\n` +
            `Completed Exams,${examStats.completedExams}\n\n` +
            'Average Scores\nExam,Score\n' +
            examStats.averageScores.map(exam =>
              `${exam.examName},${exam.average}`
            ).join('\n');
          fileName = 'exam_stats.csv';
          break;
      }

      const fileUri = `${FileSystem.documentDirectory}${fileName}`;
      await FileSystem.writeAsStringAsync(fileUri, csvContent);

      if (await Sharing.isAvailableAsync()) {
        await Sharing.shareAsync(fileUri);
      }
    } catch (error) {
      console.error('Error generating report:', error);
    }
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" />
        <Text style={styles.loadingText}>Loading statistics...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>{error}</Text>
        <CustomButton mode="contained" onPress={fetchAllStats}>
          Retry
        </CustomButton>
      </View>
    );
  }

  const userPieChartData = [
    {
      name: 'Teachers',
      population: userStats.teachers,
      color: '#FF6384',
      legendFontColor: '#7F7F7F',
      legendFontSize: 12,
    },
    {
      name: 'Students',
      population: userStats.students,
      color: '#36A2EB',
      legendFontColor: '#7F7F7F',
      legendFontSize: 12,
    },
    {
      name: 'Parents',
      population: userStats.parents,
      color: '#FFCE56',
      legendFontColor: '#7F7F7F',
      legendFontSize: 12,
    },
  ];

  const activityLineChartData = {
    labels: ['6d', '5d', '4d', '3d', '2d', '1d', 'Today'],
    datasets: [
      {
        data: sanitizeChartData(activityStats.lastWeekActivities),
        color: (opacity = 1) => `rgba(54, 162, 235, ${opacity})`,
        strokeWidth: 2,
      },
    ],
  };

  return (
    <ScrollView style={styles.container}>
      <Card style={styles.card}>
        <Card.Content>
          <Title>User Statistics</Title>
          <View style={styles.chartContainer}>
            <PieChart
              data={userPieChartData.map(item => ({
                ...item,
                population: isNaN(item.population) || item.population === undefined ||
                            item.population === null || item.population === Infinity ||
                            item.population === -Infinity ? 0 : item.population
              }))}
              width={screenWidth - 40}
              height={220}
              chartConfig={chartConfig}
              accessor="population"
              backgroundColor="transparent"
              paddingLeft="15"
            />
          </View>
          <DataTable>
            <DataTable.Header>
              <DataTable.Title>Category</DataTable.Title>
              <DataTable.Title numeric>Count</DataTable.Title>
            </DataTable.Header>
            <DataTable.Row>
              <DataTable.Cell>Total Users</DataTable.Cell>
              <DataTable.Cell numeric>{userStats.total}</DataTable.Cell>
            </DataTable.Row>
            <DataTable.Row>
              <DataTable.Cell>Teachers</DataTable.Cell>
              <DataTable.Cell numeric>{userStats.teachers}</DataTable.Cell>
            </DataTable.Row>
            <DataTable.Row>
              <DataTable.Cell>Students</DataTable.Cell>
              <DataTable.Cell numeric>{userStats.students}</DataTable.Cell>
            </DataTable.Row>
            <DataTable.Row>
              <DataTable.Cell>Parents</DataTable.Cell>
              <DataTable.Cell numeric>{userStats.parents}</DataTable.Cell>
            </DataTable.Row>
          </DataTable>
          <CustomButton
            mode="outlined"
            onPress={() => generateCSVReport('users')}
          >
            Export User Stats
          </CustomButton>
        </Card.Content>
      </Card>

      <Card style={styles.card}>
        <Card.Content>
          <Title>Activity Statistics</Title>
          <View style={styles.chartContainer}>
            <LineChart
              data={sanitizeChartDatasets(activityLineChartData)}
              width={screenWidth - 40}
              height={220}
              chartConfig={chartConfig}
              bezier
              style={{
                marginVertical: 8,
                borderRadius: 16,
              }}
            />
          </View>
          <DataTable>
            <DataTable.Row>
              <DataTable.Cell>Total Logins</DataTable.Cell>
              <DataTable.Cell numeric>{activityStats.totalLogins}</DataTable.Cell>
            </DataTable.Row>
            <DataTable.Row>
              <DataTable.Cell>Active Users</DataTable.Cell>
              <DataTable.Cell numeric>{activityStats.activeUsers}</DataTable.Cell>
            </DataTable.Row>
          </DataTable>
          <CustomButton
            mode="outlined"
            onPress={() => generateCSVReport('activities')}
          >
            Export Activity Stats
          </CustomButton>
        </Card.Content>
      </Card>

      <Card style={styles.card}>
        <Card.Content>
          <Title>Library Statistics</Title>
          <DataTable>
            <DataTable.Row>
              <DataTable.Cell>Total Books</DataTable.Cell>
              <DataTable.Cell numeric>{libraryStats.totalBooks}</DataTable.Cell>
            </DataTable.Row>
            <DataTable.Row>
              <DataTable.Cell>Borrowed Books</DataTable.Cell>
              <DataTable.Cell numeric>{libraryStats.borrowedBooks}</DataTable.Cell>
            </DataTable.Row>
            <DataTable.Row>
              <DataTable.Cell>Overdue Books</DataTable.Cell>
              <DataTable.Cell numeric>{libraryStats.overdueBooks}</DataTable.Cell>
            </DataTable.Row>
          </DataTable>
          <Title style={styles.subtitle}>Popular Books</Title>
          {libraryStats.popularBooks.map((book, index) => (
            <List.Item
              key={book.id}
              title={book.title}
              description={`Borrowed ${book.borrowCount} times`}
              left={props => <List.Icon {...props} icon="book" />}
            />
          ))}
          <CustomButton
            mode="outlined"
            onPress={() => generateCSVReport('library')}
          >
            Export Library Stats
          </CustomButton>
        </Card.Content>
      </Card>

      <Card style={[styles.card, styles.lastCard]}>
        <Card.Content>
          <Title>Exam Statistics</Title>
          <DataTable>
            <DataTable.Row>
              <DataTable.Cell>Total Exams</DataTable.Cell>
              <DataTable.Cell numeric>{examStats.totalExams}</DataTable.Cell>
            </DataTable.Row>
            <DataTable.Row>
              <DataTable.Cell>Ongoing Exams</DataTable.Cell>
              <DataTable.Cell numeric>{examStats.ongoingExams}</DataTable.Cell>
            </DataTable.Row>
            <DataTable.Row>
              <DataTable.Cell>Completed Exams</DataTable.Cell>
              <DataTable.Cell numeric>{examStats.completedExams}</DataTable.Cell>
            </DataTable.Row>
          </DataTable>
          <Title style={styles.subtitle}>Average Scores</Title>
          {examStats.averageScores.map((exam, index) => (
            <List.Item
              key={index}
              title={exam.examName}
              description={`Average Score: ${exam.average}%`}
              left={props => <List.Icon {...props} icon="file-document" />}
            />
          ))}
          <CustomButton
            mode="outlined"
            onPress={() => generateCSVReport('exams')}
          >
            Export Exam Stats
          </CustomButton>
        </Card.Content>
      </Card>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  errorText: {
    color: 'red',
    fontSize: 16,
    marginBottom: 20,
    textAlign: 'center',
  },
  card: {
    margin: 10,
    elevation: 4,
  },
  lastCard: {
    marginBottom: 20,
  },
  chartContainer: {
    alignItems: 'center',
    marginVertical: 10,
  },
  subtitle: {
    fontSize: 16,
    marginTop: 20,
    marginBottom: 10,
  },
});

export default SystemReports;
