import React, { useState } from 'react';
import { StyleSheet, View, StatusBar } from 'react-native';
import { Appbar, useTheme } from 'react-native-paper';
import { useNavigation } from '@react-navigation/native';
import { useLanguage } from '../../context/LanguageContext';
import NotificationBadge from './NotificationBadge';
import LanguageSelector from './LanguageSelector';
import { LinearGradient } from 'expo-linear-gradient';
import * as Animatable from 'react-native-animatable';

const StudentAppHeader = ({ title, onMenuPress, showBackButton = false }) => {
  const theme = useTheme();
  const navigation = useNavigation();
  const { isRTL, translate, getTextStyle } = useLanguage();

  return (
    <Animatable.View animation="fadeIn" duration={500}>
      <StatusBar
        backgroundColor="#1976d2"
        barStyle="light-content"
      />
      <LinearGradient
        colors={['#1976d2', '#1565C0']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={styles.gradientHeader}
      >
        <Appbar.Header style={[styles.header, { backgroundColor: 'transparent' }]}>
          <Animatable.View animation={isRTL ? "fadeInRight" : "fadeInLeft"} duration={600}>
            {showBackButton ? (
              <Appbar.BackAction
                onPress={() => navigation.goBack()}
                color="white"
                style={isRTL ? styles.rightIcon : {}}
                size={26}
              />
            ) : (
              <Appbar.Action
                icon="menu"
                color="white"
                onPress={onMenuPress}
                style={isRTL ? styles.rightIcon : {}}
                size={26}
              />
            )}
          </Animatable.View>

          <Animatable.View animation="fadeInDown" duration={600} style={styles.titleContainer}>
            <Appbar.Content
              title={title}
              titleStyle={[styles.title, isRTL && styles.rtlTitle, getTextStyle({ fontSize: 18, fontWeight: 'bold' })]}
            />
          </Animatable.View>

          <Animatable.View animation={isRTL ? "fadeInLeft" : "fadeInRight"} duration={600} style={styles.actionsContainer}>
            <NotificationBadge
              size={24}
              color="white"
              onPress={() => navigation.navigate('NotificationCenter')}
              containerStyle={styles.notificationContainer}
            />
            <LanguageSelector />
          </Animatable.View>
        </Appbar.Header>
      </LinearGradient>
    </Animatable.View>
  );
};

const styles = StyleSheet.create({
  gradientHeader: {
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
  },
  header: {
    elevation: 0,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: 'white',
  },
  rtlTitle: {
    textAlign: 'right',
  },
  rightIcon: {
    marginLeft: 'auto',
  },
  notificationContainer: {
    marginRight: 8,
  },
  titleContainer: {
    flex: 1,
  },
  actionsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  }
});

export default StudentAppHeader;
