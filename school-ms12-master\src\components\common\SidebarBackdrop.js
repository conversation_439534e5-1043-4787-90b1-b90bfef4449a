import React, { useEffect, useRef } from 'react';
import { StyleSheet, TouchableWithoutFeedback, Animated, Dimensions, Platform } from 'react-native';
import * as Animatable from 'react-native-animatable';

const SidebarBackdrop = ({ visible, onPress, fadeAnim }) => {
  const opacity = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (visible) {
      Animated.timing(opacity, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }).start();
    } else {
      Animated.timing(opacity, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }).start();
    }
  }, [visible, opacity]);

  if (!visible && Platform.OS !== 'web') return null;

  return (
    <TouchableWithoutFeedback onPress={onPress}>
      <Animated.View
        style={[
          styles.backdrop,
          {
            opacity: fadeAnim || opacity,
            pointerEvents: visible ? 'auto' : 'none'
          }
        ]}
      />
    </TouchableWithoutFeedback>
  );
};

const styles = StyleSheet.create({
  backdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    zIndex: 999,
    ...Platform.select({
      web: {
        cursor: 'pointer',
      },
    }),
  },
});

export default SidebarBackdrop;
