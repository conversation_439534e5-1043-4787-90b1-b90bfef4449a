import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import {
  Card,
  Title,
  Text,
  DataTable,
  ActivityIndicator,
  Button,
  Chip,
  Badge,
  IconButton,
  ProgressBar,
  Divider
} from 'react-native-paper';
import { db, auth } from '../../config/firebase';
import { collection, query, where, getDocs, doc, getDoc, updateDoc } from 'firebase/firestore';
import { useLanguage } from '../../context/LanguageContext';
import ScrollableTable from '../common/ScrollableTable';
import * as Animatable from 'react-native-animatable';
import CloudinaryAvatar from '../common/CloudinaryAvatar';

const DetailedResultView = ({ classId, sectionName, subject, notificationId }) => {
  const { translate } = useLanguage();
  const [loading, setLoading] = useState(true);
  const [results, setResults] = useState([]);
  const [error, setError] = useState(null);
  const [studentInfo, setStudentInfo] = useState(null);
  const [hasNewGrades, setHasNewGrades] = useState(!!notificationId);
  const [selectedSubject, setSelectedSubject] = useState(subject || null);
  const [assessmentDetails, setAssessmentDetails] = useState({});
  const [showAssessmentDetails, setShowAssessmentDetails] = useState(false);

  useEffect(() => {
    fetchStudentInfo();
    fetchResults();

    // If this component was opened from a notification, mark it as read
    if (notificationId) {
      markNotificationAsRead(notificationId);
    }
  }, [notificationId]);

  // Mark notification as read
  const markNotificationAsRead = async (notificationId) => {
    try {
      const notificationRef = doc(db, 'notifications', notificationId);
      await updateDoc(notificationRef, {
        read: true
      });

      // Update badge count
      const userRef = doc(db, 'users', auth.currentUser.uid);
      const userDoc = await getDoc(userRef);

      if (userDoc.exists()) {
        const userData = userDoc.data();
        const currentBadgeCount = userData.badgeCount || 0;

        if (currentBadgeCount > 0) {
          await updateDoc(userRef, {
            badgeCount: currentBadgeCount - 1
          });
        }
      }
    } catch (error) {
      console.error('Error marking notification as read:', error);
      // Continue even if marking as read fails
    }
  };

  const fetchStudentInfo = async () => {
    try {
      const studentRef = doc(db, 'users', auth.currentUser.uid);
      const studentDoc = await getDoc(studentRef);

      if (studentDoc.exists()) {
        setStudentInfo(studentDoc.data());
      }
    } catch (error) {
      console.error('Error fetching student info:', error);
      setError('Failed to load student information');
    }
  };

  const fetchResults = async () => {
    try {
      setLoading(true);
      setError(null);

      // First try to get results from the class document if classId is provided
      if (classId) {
        const classRef = doc(db, 'classes', classId);
        const classDoc = await getDoc(classRef);

        if (classDoc.exists()) {
          const classData = classDoc.data();
          const grades = classData.grades || {};

          // If a specific subject is selected, only get that subject's grades
          if (selectedSubject && grades[selectedSubject]) {
            const subjectGrades = grades[selectedSubject];
            const studentScores = subjectGrades.studentScores || [];

            // Find this student's scores
            const studentScore = studentScores.find(s => s.studentId === auth.currentUser.uid);

            if (studentScore) {
              // Format the result in a way that's compatible with the existing code
              const result = {
                id: `${classId}_${selectedSubject}`,
                studentId: auth.currentUser.uid,
                studentName: studentInfo?.displayName || studentScore.studentName,
                className: classData.name,
                sectionName: classData.section,
                status: 'approved',
                subjectScores: {
                  [selectedSubject]: {
                    totalScore: studentScore.totalScore,
                    assessmentScores: studentScore.assessmentScores || [],
                    teacherName: subjectGrades.teacherName
                  }
                },
                totalPercentage: studentScore.totalScore,
                createdAt: subjectGrades.lastUpdated,
                approvedAt: subjectGrades.lastUpdated
              };

              setResults([result]);

              // Also set assessment details for this subject
              if (subjectGrades.assessments) {
                setAssessmentDetails({
                  [selectedSubject]: subjectGrades.assessments
                });
              }

              setLoading(false);
              return;
            }
          } else {
            // Get all subjects' grades
            const allResults = [];

            for (const subject in grades) {
              const subjectGrades = grades[subject];
              const studentScores = subjectGrades.studentScores || [];

              // Find this student's scores
              const studentScore = studentScores.find(s => s.studentId === auth.currentUser.uid);

              if (studentScore) {
                // Format the result
                const result = {
                  id: `${classId}_${subject}`,
                  studentId: auth.currentUser.uid,
                  studentName: studentInfo?.displayName || studentScore.studentName,
                  className: classData.name,
                  sectionName: classData.section,
                  subject: subject,
                  status: 'approved',
                  subjectScores: {
                    [subject]: {
                      totalScore: studentScore.totalScore,
                      assessmentScores: studentScore.assessmentScores || [],
                      teacherName: subjectGrades.teacherName
                    }
                  },
                  totalPercentage: studentScore.totalScore,
                  createdAt: subjectGrades.lastUpdated,
                  approvedAt: subjectGrades.lastUpdated
                };

                allResults.push(result);

                // Also set assessment details for this subject
                if (subjectGrades.assessments) {
                  setAssessmentDetails(prev => ({
                    ...prev,
                    [subject]: subjectGrades.assessments
                  }));
                }
              }
            }

            if (allResults.length > 0) {
              // Sort by creation date (newest first)
              allResults.sort((a, b) => {
                const dateA = a.createdAt?.seconds ? new Date(a.createdAt.seconds * 1000) : new Date(0);
                const dateB = b.createdAt?.seconds ? new Date(b.createdAt.seconds * 1000) : new Date(0);
                return dateB - dateA;
              });

              setResults(allResults);
              setLoading(false);
              return;
            }
          }
        }
      }

      // Fallback to the old method if class-based approach doesn't work
      const resultsRef = collection(db, 'results');
      const q = query(
        resultsRef,
        where('studentId', '==', auth.currentUser.uid),
        where('status', '==', 'approved')
      );

      const snapshot = await getDocs(q);

      if (snapshot.empty) {
        setResults([]);
        setLoading(false);
        return;
      }

      const resultData = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      // Sort by creation date (newest first)
      resultData.sort((a, b) => {
        const dateA = a.createdAt?.toDate?.() || new Date(0);
        const dateB = b.createdAt?.toDate?.() || new Date(0);
        return dateB - dateA;
      });

      setResults(resultData);
    } catch (error) {
      console.error('Error fetching results:', error);
      setError('Failed to load results');
    } finally {
      setLoading(false);
    }
  };

  const getGradeColor = (percentage) => {
    if (percentage >= 90) return '#4CAF50'; // A - Green
    if (percentage >= 80) return '#8BC34A'; // B - Light Green
    if (percentage >= 70) return '#CDDC39'; // C - Lime
    if (percentage >= 60) return '#FFC107'; // D - Amber
    return '#F44336'; // F - Red
  };

  const getGradeLetter = (percentage) => {
    if (percentage >= 90) return 'A';
    if (percentage >= 80) return 'B';
    if (percentage >= 70) return 'C';
    if (percentage >= 60) return 'D';
    return 'F';
  };

  if (loading) {
    return (
      <Animatable.View animation="fadeIn" style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#1976d2" />
        <Text style={styles.loadingText}>{translate('student.results.loading') || 'Loading your results...'}</Text>
      </Animatable.View>
    );
  }

  if (error) {
    return (
      <Animatable.View animation="fadeIn" style={styles.errorContainer}>
        <IconButton icon="alert-circle-outline" size={64} color="#F44336" />
        <Text style={styles.errorText}>{error}</Text>
        <Button mode="contained" onPress={fetchResults} style={styles.retryButton}>
          {translate('common.retry') || 'Retry'}
        </Button>
      </Animatable.View>
    );
  }

  if (results.length === 0) {
    return (
      <Animatable.View animation="fadeIn" duration={500}>
        <Card style={styles.emptyCard}>
          <Card.Content style={styles.emptyCardContent}>
            <IconButton icon="clipboard-text-outline" size={64} color="#BDBDBD" />
            <Text style={styles.emptyText}>
              {translate('student.results.noResults') || 'No approved results available yet.'}
            </Text>
            <Text style={styles.emptySubtext}>
              {translate('student.results.checkLater') || 'Check back later after your teacher submits grades and they are approved by the admin.'}
            </Text>
          </Card.Content>
        </Card>
      </Animatable.View>
    );
  }

  // Use the most recent result for detailed view
  const latestResult = results[0];
  const subjectScores = latestResult.subjectScores || {};
  const subjects = Object.keys(subjectScores);

  // If a specific subject is selected, filter to only show that subject
  const displaySubjects = selectedSubject ? [selectedSubject] : subjects;

  return (
    <ScrollView style={styles.container}>
      {hasNewGrades && (
        <Animatable.View animation="fadeIn" duration={500}>
          <Card style={styles.newGradeCard}>
            <Card.Content style={styles.newGradeContent}>
              <Badge size={24} style={styles.newBadge}>NEW</Badge>
              <Text style={styles.newGradeText}>
                {translate('student.results.newGrades') || 'New grades have been published!'}
              </Text>
              <IconButton
                icon="close-circle"
                size={20}
                color="#FFF"
                style={styles.dismissButton}
                onPress={() => setHasNewGrades(false)}
              />
            </Card.Content>
          </Card>
        </Animatable.View>
      )}

      <Animatable.View animation="fadeIn" duration={500} delay={200}>
        <Card style={styles.studentInfoCard}>
          <Card.Content>
            <View style={styles.studentInfoHeader}>
              <View style={styles.avatarContainer}>
                <CloudinaryAvatar
                  source={studentInfo?.photoURL}
                  label={(studentInfo?.displayName || latestResult.studentName || 'S').substring(0, 2)}
                  size={60}
                  backgroundColor="#2196F3"
                />
              </View>
              <View style={styles.studentNameContainer}>
                <Title style={styles.studentName}>{studentInfo?.displayName || latestResult.studentName}</Title>
                <View style={styles.studentMetaRow}>
                  <Chip icon="school" style={styles.classChip} mode="outlined">
                    {latestResult.className}-{latestResult.sectionName}
                  </Chip>
                  <Chip icon="identifier" style={styles.rollChip} mode="outlined">
                    {studentInfo?.rollNumber || latestResult.rollNumber || 'N/A'}
                  </Chip>
                </View>
              </View>
            </View>
          </Card.Content>
        </Card>
      </Animatable.View>

      <Animatable.View animation="fadeIn" duration={500} delay={300}>
        <Card style={styles.summaryCard}>
          <Card.Content>
            <View style={styles.cardTitleContainer}>
              <Title style={styles.cardTitle}>{translate('student.results.summary') || 'Result Summary'}</Title>
              {latestResult.approvedAt && (
                <Chip
                  icon="calendar-check"
                  style={styles.dateChip}
                  mode="outlined"
                >
                  {latestResult.approvedAt?.toDate?.().toLocaleDateString() ||
                   (latestResult.approvedAt?.seconds ? new Date(latestResult.approvedAt.seconds * 1000).toLocaleDateString() : 'N/A')}
                </Chip>
              )}
            </View>

            <Divider style={styles.divider} />

            <View style={styles.summaryRow}>
              <View style={styles.summaryItem}>
                <Text style={styles.summaryLabel}>{translate('student.results.totalScore') || 'Total Score'}:</Text>
                <Text style={styles.summaryValue}>
                  {latestResult.totalPoints || latestResult.totalPercentage?.toFixed(0) || '0'} / 100
                </Text>
              </View>
              <View style={styles.summaryItem}>
                <Text style={styles.summaryLabel}>{translate('student.results.percentage') || 'Percentage'}:</Text>
                <Text style={[styles.summaryValue, { color: getGradeColor(latestResult.totalPercentage) }]}>
                  {latestResult.totalPercentage?.toFixed(1) || '0.0'}%
                </Text>
              </View>
              <View style={styles.summaryItem}>
                <Text style={styles.summaryLabel}>{translate('student.results.grade') || 'Grade'}:</Text>
                <Chip style={[styles.gradeChip, { backgroundColor: getGradeColor(latestResult.totalPercentage) }]}>
                  {getGradeLetter(latestResult.totalPercentage)}
                </Chip>
              </View>
              <View style={styles.summaryItem}>
                <Text style={styles.summaryLabel}>{translate('student.results.rank') || 'Rank'}:</Text>
                <Text style={styles.summaryValue}>#{latestResult.rank || 'N/A'}</Text>
              </View>
            </View>

            {latestResult.totalPercentage && (
              <View style={styles.progressContainer}>
                <ProgressBar
                  progress={latestResult.totalPercentage / 100}
                  color={getGradeColor(latestResult.totalPercentage)}
                  style={styles.progressBar}
                />
                <View style={styles.progressLabels}>
                  <Text style={styles.progressLabel}>0%</Text>
                  <Text style={styles.progressLabel}>50%</Text>
                  <Text style={styles.progressLabel}>100%</Text>
                </View>
              </View>
            )}
          </Card.Content>
        </Card>
      </Animatable.View>

      <Animatable.View animation="fadeIn" duration={500} delay={400}>
        <Card style={styles.tableCard}>
          <Card.Content>
            <View style={styles.cardTitleContainer}>
              <Title style={styles.cardTitle}>{translate('student.results.subjectResults') || 'Subject-wise Results'}</Title>
              {displaySubjects.length > 1 && (
                <Chip
                  icon="filter-variant"
                  style={styles.filterChip}
                  onPress={() => {/* Add subject filter functionality */}}
                >
                  {translate('student.results.filter') || 'Filter'}
                </Chip>
              )}
            </View>

            <Divider style={styles.divider} />

            <ScrollableTable
              header={
                <DataTable.Header style={styles.tableHeader}>
                  <DataTable.Title style={styles.subjectColumn}>{translate('student.results.subject') || 'Subject'}</DataTable.Title>
                  <DataTable.Title numeric style={styles.scoreColumn}>{translate('student.results.score') || 'Score'}</DataTable.Title>
                  <DataTable.Title numeric style={styles.percentageColumn}>{translate('student.results.percentage') || '%'}</DataTable.Title>
                  <DataTable.Title style={styles.gradeColumn}>{translate('student.results.grade') || 'Grade'}</DataTable.Title>
                </DataTable.Header>
              }
              maxHeight={300}
            >
              {displaySubjects.map((subject, index) => {
                const subjectData = subjectScores[subject];
                const score = subjectData.totalScore || 0;
                const maxScore = 100; // Assuming max score is 100
                const percentage = (score / maxScore) * 100;

                return (
                  <DataTable.Row
                    key={index}
                    style={index % 2 === 0 ? styles.evenRow : styles.oddRow}
                    onPress={() => {
                      setSelectedSubject(subject);
                      setShowAssessmentDetails(true);
                    }}
                  >
                    <DataTable.Cell style={styles.subjectColumn}>
                      <View style={styles.subjectCell}>
                        <Text style={styles.subjectText}>{subject}</Text>
                        {subjectData.teacherName && (
                          <Text style={styles.teacherText}>
                            {translate('student.results.teacher') || 'Teacher'}: {subjectData.teacherName}
                          </Text>
                        )}
                      </View>
                    </DataTable.Cell>
                    <DataTable.Cell numeric style={styles.scoreColumn}>{score.toFixed(1)}</DataTable.Cell>
                    <DataTable.Cell numeric style={styles.percentageColumn}>
                      <Text style={{ color: getGradeColor(percentage) }}>
                        {percentage.toFixed(1)}%
                      </Text>
                    </DataTable.Cell>
                    <DataTable.Cell style={styles.gradeColumn}>
                      <Chip
                        style={[styles.gradeChip, { backgroundColor: getGradeColor(percentage) }]}
                        textStyle={styles.gradeChipText}
                      >
                        {getGradeLetter(percentage)}
                      </Chip>
                    </DataTable.Cell>
                  </DataTable.Row>
                );
              })}
            </ScrollableTable>

            <Text style={styles.tapHintText}>
              {translate('student.results.tapForDetails') || 'Tap on a subject to view assessment details'}
            </Text>
          </Card.Content>
        </Card>
      </Animatable.View>

      {/* Assessment Details Section */}
      {showAssessmentDetails && selectedSubject && (
        <Animatable.View animation="fadeIn" duration={500}>
          <Card style={styles.tableCard}>
            <Card.Content>
              <View style={styles.cardTitleContainer}>
                <Title style={styles.cardTitle}>
                  {translate('student.results.assessmentDetails') || 'Assessment Details'}: {selectedSubject}
                </Title>
                <IconButton
                  icon="close"
                  size={20}
                  onPress={() => setShowAssessmentDetails(false)}
                />
              </View>

              <Divider style={styles.divider} />

              {(() => {
                const subjectData = subjectScores[selectedSubject];
                const assessments = subjectData?.assessmentScores || [];
                const subjectAssessments = assessmentDetails[selectedSubject] || [];

                if (assessments.length === 0 && subjectAssessments.length === 0) {
                  return (
                    <View style={styles.noAssessmentsContainer}>
                      <IconButton icon="clipboard-text-off-outline" size={40} color="#BDBDBD" />
                      <Text style={styles.noAssessmentsText}>
                        {translate('student.results.noAssessments') || 'No assessment details available for this subject.'}
                      </Text>
                    </View>
                  );
                }

                // Combine assessment details with scores
                const combinedAssessments = [];

                // First add from assessmentScores
                if (assessments.length > 0) {
                  assessments.forEach(assessment => {
                    combinedAssessments.push({
                      ...assessment,
                      score: assessment.score || 0,
                      maxPoints: assessment.maxPoints || 100
                    });
                  });
                }

                // Then add any missing assessments from subjectAssessments
                if (subjectAssessments.length > 0) {
                  subjectAssessments.forEach(assessment => {
                    const exists = combinedAssessments.some(a => a.id === assessment.id);
                    if (!exists) {
                      // Find score for this assessment
                      const score = assessments.find(a => a.id === assessment.id)?.score || 0;

                      combinedAssessments.push({
                        ...assessment,
                        score: score,
                        maxPoints: assessment.points || 100
                      });
                    }
                  });
                }

                return (
                  <ScrollableTable
                    header={
                      <DataTable.Header style={styles.tableHeader}>
                        <DataTable.Title style={styles.assessmentColumn}>
                          {translate('student.results.assessment') || 'Assessment'}
                        </DataTable.Title>
                        <DataTable.Title style={styles.typeColumn}>
                          {translate('student.results.type') || 'Type'}
                        </DataTable.Title>
                        <DataTable.Title numeric style={styles.scoreColumn}>
                          {translate('student.results.score') || 'Score'}
                        </DataTable.Title>
                        <DataTable.Title numeric style={styles.percentageColumn}>
                          {translate('student.results.percentage') || '%'}
                        </DataTable.Title>
                      </DataTable.Header>
                    }
                    maxHeight={300}
                  >
                    {combinedAssessments.map((assessment, index) => {
                      const score = assessment.score || 0;
                      const maxScore = assessment.maxPoints || assessment.points || 100;
                      const percentage = (score / maxScore) * 100;

                      return (
                        <DataTable.Row key={index} style={index % 2 === 0 ? styles.evenRow : styles.oddRow}>
                          <DataTable.Cell style={styles.assessmentColumn}>
                            <Text>{assessment.title}</Text>
                            {assessment.description && (
                              <Text style={styles.assessmentDescription}>{assessment.description}</Text>
                            )}
                          </DataTable.Cell>
                          <DataTable.Cell style={styles.typeColumn}>
                            <Chip size="small" style={styles.typeChip}>
                              {assessment.type}
                            </Chip>
                          </DataTable.Cell>
                          <DataTable.Cell numeric style={styles.scoreColumn}>
                            {score.toFixed(1)}/{maxScore}
                          </DataTable.Cell>
                          <DataTable.Cell numeric style={styles.percentageColumn}>
                            <Text style={{ color: getGradeColor(percentage) }}>
                              {percentage.toFixed(1)}%
                            </Text>
                          </DataTable.Cell>
                        </DataTable.Row>
                      );
                    })}
                  </ScrollableTable>
                );
              })()}
            </Card.Content>
          </Card>
        </Animatable.View>
      )}

      <View style={styles.footer}>
        <Text style={styles.footerText}>
          {translate('student.results.approvedOn') || 'Results approved on'}: {
            latestResult.approvedAt?.toDate?.().toLocaleDateString() ||
            (latestResult.approvedAt?.seconds ? new Date(latestResult.approvedAt.seconds * 1000).toLocaleDateString() : 'N/A')
          }
        </Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    marginBottom: 20,
    fontSize: 16,
    color: '#F44336',
    textAlign: 'center',
  },
  retryButton: {
    marginTop: 16,
  },
  emptyCard: {
    margin: 16,
    padding: 16,
    elevation: 2,
  },
  emptyCardContent: {
    alignItems: 'center',
    padding: 16,
  },
  emptyText: {
    textAlign: 'center',
    fontSize: 18,
    fontWeight: 'bold',
    marginVertical: 8,
  },
  emptySubtext: {
    textAlign: 'center',
    fontSize: 14,
    color: '#666',
    marginBottom: 16,
  },
  newGradeCard: {
    marginBottom: 16,
    backgroundColor: '#4CAF50',
    elevation: 3,
  },
  newGradeContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
  },
  newBadge: {
    backgroundColor: '#FFC107',
    color: '#000',
    marginRight: 8,
  },
  newGradeText: {
    color: 'white',
    flex: 1,
    fontWeight: 'bold',
  },
  dismissButton: {
    margin: 0,
  },
  studentInfoCard: {
    marginBottom: 16,
    elevation: 2,
  },
  studentInfoHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatarContainer: {
    marginRight: 16,
  },
  studentNameContainer: {
    flex: 1,
  },
  studentName: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  studentMetaRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  classChip: {
    marginRight: 8,
    marginBottom: 4,
  },
  rollChip: {
    marginBottom: 4,
  },
  summaryCard: {
    marginBottom: 16,
    elevation: 2,
  },
  tableCard: {
    marginBottom: 16,
    elevation: 2,
  },
  cardTitleContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  dateChip: {
    height: 28,
  },
  filterChip: {
    height: 28,
  },
  divider: {
    marginVertical: 12,
  },
  summaryRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  summaryItem: {
    width: '48%',
    marginBottom: 12,
  },
  summaryLabel: {
    fontSize: 14,
    color: '#666',
  },
  summaryValue: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  progressContainer: {
    marginTop: 8,
  },
  progressBar: {
    height: 8,
    borderRadius: 4,
  },
  progressLabels: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 4,
  },
  progressLabel: {
    fontSize: 12,
    color: '#666',
  },
  gradeChip: {
    alignSelf: 'flex-start',
    height: 28,
  },
  gradeChipText: {
    color: 'white',
    fontWeight: 'bold',
  },
  tableHeader: {
    backgroundColor: '#f5f5f5',
  },
  evenRow: {
    backgroundColor: '#ffffff',
  },
  oddRow: {
    backgroundColor: '#f9f9f9',
  },
  subjectColumn: {
    flex: 3,
  },
  subjectCell: {
    flex: 1,
    justifyContent: 'center',
  },
  subjectText: {
    fontWeight: '500',
  },
  teacherText: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
  },
  scoreColumn: {
    flex: 1,
  },
  maxScoreColumn: {
    flex: 1,
  },
  percentageColumn: {
    flex: 1.5,
  },
  gradeColumn: {
    flex: 1,
  },
  teacherColumn: {
    flex: 2,
  },
  tapHintText: {
    fontSize: 12,
    color: '#666',
    fontStyle: 'italic',
    textAlign: 'center',
    marginTop: 8,
  },
  noAssessmentsContainer: {
    alignItems: 'center',
    padding: 24,
  },
  noAssessmentsText: {
    textAlign: 'center',
    color: '#666',
    marginTop: 8,
  },
  assessmentSection: {
    marginBottom: 20,
  },
  subjectTitle: {
    fontSize: 16,
    marginBottom: 8,
  },
  assessmentColumn: {
    flex: 3,
  },
  assessmentDescription: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
  },
  typeColumn: {
    flex: 1.5,
  },
  weightColumn: {
    flex: 1,
  },
  typeChip: {
    backgroundColor: '#E0E0E0',
  },
  footer: {
    marginBottom: 24,
    alignItems: 'center',
  },
  footerText: {
    fontSize: 12,
    color: '#666',
    fontStyle: 'italic',
  },
});

export default DetailedResultView;
