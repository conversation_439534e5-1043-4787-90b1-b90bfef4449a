// Import our platform polyfill to ensure Platform is available
import '../utils/platformPolyfill';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Try to get Platform and NativeModules from react-native, but use fallbacks if not available
let Platform, NativeModules;
try {
  const ReactNative = require('react-native');
  Platform = ReactNative.Platform;
  NativeModules = ReactNative.NativeModules;
} catch (error) {
  console.log('Using Platform polyfill in languageUtils');
  Platform = global.Platform || { OS: 'unknown' };
  NativeModules = {
    SettingsManager: { settings: { AppleLocale: 'en_US', AppleLanguages: ['en'] } },
    I18nManager: { localeIdentifier: 'en_US' }
  };
}

/**
 * Utility functions for language handling
 */
export const languageUtils = {
  /**
   * Get the device's locale
   * @returns {string} The device locale code (e.g., 'en', 'am', 'or')
   */
  getDeviceLocale: () => {
    try {
      // Get the device locale
      let locale = 'en_US'; // Default fallback

      try {
        if (Platform && Platform.OS === 'ios') {
          locale = NativeModules.SettingsManager.settings.AppleLocale ||
                  NativeModules.SettingsManager.settings.AppleLanguages[0] || locale;
        } else if (Platform && NativeModules.I18nManager) {
          locale = NativeModules.I18nManager.localeIdentifier || locale;
        }
      } catch (platformError) {
        console.log('Error detecting platform in getDeviceLocale:', platformError);
      }

      // Extract language code from locale
      const languageCode = locale.split('_')[0];
      return languageCode;
    } catch (error) {
      console.error('Error in getDeviceLocale:', error);
      return 'en'; // Default to English on error
    }
  },

  /**
   * Get the user's preferred language
   * @param {Object} supportedLanguages - Object containing supported language codes
   * @returns {Promise<string>} The preferred language code
   */
  getUserLanguage: async (supportedLanguages) => {
    try {
      // Ensure we have valid supportedLanguages
      const validLanguages = supportedLanguages || { en: true, am: true, or: true };

      try {
        // Try to get saved language preference
        const savedLanguage = await AsyncStorage.getItem('language');
        if (savedLanguage && validLanguages[savedLanguage]) {
          console.log('Using saved language preference:', savedLanguage);
          return savedLanguage;
        }
      } catch (storageError) {
        console.error('Error accessing AsyncStorage:', storageError);
        // Continue to next method if AsyncStorage fails
      }

      try {
        // If no saved preference, use device locale if supported
        const deviceLocale = languageUtils.getDeviceLocale();
        if (deviceLocale && validLanguages[deviceLocale]) {
          console.log('Using device locale:', deviceLocale);
          return deviceLocale;
        }
      } catch (localeError) {
        console.error('Error getting device locale:', localeError);
        // Continue to default if locale detection fails
      }

      // Default to English if all else fails
      console.log('Defaulting to English language');
      return 'en';
    } catch (error) {
      console.error('Error in getUserLanguage:', error);
      return 'en'; // Default to English on any error
    }
  },

  /**
   * Format text based on language direction
   * @param {string} text - The text to format
   * @param {boolean} isRTL - Whether the language is right-to-left
   * @returns {string} Formatted text
   */
  formatText: (text, isRTL) => {
    if (!text) return '';

    // Add any language-specific formatting here
    return text;
  },

  /**
   * Get text style based on language
   * @param {string} language - The language code
   * @param {Object} baseStyle - Base style object
   * @returns {Object} Style object with language-specific adjustments
   */
  getTextStyle: (language, baseStyle = {}) => {
    const languageConfig = {
      en: { isRTL: false, fontScale: 1 },
      am: { isRTL: false, fontScale: 1.1 }, // Slightly larger for Amharic
      or: { isRTL: false, fontScale: 1 }
    };

    const config = languageConfig[language] || languageConfig.en;

    return {
      ...baseStyle,
      writingDirection: config.isRTL ? 'rtl' : 'ltr',
      fontSize: (baseStyle.fontSize || 14) * config.fontScale,
    };
  }
};

export default languageUtils;
