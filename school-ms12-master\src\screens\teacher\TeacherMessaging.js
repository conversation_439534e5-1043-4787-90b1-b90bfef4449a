import React, { useState } from 'react';
import { StyleSheet, StatusBar } from 'react-native';
// Theme import removed
import { useAuth } from '../../context/AuthContext';
import { useLanguage } from '../../context/LanguageContext';
import MessagingComponent from '../../components/messaging/MessagingComponent';
import TeacherAppHeader from '../../components/common/TeacherAppHeader';
import TeacherSidebar from '../../components/common/TeacherSidebar';

const TeacherMessaging = ({ navigation }) => {
  // No theme needed
  const { user } = useAuth();
  const { translate } = useLanguage();
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [activeSidebarItem, setActiveSidebarItem] = useState('messaging');

  const toggleDrawer = () => {
    setDrawerOpen(!drawerOpen);
  };

  // Custom header for teacher dashboard
  const customHeader = (
    <TeacherAppHeader
      title={translate('communication.teacherTitle') || 'Teacher Communication Center'}
      onMenuPress={toggleDrawer}
    />
  );

  // Custom sidebar for teacher dashboard
  const customSidebar = (
    <TeacherSidebar
      visible={drawerOpen}
      onClose={toggleDrawer}
      navigation={navigation}
      activeSidebarItem={activeSidebarItem}
      setActiveSidebarItem={setActiveSidebarItem}
    />
  );

  return (
    <MessagingComponent
      customHeader={customHeader}
      customSidebar={customSidebar}
      userRole="teacher"
    />
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

export default TeacherMessaging;
