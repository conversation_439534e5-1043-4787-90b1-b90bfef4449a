import React, { useState, useEffect } from 'react';
import { View, ScrollView, StyleSheet } from 'react-native';
import { Card, Title, Text, Portal, Modal, List, ActivityIndicator, Surface, Divider } from 'react-native-paper';
import { db } from '../../config/firebase';
import { doc, getDoc, updateDoc } from 'firebase/firestore';
import { useAuth } from '../../context/AuthContext';
import CustomButton from '../../components/common/CustomButton';
import CustomInput from '../../components/common/CustomInput';
import ChangePasswordModal from '../../components/common/ChangePasswordModal';
import { useLanguage } from '../../context/LanguageContext';
import * as ImagePicker from 'expo-image-picker';
import ProfileImageService from '../../services/ProfileImageService';
import CloudinaryAvatar from '../../components/common/CloudinaryAvatar';

const ProfileManagement = () => {
  const { user, updateUserProfile } = useAuth();
  const { translate, getTextStyle } = useLanguage();
  const [profile, setProfile] = useState(null);
  const [visible, setVisible] = useState(false);
  const [passwordModalVisible, setPasswordModalVisible] = useState(false);
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);
  const [imageUri, setImageUri] = useState(null);

  const [formData, setFormData] = useState({
    displayName: '',
    email: '',
    phone: '',
    address: '',
    parentName: '',
    parentPhone: '',
    emergencyContact: '',
    bloodGroup: '',
    dateOfBirth: '',
    admissionNumber: '',
    bio: '',
  });

  useEffect(() => {
    fetchProfile();
  }, []);

  const fetchProfile = async () => {
    try {
      setLoading(true);
      const profileRef = doc(db, 'students', user.uid);
      const docSnap = await getDoc(profileRef);

      if (docSnap.exists()) {
        const profileData = docSnap.data();
        setProfile(profileData);
        setFormData({
          displayName: profileData.displayName || '',
          email: profileData.email || '',
          phone: profileData.phone || '',
          address: profileData.address || '',
          parentName: profileData.parentName || '',
          parentPhone: profileData.parentPhone || '',
          emergencyContact: profileData.emergencyContact || '',
          bloodGroup: profileData.bloodGroup || '',
          dateOfBirth: profileData.dateOfBirth || '',
          admissionNumber: profileData.admissionNumber || '',
          bio: profileData.bio || '',
        });
        setImageUri(profileData.photoURL);
      }
    } catch (error) {
      console.error('Error fetching profile:', error);
    } finally {
      setLoading(false);
    }
  };

  const pickImage = async () => {
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [1, 1],
      quality: 1,
    });

    if (!result.canceled) {
      setImageUri(result.assets[0].uri);
    }
  };

  const handleUpdateProfile = async () => {
    try {
      setUpdating(true);

      // Handle profile image upload if changed
      let photoURL = imageUri;
      if (imageUri && !imageUri.startsWith('http')) {
        try {
          // Upload and update profile image using the ProfileImageService
          photoURL = await ProfileImageService.uploadAndUpdateProfileImage(
            imageUri,
            user,
            'student'
          );
        } catch (imageError) {
          console.error('Error uploading profile image:', imageError);
          // Continue with other updates even if image upload fails
        }
      }

      // Update profile in Firestore
      const profileRef = doc(db, 'students', user.uid);
      await updateDoc(profileRef, {
        ...formData,
        photoURL,
        updatedAt: new Date().toISOString(),
      });

      // Update auth profile
      await updateUserProfile({
        displayName: formData.displayName,
        photoURL,
      });

      setVisible(false);
      // Refresh profile data
      fetchProfile();
    } catch (error) {
      console.error('Error updating profile:', error);
    } finally {
      setUpdating(false);
    }
  };

  if (loading) {
    return (
      <View style={[styles.container, styles.centerContent]}>
        <ActivityIndicator size="large" color="#2196F3" />
        <Text style={styles.loadingText}>Loading profile...</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <Surface style={styles.profileCard}>
        <View style={styles.avatarContainer}>
          <CloudinaryAvatar
            source={imageUri}
            label={formData.displayName?.substring(0, 2) || 'ST'}
            size={120}
            style={styles.avatar}
            backgroundColor="#2196F3"
          />
          <CustomButton
            mode="text"
            onPress={pickImage}
            style={styles.changePhotoButton}
          >
            {translate('profileManagement.changePhoto') || 'Change Photo'}
          </CustomButton>
        </View>

        <View style={styles.infoContainer}>
          <Title style={styles.name}>{formData.displayName}</Title>
          <Text style={styles.email}>{formData.email}</Text>

          <List.Section>
            <List.Item
              title="Phone"
              description={formData.phone || 'Not set'}
              left={props => <List.Icon {...props} icon="phone" />}
            />
            <List.Item
              title="Address"
              description={formData.address || 'Not set'}
              left={props => <List.Icon {...props} icon="map-marker" />}
            />
            <List.Item
              title="Parent Name"
              description={formData.parentName || 'Not set'}
              left={props => <List.Icon {...props} icon="account-child" />}
            />
            <List.Item
              title="Parent Phone"
              description={formData.parentPhone || 'Not set'}
              left={props => <List.Icon {...props} icon="phone-classic" />}
            />
            <List.Item
              title="Emergency Contact"
              description={formData.emergencyContact || 'Not set'}
              left={props => <List.Icon {...props} icon="phone-alert" />}
            />
            <List.Item
              title="Blood Group"
              description={formData.bloodGroup || 'Not set'}
              left={props => <List.Icon {...props} icon="water" color="red" />}
            />
            <List.Item
              title="Date of Birth"
              description={formData.dateOfBirth || 'Not set'}
              left={props => <List.Icon {...props} icon="calendar" />}
            />
            <List.Item
              title="Admission Number"
              description={formData.admissionNumber || 'Not set'}
              left={props => <List.Icon {...props} icon="card-account-details" />}
            />
          </List.Section>

          <CustomButton
            mode="contained"
            onPress={() => setVisible(true)}
            style={styles.editButton}
          >
            {translate('profileManagement.editProfile') || 'Edit Profile'}
          </CustomButton>

          <CustomButton
            mode="outlined"
            onPress={() => setPasswordModalVisible(true)}
            style={styles.passwordButton}
            icon="lock-reset"
          >
            {translate('profileManagement.changePassword') || 'Change Password'}
          </CustomButton>
        </View>
      </Surface>

      {/* Change Password Modal */}
      <ChangePasswordModal
        visible={passwordModalVisible}
        onDismiss={() => setPasswordModalVisible(false)}
        onSuccess={() => setPasswordModalVisible(false)}
      />

      <Portal>
        <Modal
          visible={visible}
          onDismiss={() => setVisible(false)}
          contentContainerStyle={styles.modalContainer}
        >
          <ScrollView>
            <Title style={styles.modalTitle}>Edit Profile</Title>

            {Object.keys(formData).map((key) => (
              <CustomInput
                key={key}
                label={key.charAt(0).toUpperCase() + key.slice(1).replace(/([A-Z])/g, ' $1')}
                value={formData[key]}
                onChangeText={(text) => setFormData({ ...formData, [key]: text })}
                style={styles.input}
              />
            ))}

            <View style={styles.modalButtons}>
              <CustomButton
                mode="outlined"
                onPress={() => setVisible(false)}
                style={styles.modalButton}
              >
                Cancel
              </CustomButton>
              <CustomButton
                mode="contained"
                onPress={handleUpdateProfile}
                loading={updating}
                disabled={updating}
                style={styles.modalButton}
              >
                Save Changes
              </CustomButton>
            </View>
          </ScrollView>
        </Modal>
      </Portal>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  profileCard: {
    margin: 16,
    padding: 16,
    borderRadius: 12,
    elevation: 4,
    backgroundColor: '#ffffff',
  },
  avatarContainer: {
    alignItems: 'center',
    marginBottom: 20,
  },
  avatar: {
    marginBottom: 10,
  },
  changePhotoButton: {
    marginTop: 8,
  },
  infoContainer: {
    padding: 16,
  },
  name: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  email: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 20,
  },
  editButton: {
    marginTop: 20,
  },
  passwordButton: {
    marginTop: 10,
  },
  modalContainer: {
    backgroundColor: 'white',
    padding: 20,
    margin: 20,
    borderRadius: 12,
    maxHeight: '80%',
  },
  modalTitle: {
    textAlign: 'center',
    marginBottom: 20,
  },
  input: {
    marginBottom: 12,
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
  },
  modalButton: {
    flex: 1,
    marginHorizontal: 5,
  },
});

export default ProfileManagement;
