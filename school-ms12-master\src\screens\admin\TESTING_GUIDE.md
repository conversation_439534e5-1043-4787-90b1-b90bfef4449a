# Testing Guide for Mobile User Management

This guide will help you test the Mobile User Management implementation on an Android device using Expo Go.

## Prerequisites

1. Make sure you have Expo Go installed on your Android device
2. Ensure your development machine and Android device are on the same network
3. Verify that the MobileUserManagement component has been properly combined
4. Confirm that the navigation has been updated to include the MobileUserManagement screen

## Testing Steps

### 1. Start the Expo Development Server

```bash
# Navigate to your project directory
cd your-project-directory

# Start the Expo development server
expo start
```

### 2. Connect Your Android Device

- Open Expo Go on your Android device
- Scan the QR code displayed in your terminal or Expo DevTools
- Alternatively, you can enter the development URL manually

### 3. Test User Authentication

- Log in with admin credentials
- Verify that you can access the admin dashboard

### 4. Navigate to User Management

- Tap on the "Users" icon in the bottom navigation
- Verify that the Mobile User Management screen appears
- Check that the screen title and subtitle are displayed correctly

### 5. Test Data Loading

- Verify that user data is loaded from Firebase
- Check that the loading indicator and progress bar work correctly
- Confirm that user statistics (total, active, inactive, new) are displayed

### 6. Test Search Functionality

- Enter a search term in the search bar
- Verify that the list filters correctly based on the search
- Test searching by name, email, role, etc.
- Clear the search and verify that all users are displayed again

### 7. Test User List Display

- Verify that user cards display correctly with:
  - User avatar or initials
  - Full name
  - Email
  - Role chip
  - Status chip
- Check that role-specific colors are applied correctly
- Verify that the list scrolls smoothly

### 8. Test Swipe Actions

- Swipe left on a user card to reveal the edit and delete actions
- Swipe right to reveal the status change action
- Verify that the swipe actions are responsive and visually clear

### 9. Test User Details Modal

- Tap on a user card to open the user details modal
- Verify that all user information is displayed correctly
- Test the modal close button
- Check that the edit and reset password buttons work

### 10. Test Status Change

- Swipe right on a user card and tap "Activate" or "Deactivate"
- Verify that the confirmation dialog appears
- Test both confirming and canceling the action
- Check that the user status updates correctly in the UI
- Verify that a success message appears

### 11. Test User Deletion

- Swipe left on a user card and tap "Delete"
- Verify that the confirmation dialog appears
- Test both confirming and canceling the action
- Check that the user is removed from the list when deleted
- Verify that a success message appears

### 12. Test Password Reset

- Open a user's details and tap "Reset Password"
- Verify that the confirmation dialog appears
- Test both confirming and canceling the action
- Check that a success message appears

### 13. Test Filter and Sort

- Tap the filter FAB action
- Verify that the filter menu appears
- Test filtering by different roles and statuses
- Tap the sort FAB action
- Verify that the sort menu appears
- Test sorting by different fields and directions

### 14. Test Network Handling

- Enable airplane mode on your device
- Verify that an offline message appears
- Disable airplane mode
- Check that data is automatically refreshed

### 15. Test Pull-to-Refresh

- Pull down on the user list
- Verify that the refresh indicator appears
- Check that the data is refreshed

### 16. Test RTL Support

- Change the app language to an RTL language (if supported)
- Verify that the UI adjusts correctly for RTL layout

### 17. Test Animations

- Verify that animations work smoothly when:
  - Loading the screen
  - Opening/closing modals
  - Displaying user cards
  - Showing statistics

### 18. Test Bottom Navigation

- Verify that the bottom navigation bar is displayed
- Test navigating to other screens and back to User Management

## Reporting Issues

If you encounter any issues during testing, document them with the following information:

1. Description of the issue
2. Steps to reproduce
3. Expected behavior
4. Actual behavior
5. Screenshots (if applicable)
6. Device information (model, Android version)
7. Expo Go version

## Performance Considerations

Pay attention to the following performance aspects:

- Initial loading time
- Scrolling smoothness with many users
- Animation performance
- Response time for actions (status change, deletion, etc.)
- Memory usage over time
