import { Cloudinary } from '@cloudinary/url-gen';
import { fill, scale, crop, thumbnail } from '@cloudinary/url-gen/actions/resize';
import { byRadius } from '@cloudinary/url-gen/actions/roundCorners';
import { format, quality } from '@cloudinary/url-gen/actions/delivery';
import { auto } from '@cloudinary/url-gen/qualifiers/quality';

// Parse Cloudinary URL from environment variable
const parseCloudinaryUrl = (url) => {
  try {
    // Format: cloudinary://api_key:api_secret@cloud_name
    const regex = /cloudinary:\/\/([^:]+):([^@]+)@(.+)/;
    const match = url.match(regex);

    if (match && match.length === 4) {
      return {
        apiKey: match[1],
        apiSecret: match[2],
        cloudName: match[3]
      };
    }

    throw new Error('Invalid Cloudinary URL format');
  } catch (error) {
    console.error('Error parsing Cloudinary URL:', error);
    return {
      apiKey: '173765679161433',
      apiSecret: 'TfMvTIc2j0kMeHijwsXwVgPyyGo',
      cloudName: 'dgonrknbt'
    };
  }
};

// Use the provided Cloudinary URL
const cloudinaryConfig = parseCloudinaryUrl('cloudinary://173765679161433:TfMvTIc2j0kMeHijwsXwVgPyyGo@dgonrknbt');

// Create a Cloudinary instance for URL generation
const cld = new Cloudinary({
  cloud: {
    cloudName: cloudinaryConfig.cloudName
  },
  url: {
    secure: true
  }
});

class CloudinaryService {
  /**
   * Upload a file to Cloudinary
   * @param {File|Blob} file - The file to upload
   * @param {Object} options - Upload options
   * @param {string} options.folder - Cloudinary folder to store the file in
   * @param {string} options.resourceType - Type of resource ('image', 'video', 'raw', 'auto')
   * @param {Array} options.tags - Tags to assign to the uploaded file
   * @param {Function} onProgress - Progress callback function
   * @returns {Promise<Object>} - Cloudinary response
   */
  static async uploadFile(file, options = {}, onProgress = null) {
    try {
      const {
        folder = 'school-ms',
        resourceType = 'auto',
        tags = [],
        publicId = null
      } = options;

      // Create a FormData object to prepare the file for upload
      const formData = new FormData();

      // Handle both File/Blob objects and local URIs from Expo's image picker
      if (typeof file === 'string' && file.startsWith('file://')) {
        // For Expo/React Native, we need to create a file object from the URI
        const fileType = file.endsWith('.jpg') || file.endsWith('.jpeg') ? 'image/jpeg' :
                        file.endsWith('.png') ? 'image/png' :
                        file.endsWith('.gif') ? 'image/gif' : 'image/jpeg';

        formData.append('file', {
          uri: file,
          type: fileType,
          name: `upload.${fileType.split('/')[1]}`
        });
      } else {
        formData.append('file', file);
      }

      // Use unsigned upload with a preset (create this in your Cloudinary dashboard)
      formData.append('upload_preset', 'mobile_upload'); // Use the mobile_upload preset
      formData.append('folder', folder);

      if (tags && tags.length > 0) {
        formData.append('tags', tags.join(','));
      }

      if (publicId) {
        formData.append('public_id', publicId);
      }

      // Determine the correct upload URL based on resource type
      const uploadUrl = `https://api.cloudinary.com/v1_1/${cloudinaryConfig.cloudName}/${resourceType}/upload`;

      // Use XMLHttpRequest for tracking upload progress
      if (onProgress) {
        return new Promise((resolve, reject) => {
          const xhr = new XMLHttpRequest();

          xhr.open('POST', uploadUrl, true);

          xhr.upload.onprogress = (event) => {
            if (event.lengthComputable) {
              const progress = Math.round((event.loaded / event.total) * 100);
              onProgress(progress);
            }
          };

          xhr.onload = () => {
            if (xhr.status === 200) {
              const response = JSON.parse(xhr.responseText);
              resolve(response);
            } else {
              reject(new Error('Upload failed'));
            }
          };

          xhr.onerror = () => reject(new Error('Upload failed'));

          xhr.send(formData);
        });
      } else {
        // Use fetch API if progress tracking is not required
        const response = await fetch(uploadUrl, {
          method: 'POST',
          body: formData
        });

        if (!response.ok) {
          throw new Error(`Upload failed with status: ${response.status}`);
        }

        return await response.json();
      }
    } catch (error) {
      console.error('Error uploading to Cloudinary:', error);
      throw error;
    }
  }

  /**
   * Upload an image to Cloudinary
   * @param {File|Blob} file - The image file to upload
   * @param {Object} options - Upload options
   * @param {Function} onProgress - Progress callback function
   * @returns {Promise<Object>} - Cloudinary response
   */
  static uploadImage(file, options = {}, onProgress = null) {
    return this.uploadFile(file, {
      ...options,
      resourceType: 'image',
      folder: options.folder || 'school-ms/images'
    }, onProgress);
  }

  /**
   * Upload a video to Cloudinary
   * @param {File|Blob} file - The video file to upload
   * @param {Object} options - Upload options
   * @param {Function} onProgress - Progress callback function
   * @returns {Promise<Object>} - Cloudinary response
   */
  static uploadVideo(file, options = {}, onProgress = null) {
    return this.uploadFile(file, {
      ...options,
      resourceType: 'video',
      folder: options.folder || 'school-ms/videos'
    }, onProgress);
  }

  /**
   * Upload a document (PDF, DOCX, PPT, etc.) to Cloudinary
   * @param {File|Blob} file - The document file to upload
   * @param {Object} options - Upload options
   * @param {Function} onProgress - Progress callback function
   * @returns {Promise<Object>} - Cloudinary response
   */
  static uploadDocument(file, options = {}, onProgress = null) {
    return this.uploadFile(file, {
      ...options,
      resourceType: 'raw',
      folder: options.folder || 'school-ms/documents'
    }, onProgress);
  }

  /**
   * Generate a Cloudinary URL for an asset
   * @param {string} publicId - The public ID of the asset
   * @param {Object} options - URL generation options
   * @returns {string} - The Cloudinary URL
   */
  static getUrl(publicId, options = {}) {
    const {
      width,
      height,
      crop,
      format,
      quality,
      effect
    } = options;

    let asset = cld.image(publicId);

    // Apply transformations if needed
    if (width || height) {
      asset = asset.resize(crop || 'scale', width, height);
    }

    if (format) {
      asset = asset.format(format);
    }

    if (quality) {
      asset = asset.quality(quality);
    }

    if (effect) {
      asset = asset.effect(effect);
    }

    return asset.toURL();
  }

  /**
   * Delete an asset from Cloudinary
   * @param {string} publicId - The public ID of the asset to delete
   * @param {string} resourceType - Type of resource ('image', 'video', 'raw')
   * @returns {Promise<Object>} - Cloudinary response
   */
  static async deleteAsset(publicId, resourceType = 'image') {
    try {
      if (!publicId) {
        throw new Error('Public ID is required for deletion');
      }

      console.log(`Deleting asset from Cloudinary: ${publicId} (${resourceType})`);

      // Create form data for the delete request
      const formData = new FormData();
      formData.append('public_id', publicId);
      formData.append('api_key', cloudinaryConfig.apiKey);

      // Generate timestamp for the signature
      const timestamp = Math.floor(Date.now() / 1000);
      formData.append('timestamp', timestamp.toString());

      // In a production app, you would generate the signature on the server side
      // For this example, we'll use a simplified approach with a direct API call

      // Make the delete request
      const response = await fetch(`https://api.cloudinary.com/v1_1/${cloudinaryConfig.cloudName}/${resourceType}/destroy`, {
        method: 'POST',
        body: formData,
        headers: {
          'X-Requested-With': 'XMLHttpRequest',
        }
      });

      const result = await response.json();

      if (response.ok && result.result === 'ok') {
        console.log(`Successfully deleted asset: ${publicId}`);
        return result;
      } else {
        console.error('Error deleting asset:', result.error || 'Unknown error');
        throw new Error(result.error || 'Failed to delete asset');
      }
    } catch (error) {
      console.error('Error deleting from Cloudinary:', error);
      throw error;
    }
  }

  /**
   * Create a signed upload URL for direct uploads from the client
   * @param {Object} options - Options for the signed URL
   * @returns {Object} - Signed upload parameters
   */
  static getSignedUploadParams(options = {}) {
    const {
      folder = 'school-ms',
      resourceType = 'auto',
      publicId = null,
      tags = []
    } = options;

    const timestamp = Math.round((new Date()).getTime() / 1000);

    const params = {
      timestamp,
      folder,
      upload_preset: 'mobile_upload', // Use the mobile_upload unsigned upload preset
      api_key: cloudinaryConfig.apiKey,
      cloud_name: cloudinaryConfig.cloudName
    };

    if (tags && tags.length > 0) {
      params.tags = tags.join(',');
    }

    if (publicId) {
      params.public_id = publicId;
    }

    return {
      ...params,
      resourceType,
      uploadUrl: `https://api.cloudinary.com/v1_1/${cloudinaryConfig.cloudName}/${resourceType}/upload`
    };
  }

  /**
   * Generate a Cloudinary URL for previewing a document
   * @param {string} publicId - The public ID of the document
   * @param {Object} options - Options for the preview
   * @returns {string} - The Cloudinary URL for the document preview
   */
  static getDocumentPreviewUrl(publicId, options = {}) {
    if (!publicId) return null;

    const { page = 1, width = 800, format = 'jpg' } = options;

    // For PDFs and other documents, we can generate a preview image of a specific page
    return `https://res.cloudinary.com/${cloudinaryConfig.cloudName}/image/fetch/f_${format},w_${width},pg_${page}/${publicId}`;
  }

  /**
   * Upload an image from a local URI (for React Native/Expo)
   * @param {string} uri - Local URI of the image
   * @param {Object} options - Upload options
   * @returns {Promise<Object>} - Upload result
   */
  static async uploadFromUri(uri, options = {}) {
    try {
      if (!uri) {
        throw new Error('URI is required for upload');
      }

      return await this.uploadFile(uri, {
        ...options,
        resourceType: 'image'
      });
    } catch (error) {
      console.error('Error uploading from URI:', error);
      throw error;
    }
  }

  /**
   * Check if a file is an acceptable image type
   * @param {File} file - The file to check
   * @returns {boolean} - Whether the file is an acceptable image
   */
  static isAcceptableImage(file) {
    const acceptableTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'];
    return acceptableTypes.includes(file.type);
  }

  /**
   * Check if a file is an acceptable video type
   * @param {File} file - The file to check
   * @returns {boolean} - Whether the file is an acceptable video
   */
  static isAcceptableVideo(file) {
    const acceptableTypes = ['video/mp4', 'video/webm', 'video/ogg', 'video/quicktime'];
    return acceptableTypes.includes(file.type);
  }

  /**
   * Check if a file is an acceptable document type
   * @param {File} file - The file to check
   * @returns {boolean} - Whether the file is an acceptable document
   */
  static isAcceptableDocument(file) {
    const acceptableTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .docx
      'application/vnd.ms-powerpoint',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation', // .pptx
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' // .xlsx
    ];
    return acceptableTypes.includes(file.type);
  }

  /**
   * Convert a Firebase Storage URL to a Cloudinary URL
   * This is useful during migration from Firebase Storage to Cloudinary
   *
   * @param {string} firebaseUrl - Firebase Storage URL
   * @param {Object} options - Transformation options
   * @returns {string} - Cloudinary URL
   */
  static getCloudinaryFromFirebase(firebaseUrl, options = {}) {
    if (!firebaseUrl) return null;

    // Use Cloudinary's fetch capability to serve the image from Firebase
    // This allows us to apply Cloudinary transformations to Firebase images
    // during the migration period

    const { width, height, crop = 'fill', quality = 'auto', format } = options;

    let transformations = [];

    if (width) transformations.push(`w_${width}`);
    if (height) transformations.push(`h_${height}`);
    if (crop) transformations.push(`c_${crop}`);
    if (quality) transformations.push(`q_${quality}`);
    if (format) transformations.push(`f_${format}`);

    const transformationString = transformations.length > 0
      ? transformations.join(',') + '/'
      : '';

    return `https://res.cloudinary.com/${cloudinaryConfig.cloudName}/image/fetch/${transformationString}${encodeURIComponent(firebaseUrl)}`;
  }

  /**
   * Get image URL from either Cloudinary public ID or Firebase URL
   * This helps with the migration from Firebase to Cloudinary
   *
   * @param {string} imageSource - Either a Cloudinary public ID or a Firebase URL
   * @param {Object} options - Transformation options
   * @returns {string} - Image URL
   */
  static getImageUrl(imageSource, options = {}) {
    if (!imageSource) return null;

    // Check if it's a Firebase URL
    if (typeof imageSource === 'string' &&
        (imageSource.includes('firebasestorage.googleapis.com') ||
         imageSource.includes('storage.googleapis.com'))) {
      return this.getCloudinaryFromFirebase(imageSource, options);
    }

    // Otherwise treat it as a Cloudinary public ID
    return this.getUrl(imageSource, options);
  }
}

export default CloudinaryService;