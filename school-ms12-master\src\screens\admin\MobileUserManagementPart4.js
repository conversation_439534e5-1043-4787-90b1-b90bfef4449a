        {/* Confirmation dialogs */}
        <Portal>
          <Dialog
            visible={showDeleteConfirmDialog}
            onDismiss={() => setShowDeleteConfirmDialog(false)}
            style={styles.dialog}
          >
            <Dialog.Title>{translate('userManagement.confirmDelete')}</Dialog.Title>
            <Dialog.Content>
              <Paragraph>
                {translate('userManagement.deleteWarning')}
              </Paragraph>
              {selectedUser && (
                <Surface style={styles.userSummary}>
                  <Text style={styles.userSummaryName}>
                    {`${selectedUser.firstName || ''} ${selectedUser.lastName || ''}`}
                  </Text>
                  <Text style={styles.userSummaryEmail}>{selectedUser.email}</Text>
                </Surface>
              )}
            </Dialog.Content>
            <Dialog.Actions>
              <Button
                onPress={() => setShowDeleteConfirmDialog(false)}
              >
                {translate('common.cancel')}
              </Button>
              <Button
                mode="contained"
                onPress={() => selectedUser && handleDeleteUser(selectedUser.id)}
                style={{ backgroundColor: mobileTheme.colors.error }}
                loading={loading}
                disabled={loading}
              >
                {translate('common.delete')}
              </Button>
            </Dialog.Actions>
          </Dialog>
          
          <Dialog
            visible={showActivateConfirmDialog}
            onDismiss={() => setShowActivateConfirmDialog(false)}
            style={styles.dialog}
          >
            <Dialog.Title>{translate('userManagement.confirmActivate')}</Dialog.Title>
            <Dialog.Content>
              <Paragraph>
                {translate('userManagement.activateWarning')}
              </Paragraph>
              {selectedUser && (
                <Surface style={styles.userSummary}>
                  <Text style={styles.userSummaryName}>
                    {`${selectedUser.firstName || ''} ${selectedUser.lastName || ''}`}
                  </Text>
                  <Text style={styles.userSummaryEmail}>{selectedUser.email}</Text>
                </Surface>
              )}
            </Dialog.Content>
            <Dialog.Actions>
              <Button
                onPress={() => setShowActivateConfirmDialog(false)}
              >
                {translate('common.cancel')}
              </Button>
              <Button
                mode="contained"
                onPress={() => {
                  selectedUser && handleStatusChange(selectedUser.id, 'active');
                  setShowActivateConfirmDialog(false);
                }}
                style={{ backgroundColor: mobileTheme.colors.success }}
                loading={loading}
                disabled={loading}
              >
                {translate('userManagement.activate')}
              </Button>
            </Dialog.Actions>
          </Dialog>
          
          <Dialog
            visible={showDeactivateConfirmDialog}
            onDismiss={() => setShowDeactivateConfirmDialog(false)}
            style={styles.dialog}
          >
            <Dialog.Title>{translate('userManagement.confirmDeactivate')}</Dialog.Title>
            <Dialog.Content>
              <Paragraph>
                {translate('userManagement.deactivateWarning')}
              </Paragraph>
              {selectedUser && (
                <Surface style={styles.userSummary}>
                  <Text style={styles.userSummaryName}>
                    {`${selectedUser.firstName || ''} ${selectedUser.lastName || ''}`}
                  </Text>
                  <Text style={styles.userSummaryEmail}>{selectedUser.email}</Text>
                </Surface>
              )}
            </Dialog.Content>
            <Dialog.Actions>
              <Button
                onPress={() => setShowDeactivateConfirmDialog(false)}
              >
                {translate('common.cancel')}
              </Button>
              <Button
                mode="contained"
                onPress={() => {
                  selectedUser && handleStatusChange(selectedUser.id, 'inactive');
                  setShowDeactivateConfirmDialog(false);
                }}
                style={{ backgroundColor: mobileTheme.colors.warning }}
                loading={loading}
                disabled={loading}
              >
                {translate('userManagement.deactivate')}
              </Button>
            </Dialog.Actions>
          </Dialog>
          
          <Dialog
            visible={showPasswordResetDialog}
            onDismiss={() => setShowPasswordResetDialog(false)}
            style={styles.dialog}
          >
            <Dialog.Title>{translate('userManagement.resetPassword')}</Dialog.Title>
            <Dialog.Content>
              <Paragraph>
                {translate('userManagement.resetPasswordWarning')}
              </Paragraph>
              {selectedUser && (
                <Surface style={styles.userSummary}>
                  <Text style={styles.userSummaryName}>
                    {`${selectedUser.firstName || ''} ${selectedUser.lastName || ''}`}
                  </Text>
                  <Text style={styles.userSummaryEmail}>{selectedUser.email}</Text>
                </Surface>
              )}
            </Dialog.Content>
            <Dialog.Actions>
              <Button
                onPress={() => setShowPasswordResetDialog(false)}
              >
                {translate('common.cancel')}
              </Button>
              <Button
                mode="contained"
                onPress={() => selectedUser && handlePasswordReset(selectedUser.email)}
                style={{ backgroundColor: mobileTheme.colors.primary }}
                loading={passwordResetLoading}
                disabled={passwordResetLoading}
              >
                {translate('userManagement.sendResetLink')}
              </Button>
            </Dialog.Actions>
          </Dialog>
          
          {/* User details modal */}
          <Modal
            visible={showUserDetailsModal}
            onDismiss={() => setShowUserDetailsModal(false)}
            contentContainerStyle={styles.modalContainer}
          >
            {selectedUser && (
              <Surface style={styles.modalSurface}>
                <View style={styles.modalHeader}>
                  <Title style={styles.modalTitle}>
                    {translate('userManagement.userDetails')}
                  </Title>
                  <IconButton
                    icon="close"
                    size={24}
                    onPress={() => setShowUserDetailsModal(false)}
                  />
                </View>
                
                <Divider />
                
                <ScrollView style={styles.modalContent}>
                  <Animatable.View 
                    animation="fadeIn" 
                    duration={500}
                    style={styles.userDetailHeader}
                  >
                    {selectedUser.photoURL ? (
                      <Avatar.Image 
                        source={{ uri: selectedUser.photoURL }} 
                        size={80} 
                        style={styles.detailAvatar}
                      />
                    ) : (
                      <Avatar.Text 
                        label={`${selectedUser.firstName?.[0] || ''}${selectedUser.lastName?.[0] || ''}`.toUpperCase()} 
                        size={80} 
                        style={[styles.detailAvatar, { 
                          backgroundColor: (() => {
                            switch(selectedUser.role) {
                              case 'admin': return mobileTheme.colors.admin;
                              case 'teacher': return mobileTheme.colors.teacher;
                              case 'student': return mobileTheme.colors.student;
                              case 'parent': return mobileTheme.colors.parent;
                              default: return mobileTheme.colors.primary;
                            }
                          })()
                        }]}
                      />
                    )}
                    
                    <View style={styles.userDetailInfo}>
                      <Title style={styles.detailName}>
                        {`${selectedUser.firstName || ''} ${selectedUser.lastName || ''}`}
                      </Title>
                      <Paragraph style={styles.detailEmail}>{selectedUser.email}</Paragraph>
                    </View>
                  </Animatable.View>
                  
                  <Divider style={styles.divider} />
                  
                  <List.Section>
                    <List.Subheader>{translate('userManagement.accountInfo')}</List.Subheader>
                    
                    <List.Item
                      title={translate('userManagement.role')}
                      description={translate(`userManagement.roles.${selectedUser.role}`) || selectedUser.role}
                      left={props => <List.Icon {...props} icon="badge-account" />}
                    />
                    
                    <List.Item
                      title={translate('userManagement.status.status')}
                      description={translate(`userManagement.status.${selectedUser.status}`) || selectedUser.status}
                      left={props => <List.Icon {...props} icon={selectedUser.status === 'active' ? 'check-circle' : 'close-circle'} />}
                    />
                    
                    <List.Item
                      title={translate('userManagement.createdAt')}
                      description={
                        selectedUser.createdAt 
                          ? format(new Date(selectedUser.createdAt), 'PPP')
                          : translate('common.notAvailable')
                      }
                      left={props => <List.Icon {...props} icon="calendar-plus" />}
                    />
                    
                    <List.Item
                      title={translate('userManagement.lastLogin')}
                      description={
                        selectedUser.lastLogin 
                          ? formatDistance(new Date(selectedUser.lastLogin), new Date(), { addSuffix: true })
                          : translate('common.notAvailable')
                      }
                      left={props => <List.Icon {...props} icon="login" />}
                    />
                  </List.Section>
                  
                  <Divider style={styles.divider} />
                  
                  <List.Section>
                    <List.Subheader>{translate('userManagement.contactInfo')}</List.Subheader>
                    
                    <List.Item
                      title={translate('userManagement.email')}
                      description={selectedUser.email || translate('common.notAvailable')}
                      left={props => <List.Icon {...props} icon="email" />}
                    />
                    
                    <List.Item
                      title={translate('userManagement.phone')}
                      description={selectedUser.phoneNumber || translate('common.notAvailable')}
                      left={props => <List.Icon {...props} icon="phone" />}
                    />
                  </List.Section>
                </ScrollView>
                
                <Divider />
                
                <View style={styles.modalActions}>
                  <Button
                    mode="outlined"
                    onPress={() => {
                      setShowUserDetailsModal(false);
                      setShowPasswordResetDialog(true);
                    }}
                    style={styles.actionButton}
                    icon="key"
                  >
                    {translate('userManagement.resetPassword')}
                  </Button>
                  
                  <Button
                    mode="contained"
                    onPress={() => {
                      setShowUserDetailsModal(false);
                      navigation.navigate('UserEdit', { userId: selectedUser.id });
                    }}
                    style={styles.actionButton}
                    icon="pencil"
                  >
                    {translate('common.edit')}
                  </Button>
                </View>
              </Surface>
            )}
          </Modal>
        </Portal>
        
        {/* Snackbar for messages */}
        <Snackbar
          visible={snackbarVisible}
          onDismiss={() => setSnackbarVisible(false)}
          duration={3000}
          style={styles.snackbar}
          action={{
            label: translate('common.dismiss'),
            onPress: () => setSnackbarVisible(false),
          }}
        >
          {errorMessage}
        </Snackbar>
      </View>
    </MobileScreenWrapper>
  );
