import React, { useState, useEffect, useRef } from 'react';
import { View, ScrollView, StyleSheet, Animated, TouchableOpacity, Alert } from 'react-native';
import {
  Card,
  Title,
  Text,
  DataTable,
  Portal,
  Modal,
  TextInput,
  ActivityIndicator,
  Chip,
  IconButton,
  Searchbar,
  Menu,
  Divider,
  Button,
  useTheme,
  Snackbar,
  Surface
} from 'react-native-paper';
import { db, auth, storage } from '../../config/firebase';
import { collection, query, getDocs, addDoc, updateDoc, doc, deleteDoc, where, orderBy } from 'firebase/firestore';
import { ref, uploadBytes, getDownloadURL, deleteObject } from 'firebase/storage';
import CustomButton from '../../components/common/CustomButton';
import { Picker } from '@react-native-picker/picker';
import * as DocumentPicker from 'expo-document-picker';
import EthiopianCalendar from '../../utils/EthiopianCalendar';
import { useNavigation } from '@react-navigation/native';
import { LinearGradient } from 'expo-linear-gradient';
import * as Animatable from 'react-native-animatable';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useLanguage } from '../../context/LanguageContext';
import AdminAppHeader from '../../components/common/AdminAppHeader';
import AdminSidebar from '../../components/common/AdminSidebar';
import SidebarBackdrop from '../../components/common/SidebarBackdrop';
import ActivityService from '../../services/ActivityService';

const ResourceManagement = () => {
  // No theme needed
  const navigation = useNavigation();
  const { translate, language, getTextStyle, isRTL } = useLanguage();

  // Resource state
  const [resources, setResources] = useState([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [loading, setLoading] = useState(true);
  const [selectedResource, setSelectedResource] = useState(null);
  const [confirmDeleteVisible, setConfirmDeleteVisible] = useState(false);
  const [resourceToDelete, setResourceToDelete] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterMenuVisible, setFilterMenuVisible] = useState(false);
  const [sortBy, setSortBy] = useState('name');
  const [sortOrder, setSortOrder] = useState('asc');
  const [filterType, setFilterType] = useState('');
  const [filterStatus, setFilterStatus] = useState('');
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [formErrors, setFormErrors] = useState({});

  // Resource form state
  const [resourceForm, setResourceForm] = useState({
    name: '',
    type: 'book', // book, equipment, software, other
    category: '', // academic, sports, laboratory, library, etc.
    quantity: '1',
    condition: 'new', // new, good, fair, poor
    location: '',
    description: '',
    attachments: [],
    status: 'available', // available, in-use, maintenance, retired
  });

  // Animation states
  const [fadeAnim] = useState(new Animated.Value(0));
  const [slideAnim] = useState(new Animated.Value(50));

  // Sidebar state
  const [drawerOpen, setDrawerOpen] = useState(false);
  const drawerAnim = useRef(new Animated.Value(-300)).current;
  const backdropFadeAnim = useRef(new Animated.Value(0)).current;
  const [activeSidebarItem, setActiveSidebarItem] = useState('ResourceManagement');

  // Toggle drawer function
  const toggleDrawer = () => {
    if (drawerOpen) {
      // Close drawer
      Animated.parallel([
        Animated.timing(drawerAnim, {
          toValue: -300,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(backdropFadeAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      // Open drawer
      Animated.parallel([
        Animated.timing(drawerAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(backdropFadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    }
    setDrawerOpen(!drawerOpen);
  };

  // Animation on mount
  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 500,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 500,
        useNativeDriver: true,
      }),
    ]).start();

    // Hide the default header
    navigation.setOptions({
      headerShown: false,
    });
  }, [navigation, fadeAnim, slideAnim]);

  const resourceCategories = {
    academic: ['Textbooks', 'Reference Books', 'Study Materials'],
    sports: ['Equipment', 'Uniforms', 'Field Equipment'],
    laboratory: ['Science Equipment', 'Chemistry Lab', 'Physics Lab', 'Biology Lab'],
    library: ['Books', 'Magazines', 'Digital Resources'],
    technology: ['Computers', 'Projectors', 'Audio Equipment'],
    classroom: ['Furniture', 'Teaching Aids', 'Stationery'],
  };

  useEffect(() => {
    fetchResources();
  }, []);

  const fetchResources = async () => {
    try {
      setLoading(true);

      // Build query with filters
      let resourcesQuery = collection(db, 'resources');
      let queryConstraints = [];

      if (filterType) {
        queryConstraints.push(where('type', '==', filterType));
      }

      if (filterStatus) {
        queryConstraints.push(where('status', '==', filterStatus));
      }

      // Add sorting
      queryConstraints.push(orderBy(sortBy, sortOrder));

      // Apply query constraints if any
      if (queryConstraints.length > 0) {
        resourcesQuery = query(resourcesQuery, ...queryConstraints);
      }

      const querySnapshot = await getDocs(resourcesQuery);

      let resourceData = [];
      querySnapshot.forEach((doc) => {
        resourceData.push({ id: doc.id, ...doc.data() });
      });

      // Apply search filter if search query exists
      if (searchQuery) {
        const lowerCaseQuery = searchQuery.toLowerCase();
        resourceData = resourceData.filter(resource =>
          resource.name.toLowerCase().includes(lowerCaseQuery) ||
          resource.category.toLowerCase().includes(lowerCaseQuery) ||
          resource.description.toLowerCase().includes(lowerCaseQuery)
        );
      }

      setResources(resourceData);

      // Log activity
      await ActivityService.logActivity(
        auth.currentUser?.uid,
        'RESOURCE_VIEW',
        'Viewed resources',
        `Viewed ${resourceData.length} resources with filters`,
        null,
        {
          filters: { filterType, filterStatus, searchQuery },
          resourceCount: resourceData.length,
        }
      );
    } catch (error) {
      console.error('Error fetching resources:', error);
      setSnackbarMessage(translate('messages.errors.network'));
      setSnackbarVisible(true);
    } finally {
      setLoading(false);
    }
  };

  // Validate form
  const validateForm = () => {
    const errors = {};

    if (!resourceForm.name.trim()) {
      errors.name = translate('messages.errors.required');
    }

    if (!resourceForm.type) {
      errors.type = translate('messages.errors.required');
    }

    if (!resourceForm.category) {
      errors.category = translate('messages.errors.required');
    }

    if (!resourceForm.quantity || isNaN(resourceForm.quantity) || parseInt(resourceForm.quantity) <= 0) {
      errors.quantity = translate('messages.errors.invalid');
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleAddResource = async () => {
    try {
      // Validate form
      if (!validateForm()) {
        return;
      }

      setLoading(true);

      // Upload attachments
      const attachmentUrls = await Promise.all(
        resourceForm.attachments.map(async (file) => {
          const response = await fetch(file.uri);
          const blob = await response.blob();
          const storageRef = ref(storage, `resources/${Date.now()}_${file.name}`);
          await uploadBytes(storageRef, blob);
          return await getDownloadURL(storageRef);
        })
      );

      const timestamp = new Date().toISOString();
      const resourceData = {
        ...resourceForm,
        attachments: attachmentUrls,
        createdAt: timestamp,
        lastUpdated: timestamp,
        createdBy: auth.currentUser?.uid,
      };

      // Add to Firestore
      const docRef = await addDoc(collection(db, 'resources'), resourceData);

      // Log activity
      await ActivityService.logActivity(
        auth.currentUser?.uid,
        'RESOURCE_CREATE',
        'Created resource',
        `Created resource: ${resourceData.name}`,
        docRef.id,
        {
          resourceId: docRef.id,
          resourceName: resourceData.name,
          resourceType: resourceData.type,
        }
      );

      // Show success message
      setSnackbarMessage(translate('messages.success.save'));
      setSnackbarVisible(true);

      // Close modal and reset form
      setModalVisible(false);
      resetForm();
      fetchResources();
    } catch (error) {
      console.error('Error adding resource:', error);
      setSnackbarMessage(translate('messages.errors.network'));
      setSnackbarVisible(true);
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateResource = async () => {
    try {
      // Validate form
      if (!validateForm()) {
        return;
      }

      setLoading(true);

      // Upload new attachments and keep existing ones
      const attachmentUrls = await Promise.all(
        resourceForm.attachments.map(async (file) => {
          if (typeof file === 'string') return file;

          const response = await fetch(file.uri);
          const blob = await response.blob();
          const storageRef = ref(storage, `resources/${Date.now()}_${file.name}`);
          await uploadBytes(storageRef, blob);
          return await getDownloadURL(storageRef);
        })
      );

      const timestamp = new Date().toISOString();
      const resourceData = {
        ...resourceForm,
        attachments: attachmentUrls,
        lastUpdated: timestamp,
        updatedBy: auth.currentUser?.uid,
      };

      // Update in Firestore
      await updateDoc(doc(db, 'resources', selectedResource.id), resourceData);

      // Log activity
      await ActivityService.logActivity(
        auth.currentUser?.uid,
        'RESOURCE_UPDATE',
        'Updated resource',
        `Updated resource: ${resourceData.name}`,
        selectedResource.id,
        {
          resourceId: selectedResource.id,
          resourceName: resourceData.name,
          resourceType: resourceData.type,
          changedFields: Object.keys(resourceData).filter(key =>
            resourceData[key] !== selectedResource[key]
          ),
        }
      );

      // Show success message
      setSnackbarMessage(translate('messages.success.update'));
      setSnackbarVisible(true);

      // Close modal and reset form
      setModalVisible(false);
      setSelectedResource(null);
      resetForm();
      fetchResources();
    } catch (error) {
      console.error('Error updating resource:', error);
      setSnackbarMessage(translate('messages.errors.network'));
      setSnackbarVisible(true);
    } finally {
      setLoading(false);
    }
  };

  const confirmDelete = (resource) => {
    setResourceToDelete(resource);
    setConfirmDeleteVisible(true);
  };

  const handleDeleteResource = async () => {
    if (!resourceToDelete) return;

    try {
      setLoading(true);

      // Delete attachments from storage
      await Promise.all(
        resourceToDelete.attachments.map(async (url) => {
          const storageRef = ref(storage, url);
          try {
            await deleteObject(storageRef);
          } catch (error) {
            console.error('Error deleting file:', error);
          }
        })
      );

      // Delete resource document
      await deleteDoc(doc(db, 'resources', resourceToDelete.id));

      // Log activity
      await ActivityService.logActivity(
        auth.currentUser?.uid,
        'RESOURCE_DELETE',
        'Deleted resource',
        `Deleted resource: ${resourceToDelete.name}`,
        resourceToDelete.id,
        {
          resourceId: resourceToDelete.id,
          resourceName: resourceToDelete.name,
          resourceType: resourceToDelete.type,
        }
      );

      // Show success message
      setSnackbarMessage(translate('messages.success.delete'));
      setSnackbarVisible(true);

      // Close confirmation dialog and reset
      setConfirmDeleteVisible(false);
      setResourceToDelete(null);
      fetchResources();
    } catch (error) {
      console.error('Error deleting resource:', error);
      setSnackbarMessage(translate('messages.errors.network'));
      setSnackbarVisible(true);
    } finally {
      setLoading(false);
    }
  };

  const handleFileSelect = async () => {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: '*/*',
        copyToCacheDirectory: false,
      });

      if (result.type === 'success') {
        setResourceForm({
          ...resourceForm,
          attachments: [...resourceForm.attachments, result],
        });
      }
    } catch (error) {
      console.error('Error selecting file:', error);
    }
  };

  const resetForm = () => {
    setResourceForm({
      name: '',
      type: 'book',
      category: '',
      quantity: '1',
      condition: 'new',
      location: '',
      description: '',
      attachments: [],
      status: 'available',
    });
    setFormErrors({});
  };

  const formatDate = (date) => {
    return EthiopianCalendar.formatDate(new Date(date));
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'available': return '#4CAF50';
      case 'in-use': return '#2196F3';
      case 'maintenance': return '#FF9800';
      case 'retired': return '#f44336';
      default: return '#666';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'available': return translate('resource.status.available');
      case 'in-use': return translate('resource.status.inUse');
      case 'maintenance': return translate('resource.status.maintenance');
      case 'retired': return translate('resource.status.retired');
      default: return status;
    }
  };

  const getTypeText = (type) => {
    switch (type) {
      case 'book': return translate('resource.type.book');
      case 'equipment': return translate('resource.type.equipment');
      case 'software': return translate('resource.type.software');
      case 'other': return translate('resource.type.other');
      default: return type;
    }
  };

  const getConditionText = (condition) => {
    switch (condition) {
      case 'new': return translate('resource.condition.new');
      case 'good': return translate('resource.condition.good');
      case 'fair': return translate('resource.condition.fair');
      case 'poor': return translate('resource.condition.poor');
      default: return condition;
    }
  };

  const resetFilters = () => {
    setSearchQuery('');
    setFilterType('');
    setFilterStatus('');
    setSortBy('name');
    setSortOrder('asc');
    fetchResources();
  };

  // Apply filters and refresh data
  const applyFilters = () => {
    fetchResources();
    setFilterMenuVisible(false);
  };

  return (
    <View style={styles.mainContainer}>
      {/* Sidebar */}
      <AdminSidebar
        drawerAnim={drawerAnim}
        activeSidebarItem={activeSidebarItem}
        setActiveSidebarItem={setActiveSidebarItem}
        toggleDrawer={toggleDrawer}
      />

      {/* Backdrop */}
      <SidebarBackdrop
        visible={drawerOpen}
        onPress={toggleDrawer}
        fadeAnim={backdropFadeAnim}
      />

      {/* Admin App Header */}
      <AdminAppHeader
        title={translate('resource.management.title') || "Resource Management"}
        onMenuPress={toggleDrawer}
      />

      <Animated.View
        style={[
          styles.contentContainer,
          { opacity: fadeAnim, transform: [{ translateY: slideAnim }] },
          isRTL && styles.rtlContainer
        ]}
      >
        <ScrollView contentContainerStyle={styles.scrollContent}>
          <Animatable.View animation="fadeIn" duration={500} delay={300}>
            <Surface style={styles.searchContainer} elevation={4}>
              <View style={styles.searchRow}>
                <Searchbar
                  placeholder={translate('resource.search') || "Search resources..."}
                  onChangeText={setSearchQuery}
                  value={searchQuery}
                  style={[styles.searchBar, isRTL && styles.searchBarRTL]}
                  onSubmitEditing={fetchResources}
                />
                <IconButton
                  icon="filter-variant"
                  size={24}
                  color={filterType || filterStatus ? '#1976d2' : '#757575'}
                  onPress={() => setFilterMenuVisible(true)}
                  style={styles.filterButton}
                />
              </View>

              {(filterType || filterStatus) && (
                <View style={styles.activeFiltersContainer}>
                  <Text style={styles.activeFiltersLabel}>{translate('resource.activeFilters')}:</Text>
                  <View style={styles.filterChipsContainer}>
                    {filterType && (
                      <Chip
                        onClose={() => {
                          setFilterType('');
                          fetchResources();
                        }}
                        style={styles.filterChip}
                      >
                        {getTypeText(filterType)}
                      </Chip>
                    )}
                    {filterStatus && (
                      <Chip
                        onClose={() => {
                          setFilterStatus('');
                          fetchResources();
                        }}
                        style={styles.filterChip}
                      >
                        {getStatusText(filterStatus)}
                      </Chip>
                    )}
                    <Button
                      mode="text"
                      onPress={resetFilters}
                      compact
                    >
                      {translate('resource.clearFilters')}
                    </Button>
                  </View>
                </View>
              )}
            </Surface>

            <View style={styles.headerContainer}>
              <Title style={[styles.sectionTitle, getTextStyle()]}>{translate('resource.management.title')}</Title>
              <CustomButton
                mode="contained"
                onPress={() => {
                  setSelectedResource(null);
                  resetForm();
                  setModalVisible(true);
                }}
                icon="plus"
                style={styles.addButton}
                labelStyle={getTextStyle()}
              >
                {translate('resource.addResource')}
              </CustomButton>
            </View>

            {loading ? (
              <View style={styles.loaderContainer}>
                <ActivityIndicator size="large" color={'#1976d2'} />
                <Text style={styles.loaderText}>{translate('common.loading')}</Text>
              </View>
            ) : resources.length === 0 ? (
              <Card style={styles.emptyCard}>
                <Card.Content style={styles.emptyCardContent}>
                  <MaterialCommunityIcons name="package-variant" size={64} color="#BDBDBD" />
                  <Text style={styles.emptyText}>{translate('resource.noResources')}</Text>
                  <CustomButton
                    mode="contained"
                    onPress={() => {
                      setSelectedResource(null);
                      resetForm();
                      setModalVisible(true);
                    }}
                    icon="plus"
                    style={styles.emptyAddButton}
                    labelStyle={getTextStyle()}
                  >
                    {translate('resource.addResource')}
                  </CustomButton>
                </Card.Content>
              </Card>
            ) : (
              <Card style={styles.dataCard} elevation={4}>
                <DataTable>
                  <DataTable.Header style={styles.tableHeader}>
                    <DataTable.Title
                      style={[styles.tableHeaderCell, isRTL && styles.rtlCell]}
                      sortDirection={sortBy === 'name' ? sortOrder : 'none'}
                      onPress={() => {
                        if (sortBy === 'name') {
                          setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
                        } else {
                          setSortBy('name');
                          setSortOrder('asc');
                        }
                        fetchResources();
                      }}
                    >
                      <Text style={[styles.columnTitle, getTextStyle()]}>{translate('resource.name')}</Text>
                    </DataTable.Title>
                    <DataTable.Title
                      style={[styles.tableHeaderCell, isRTL && styles.rtlCell]}
                      sortDirection={sortBy === 'type' ? sortOrder : 'none'}
                      onPress={() => {
                        if (sortBy === 'type') {
                          setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
                        } else {
                          setSortBy('type');
                          setSortOrder('asc');
                        }
                        fetchResources();
                      }}
                    >
                      <Text style={[styles.columnTitle, getTextStyle()]}>{translate('resource.type')}</Text>
                    </DataTable.Title>
                    <DataTable.Title
                      style={[styles.tableHeaderCell, isRTL && styles.rtlCell]}
                    >
                      <Text style={[styles.columnTitle, getTextStyle()]}>{translate('resource.category')}</Text>
                    </DataTable.Title>
                    <DataTable.Title
                      numeric
                      style={[styles.tableHeaderCell, isRTL && styles.rtlCell]}
                      sortDirection={sortBy === 'quantity' ? sortOrder : 'none'}
                      onPress={() => {
                        if (sortBy === 'quantity') {
                          setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
                        } else {
                          setSortBy('quantity');
                          setSortOrder('asc');
                        }
                        fetchResources();
                      }}
                    >
                      <Text style={[styles.columnTitle, getTextStyle()]}>{translate('resource.quantity')}</Text>
                    </DataTable.Title>
                    <DataTable.Title
                      style={[styles.tableHeaderCell, isRTL && styles.rtlCell]}
                      sortDirection={sortBy === 'status' ? sortOrder : 'none'}
                      onPress={() => {
                        if (sortBy === 'status') {
                          setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
                        } else {
                          setSortBy('status');
                          setSortOrder('asc');
                        }
                        fetchResources();
                      }}
                    >
                      <Text style={[styles.columnTitle, getTextStyle()]}>{translate('resource.status')}</Text>
                    </DataTable.Title>
                    <DataTable.Title style={[styles.tableHeaderCell, styles.actionsColumn, isRTL && styles.rtlCell]}>
                      <Text style={[styles.columnTitle, getTextStyle()]}>{translate('resource.actions')}</Text>
                    </DataTable.Title>
                  </DataTable.Header>

                  {resources.map((resource) => (
                    <DataTable.Row key={resource.id} style={styles.tableRow}>
                      <DataTable.Cell style={[styles.tableCell, isRTL && styles.rtlCell]}>
                        <Text style={getTextStyle()}>{resource.name}</Text>
                      </DataTable.Cell>
                      <DataTable.Cell style={[styles.tableCell, isRTL && styles.rtlCell]}>
                        <Text style={getTextStyle()}>{getTypeText(resource.type)}</Text>
                      </DataTable.Cell>
                      <DataTable.Cell style={[styles.tableCell, isRTL && styles.rtlCell]}>
                        <Text style={getTextStyle()}>{resource.category}</Text>
                      </DataTable.Cell>
                      <DataTable.Cell numeric style={[styles.tableCell, isRTL && styles.rtlCell]}>
                        <Text style={getTextStyle()}>{resource.quantity}</Text>
                      </DataTable.Cell>
                      <DataTable.Cell style={[styles.tableCell, isRTL && styles.rtlCell]}>
                        <Chip
                          style={[
                            styles.statusChip,
                            { backgroundColor: getStatusColor(resource.status) }
                          ]}
                        >
                          <Text style={[styles.chipText, getTextStyle()]}>{getStatusText(resource.status)}</Text>
                        </Chip>
                      </DataTable.Cell>
                      <DataTable.Cell style={[styles.tableCell, styles.actionsCell, isRTL && styles.rtlCell]}>
                        <View style={styles.actionButtons}>
                          <IconButton
                            icon="eye"
                            size={20}
                            color={'#1976d2'}
                            onPress={() => {
                              setSelectedResource(resource);
                              // View details implementation
                            }}
                          />
                          <IconButton
                            icon="pencil"
                            size={20}
                            color={'#1976d2'}
                            onPress={() => {
                              setSelectedResource(resource);
                              setResourceForm(resource);
                              setModalVisible(true);
                            }}
                          />
                          <IconButton
                            icon="delete"
                            size={20}
                            color={'#B00020'}
                            onPress={() => confirmDelete(resource)}
                          />
                        </View>
                      </DataTable.Cell>
                    </DataTable.Row>
                  ))}
                </DataTable>
              </Card>
            )}
          </Animatable.View>
        </ScrollView>
      </Animated.View>

      {/* Filter Menu */}
      <Portal>
        <Menu
          visible={filterMenuVisible}
          onDismiss={() => setFilterMenuVisible(false)}
          anchor={{ x: 0, y: 0 }}
          style={styles.filterMenu}
          contentStyle={styles.filterMenuContent}
        >
          <View style={styles.filterMenuHeader}>
            <Text style={styles.filterMenuTitle}>{translate('resource.filters')}</Text>
            <IconButton
              icon="close"
              size={20}
              onPress={() => setFilterMenuVisible(false)}
            />
          </View>

          <Divider />

          <View style={styles.filterSection}>
            <Text style={styles.filterSectionTitle}>{translate('resource.type')}</Text>
            <View style={styles.filterOptions}>
              <Chip
                selected={filterType === 'book'}
                onPress={() => setFilterType(filterType === 'book' ? '' : 'book')}
                style={styles.filterOptionChip}
              >
                {translate('resource.type.book')}
              </Chip>
              <Chip
                selected={filterType === 'equipment'}
                onPress={() => setFilterType(filterType === 'equipment' ? '' : 'equipment')}
                style={styles.filterOptionChip}
              >
                {translate('resource.type.equipment')}
              </Chip>
              <Chip
                selected={filterType === 'software'}
                onPress={() => setFilterType(filterType === 'software' ? '' : 'software')}
                style={styles.filterOptionChip}
              >
                {translate('resource.type.software')}
              </Chip>
              <Chip
                selected={filterType === 'other'}
                onPress={() => setFilterType(filterType === 'other' ? '' : 'other')}
                style={styles.filterOptionChip}
              >
                {translate('resource.type.other')}
              </Chip>
            </View>
          </View>

          <Divider />

          <View style={styles.filterSection}>
            <Text style={styles.filterSectionTitle}>{translate('resource.status')}</Text>
            <View style={styles.filterOptions}>
              <Chip
                selected={filterStatus === 'available'}
                onPress={() => setFilterStatus(filterStatus === 'available' ? '' : 'available')}
                style={styles.filterOptionChip}
              >
                {translate('resource.status.available')}
              </Chip>
              <Chip
                selected={filterStatus === 'in-use'}
                onPress={() => setFilterStatus(filterStatus === 'in-use' ? '' : 'in-use')}
                style={styles.filterOptionChip}
              >
                {translate('resource.status.inUse')}
              </Chip>
              <Chip
                selected={filterStatus === 'maintenance'}
                onPress={() => setFilterStatus(filterStatus === 'maintenance' ? '' : 'maintenance')}
                style={styles.filterOptionChip}
              >
                {translate('resource.status.maintenance')}
              </Chip>
              <Chip
                selected={filterStatus === 'retired'}
                onPress={() => setFilterStatus(filterStatus === 'retired' ? '' : 'retired')}
                style={styles.filterOptionChip}
              >
                {translate('resource.status.retired')}
              </Chip>
            </View>
          </View>

          <Divider />

          <View style={styles.filterActions}>
            <Button
              mode="outlined"
              onPress={resetFilters}
              style={styles.filterActionButton}
            >
              {translate('resource.clearFilters')}
            </Button>
            <Button
              mode="contained"
              onPress={applyFilters}
              style={styles.filterActionButton}
            >
              {translate('resource.applyFilters')}
            </Button>
          </View>
        </Menu>
      </Portal>

      {/* Confirm Delete Dialog */}
      <Portal>
        <Modal
          visible={confirmDeleteVisible}
          onDismiss={() => setConfirmDeleteVisible(false)}
          contentContainerStyle={styles.confirmDialog}
        >
          <View>
            <Title style={[styles.confirmTitle, getTextStyle()]}>{translate('resource.confirmDelete')}</Title>
            <Text style={[styles.confirmText, getTextStyle()]}>
              {translate('resource.confirmDeleteMessage', { name: resourceToDelete?.name })}
            </Text>
            <View style={styles.confirmActions}>
              <Button
                mode="outlined"
                onPress={() => setConfirmDeleteVisible(false)}
                style={styles.confirmButton}
              >
                {translate('ui.buttons.cancel')}
              </Button>
              <Button
                mode="contained"
                onPress={handleDeleteResource}
                style={[styles.confirmButton, styles.deleteButton]}
                loading={loading}
              >
                {translate('ui.buttons.delete')}
              </Button>
            </View>
          </View>
        </Modal>
      </Portal>

      {/* Add/Edit Resource Modal */}
      <Portal>
        <Modal
          visible={modalVisible}
          onDismiss={() => {
            setModalVisible(false);
            setSelectedResource(null);
            resetForm();
          }}
          contentContainerStyle={styles.modalContent}
        >
          <ScrollView>
            <Animatable.View animation="fadeIn" duration={300}>
              <View style={styles.modalHeader}>
                <Title style={[styles.modalTitle, getTextStyle()]}>
                  {selectedResource
                    ? translate('resource.editResource')
                    : translate('resource.addResource')}
                </Title>
                <IconButton
                  icon="close"
                  size={24}
                  onPress={() => {
                    setModalVisible(false);
                    setSelectedResource(null);
                    resetForm();
                  }}
                />
              </View>

              <Divider style={styles.modalDivider} />

              <View style={styles.formSection}>
                <Text style={styles.formSectionTitle}>{translate('resource.basicInfo')}</Text>

                <TextInput
                  label={translate('resource.name')}
                  value={resourceForm.name}
                  onChangeText={(text) => setResourceForm({ ...resourceForm, name: text })}
                  style={[styles.input, isRTL && styles.rtlInput]}
                  error={!!formErrors.name}
                  right={<TextInput.Icon icon="package-variant" />}
                />
                {formErrors.name && (
                  <Text style={styles.errorText}>{formErrors.name}</Text>
                )}

                <View style={styles.pickerContainer}>
                  <Text style={[styles.pickerLabel, getTextStyle()]}>{translate('resource.type')}</Text>
                  <View style={[styles.pickerWrapper, !!formErrors.type && styles.pickerError]}>
                    <Picker
                      selectedValue={resourceForm.type}
                      onValueChange={(value) => setResourceForm({ ...resourceForm, type: value })}
                      style={[styles.picker, isRTL && styles.rtlPicker]}
                    >
                      <Picker.Item label={translate('resource.selectType')} value="" />
                      <Picker.Item label={translate('resource.type.book')} value="book" />
                      <Picker.Item label={translate('resource.type.equipment')} value="equipment" />
                      <Picker.Item label={translate('resource.type.software')} value="software" />
                      <Picker.Item label={translate('resource.type.other')} value="other" />
                    </Picker>
                  </View>
                  {formErrors.type && (
                    <Text style={styles.errorText}>{formErrors.type}</Text>
                  )}
                </View>

                <View style={styles.pickerContainer}>
                  <Text style={[styles.pickerLabel, getTextStyle()]}>{translate('resource.category')}</Text>
                  <View style={[styles.pickerWrapper, !!formErrors.category && styles.pickerError]}>
                    <Picker
                      selectedValue={resourceForm.category}
                      onValueChange={(value) => setResourceForm({ ...resourceForm, category: value })}
                      style={[styles.picker, isRTL && styles.rtlPicker]}
                    >
                      <Picker.Item label={translate('resource.selectCategory')} value="" />
                      {Object.entries(resourceCategories).map(([group, categories]) => (
                        categories.map((category) => (
                          <Picker.Item
                            key={category}
                            label={`${group.charAt(0).toUpperCase() + group.slice(1)} - ${category}`}
                            value={category}
                          />
                        ))
                      ))}
                    </Picker>
                  </View>
                  {formErrors.category && (
                    <Text style={styles.errorText}>{formErrors.category}</Text>
                  )}
                </View>
              </View>

              <Divider style={styles.sectionDivider} />

              <View style={styles.formSection}>
                <Text style={styles.formSectionTitle}>{translate('resource.details')}</Text>

                <TextInput
                  label={translate('resource.quantity')}
                  value={resourceForm.quantity}
                  onChangeText={(text) => setResourceForm({ ...resourceForm, quantity: text })}
                  keyboardType="numeric"
                  style={[styles.input, isRTL && styles.rtlInput]}
                  error={!!formErrors.quantity}
                  right={<TextInput.Icon icon="numeric" />}
                />
                {formErrors.quantity && (
                  <Text style={styles.errorText}>{formErrors.quantity}</Text>
                )}

                <View style={styles.pickerContainer}>
                  <Text style={[styles.pickerLabel, getTextStyle()]}>{translate('resource.condition')}</Text>
                  <Picker
                    selectedValue={resourceForm.condition}
                    onValueChange={(value) => setResourceForm({ ...resourceForm, condition: value })}
                    style={[styles.picker, isRTL && styles.rtlPicker]}
                  >
                    <Picker.Item label={translate('resource.condition.new')} value="new" />
                    <Picker.Item label={translate('resource.condition.good')} value="good" />
                    <Picker.Item label={translate('resource.condition.fair')} value="fair" />
                    <Picker.Item label={translate('resource.condition.poor')} value="poor" />
                  </Picker>
                </View>

                <TextInput
                  label={translate('resource.location')}
                  value={resourceForm.location}
                  onChangeText={(text) => setResourceForm({ ...resourceForm, location: text })}
                  style={[styles.input, isRTL && styles.rtlInput]}
                  right={<TextInput.Icon icon="map-marker" />}
                />

                <TextInput
                  label={translate('resource.description')}
                  value={resourceForm.description}
                  onChangeText={(text) => setResourceForm({ ...resourceForm, description: text })}
                  multiline
                  numberOfLines={3}
                  style={[styles.input, styles.multilineInput, isRTL && styles.rtlInput]}
                />
              </View>

              <Divider style={styles.sectionDivider} />

              <View style={styles.formSection}>
                <Text style={styles.formSectionTitle}>{translate('resource.status')}</Text>

                <View style={styles.pickerContainer}>
                  <Text style={[styles.pickerLabel, getTextStyle()]}>{translate('resource.currentStatus')}</Text>
                  <Picker
                    selectedValue={resourceForm.status}
                    onValueChange={(value) => setResourceForm({ ...resourceForm, status: value })}
                    style={[styles.picker, isRTL && styles.rtlPicker]}
                  >
                    <Picker.Item label={translate('resource.status.available')} value="available" />
                    <Picker.Item label={translate('resource.status.inUse')} value="in-use" />
                    <Picker.Item label={translate('resource.status.maintenance')} value="maintenance" />
                    <Picker.Item label={translate('resource.status.retired')} value="retired" />
                  </Picker>
                </View>
              </View>

              <Divider style={styles.sectionDivider} />

              <View style={styles.formSection}>
                <Text style={styles.formSectionTitle}>{translate('resource.attachments')}</Text>

                <CustomButton
                  mode="outlined"
                  onPress={handleFileSelect}
                  style={styles.attachButton}
                  icon="paperclip"
                  labelStyle={getTextStyle()}
                >
                  {translate('resource.addAttachment')}
                </CustomButton>

                <View style={styles.attachmentsList}>
                  {resourceForm.attachments.length === 0 ? (
                    <Text style={styles.noAttachmentsText}>{translate('resource.noAttachments')}</Text>
                  ) : (
                    resourceForm.attachments.map((file, index) => (
                      <Chip
                        key={index}
                        onClose={() => {
                          const newAttachments = [...resourceForm.attachments];
                          newAttachments.splice(index, 1);
                          setResourceForm({ ...resourceForm, attachments: newAttachments });
                        }}
                        style={styles.attachmentChip}
                        icon={typeof file === 'string' ? "file-document" : "file-upload"}
                      >
                        {typeof file === 'string' ? translate('resource.existingFile') : file.name}
                      </Chip>
                    ))
                  )}
                </View>
              </View>

              <View style={[styles.buttonContainer, isRTL && styles.rtlButtonContainer]}>
                <CustomButton
                  mode="outlined"
                  onPress={() => {
                    setModalVisible(false);
                    setSelectedResource(null);
                    resetForm();
                  }}
                  style={[styles.button, styles.cancelButton]}
                  icon="close"
                  labelStyle={getTextStyle()}
                >
                  {translate('ui.buttons.cancel')}
                </CustomButton>
                <CustomButton
                  mode="contained"
                  onPress={selectedResource ? handleUpdateResource : handleAddResource}
                  style={[styles.button, styles.submitButton]}
                  loading={loading}
                  icon={selectedResource ? "content-save" : "plus"}
                  labelStyle={getTextStyle()}
                >
                  {selectedResource
                    ? translate('ui.buttons.update')
                    : translate('ui.buttons.add')}
                </CustomButton>
              </View>
            </Animatable.View>
          </ScrollView>
        </Modal>
      </Portal>

      {/* Snackbar for notifications */}
      <Snackbar
        visible={snackbarVisible}
        onDismiss={() => setSnackbarVisible(false)}
        duration={3000}
        style={styles.snackbar}
        action={{
          label: 'OK',
          onPress: () => setSnackbarVisible(false),
        }}
      >
        {snackbarMessage}
      </Snackbar>
    </View>
  );
};

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  contentContainer: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  rtlContainer: {
    flexDirection: 'row-reverse',
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 32,
  },
  searchContainer: {
    padding: 16,
    backgroundColor: '#fff',
    borderRadius: 8,
    marginBottom: 16,
  },
  searchRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  searchBar: {
    flex: 1,
    elevation: 0,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
  },
  searchBarRTL: {
    textAlign: 'right',
  },
  filterButton: {
    marginLeft: 8,
  },
  activeFiltersContainer: {
    marginTop: 8,
  },
  activeFiltersLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  filterChipsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
  },
  filterChip: {
    marginRight: 8,
    marginBottom: 4,
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  addButton: {
    borderRadius: 8,
  },
  loaderContainer: {
    padding: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loaderText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  emptyCard: {
    padding: 16,
    borderRadius: 8,
  },
  emptyCardContent: {
    alignItems: 'center',
    padding: 32,
  },
  emptyText: {
    fontSize: 18,
    color: '#666',
    marginTop: 16,
    marginBottom: 24,
  },
  emptyAddButton: {
    marginTop: 8,
  },
  dataCard: {
    borderRadius: 8,
    overflow: 'hidden',
  },
  tableHeader: {
    backgroundColor: '#f0f0f0',
  },
  tableHeaderCell: {
    paddingVertical: 8,
  },
  columnTitle: {
    fontWeight: 'bold',
  },
  tableRow: {
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  tableCell: {
    paddingVertical: 8,
  },
  rtlCell: {
    justifyContent: 'flex-end',
  },
  actionsColumn: {
    flex: 1.5,
  },
  actionsCell: {
    justifyContent: 'center',
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
  },
  statusChip: {
    alignSelf: 'flex-start',
    height: 28,
  },
  chipText: {
    color: 'white',
    fontWeight: 'bold',
  },

  // Filter Menu Styles
  filterMenu: {
    width: '80%',
    maxWidth: 400,
    marginTop: 60,
  },
  filterMenuContent: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 0,
  },
  filterMenuHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  filterMenuTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  filterSection: {
    padding: 16,
  },
  filterSectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  filterOptions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  filterOptionChip: {
    margin: 4,
  },
  filterActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    padding: 16,
    gap: 8,
  },
  filterActionButton: {
    minWidth: 100,
  },

  // Confirm Dialog Styles
  confirmDialog: {
    backgroundColor: 'white',
    padding: 24,
    margin: 24,
    borderRadius: 8,
  },
  confirmTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  confirmText: {
    fontSize: 16,
    marginBottom: 24,
  },
  confirmActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: 8,
  },
  confirmButton: {
    minWidth: 100,
  },
  deleteButton: {
    backgroundColor: '#f44336',
  },

  // Modal Styles
  modalContent: {
    backgroundColor: 'white',
    padding: 24,
    margin: 16,
    borderRadius: 12,
    maxHeight: '90%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  modalTitle: {
    fontSize: 22,
    fontWeight: 'bold',
  },
  modalDivider: {
    marginVertical: 16,
  },
  formSection: {
    marginBottom: 16,
  },
  formSectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
    color: '#2196F3',
  },
  sectionDivider: {
    marginVertical: 16,
  },
  input: {
    marginBottom: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
  },
  rtlInput: {
    textAlign: 'right',
  },
  multilineInput: {
    minHeight: 100,
  },
  pickerContainer: {
    marginBottom: 16,
  },
  pickerLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  pickerWrapper: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 4,
    backgroundColor: '#f5f5f5',
  },
  pickerError: {
    borderColor: '#f44336',
  },
  picker: {
    backgroundColor: '#f5f5f5',
    borderRadius: 4,
  },
  rtlPicker: {
    textAlign: 'right',
  },
  errorText: {
    color: '#f44336',
    fontSize: 12,
    marginTop: -12,
    marginBottom: 12,
  },
  attachButton: {
    marginBottom: 16,
  },
  attachmentsList: {
    marginTop: 8,
  },
  attachmentChip: {
    marginBottom: 8,
  },
  noAttachmentsText: {
    color: '#666',
    fontStyle: 'italic',
    marginBottom: 8,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 24,
    gap: 12,
  },
  rtlButtonContainer: {
    flexDirection: 'row-reverse',
  },
  button: {
    minWidth: 120,
    borderRadius: 8,
  },
  cancelButton: {
    borderColor: '#666',
  },
  submitButton: {
    backgroundColor: '#2196F3',
  },
  snackbar: {
    bottom: 16,
  },
});

export default ResourceManagement;
