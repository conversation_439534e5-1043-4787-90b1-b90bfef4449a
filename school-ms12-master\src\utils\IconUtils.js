/**
 * Utility functions for working with icons in the app
 */

/**
 * Get the appropriate icon name for email/message status
 * @param {boolean} isUnread - Whether the message is unread
 * @returns {string} - Valid Material Community icon name
 */
export const getMessageIcon = (isUnread) => {
  return isUnread ? 'email-mark-as-unread' : 'email';
};

/**
 * Get the appropriate icon name for notification status
 * @param {string} type - The notification type
 * @param {boolean} isUnread - Whether the notification is unread
 * @returns {string} - Valid Material Community icon name
 */
export const getNotificationIcon = (type, isUnread = false) => {
  switch (type) {
    case 'academic':
      return 'school';
    case 'attendance':
      return 'calendar-check';
    case 'behavioral':
      return 'account-alert';
    case 'event':
      return 'calendar';
    case 'message':
      return isUnread ? 'message-badge' : 'message-text';
    case 'alert':
      return 'alert-circle';
    case 'info':
      return 'information';
    case 'success':
      return 'check-circle';
    case 'warning':
      return 'alert';
    case 'email':
      return isUnread ? 'email-mark-as-unread' : 'email';
    default:
      return isUnread ? 'bell-badge' : 'bell';
  }
};

/**
 * Get the appropriate icon name for activity type
 * @param {string} type - The activity type
 * @returns {string} - Valid Material Community icon name
 */
export const getActivityIcon = (type) => {
  switch (type) {
    case 'login':
      return 'login';
    case 'logout':
      return 'logout';
    case 'create':
      return 'plus-circle';
    case 'update':
      return 'pencil';
    case 'delete':
      return 'delete';
    case 'message':
      return 'message-text';
    case 'notification':
      return 'bell';
    case 'upload':
      return 'upload';
    case 'download':
      return 'download';
    case 'email':
      return 'email';
    default:
      return 'information';
  }
};
