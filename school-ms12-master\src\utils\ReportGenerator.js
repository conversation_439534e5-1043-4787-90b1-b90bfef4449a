import { db } from '../config/firebase';
import { collection, query, where, getDocs, orderBy } from 'firebase/firestore';
import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';
import EthiopianCalendar from './EthiopianCalendar';

class ReportGenerator {
  static async generateStudentReport(studentId, type = 'academic') {
    try {
      // Fetch student data
      const studentDoc = await db.collection('users').doc(studentId).get();
      const studentData = studentDoc.data();

      let reportData = {
        studentInfo: {
          name: studentData.displayName,
          rollNumber: studentData.rollNumber,
          class: studentData.className,
          section: studentData.section,
        },
        generatedAt: new Date().toISOString(),
      };

      switch (type) {
        case 'academic':
          reportData = await this.getAcademicData(studentId, reportData);
          break;
        case 'attendance':
          reportData = await this.getAttendanceData(studentId, reportData);
          break;
        case 'behavior':
          reportData = await this.getBehaviorData(studentId, reportData);
          break;
        case 'comprehensive':
          reportData = await this.getComprehensiveData(studentId, reportData);
          break;
      }

      return reportData;
    } catch (error) {
      console.error('Error generating student report:', error);
      throw error;
    }
  }

  static async generateClassReport(classId, type = 'academic') {
    try {
      // Fetch class data
      const classDoc = await db.collection('classes').doc(classId).get();
      const classData = classDoc.data();

      let reportData = {
        classInfo: {
          name: classData.name,
          section: classData.section,
          academicYear: classData.academicYear,
        },
        generatedAt: new Date().toISOString(),
      };

      switch (type) {
        case 'academic':
          reportData = await this.getClassAcademicData(classId, reportData);
          break;
        case 'attendance':
          reportData = await this.getClassAttendanceData(classId, reportData);
          break;
        case 'behavior':
          reportData = await this.getClassBehaviorData(classId, reportData);
          break;
        case 'comprehensive':
          reportData = await this.getClassComprehensiveData(classId, reportData);
          break;
      }

      return reportData;
    } catch (error) {
      console.error('Error generating class report:', error);
      throw error;
    }
  }

  static async getAcademicData(studentId, reportData) {
    const gradesRef = collection(db, 'grades');
    const q = query(
      gradesRef,
      where('studentId', '==', studentId),
      orderBy('date', 'desc')
    );
    const gradesSnapshot = await getDocs(q);

    const grades = [];
    gradesSnapshot.forEach(doc => {
      grades.push(doc.data());
    });

    // Calculate statistics
    const subjectAverages = {};
    grades.forEach(grade => {
      if (!subjectAverages[grade.subjectId]) {
        subjectAverages[grade.subjectId] = {
          total: 0,
          count: 0,
          name: grade.subjectName,
        };
      }
      subjectAverages[grade.subjectId].total += grade.score;
      subjectAverages[grade.subjectId].count++;
    });

    reportData.academic = {
      grades,
      subjectAverages: Object.entries(subjectAverages).map(([id, data]) => ({
        subjectId: id,
        subjectName: data.name,
        average: data.total / data.count,
      })),
      overallAverage: grades.reduce((acc, grade) => acc + grade.score, 0) / grades.length,
    };

    return reportData;
  }

  static async getAttendanceData(studentId, reportData) {
    const attendanceRef = collection(db, 'attendance');
    const q = query(
      attendanceRef,
      where('studentId', '==', studentId),
      orderBy('date', 'desc')
    );
    const attendanceSnapshot = await getDocs(q);

    const attendance = [];
    attendanceSnapshot.forEach(doc => {
      attendance.push(doc.data());
    });

    // Calculate statistics
    const totalDays = attendance.length;
    const presentDays = attendance.filter(a => a.status === 'present').length;
    const absentDays = attendance.filter(a => a.status === 'absent').length;
    const lateDays = attendance.filter(a => a.status === 'late').length;

    reportData.attendance = {
      records: attendance,
      statistics: {
        totalDays,
        presentDays,
        absentDays,
        lateDays,
        attendanceRate: (presentDays / totalDays) * 100,
      },
    };

    return reportData;
  }

  static async getBehaviorData(studentId, reportData) {
    const behaviorRef = collection(db, 'behavior');
    const q = query(
      behaviorRef,
      where('studentId', '==', studentId),
      orderBy('date', 'desc')
    );
    const behaviorSnapshot = await getDocs(q);

    const behavior = [];
    behaviorSnapshot.forEach(doc => {
      behavior.push(doc.data());
    });

    // Calculate statistics
    const categories = behavior.reduce((acc, record) => {
      if (!acc[record.category]) {
        acc[record.category] = 0;
      }
      acc[record.category]++;
      return acc;
    }, {});

    reportData.behavior = {
      records: behavior,
      categories,
      totalRecords: behavior.length,
    };

    return reportData;
  }

  static async getComprehensiveData(studentId, reportData) {
    reportData = await this.getAcademicData(studentId, reportData);
    reportData = await this.getAttendanceData(studentId, reportData);
    reportData = await this.getBehaviorData(studentId, reportData);
    return reportData;
  }

  static async getClassAcademicData(classId, reportData) {
    const studentsRef = collection(db, 'users');
    const studentsQuery = query(
      studentsRef,
      where('classId', '==', classId),
      where('role', '==', 'student')
    );
    const studentsSnapshot = await getDocs(studentsQuery);

    const studentGrades = [];
    for (const studentDoc of studentsSnapshot.docs) {
      const studentData = studentDoc.data();
      const grades = await this.getAcademicData(studentDoc.id, {}).then(data => data.academic);
      studentGrades.push({
        studentId: studentDoc.id,
        studentName: studentData.displayName,
        ...grades,
      });
    }

    // Calculate class statistics
    const subjectAverages = {};
    studentGrades.forEach(student => {
      student.subjectAverages.forEach(subject => {
        if (!subjectAverages[subject.subjectId]) {
          subjectAverages[subject.subjectId] = {
            total: 0,
            count: 0,
            name: subject.subjectName,
          };
        }
        subjectAverages[subject.subjectId].total += subject.average;
        subjectAverages[subject.subjectId].count++;
      });
    });

    reportData.academic = {
      studentGrades,
      classAverages: Object.entries(subjectAverages).map(([id, data]) => ({
        subjectId: id,
        subjectName: data.name,
        average: data.total / data.count,
      })),
      overallClassAverage: studentGrades.reduce((acc, student) => acc + student.overallAverage, 0) / studentGrades.length,
    };

    return reportData;
  }

  static async getClassAttendanceData(classId, reportData) {
    const studentsRef = collection(db, 'users');
    const studentsQuery = query(
      studentsRef,
      where('classId', '==', classId),
      where('role', '==', 'student')
    );
    const studentsSnapshot = await getDocs(studentsQuery);

    const studentAttendance = [];
    for (const studentDoc of studentsSnapshot.docs) {
      const studentData = studentDoc.data();
      const attendance = await this.getAttendanceData(studentDoc.id, {}).then(data => data.attendance);
      studentAttendance.push({
        studentId: studentDoc.id,
        studentName: studentData.displayName,
        ...attendance,
      });
    }

    // Calculate class statistics
    const totalStudents = studentAttendance.length;
    const averageAttendanceRate = studentAttendance.reduce((acc, student) => 
      acc + student.statistics.attendanceRate, 0) / totalStudents;

    reportData.attendance = {
      studentAttendance,
      classStatistics: {
        totalStudents,
        averageAttendanceRate,
      },
    };

    return reportData;
  }

  static async getClassBehaviorData(classId, reportData) {
    const studentsRef = collection(db, 'users');
    const studentsQuery = query(
      studentsRef,
      where('classId', '==', classId),
      where('role', '==', 'student')
    );
    const studentsSnapshot = await getDocs(studentsQuery);

    const studentBehavior = [];
    for (const studentDoc of studentsSnapshot.docs) {
      const studentData = studentDoc.data();
      const behavior = await this.getBehaviorData(studentDoc.id, {}).then(data => data.behavior);
      studentBehavior.push({
        studentId: studentDoc.id,
        studentName: studentData.displayName,
        ...behavior,
      });
    }

    // Calculate class statistics
    const categoryTotals = studentBehavior.reduce((acc, student) => {
      Object.entries(student.categories).forEach(([category, count]) => {
        if (!acc[category]) {
          acc[category] = 0;
        }
        acc[category] += count;
      });
      return acc;
    }, {});

    reportData.behavior = {
      studentBehavior,
      classCategories: categoryTotals,
      totalRecords: studentBehavior.reduce((acc, student) => acc + student.totalRecords, 0),
    };

    return reportData;
  }

  static async getClassComprehensiveData(classId, reportData) {
    reportData = await this.getClassAcademicData(classId, reportData);
    reportData = await this.getClassAttendanceData(classId, reportData);
    reportData = await this.getClassBehaviorData(classId, reportData);
    return reportData;
  }

  static async exportToCSV(data, filename) {
    try {
      let csvContent = '';

      // Add headers
      const headers = this.getCSVHeaders(data);
      csvContent += headers.join(',') + '\n';

      // Add data rows
      const rows = this.getCSVRows(data);
      csvContent += rows.join('\n');

      // Save file
      const path = `${FileSystem.documentDirectory}${filename}.csv`;
      await FileSystem.writeAsStringAsync(path, csvContent);

      // Share file
      if (await Sharing.isAvailableAsync()) {
        await Sharing.shareAsync(path);
      }

      return path;
    } catch (error) {
      console.error('Error exporting to CSV:', error);
      throw error;
    }
  }

  static getCSVHeaders(data) {
    const headers = ['Student Name', 'Class', 'Roll Number'];
    
    if (data.academic) {
      headers.push('Overall Average');
      data.academic.subjectAverages?.forEach(subject => {
        headers.push(subject.subjectName);
      });
    }
    
    if (data.attendance) {
      headers.push('Attendance Rate', 'Present Days', 'Absent Days', 'Late Days');
    }
    
    if (data.behavior) {
      headers.push('Behavior Records');
      Object.keys(data.behavior.categories || {}).forEach(category => {
        headers.push(category);
      });
    }

    return headers;
  }

  static getCSVRows(data) {
    const rows = [];
    const studentInfo = data.studentInfo;
    
    let row = [
      studentInfo.name,
      studentInfo.class,
      studentInfo.rollNumber,
    ];

    if (data.academic) {
      row.push(data.academic.overallAverage.toFixed(2));
      data.academic.subjectAverages?.forEach(subject => {
        row.push(subject.average.toFixed(2));
      });
    }

    if (data.attendance) {
      row.push(
        data.attendance.statistics.attendanceRate.toFixed(2),
        data.attendance.statistics.presentDays,
        data.attendance.statistics.absentDays,
        data.attendance.statistics.lateDays
      );
    }

    if (data.behavior) {
      row.push(data.behavior.totalRecords);
      Object.values(data.behavior.categories || {}).forEach(count => {
        row.push(count);
      });
    }

    rows.push(row.join(','));
    return rows;
  }

  static formatDate(date) {
    return EthiopianCalendar.formatDate(new Date(date));
  }
}

export default ReportGenerator;
