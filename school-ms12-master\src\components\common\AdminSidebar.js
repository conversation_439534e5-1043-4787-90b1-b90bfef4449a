import React, { useState, useEffect, useRef } from 'react';
import { View, ScrollView, StyleSheet, Text, TouchableOpacity, Animated, Dimensions, Platform } from 'react-native';
import { Title, Paragraph, IconButton, Divider, List, useTheme, Badge, Surface } from 'react-native-paper';
import CloudinaryAvatar from './CloudinaryAvatar';
import { LinearGradient } from 'expo-linear-gradient';
import { useNavigation } from '@react-navigation/native';
import { useLanguage } from '../../context/LanguageContext';
import { useAuth } from '../../context/AuthContext';
import * as Animatable from 'react-native-animatable';
import { MaterialCommunityIcons } from '@expo/vector-icons';

const AdminSidebar = ({
  visible,
  onClose,
  drawerAnim: externalDrawerAnim,
  toggleDrawer,
  activeSidebarItem,
  setActiveSidebarItem,
  navigation
}) => {
  // Create our own animation value if not provided externally
  const internalDrawerAnim = useRef(new Animated.Value(-280)).current;
  // Use the external drawerAnim if provided, otherwise use our internal one
  const drawerAnim = externalDrawerAnim || internalDrawerAnim;

  const theme = useTheme();
  const { translate, language, isRTL } = useLanguage();
  const { user, logout } = useAuth();
  const [expandedSections, setExpandedSections] = useState({});
  const [searchQuery, setSearchQuery] = useState('');
  const [hoveredItem, setHoveredItem] = useState(null);
  const screenWidth = Dimensions.get('window').width;
  const isTablet = screenWidth >= 768;
  const [isVisible, setIsVisible] = useState(visible || false);

  // Get user initials for avatar
  const getUserInitials = () => {
    if (!user || !user.displayName) return 'A';

    const nameParts = user.displayName.split(' ');
    if (nameParts.length >= 2) {
      return `${nameParts[0][0]}${nameParts[1][0]}`;
    }
    return nameParts[0][0];
  };

  // Handle drawer animation when visibility changes
  useEffect(() => {
    if (externalDrawerAnim) {
      // If using external animation, update our internal visibility state
      setIsVisible(visible);
    } else {
      // If using internal animation, animate based on visibility prop
      Animated.timing(internalDrawerAnim, {
        toValue: visible ? 0 : -280,
        duration: 300,
        useNativeDriver: true,
      }).start();
      setIsVisible(visible);
    }
  }, [visible, internalDrawerAnim, externalDrawerAnim]);

  // Toggle section expansion
  const toggleSection = (sectionIndex) => {
    setExpandedSections(prev => ({
      ...prev,
      [sectionIndex]: !prev[sectionIndex]
    }));
  };

  const adminMenuItems = [
    {
      category: translate('admin.dashboard.academicManagement'),
      items: [
        { title: translate('admin.navigation.academicCalendar'), icon: 'calendar', route: 'AcademicCalendar' },
        { title: translate('admin.navigation.semester'), icon: 'calendar-text', route: 'SemesterManagement' },
        { title: translate('admin.navigation.subjectManagement'), icon: 'book', route: 'SubjectManagement' },
      ]
    },
    {
      category: translate('admin.dashboard.userManagement'),
      items: [
        { title: translate('admin.navigation.students'), icon: 'account-multiple', route: 'StudentManagement' },
        { title: translate('admin.navigation.teachers'), icon: 'account-tie', route: 'TeacherManagement' },
        { title: translate('admin.navigation.parents'), icon: 'account-child', route: 'ParentManagement' },
        { title: translate('admin.navigation.userManagement'), icon: 'account-cog', route: 'UserManagement' },

      ]
    },
    {
      category: translate('admin.dashboard.classExam'),
      items: [
        { title: translate('admin.navigation.classManagement'), icon: 'school', route: 'ClassManagement' },
        { title: translate('admin.navigation.examSchedule'), icon: 'file-document-edit', route: 'ExamManagement' },
        { title: translate('admin.navigation.weeklyClassSchedule'), icon: 'calendar-check', route: 'ClassSchedule' },
        { title: translate('admin.navigation.results'), icon: 'chart-box', route: 'ResultsManagement' },
        { title: translate('admin.navigation.gradeApproval'), icon: 'clipboard-check', route: 'AdminGradeApproval' },
      ]
    },
    {
      category: translate('admin.dashboard.attendance'),
      items: [
        { title: translate('admin.dashboard.attendanceManagement'), icon: 'calendar-check', route: 'AttendanceManagement' },
        { title: translate('admin.dashboard.attendanceApproval'), icon: 'check-decagram', route: 'AdminAttendanceApproval' },
        { title: translate('admin.dashboard.attendanceReports'), icon: 'file-chart', route: 'AttendanceReports' },
      ]
    },
    {
      category: translate('admin.dashboard.teacherTools'),
      items: [
        { title: translate('admin.navigation.teacherSchedule'), icon: 'clock-outline', route: 'TeacherSchedule' },
        { title: translate('admin.navigation.teacherEvaluation'), icon: 'star-outline', route: 'TeacherEvaluation' },
        { title: translate('admin.navigation.teacherDocuments'), icon: 'file-document', route: 'TeacherDocuments' },
        { title: translate('admin.navigation.teacherAttendance'), icon: 'calendar-check', route: 'TeacherAttendance' },
        { title: translate('admin.navigation.teacherPermissions'), icon: 'shield-account', route: 'TeacherPermissions' },
      ]
    },
    {
      category: translate('admin.dashboard.resourcesLibrary'),
      items: [
        { title: translate('admin.navigation.libraryManagement'), icon: 'library', route: 'LibraryManagement' },
        { title: translate('admin.navigation.librarySettings'), icon: 'cog', route: 'LibrarySettings' },
        { title: translate('admin.navigation.resourceManagement'), icon: 'folder', route: 'ResourceManagement' },
      ]
    },
    {
      category: translate('admin.dashboard.communication'),
      items: [
        { title: translate('admin.navigation.messageCenter'), icon: 'message-text', route: 'CommunicationCenter' },
        { title: translate('admin.navigation.announcements'), icon: 'bullhorn', route: 'AnnouncementManagement' },
        { title: translate('admin.navigation.notifications'), icon: 'bell', route: 'NotificationSystem' },
      ]
    },
    {
      category: translate('admin.dashboard.systemReports'),
      items: [
        { title: translate('admin.navigation.schoolSettings'), icon: 'cog', route: 'SchoolSettings' },
        { title: translate('admin.navigation.timeSettings'), icon: 'clock', route: 'TimeSettings' },
        { title: translate('admin.navigation.systemReports'), icon: 'file-chart', route: 'SystemReports' },
      ]
    },
  ];

  const handleSidebarItemPress = (item) => {
    if (item.key === 'dashboard') {
      setActiveSidebarItem('dashboard');
      navigation.navigate('AdminDashboard');
    } else {
      setActiveSidebarItem(item.key);
      if (item.route) {
        navigation.navigate(item.route);
      }
    }
    // Close drawer on mobile
    if (Dimensions.get('window').width < 768) {
      // Use the appropriate close method based on what was provided
      if (toggleDrawer) {
        toggleDrawer();
      } else if (onClose) {
        onClose();
      }
    }
  };

  const handleLogout = async () => {
    try {
      await logout();
      navigation.reset({
        index: 0,
        routes: [{ name: 'Login' }],
      });
    } catch (error) {
      console.error('Error logging out:', error);
    }
  };

  return (
    <Animated.View
      style={[
        styles.sidebar,
        { transform: [{ translateX: drawerAnim }] },
        isRTL && styles.sidebarRTL
      ]}
    >
      <LinearGradient
        colors={['#1976d2', '#1565C0']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.sidebarHeader}
      >
        <View style={styles.closeButtonContainer}>
          <IconButton
            icon="close"
            color="white"
            size={24}
            onPress={onClose}
            style={styles.closeButton}
          />
        </View>

        <Animatable.View
          animation="fadeIn"
          duration={800}
          style={styles.sidebarProfile}
        >
          <CloudinaryAvatar
            source={user?.photoURL}
            label={getUserInitials()}
            size={80}
            style={styles.sidebarAvatar}
            color="white"
            labelStyle={styles.avatarLabel}
            backgroundColor="#1976d2"
          />
          <Animatable.View animation="fadeInUp" delay={300} duration={800}>
            <Title style={styles.sidebarName}>{user?.displayName || translate('administrative.roles.admin')}</Title>
            <Paragraph style={styles.sidebarRole}>{translate('administrative.roles.admin')}</Paragraph>
          </Animatable.View>
        </Animatable.View>
      </LinearGradient>

      <ScrollView
        style={styles.sidebarMenu}
        showsVerticalScrollIndicator={false}
      >
        <Animatable.View animation="fadeInUp" duration={500} delay={100}>
          <TouchableOpacity
            style={[styles.dashboardItem, activeSidebarItem === 'dashboard' && styles.sidebarItemActive]}
            onPress={() => handleSidebarItemPress({ key: 'dashboard' })}
          >
            <Surface style={[styles.dashboardSurface, activeSidebarItem === 'dashboard' && styles.activeDashboardSurface]}>
              <MaterialCommunityIcons
                name="view-dashboard"
                color={activeSidebarItem === 'dashboard' ? 'white' : '#2196F3'}
                size={24}
              />
              <Text style={[
                styles.dashboardText,
                activeSidebarItem === 'dashboard' && styles.activeDashboardText
              ]}>
                {translate('admin.navigation.dashboard')}
              </Text>
            </Surface>
          </TouchableOpacity>
        </Animatable.View>

        <List.Section>
          {/* Dynamically generate sidebar items from adminMenuItems */}
          {adminMenuItems.map((section, sectionIndex) => (
            <Animatable.View
              key={`section-${sectionIndex}`}
              animation="fadeInUp"
              duration={500}
              delay={150 + (sectionIndex * 50)}
            >
              <TouchableOpacity
                style={styles.sectionHeader}
                onPress={() => toggleSection(sectionIndex)}
              >
                <View style={styles.sectionHeaderContent}>
                  <Text style={styles.sectionHeaderText}>
                    {section.category}
                  </Text>
                  <IconButton
                    icon={expandedSections[sectionIndex] ? "chevron-up" : "chevron-down"}
                    color="#616161"
                    size={20}
                    style={styles.sectionToggle}
                  />
                </View>
              </TouchableOpacity>

              {(expandedSections[sectionIndex] === undefined || expandedSections[sectionIndex]) && (
                <Animatable.View
                  animation={expandedSections[sectionIndex] ? "fadeInDown" : undefined}
                  duration={300}
                >
                  {section.items.map((item, itemIndex) => (
                    <TouchableOpacity
                      key={`item-${sectionIndex}-${itemIndex}`}
                      style={[
                        styles.sidebarItem,
                        activeSidebarItem === item.route && styles.sidebarItemActive,
                        hoveredItem === `${sectionIndex}-${itemIndex}` && styles.sidebarItemHovered
                      ]}
                      onPress={() => handleSidebarItemPress({ key: item.route, route: item.route })}
                      onMouseEnter={() => setHoveredItem(`${sectionIndex}-${itemIndex}`)}
                      onMouseLeave={() => setHoveredItem(null)}
                    >
                      <View style={styles.sidebarItemContent}>
                        <MaterialCommunityIcons
                          name={item.icon}
                          color={activeSidebarItem === item.route ? '#2196F3' : '#757575'}
                          size={22}
                          style={styles.itemIcon}
                        />
                        <Text style={[
                          styles.sidebarItemText,
                          activeSidebarItem === item.route && styles.sidebarItemTextActive
                        ]}>
                          {item.title}
                        </Text>
                      </View>
                    </TouchableOpacity>
                  ))}
                </Animatable.View>
              )}
            </Animatable.View>
          ))}
        </List.Section>

        <Divider style={styles.sidebarDivider} />

        <Animatable.View animation="fadeInUp" duration={500} delay={600}>
          <TouchableOpacity
            style={[
              styles.sidebarItem,
              activeSidebarItem === 'AdminProfile' && styles.sidebarItemActive
            ]}
            onPress={() => navigation.navigate('AdminProfile')}
          >
            <View style={styles.sidebarItemContent}>
              <MaterialCommunityIcons
                name="account"
                color={activeSidebarItem === 'AdminProfile' ? '#2196F3' : '#757575'}
                size={22}
                style={styles.itemIcon}
              />
              <Text style={[
                styles.sidebarItemText,
                activeSidebarItem === 'AdminProfile' && styles.sidebarItemTextActive
              ]}>
                {translate('common.profile')}
              </Text>
            </View>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.sidebarItem,
              activeSidebarItem === 'SystemSettings' && styles.sidebarItemActive
            ]}
            onPress={() => navigation.navigate('SystemSettings')}
          >
            <View style={styles.sidebarItemContent}>
              <MaterialCommunityIcons
                name="cog"
                color={activeSidebarItem === 'SystemSettings' ? '#2196F3' : '#757575'}
                size={22}
                style={styles.itemIcon}
              />
              <Text style={[
                styles.sidebarItemText,
                activeSidebarItem === 'SystemSettings' && styles.sidebarItemTextActive
              ]}>
                {translate('common.settings')}
              </Text>
            </View>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.logoutItem}
            onPress={handleLogout}
          >
            <View style={styles.sidebarItemContent}>
              <MaterialCommunityIcons
                name="logout"
                color="#F44336"
                size={22}
                style={styles.itemIcon}
              />
              <Text style={styles.logoutText}>
                {translate('common.logout')}
              </Text>
            </View>
          </TouchableOpacity>
        </Animatable.View>

        <View style={styles.sidebarFooter}>
          <Text style={styles.versionText}>v1.0.0</Text>
        </View>
      </ScrollView>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  sidebar: {
    position: 'absolute',
    left: 0,
    top: 0,
    bottom: 0,
    width: 280,
    backgroundColor: '#fff',
    zIndex: 1000,
    elevation: 10,
    shadowColor: '#000',
    shadowOffset: { width: 2, height: 0 },
    shadowOpacity: 0.3,
    shadowRadius: 5,
  },
  sidebarRTL: {
    left: undefined,
    right: 0,
    shadowOffset: { width: -2, height: 0 },
  },
  closeButtonContainer: {
    position: 'absolute',
    top: Platform.OS === 'ios' ? 40 : 10,
    right: 10,
    zIndex: 10,
  },
  closeButton: {
    margin: 0,
  },
  sidebarHeader: {
    padding: 16,
    paddingTop: Platform.OS === 'ios' ? 60 : 48,
  },
  sidebarProfile: {
    alignItems: 'center',
    marginBottom: 16,
    marginTop: 8,
  },
  sidebarAvatar: {
    marginBottom: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.5)',
  },
  avatarLabel: {
    fontWeight: 'bold',
    fontSize: 28,
  },
  sidebarName: {
    color: 'white',
    fontSize: 18,
    textAlign: 'center',
    fontWeight: 'bold',
  },
  sidebarRole: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 14,
    textAlign: 'center',
    marginTop: 4,
  },
  sidebarMenu: {
    flex: 1,
    paddingTop: 8,
  },
  dashboardItem: {
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  dashboardSurface: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    backgroundColor: '#e3f2fd',
    elevation: 1,
  },
  activeDashboardSurface: {
    backgroundColor: '#2196F3',
  },
  dashboardText: {
    fontSize: 15,
    fontWeight: 'bold',
    marginLeft: 12,
    color: '#2196F3',
  },
  activeDashboardText: {
    color: 'white',
  },
  sectionHeader: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    marginBottom: 4,
  },
  sectionHeaderContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  sectionHeaderText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#616161',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  sectionToggle: {
    margin: 0,
    padding: 0,
  },
  sidebarItem: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    marginVertical: 1,
    borderRadius: 4,
    marginHorizontal: 8,
  },
  sidebarItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  itemIcon: {
    marginRight: 12,
    width: 22,
  },
  sidebarItemActive: {
    backgroundColor: 'rgba(33, 150, 243, 0.1)',
    borderLeftWidth: 4,
    borderLeftColor: '#2196F3',
  },
  sidebarItemHovered: {
    backgroundColor: '#f5f5f5',
  },
  sidebarItemText: {
    fontSize: 14,
    color: '#424242',
  },
  sidebarItemTextActive: {
    color: '#2196F3',
    fontWeight: 'bold',
  },
  sidebarDivider: {
    marginVertical: 16,
    marginHorizontal: 16,
  },
  logoutItem: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    marginVertical: 1,
    borderRadius: 4,
    marginHorizontal: 8,
  },
  logoutText: {
    fontSize: 14,
    color: '#F44336',
    fontWeight: '500',
  },
  sidebarFooter: {
    padding: 16,
    alignItems: 'center',
    marginTop: 16,
    marginBottom: 8,
  },
  versionText: {
    fontSize: 12,
    color: '#9e9e9e',
  },
});

export default AdminSidebar;
