import React, { useState, useEffect, useRef } from 'react';
import { View, StyleSheet, SafeAreaView, StatusBar, Dimensions, Platform, ScrollView, RefreshControl, TouchableOpacity } from 'react-native';
import { Card, Title, Text, Avatar, Searchbar, Divider, List, Chip, ActivityIndicator, useTheme, IconButton, Badge, FAB } from 'react-native-paper';
import { db } from '../../config/firebase';
import { collection, query, where, getDocs } from 'firebase/firestore';
import { useAuth } from '../../context/AuthContext';
import { useLanguage } from '../../context/LanguageContext';
import TeacherAppHeader from '../../components/common/TeacherAppHeader';
import TeacherSidebar from '../../components/common/TeacherSidebar';
import SidebarBackdrop from '../../components/common/SidebarBackdrop';
import * as Animatable from 'react-native-animatable';
import { LinearGradient } from 'expo-linear-gradient';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import { Animated } from 'react-native';

const TeacherDirectory = ({ navigation }) => {
  const theme = useTheme();
  const { user } = useAuth();
  const { translate, getTextStyle, language } = useLanguage();
  
  // Animation refs
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;
  const drawerAnim = useRef(new Animated.Value(-300)).current;
  const backdropFadeAnim = useRef(new Animated.Value(0)).current;
  
  // State variables
  const [teachers, setTeachers] = useState([]);
  const [filteredTeachers, setFilteredTeachers] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [activeSidebarItem, setActiveSidebarItem] = useState('teacherDirectory');
  const [selectedTeacher, setSelectedTeacher] = useState(null);
  const [selectedDepartment, setSelectedDepartment] = useState('all');
  const [departments, setDepartments] = useState([]);
  
  // Fetch teachers on component mount
  useEffect(() => {
    fetchTeachers();
    
    // Start animations
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 500,
        useNativeDriver: true,
      }),
      Animated.spring(slideAnim, {
        toValue: 0,
        speed: 20,
        bounciness: 5,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);
  
  // Filter teachers when search query or department changes
  useEffect(() => {
    filterTeachers();
  }, [searchQuery, selectedDepartment, teachers]);
  
  // Fetch teachers from Firestore
  const fetchTeachers = async () => {
    try {
      setLoading(true);
      const teachersRef = collection(db, 'users');
      const q = query(teachersRef, where('role', '==', 'teacher'));
      const querySnapshot = await getDocs(q);
      
      const teachersData = [];
      const departmentsSet = new Set(['all']);
      
      querySnapshot.forEach((doc) => {
        const data = doc.data();
        const department = data.department || 'Unassigned';
        departmentsSet.add(department);
        
        teachersData.push({
          id: doc.id,
          ...data,
          fullName: `${data.firstName || ''} ${data.lastName || ''}`.trim() || data.email,
          department: department,
          subjects: data.subjects || [],
          qualification: data.qualification || '',
          experience: data.experience || '',
          phone: data.phone || '',
          email: data.email || '',
          photoURL: data.photoURL || '',
        });
      });
      
      setTeachers(teachersData);
      setDepartments(Array.from(departmentsSet));
      setLoading(false);
    } catch (error) {
      console.error('Error fetching teachers:', error);
      setLoading(false);
    }
  };
  
  // Filter teachers based on search query and selected department
  const filterTeachers = () => {
    let filtered = teachers;
    
    // Filter by department
    if (selectedDepartment !== 'all') {
      filtered = filtered.filter(teacher => teacher.department === selectedDepartment);
    }
    
    // Filter by search query
    if (searchQuery) {
      filtered = filtered.filter(teacher => 
        teacher.fullName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        teacher.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (teacher.subjects && teacher.subjects.some(subject => 
          subject.toLowerCase().includes(searchQuery.toLowerCase())
        ))
      );
    }
    
    setFilteredTeachers(filtered);
  };
  
  // Handle refresh
  const onRefresh = async () => {
    setRefreshing(true);
    await fetchTeachers();
    setRefreshing(false);
  };
  
  // Toggle drawer
  const toggleDrawer = () => {
    if (drawerOpen) {
      Animated.parallel([
        Animated.spring(drawerAnim, {
          toValue: -300,
          tension: 80,
          friction: 10,
          useNativeDriver: true,
        }),
        Animated.timing(backdropFadeAnim, {
          toValue: 0,
          duration: 250,
          useNativeDriver: true,
        })
      ]).start(() => setDrawerOpen(false));
    } else {
      setDrawerOpen(true);
      Animated.parallel([
        Animated.spring(drawerAnim, {
          toValue: 0,
          tension: 50,
          friction: 10,
          useNativeDriver: true,
        }),
        Animated.timing(backdropFadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        })
      ]).start();
      
      // Add haptic feedback if available
      if (Platform.OS === 'ios' && 'impactAsync' in Haptics) {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      }
    }
  };
  
  // Render teacher card
  const renderTeacherCard = (teacher) => {
    return (
      <Animatable.View 
        animation="fadeInUp" 
        duration={500} 
        delay={teachers.indexOf(teacher) * 100}
        key={teacher.id}
      >
        <Card 
          style={styles.teacherCard}
          onPress={() => {
            setSelectedTeacher(teacher);
            // Add haptic feedback
            if (Platform.OS === 'ios' && 'impactAsync' in Haptics) {
              Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
            }
          }}
        >
          <Card.Content style={styles.cardContent}>
            <View style={styles.teacherHeader}>
              <Avatar.Image 
                source={teacher.photoURL ? { uri: teacher.photoURL } : require('../../assets/default-avatar.png')} 
                size={60} 
                style={styles.avatar}
              />
              <View style={styles.teacherInfo}>
                <Title style={[styles.teacherName, getTextStyle()]}>{teacher.fullName}</Title>
                <Chip style={styles.departmentChip}>{teacher.department}</Chip>
                <Text style={styles.teacherEmail}>{teacher.email}</Text>
              </View>
            </View>
            
            <Divider style={styles.divider} />
            
            <View style={styles.subjectsContainer}>
              <Text style={[styles.sectionTitle, getTextStyle()]}>{translate('teacher.directory.subjects') || 'Subjects'}</Text>
              <View style={styles.subjectChips}>
                {teacher.subjects && teacher.subjects.length > 0 ? (
                  teacher.subjects.map((subject, index) => (
                    <Chip key={index} style={styles.subjectChip} textStyle={styles.subjectChipText}>
                      {subject}
                    </Chip>
                  ))
                ) : (
                  <Text style={styles.noSubjects}>{translate('teacher.directory.noSubjects') || 'No subjects assigned'}</Text>
                )}
              </View>
            </View>
            
            {teacher.qualification && (
              <View style={styles.infoRow}>
                <MaterialCommunityIcons name="school" size={18} color={theme.colors.primary} />
                <Text style={styles.infoText}>{teacher.qualification}</Text>
              </View>
            )}
            
            {teacher.experience && (
              <View style={styles.infoRow}>
                <MaterialCommunityIcons name="briefcase" size={18} color={theme.colors.primary} />
                <Text style={styles.infoText}>{teacher.experience} {translate('teacher.directory.yearsExperience') || 'years experience'}</Text>
              </View>
            )}
          </Card.Content>
        </Card>
      </Animatable.View>
    );
  };
  
  return (
    <SafeAreaView style={styles.safeArea}>
      <StatusBar backgroundColor="#1976d2" barStyle="light-content" />
      
      {/* Teacher App Header */}
      <TeacherAppHeader
        title={translate('teacher.directory.title') || "Teacher Directory"}
        onMenuPress={toggleDrawer}
        showBackButton={true}
        onBackPress={() => navigation.goBack()}
      />
      
      {/* Teacher Sidebar */}
      <TeacherSidebar
        visible={drawerOpen}
        onClose={toggleDrawer}
        navigation={navigation}
        activeSidebarItem={activeSidebarItem}
        setActiveSidebarItem={setActiveSidebarItem}
        drawerAnim={drawerAnim}
      />
      
      {/* Backdrop */}
      <SidebarBackdrop
        visible={drawerOpen}
        onPress={toggleDrawer}
        fadeAnim={backdropFadeAnim}
      />
      
      {/* Main Content */}
      <Animated.View
        style={[
          styles.content,
          { opacity: fadeAnim, transform: [{ translateY: slideAnim }] }
        ]}
      >
        {/* Search and Filter */}
        <View style={styles.searchContainer}>
          <Searchbar
            placeholder={translate('teacher.directory.searchPlaceholder') || "Search teachers..."}
            onChangeText={setSearchQuery}
            value={searchQuery}
            style={styles.searchBar}
          />
          
          <ScrollView 
            horizontal 
            showsHorizontalScrollIndicator={false}
            style={styles.departmentsScroll}
            contentContainerStyle={styles.departmentsContainer}
          >
            {departments.map((department) => (
              <Chip
                key={department}
                selected={selectedDepartment === department}
                onPress={() => setSelectedDepartment(department)}
                style={[
                  styles.departmentFilterChip,
                  selectedDepartment === department && styles.selectedDepartmentChip
                ]}
                textStyle={[
                  styles.departmentFilterText,
                  selectedDepartment === department && styles.selectedDepartmentText
                ]}
              >
                {department === 'all' 
                  ? translate('teacher.directory.allDepartments') || 'All Departments'
                  : department}
              </Chip>
            ))}
          </ScrollView>
        </View>
        
        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={theme.colors.primary} />
            <Text style={styles.loadingText}>{translate('common.loading') || 'Loading...'}</Text>
          </View>
        ) : (
          <ScrollView
            style={styles.teachersList}
            contentContainerStyle={styles.teachersListContent}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={onRefresh}
                colors={[theme.colors.primary]}
              />
            }
          >
            {filteredTeachers.length > 0 ? (
              filteredTeachers.map(renderTeacherCard)
            ) : (
              <View style={styles.emptyContainer}>
                <MaterialCommunityIcons name="account-search" size={64} color={theme.colors.disabled} />
                <Text style={styles.emptyText}>
                  {translate('teacher.directory.noTeachersFound') || 'No teachers found'}
                </Text>
              </View>
            )}
          </ScrollView>
        )}
      </Animated.View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  searchContainer: {
    marginBottom: 16,
  },
  searchBar: {
    elevation: 2,
    borderRadius: 8,
    marginBottom: 8,
  },
  departmentsScroll: {
    maxHeight: 40,
  },
  departmentsContainer: {
    paddingVertical: 4,
  },
  departmentFilterChip: {
    marginRight: 8,
    backgroundColor: '#f0f0f0',
  },
  selectedDepartmentChip: {
    backgroundColor: '#2196F3',
  },
  departmentFilterText: {
    color: '#757575',
  },
  selectedDepartmentText: {
    color: 'white',
  },
  teachersList: {
    flex: 1,
  },
  teachersListContent: {
    paddingBottom: 16,
  },
  teacherCard: {
    marginBottom: 16,
    borderRadius: 8,
    elevation: 2,
  },
  cardContent: {
    padding: 16,
  },
  teacherHeader: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  avatar: {
    marginRight: 16,
    backgroundColor: '#e0e0e0',
  },
  teacherInfo: {
    flex: 1,
    justifyContent: 'center',
  },
  teacherName: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  departmentChip: {
    alignSelf: 'flex-start',
    marginTop: 4,
    marginBottom: 4,
    height: 24,
  },
  teacherEmail: {
    fontSize: 14,
    color: '#757575',
  },
  divider: {
    marginVertical: 12,
  },
  subjectsContainer: {
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  subjectChips: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  subjectChip: {
    margin: 4,
    backgroundColor: '#e3f2fd',
  },
  subjectChipText: {
    color: '#1976d2',
  },
  noSubjects: {
    fontStyle: 'italic',
    color: '#9e9e9e',
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
  },
  infoText: {
    marginLeft: 8,
    fontSize: 14,
    color: '#616161',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#757575',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 64,
  },
  emptyText: {
    marginTop: 16,
    fontSize: 16,
    color: '#757575',
    textAlign: 'center',
  },
});

export default TeacherDirectory;
