import React, { useState, useEffect } from 'react';
import { View, ScrollView, StyleSheet } from 'react-native';
import { Card, Title, Text, Portal, Modal, List, FAB } from 'react-native-paper';
import { Calendar } from 'react-native-calendars';
import { db } from '../../config/firebase';
import { collection, query, getDocs, addDoc, where, deleteDoc, doc } from 'firebase/firestore';
import { useAuth } from '../../context/AuthContext';
import CustomButton from '../../components/common/CustomButton';
import CustomInput from '../../components/common/CustomInput';

const CalendarManagement = () => {
  const { user } = useAuth();
  const [events, setEvents] = useState([]);
  const [selectedDate, setSelectedDate] = useState('');
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [markedDates, setMarkedDates] = useState({});
  
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    startTime: '',
    endTime: '',
    location: '',
    type: 'personal', // personal, exam, assignment
  });

  useEffect(() => {
    fetchEvents();
  }, []);

  useEffect(() => {
    updateMarkedDates();
  }, [events]);

  const fetchEvents = async () => {
    try {
      const eventsRef = collection(db, 'events');
      const q = query(
        eventsRef,
        where('userId', '==', user.uid)
      );
      const querySnapshot = await getDocs(q);
      
      const eventsData = [];
      querySnapshot.forEach((doc) => {
        eventsData.push({ id: doc.id, ...doc.data() });
      });
      
      setEvents(eventsData);
    } catch (error) {
      console.error('Error fetching events:', error);
    }
  };

  const updateMarkedDates = () => {
    const marked = {};
    events.forEach(event => {
      const date = event.date;
      marked[date] = {
        marked: true,
        dotColor: getEventColor(event.type),
      };
    });
    setMarkedDates(marked);
  };

  const getEventColor = (type) => {
    switch (type) {
      case 'exam':
        return '#F44336';
      case 'assignment':
        return '#2196F3';
      default:
        return '#4CAF50';
    }
  };

  const handleAddEvent = async () => {
    try {
      setLoading(true);
      
      const eventData = {
        ...formData,
        date: selectedDate,
        userId: user.uid,
        createdAt: new Date().toISOString(),
      };

      const eventsRef = collection(db, 'events');
      await addDoc(eventsRef, eventData);
      
      setVisible(false);
      setFormData({
        title: '',
        description: '',
        startTime: '',
        endTime: '',
        location: '',
        type: 'personal',
      });
      fetchEvents();
    } catch (error) {
      console.error('Error adding event:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteEvent = async (eventId) => {
    try {
      await deleteDoc(doc(db, 'events', eventId));
      fetchEvents();
    } catch (error) {
      console.error('Error deleting event:', error);
    }
  };

  const getDayEvents = (date) => {
    return events.filter(event => event.date === date);
  };

  return (
    <View style={styles.container}>
      <Card style={styles.calendarCard}>
        <Card.Content>
          <Calendar
            onDayPress={(day) => setSelectedDate(day.dateString)}
            markedDates={{
              ...markedDates,
              [selectedDate]: {
                ...markedDates[selectedDate],
                selected: true,
              },
            }}
            theme={{
              selectedDayBackgroundColor: '#2196F3',
              todayTextColor: '#2196F3',
              arrowColor: '#2196F3',
            }}
          />
        </Card.Content>
      </Card>

      {selectedDate && (
        <Card style={styles.eventsCard}>
          <Card.Content>
            <Title>Events for {selectedDate}</Title>
            {getDayEvents(selectedDate).map((event) => (
              <List.Item
                key={event.id}
                title={event.title}
                description={`${event.startTime} - ${event.endTime}\n${event.description}`}
                left={props => (
                  <List.Icon
                    {...props}
                    icon={
                      event.type === 'exam' ? 'pencil' :
                      event.type === 'assignment' ? 'book' :
                      'calendar'
                    }
                    color={getEventColor(event.type)}
                  />
                )}
                right={props => (
                  <CustomButton
                    mode="text"
                    onPress={() => handleDeleteEvent(event.id)}
                    {...props}
                  >
                    Delete
                  </CustomButton>
                )}
              />
            ))}
            {getDayEvents(selectedDate).length === 0 && (
              <Text>No events for this date</Text>
            )}
          </Card.Content>
        </Card>
      )}

      <Portal>
        <Modal
          visible={visible}
          onDismiss={() => setVisible(false)}
          contentContainerStyle={styles.modalContent}
        >
          <ScrollView>
            <Title>Add New Event</Title>

            <CustomInput
              label="Title"
              value={formData.title}
              onChangeText={(text) => setFormData({ ...formData, title: text })}
            />

            <CustomInput
              label="Description"
              value={formData.description}
              onChangeText={(text) => setFormData({ ...formData, description: text })}
              multiline
              numberOfLines={3}
            />

            <CustomInput
              label="Start Time"
              value={formData.startTime}
              onChangeText={(text) => setFormData({ ...formData, startTime: text })}
              placeholder="HH:MM"
            />

            <CustomInput
              label="End Time"
              value={formData.endTime}
              onChangeText={(text) => setFormData({ ...formData, endTime: text })}
              placeholder="HH:MM"
            />

            <CustomInput
              label="Location"
              value={formData.location}
              onChangeText={(text) => setFormData({ ...formData, location: text })}
            />

            <List.Section title="Event Type">
              <List.Item
                title="Personal"
                left={props => (
                  <List.Icon
                    {...props}
                    icon="calendar"
                    color={getEventColor('personal')}
                  />
                )}
                onPress={() => setFormData({ ...formData, type: 'personal' })}
              />
              <List.Item
                title="Exam"
                left={props => (
                  <List.Icon
                    {...props}
                    icon="pencil"
                    color={getEventColor('exam')}
                  />
                )}
                onPress={() => setFormData({ ...formData, type: 'exam' })}
              />
              <List.Item
                title="Assignment"
                left={props => (
                  <List.Icon
                    {...props}
                    icon="book"
                    color={getEventColor('assignment')}
                  />
                )}
                onPress={() => setFormData({ ...formData, type: 'assignment' })}
              />
            </List.Section>

            <View style={styles.modalButtons}>
              <CustomButton
                mode="contained"
                onPress={handleAddEvent}
                loading={loading}
              >
                Add Event
              </CustomButton>
              
              <CustomButton
                mode="outlined"
                onPress={() => setVisible(false)}
                style={styles.cancelButton}
              >
                Cancel
              </CustomButton>
            </View>
          </ScrollView>
        </Modal>
      </Portal>

      <FAB
        style={styles.fab}
        icon="plus"
        onPress={() => {
          if (selectedDate) {
            setVisible(true);
          }
        }}
        disabled={!selectedDate}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  calendarCard: {
    margin: 10,
  },
  eventsCard: {
    margin: 10,
  },
  modalContent: {
    backgroundColor: 'white',
    padding: 20,
    margin: 20,
    borderRadius: 10,
    maxHeight: '80%',
  },
  modalButtons: {
    marginTop: 20,
  },
  cancelButton: {
    marginTop: 10,
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
    backgroundColor: '#2196F3',
  },
});

export default CalendarManagement;
