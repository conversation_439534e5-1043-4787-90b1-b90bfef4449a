const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Starting final APK build process...');

// Paths to the problematic files
const autoLinkingGradle = path.join(
  __dirname,
  'node_modules',
  'expo-modules-autolinking',
  'scripts',
  'android',
  'autolinking_implementation.gradle'
);

const corePluginGradle = path.join(
  __dirname,
  'node_modules',
  'expo-modules-core',
  'android',
  'ExpoModulesCorePlugin.gradle'
);

// Create backups
console.log('📦 Creating backups of the problematic files...');
if (fs.existsSync(autoLinkingGradle)) {
  fs.copyFileSync(autoLinkingGradle, `${autoLinkingGradle}.bak`);
}

if (fs.existsSync(corePluginGradle)) {
  fs.copyFileSync(corePluginGradle, `${corePluginGradle}.bak`);
}

// Patch the problematic files
console.log('🔧 Patching Gradle files to bypass NDK issues...');

// Patch 1: Update the autolinking_implementation.gradle file
try {
  if (fs.existsSync(autoLinkingGradle)) {
    let content = fs.readFileSync(autoLinkingGradle, 'utf8');
    
    // Add a try-catch block around the code at line 377 that's causing issues
    const problematicLineIndex = 376; // Line 377 (0-indexed)
    const lines = content.split('\n');
    
    if (lines.length > problematicLineIndex) {
      let modifiedLines = [...lines];
      
      // Insert try-catch block
      modifiedLines.splice(problematicLineIndex, 0, 
        '    try {',
        '      // Try to configure the project, but ignore errors related to NDK'
      );
      
      // Find a good place to close the try-catch block (around line 390)
      let closingIndex = problematicLineIndex + 20;
      if (closingIndex < modifiedLines.length) {
        modifiedLines.splice(closingIndex, 0,
          '    } catch (Exception e) {',
          '      println "Warning: Skipping NDK configuration due to error: ${e.message}"',
          '    }'
        );
      }
      
      // Write the modified file
      fs.writeFileSync(autoLinkingGradle, modifiedLines.join('\n'), 'utf8');
      console.log('   ✓ autolinking_implementation.gradle patched');
    }
  }
} catch (error) {
  console.log(`❌ Error patching autolinking_implementation.gradle: ${error.message}`);
}

// Patch 2: Update ExpoModulesCorePlugin.gradle
try {
  if (fs.existsSync(corePluginGradle)) {
    let content = fs.readFileSync(corePluginGradle, 'utf8');
    
    // Modify the useExpoPublishing method to add try-catch blocks
    content = content.replace(
      /ext\.useExpoPublishing = \{([\s\S]*?)}/m,
      `ext.useExpoPublishing = {
  try {
    if (!project.plugins.hasPlugin('maven-publish')) {
      apply plugin: 'maven-publish'
    }
    
    afterEvaluate {
      try {
        publishing {
          publications {
            try {
              release(MavenPublication) {
                // Only attempt to add components that exist
                if (components.findByName('release') != null) {
                  from components.release
                } else if (components.findByName('java') != null) {
                  from components.java
                }
              }
            } catch (Exception e) {
              println "Warning: Failed to configure release publication: \${e.message}"
            }
          }
          repositories {
            maven {
              url = mavenLocal().url
            }
          }
        }
      } catch (Exception e) {
        println "Warning: Failed to configure publishing: \${e.message}"
      }
    }

    if (project.plugins.hasPlugin('com.android.library')) {
      try {
        android {
          try {
            publishing {
              try {
                singleVariant("release") {
                  withSourcesJar()
                }
              } catch (Exception e) {
                println "Warning: Failed to configure variant: \${e.message}"
              }
            }
          } catch (Exception e) {
            println "Warning: Failed to configure android publishing: \${e.message}"
          }
        }
      } catch (Exception e) {
        println "Warning: Failed to configure android block: \${e.message}"
      }
    }
  } catch (Exception e) {
    println "Warning: Failed in useExpoPublishing: \${e.message}"
  }
}`
    );
    
    // Write the modified file
    fs.writeFileSync(corePluginGradle, content, 'utf8');
    console.log('   ✓ ExpoModulesCorePlugin.gradle patched');
  }
} catch (error) {
  console.log(`❌ Error patching ExpoModulesCorePlugin.gradle: ${error.message}`);
}

// Fix the local.properties file
console.log('🔧 Setting up local.properties...');
const localPropertiesPath = path.join(__dirname, 'android', 'local.properties');
fs.writeFileSync(localPropertiesPath, 
  'sdk.dir=D:\\Android\\Sdk\n' +
  'android.useAndroidX=true\n' +
  'android.enableJetifier=true\n' +
  'android.disableAutomaticComponentCreation=true\n', 
  'utf8'
);

// Update gradle.properties to skip NDK
console.log('🔧 Updating gradle.properties...');
const gradlePropertiesPath = path.join(__dirname, 'android', 'gradle.properties');
let gradleContent = fs.readFileSync(gradlePropertiesPath, 'utf8');
if (!gradleContent.includes('android.enableNativeCompile=false')) {
  gradleContent += '\n# Disable NDK\n' +
    'android.enableNativeCompile=false\n' +
    'android.defaults.buildfeatures.buildconfig=true\n' +
    'android.disableAutomaticComponentCreation=true\n';
  fs.writeFileSync(gradlePropertiesPath, gradleContent, 'utf8');
}

// Build the APK
console.log('🏗️ Building APK...');
try {
  execSync('cd android && gradlew.bat clean', { stdio: 'inherit' });
  execSync('cd android && gradlew.bat assembleRelease', { stdio: 'inherit' });
  
  // Check if the APK was built
  const apkPath = path.join(__dirname, 'android', 'app', 'build', 'outputs', 'apk', 'release', 'app-release.apk');
  if (fs.existsSync(apkPath)) {
    const destPath = path.join(__dirname, 'app-release.apk');
    fs.copyFileSync(apkPath, destPath);
    console.log('✅ Build successful! APK is available at:');
    console.log(`   - ${apkPath}`);
    console.log(`   - ${destPath}`);
  } else {
    console.log('❌ Build completed but no APK was found.');
  }
} catch (error) {
  console.log(`❌ Build failed: ${error.message}`);
}

// Restore the original files
console.log('🔄 Restoring original files...');
if (fs.existsSync(`${autoLinkingGradle}.bak`)) {
  fs.copyFileSync(`${autoLinkingGradle}.bak`, autoLinkingGradle);
  fs.unlinkSync(`${autoLinkingGradle}.bak`);
}

if (fs.existsSync(`${corePluginGradle}.bak`)) {
  fs.copyFileSync(`${corePluginGradle}.bak`, corePluginGradle);
  fs.unlinkSync(`${corePluginGradle}.bak`);
}

console.log('🏁 Build process completed!'); 