import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { List, Divider, Text, Surface, Avatar, useTheme } from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useLanguage } from '../../context/LanguageContext';
import { useNavigation } from '@react-navigation/native';

const ParentNavigationMenu = ({ selectedChild, children, onChildSelect }) => {
  const theme = useTheme();
  const { translate, language } = useLanguage();
  const navigation = useNavigation();

  // Debug log for translation
  useEffect(() => {
    console.log('Navigation Menu - Current language:', language);
    console.log('Navigation Menu - Translation test:', translate('parent.navigation.dashboard'));
  }, [language, translate]);

  const [expandedSection, setExpandedSection] = useState('dashboard');

  const handleSectionPress = (section) => {
    setExpandedSection(expandedSection === section ? null : section);
  };

  const navigateTo = (screen, params = {}) => {
    if (selectedChild) {
      navigation.navigate(screen, {
        childId: selectedChild.id,
        ...params
      });
    } else if (children && children.length > 0) {
      navigation.navigate(screen, {
        childId: children[0].id,
        ...params
      });
    } else {
      // No child selected or available
      alert(translate('parent.navigation.selectChildFirst'));
    }
  };

  const getInitials = (child) => {
    if (!child) return '?';
    return `${(child.firstName || '').charAt(0)}${(child.lastName || '').charAt(0)}`.toUpperCase();
  };

  return (
    <View style={styles.container}>
      {/* Child Selector */}
      <Surface style={styles.childSelectorContainer} elevation={2}>
        <Text style={styles.childSelectorTitle}>
          {translate('parent.navigation.yourChildren')}
        </Text>

        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.childrenScrollView}
        >
          {children.map((child) => (
            <TouchableOpacity
              key={child.id}
              style={[
                styles.childItem,
                selectedChild?.id === child.id && styles.selectedChildItem
              ]}
              onPress={() => onChildSelect(child)}
            >
              {child.photoURL ? (
                <Avatar.Image
                  size={40}
                  source={{ uri: child.photoURL }}
                  style={styles.childAvatar}
                />
              ) : (
                <Avatar.Text
                  size={40}
                  label={getInitials(child)}
                  style={styles.childAvatar}
                  color="#fff"
                  backgroundColor={selectedChild?.id === child.id ? theme.colors.primary : '#9e9e9e'}
                />
              )}
              <Text
                style={[
                  styles.childName,
                  selectedChild?.id === child.id && styles.selectedChildName
                ]}
                numberOfLines={1}
              >
                {child.name || `${child.firstName || ''} ${child.lastName || ''}`.trim() || translate('common.unnamed')}
              </Text>
              <Text
                style={styles.childClass}
                numberOfLines={1}
              >
                {child.className || child.classId || child.class || translate('common.notAssigned')} - {child.section || child.sectionName || translate('common.notAssigned')}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </Surface>

      <Divider />

      {/* Navigation Menu */}
      <ScrollView style={styles.menuScrollView}>
        <List.Section>
          <List.Item
            title={translate('parent.navigation.dashboard')}
            left={props => <List.Icon {...props} icon="view-dashboard" />}
            onPress={() => navigation.navigate('ParentDashboard')}
            style={styles.menuItem}
          />

          <List.Accordion
            title={translate('parent.navigation.academics')}
            left={props => <List.Icon {...props} icon="school" />}
            expanded={expandedSection === 'academics'}
            onPress={() => handleSectionPress('academics')}
            style={styles.menuItem}
          >
            <List.Item
              title={translate('parent.navigation.grades')}
              left={props => <List.Icon {...props} icon="chart-line" />}
              onPress={() => navigateTo('ParentChildGrades')}
              style={styles.subMenuItem}
            />
            <List.Item
              title={translate('parent.navigation.homework')}
              left={props => <List.Icon {...props} icon="book-open-variant" />}
              onPress={() => navigateTo('ParentHomework')}
              style={styles.subMenuItem}
            />
            <List.Item
              title={translate('parent.navigation.classSchedule')}
              left={props => <List.Icon {...props} icon="calendar-clock" />}
              onPress={() => navigateTo('ParentClassSchedule')}
              style={styles.subMenuItem}
            />
            <List.Item
              title={translate('parent.navigation.examSchedule')}
              left={props => <List.Icon {...props} icon="calendar-alert" />}
              onPress={() => navigateTo('ParentExamSchedule')}
              style={styles.subMenuItem}
            />
          </List.Accordion>

          <List.Accordion
            title={translate('parent.navigation.attendance')}
            left={props => <List.Icon {...props} icon="account-check" />}
            expanded={expandedSection === 'attendance'}
            onPress={() => handleSectionPress('attendance')}
            style={styles.menuItem}
          >
            <List.Item
              title={translate('parent.navigation.attendanceView')}
              left={props => <List.Icon {...props} icon="calendar-check" />}
              onPress={() => navigateTo('ParentAttendanceView')}
              style={styles.subMenuItem}
            />
            <List.Item
              title={translate('parent.navigation.attendanceReport')}
              left={props => <List.Icon {...props} icon="file-chart" />}
              onPress={() => navigateTo('ParentAttendanceReport')}
              style={styles.subMenuItem}
            />
          </List.Accordion>

          <List.Accordion
            title={translate('parent.navigation.behavior')}
            left={props => <List.Icon {...props} icon="account-star" />}
            expanded={expandedSection === 'behavior'}
            onPress={() => handleSectionPress('behavior')}
            style={styles.menuItem}
          >
            <List.Item
              title={translate('parent.navigation.behaviorView')}
              left={props => <List.Icon {...props} icon="star-check" />}
              onPress={() => navigateTo('ParentBehaviorView')}
              style={styles.subMenuItem}
            />
            <List.Item
              title={translate('parent.navigation.behaviorReport')}
              left={props => <List.Icon {...props} icon="file-chart" />}
              onPress={() => navigateTo('ParentBehaviorReport')}
              style={styles.subMenuItem}
            />
          </List.Accordion>

          <List.Accordion
            title={translate('parent.navigation.communication')}
            left={props => <List.Icon {...props} icon="message-text" />}
            expanded={expandedSection === 'communication'}
            onPress={() => handleSectionPress('communication')}
            style={styles.menuItem}
          >
            <List.Item
              title={translate('parent.navigation.messages')}
              left={props => <List.Icon {...props} icon="message-text-outline" />}
              onPress={() => navigation.navigate('ParentMessaging')}
              style={styles.subMenuItem}
            />
            <List.Item
              title={translate('parent.navigation.parentTeacherMeetings')}
              left={props => <List.Icon {...props} icon="account-group" />}
              onPress={() => navigation.navigate('MeetingScheduler')}
              style={styles.subMenuItem}
            />
          </List.Accordion>

          <List.Item
            title={translate('parent.navigation.notifications')}
            left={props => <List.Icon {...props} icon="bell" />}
            onPress={() => navigation.navigate('Notifications')}
            style={styles.menuItem}
          />

          <List.Item
            title={translate('parent.navigation.profile')}
            left={props => <List.Icon {...props} icon="account-cog" />}
            onPress={() => navigation.navigate('ProfileManagement')}
            style={styles.menuItem}
          />

          <List.Item
            title={translate('parent.navigation.settings')}
            left={props => <List.Icon {...props} icon="cog" />}
            onPress={() => navigation.navigate('Settings')}
            style={styles.menuItem}
          />
        </List.Section>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  childSelectorContainer: {
    padding: 16,
    backgroundColor: '#f5f5f5',
  },
  childSelectorTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 12,
    color: '#424242',
  },
  childrenScrollView: {
    flexGrow: 0,
  },
  childItem: {
    alignItems: 'center',
    marginRight: 16,
    width: 80,
    padding: 8,
    borderRadius: 8,
  },
  selectedChildItem: {
    backgroundColor: '#e3f2fd',
  },
  childAvatar: {
    marginBottom: 8,
  },
  childName: {
    fontSize: 14,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 4,
  },
  selectedChildName: {
    color: '#1976d2',
  },
  childClass: {
    fontSize: 12,
    color: '#757575',
    textAlign: 'center',
  },
  menuScrollView: {
    flex: 1,
  },
  menuItem: {
    paddingVertical: 4,
  },
  subMenuItem: {
    paddingLeft: 32,
  },
});

export default ParentNavigationMenu;
