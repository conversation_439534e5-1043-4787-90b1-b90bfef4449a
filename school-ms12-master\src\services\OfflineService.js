import { db } from '../config/firebase';
import { collection, query, where, getDocs, writeBatch, doc, getDoc, setDoc, deleteDoc } from 'firebase/firestore';
import localforage from 'localforage';
import { openDB } from 'idb';
import NetInfo from '@react-native-community/netinfo';
import { Platform, AppState } from 'react-native';

class OfflineService {
    static instance = null;

    constructor() {
        if (OfflineService.instance) {
            return OfflineService.instance;
        }
        OfflineService.instance = this;

        this.dbName = 'schoolMS_offline';
        this.syncInterval = 5 * 60 * 1000; // 5 minutes
        this.maxRetries = 3;
        this.isOnline = true;
        this.syncListeners = [];
        this.lastSyncAttempt = 0;
        this.syncInProgress = false;

        // Collections to sync
        this.syncCollections = [
            'students',
            'teachers',
            'classes',
            'attendance',
            'grades',
            'homework',
            'behavior',
            'announcements',
            'notifications',
            'messages',
            'timetable',
            'assignments'
        ];

        this.initializeOfflineDB();
        this.setupSyncListener();
    }

    // Initialize IndexedDB
    async initializeOfflineDB() {
        try {
            this.db = await openDB(this.dbName, 2, {
                upgrade(db, oldVersion, newVersion, transaction) {
                    // Create stores for each collection
                    const stores = [
                        'students', 'teachers', 'classes', 'attendance', 'grades',
                        'homework', 'behavior', 'announcements', 'pendingChanges',
                        'notifications', 'messages', 'timetable', 'assignments',
                        'settings', 'userCache'
                    ];

                    stores.forEach(store => {
                        if (!db.objectStoreNames.contains(store)) {
                            db.createObjectStore(store, { keyPath: 'id' });
                        }
                    });

                    // Create indexes for faster queries
                    const tx = transaction;

                    // Add indexes to relevant stores
                    if (db.objectStoreNames.contains('students')) {
                        const studentStore = tx.objectStore('students');
                        if (!studentStore.indexNames.contains('classId')) {
                            studentStore.createIndex('classId', 'classId', { unique: false });
                        }
                    }

                    if (db.objectStoreNames.contains('assignments')) {
                        const assignmentStore = tx.objectStore('assignments');
                        if (!assignmentStore.indexNames.contains('classId')) {
                            assignmentStore.createIndex('classId', 'classId', { unique: false });
                        }
                        if (!assignmentStore.indexNames.contains('dueDate')) {
                            assignmentStore.createIndex('dueDate', 'dueDate', { unique: false });
                        }
                    }
                }
            });

            // Initialize localforage for larger data
            this.localCache = localforage.createInstance({
                name: this.dbName
            });

            console.log('Offline database initialized successfully');
            return true;
        } catch (error) {
            console.error('Failed to initialize offline database:', error);
            return false;
        }
    }

    // Setup network sync listener
    setupSyncListener() {
        // Use NetInfo for React Native
        this.unsubscribeNetInfo = NetInfo.addEventListener(state => {
            const wasOffline = !this.isOnline;
            this.isOnline = state.isConnected && state.isInternetReachable;

            // If we're coming back online, sync with server
            if (wasOffline && this.isOnline) {
                this.syncWithServer();
            }

            // Notify listeners of network state change
            this.notifyNetworkListeners();
        });

        // Listen for app state changes to sync when app comes to foreground
        this.appStateSubscription = AppState.addEventListener('change', nextAppState => {
            if (nextAppState === 'active' && this.isOnline) {
                // Only sync if it's been more than 5 minutes since last sync attempt
                const now = Date.now();
                if (now - this.lastSyncAttempt > this.syncInterval) {
                    this.syncWithServer();
                    this.lastSyncAttempt = now;
                }
            }
        });
    }

    // Sync data with server
    async syncWithServer() {
        try {
            // First, sync pending changes to server
            await this.syncPendingChanges();

            // Then, fetch latest data from server
            for (const collection of this.syncCollections) {
                await this.syncCollection(collection);
            }

            // Update last sync timestamp
            await this.localCache.setItem('lastSync', Date.now());

            console.log('Sync completed successfully');
        } catch (error) {
            console.error('Sync failed:', error);
            this.notifyUser('Sync failed. Will retry automatically.');
        }
    }

    // Sync pending changes to server
    async syncPendingChanges() {
        const pendingChanges = await this.db.getAll('pendingChanges');
        const batch = writeBatch(db);

        for (const change of pendingChanges) {
            try {
                const docRef = doc(db, change.collection, change.id);

                switch (change.type) {
                    case 'add':
                    case 'update':
                        batch.set(docRef, change.data, { merge: true });
                        break;
                    case 'delete':
                        batch.delete(docRef);
                        break;
                }

                // Remove from pending changes if successful
                await this.db.delete('pendingChanges', change.id);
            } catch (error) {
                console.error('Failed to sync change:', change, error);
            }
        }

        await batch.commit();
    }

    // Sync specific collection
    async syncCollection(collectionName) {
        const lastSync = await this.localCache.getItem('lastSync') || 0;

        // Get all documents updated since last sync
        const snapshot = await getDocs(
            query(collection(db, collectionName),
                where('updatedAt', '>', lastSync)
            )
        );

        // Update local database
        const tx = this.db.transaction(collectionName, 'readwrite');
        const store = tx.objectStore(collectionName);

        snapshot.forEach(doc => {
            store.put({ id: doc.id, ...doc.data() });
        });

        await tx.done;
    }

    // Save data locally when offline
    async saveOffline(collectionName, data) {
        try {
            // Save to IndexedDB
            await this.db.put(collectionName, {
                id: data.id,
                ...data,
                updatedAt: Date.now()
            });

            // Add to pending changes
            await this.db.put('pendingChanges', {
                id: Date.now().toString(),
                collection: collectionName,
                type: data.id ? 'update' : 'add',
                data: data,
                timestamp: Date.now()
            });

            return true;
        } catch (error) {
            console.error('Offline save failed:', error);
            return false;
        }
    }

    // Get data from local database
    async getOfflineData(collectionName, query = {}) {
        try {
            let data;
            if (query.id) {
                data = await this.db.get(collectionName, query.id);
            } else {
                data = await this.db.getAll(collectionName);

                // Apply filters if any
                if (query.filters) {
                    data = data.filter(item => {
                        return Object.entries(query.filters).every(([key, value]) =>
                            item[key] === value
                        );
                    });
                }
            }
            return data;
        } catch (error) {
            console.error('Offline data retrieval failed:', error);
            return null;
        }
    }

    // Delete data locally
    async deleteOffline(collectionName, id) {
        try {
            await this.db.delete(collectionName, id);

            // Add to pending changes
            await this.db.put('pendingChanges', {
                id: Date.now().toString(),
                collection: collectionName,
                type: 'delete',
                data: { id },
                timestamp: Date.now()
            });

            return true;
        } catch (error) {
            console.error('Offline delete failed:', error);
            return false;
        }
    }

    // Check if offline data is available
    async hasOfflineData(collectionName) {
        const count = await this.db.count(collectionName);
        return count > 0;
    }

    // Get offline status
    async getOfflineStatus() {
        const lastSync = await this.localCache.getItem('lastSync');
        const pendingChanges = await this.db.count('pendingChanges');

        return {
            isOnline: navigator.onLine,
            lastSync,
            pendingChanges,
            dataAvailable: await Promise.all(
                this.syncCollections.map(async collection => ({
                    collection,
                    available: await this.hasOfflineData(collection)
                }))
            )
        };
    }

    // Clear offline data
    async clearOfflineData() {
        try {
            await Promise.all(
                this.syncCollections.map(collection =>
                    this.db.clear(collection)
                )
            );
            await this.localCache.clear();
            return true;
        } catch (error) {
            console.error('Failed to clear offline data:', error);
            return false;
        }
    }

    // Notify user of sync status
    notifyUser(message) {
        // Implementation depends on UI notification system
        console.log('Sync notification:', message);
    }
}

export default new OfflineService();
