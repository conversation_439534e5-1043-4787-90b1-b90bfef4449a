# Admin Screens Manual Testing Checklist

Use this checklist to manually verify that all admin screens are working correctly.

## Admin Dashboard

- [ ] Dashboard loads without errors
- [ ] Quick stats display correct numbers (students, teachers, classes, active users)
- [ ] Sidebar menu shows all expected items
- [ ] Search functionality works correctly
- [ ] Menu items navigate to the correct screens
- [ ] FAB (Floating Action Button) menu works correctly
- [ ] Recent activities section shows latest activities
- [ ] Dashboard tabs (enrollment, performance, resources) work correctly
- [ ] Ethiopian calendar dates are displayed correctly

## Academic Management

### Academic Calendar
- [ ] Calendar screen loads without errors
- [ ] Terms list displays correctly
- [ ] Holidays list displays correctly
- [ ] Add term button opens modal with Ethiopian date picker
- [ ] Add holiday button opens modal with Ethiopian date picker
- [ ] Can successfully add a new term
- [ ] Can successfully add a new holiday
- [ ] Ethiopian dates display correctly in the lists

### Academic Year Management
- [ ] Screen loads without errors
- [ ] Academic years list displays correctly
- [ ] Can add a new academic year with Ethiopian dates
- [ ] Can edit an existing academic year
- [ ] Can activate/deactivate an academic year

### Semester Management
- [ ] Screen loads without errors
- [ ] Semesters list displays correctly
- [ ] Can add a new semester with Ethiopian dates
- [ ] Can edit an existing semester
- [ ] Can link semester to academic year

### Course Management
- [ ] Screen loads without errors
- [ ] Courses list displays correctly
- [ ] Can add a new course
- [ ] Can edit an existing course
- [ ] Can assign teachers to courses

### Subject Management
- [ ] Screen loads without errors
- [ ] Subjects list displays correctly
- [ ] Can add a new subject
- [ ] Can edit an existing subject
- [ ] Can assign subjects to classes/courses

### Grade Settings
- [ ] Screen loads without errors
- [ ] Grade scales list displays correctly
- [ ] Can add a new grade scale
- [ ] Can edit an existing grade scale
- [ ] Grade calculation rules work correctly

## User Management

### Student Management
- [ ] Screen loads without errors
- [ ] Students list displays correctly
- [ ] Search functionality works
- [ ] Can add a new student with Ethiopian date of birth
- [ ] Can edit an existing student
- [ ] Can view student details
- [ ] Can assign student to class/section

### Teacher Management
- [ ] Screen loads without errors
- [ ] Teachers list displays correctly
- [ ] Search functionality works
- [ ] Can add a new teacher
- [ ] Can edit an existing teacher
- [ ] Can assign subjects to teachers
- [ ] Can manage teacher permissions

### Parent Management
- [ ] Screen loads without errors
- [ ] Parents list displays correctly
- [ ] Search functionality works
- [ ] Can add a new parent
- [ ] Can edit an existing parent
- [ ] Can link parents to students

### User Management
- [ ] Screen loads without errors
- [ ] Users list displays correctly
- [ ] Search functionality works
- [ ] Can add a new user
- [ ] Can edit an existing user
- [ ] Can assign user roles
- [ ] Can activate/deactivate users

### Admin Management
- [ ] Screen loads without errors
- [ ] Admins list displays correctly
- [ ] Can add a new admin
- [ ] Can edit an existing admin
- [ ] Can manage admin permissions

## Class and Exam Management

### Class Management
- [ ] Screen loads without errors
- [ ] Classes list displays correctly
- [ ] Can add a new class
- [ ] Can edit an existing class
- [ ] Can manage class sections
- [ ] Can assign teachers to classes

### Exam Management
- [ ] Screen loads without errors
- [ ] Exams list displays correctly
- [ ] Can add a new exam with Ethiopian dates
- [ ] Can edit an existing exam
- [ ] Can assign subjects to exams

### Exam Routine Manager
- [ ] Screen loads without errors
- [ ] Exam routines display correctly
- [ ] Can create a new exam routine with Ethiopian dates and times
- [ ] Can edit an existing exam routine
- [ ] Ethiopian time picker works correctly

### Results Management
- [ ] Screen loads without errors
- [ ] Results list displays correctly
- [ ] Can enter new results
- [ ] Can edit existing results
- [ ] Result calculation works correctly
- [ ] Can publish results

### Result Approval
- [ ] Screen loads without errors
- [ ] Pending approvals list displays correctly
- [ ] Can approve results
- [ ] Can reject results with comments
- [ ] Approval status updates correctly

## Settings and Reports

### School Settings
- [ ] Screen loads without errors
- [ ] School information displays correctly
- [ ] Can update school information
- [ ] Can upload/change school logo
- [ ] Can update contact information

### System Reports
- [ ] Screen loads without errors
- [ ] Reports dashboard displays correctly
- [ ] Can generate different types of reports
- [ ] Can export reports
- [ ] Date range selection with Ethiopian calendar works correctly

### Library Management
- [ ] Screen loads without errors
- [ ] Books list displays correctly
- [ ] Can add a new book
- [ ] Can edit an existing book
- [ ] Can manage book checkout/return

### Time Settings
- [ ] Screen loads without errors
- [ ] Time settings display correctly
- [ ] Can update time format
- [ ] Can update academic periods
- [ ] Ethiopian time format settings work correctly

## Ethiopian Calendar Implementation

- [ ] EthiopianDatePicker works correctly in all screens
- [ ] EthiopianTimePicker works correctly in all screens
- [ ] Dates display correctly in Ethiopian format
- [ ] Date range selection works correctly
- [ ] Different display modes (calendar, spinner, compact) work correctly

## Error Handling

- [ ] Network error messages display correctly
- [ ] Form validation errors display correctly
- [ ] Database operation errors are handled properly
- [ ] Retry mechanisms work correctly

## Multilingual Support

- [ ] Can switch between languages (Amharic, Oromo, English)
- [ ] All screens display correctly in Amharic
- [ ] All screens display correctly in Oromo
- [ ] All screens display correctly in English
- [ ] Ethiopian calendar dates display correctly in all languages

## Notes

- Record any issues found with screenshots and detailed steps to reproduce
- Note any performance issues or slow-loading screens
- Check for consistency in UI/UX across all screens
