import React, { useState, useEffect, useRef } from 'react';
import { View, ScrollView, StyleSheet, StatusBar, SafeAreaView, TouchableOpacity, Animated, Dimensions } from 'react-native';
import {
  DataTable,
  Card,
  Title,
  Button,
  Chip,
  IconButton,
  Text,
  ActivityIndicator,
  Snackbar,
  Portal,
  Dialog,
  TextInput,
  Searchbar,
  Menu,
  Divider,
  Avatar,
  List,
  Surface
} from 'react-native-paper';
import { db, auth } from '../../config/firebase';
import {
  collection,
  query,
  where,
  getDocs,
  doc,
  getDoc,
  updateDoc,
  orderBy,
  Timestamp,
  serverTimestamp,
  limit
} from 'firebase/firestore';
import { useLanguage } from '../../context/LanguageContext';
import * as Animatable from 'react-native-animatable';
import { LinearGradient } from 'expo-linear-gradient';
import AdminAppHeader from '../../components/common/AdminAppHeader';
import AdminSidebar from '../../components/common/AdminSidebar';
import SidebarBackdrop from '../../components/common/SidebarBackdrop';
import AttendanceNotificationService from '../../services/AttendanceNotificationService';



const AdminAttendanceApproval = ({ route, navigation }) => {
  const { translate, language, isRTL } = useLanguage();
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const { width } = Dimensions.get('window');

  // Sidebar state
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [activeSidebarItem, setActiveSidebarItem] = useState('AdminAttendanceApproval');
  const backdropFadeAnim = useRef(new Animated.Value(0)).current;

  // Attendance submissions state
  const [submissions, setSubmissions] = useState([]);
  const [filteredSubmissions, setFilteredSubmissions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [dateFilter, setDateFilter] = useState('all');
  const [sortBy, setSortBy] = useState('date');
  const [sortOrder, setSortOrder] = useState('desc');

  // Detail view state
  const [selectedSubmission, setSelectedSubmission] = useState(null);
  const [detailsVisible, setDetailsVisible] = useState(false);
  const [attendanceRecords, setAttendanceRecords] = useState([]);

  // Approval state
  const [approvalDialogVisible, setApprovalDialogVisible] = useState(false);
  const [rejectionDialogVisible, setRejectionDialogVisible] = useState(false);
  const [rejectionReason, setRejectionReason] = useState('');
  const [processingAction, setProcessingAction] = useState(false);

  // Notification state
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');

  // Toggle sidebar
  const toggleDrawer = () => {
    const newValue = !drawerOpen;
    setDrawerOpen(newValue);

    // Animate backdrop
    Animated.timing(backdropFadeAnim, {
      toValue: newValue ? 0.5 : 0,
      duration: 300,
      useNativeDriver: true,
    }).start();
  };

  // Start animations when component mounts
  useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 1000,
      useNativeDriver: true,
    }).start();

    // Hide the default header
    navigation.setOptions({
      headerShown: false,
    });
  }, []);

  // Fetch attendance submissions
  useEffect(() => {
    fetchAttendanceSubmissions();

    // Check if we need to highlight a specific submission from notification
    if (route?.params?.highlight) {
      const highlightId = route.params.highlight;
      setSnackbarMessage(translate('admin.attendanceApproval.viewingFromNotification'));
      setSnackbarVisible(true);
    }
  }, [route?.params]);

  // Handle highlighted submission after data is loaded
  useEffect(() => {
    if (route?.params?.highlight && submissions.length > 0) {
      const highlightId = route.params.highlight;
      const submission = submissions.find(s => s.id === highlightId);

      if (submission) {
        // Automatically view the details of the highlighted submission
        viewSubmissionDetails(submission);
      }
    }
  }, [submissions, route?.params]);

  // Filter submissions when filters change
  useEffect(() => {
    applyFilters();
  }, [submissions, searchQuery, statusFilter, dateFilter, sortBy, sortOrder]);

  const fetchAttendanceSubmissions = async () => {
    try {
      setLoading(true);
      setError(null);

      const submissionsRef = collection(db, 'attendanceSubmissions');
      let submissionsQuery = query(
        submissionsRef,
        orderBy('submittedAt', 'desc')
      );

      const submissionsSnapshot = await getDocs(submissionsQuery);

      if (submissionsSnapshot.empty) {
        setSubmissions([]);
        setFilteredSubmissions([]);
        setLoading(false);
        return;
      }

      const submissionsData = submissionsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        submittedAt: doc.data().submittedAt?.toDate() || new Date()
      }));

      setSubmissions(submissionsData);
      setFilteredSubmissions(submissionsData);
    } catch (err) {
      console.error('Error fetching attendance submissions:', err);
      setError('Failed to load attendance submissions');
    } finally {
      setLoading(false);
    }
  };

  const applyFilters = () => {
    let filtered = [...submissions];

    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        submission =>
          submission.className?.toLowerCase().includes(query) ||
          submission.sectionName?.toLowerCase().includes(query) ||
          submission.teacherName?.toLowerCase().includes(query)
      );
    }

    // Apply status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(submission => submission.status === statusFilter);
    }

    // Apply date filter
    if (dateFilter !== 'all') {
      const today = new Date();
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);
      const lastWeek = new Date(today);
      lastWeek.setDate(lastWeek.getDate() - 7);
      const lastMonth = new Date(today);
      lastMonth.setMonth(lastMonth.getMonth() - 1);

      filtered = filtered.filter(submission => {
        const submissionDate = new Date(submission.date);

        switch (dateFilter) {
          case 'today':
            return submissionDate.toDateString() === today.toDateString();
          case 'yesterday':
            return submissionDate.toDateString() === yesterday.toDateString();
          case 'week':
            return submissionDate >= lastWeek;
          case 'month':
            return submissionDate >= lastMonth;
          default:
            return true;
        }
      });
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let comparison = 0;

      switch (sortBy) {
        case 'date':
          comparison = new Date(a.date) - new Date(b.date);
          break;
        case 'class':
          comparison = a.className.localeCompare(b.className);
          break;
        case 'section':
          comparison = a.sectionName.localeCompare(b.sectionName);
          break;
        case 'teacher':
          comparison = a.teacherName.localeCompare(b.teacherName);
          break;
        case 'status':
          comparison = a.status.localeCompare(b.status);
          break;
        case 'submitted':
          comparison = a.submittedAt - b.submittedAt;
          break;
        default:
          comparison = 0;
      }

      return sortOrder === 'asc' ? comparison : -comparison;
    });

    setFilteredSubmissions(filtered);
  };

  // Placeholder for future implementation
  const viewSubmissionDetails = (submission) => {
    setSelectedSubmission(submission);
    setDetailsVisible(true);
    fetchAttendanceRecords(submission);
  };

  // Fetch attendance records for a submission
  const fetchAttendanceRecords = async (submission) => {
    try {
      setLoading(true);

      if (!submission || !submission.attendanceIds || submission.attendanceIds.length === 0) {
        setAttendanceRecords([]);
        setLoading(false);
        return;
      }

      const attendanceRef = collection(db, 'attendance');
      const attendancePromises = submission.attendanceIds.map(async (id) => {
        const docRef = doc(attendanceRef, id);
        const docSnap = await getDoc(docRef);

        if (docSnap.exists()) {
          return {
            id: docSnap.id,
            ...docSnap.data()
          };
        }

        return null;
      });

      const records = (await Promise.all(attendancePromises))
        .filter(record => record !== null)
        .sort((a, b) => a.studentName.localeCompare(b.studentName));

      setAttendanceRecords(records);
    } catch (error) {
      console.error('Error fetching attendance records:', error);
      setSnackbarMessage('Failed to load attendance records');
      setSnackbarVisible(true);
    } finally {
      setLoading(false);
    }
  };

  // Placeholder for future implementation
  const handleApproveSubmission = (submission) => {
    setSelectedSubmission(submission);
    setApprovalDialogVisible(true);
  };

  // Placeholder for future implementation
  const handleRejectSubmission = (submission) => {
    setSelectedSubmission(submission);
    setRejectionReason('');
    setRejectionDialogVisible(true);
  };

  // Approve attendance submission
  const approveSubmission = async () => {
    try {
      setProcessingAction(true);

      if (!selectedSubmission || !selectedSubmission.id) {
        throw new Error('No submission selected');
      }

      // Update submission status
      const submissionRef = doc(db, 'attendanceSubmissions', selectedSubmission.id);
      await updateDoc(submissionRef, {
        status: 'approved',
        approvedBy: auth.currentUser?.uid || '',
        approvedByName: auth.currentUser?.displayName || auth.currentUser?.email || 'Unknown Admin',
        approvedAt: serverTimestamp(),
        approvalNote: 'Attendance has been reviewed and approved. Students and parents will be notified.'
      });

      // Update all attendance records
      if (selectedSubmission.attendanceIds && selectedSubmission.attendanceIds.length > 0) {
        const attendanceRef = collection(db, 'attendance');
        const updatePromises = selectedSubmission.attendanceIds.map(async (id) => {
          const docRef = doc(attendanceRef, id);
          await updateDoc(docRef, {
            approved: true,
            approvedAt: serverTimestamp(),
            approvedBy: auth.currentUser?.uid || '',
            approvedByName: auth.currentUser?.displayName || auth.currentUser?.email || 'Unknown Admin',
            visible: true // Make it visible to students and parents
          });
        });

        await Promise.all(updatePromises);
      }

      // Update local state
      const updatedSubmission = {
        ...selectedSubmission,
        status: 'approved',
        approvedBy: auth.currentUser?.uid || '',
        approvedByName: auth.currentUser?.displayName || auth.currentUser?.email || 'Unknown Admin',
        approvedAt: new Date(),
        approvalNote: 'Attendance has been reviewed and approved. Students and parents will be notified.'
      };

      setSubmissions(prev =>
        prev.map(submission =>
          submission.id === selectedSubmission.id
            ? updatedSubmission
            : submission
        )
      );

      // Send notifications to teacher, students, and parents
      try {
        await AttendanceNotificationService.notifyAttendanceApproval(updatedSubmission);
        console.log('Attendance approval notifications sent to teacher, students, and parents');
      } catch (notificationError) {
        console.error('Error sending attendance approval notifications:', notificationError);
        // Continue even if notification fails
      }

      // Show success message
      setSnackbarMessage(translate('admin.attendanceApproval.approvalSuccess') + ' Students and parents have been notified.');
      setSnackbarVisible(true);

      // Close dialog
      setApprovalDialogVisible(false);

      // Refresh data
      fetchAttendanceSubmissions();

    } catch (error) {
      console.error('Error approving submission:', error);
      setSnackbarMessage(translate('admin.attendanceApproval.approvalError'));
      setSnackbarVisible(true);
    } finally {
      setProcessingAction(false);
    }
  };

  // Reject attendance submission
  const rejectSubmission = async () => {
    try {
      setProcessingAction(true);

      if (!selectedSubmission || !selectedSubmission.id) {
        throw new Error('No submission selected');
      }

      if (!rejectionReason.trim()) {
        throw new Error('Rejection reason is required');
      }

      // Update submission status
      const submissionRef = doc(db, 'attendanceSubmissions', selectedSubmission.id);
      await updateDoc(submissionRef, {
        status: 'rejected',
        rejectedBy: auth.currentUser?.uid || '',
        rejectedByName: auth.currentUser?.displayName || auth.currentUser?.email || 'Unknown Admin',
        rejectedAt: serverTimestamp(),
        rejectionReason: rejectionReason.trim()
      });

      // Update all attendance records
      if (selectedSubmission.attendanceIds && selectedSubmission.attendanceIds.length > 0) {
        const attendanceRef = collection(db, 'attendance');
        const updatePromises = selectedSubmission.attendanceIds.map(async (id) => {
          const docRef = doc(attendanceRef, id);
          await updateDoc(docRef, {
            submitted: false,
            submissionId: null
          });
        });

        await Promise.all(updatePromises);
      }

      // Update local state
      const updatedSubmission = {
        ...selectedSubmission,
        status: 'rejected',
        rejectedBy: auth.currentUser?.uid || '',
        rejectedByName: auth.currentUser?.displayName || auth.currentUser?.email || 'Unknown Admin',
        rejectedAt: new Date(),
        rejectionReason: rejectionReason.trim()
      };

      setSubmissions(prev =>
        prev.map(submission =>
          submission.id === selectedSubmission.id
            ? updatedSubmission
            : submission
        )
      );

      // Send notification to teacher about the rejection
      try {
        await AttendanceNotificationService.notifyAttendanceRejection(updatedSubmission);
        console.log('Attendance rejection notification sent to teacher');
      } catch (notificationError) {
        console.error('Error sending attendance rejection notification:', notificationError);
        // Continue even if notification fails
      }

      // Show success message
      setSnackbarMessage(translate('admin.attendanceApproval.rejectionSuccess'));
      setSnackbarVisible(true);

      // Close dialog and reset rejection reason
      setRejectionDialogVisible(false);
      setRejectionReason('');

      // Refresh data
      fetchAttendanceSubmissions();

    } catch (error) {
      console.error('Error rejecting submission:', error);
      setSnackbarMessage(translate('admin.attendanceApproval.rejectionError'));
      setSnackbarVisible(true);
    } finally {
      setProcessingAction(false);
    }
  };

  // Render loading state
  const renderLoading = () => (
    <View style={styles.loadingContainer}>
      <ActivityIndicator size="large" color="#1976d2" />
      <Text style={styles.loadingText}>{translate('common.loading')}</Text>
    </View>
  );

  // Render error state
  const renderError = () => (
    <View style={styles.errorContainer}>
      <IconButton
        icon="alert-circle"
        size={48}
        color="#B00020"
      />
      <Text style={styles.errorText}>{error}</Text>
      <Button
        mode="contained"
        onPress={fetchAttendanceSubmissions}
        style={styles.retryButton}
      >
        {translate('common.retry')}
      </Button>
    </View>
  );

  // Render empty state
  const renderEmpty = () => (
    <View style={styles.emptyContainer}>
      <IconButton
        icon="calendar-check"
        size={48}
        color="#BDBDBD"
      />
      <Text style={styles.emptyText}>
        {translate('admin.attendanceApproval.noSubmissions')}
      </Text>
    </View>
  );

  // Render filters
  const renderFilters = () => (
    <Card style={styles.filtersCard}>
      <Card.Content>
        <View style={styles.searchContainer}>
          <Searchbar
            placeholder={translate('admin.attendanceApproval.search')}
            onChangeText={setSearchQuery}
            value={searchQuery}
            style={styles.searchBar}
          />
        </View>

        <View style={styles.filterContainer}>
          <View style={styles.filterGroup}>
            <Text style={styles.filterLabel}>{translate('admin.attendanceApproval.status')}</Text>
            <View style={styles.chipContainer}>
              <Chip
                selected={statusFilter === 'all'}
                onPress={() => setStatusFilter('all')}
                style={styles.filterChip}
              >
                {translate('adminAttendanceApproval.all')}
              </Chip>
              <Chip
                selected={statusFilter === 'pending'}
                onPress={() => setStatusFilter('pending')}
                style={styles.filterChip}
              >
                {translate('adminAttendanceApproval.pending')}
              </Chip>
              <Chip
                selected={statusFilter === 'approved'}
                onPress={() => setStatusFilter('approved')}
                style={styles.filterChip}
              >
                {translate('adminAttendanceApproval.approved')}
              </Chip>
              <Chip
                selected={statusFilter === 'rejected'}
                onPress={() => setStatusFilter('rejected')}
                style={styles.filterChip}
              >
                {translate('adminAttendanceApproval.rejected')}
              </Chip>
            </View>
          </View>

          <View style={styles.filterGroup}>
            <Text style={styles.filterLabel}>{translate('adminAttendanceApproval.date')}</Text>
            <View style={styles.chipContainer}>
              <Chip
                selected={dateFilter === 'all'}
                onPress={() => setDateFilter('all')}
                style={styles.filterChip}
              >
                {translate('adminAttendanceApproval.all')}
              </Chip>
              <Chip
                selected={dateFilter === 'today'}
                onPress={() => setDateFilter('today')}
                style={styles.filterChip}
              >
                {translate('adminAttendanceApproval.today')}
              </Chip>
              <Chip
                selected={dateFilter === 'week'}
                onPress={() => setDateFilter('week')}
                style={styles.filterChip}
              >
                {translate('adminAttendanceApproval.thisWeek')}
              </Chip>
              <Chip
                selected={dateFilter === 'month'}
                onPress={() => setDateFilter('month')}
                style={styles.filterChip}
              >
                {translate('adminAttendanceApproval.thisMonth')}
              </Chip>
            </View>
          </View>
        </View>
      </Card.Content>
    </Card>
  );

  // Render submissions list
  const renderSubmissionsList = () => (
    <Card style={styles.submissionsCard}>
      <Card.Content>
        <DataTable>
          <DataTable.Header>
            <DataTable.Title
              sortDirection={sortBy === 'date' ? sortOrder : null}
              onPress={() => {
                if (sortBy === 'date') {
                  setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
                } else {
                  setSortBy('date');
                  setSortOrder('desc');
                }
              }}
            >
              {translate('adminAttendanceApproval.date')}
            </DataTable.Title>
            <DataTable.Title
              sortDirection={sortBy === 'class' ? sortOrder : null}
              onPress={() => {
                if (sortBy === 'class') {
                  setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
                } else {
                  setSortBy('class');
                  setSortOrder('asc');
                }
              }}
            >
              {translate('adminAttendanceApproval.class')}
            </DataTable.Title>
            <DataTable.Title
              sortDirection={sortBy === 'teacher' ? sortOrder : null}
              onPress={() => {
                if (sortBy === 'teacher') {
                  setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
                } else {
                  setSortBy('teacher');
                  setSortOrder('asc');
                }
              }}
            >
              {translate('adminAttendanceApproval.teacher')}
            </DataTable.Title>
            <DataTable.Title
              sortDirection={sortBy === 'status' ? sortOrder : null}
              onPress={() => {
                if (sortBy === 'status') {
                  setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
                } else {
                  setSortBy('status');
                  setSortOrder('asc');
                }
              }}
            >
              {translate('adminAttendanceApproval.status')}
            </DataTable.Title>
            <DataTable.Title>
              {translate('adminAttendanceApproval.actions')}
            </DataTable.Title>
          </DataTable.Header>

          {filteredSubmissions.map((submission, index) => (
            <DataTable.Row key={submission.id}>
              <DataTable.Cell>
                {new Date(submission.date).toLocaleDateString()}
              </DataTable.Cell>
              <DataTable.Cell>
                {`${submission.className} - ${submission.sectionName}`}
              </DataTable.Cell>
              <DataTable.Cell>
                {submission.teacherName}
              </DataTable.Cell>
              <DataTable.Cell>
                <Chip
                  mode="outlined"
                  style={[
                    styles.statusChip,
                    submission.status === 'approved' ? styles.approvedChip :
                    submission.status === 'rejected' ? styles.rejectedChip :
                    styles.pendingChip
                  ]}
                >
                  {submission.status === 'approved'
                    ? translate('adminAttendanceApproval.approved')
                    : submission.status === 'rejected'
                    ? translate('adminAttendanceApproval.rejected')
                    : translate('adminAttendanceApproval.pending')}
                </Chip>
              </DataTable.Cell>
              <DataTable.Cell>
                <View style={styles.actionsContainer}>
                  <IconButton
                    icon="eye"
                    size={20}
                    onPress={() => viewSubmissionDetails(submission)}
                  />
                  {submission.status === 'pending' && (
                    <>
                      <IconButton
                        icon="check-circle"
                        size={20}
                        color="#4CAF50"
                        onPress={() => handleApproveSubmission(submission)}
                      />
                      <IconButton
                        icon="close-circle"
                        size={20}
                        color="#F44336"
                        onPress={() => handleRejectSubmission(submission)}
                      />
                    </>
                  )}
                </View>
              </DataTable.Cell>
            </DataTable.Row>
          ))}
        </DataTable>
      </Card.Content>
    </Card>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar backgroundColor={'#1976d2'} barStyle="light-content" />

      {/* Admin App Header */}
      <AdminAppHeader
        title={translate('admin.attendanceApproval.title')}
        onMenuPress={toggleDrawer}
        navigation={navigation}
        showNotification={true}
      />

      {/* Admin Sidebar */}
      <AdminSidebar
        visible={drawerOpen}
        onClose={toggleDrawer}
        navigation={navigation}
        activeSidebarItem={activeSidebarItem}
        setActiveSidebarItem={setActiveSidebarItem}
      />

      {/* Sidebar Backdrop */}
      <SidebarBackdrop
        visible={drawerOpen}
        onPress={toggleDrawer}
        fadeAnim={backdropFadeAnim}
      />

      {/* Main Content */}
      <Animated.View
        style={[
          styles.content,
          { opacity: fadeAnim }
        ]}
      >
        <ScrollView style={styles.scrollView}>
          <View style={styles.headerContainer}>
            <Title style={styles.headerTitle}>
              {translate('admin.attendanceApproval.title')}
            </Title>
            <Text style={styles.headerSubtitle}>
              {translate('admin.attendanceApproval.subtitle')}
            </Text>
          </View>

          {renderFilters()}

          {loading ? (
            renderLoading()
          ) : error ? (
            renderError()
          ) : filteredSubmissions.length === 0 ? (
            renderEmpty()
          ) : (
            renderSubmissionsList()
          )}
        </ScrollView>
      </Animated.View>

      {/* Approval Dialog */}
      <Portal>
        <Dialog
          visible={approvalDialogVisible}
          onDismiss={() => setApprovalDialogVisible(false)}
          style={styles.dialog}
        >
          <Dialog.Title>
            {translate('admin.attendanceApproval.approveAttendance')}
          </Dialog.Title>
          <Dialog.Content>
            <Text style={styles.dialogContent}>
              {translate('admin.attendanceApproval.approveConfirmation')}
            </Text>
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={() => setApprovalDialogVisible(false)}>
              {translate('common.cancel')}
            </Button>
            <Button
              mode="contained"
              onPress={approveSubmission}
              loading={processingAction}
              disabled={processingAction}
              style={styles.approveButton}
            >
              {translate('adminAttendanceApproval.approve')}
            </Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>

      {/* Rejection Dialog */}
      <Portal>
        <Dialog
          visible={rejectionDialogVisible}
          onDismiss={() => setRejectionDialogVisible(false)}
          style={styles.dialog}
        >
          <Dialog.Title>
            {translate('admin.attendanceApproval.rejectAttendance')}
          </Dialog.Title>
          <Dialog.Content>
            <Text style={styles.dialogContent}>
              {translate('admin.attendanceApproval.rejectConfirmation')}
            </Text>
            <TextInput
              label={translate('admin.attendanceApproval.rejectReason')}
              value={rejectionReason}
              onChangeText={setRejectionReason}
              mode="outlined"
              multiline
              numberOfLines={3}
              style={styles.rejectionInput}
              placeholder={translate('admin.attendanceApproval.rejectReasonPlaceholder')}
            />
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={() => setRejectionDialogVisible(false)}>
              {translate('common.cancel')}
            </Button>
            <Button
              mode="contained"
              onPress={rejectSubmission}
              loading={processingAction}
              disabled={processingAction || !rejectionReason.trim()}
              style={styles.rejectButton}
            >
              {translate('adminAttendanceApproval.reject')}
            </Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>

      {/* Details Dialog */}
      <Portal>
        <Dialog
          visible={detailsVisible}
          onDismiss={() => setDetailsVisible(false)}
          style={styles.detailsDialog}
        >
          <Dialog.Title>
            {translate('admin.attendanceApproval.attendanceDetails')}
          </Dialog.Title>
          <Dialog.ScrollArea style={styles.dialogScrollArea}>
            <ScrollView>
              {selectedSubmission && (
                <View style={styles.submissionDetails}>
                  <View style={styles.detailRow}>
                    <Text style={styles.detailLabel}>{translate('admin.attendanceApproval.class')}:</Text>
                    <Text style={styles.detailValue}>{selectedSubmission.className} - {selectedSubmission.sectionName}</Text>
                  </View>
                  <View style={styles.detailRow}>
                    <Text style={styles.detailLabel}>{translate('admin.attendanceApproval.date')}:</Text>
                    <Text style={styles.detailValue}>{new Date(selectedSubmission.date).toLocaleDateString()}</Text>
                  </View>
                  <View style={styles.detailRow}>
                    <Text style={styles.detailLabel}>{translate('admin.attendanceApproval.teacher')}:</Text>
                    <Text style={styles.detailValue}>{selectedSubmission.teacherName}</Text>
                  </View>
                  <View style={styles.detailRow}>
                    <Text style={styles.detailLabel}>{translate('admin.attendanceApproval.submittedAt')}:</Text>
                    <Text style={styles.detailValue}>
                      {selectedSubmission.submittedAt instanceof Date
                        ? selectedSubmission.submittedAt.toLocaleString()
                        : new Date(selectedSubmission.submittedAt).toLocaleString()}
                    </Text>
                  </View>
                  <View style={styles.detailRow}>
                    <Text style={styles.detailLabel}>{translate('admin.attendanceApproval.status')}:</Text>
                    <Chip
                      mode="outlined"
                      style={[
                        styles.statusChip,
                        selectedSubmission.status === 'approved' ? styles.approvedChip :
                        selectedSubmission.status === 'rejected' ? styles.rejectedChip :
                        styles.pendingChip
                      ]}
                    >
                      {selectedSubmission.status === 'approved'
                        ? translate('admin.attendanceApproval.approved')
                        : selectedSubmission.status === 'rejected'
                        ? translate('admin.attendanceApproval.rejected')
                        : translate('admin.attendanceApproval.pending')}
                    </Chip>
                  </View>

                  {selectedSubmission.note && (
                    <View style={styles.noteContainer}>
                      <Text style={styles.detailLabel}>{translate('admin.attendanceApproval.notes')}:</Text>
                      <Surface style={styles.noteCard}>
                        <Text style={styles.noteText}>{selectedSubmission.note}</Text>
                      </Surface>
                    </View>
                  )}

                  {selectedSubmission.rejectionReason && (
                    <View style={styles.noteContainer}>
                      <Text style={styles.detailLabel}>{translate('admin.attendanceApproval.rejectReason')}:</Text>
                      <Surface style={styles.noteCard}>
                        <Text style={styles.noteText}>{selectedSubmission.rejectionReason}</Text>
                      </Surface>
                    </View>
                  )}

                  <View style={styles.summaryContainer}>
                    <View style={styles.summaryItem}>
                      <Text style={styles.summaryLabel}>{translate('admin.attendanceApproval.totalStudents')}</Text>
                      <Text style={styles.summaryValue}>{selectedSubmission.summary?.total || 0}</Text>
                    </View>
                    <View style={styles.summaryItem}>
                      <Text style={styles.summaryLabel}>{translate('admin.attendanceApproval.present')}</Text>
                      <Text style={[styles.summaryValue, styles.presentValue]}>{selectedSubmission.summary?.present || 0}</Text>
                    </View>
                    <View style={styles.summaryItem}>
                      <Text style={styles.summaryLabel}>{translate('admin.attendanceApproval.absent')}</Text>
                      <Text style={[styles.summaryValue, styles.absentValue]}>{selectedSubmission.summary?.absent || 0}</Text>
                    </View>
                    <View style={styles.summaryItem}>
                      <Text style={styles.summaryLabel}>{translate('admin.attendanceApproval.late')}</Text>
                      <Text style={[styles.summaryValue, styles.lateValue]}>{selectedSubmission.summary?.late || 0}</Text>
                    </View>
                    <View style={styles.summaryItem}>
                      <Text style={styles.summaryLabel}>{translate('admin.attendanceApproval.excused')}</Text>
                      <Text style={[styles.summaryValue, styles.excusedValue]}>{selectedSubmission.summary?.excused || 0}</Text>
                    </View>
                    <View style={styles.summaryItem}>
                      <Text style={styles.summaryLabel}>{translate('admin.attendanceApproval.attendanceRate')}</Text>
                      <Text style={styles.summaryValue}>
                        {selectedSubmission.summary?.percentage
                          ? `${selectedSubmission.summary.percentage.toFixed(1)}%`
                          : '0%'}
                      </Text>
                    </View>
                  </View>

                  <Divider style={styles.divider} />

                  <Title style={styles.sectionTitle}>{translate('admin.attendanceApproval.students')}</Title>

                  {loading ? (
                    <ActivityIndicator size="large" color={'#1976d2'} style={styles.recordsLoading} />
                  ) : attendanceRecords.length === 0 ? (
                    <Text style={styles.noRecordsText}>{translate('admin.attendanceApproval.noSubmissions')}</Text>
                  ) : (
                    <DataTable>
                      <DataTable.Header>
                        <DataTable.Title style={styles.studentNameColumn}>{translate('admin.attendanceApproval.name')}</DataTable.Title>
                        <DataTable.Title style={styles.statusColumn}>{translate('admin.attendanceApproval.status')}</DataTable.Title>
                        <DataTable.Title style={styles.remarksColumn}>{translate('admin.attendanceApproval.notes')}</DataTable.Title>
                      </DataTable.Header>

                      {attendanceRecords.map(record => (
                        <DataTable.Row key={record.id}>
                          <DataTable.Cell style={styles.studentNameColumn}>
                            <View style={styles.studentInfoContainer}>
                              <Avatar.Text
                                size={32}
                                label={record.studentName.substring(0, 2).toUpperCase()}
                                style={styles.studentAvatar}
                              />
                              <Text style={styles.studentName}>{record.studentName}</Text>
                            </View>
                          </DataTable.Cell>
                          <DataTable.Cell style={styles.statusColumn}>
                            <Chip
                              mode="outlined"
                              icon={
                                record.status === 'present' ? 'check-circle' :
                                record.status === 'absent' ? 'close-circle' :
                                record.status === 'late' ? 'clock' : 'account-clock'
                              }
                              style={[
                                styles.statusChip,
                                record.status === 'present' ? styles.presentChip :
                                record.status === 'absent' ? styles.absentChip :
                                record.status === 'late' ? styles.lateChip : styles.excusedChip
                              ]}
                            >
                              {record.status === 'present'
                                ? translate('admin.attendanceApproval.present')
                                : record.status === 'absent'
                                ? translate('admin.attendanceApproval.absent')
                                : record.status === 'late'
                                ? translate('admin.attendanceApproval.late')
                                : translate('admin.attendanceApproval.excused')}
                            </Chip>
                          </DataTable.Cell>
                          <DataTable.Cell style={styles.remarksColumn}>
                            {record.remarks ? (
                              <Text style={styles.remarksText}>{record.remarks}</Text>
                            ) : (
                              <Text style={styles.noRemarksText}>-</Text>
                            )}
                          </DataTable.Cell>
                        </DataTable.Row>
                      ))}
                    </DataTable>
                  )}
                </View>
              )}
            </ScrollView>
          </Dialog.ScrollArea>
          <Dialog.Actions>
            <Button onPress={() => setDetailsVisible(false)}>
              {translate('common.close')}
            </Button>
            {selectedSubmission && selectedSubmission.status === 'pending' && (
              <>
                <Button
                  mode="contained"
                  onPress={() => {
                    setDetailsVisible(false);
                    handleRejectSubmission(selectedSubmission);
                  }}
                  style={styles.rejectButton}
                >
                  {translate('adminAttendanceApproval.reject')}
                </Button>
                <Button
                  mode="contained"
                  onPress={() => {
                    setDetailsVisible(false);
                    handleApproveSubmission(selectedSubmission);
                  }}
                  style={styles.approveButton}
                >
                  {translate('admin.attendanceApproval.approve')}
                </Button>
              </>
            )}
          </Dialog.Actions>
        </Dialog>
      </Portal>

      {/* Snackbar for notifications */}
      <Snackbar
        visible={snackbarVisible}
        onDismiss={() => setSnackbarVisible(false)}
        duration={3000}
        style={styles.snackbar}
      >
        {snackbarMessage}
      </Snackbar>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  scrollView: {
    flex: 1,
  },
  headerContainer: {
    marginBottom: 16,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  headerSubtitle: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  filtersCard: {
    marginBottom: 16,
    borderRadius: 8,
  },
  searchContainer: {
    marginBottom: 12,
  },
  searchBar: {
    elevation: 0,
    backgroundColor: '#f0f0f0',
  },
  filterContainer: {
    gap: 12,
  },
  filterGroup: {
    marginBottom: 8,
  },
  filterLabel: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  chipContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  filterChip: {
    marginRight: 8,
    marginBottom: 8,
  },
  submissionsCard: {
    borderRadius: 8,
    marginBottom: 16,
  },
  statusChip: {
    height: 28,
  },
  approvedChip: {
    backgroundColor: '#E8F5E9',
    borderColor: '#4CAF50',
  },
  pendingChip: {
    backgroundColor: '#FFF8E1',
    borderColor: '#FFC107',
  },
  rejectedChip: {
    backgroundColor: '#FFEBEE',
    borderColor: '#F44336',
  },
  presentChip: {
    backgroundColor: '#E8F5E9',
    borderColor: '#4CAF50',
  },
  absentChip: {
    backgroundColor: '#FFEBEE',
    borderColor: '#F44336',
  },
  lateChip: {
    backgroundColor: '#FFF8E1',
    borderColor: '#FF9800',
  },
  excusedChip: {
    backgroundColor: '#E1F5FE',
    borderColor: '#03A9F4',
  },
  studentNameColumn: {
    flex: 2,
  },
  statusColumn: {
    flex: 1.5,
    justifyContent: 'center',
  },
  remarksColumn: {
    flex: 2.5,
  },
  studentInfoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  studentAvatar: {
    marginRight: 8,
    backgroundColor: '#1976D2',
  },
  studentName: {
    fontSize: 14,
    fontWeight: '500',
  },
  remarksText: {
    fontSize: 14,
    color: '#333',
  },
  noRemarksText: {
    fontSize: 14,
    color: '#999',
    fontStyle: 'italic',
  },
  summaryContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginVertical: 12,
  },
  summaryItem: {
    width: '30%',
    marginBottom: 12,
    backgroundColor: '#f9f9f9',
    padding: 12,
    borderRadius: 8,
    elevation: 1,
  },
  summaryLabel: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
  },
  summaryValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  presentValue: {
    color: '#4CAF50',
  },
  absentValue: {
    color: '#F44336',
  },
  lateValue: {
    color: '#FF9800',
  },
  excusedValue: {
    color: '#03A9F4',
  },
  actionsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  loadingContainer: {
    padding: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#666',
  },
  errorContainer: {
    padding: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  errorText: {
    marginTop: 12,
    marginBottom: 16,
    fontSize: 16,
    color: '#F44336',
    textAlign: 'center',
  },
  retryButton: {
    marginTop: 8,
  },
  emptyContainer: {
    padding: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyText: {
    marginTop: 12,
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  snackbar: {
    bottom: 16,
  },
  detailsDialog: {
    borderRadius: 12,
    maxWidth: 600,
  },
  dialogScrollArea: {
    maxHeight: '70%',
  },
  submissionDetails: {
    padding: 8,
  },
  detailRow: {
    flexDirection: 'row',
    marginBottom: 12,
    alignItems: 'center',
  },
  detailLabel: {
    fontWeight: 'bold',
    width: 100,
    fontSize: 14,
  },
  detailValue: {
    flex: 1,
    fontSize: 14,
  },
  noteContainer: {
    marginVertical: 12,
  },
  noteCard: {
    padding: 12,
    borderRadius: 8,
    backgroundColor: '#f5f5f5',
    marginTop: 4,
  },
  noteText: {
    fontSize: 14,
    color: '#333',
  },
  sectionTitle: {
    fontSize: 18,
    marginVertical: 12,
  },
  divider: {
    marginVertical: 16,
  },
  recordsLoading: {
    marginVertical: 24,
  },
  noRecordsText: {
    textAlign: 'center',
    marginVertical: 24,
    color: '#666',
  },
  approveButton: {
    backgroundColor: '#4CAF50',
  },
  rejectButton: {
    backgroundColor: '#F44336',
  },
  rejectionInput: {
    marginTop: 16,
  },
});

export default AdminAttendanceApproval;
