import React, { useState, useEffect } from 'react';
import { View, ScrollView, StyleSheet } from 'react-native';
import { Card, Title, DataTable, Portal, Modal, TextInput, Text, ActivityIndicator } from 'react-native-paper';
import { db } from '../../config/firebase';
import { collection, query, where, getDocs, addDoc, updateDoc, doc, getDoc } from 'firebase/firestore';
import CustomButton from '../../components/common/CustomButton';
import { Picker } from '@react-native-picker/picker';

const MarkSheetSubmission = () => {
  const [classes, setClasses] = useState([]);
  const [subjects, setSubjects] = useState([]);
  const [students, setStudents] = useState([]);
  const [selectedClass, setSelectedClass] = useState('');
  const [selectedSubject, setSelectedSubject] = useState('');
  const [examTypes, setExamTypes] = useState(['Midterm', 'Final', 'Assignment', 'Quiz']);
  const [selectedExamType, setSelectedExamType] = useState('');
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedStudent, setSelectedStudent] = useState(null);
  const [markSheet, setMarkSheet] = useState({});
  const [maxMarks, setMaxMarks] = useState('');
  const [remarks, setRemarks] = useState('');

  useEffect(() => {
    fetchClasses();
    fetchSubjects();
  }, []);

  useEffect(() => {
    if (selectedClass) {
      fetchStudents();
      fetchExistingMarkSheet();
    }
  }, [selectedClass, selectedSubject, selectedExamType]);

  const fetchClasses = async () => {
    try {
      const classesRef = collection(db, 'classes');
      const querySnapshot = await getDocs(classesRef);
      
      const classData = [];
      querySnapshot.forEach((doc) => {
        classData.push({ id: doc.id, ...doc.data() });
      });
      
      setClasses(classData);
    } catch (error) {
      console.error('Error fetching classes:', error);
    }
  };

  const fetchSubjects = async () => {
    try {
      const subjectsRef = collection(db, 'subjects');
      const querySnapshot = await getDocs(subjectsRef);
      
      const subjectData = [];
      querySnapshot.forEach((doc) => {
        subjectData.push({ id: doc.id, ...doc.data() });
      });
      
      setSubjects(subjectData);
    } catch (error) {
      console.error('Error fetching subjects:', error);
    }
  };

  const fetchStudents = async () => {
    try {
      setLoading(true);
      const studentsRef = collection(db, 'users');
      const q = query(
        studentsRef,
        where('role', '==', 'student'),
        where('classId', '==', selectedClass)
      );
      const querySnapshot = await getDocs(q);
      
      const studentData = [];
      querySnapshot.forEach((doc) => {
        studentData.push({ id: doc.id, ...doc.data() });
      });
      
      setStudents(studentData);
    } catch (error) {
      console.error('Error fetching students:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchExistingMarkSheet = async () => {
    if (!selectedClass || !selectedSubject || !selectedExamType) return;

    try {
      const markSheetRef = collection(db, 'markSheets');
      const q = query(
        markSheetRef,
        where('classId', '==', selectedClass),
        where('subjectId', '==', selectedSubject),
        where('examType', '==', selectedExamType)
      );
      const querySnapshot = await getDocs(q);
      
      if (!querySnapshot.empty) {
        const markSheetData = querySnapshot.docs[0].data();
        setMarkSheet(markSheetData.marks || {});
        setMaxMarks(markSheetData.maxMarks || '');
      } else {
        setMarkSheet({});
        setMaxMarks('');
      }
    } catch (error) {
      console.error('Error fetching mark sheet:', error);
    }
  };

  const handleSubmitMark = async () => {
    if (!selectedStudent || !selectedClass || !selectedSubject || !selectedExamType) return;

    try {
      const markSheetRef = collection(db, 'markSheets');
      const q = query(
        markSheetRef,
        where('classId', '==', selectedClass),
        where('subjectId', '==', selectedSubject),
        where('examType', '==', selectedExamType)
      );
      const querySnapshot = await getDocs(q);

      const markData = {
        marks: markSheet[selectedStudent.id] || 0,
        remarks: remarks,
        timestamp: new Date().toISOString(),
      };

      if (querySnapshot.empty) {
        await addDoc(markSheetRef, {
          classId: selectedClass,
          subjectId: selectedSubject,
          examType: selectedExamType,
          maxMarks: maxMarks,
          marks: {
            [selectedStudent.id]: markData
          },
          submittedAt: new Date().toISOString(),
        });
      } else {
        const docRef = doc(db, 'markSheets', querySnapshot.docs[0].id);
        await updateDoc(docRef, {
          [`marks.${selectedStudent.id}`]: markData,
          updatedAt: new Date().toISOString(),
        });
      }

      setModalVisible(false);
      setSelectedStudent(null);
      setRemarks('');
      fetchExistingMarkSheet();
    } catch (error) {
      console.error('Error submitting marks:', error);
    }
  };

  const getMarkStatus = (marks) => {
    if (!marks || !maxMarks) return '';
    const percentage = (marks / parseInt(maxMarks)) * 100;
    if (percentage >= 80) return 'Excellent';
    if (percentage >= 60) return 'Good';
    if (percentage >= 40) return 'Pass';
    return 'Needs Improvement';
  };

  return (
    <ScrollView style={styles.container}>
      <Card style={styles.card}>
        <Card.Content>
          <Title>Mark Sheet Submission</Title>

          <View style={styles.filterContainer}>
            <View style={styles.pickerContainer}>
              <Text style={styles.pickerLabel}>Class</Text>
              <Picker
                selectedValue={selectedClass}
                onValueChange={(value) => setSelectedClass(value)}
                style={styles.picker}
              >
                <Picker.Item label="Select Class" value="" />
                {classes.map((cls) => (
                  <Picker.Item key={cls.id} label={cls.name} value={cls.id} />
                ))}
              </Picker>
            </View>

            <View style={styles.pickerContainer}>
              <Text style={styles.pickerLabel}>Subject</Text>
              <Picker
                selectedValue={selectedSubject}
                onValueChange={(value) => setSelectedSubject(value)}
                style={styles.picker}
              >
                <Picker.Item label="Select Subject" value="" />
                {subjects.map((subject) => (
                  <Picker.Item key={subject.id} label={subject.name} value={subject.id} />
                ))}
              </Picker>
            </View>

            <View style={styles.pickerContainer}>
              <Text style={styles.pickerLabel}>Exam Type</Text>
              <Picker
                selectedValue={selectedExamType}
                onValueChange={(value) => setSelectedExamType(value)}
                style={styles.picker}
              >
                <Picker.Item label="Select Exam Type" value="" />
                {examTypes.map((type) => (
                  <Picker.Item key={type} label={type} value={type} />
                ))}
              </Picker>
            </View>

            <TextInput
              label="Maximum Marks"
              value={maxMarks}
              onChangeText={setMaxMarks}
              keyboardType="numeric"
              style={styles.input}
            />
          </View>

          {loading ? (
            <ActivityIndicator style={styles.loader} />
          ) : (
            <DataTable>
              <DataTable.Header>
                <DataTable.Title>Student Name</DataTable.Title>
                <DataTable.Title numeric>Marks</DataTable.Title>
                <DataTable.Title>Status</DataTable.Title>
                <DataTable.Title>Actions</DataTable.Title>
              </DataTable.Header>

              {students.map((student) => (
                <DataTable.Row key={student.id}>
                  <DataTable.Cell>{student.displayName}</DataTable.Cell>
                  <DataTable.Cell numeric>
                    {markSheet[student.id]?.marks || '-'}
                  </DataTable.Cell>
                  <DataTable.Cell>
                    {getMarkStatus(markSheet[student.id]?.marks)}
                  </DataTable.Cell>
                  <DataTable.Cell>
                    <CustomButton
                      mode="outlined"
                      onPress={() => {
                        setSelectedStudent(student);
                        setRemarks(markSheet[student.id]?.remarks || '');
                        setModalVisible(true);
                      }}
                      style={styles.actionButton}
                    >
                      Enter Marks
                    </CustomButton>
                  </DataTable.Cell>
                </DataTable.Row>
              ))}
            </DataTable>
          )}
        </Card.Content>
      </Card>

      <Portal>
        <Modal
          visible={modalVisible}
          onDismiss={() => setModalVisible(false)}
          contentContainerStyle={styles.modalContent}
        >
          {selectedStudent && (
            <View>
              <Title>Enter Marks for {selectedStudent.displayName}</Title>
              
              <TextInput
                label="Marks"
                value={String(markSheet[selectedStudent.id]?.marks || '')}
                onChangeText={(text) => {
                  setMarkSheet({
                    ...markSheet,
                    [selectedStudent.id]: {
                      ...markSheet[selectedStudent.id],
                      marks: text,
                    },
                  });
                }}
                keyboardType="numeric"
                style={styles.input}
              />

              <TextInput
                label="Remarks"
                value={remarks}
                onChangeText={setRemarks}
                multiline
                numberOfLines={3}
                style={styles.input}
              />

              <View style={styles.buttonContainer}>
                <CustomButton
                  mode="outlined"
                  onPress={() => setModalVisible(false)}
                  style={[styles.button, styles.cancelButton]}
                >
                  Cancel
                </CustomButton>
                <CustomButton
                  mode="contained"
                  onPress={handleSubmitMark}
                  style={[styles.button, styles.saveButton]}
                >
                  Submit
                </CustomButton>
              </View>
            </View>
          )}
        </Modal>
      </Portal>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  card: {
    margin: 16,
  },
  filterContainer: {
    marginBottom: 20,
  },
  pickerContainer: {
    marginBottom: 16,
  },
  pickerLabel: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
  },
  picker: {
    backgroundColor: '#f5f5f5',
    borderRadius: 4,
  },
  input: {
    marginBottom: 16,
  },
  loader: {
    marginVertical: 20,
  },
  actionButton: {
    marginHorizontal: 4,
  },
  modalContent: {
    backgroundColor: 'white',
    padding: 20,
    margin: 20,
    borderRadius: 8,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: 8,
    marginTop: 16,
  },
  button: {
    minWidth: 100,
  },
  cancelButton: {
    borderColor: '#666',
  },
  saveButton: {
    backgroundColor: '#2196F3',
  },
});

export default MarkSheetSubmission;
