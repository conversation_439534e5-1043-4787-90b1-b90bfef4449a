import { DefaultTheme } from 'react-native-paper';
import { Dimensions, Platform } from 'react-native';

const { width, height } = Dimensions.get('window');

// Responsive sizing helper
const scale = size => (width / 375) * size;

// Color palette
const colors = {
  // Primary colors
  primary: '#1976d2',
  primaryLight: '#63a4ff',
  primaryDark: '#004ba0',
  
  // Secondary colors
  secondary: '#ff9800',
  secondaryLight: '#ffc947',
  secondaryDark: '#c66900',
  
  // Accent colors
  accent: '#4caf50',
  
  // Role-specific colors
  student: '#5c6bc0', // Indigo
  teacher: '#26a69a', // Teal
  parent: '#7e57c2', // Deep Purple
  admin: '#ec407a', // Pink
  
  // Semantic colors
  success: '#4caf50',
  warning: '#ff9800',
  error: '#f44336',
  info: '#2196f3',
  
  // Neutral colors
  background: '#f5f5f5',
  surface: '#ffffff',
  surfaceVariant: '#f0f0f0',
  divider: '#e0e0e0',
  
  // Text colors
  text: '#212121',
  textSecondary: '#757575',
  textDisabled: '#9e9e9e',
  textOnPrimary: '#ffffff',
  textOnSecondary: '#000000',
  
  // Status colors
  active: '#4caf50',
  inactive: '#9e9e9e',
  pending: '#ff9800',
  
  // Transparent colors for overlays
  overlay: 'rgba(0, 0, 0, 0.5)',
  scrim: 'rgba(0, 0, 0, 0.32)',
};

// Spacing scale
const spacing = {
  xs: scale(4),
  s: scale(8),
  m: scale(16),
  l: scale(24),
  xl: scale(32),
  xxl: scale(48),
};

// Border radius
const borderRadius = {
  xs: scale(2),
  s: scale(4),
  m: scale(8),
  l: scale(12),
  xl: scale(16),
  xxl: scale(24),
  round: scale(999),
};

// Typography
const fontConfig = {
  regular: {
    fontFamily: Platform.OS === 'ios' ? 'System' : 'Roboto',
    fontWeight: 'normal',
  },
  medium: {
    fontFamily: Platform.OS === 'ios' ? 'System' : 'Roboto',
    fontWeight: '500',
  },
  bold: {
    fontFamily: Platform.OS === 'ios' ? 'System' : 'Roboto',
    fontWeight: 'bold',
  },
  light: {
    fontFamily: Platform.OS === 'ios' ? 'System' : 'Roboto',
    fontWeight: '300',
  },
};

const typography = {
  displayLarge: {
    ...fontConfig.light,
    fontSize: scale(28),
    letterSpacing: 0,
    lineHeight: scale(34),
  },
  displayMedium: {
    ...fontConfig.light,
    fontSize: scale(24),
    letterSpacing: 0,
    lineHeight: scale(30),
  },
  displaySmall: {
    ...fontConfig.regular,
    fontSize: scale(20),
    letterSpacing: 0,
    lineHeight: scale(26),
  },
  headlineLarge: {
    ...fontConfig.medium,
    fontSize: scale(18),
    letterSpacing: 0,
    lineHeight: scale(24),
  },
  headlineMedium: {
    ...fontConfig.medium,
    fontSize: scale(16),
    letterSpacing: 0.15,
    lineHeight: scale(22),
  },
  headlineSmall: {
    ...fontConfig.medium,
    fontSize: scale(14),
    letterSpacing: 0.1,
    lineHeight: scale(20),
  },
  bodyLarge: {
    ...fontConfig.regular,
    fontSize: scale(16),
    letterSpacing: 0.15,
    lineHeight: scale(24),
  },
  bodyMedium: {
    ...fontConfig.regular,
    fontSize: scale(14),
    letterSpacing: 0.25,
    lineHeight: scale(20),
  },
  bodySmall: {
    ...fontConfig.regular,
    fontSize: scale(12),
    letterSpacing: 0.4,
    lineHeight: scale(16),
  },
  labelLarge: {
    ...fontConfig.medium,
    fontSize: scale(14),
    letterSpacing: 0.1,
    lineHeight: scale(20),
  },
  labelMedium: {
    ...fontConfig.medium,
    fontSize: scale(12),
    letterSpacing: 0.5,
    lineHeight: scale(16),
  },
  labelSmall: {
    ...fontConfig.medium,
    fontSize: scale(10),
    letterSpacing: 0.5,
    lineHeight: scale(14),
  },
};

// Elevation (shadows)
const elevation = {
  level0: {
    elevation: 0,
    shadowColor: 'transparent',
  },
  level1: {
    elevation: 1,
    shadowColor: colors.text,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.18,
    shadowRadius: 1.0,
  },
  level2: {
    elevation: 2,
    shadowColor: colors.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
  },
  level3: {
    elevation: 3,
    shadowColor: colors.text,
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
  },
  level4: {
    elevation: 4,
    shadowColor: colors.text,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  level5: {
    elevation: 5,
    shadowColor: colors.text,
    shadowOffset: { width: 0, height: 5 },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,
  },
};

// Animation durations
const animation = {
  scale: 0.9,
  fast: 200,
  normal: 300,
  slow: 500,
};

// Create the theme
const mobileTheme = {
  ...DefaultTheme,
  dark: false,
  roundness: borderRadius.m,
  colors: {
    ...DefaultTheme.colors,
    ...colors,
  },
  spacing,
  borderRadius,
  typography,
  elevation,
  animation,
  isRTL: false, // Will be set dynamically based on language
  
  // Helper functions
  getElevation: level => elevation[`level${level}`] || elevation.level1,
  getSpacing: size => spacing[size] || spacing.m,
  getBorderRadius: size => borderRadius[size] || borderRadius.m,
};

export default mobileTheme;
