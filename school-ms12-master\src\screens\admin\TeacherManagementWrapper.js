import React from 'react';
import TeacherManagement from './TeacherManagement';
import { useLanguage } from '../../context/LanguageContext';

const TeacherManagementWrapper = (props) => {
  // Check if language context is available
  let contextAvailable = true;
  try {
    useLanguage();
  } catch (error) {
    contextAvailable = false;
    console.log('Language context not available in TeacherManagementWrapper:', error.message);
  }

  // Simply pass through to the TeacherManagement component
  // The context should be provided at a higher level in the app
  return <TeacherManagement {...props} />;
};

export default TeacherManagementWrapper;
