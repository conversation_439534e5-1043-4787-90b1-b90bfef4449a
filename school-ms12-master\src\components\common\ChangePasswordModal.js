import React, { useState } from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import { Modal, Portal, Title, Text, useTheme, TextInput } from 'react-native-paper';
import CustomInput from './CustomInput';
import CustomButton from './CustomButton';
import { useLanguage } from '../../context/LanguageContext';
import { EmailAuthProvider, reauthenticateWithCredential, updatePassword } from 'firebase/auth';
import { auth, db } from '../../config/firebase';
import { collection, addDoc, serverTimestamp } from 'firebase/firestore';
import * as Animatable from 'react-native-animatable';
import { MaterialCommunityIcons } from '@expo/vector-icons';

const ChangePasswordModal = ({ visible, onDismiss, onSuccess }) => {
  const theme = useTheme();
  const { translate, getTextStyle } = useLanguage();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const [passwordForm, setPasswordForm] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  });
  const [passwordVisibility, setPasswordVisibility] = useState({
    currentPassword: false,
    newPassword: false,
    confirmPassword: false,
  });

  // Password validation
  const [validations, setValidations] = useState({
    length: false,
    uppercase: false,
    lowercase: false,
    number: false,
    special: false,
    match: false,
  });

  // Update password validations when the password changes
  const validatePassword = (password, confirmPassword) => {
    const newValidations = {
      length: password.length >= 8,
      uppercase: /[A-Z]/.test(password),
      lowercase: /[a-z]/.test(password),
      number: /[0-9]/.test(password),
      special: /[^A-Za-z0-9]/.test(password),
      match: password === confirmPassword && password !== '',
    };
    setValidations(newValidations);
    return Object.values(newValidations).every(v => v);
  };

  const handlePasswordChange = (field, value) => {
    const updatedForm = { ...passwordForm, [field]: value };
    setPasswordForm(updatedForm);
    
    if (field === 'newPassword' || field === 'confirmPassword') {
      validatePassword(
        field === 'newPassword' ? value : passwordForm.newPassword,
        field === 'confirmPassword' ? value : passwordForm.confirmPassword
      );
    }
  };

  const togglePasswordVisibility = (field) => {
    setPasswordVisibility({
      ...passwordVisibility,
      [field]: !passwordVisibility[field],
    });
  };

  const handleChangePassword = async () => {
    try {
      setLoading(true);
      setError('');
      
      // Validate passwords
      if (!validatePassword(passwordForm.newPassword, passwordForm.confirmPassword)) {
        setError(translate('profileManagement.passwordRequirements'));
        setLoading(false);
        return;
      }

      const user = auth.currentUser;
      if (!user) {
        throw new Error(translate('errors.noUserSignedIn'));
      }

      // Re-authenticate user
      const credential = EmailAuthProvider.credential(
        user.email,
        passwordForm.currentPassword
      );
      
      await reauthenticateWithCredential(user, credential);
      
      // Update password
      await updatePassword(user, passwordForm.newPassword);
      
      // Log the password change
      await addDoc(collection(db, 'password_change_logs'), {
        userId: user.uid,
        email: user.email,
        timestamp: serverTimestamp()
      });
      
      // Show success message
      setSuccess(true);
      
      // Reset form
      setPasswordForm({
        currentPassword: '',
        newPassword: '',
        confirmPassword: '',
      });
      
      // Call onSuccess callback if provided
      if (onSuccess) {
        setTimeout(() => {
          onSuccess();
        }, 2000);
      }
    } catch (error) {
      console.error('Error changing password:', error);
      
      let errorMessage = translate('errors.passwordChangeFailed');
      
      if (error.code === 'auth/wrong-password') {
        errorMessage = translate('errors.incorrectCurrentPassword');
      } else if (error.code === 'auth/weak-password') {
        errorMessage = translate('errors.weakPassword');
      } else if (error.code === 'auth/requires-recent-login') {
        errorMessage = translate('errors.recentLoginRequired');
      }
      
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const renderValidationItem = (valid, text) => (
    <View style={styles.validationItem}>
      <MaterialCommunityIcons
        name={valid ? 'check-circle' : 'circle-outline'}
        size={16}
        color={valid ? theme.colors.success : theme.colors.placeholder}
      />
      <Text style={[
        styles.validationText,
        { color: valid ? theme.colors.success : theme.colors.placeholder }
      ]}>
        {text}
      </Text>
    </View>
  );

  return (
    <Portal>
      <Modal
        visible={visible}
        onDismiss={onDismiss}
        contentContainerStyle={[styles.modalContent, { backgroundColor: theme.colors.surface }]}
      >
        <ScrollView>
          <Title style={[styles.title, getTextStyle()]}>{translate('profileManagement.changePassword')}</Title>
          
          {success ? (
            <Animatable.View animation="fadeIn" duration={500} style={styles.successContainer}>
              <MaterialCommunityIcons
                name="check-circle"
                size={60}
                color={theme.colors.success}
              />
              <Text style={[styles.successText, getTextStyle()]}>{translate('profileManagement.passwordChangeSuccess')}</Text>
            </Animatable.View>
          ) : (
            <>
              {error ? (
                <Animatable.View animation="shake" duration={500}>
                  <Text style={[styles.errorText, getTextStyle()]}>{error}</Text>
                </Animatable.View>
              ) : null}
              
              <CustomInput
                label={translate('profileManagement.currentPassword')}
                value={passwordForm.currentPassword}
                onChangeText={(text) => handlePasswordChange('currentPassword', text)}
                secureTextEntry={!passwordVisibility.currentPassword}
                right={
                  <TextInput.Icon
                    name={passwordVisibility.currentPassword ? 'eye-off' : 'eye'}
                    onPress={() => togglePasswordVisibility('currentPassword')}
                  />
                }
              />
              
              <CustomInput
                label={translate('profileManagement.newPassword')}
                value={passwordForm.newPassword}
                onChangeText={(text) => handlePasswordChange('newPassword', text)}
                secureTextEntry={!passwordVisibility.newPassword}
                right={
                  <TextInput.Icon
                    name={passwordVisibility.newPassword ? 'eye-off' : 'eye'}
                    onPress={() => togglePasswordVisibility('newPassword')}
                  />
                }
              />
              
              <CustomInput
                label={translate('profileManagement.confirmPassword')}
                value={passwordForm.confirmPassword}
                onChangeText={(text) => handlePasswordChange('confirmPassword', text)}
                secureTextEntry={!passwordVisibility.confirmPassword}
                right={
                  <TextInput.Icon
                    name={passwordVisibility.confirmPassword ? 'eye-off' : 'eye'}
                    onPress={() => togglePasswordVisibility('confirmPassword')}
                  />
                }
              />
              
              <View style={styles.validationContainer}>
                <Text style={[styles.validationTitle, getTextStyle()]}>{translate('profileManagement.passwordRequirements')}</Text>
                {renderValidationItem(validations.length, translate('profileManagement.passwordLength'))}
                {renderValidationItem(validations.uppercase, translate('profileManagement.passwordUppercase'))}
                {renderValidationItem(validations.lowercase, translate('profileManagement.passwordLowercase'))}
                {renderValidationItem(validations.number, translate('profileManagement.passwordNumber'))}
                {renderValidationItem(validations.special, translate('profileManagement.passwordSpecial'))}
                {renderValidationItem(validations.match, translate('profileManagement.passwordMatch'))}
              </View>
              
              <View style={styles.buttonContainer}>
                <CustomButton
                  mode="contained"
                  onPress={handleChangePassword}
                  loading={loading}
                  disabled={
                    !passwordForm.currentPassword ||
                    !passwordForm.newPassword ||
                    !passwordForm.confirmPassword ||
                    !Object.values(validations).every(v => v)
                  }
                  style={styles.saveButton}
                >
                  {translate('profileManagement.saveChanges')}
                </CustomButton>
                
                <CustomButton
                  mode="outlined"
                  onPress={onDismiss}
                  style={styles.cancelButton}
                >
                  {translate('common.cancel')}
                </CustomButton>
              </View>
            </>
          )}
        </ScrollView>
      </Modal>
    </Portal>
  );
};

const styles = StyleSheet.create({
  modalContent: {
    margin: 20,
    padding: 20,
    borderRadius: 8,
    maxHeight: '80%',
  },
  title: {
    marginBottom: 20,
    textAlign: 'center',
    fontWeight: 'bold',
  },
  errorText: {
    color: 'red',
    marginBottom: 15,
    textAlign: 'center',
  },
  validationContainer: {
    marginTop: 10,
    marginBottom: 20,
    padding: 10,
    borderRadius: 8,
    backgroundColor: 'rgba(0, 0, 0, 0.03)',
  },
  validationTitle: {
    fontWeight: 'bold',
    marginBottom: 8,
  },
  validationItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 4,
  },
  validationText: {
    marginLeft: 8,
    fontSize: 12,
  },
  buttonContainer: {
    marginTop: 10,
  },
  saveButton: {
    marginBottom: 10,
  },
  cancelButton: {
    marginBottom: 10,
  },
  successContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  successText: {
    marginTop: 20,
    fontSize: 16,
    textAlign: 'center',
  },
});

export default ChangePasswordModal;
