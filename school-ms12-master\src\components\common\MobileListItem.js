import React from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import { Surface, Text, Avatar, Divider, Badge } from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import * as Animatable from 'react-native-animatable';
import { Swipeable } from 'react-native-gesture-handler';
import mobileTheme from '../../theme/mobileTheme';
import { useLanguage } from '../../context/LanguageContext';

/**
 * Mobile-optimized list item component with swipe actions
 * @param {Object} props - Component props
 * @param {string} props.title - Item title
 * @param {string} props.subtitle - Item subtitle
 * @param {string} props.description - Item description
 * @param {string} props.icon - Icon name
 * @param {string} props.avatarText - Text to display in avatar
 * @param {string} props.avatarImage - Image URL for avatar
 * @param {string} props.avatarColor - Background color for avatar
 * @param {Array} props.rightActions - Right swipe actions
 * @param {Array} props.leftActions - Left swipe actions
 * @param {Function} props.onPress - Function to call when item is pressed
 * @param {Function} props.onLongPress - Function to call when item is long pressed
 * @param {boolean} props.showDivider - Whether to show divider
 * @param {string} props.status - Status indicator (active, inactive, pending)
 * @param {number} props.badgeCount - Badge count
 * @param {string} props.badgeColor - Badge color
 * @param {Object} props.style - Additional styles
 */
const MobileListItem = ({
  title,
  subtitle,
  description,
  icon,
  avatarText,
  avatarImage,
  avatarColor = mobileTheme.colors.primary,
  rightActions = [],
  leftActions = [],
  onPress,
  onLongPress,
  showDivider = true,
  status,
  badgeCount,
  badgeColor = mobileTheme.colors.notification,
  style,
}) => {
  const { translate, getTextStyle, isRTL } = useLanguage();
  
  // Render right swipe actions
  const renderRightActions = () => {
    if (rightActions.length === 0) return null;
    
    return (
      <View style={styles.actionsContainer}>
        {rightActions.map((action, index) => (
          <TouchableOpacity
            key={`right-action-${index}`}
            style={[
              styles.actionButton,
              { backgroundColor: action.color || mobileTheme.colors.primary }
            ]}
            onPress={action.onPress}
          >
            <MaterialCommunityIcons
              name={action.icon}
              size={24}
              color="white"
            />
            {action.label && (
              <Text style={styles.actionLabel}>
                {translate(action.label) || action.label}
              </Text>
            )}
          </TouchableOpacity>
        ))}
      </View>
    );
  };
  
  // Render left swipe actions
  const renderLeftActions = () => {
    if (leftActions.length === 0) return null;
    
    return (
      <View style={styles.actionsContainer}>
        {leftActions.map((action, index) => (
          <TouchableOpacity
            key={`left-action-${index}`}
            style={[
              styles.actionButton,
              { backgroundColor: action.color || mobileTheme.colors.primary }
            ]}
            onPress={action.onPress}
          >
            <MaterialCommunityIcons
              name={action.icon}
              size={24}
              color="white"
            />
            {action.label && (
              <Text style={styles.actionLabel}>
                {translate(action.label) || action.label}
              </Text>
            )}
          </TouchableOpacity>
        ))}
      </View>
    );
  };
  
  // Render avatar
  const renderAvatar = () => {
    if (avatarImage) {
      return (
        <Avatar.Image
          size={40}
          source={{ uri: avatarImage }}
          style={styles.avatar}
        />
      );
    } else if (avatarText) {
      return (
        <Avatar.Text
          size={40}
          label={avatarText}
          style={[styles.avatar, { backgroundColor: avatarColor }]}
        />
      );
    } else if (icon) {
      return (
        <Avatar.Icon
          size={40}
          icon={icon}
          style={[styles.avatar, { backgroundColor: avatarColor }]}
        />
      );
    }
    
    return null;
  };
  
  // Render status indicator
  const renderStatus = () => {
    if (!status) return null;
    
    let statusColor;
    switch(status) {
      case 'active': statusColor = mobileTheme.colors.active; break;
      case 'inactive': statusColor = mobileTheme.colors.inactive; break;
      case 'pending': statusColor = mobileTheme.colors.pending; break;
      default: statusColor = status; // Use status as color if not a known status
    }
    
    return (
      <View style={[styles.statusIndicator, { backgroundColor: statusColor }]} />
    );
  };
  
  return (
    <Swipeable
      renderRightActions={renderRightActions}
      renderLeftActions={renderLeftActions}
      enabled={rightActions.length > 0 || leftActions.length > 0}
    >
      <Surface style={[styles.container, style]}>
        <TouchableOpacity
          onPress={onPress}
          onLongPress={onLongPress}
          activeOpacity={0.7}
          style={styles.touchable}
        >
          <View style={[styles.content, isRTL && styles.contentRTL]}>
            {/* Avatar/Icon */}
            <View style={styles.avatarContainer}>
              {renderAvatar()}
              {renderStatus()}
            </View>
            
            {/* Text content */}
            <View style={[styles.textContainer, isRTL && styles.textContainerRTL]}>
              <View style={[styles.titleRow, isRTL && styles.titleRowRTL]}>
                <Text
                  style={[
                    styles.title,
                    getTextStyle({ fontSize: 16 })
                  ]}
                  numberOfLines={1}
                >
                  {translate(title) || title}
                </Text>
                
                {badgeCount > 0 && (
                  <Badge
                    size={20}
                    style={[styles.badge, { backgroundColor: badgeColor }]}
                  >
                    {badgeCount > 99 ? '99+' : badgeCount}
                  </Badge>
                )}
              </View>
              
              {subtitle && (
                <Text
                  style={[
                    styles.subtitle,
                    getTextStyle({ fontSize: 14 })
                  ]}
                  numberOfLines={1}
                >
                  {translate(subtitle) || subtitle}
                </Text>
              )}
              
              {description && (
                <Text
                  style={[
                    styles.description,
                    getTextStyle({ fontSize: 12 })
                  ]}
                  numberOfLines={2}
                >
                  {translate(description) || description}
                </Text>
              )}
            </View>
            
            {/* Chevron icon */}
            <MaterialCommunityIcons
              name={isRTL ? "chevron-left" : "chevron-right"}
              size={24}
              color={mobileTheme.colors.disabled}
              style={styles.chevron}
            />
          </View>
        </TouchableOpacity>
        
        {showDivider && <Divider style={styles.divider} />}
      </Surface>
    </Swipeable>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: mobileTheme.colors.surface,
  },
  touchable: {
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  contentRTL: {
    flexDirection: 'row-reverse',
  },
  avatarContainer: {
    position: 'relative',
    marginRight: 16,
  },
  avatar: {
    marginRight: 0,
  },
  statusIndicator: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 12,
    height: 12,
    borderRadius: 6,
    borderWidth: 2,
    borderColor: mobileTheme.colors.surface,
  },
  textContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  textContainerRTL: {
    alignItems: 'flex-end',
  },
  titleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  titleRowRTL: {
    flexDirection: 'row-reverse',
  },
  title: {
    fontSize: 16,
    fontWeight: '500',
    color: mobileTheme.colors.text,
    flex: 1,
  },
  subtitle: {
    fontSize: 14,
    color: mobileTheme.colors.textSecondary,
    marginTop: 2,
  },
  description: {
    fontSize: 12,
    color: mobileTheme.colors.textSecondary,
    marginTop: 4,
  },
  badge: {
    marginLeft: 8,
  },
  chevron: {
    marginLeft: 8,
  },
  divider: {
    marginLeft: 72,
  },
  actionsContainer: {
    flexDirection: 'row',
    width: 192,
    height: '100%',
  },
  actionButton: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  actionLabel: {
    color: 'white',
    fontSize: 12,
    marginTop: 4,
  },
});

export default MobileListItem;
