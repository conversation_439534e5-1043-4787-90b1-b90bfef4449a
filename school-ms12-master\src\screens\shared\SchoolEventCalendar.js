import React, { useState, useEffect } from 'react';
import { View, ScrollView, StyleSheet } from 'react-native';
import { Card, Title, Text, Portal, Modal, TextInput, ActivityIndicator, Chip, IconButton, useTheme } from 'react-native-paper';
import { db, auth } from '../../config/firebase';
import { collection, query, where, getDocs, addDoc, doc, getDoc, updateDoc, deleteDoc, orderBy } from 'firebase/firestore';
import CustomButton from '../../components/common/CustomButton';
import { Agenda } from 'react-native-calendars';
import EthiopianCalendar from '../../utils/EthiopianCalendar';
import EthiopianDatePicker from '../../components/common/EthiopianDatePicker';

const SchoolEventCalendar = ({ isAdmin = false }) => {
  const [events, setEvents] = useState({});
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState(null);
  const [loading, setLoading] = useState(true);
  const [eventForm, setEventForm] = useState({
    title: '',
    description: '',
    date: new Date().toISOString(),
    startTime: '',
    endTime: '',
    location: '',
    type: 'general', // general, academic, extracurricular
    participants: [], // classes or roles involved
    status: 'upcoming',
  });
  // No theme needed
  // Default to English for this component
  const language = 'en';

  useEffect(() => {
    fetchEvents();
  }, []);

  const fetchEvents = async () => {
    try {
      setLoading(true);

      const eventsRef = collection(db, 'events');
      const q = query(eventsRef, orderBy('date'));
      const querySnapshot = await getDocs(q);

      const eventData = {};
      querySnapshot.forEach((doc) => {
        const event = { id: doc.id, ...doc.data() };
        const dateStr = event.date.split('T')[0];

        if (!eventData[dateStr]) {
          eventData[dateStr] = [];
        }
        eventData[dateStr].push(event);
      });

      setEvents(eventData);
      setLoading(false);
    } catch (error) {
      console.error('Error fetching events:', error);
      setLoading(false);
    }
  };

  const handleAddEvent = async () => {
    try {
      setLoading(true);

      const newEvent = {
        ...eventForm,
        createdBy: auth.currentUser.uid,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      const docRef = await addDoc(collection(db, 'events'), newEvent);

      // Update local state
      const dateStr = newEvent.date.split('T')[0];
      setEvents(prev => ({
        ...prev,
        [dateStr]: [...(prev[dateStr] || []), { id: docRef.id, ...newEvent }],
      }));

      setModalVisible(false);
      resetForm();
    } catch (error) {
      console.error('Error adding event:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateEvent = async () => {
    try {
      setLoading(true);

      const updatedEvent = {
        ...eventForm,
        updatedAt: new Date().toISOString(),
      };

      await updateDoc(doc(db, 'events', selectedEvent.id), updatedEvent);

      // Update local state
      const oldDateStr = selectedEvent.date.split('T')[0];
      const newDateStr = updatedEvent.date.split('T')[0];

      setEvents(prev => {
        const newEvents = { ...prev };

        // Remove from old date
        if (newEvents[oldDateStr]) {
          newEvents[oldDateStr] = newEvents[oldDateStr].filter(e => e.id !== selectedEvent.id);
          if (newEvents[oldDateStr].length === 0) {
            delete newEvents[oldDateStr];
          }
        }

        // Add to new date
        if (!newEvents[newDateStr]) {
          newEvents[newDateStr] = [];
        }
        newEvents[newDateStr].push({ id: selectedEvent.id, ...updatedEvent });

        return newEvents;
      });

      setModalVisible(false);
      setSelectedEvent(null);
      resetForm();
    } catch (error) {
      console.error('Error updating event:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteEvent = async () => {
    try {
      setLoading(true);

      await deleteDoc(doc(db, 'events', selectedEvent.id));

      // Update local state
      const dateStr = selectedEvent.date.split('T')[0];
      setEvents(prev => {
        const newEvents = { ...prev };
        newEvents[dateStr] = newEvents[dateStr].filter(e => e.id !== selectedEvent.id);
        if (newEvents[dateStr].length === 0) {
          delete newEvents[dateStr];
        }
        return newEvents;
      });

      setModalVisible(false);
      setSelectedEvent(null);
      resetForm();
    } catch (error) {
      console.error('Error deleting event:', error);
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setEventForm({
      title: '',
      description: '',
      date: new Date().toISOString(),
      startTime: '',
      endTime: '',
      location: '',
      type: 'general',
      participants: [],
      status: 'upcoming',
    });
  };

  // Format date using Ethiopian calendar with language context
  const formatDate = (date) => {
    if (!date) return '';
    try {
      return EthiopianCalendar.formatDate(new Date(date), language);
    } catch (error) {
      console.error('Error formatting date:', error);
      return String(date);
    }
  };

  const renderEvent = (event) => {
    const typeColors = {
      general: '#2196F3',
      academic: '#4CAF50',
      extracurricular: '#FF9800',
    };

    return (
      <Card
        style={[styles.eventCard, { borderLeftColor: typeColors[event.type] }]}
        onPress={() => {
          if (isAdmin) {
            setSelectedEvent(event);
            setEventForm(event);
            setModalVisible(true);
          }
        }}
      >
        <Card.Content>
          <View style={styles.eventHeader}>
            <Title style={styles.eventTitle}>{event.title}</Title>
            <Chip style={{ backgroundColor: typeColors[event.type] }}>
              {event.type.charAt(0).toUpperCase() + event.type.slice(1)}
            </Chip>
          </View>

          <Text style={styles.eventTime}>
            {event.startTime} - {event.endTime}
          </Text>
          <Text style={styles.eventLocation}>{event.location}</Text>
          <Text style={styles.eventDescription}>{event.description}</Text>

          {event.participants?.length > 0 && (
            <View style={styles.participants}>
              <Text style={styles.participantsTitle}>Participants:</Text>
              <View style={styles.chipContainer}>
                {event.participants.map((participant, index) => (
                  <Chip key={index} style={styles.participantChip}>
                    {participant}
                  </Chip>
                ))}
              </View>
            </View>
          )}
        </Card.Content>
      </Card>
    );
  };

  return (
    <View style={styles.container}>
      {loading ? (
        <ActivityIndicator style={styles.loader} />
      ) : (
        <>
          {isAdmin && (
            <CustomButton
              mode="contained"
              onPress={() => {
                setSelectedEvent(null);
                setModalVisible(true);
              }}
              style={styles.addButton}
            >
              Add Event
            </CustomButton>
          )}

          <Agenda
            items={events}
            renderItem={renderEvent}
            theme={{
              selectedDayBackgroundColor: '#1976d2',
              selectedDayTextColor: '#ffffff',
              todayTextColor: '#1976d2',
              dotColor: '#1976d2',
              agendaDayTextColor: '#1976d2',
              agendaDayNumColor: '#1976d2',
              agendaTodayColor: '#1976d2',
            }}
          />

          <Portal>
            <Modal
              visible={modalVisible}
              onDismiss={() => {
                setModalVisible(false);
                setSelectedEvent(null);
                resetForm();
              }}
              contentContainerStyle={styles.modalContent}
            >
              <ScrollView>
                <Title>{selectedEvent ? 'Edit Event' : 'Add Event'}</Title>

                <TextInput
                  label="Title"
                  value={eventForm.title}
                  onChangeText={(text) => setEventForm({ ...eventForm, title: text })}
                  style={styles.input}
                />

                <TextInput
                  label="Description"
                  value={eventForm.description}
                  onChangeText={(text) => setEventForm({ ...eventForm, description: text })}
                  multiline
                  numberOfLines={3}
                  style={styles.input}
                />

                <View style={styles.datePickerContainer}>
                  <Text style={styles.datePickerLabel}>Date</Text>
                  <EthiopianDatePicker
                    value={eventForm.date ? new Date(eventForm.date) : new Date()}
                    onChange={(date) => setEventForm({ ...eventForm, date: date.toISOString() })}
                    label="Select Event Date"
                    language={language}
                  />
                </View>

                <View style={styles.timeContainer}>
                  <TextInput
                    label="Start Time"
                    value={eventForm.startTime}
                    onChangeText={(text) => setEventForm({ ...eventForm, startTime: text })}
                    style={[styles.input, styles.timeInput]}
                  />
                  <TextInput
                    label="End Time"
                    value={eventForm.endTime}
                    onChangeText={(text) => setEventForm({ ...eventForm, endTime: text })}
                    style={[styles.input, styles.timeInput]}
                  />
                </View>

                <TextInput
                  label="Location"
                  value={eventForm.location}
                  onChangeText={(text) => setEventForm({ ...eventForm, location: text })}
                  style={styles.input}
                />

                <View style={styles.buttonContainer}>
                  <CustomButton
                    mode="outlined"
                    onPress={() => {
                      setModalVisible(false);
                      setSelectedEvent(null);
                      resetForm();
                    }}
                    style={[styles.button, styles.cancelButton]}
                  >
                    Cancel
                  </CustomButton>

                  {selectedEvent && (
                    <CustomButton
                      mode="outlined"
                      onPress={handleDeleteEvent}
                      style={[styles.button, styles.deleteButton]}
                      loading={loading}
                    >
                      Delete
                    </CustomButton>
                  )}

                  <CustomButton
                    mode="contained"
                    onPress={selectedEvent ? handleUpdateEvent : handleAddEvent}
                    style={[styles.button, styles.submitButton]}
                    loading={loading}
                  >
                    {selectedEvent ? 'Update' : 'Add'}
                  </CustomButton>
                </View>
              </ScrollView>
            </Modal>
          </Portal>
        </>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  addButton: {
    margin: 16,
  },
  eventCard: {
    margin: 8,
    borderLeftWidth: 4,
  },
  eventHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  eventTitle: {
    fontSize: 16,
    flex: 1,
    marginRight: 8,
  },
  eventTime: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  eventLocation: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  eventDescription: {
    marginTop: 8,
    color: '#444',
  },
  participants: {
    marginTop: 12,
  },
  participantsTitle: {
    fontSize: 14,
    marginBottom: 4,
  },
  chipContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 4,
  },
  participantChip: {
    marginVertical: 2,
  },
  modalContent: {
    backgroundColor: 'white',
    padding: 20,
    margin: 20,
    borderRadius: 8,
    maxHeight: '80%',
  },
  input: {
    marginBottom: 16,
  },
  timeContainer: {
    flexDirection: 'row',
    gap: 16,
  },
  timeInput: {
    flex: 1,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: 8,
    marginTop: 16,
  },
  button: {
    minWidth: 100,
  },
  cancelButton: {
    borderColor: '#666',
  },
  deleteButton: {
    borderColor: '#f44336',
    color: '#f44336',
  },
  submitButton: {
    backgroundColor: '#2196F3',
  },
  loader: {
    marginVertical: 20,
  },
  datePickerContainer: {
    marginBottom: 16,
  },
  datePickerLabel: {
    fontSize: 14,
    marginBottom: 8,
    color: '#666',
  },
});

export default SchoolEventCalendar;
