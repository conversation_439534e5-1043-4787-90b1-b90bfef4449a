import React, { useState, useEffect } from 'react';
import { StyleSheet, View, TouchableOpacity, Platform, Dimensions, Image } from 'react-native';
import { IconButton, Menu, Text, Surface, Badge, Divider, Avatar } from 'react-native-paper';
import { useLanguage } from '../../context/LanguageContext';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import * as Animatable from 'react-native-animatable';
import TranslationDebugger from '../../utils/TranslationDebugger';
import TranslationUpdater from '../../utils/TranslationUpdater';

const LanguageSelector = ({ showDebugOptions = __DEV__ }) => {
  const [visible, setVisible] = useState(false);
  const { language, setLanguage, supportedLanguages, getTextStyle, translate } = useLanguage();

  const openMenu = () => setVisible(true);
  const closeMenu = () => setVisible(false);

  const handleLanguageChange = async (langCode) => {
    try {
      console.log(`Changing language to: ${langCode}`);
      await setLanguage(langCode);

      // Notify user of language change
      if (Platform.OS !== 'web') {
        // For mobile, we could show a toast notification
        // But for web, we'll just log it
        console.log(`Language changed to ${supportedLanguages[langCode]}`);
      }

      // Force global UI refresh by triggering animations
      setAnimateFlag(true);
      setTimeout(() => setAnimateFlag(false), 1000);

      closeMenu();
    } catch (error) {
      console.error('Error changing language:', error);
    }
  };

  // Debug function to show missing translations
  const showMissingTranslations = () => {
    closeMenu();
    setTimeout(() => {
      TranslationDebugger.showTranslationExportMenu(language);
      TranslationDebugger.displayMissingTranslations(language);
    }, 500);
  };

  // Debug function to update translation files
  const updateTranslationFiles = () => {
    closeMenu();
    setTimeout(() => {
      TranslationUpdater.updateTranslationFiles(language);
    }, 500);
  };

  // Debug function to toggle showing translation keys in UI
  const toggleTranslationKeys = () => {
    closeMenu();
    setTimeout(() => {
      TranslationUpdater.toggleTranslationKeys();
    }, 500);
  };

  // Language flag/icon mapper
  const languageIcons = {
    en: 'flag-usa',
    am: 'flag-outline',   // Icon for Amharic
    om: 'flag-variant-outline',   // Icon for Afaan Oromo
    ctu: 'translate'      // Icon for Ctu
  };

  // Language names in their native language
  const nativeLanguageNames = {
    en: 'English',
    am: 'አማርኛ',
    om: 'Afaan Oromoo',
    ctu: 'Ctu'
  };

  // Flag emoji for each language
  const languageFlags = {
    en: '🇺🇸',
    am: '🇪🇹',
    om: '🇪🇹',
    ctu: '🌐'
  };

  // Create a short language tag to display
  const getLanguageTag = (code) => {
    return code.toUpperCase();
  };

  // Animation when language changes
  const [animateFlag, setAnimateFlag] = useState(false);

  useEffect(() => {
    setAnimateFlag(true);
    const timer = setTimeout(() => setAnimateFlag(false), 1000);
    return () => clearTimeout(timer);
  }, [language]);

  return (
    <View style={styles.container}>
      <Menu
        visible={visible}
        onDismiss={closeMenu}
        anchor={
          <TouchableOpacity onPress={openMenu} style={styles.languageButton}>
            <Animatable.View
              animation={animateFlag ? "pulse" : undefined}
              style={styles.languageButtonContent}
            >
              {languageFlags[language] ? (
                <Text style={styles.flagEmoji}>{languageFlags[language]}</Text>
              ) : (
                <MaterialCommunityIcons name="translate" size={20} color="#fff" />
              )}
              <Badge style={styles.languageBadge} size={16}>
                {getLanguageTag(language)}
              </Badge>
            </Animatable.View>
          </TouchableOpacity>
        }
        contentStyle={styles.menuContent}
      >
        <View style={styles.menuHeader}>
          <Text style={styles.menuTitle}>{translate('common.selectLanguage')}</Text>
        </View>
        <Divider />
        {Object.entries(supportedLanguages).map(([code, name]) => (
          <Menu.Item
            key={code}
            onPress={() => handleLanguageChange(code)}
            title={
              <View style={styles.languageItemContent}>
                <Text style={[
                  styles.languageName,
                  getTextStyle({ fontSize: 14 }),
                  code === language ? styles.selectedLanguageText : null
                ]}>
                  {name}
                </Text>
                <Text style={styles.nativeLanguageName}>
                  {nativeLanguageNames[code] || name}
                </Text>
              </View>
            }
            style={[
              styles.languageItem,
              code === language ? styles.selectedLanguage : null
            ]}
            leadingIcon={() => (
              <View style={styles.languageIconContainer}>
                {languageFlags[code] ? (
                  <Text style={styles.menuFlagEmoji}>{languageFlags[code]}</Text>
                ) : (
                  <MaterialCommunityIcons
                    name={languageIcons[code] || "translate"}
                    size={24}
                    color={code === language ? "#2196F3" : "#757575"}
                  />
                )}
              </View>
            )}
            trailingIcon={code === language ? "check" : undefined}
          />
        ))}

        {/* Debug options - only visible in development mode */}
        {showDebugOptions && (
          <>
            <Divider style={styles.debugDivider} />
            <Menu.Item
              onPress={showMissingTranslations}
              title="Show Missing Translations"
              style={styles.debugOption}
              leadingIcon="bug-outline"
            />
            <Menu.Item
              onPress={updateTranslationFiles}
              title="Export Missing Translations"
              style={styles.debugOption}
              leadingIcon="file-export"
            />
            <Menu.Item
              onPress={toggleTranslationKeys}
              title="Toggle Translation Keys"
              style={styles.debugOption}
              leadingIcon="eye"
            />
            <Menu.Item
              onPress={() => {
                TranslationDebugger.resetMissingTranslations();
                closeMenu();
              }}
              title="Reset Translation Tracker"
              style={styles.debugOption}
              leadingIcon="refresh"
            />
          </>
        )}
      </Menu>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginRight: 8,
  },
  languageButton: {
    padding: 8,
    borderRadius: 20,
  },
  languageButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  flagEmoji: {
    fontSize: 18,
    marginRight: 2,
  },
  languageBadge: {
    backgroundColor: '#ff9800',
    marginLeft: -8,
    marginTop: -8,
  },
  menuContent: {
    width: 220,
    borderRadius: 8,
    marginTop: 8,
    overflow: 'hidden',
  },
  menuHeader: {
    padding: 12,
    backgroundColor: '#f5f5f5',
  },
  menuTitle: {
    fontSize: 14,
    color: '#424242',
    fontWeight: 'bold',
    textAlign: 'center',
  },
  languageItem: {
    height: 56,
    paddingVertical: 8,
  },
  selectedLanguage: {
    backgroundColor: 'rgba(33, 150, 243, 0.1)',
  },
  languageItemContent: {
    flexDirection: 'column',
    justifyContent: 'center',
  },
  languageName: {
    fontSize: 14,
    fontWeight: '500',
  },
  nativeLanguageName: {
    fontSize: 12,
    opacity: 0.7,
    marginTop: 2,
  },
  selectedLanguageText: {
    fontWeight: 'bold',
    color: '#2196F3',
  },
  languageIconContainer: {
    width: 32,
    height: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  menuFlagEmoji: {
    fontSize: 22,
  },
  debugDivider: {
    marginVertical: 8,
    backgroundColor: '#ff9800',
    height: 1,
  },
  debugOption: {
    backgroundColor: 'rgba(255, 152, 0, 0.1)',
  },
});

export default LanguageSelector;
