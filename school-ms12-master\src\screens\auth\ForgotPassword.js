import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  TouchableWithoutFeedback,
  Keyboard,
  Animated,
  Dimensions
} from 'react-native';
import {
  TextInput,
  Button,
  Title,
  Text,
  Menu,
  Surface,
  useTheme,
  IconButton
} from 'react-native-paper';
import { auth } from '../../config/firebase';
import { useLanguage } from '../../context/LanguageContext';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import * as Animatable from 'react-native-animatable';
import EmailVerificationService from '../../services/EmailVerificationService';
import EmailNotificationService from '../../services/EmailNotificationService';

const { width, height } = Dimensions.get('window');

// Default colors in case theme is not available
const DEFAULT_COLORS = {
  primary: '#2196F3',
  accent: '#03A9F4',
  error: '#B00020',
  success: '#4CAF50'
};

const ForgotPassword = ({ navigation }) => {
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [message, setMessage] = useState('');
  const [menuVisible, setMenuVisible] = useState(false);
  const [emailFocused, setEmailFocused] = useState(false);
  const { translate, language, setLanguage, supportedLanguages } = useLanguage();
  const theme = useTheme();

  // Get colors from theme or use defaults
  const colors = {
    primary: theme?.colors?.primary || DEFAULT_COLORS.primary,
    accent: theme?.colors?.accent || DEFAULT_COLORS.accent,
    error: theme?.colors?.error || DEFAULT_COLORS.error,
    success: theme?.colors?.success || DEFAULT_COLORS.success
  };

  // Animation values
  const [fadeAnim] = useState(new Animated.Value(0));
  const [slideAnim] = useState(new Animated.Value(50));
  const [formSlideAnim] = useState(new Animated.Value(100));

  React.useEffect(() => {
    // Start entrance animations in sequence
    Animated.sequence([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 500,
        useNativeDriver: true,
      }),
      Animated.timing(formSlideAnim, {
        toValue: 0,
        duration: 500,
        useNativeDriver: true,
      })
    ]).start();
  }, []);

  const handleResetPassword = async () => {
    if (!email) {
      setError(translate('auth.forgotPassword.errors.enterEmail'));
      return;
    }

    try {
      setError('');
      setMessage('');
      setLoading(true);

      // Use the enhanced EmailVerificationService
      const result = await EmailVerificationService.sendPasswordResetEmail(email);

      // Check if there was an error
      if (result && result.success === false) {
        setError(result.message || translate('auth.forgotPassword.errors.resetFailed'));
        return;
      }

      // Try to send a custom email notification
      try {
        // Create a reset link (this would be handled by Firebase in production)
        const resetLink = `${window.location.origin}/reset-password-confirm`;
        await EmailNotificationService.sendPasswordResetEmail(email, resetLink);
      } catch (emailError) {
        console.log('Custom email notification failed, using Firebase default:', emailError);
        // Continue even if custom email fails, as Firebase will send its own
      }

      setMessage(translate('auth.forgotPassword.success'));
    } catch (err) {
      setError(translate('auth.forgotPassword.errors.resetFailed'));
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const handleLanguageChange = async (langCode) => {
    try {
      await setLanguage(langCode);
    setMenuVisible(false);
    } catch (error) {
      console.error('Failed to change language:', error);
    }
  };

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={['#4A6FFF', '#6A5ACD', '#8A2BE2']}
        style={styles.gradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <View style={styles.gradientPattern}>
          {/* Pattern overlay created with small dots */}
          {Array.from({ length: 20 }).map((_, i) => (
            <View
              key={i}
              style={[
                styles.patternDot,
                {
                  left: `${Math.random() * 100}%`,
                  top: `${Math.random() * 100}%`,
                  width: Math.random() * 4 + 1,
                  height: Math.random() * 4 + 1,
                  opacity: Math.random() * 0.3 + 0.1
                }
              ]}
            />
          ))}
        </View>
        <TouchableWithoutFeedback onPress={Keyboard.dismiss} accessible={false}>
          <KeyboardAvoidingView
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            style={styles.keyboardAvoidingView}
          >
            <Animated.View
              style={[
                styles.languageContainer,
                {
                  opacity: fadeAnim,
                  transform: [{ translateY: slideAnim }]
                }
              ]}
            >
      <Menu
        visible={menuVisible}
        onDismiss={() => setMenuVisible(false)}
        anchor={
                  <IconButton
                    icon="translate"
                    size={24}
                    color="white"
                    onPress={() => setMenuVisible(true)}
            style={styles.languageButton}
                  />
        }
      >
                {Object.entries(supportedLanguages).map(([code, name]) => (
          <Menu.Item
                    key={code}
                    onPress={() => handleLanguageChange(code)}
                    title={name}
                    disabled={code === language}
          />
        ))}
      </Menu>
            </Animated.View>

            <Animated.View
              style={[
                styles.formContainer,
                {
                  opacity: fadeAnim,
                  transform: [{ translateY: formSlideAnim }]
                }
              ]}
            >
              <Animatable.Text
                animation="fadeIn"
                duration={1500}
                style={styles.title}
              >
                {translate('auth.forgotPassword.title')}
              </Animatable.Text>

              {error ? (
                <Animatable.View animation="fadeIn" duration={300}>
                  <View style={styles.errorContainer}>
                    <MaterialCommunityIcons
                      name="alert-circle"
                      size={18}
                      color="#FFFFFF"
                    />
                    <Text style={styles.errorText}>
                      {error}
                    </Text>
                  </View>
                </Animatable.View>
              ) : null}

              {message ? (
                <Animatable.View animation="fadeIn" duration={300}>
                  <View style={styles.successContainer}>
                    <MaterialCommunityIcons
                      name="check-circle"
                      size={18}
                      color="#FFFFFF"
                    />
                    <Text style={styles.successText}>
                      {message}
                    </Text>
                  </View>
                </Animatable.View>
              ) : null}

              <View style={[styles.inputContainer, emailFocused && styles.inputContainerFocused]}>
                <MaterialCommunityIcons
                  name="email"
                  size={24}
                  color={emailFocused ? '#FFFFFF' : 'rgba(255, 255, 255, 0.7)'}
                  style={styles.inputIcon}
                />
                <TextInput
                  label={translate('auth.forgotPassword.emailLabel')}
                  value={email}
                  onChangeText={setEmail}
                  mode="flat"
                  style={styles.input}
                  keyboardType="email-address"
                  autoCapitalize="none"
                  onFocus={() => setEmailFocused(true)}
                  onBlur={() => setEmailFocused(false)}
                  theme={{
                    colors: {
                      background: 'transparent',
                      text: '#FFFFFF',
                      placeholder: 'rgba(255, 255, 255, 0.7)',
                      primary: '#FFFFFF'
                    }
                  }}
                  textColor="#FFFFFF"
                  placeholderTextColor="rgba(255, 255, 255, 0.7)"
                  underlineColor="rgba(255, 255, 255, 0.3)"
                  activeUnderlineColor="#FFFFFF"
                />
              </View>

              <Animatable.View animation="fadeIn" delay={800}>
                <Button
                  mode="contained"
                  onPress={handleResetPassword}
                  loading={loading}
                  disabled={loading}
                  style={styles.button}
                  contentStyle={styles.buttonContent}
                  labelStyle={styles.buttonLabel}
                >
                  {translate('auth.forgotPassword.resetButton')}
                </Button>
              </Animatable.View>

              <Animatable.View animation="fadeIn" delay={1000}>
                <Button
                  mode="outlined"
                  onPress={() => navigation.navigate('Login')}
                  style={styles.link}
                  textColor="#FFFFFF"
                >
                  {translate('auth.forgotPassword.backToLogin')}
                </Button>
              </Animatable.View>
            </Animated.View>
          </KeyboardAvoidingView>
        </TouchableWithoutFeedback>
      </LinearGradient>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gradient: {
    flex: 1,
    width: '100%',
    height: '100%',
    position: 'relative',
  },
  gradientPattern: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1,
  },
  patternDot: {
    position: 'absolute',
    borderRadius: 50,
    backgroundColor: '#FFFFFF',
  },
  keyboardAvoidingView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    position: 'relative',
    zIndex: 2,
  },
  languageContainer: {
    position: 'absolute',
    top: 50,
    right: 20,
    zIndex: 3,
  },
  languageButton: {
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    borderRadius: 20,
  },
  formContainer: {
    width: '100%',
    maxWidth: 400,
    paddingHorizontal: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
    color: '#FFFFFF',
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(220, 53, 69, 0.7)',
    padding: 12,
    borderRadius: 8,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  errorText: {
    marginLeft: 8,
    fontSize: 14,
    flex: 1,
    color: '#FFFFFF',
    fontWeight: '500',
  },
  successContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(40, 167, 69, 0.7)',
    padding: 12,
    borderRadius: 8,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  successText: {
    marginLeft: 8,
    fontSize: 14,
    flex: 1,
    color: '#FFFFFF',
    fontWeight: '500',
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    borderRadius: 12,
    marginBottom: 20,
    paddingHorizontal: 15,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    shadowColor: 'rgba(0, 0, 0, 0.3)',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 5,
    elevation: 3,
  },
  inputContainerFocused: {
    backgroundColor: 'rgba(255, 255, 255, 0.25)',
    borderColor: 'rgba(255, 255, 255, 0.5)',
  },
  inputIcon: {
    marginRight: 12,
  },
  input: {
    flex: 1,
    backgroundColor: 'transparent',
    color: '#FFFFFF',
    height: 60,
  },
  button: {
    borderRadius: 12,
    marginTop: 5,
    elevation: 5,
    backgroundColor: '#FFFFFF',
  },
  buttonContent: {
    height: 56,
    backgroundColor: '#FFFFFF',
  },
  buttonLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#4A6FFF',
  },
  link: {
    marginTop: 20,
    borderColor: 'rgba(255, 255, 255, 0.5)',
    borderRadius: 12,
  },
});

export default ForgotPassword;