import React, { useState, useEffect } from 'react';
import { View, ScrollView, StyleSheet } from 'react-native';
import { Card, Title, Text, DataTable, Searchbar, List, Chip } from 'react-native-paper';
import { db } from '../../config/firebase';
import { collection, addDoc, query, getDocs, doc, updateDoc, where, serverTimestamp } from 'firebase/firestore';
import { useAuth } from '../../context/AuthContext';
import CustomButton from '../../components/common/CustomButton';

const ExamAttendance = () => {
  const { user } = useAuth();
  const [exams, setExams] = useState([]);
  const [students, setStudents] = useState([]);
  const [selectedExam, setSelectedExam] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(false);
  const [attendanceRecords, setAttendanceRecords] = useState([]);

  useEffect(() => {
    fetchExams();
  }, []);

  useEffect(() => {
    if (selectedExam) {
      fetchStudents();
      fetchAttendanceRecords();
    }
  }, [selectedExam]);

  const fetchExams = async () => {
    try {
      const examsRef = collection(db, 'exams');
      const q = query(
        examsRef,
        where('teacherId', '==', user.uid),
        where('status', '==', 'ongoing')
      );
      const querySnapshot = await getDocs(q);
      
      const examsData = [];
      querySnapshot.forEach((doc) => {
        examsData.push({ id: doc.id, ...doc.data() });
      });
      
      setExams(examsData);
    } catch (error) {
      console.error('Error fetching exams:', error);
    }
  };

  const fetchStudents = async () => {
    try {
      const studentsRef = collection(db, 'students');
      const q = query(studentsRef, where('classId', '==', selectedExam.classId));
      const querySnapshot = await getDocs(q);
      
      const studentsData = [];
      querySnapshot.forEach((doc) => {
        studentsData.push({ id: doc.id, ...doc.data() });
      });
      
      setStudents(studentsData);
    } catch (error) {
      console.error('Error fetching students:', error);
    }
  };

  const fetchAttendanceRecords = async () => {
    try {
      const attendanceRef = collection(db, 'examAttendance');
      const q = query(attendanceRef, where('examId', '==', selectedExam.id));
      const querySnapshot = await getDocs(q);
      
      const attendanceData = [];
      querySnapshot.forEach((doc) => {
        attendanceData.push({ id: doc.id, ...doc.data() });
      });
      
      setAttendanceRecords(attendanceData);
    } catch (error) {
      console.error('Error fetching attendance records:', error);
    }
  };

  const handleMarkAttendance = async (studentId, status) => {
    try {
      setLoading(true);
      
      // Find existing attendance record
      const existingRecord = attendanceRecords.find(
        record => record.studentId === studentId
      );

      if (existingRecord) {
        // Update existing record
        const attendanceRef = doc(db, 'examAttendance', existingRecord.id);
        await updateDoc(attendanceRef, {
          status,
          updatedAt: serverTimestamp(),
        });
      } else {
        // Create new record
        const attendanceRef = collection(db, 'examAttendance');
        await addDoc(attendanceRef, {
          examId: selectedExam.id,
          studentId,
          status,
          markedBy: user.uid,
          createdAt: serverTimestamp(),
        });
      }

      fetchAttendanceRecords();
    } catch (error) {
      console.error('Error marking attendance:', error);
    } finally {
      setLoading(false);
    }
  };

  const getAttendanceStatus = (studentId) => {
    const record = attendanceRecords.find(
      record => record.studentId === studentId
    );
    return record ? record.status : 'unmarked';
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'present':
        return '#4CAF50';
      case 'absent':
        return '#F44336';
      case 'late':
        return '#FFC107';
      default:
        return '#9E9E9E';
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {exams.map((exam) => (
            <Chip
              key={exam.id}
              selected={selectedExam?.id === exam.id}
              onPress={() => setSelectedExam(exam)}
              style={styles.chip}
            >
              {exam.name}
            </Chip>
          ))}
        </ScrollView>
      </View>

      {selectedExam ? (
        <>
          <Card style={styles.examCard}>
            <Card.Content>
              <Title>{selectedExam.name}</Title>
              <Text>Date: {new Date(selectedExam.date).toLocaleDateString()}</Text>
              <Text>Time: {selectedExam.startTime} - {selectedExam.endTime}</Text>
              <Text>Subject: {selectedExam.subject}</Text>
            </Card.Content>
          </Card>

          <Searchbar
            placeholder="Search students..."
            onChangeText={setSearchQuery}
            value={searchQuery}
            style={styles.searchBar}
          />

          <DataTable>
            <DataTable.Header>
              <DataTable.Title>Roll No</DataTable.Title>
              <DataTable.Title>Name</DataTable.Title>
              <DataTable.Title>Status</DataTable.Title>
              <DataTable.Title>Actions</DataTable.Title>
            </DataTable.Header>

            {students
              .filter(student =>
                student.name.toLowerCase().includes(searchQuery.toLowerCase())
              )
              .map((student) => {
                const status = getAttendanceStatus(student.id);
                return (
                  <DataTable.Row key={student.id}>
                    <DataTable.Cell>{student.rollNo}</DataTable.Cell>
                    <DataTable.Cell>{student.name}</DataTable.Cell>
                    <DataTable.Cell>
                      <Chip
                        mode="outlined"
                        selectedColor={getStatusColor(status)}
                      >
                        {status.charAt(0).toUpperCase() + status.slice(1)}
                      </Chip>
                    </DataTable.Cell>
                    <DataTable.Cell>
                      <View style={styles.actionButtons}>
                        <CustomButton
                          mode="text"
                          onPress={() => handleMarkAttendance(student.id, 'present')}
                          loading={loading}
                          disabled={status === 'present'}
                        >
                          Present
                        </CustomButton>
                        <CustomButton
                          mode="text"
                          onPress={() => handleMarkAttendance(student.id, 'absent')}
                          loading={loading}
                          disabled={status === 'absent'}
                        >
                          Absent
                        </CustomButton>
                        <CustomButton
                          mode="text"
                          onPress={() => handleMarkAttendance(student.id, 'late')}
                          loading={loading}
                          disabled={status === 'late'}
                        >
                          Late
                        </CustomButton>
                      </View>
                    </DataTable.Cell>
                  </DataTable.Row>
                );
              })}
          </DataTable>
        </>
      ) : (
        <View style={styles.noExamContainer}>
          <Text>No ongoing exams found.</Text>
          <Text>Please select an exam to mark attendance.</Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: 'white',
    padding: 10,
    elevation: 2,
  },
  examCard: {
    margin: 10,
  },
  searchBar: {
    margin: 10,
    elevation: 2,
  },
  chip: {
    margin: 4,
  },
  actionButtons: {
    flexDirection: 'row',
  },
  noExamContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default ExamAttendance;
