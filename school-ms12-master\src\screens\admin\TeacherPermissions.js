import React, { useState, useEffect } from 'react';
import { View, ScrollView, StyleSheet } from 'react-native';
import { Card, Title, DataTable, Switch, Portal, Modal, Text, Snackbar, Paragraph } from 'react-native-paper';
import { db } from '../../config/firebase';
import { collection, query, where, getDocs, updateDoc, doc, addDoc, deleteDoc } from 'firebase/firestore';
import CustomButton from '../../components/common/CustomButton';

const TeacherPermissions = () => {
  const [teachers, setTeachers] = useState([]);
  const [selectedTeacher, setSelectedTeacher] = useState(null);
  const [modalVisible, setModalVisible] = useState(false);
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');

  const permissions = [
    { 
      id: 'registration', 
      label: 'Register Students & Parents',
      description: 'Allow teacher to register new students and parents'
    },
    { id: 'grades', label: 'Modify Grades', description: 'Allow teacher to modify student grades' },
    { id: 'attendance', label: 'Manage Attendance', description: 'Allow teacher to manage student attendance' },
    { id: 'library', label: 'Access Library', description: 'Allow teacher to access library resources' },
    { id: 'announcements', label: 'Send Announcements', description: 'Allow teacher to send announcements' },
    { id: 'events', label: 'Schedule Events', description: 'Allow teacher to schedule school events' },
  ];

  useEffect(() => {
    fetchTeachers();
  }, []);

  const fetchTeachers = async () => {
    try {
      // Fetch teachers
      const teachersRef = collection(db, 'users');
      const q = query(teachersRef, where('role', '==', 'teacher'));
      const querySnapshot = await getDocs(q);
      
      const teachersArray = [];
      
      // For each teacher, fetch their permissions
      for (const teacherDoc of querySnapshot.docs) {
        const teacher = teacherDoc.data();
        
        // Fetch permissions from the permissions collection
        const permissionsRef = collection(db, 'permissions');
        const permissionsQuery = query(
          permissionsRef,
          where('teacherId', '==', teacherDoc.id)
        );
        const permissionsSnapshot = await getDocs(permissionsQuery);
        
        const teacherPermissions = {};
        permissionsSnapshot.forEach(permDoc => {
          const permData = permDoc.data();
          teacherPermissions[permData.type] = permData.status === 'approved';
        });

        teachersArray.push({
          id: teacherDoc.id,
          name: teacher.displayName || teacher.email.split('@')[0],
          email: teacher.email,
          permissions: teacherPermissions,
        });
      }
      
      setTeachers(teachersArray);
    } catch (error) {
      console.error('Error fetching teachers:', error);
      showSnackbar('Error fetching teachers');
    }
  };

  const showSnackbar = (message) => {
    setSnackbarMessage(message);
    setSnackbarVisible(true);
  };

  const handlePermissionChange = async (teacherId, permission, value) => {
    try {
      const permissionsRef = collection(db, 'permissions');
      const q = query(
        permissionsRef,
        where('teacherId', '==', teacherId),
        where('type', '==', permission)
      );
      const querySnapshot = await getDocs(q);

      if (value) {
        // Grant permission
        if (querySnapshot.empty) {
          await addDoc(permissionsRef, {
            teacherId,
            type: permission,
            status: 'approved',
            grantedAt: new Date().toISOString(),
          });
        } else {
          const permDoc = querySnapshot.docs[0];
          await updateDoc(doc(db, 'permissions', permDoc.id), {
            status: 'approved',
            grantedAt: new Date().toISOString(),
          });
        }
      } else {
        // Revoke permission
        querySnapshot.forEach(async (doc) => {
          await deleteDoc(doc.ref);
        });
      }
      
      // Update local state
      setTeachers(teachers.map(teacher => 
        teacher.id === teacherId 
          ? {
              ...teacher,
              permissions: {
                ...teacher.permissions,
                [permission]: value
              }
            }
          : teacher
      ));
      
      setSelectedTeacher(prev => ({
        ...prev,
        permissions: {
          ...prev.permissions,
          [permission]: value
        }
      }));

      showSnackbar(`Permission ${value ? 'granted' : 'revoked'} successfully`);
    } catch (error) {
      console.error('Error updating permission:', error);
      showSnackbar('Error updating permission');
    }
  };

  const handleBulkPermissions = async (teacherId, value) => {
    try {
      for (const permission of permissions) {
        await handlePermissionChange(teacherId, permission.id, value);
      }
      showSnackbar(`All permissions ${value ? 'granted' : 'revoked'} successfully`);
    } catch (error) {
      console.error('Error updating permissions:', error);
      showSnackbar('Error updating permissions');
    }
  };

  return (
    <ScrollView style={styles.container}>
      <Card style={styles.card}>
        <Card.Content>
          <Title>Teacher Permissions Management</Title>

          <DataTable>
            <DataTable.Header>
              <DataTable.Title>Name</DataTable.Title>
              <DataTable.Title>Email</DataTable.Title>
              <DataTable.Title>Active Permissions</DataTable.Title>
              <DataTable.Title>Actions</DataTable.Title>
            </DataTable.Header>

            {teachers.map((teacher) => (
              <DataTable.Row key={teacher.id}>
                <DataTable.Cell>{teacher.name}</DataTable.Cell>
                <DataTable.Cell>{teacher.email}</DataTable.Cell>
                <DataTable.Cell>
                  {Object.values(teacher.permissions || {}).filter(Boolean).length} / {permissions.length}
                </DataTable.Cell>
                <DataTable.Cell>
                  <CustomButton
                    mode="outlined"
                    onPress={() => {
                      setSelectedTeacher(teacher);
                      setModalVisible(true);
                    }}
                  >
                    Manage
                  </CustomButton>
                </DataTable.Cell>
              </DataTable.Row>
            ))}
          </DataTable>
        </Card.Content>
      </Card>

      <Portal>
        <Modal
          visible={modalVisible}
          onDismiss={() => setModalVisible(false)}
          contentContainerStyle={styles.modalContent}
        >
          {selectedTeacher && (
            <ScrollView>
              <Title>Permissions for {selectedTeacher.name}</Title>
              
              <View style={styles.bulkActions}>
                <CustomButton
                  mode="outlined"
                  onPress={() => handleBulkPermissions(selectedTeacher.id, true)}
                  style={styles.bulkButton}
                >
                  Grant All
                </CustomButton>
                <CustomButton
                  mode="outlined"
                  onPress={() => handleBulkPermissions(selectedTeacher.id, false)}
                  style={styles.bulkButton}
                >
                  Revoke All
                </CustomButton>
              </View>

              {permissions.map((permission) => (
                <View key={permission.id} style={styles.permissionItem}>
                  <View style={styles.permissionText}>
                    <Text style={styles.permissionLabel}>{permission.label}</Text>
                    <Text style={styles.permissionDescription}>{permission.description}</Text>
                  </View>
                  <Switch
                    value={selectedTeacher.permissions?.[permission.id] || false}
                    onValueChange={(value) => 
                      handlePermissionChange(selectedTeacher.id, permission.id, value)
                    }
                  />
                </View>
              ))}

              <CustomButton
                mode="contained"
                onPress={() => setModalVisible(false)}
                style={styles.closeButton}
              >
                Close
              </CustomButton>
            </ScrollView>
          )}
        </Modal>
      </Portal>

      <Snackbar
        visible={snackbarVisible}
        onDismiss={() => setSnackbarVisible(false)}
        duration={3000}
      >
        {snackbarMessage}
      </Snackbar>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  card: {
    margin: 16,
  },
  modalContent: {
    backgroundColor: 'white',
    padding: 20,
    margin: 20,
    borderRadius: 8,
    maxHeight: '80%',
  },
  bulkActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: 16,
  },
  bulkButton: {
    flex: 1,
    marginHorizontal: 8,
  },
  permissionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  permissionText: {
    flex: 1,
    marginRight: 16,
  },
  permissionLabel: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  permissionDescription: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
  },
  closeButton: {
    marginTop: 20,
  },
});

export default TeacherPermissions;
