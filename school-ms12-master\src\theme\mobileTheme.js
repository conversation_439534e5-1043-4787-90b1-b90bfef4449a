import { DefaultTheme } from 'react-native-paper';

/**
 * Mobile-optimized theme for the School Management System
 * This theme provides better spacing, colors, and typography for mobile devices
 */
const mobileTheme = {
  ...DefaultTheme,
  dark: false,
  roundness: 12,
  colors: {
    ...DefaultTheme.colors,
    // Primary colors
    primary: '#1976d2',
    primaryDark: '#0d47a1',
    primaryLight: '#42a5f5',
    
    // Secondary colors
    secondary: '#ff9800',
    secondaryDark: '#f57c00',
    secondaryLight: '#ffb74d',
    
    // Accent colors
    accent: '#9c27b0',
    
    // Role-specific colors
    admin: '#5c6bc0',
    teacher: '#26a69a',
    student: '#ef5350',
    parent: '#ffa726',
    
    // Functional colors
    success: '#4caf50',
    warning: '#ff9800',
    error: '#f44336',
    info: '#2196f3',
    
    // UI colors
    background: '#f5f7fa',
    surface: '#ffffff',
    surfaceVariant: '#f0f4f8',
    card: '#ffffff',
    text: '#263238',
    textSecondary: '#546e7a',
    disabled: '#9e9e9e',
    placeholder: '#9e9e9e',
    backdrop: 'rgba(0, 0, 0, 0.5)',
    notification: '#f50057',
    divider: '#e0e0e0',
    
    // Status colors
    active: '#4caf50',
    inactive: '#9e9e9e',
    pending: '#ff9800',
    
    // Chart colors
    chartPrimary: '#1976d2',
    chartSecondary: '#ff9800',
    chartSuccess: '#4caf50',
    chartError: '#f44336',
    chartNeutral: '#9e9e9e',
  },
  
  // Typography
  fonts: {
    ...DefaultTheme.fonts,
    regular: {
      fontFamily: 'Roboto',
      fontWeight: 'normal',
    },
    medium: {
      fontFamily: 'Roboto',
      fontWeight: '500',
    },
    light: {
      fontFamily: 'Roboto',
      fontWeight: '300',
    },
    thin: {
      fontFamily: 'Roboto',
      fontWeight: '100',
    },
  },
  
  // Spacing
  spacing: {
    xs: 4,
    s: 8,
    m: 16,
    l: 24,
    xl: 32,
    xxl: 48,
  },
  
  // Icon sizes
  iconSizes: {
    xs: 16,
    s: 20,
    m: 24,
    l: 32,
    xl: 40,
  },
  
  // Animation
  animation: {
    scale: 1.0,
  },
  
  // Mobile-specific properties
  mobile: {
    bottomNavHeight: 56,
    headerHeight: 56,
    tabBarHeight: 48,
    fabSize: 56,
    minTouchSize: 48, // Minimum touchable area size
    statusBarHeight: 24,
    
    // Shadows
    elevation: {
      none: 0,
      xs: 1,
      s: 2,
      m: 4,
      l: 8,
      xl: 16,
    },
  },
};

export default mobileTheme;
