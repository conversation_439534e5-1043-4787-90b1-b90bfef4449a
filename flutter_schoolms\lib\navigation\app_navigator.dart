import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import '../context/auth_context.dart';
import '../screens/auth/login_screen.dart';
import '../screens/auth/signup_screen.dart';
import '../screens/auth/forgot_password_screen.dart';
import '../screens/splash_screen.dart';
import '../screens/admin/admin_dashboard_screen.dart';
import '../screens/teacher/teacher_dashboard_screen.dart';
import '../screens/student/student_dashboard_screen.dart';
import '../screens/parent/parent_dashboard_screen.dart';

class AppNavigator {
  static final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

  static final GoRouter router = GoRouter(
    navigatorKey: navigatorKey,
    initialLocation: '/splash',
    redirect: (context, state) {
      final authContext = context.read<AuthContext>();
      final isAuthenticated = authContext.isAuthenticated;
      final userRole = authContext.userRole;
      
      // If user is not authenticated and trying to access protected routes
      if (!isAuthenticated && !_isPublicRoute(state.location)) {
        return '/login';
      }
      
      // If user is authenticated and trying to access auth routes
      if (isAuthenticated && _isAuthRoute(state.location)) {
        return _getHomeRouteForRole(userRole);
      }
      
      return null; // No redirect needed
    },
    routes: [
      // Splash screen
      GoRoute(
        path: '/splash',
        name: 'splash',
        builder: (context, state) => const SplashScreen(),
      ),
      
      // Auth routes
      GoRoute(
        path: '/login',
        name: 'login',
        builder: (context, state) => const LoginScreen(),
      ),
      GoRoute(
        path: '/signup',
        name: 'signup',
        builder: (context, state) => const SignupScreen(),
      ),
      GoRoute(
        path: '/forgot-password',
        name: 'forgot-password',
        builder: (context, state) => const ForgotPasswordScreen(),
      ),
      
      // Admin routes
      GoRoute(
        path: '/admin',
        name: 'admin-dashboard',
        builder: (context, state) => const AdminDashboardScreen(),
        routes: [
          GoRoute(
            path: 'users',
            name: 'admin-users',
            builder: (context, state) => const Placeholder(), // UserManagementScreen(),
          ),
          GoRoute(
            path: 'classes',
            name: 'admin-classes',
            builder: (context, state) => const Placeholder(), // ClassManagementScreen(),
          ),
          GoRoute(
            path: 'reports',
            name: 'admin-reports',
            builder: (context, state) => const Placeholder(), // ReportsScreen(),
          ),
        ],
      ),
      
      // Teacher routes
      GoRoute(
        path: '/teacher',
        name: 'teacher-dashboard',
        builder: (context, state) => const TeacherDashboardScreen(),
        routes: [
          GoRoute(
            path: 'attendance',
            name: 'teacher-attendance',
            builder: (context, state) => const Placeholder(), // AttendanceScreen(),
          ),
          GoRoute(
            path: 'grades',
            name: 'teacher-grades',
            builder: (context, state) => const Placeholder(), // GradesScreen(),
          ),
          GoRoute(
            path: 'classes',
            name: 'teacher-classes',
            builder: (context, state) => const Placeholder(), // TeacherClassesScreen(),
          ),
        ],
      ),
      
      // Student routes
      GoRoute(
        path: '/student',
        name: 'student-dashboard',
        builder: (context, state) => const StudentDashboardScreen(),
        routes: [
          GoRoute(
            path: 'grades',
            name: 'student-grades',
            builder: (context, state) => const Placeholder(), // StudentGradesScreen(),
          ),
          GoRoute(
            path: 'schedule',
            name: 'student-schedule',
            builder: (context, state) => const Placeholder(), // ScheduleScreen(),
          ),
          GoRoute(
            path: 'assignments',
            name: 'student-assignments',
            builder: (context, state) => const Placeholder(), // AssignmentsScreen(),
          ),
        ],
      ),
      
      // Parent routes
      GoRoute(
        path: '/parent',
        name: 'parent-dashboard',
        builder: (context, state) => const ParentDashboardScreen(),
        routes: [
          GoRoute(
            path: 'children',
            name: 'parent-children',
            builder: (context, state) => const Placeholder(), // ChildrenScreen(),
          ),
          GoRoute(
            path: 'reports',
            name: 'parent-reports',
            builder: (context, state) => const Placeholder(), // ParentReportsScreen(),
          ),
        ],
      ),
      
      // Shared routes
      GoRoute(
        path: '/profile',
        name: 'profile',
        builder: (context, state) => const Placeholder(), // ProfileScreen(),
      ),
      GoRoute(
        path: '/notifications',
        name: 'notifications',
        builder: (context, state) => const Placeholder(), // NotificationsScreen(),
      ),
      GoRoute(
        path: '/settings',
        name: 'settings',
        builder: (context, state) => const Placeholder(), // SettingsScreen(),
      ),
    ],
    errorBuilder: (context, state) => Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              'Page Not Found',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              'The page you are looking for does not exist.',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => context.go('/'),
              child: const Text('Go Home'),
            ),
          ],
        ),
      ),
    ),
  );

  // Helper methods
  static bool _isPublicRoute(String location) {
    const publicRoutes = ['/splash', '/login', '/signup', '/forgot-password'];
    return publicRoutes.contains(location);
  }

  static bool _isAuthRoute(String location) {
    const authRoutes = ['/login', '/signup', '/forgot-password'];
    return authRoutes.contains(location);
  }

  static String _getHomeRouteForRole(String? role) {
    switch (role) {
      case 'admin':
        return '/admin';
      case 'teacher':
        return '/teacher';
      case 'student':
        return '/student';
      case 'parent':
        return '/parent';
      default:
        return '/login';
    }
  }

  // Navigation helper methods
  static void goToLogin() {
    router.go('/login');
  }

  static void goToSignup() {
    router.go('/signup');
  }

  static void goToForgotPassword() {
    router.go('/forgot-password');
  }

  static void goToDashboard(String role) {
    router.go(_getHomeRouteForRole(role));
  }

  static void goToProfile() {
    router.go('/profile');
  }

  static void goToNotifications() {
    router.go('/notifications');
  }

  static void goToSettings() {
    router.go('/settings');
  }

  static void goBack() {
    if (router.canPop()) {
      router.pop();
    }
  }
}
