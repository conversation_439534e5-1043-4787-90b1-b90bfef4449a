import React, { useState, useEffect, useMemo } from 'react';
import { View, ScrollView, StyleSheet, Dimensions, TouchableOpacity, ImageBackground, Animated, Alert, Text, StatusBar, RefreshControl } from 'react-native';
import { Card, Title, Paragraph, IconButton, Portal, Dialog, Button, Avatar, Badge, Divider, List, ActivityIndicator, BottomNavigation, Searchbar, Surface, FAB, Chip, useTheme } from 'react-native-paper';
import { useNavigation } from '@react-navigation/native';
import { signOut } from 'firebase/auth';
import { auth, db } from '../../config/firebase';
import { collection, query, getDocs, where, doc, getDoc, updateDoc } from 'firebase/firestore';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Pie<PERSON>hart, ProgressChart } from 'react-native-chart-kit';
import { LinearGradient } from 'expo-linear-gradient';
import { SafeAreaView } from 'react-native-safe-area-context';
import NotificationService from '../../services/NotificationService';
import MessagingFAB from '../../components/common/MessagingFAB';
import DashboardClassWidget from '../../components/common/DashboardClassWidget';
import { sanitizeChartData, sanitizeChartDatasets } from '../../utils/ChartUtils';
import * as Animatable from 'react-native-animatable';
import { useLanguage } from '../../context/LanguageContext';
import StudentAppHeader from '../../components/common/StudentAppHeader';
import StudentSidebar from '../../components/common/StudentSidebar';

// Today's Schedule Widget Component
const TodayScheduleWidget = ({ classId, section }) => {
  const [loading, setLoading] = useState(true);
  const [todaySchedule, setTodaySchedule] = useState([]);
  const [error, setError] = useState('');
  const navigation = useNavigation();

  // Get today's day name
  const getDayName = () => {
    const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    return days[new Date().getDay()];
  };

  useEffect(() => {
    fetchTodaySchedule();
  }, [classId, section]);

  const fetchTodaySchedule = async () => {
    try {
      setLoading(true);

      if (!classId || !section) {
        setError('Class or section information missing');
        setLoading(false);
        return;
      }

      const today = getDayName();

      const timetableRef = collection(db, 'timetables');
      const timetableQuery = query(
        timetableRef,
        where('classId', '==', classId),
        where('sectionName', '==', section),
        where('day', '==', today),
        where('published', '==', true)
      );

      const querySnapshot = await getDocs(timetableQuery);

      const scheduleData = [];
      querySnapshot.forEach((doc) => {
        scheduleData.push({ id: doc.id, ...doc.data() });
      });

      // Sort by start time
      scheduleData.sort((a, b) => {
        const timeA = a.startTime.split(':').map(Number);
        const timeB = b.startTime.split(':').map(Number);

        if (timeA[0] !== timeB[0]) {
          return timeA[0] - timeB[0];
        }
        return timeA[1] - timeB[1];
      });

      setTodaySchedule(scheduleData);
      setLoading(false);
    } catch (error) {
      console.error('Error fetching today\'s schedule:', error);
      setError('Failed to load schedule. Please try again.');
      setLoading(false);
    }
  };

  // Get subject name by ID
  const getSubjectName = (subjectId) => {
    // This would ideally fetch from a subjects collection
    // For now, return a placeholder
    return subjectId || 'Unknown Subject';
  };

  // Get teacher name by ID
  const getTeacherName = (teacherId) => {
    // This would ideally fetch from the users collection
    // For now, return a placeholder
    return teacherId || 'Unknown Teacher';
  };

  if (loading) {
    return (
      <View style={styles.scheduleLoadingContainer}>
        <ActivityIndicator size="small" color="#2196F3" />
        <Text style={styles.scheduleLoadingText}>Loading today's schedule...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.scheduleErrorContainer}>
        <Text style={styles.scheduleErrorText}>{error}</Text>
      </View>
    );
  }

  if (todaySchedule.length === 0) {
    return (
      <View style={styles.scheduleEmptyContainer}>
        <IconButton icon="calendar-blank" size={40} color="#9e9e9e" />
        <Text style={styles.scheduleEmptyText}>No classes scheduled for today</Text>
        <Button
          mode="outlined"
          onPress={() => navigation.navigate('StudentClassSchedule')}
          style={styles.scheduleEmptyButton}
        >
          View Full Schedule
        </Button>
      </View>
    );
  }

  return (
    <View style={styles.todayScheduleContainer}>
      <Text style={styles.todayText}>Today: {getDayName()}</Text>

      {todaySchedule.map((entry, index) => {
        const now = new Date();
        const currentHour = now.getHours();
        const currentMinute = now.getMinutes();

        const [startHour, startMinute] = entry.startTime.split(':').map(Number);
        const [endHour, endMinute] = entry.endTime.split(':').map(Number);

        const isCurrentClass =
          (currentHour > startHour || (currentHour === startHour && currentMinute >= startMinute)) &&
          (currentHour < endHour || (currentHour === endHour && currentMinute < endMinute));

        const isPastClass =
          currentHour > endHour || (currentHour === endHour && currentMinute >= endMinute);

        return (
          <Surface
            key={entry.id}
            style={[
              styles.scheduleItemCard,
              isCurrentClass && styles.currentClassCard,
              isPastClass && styles.pastClassCard
            ]}
          >
            <View style={styles.scheduleItemContent}>
              <View style={styles.scheduleTimeContainer}>
                <Text style={styles.scheduleTimeText}>{entry.startTime} - {entry.endTime}</Text>
                <Chip
                  mode="flat"
                  style={[
                    styles.scheduleStatusChip,
                    isCurrentClass && styles.currentClassChip,
                    isPastClass && styles.pastClassChip
                  ]}
                  textStyle={styles.scheduleStatusText}
                >
                  {isCurrentClass ? 'Current' : isPastClass ? 'Completed' : 'Upcoming'}
                </Chip>
              </View>

              <View style={styles.scheduleDetailsContainer}>
                <Text style={styles.scheduleSubjectText}>{getSubjectName(entry.subjectId)}</Text>
                <Text style={styles.scheduleTeacherText}>Teacher: {getTeacherName(entry.teacherId)}</Text>
                <Text style={styles.scheduleRoomText}>Room: {entry.roomNumber || 'N/A'}</Text>
              </View>
            </View>
          </Surface>
        );
      })}
    </View>
  );
};

const screenWidth = Dimensions.get('window').width;

const chartConfig = {
  backgroundColor: 'transparent',
  backgroundGradientFrom: '#ffffff',
  backgroundGradientTo: '#ffffff',
  decimalPlaces: 0,
  color: (opacity = 1) => `rgba(33, 150, 243, ${opacity})`,
  labelColor: (opacity = 1) => `rgba(0, 0, 0, ${opacity})`,
  style: {
    borderRadius: 16
  },
  propsForDots: {
    r: '6',
    strokeWidth: '2',
    stroke: '#ffa726'
  }
};

// Function to get menu items with translations
const getStudentMenuItems = (translate) => [
  {
    category: translate('studentDashboard.menuCategories.academic') || 'Academic',
    gradient: ['#4CAF50', '#81C784'],
    items: [
      { title: translate('studentDashboard.menuItems.grades') || 'Grades', icon: 'chart-line', route: 'StudentGrades', color: '#4CAF50' },
      { title: translate('studentDashboard.menuItems.results') || 'Results', icon: 'trophy', route: 'ResultManagement', color: '#2196F3' },
      { title: translate('studentDashboard.menuItems.subjects') || 'Subjects', icon: 'book-open-variant', route: 'SubjectDetails', color: '#9C27B0' },
      { title: translate('studentDashboard.menuItems.homework') || 'Homework', icon: 'book-check', route: 'HomeworkView', color: '#FF9800' }
    ]
  },
  {
    category: translate('studentDashboard.menuCategories.scheduleAttendance') || 'Schedule & Attendance',
    gradient: ['#2196F3', '#64B5F6'],
    items: [
      { title: translate('studentDashboard.menuItems.schedule') || 'Schedule', icon: 'calendar-clock', route: 'ScheduleManagement', color: '#E91E63' },
      { title: translate('studentDashboard.menuItems.attendance') || 'Attendance', icon: 'account-check', route: 'StudentAttendance', color: '#3F51B5' },
      { title: translate('studentDashboard.menuItems.calendar') || 'Calendar', icon: 'calendar', route: 'CalendarManagement', color: '#009688' },
      { title: translate('studentDashboard.menuItems.examSchedule') || 'Exam Schedule', icon: 'calendar-text', route: 'StudentExamSchedule', color: '#673AB7' }
    ]
  },
  {
    category: translate('studentDashboard.menuCategories.resources') || 'Resources & Support',
    gradient: ['#9C27B0', '#BA68C8'],
    items: [
      { title: translate('studentDashboard.menuItems.library') || 'Library', icon: 'library', route: 'LibraryAccess', color: '#795548' },
      { title: translate('studentDashboard.menuItems.portfolio') || 'Portfolio', icon: 'folder-account', route: 'StudentPortfolio', color: '#607D8B' },
      { title: translate('studentDashboard.menuItems.behavior') || 'Behavior', icon: 'account-star', route: 'BehaviorView', color: '#FF5722' },
      { title: translate('studentDashboard.menuItems.achievements') || 'Achievements', icon: 'medal', route: 'StudentAchievements', color: '#FFC107' }
    ]
  },
  {
    category: translate('studentDashboard.menuCategories.communication') || 'Communication & Reports',
    gradient: ['#FF5722', '#FF8A65'],
    items: [
      { title: translate('studentDashboard.menuItems.messages') || 'Messages', icon: 'message', route: 'CommunicationCenter', color: '#00BCD4' },
      { title: translate('studentDashboard.menuItems.reports') || 'Reports', icon: 'file-document', route: 'Reports', color: '#8BC34A' },
      { title: translate('studentDashboard.menuItems.notifications') || 'Notifications', icon: 'bell', route: 'Notifications', color: '#FF9800' }
    ]
  },
  {
    category: translate('studentDashboard.menuCategories.personalTools') || 'Personal Tools',
    gradient: ['#009688', '#4DB6AC'],
    items: [
      { title: translate('studentDashboard.menuItems.profile') || 'Profile', icon: 'account-circle', route: 'ProfileManagement', color: '#3F51B5' },
      { title: translate('studentDashboard.menuItems.settings') || 'Settings', icon: 'cog', route: 'StudentSettings', color: '#607D8B' },
      { title: translate('studentDashboard.menuItems.help') || 'Help & Support', icon: 'help-circle', route: 'HelpSupport', color: '#9C27B0' }
    ]
  }
];

const StudentDashboard = ({ navigation }) => {
  const [logoutDialogVisible, setLogoutDialogVisible] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [studentData, setStudentData] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [scaleAnim] = useState(new Animated.Value(0.95));
  const [notifications, setNotifications] = useState([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [examSchedules, setExamSchedules] = useState([]);
  const [classInfo, setClassInfo] = useState(null);
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [activeSidebarItem, setActiveSidebarItem] = useState('StudentDashboard');
  const [menuItems, setMenuItems] = useState([]);
  const [showAllItems, setShowAllItems] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');
  const [refreshing, setRefreshing] = useState(false);

  // Theme and language hooks
  const theme = useTheme();
  const { translate, language, isRTL } = useLanguage();

  // Debug log for translation
  useEffect(() => {
    console.log('Current language:', language);
    console.log('Translation test:', translate('studentDashboard.dashboard'));
  }, [language, translate]);

  // Update menu items when language changes
  useEffect(() => {
    console.log('Language changed, updating menu items');
    const updatedMenuItems = getStudentMenuItems(translate);
    setMenuItems(updatedMenuItems);
  }, [translate, language]);

  // Toggle drawer function
  const toggleDrawer = () => {
    setDrawerVisible(!drawerVisible);
    console.log('Drawer toggled:', !drawerVisible);
  };

  useEffect(() => {
    fetchStudentData();
    fetchNotifications();
    // Removed auto-refresh interval that was causing disturbance
    Animated.spring(scaleAnim, {
      toValue: 1,
      friction: 3,
      tension: 40,
      useNativeDriver: true,
    }).start();

    // Set up notification listeners
    const notificationListener = NotificationService.addNotificationListener(
      notification => {
        handleNotification(notification);
      }
    );

    const responseListener = NotificationService.addNotificationResponseListener(
      response => {
        handleNotificationResponse(response);
      }
    );

    // Clean up listeners
    return () => {
      NotificationService.removeNotificationListener(notificationListener);
      NotificationService.removeNotificationListener(responseListener);
    };
  }, []);

  // State for schedule data
  const [scheduleData, setScheduleData] = useState([]);
  const [todaySchedule, setTodaySchedule] = useState([]);
  const [isScheduleLoading, setIsScheduleLoading] = useState(false);

  // State for homework data
  const [homeworkData, setHomeworkData] = useState([]);
  const [isHomeworkLoading, setIsHomeworkLoading] = useState(false);

  // State for behavior data
  const [behaviorData, setBehaviorData] = useState([]);
  const [isBehaviorLoading, setIsBehaviorLoading] = useState(false);

  // Fetch exam schedules and class schedule when classInfo changes
  useEffect(() => {
    if (classInfo) {
      console.log('Class info updated, fetching student data');
      fetchExamSchedules();
      fetchStudentSchedule();
      fetchHomework();
      fetchBehaviorRecords();
    }
  }, [classInfo]);

  // Fetch the student's class schedule
  const fetchStudentSchedule = async () => {
    try {
      setIsScheduleLoading(true);
      console.log('Fetching schedule for student');

      if (!classInfo || !classInfo.id) {
        console.log('No class info available');
        setScheduleData([]);
        setTodaySchedule([]);
        setIsScheduleLoading(false);
        return;
      }

      const classId = classInfo.id;
      const section = classInfo.section;

      console.log(`Fetching schedule for class ${classId}, section ${section}`);

      // Get timetable entries for the student's class and section
      const timetableRef = collection(db, 'timetables');

      // Try different query approaches
      let scheduleEntries = [];

      // Approach 1: Try with compound query if indexes are set up
      try {
        const compoundQuery = query(
          timetableRef,
          where('classId', '==', classId),
          where('section', '==', section),
          where('published', '==', true)
        );

        const compoundSnapshot = await getDocs(compoundQuery);

        if (!compoundSnapshot.empty) {
          compoundSnapshot.forEach((doc) => {
            scheduleEntries.push({ id: doc.id, ...doc.data() });
          });
          console.log('Found schedule entries using compound query:', scheduleEntries.length);
        }
      } catch (indexError) {
        console.log('Compound query failed, likely missing index:', indexError);
        // Continue to fallback approach
      }

      // Approach 2: If compound query failed or returned no results, try with single filter + client filtering
      if (scheduleEntries.length === 0) {
        console.log('Using fallback query approach for schedule');

        // First, get all published timetable entries
        const publishedQuery = query(
          timetableRef,
          where('published', '==', true)
        );

        const publishedSnapshot = await getDocs(publishedQuery);

        // Filter locally for student's class and section
        publishedSnapshot.forEach((doc) => {
          const entryData = doc.data();
          // Check both exact match and array inclusion (some systems store sections as arrays)
          const sectionMatch =
            entryData.section === section ||
            (Array.isArray(entryData.sections) && entryData.sections.includes(section)) ||
            (entryData.sectionName === section);

          if (entryData.classId === classId && sectionMatch) {
            scheduleEntries.push({ id: doc.id, ...entryData });
          }
        });

        console.log('Found schedule entries using fallback approach:', scheduleEntries.length);
      }

      // If still no entries, try class-wide schedule entries
      if (scheduleEntries.length === 0) {
        console.log('Checking for class-wide schedule entries');

        const classQuery = query(
          timetableRef,
          where('classId', '==', classId),
          where('published', '==', true)
        );

        try {
          const classSnapshot = await getDocs(classQuery);

          classSnapshot.forEach((doc) => {
            const entryData = doc.data();
            // Include entries that don't specify a section (class-wide entries)
            if (!entryData.section && !entryData.sections && !entryData.sectionName) {
              scheduleEntries.push({ id: doc.id, ...entryData });
            }
          });

          console.log('Found class-wide schedule entries:', scheduleEntries.length);
        } catch (error) {
          console.log('Error fetching class-wide schedule entries:', error);
        }
      }

      // Enrich schedule data with subject and teacher names
      if (scheduleEntries.length > 0) {
        await enrichScheduleData(scheduleEntries);
      } else {
        setScheduleData([]);
        setTodaySchedule([]);
      }

      // Get today's schedule
      const today = new Date();
      const dayOfWeek = today.getDay(); // 0 = Sunday, 1 = Monday, etc.
      const dayNames = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
      const todayName = dayNames[dayOfWeek];

      const todayEntries = scheduleEntries.filter(entry =>
        entry.day && entry.day.toLowerCase() === todayName
      );

      // Sort by start time
      todayEntries.sort((a, b) => {
        if (a.startTime < b.startTime) return -1;
        if (a.startTime > b.startTime) return 1;
        return 0;
      });

      console.log(`Found ${todayEntries.length} schedule entries for today (${todayName})`);
      setTodaySchedule(todayEntries);

    } catch (error) {
      console.error('Error fetching student schedule:', error);
      setScheduleData([]);
      setTodaySchedule([]);
    } finally {
      setIsScheduleLoading(false);
    }
  };

  // Enrich schedule data with subject and teacher names
  const enrichScheduleData = async (scheduleEntries) => {
    try {
      // Get all unique subject IDs and teacher IDs
      const subjectIds = [...new Set(scheduleEntries.filter(e => e.subjectId).map(e => e.subjectId))];
      const teacherIds = [...new Set(scheduleEntries.filter(e => e.teacherId).map(e => e.teacherId))];

      // Fetch subject details
      const subjectDetails = {};

      for (const subjectId of subjectIds) {
        try {
          const subjectDoc = await getDoc(doc(db, 'subjects', subjectId));
          if (subjectDoc.exists()) {
            subjectDetails[subjectId] = subjectDoc.data();
          }
        } catch (error) {
          console.log(`Error fetching subject ${subjectId}:`, error);
        }
      }

      // Fetch teacher details
      const teacherDetails = {};

      for (const teacherId of teacherIds) {
        try {
          const teacherDoc = await getDoc(doc(db, 'users', teacherId));
          if (teacherDoc.exists()) {
            teacherDetails[teacherId] = teacherDoc.data();
          }
        } catch (error) {
          console.log(`Error fetching teacher ${teacherId}:`, error);
        }
      }

      // Update schedule entries with subject and teacher names
      const enrichedEntries = scheduleEntries.map(entry => {
        const enriched = { ...entry };

        if (entry.subjectId && subjectDetails[entry.subjectId]) {
          enriched.subjectName = subjectDetails[entry.subjectId].name;
        }

        if (entry.teacherId && teacherDetails[entry.teacherId]) {
          const teacher = teacherDetails[entry.teacherId];
          enriched.teacherName = teacher.displayName || `${teacher.firstName || ''} ${teacher.lastName || ''}`.trim();
        }

        return enriched;
      });

      setScheduleData(enrichedEntries);
    } catch (error) {
      console.error('Error enriching schedule data:', error);
    }
  };

  // Fetch homework assignments for the student
  const fetchHomework = async () => {
    try {
      setIsHomeworkLoading(true);
      console.log('Fetching homework for student');

      if (!classInfo || !classInfo.id) {
        console.log('No class info available');
        setHomeworkData([]);
        setIsHomeworkLoading(false);
        return;
      }

      const classId = classInfo.id;
      const section = classInfo.section;
      const studentId = auth.currentUser.uid;

      console.log(`Fetching homework for class ${classId}, section ${section}, student ${studentId}`);

      // Get homework assignments for the student's class and section
      const homeworkRef = collection(db, 'homework');

      // Try different query approaches
      let homeworkEntries = [];

      // Approach 1: Try with compound query if indexes are set up
      try {
        const compoundQuery = query(
          homeworkRef,
          where('classId', '==', classId),
          where('section', '==', section),
          where('published', '==', true)
        );

        const compoundSnapshot = await getDocs(compoundQuery);

        if (!compoundSnapshot.empty) {
          compoundSnapshot.forEach((doc) => {
            homeworkEntries.push({ id: doc.id, ...doc.data() });
          });
          console.log('Found homework entries using compound query:', homeworkEntries.length);
        }
      } catch (indexError) {
        console.log('Compound query failed, likely missing index:', indexError);
        // Continue to fallback approach
      }

      // Approach 2: If compound query failed or returned no results, try with single filter + client filtering
      if (homeworkEntries.length === 0) {
        console.log('Using fallback query approach for homework');

        // First, get all published homework entries
        const publishedQuery = query(
          homeworkRef,
          where('published', '==', true)
        );

        const publishedSnapshot = await getDocs(publishedQuery);

        // Filter locally for student's class and section
        publishedSnapshot.forEach((doc) => {
          const entryData = doc.data();
          // Check both exact match and array inclusion (some systems store sections as arrays)
          const sectionMatch =
            entryData.section === section ||
            (Array.isArray(entryData.sections) && entryData.sections.includes(section)) ||
            (entryData.sectionName === section);

          if (entryData.classId === classId && sectionMatch) {
            homeworkEntries.push({ id: doc.id, ...entryData });
          }
        });

        console.log('Found homework entries using fallback approach:', homeworkEntries.length);
      }

      // If still no entries, try class-wide homework entries
      if (homeworkEntries.length === 0) {
        console.log('Checking for class-wide homework entries');

        const classQuery = query(
          homeworkRef,
          where('classId', '==', classId),
          where('published', '==', true)
        );

        try {
          const classSnapshot = await getDocs(classQuery);

          classSnapshot.forEach((doc) => {
            const entryData = doc.data();
            // Include entries that don't specify a section (class-wide entries)
            if (!entryData.section && !entryData.sections && !entryData.sectionName) {
              homeworkEntries.push({ id: doc.id, ...entryData });
            }
          });

          console.log('Found class-wide homework entries:', classSnapshot.size);
        } catch (error) {
          console.log('Error fetching class-wide homework entries:', error);
        }
      }

      // Check for homework submissions by this student
      if (homeworkEntries.length > 0) {
        const submissionsRef = collection(db, 'homework_submissions');

        for (let i = 0; i < homeworkEntries.length; i++) {
          const homework = homeworkEntries[i];

          try {
            const submissionQuery = query(
              submissionsRef,
              where('homeworkId', '==', homework.id),
              where('studentId', '==', studentId)
            );

            const submissionSnapshot = await getDocs(submissionQuery);

            if (!submissionSnapshot.empty) {
              const submissionData = submissionSnapshot.docs[0].data();
              homeworkEntries[i] = {
                ...homework,
                submission: {
                  id: submissionSnapshot.docs[0].id,
                  ...submissionData
                },
                status: submissionData.status || 'submitted'
              };
            } else {
              homeworkEntries[i] = {
                ...homework,
                status: 'pending'
              };
            }
          } catch (error) {
            console.log(`Error fetching submission for homework ${homework.id}:`, error);
          }
        }
      }

      // Sort by due date (most recent first)
      homeworkEntries.sort((a, b) => {
        // Handle different date formats
        const dateA = a.dueDate instanceof Date ? a.dueDate : new Date(a.dueDate);
        const dateB = b.dueDate instanceof Date ? b.dueDate : new Date(b.dueDate);

        // Check if dates are valid
        const validDateA = !isNaN(dateA.getTime());
        const validDateB = !isNaN(dateB.getTime());

        // If both dates are valid, compare them
        if (validDateA && validDateB) {
          return dateB - dateA; // Most recent first
        }

        // If only one date is valid, prioritize the valid one
        if (validDateA) return -1;
        if (validDateB) return 1;

        // If neither date is valid, maintain original order
        return 0;
      });

      // Enrich homework data with subject names
      await enrichHomeworkData(homeworkEntries);

    } catch (error) {
      console.error('Error fetching homework:', error);
      setHomeworkData([]);
    } finally {
      setIsHomeworkLoading(false);
    }
  };

  // Enrich homework data with subject names
  const enrichHomeworkData = async (homeworkEntries) => {
    try {
      // Get all unique subject IDs
      const subjectIds = [...new Set(homeworkEntries.filter(e => e.subjectId).map(e => e.subjectId))];

      // Fetch subject details
      const subjectDetails = {};

      for (const subjectId of subjectIds) {
        try {
          const subjectDoc = await getDoc(doc(db, 'subjects', subjectId));
          if (subjectDoc.exists()) {
            subjectDetails[subjectId] = subjectDoc.data();
          }
        } catch (error) {
          console.log(`Error fetching subject ${subjectId}:`, error);
        }
      }

      // Update homework entries with subject names
      const enrichedEntries = homeworkEntries.map(entry => {
        const enriched = { ...entry };

        if (entry.subjectId && subjectDetails[entry.subjectId]) {
          enriched.subjectName = subjectDetails[entry.subjectId].name;
        }

        return enriched;
      });

      setHomeworkData(enrichedEntries);
    } catch (error) {
      console.error('Error enriching homework data:', error);
    }
  };

  // Fetch behavior records for the student
  const fetchBehaviorRecords = async () => {
    try {
      setIsBehaviorLoading(true);
      console.log('Fetching behavior records for student');

      const studentId = auth.currentUser.uid;

      console.log(`Fetching behavior records for student ${studentId}`);

      // Get behavior records for the student
      const behaviorRef = collection(db, 'behavior');

      // First try: Use a simple query without orderBy
      try {
        const simpleQuery = query(
          behaviorRef,
          where('studentId', '==', studentId)
        );

        const behaviorSnapshot = await getDocs(simpleQuery);

        const behaviorEntries = [];
        behaviorSnapshot.forEach((doc) => {
          behaviorEntries.push({ id: doc.id, ...doc.data() });
        });

        // Sort manually on the client side
        behaviorEntries.sort((a, b) => {
          // Handle different timestamp formats
          const timeA = a.timestamp?.seconds ? a.timestamp.seconds : a.timestamp ? new Date(a.timestamp).getTime() / 1000 : 0;
          const timeB = b.timestamp?.seconds ? b.timestamp.seconds : b.timestamp ? new Date(b.timestamp).getTime() / 1000 : 0;
          return timeB - timeA; // Descending order (newest first)
        });

        // Limit to 10 records after sorting
        const limitedEntries = behaviorEntries.slice(0, 10);

        console.log(`Found ${behaviorEntries.length} behavior records for student (showing ${limitedEntries.length})`);
        setBehaviorData(limitedEntries);
      } catch (error) {
        console.log('Error fetching behavior records:', error);
        setBehaviorData([]);
      }
    } catch (error) {
      console.error('Error fetching behavior records:', error);
      setBehaviorData([]);
    } finally {
      setIsBehaviorLoading(false);
    }
  };

  const fetchStudentData = async () => {
    try {
      // Get user data
      const studentRef = doc(db, 'users', auth.currentUser.uid);
      const studentDoc = await getDoc(studentRef);

      if (studentDoc.exists()) {
        const userData = studentDoc.data();
        setStudentData(userData);
        console.log('User data:', userData);

        // Check if user data has classId and section directly
        if (userData.classId && (userData.sectionName || userData.section)) {
          const sectionName = userData.sectionName || userData.section;
          console.log(`Found class info in user data: Class ${userData.classId}, Section ${sectionName}`);

          try {
            // Get class details
            const classRef = doc(db, 'classes', userData.classId);
            const classDoc = await getDoc(classRef);

            if (classDoc.exists()) {
              const classData = classDoc.data();
              setClassInfo({
                id: userData.classId,
                name: classData.name || 'Unknown Class',
                section: sectionName,
                teacherId: classData.teacherId,
                teacherName: classData.teacherName
              });
              console.log('Class info set from user data:', classData);

              // Update student data with attendance and performance if missing
              if (!userData.attendance || !userData.performance) {
                await fetchStudentStats(userData.classId, sectionName, auth.currentUser.uid);
              }
            } else {
              console.log(`Class document not found for ID: ${userData.classId}`);
              // Set basic class info even if class document doesn't exist
              setClassInfo({
                id: userData.classId,
                name: userData.className || 'Unknown Class',
                section: sectionName,
                teacherId: null,
                teacherName: 'Unknown Teacher'
              });
            }
          } catch (classError) {
            console.error('Error fetching class details:', classError);
            // Set basic class info even if there's an error
            setClassInfo({
              id: userData.classId,
              name: userData.className || 'Unknown Class',
              section: sectionName,
              teacherId: null,
              teacherName: 'Unknown Teacher'
            });
          }

          return; // Exit early since we found the class info
        }

        // Fallback: Get student's class information from students collection
        console.log('Fetching student info from students collection');
        const studentsRef = collection(db, 'students');
        const studentQuery = query(studentsRef, where('id', '==', auth.currentUser.uid));
        const studentSnapshot = await getDocs(studentQuery);

        if (!studentSnapshot.empty) {
          const studentData = studentSnapshot.docs[0].data();
          console.log('Student data from students collection:', studentData);

          if (!studentData.classId || (!studentData.section && !studentData.sectionName)) {
            console.log('Student record missing classId or section');

            // Try one more fallback - check if user has a class assignment in the enrollments collection
            await checkEnrollments();
            return;
          }

          const sectionName = studentData.sectionName || studentData.section;

          try {
            // Get class details
            const classRef = doc(db, 'classes', studentData.classId);
            const classDoc = await getDoc(classRef);

            if (classDoc.exists()) {
              const classData = classDoc.data();
              setClassInfo({
                id: studentData.classId,
                name: classData.name || 'Unknown Class',
                section: sectionName,
                teacherId: classData.teacherId,
                teacherName: classData.teacherName
              });
              console.log('Class info set from students collection:', classData);

              // Update student data with attendance and performance
              await fetchStudentStats(studentData.classId, sectionName, auth.currentUser.uid);
            } else {
              console.log(`Class document not found for ID: ${studentData.classId}`);
              // Set basic class info even if class document doesn't exist
              setClassInfo({
                id: studentData.classId,
                name: 'Unknown Class',
                section: sectionName,
                teacherId: null,
                teacherName: 'Unknown Teacher'
              });
            }
          } catch (classError) {
            console.error('Error fetching class details:', classError);
            // Set basic class info even if there's an error
            setClassInfo({
              id: studentData.classId,
              name: 'Unknown Class',
              section: sectionName,
              teacherId: null,
              teacherName: 'Unknown Teacher'
            });
          }
        } else {
          console.log('No student record found in students collection');
          // Try one more fallback - check if user has a class assignment in the enrollments collection
          await checkEnrollments();
        }
      } else {
        console.log('User document not found');
      }
    } catch (error) {
      console.error('Error fetching student data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Check enrollments collection as a last resort
  const checkEnrollments = async () => {
    try {
      console.log('Checking enrollments collection for student class info');
      const enrollmentsRef = collection(db, 'enrollments');
      const enrollmentQuery = query(
        enrollmentsRef,
        where('studentId', '==', auth.currentUser.uid),
        where('status', '==', 'active')
      );

      const enrollmentSnapshot = await getDocs(enrollmentQuery);

      if (!enrollmentSnapshot.empty) {
        const enrollmentData = enrollmentSnapshot.docs[0].data();
        console.log('Found enrollment data:', enrollmentData);

        if (enrollmentData.classId && enrollmentData.section) {
          // Get class details
          const classRef = doc(db, 'classes', enrollmentData.classId);
          const classDoc = await getDoc(classRef);

          if (classDoc.exists()) {
            const classData = classDoc.data();
            setClassInfo({
              id: enrollmentData.classId,
              name: classData.name || 'Unknown Class',
              section: enrollmentData.section,
              teacherId: classData.teacherId,
              teacherName: classData.teacherName
            });
            console.log('Class info set from enrollments collection:', classData);

            // Update student data with attendance and performance
            await fetchStudentStats(enrollmentData.classId, enrollmentData.section, auth.currentUser.uid);
          }
        }
      } else {
        console.log('No enrollment record found');
      }
    } catch (error) {
      console.error('Error checking enrollments:', error);
    }
  };

  // Fetch student attendance and performance stats
  const fetchStudentStats = async (classId, section, studentId) => {
    try {
      console.log('Fetching student stats for:', studentId);

      // Fetch attendance data
      const attendanceRef = collection(db, 'attendance');
      const attendanceQuery = query(
        attendanceRef,
        where('studentId', '==', studentId)
      );

      const attendanceSnapshot = await getDocs(attendanceQuery);

      let present = 0;
      let absent = 0;

      attendanceSnapshot.forEach(doc => {
        const data = doc.data();
        if (data.status === 'present') {
          present++;
        } else if (data.status === 'absent') {
          absent++;
        }
      });

      // Fetch grades data for performance
      const gradesRef = collection(db, 'grades');
      const gradesQuery = query(
        gradesRef,
        where('studentId', '==', studentId),
        where('status', '==', 'published')
      );

      const gradesSnapshot = await getDocs(gradesQuery);

      const termScores = [0, 0, 0, 0]; // Term 1-4 scores
      let gpa = 0;

      if (!gradesSnapshot.empty) {
        let totalScore = 0;
        let totalMaxScore = 0;

        gradesSnapshot.forEach(doc => {
          const data = doc.data();

          // Update term scores if available
          if (data.term && data.score !== undefined) {
            const termIndex = parseInt(data.term.replace('Term ', '')) - 1;
            if (termIndex >= 0 && termIndex < 4) {
              termScores[termIndex] = Math.max(termScores[termIndex], data.percentage || 0);
            }
          }

          // Calculate GPA
          if (data.score !== undefined && data.maxScore) {
            totalScore += data.score;
            totalMaxScore += data.maxScore;
          }
        });

        // Calculate GPA (4.0 scale)
        if (totalMaxScore > 0) {
          const percentage = (totalScore / totalMaxScore) * 100;
          // Simple GPA calculation (can be refined based on school's grading system)
          if (percentage >= 90) gpa = 4.0;
          else if (percentage >= 80) gpa = 3.5;
          else if (percentage >= 70) gpa = 3.0;
          else if (percentage >= 60) gpa = 2.5;
          else if (percentage >= 50) gpa = 2.0;
          else gpa = 1.0;
        }
      }

      // Update student data with the fetched stats
      setStudentData(prevData => ({
        ...prevData,
        attendance: {
          present,
          absent
        },
        performance: termScores,
        gpa: gpa.toFixed(1)
      }));

      // Also update the user document with these stats for future use
      const userRef = doc(db, 'users', studentId);
      await updateDoc(userRef, {
        attendance: { present, absent },
        performance: termScores,
        gpa: gpa.toFixed(1),
        updatedAt: new Date().toISOString()
      });

      console.log('Updated student stats:', { attendance: { present, absent }, performance: termScores, gpa });

    } catch (error) {
      console.error('Error fetching student stats:', error);
    }
  };

  const fetchNotifications = async () => {
    try {
      setIsLoading(true);
      const notificationData = await NotificationService.getNotifications(20);
      setNotifications(notificationData);
      const count = await NotificationService.getUnreadCount();
      setUnreadCount(count);
    } catch (error) {
      console.error('Error fetching notifications:', error);
      // Show error message to user
      setNotifications([]);
      setUnreadCount(0);
    } finally {
      setIsLoading(false);
    }
  };

  const handleLogout = async () => {
    try {
      await signOut(auth);
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  const onSearch = (query) => {
    setSearchQuery(query);
  };

  const fetchExamSchedules = async () => {
    try {
      console.log('Fetching exam schedules for student');

      // Wait for classInfo to be set
      if (!classInfo) {
        console.log('Waiting for class info to be set');
        setExamSchedules([]);
        return;
      }

      const classId = classInfo.id;
      const section = classInfo.section;

      if (!classId || !section) {
        console.log('Student has no class or section assigned in classInfo');
        setExamSchedules([]);
        return;
      }

      console.log(`Student class: ${classId}, section: ${section}`);

      // Get exams for the student's class and section
      const examsRef = collection(db, 'exams');

      // Try different query approaches to find exams
      let examsData = [];

      // Approach 1: Try with a simple query without compound conditions
      try {
        // Get all exams (we'll filter client-side)
        const simpleQuery = query(examsRef);
        const examsSnapshot = await getDocs(simpleQuery);

        if (!examsSnapshot.empty) {
          examsSnapshot.forEach((doc) => {
            const examData = doc.data();

            // Only include published exams for this class/section
            if (examData.published === true) {
              // Check both exact match and array inclusion (some systems store sections as arrays)
              const sectionMatch =
                examData.section === section ||
                (Array.isArray(examData.sections) && examData.sections.includes(section)) ||
                (examData.sectionName === section);

              // Include if it matches the class and section, or if it's a class-wide exam
              if (examData.classId === classId &&
                  (sectionMatch || (!examData.section && !examData.sections && !examData.sectionName))) {
                examsData.push({ id: doc.id, ...examData });
              }
            }
          });
          console.log('Found exams using simple query:', examsData.length);
        }
      } catch (error) {
        console.log('Simple query failed:', error);
        // Set empty data to avoid breaking the UI
        examsData = [];
      }

      // Sort by date
      examsData.sort((a, b) => {
        // Handle different date formats
        const dateA = a.date instanceof Date ? a.date : new Date(a.date);
        const dateB = b.date instanceof Date ? b.date : new Date(b.date);

        // Check if dates are valid
        const validDateA = !isNaN(dateA.getTime());
        const validDateB = !isNaN(dateB.getTime());

        // If both dates are valid, compare them
        if (validDateA && validDateB) {
          return dateA - dateB;
        }

        // If only one date is valid, prioritize the valid one
        if (validDateA) return -1;
        if (validDateB) return 1;

        // If neither date is valid, maintain original order
        return 0;
      });

      // Filter out past exams older than 7 days
      const sevenDaysAgo = new Date();
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

      const filteredExams = examsData.filter(exam => {
        const examDate = new Date(exam.date);
        return !isNaN(examDate.getTime()) && examDate >= sevenDaysAgo;
      });

      console.log(`Found ${examsData.length} exam schedules for student (${filteredExams.length} upcoming or recent)`);
      setExamSchedules(filteredExams);

      // If we have exams, fetch additional details like subject names
      if (filteredExams.length > 0) {
        await enrichExamData(filteredExams);
      }
    } catch (error) {
      console.error('Error fetching exam schedules:', error);
      setExamSchedules([]);
    }
  };

  // Enrich exam data with subject names and other details
  const enrichExamData = async (exams) => {
    try {
      // Get all unique subject IDs from exams
      const subjectIds = [...new Set(exams.filter(e => e.subjectId).map(e => e.subjectId))];

      if (subjectIds.length === 0) return;

      // Fetch subject details
      const subjectDetails = {};

      for (const subjectId of subjectIds) {
        try {
          const subjectDoc = await getDoc(doc(db, 'subjects', subjectId));
          if (subjectDoc.exists()) {
            subjectDetails[subjectId] = subjectDoc.data();
          }
        } catch (error) {
          console.log(`Error fetching subject ${subjectId}:`, error);
        }
      }

      // Update exams with subject names
      const enrichedExams = exams.map(exam => {
        const enriched = { ...exam };

        if (exam.subjectId && subjectDetails[exam.subjectId]) {
          enriched.subjectName = subjectDetails[exam.subjectId].name;
        }

        return enriched;
      });

      setExamSchedules(enrichedExams);
    } catch (error) {
      console.error('Error enriching exam data:', error);
    }
  };

  // Handle incoming notification while app is in foreground
  const handleNotification = (notification) => {
    try {
      console.log('Received notification:', notification);

      // Refresh notifications list
      fetchNotifications();

      // Handle specific notification types
      if (notification.request?.content?.data?.type === 'exam_schedule') {
        // Show an alert for exam schedule notification
        Alert.alert(
          notification.request.content.title || 'New Exam Schedule',
          notification.request.content.body || 'A new exam has been scheduled.',
          [
            {
              text: 'View Details',
              onPress: async () => {
                const navAction = await NotificationService.handleExamScheduleNotification(notification.request.content);
                if (navAction) {
                  navigation.navigate(navAction.screen, navAction.params);
                }
              },
            },
            {
              text: 'Dismiss',
              style: 'cancel',
            },
          ]
        );
      }
    } catch (error) {
      console.error('Error handling notification:', error);
    }
  };

  // Handle notification response when user taps on a notification
  const handleNotificationResponse = async (response) => {
    try {
      console.log('Notification response:', response);

      const { notification } = response;
      const data = notification.request.content.data;

      console.log('Notification data:', data);

      // Mark the notification as read
      if (data.notificationId) {
        console.log('Marking notification as read:', data.notificationId);
        await NotificationService.markAsRead(data.notificationId);
      }

      // Handle specific notification types
      if (data.type === 'exam_schedule') {
        console.log('Handling exam schedule notification');
        const navAction = await NotificationService.handleExamScheduleNotification(notification.request.content);
        console.log('Navigation action:', navAction);

        if (navAction) {
          console.log(`Navigating to ${navAction.screen}`, navAction.params);
          navigation.navigate(navAction.screen, navAction.params);
        } else {
          console.log('No navigation action returned, navigating to StudentExamSchedule');
          navigation.navigate('StudentExamSchedule');
        }
      }

      // Refresh notifications
      fetchNotifications();
    } catch (error) {
      console.error('Error handling notification response:', error);
      // Fallback navigation
      try {
        navigation.navigate('StudentExamSchedule');
      } catch (navError) {
        console.error('Error navigating to StudentExamSchedule:', navError);
      }
    }
  };

  // Handle pull-to-refresh
  const onRefresh = async () => {
    try {
      setRefreshing(true);
      await Promise.all([
        fetchStudentData(),
        fetchNotifications(),
        fetchExamSchedules(),
        fetchStudentSchedule(),
        fetchHomework(),
        fetchBehaviorRecords()
      ]);
    } catch (error) {
      console.error('Error refreshing data:', error);
    } finally {
      setRefreshing(false);
    }
  };

  // Filter menu items based on search query
  const filteredMenuItems = useMemo(() => {
    if (!searchQuery.trim()) return menuItems;

    return menuItems.map(category => {
      const filteredItems = category.items.filter(item =>
        item.title.toLowerCase().includes(searchQuery.toLowerCase())
      );

      return filteredItems.length > 0
        ? { ...category, items: filteredItems }
        : null;
    }).filter(Boolean);
  }, [menuItems, searchQuery]);

  // Get random color for menu items
  const getRandomColor = (index) => {
    const colors = ['#4CAF50', '#2196F3', '#9C27B0', '#FF9800', '#E91E63', '#3F51B5', '#009688', '#673AB7', '#FF5722', '#795548'];
    return colors[index % colors.length];
  };

  const renderWelcomeCard = () => (
    <Animatable.View animation="fadeIn" duration={800}>
      <Surface style={styles.welcomeCard}>
        <LinearGradient
          colors={['#1976d2', '#64B5F6']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.welcomeGradient}
        >
          <View style={styles.welcomeContent}>
            <Avatar.Image
              size={80}
              source={{ uri: studentData?.photoURL || `https://ui-avatars.com/api/?name=${studentData?.name || 'Student'}&background=1976d2&color=fff` }}
              style={styles.avatar}
            />
            <Animatable.View animation="fadeInUp" delay={300} duration={800} style={styles.welcomeText}>
              <Title style={styles.welcomeName}>
                {translate('studentDashboard.welcome') || 'Welcome'}, {studentData?.name}
              </Title>
              <View style={styles.roleContainer}>
                <Badge style={styles.roleBadge}>
                  {translate('roles.student') || 'Student'}
                </Badge>
              </View>

              {studentData?.performance && (
                <View style={styles.statsContainer}>
                  <View style={styles.statItem}>
                    <Text style={styles.statValue}>
                      {studentData?.gpa || '3.8'}
                    </Text>
                    <Text style={styles.statLabel}>
                      {translate('studentDashboard.gpa') || 'GPA'}
                    </Text>
                  </View>

                  <View style={styles.statItem}>
                    <Text style={styles.statValue}>
                      {studentData?.attendance?.present
                        ? `${Math.round((studentData.attendance.present / (studentData.attendance.present + studentData.attendance.absent || 1)) * 100)}%`
                        : '95%'}
                    </Text>
                    <Text style={styles.statLabel}>
                      {translate('studentDashboard.attendance') || 'Attendance'}
                    </Text>
                  </View>
                </View>
              )}
            </Animatable.View>
          </View>
        </LinearGradient>
      </Surface>
    </Animatable.View>
  );

  const renderMenuItem = (item) => (
    <Animated.View
      key={item.route}
      style={[
        styles.menuItem,
        { transform: [{ scale: scaleAnim }] }
      ]}
    >
      <TouchableOpacity
        onPress={() => navigation.navigate(item.route)}
        style={styles.menuTouchable}
      >
        <Surface style={[styles.menuCard, { borderLeftColor: item.color }]}>
          <LinearGradient
            colors={['#ffffff', '#f5f5f5']}
            style={styles.menuGradient}
          >
            <IconButton
              icon={item.icon}
              size={30}
              color={item.color}
              style={styles.menuIcon}
            />
            <Title style={styles.menuTitle}>{item.title}</Title>
          </LinearGradient>
        </Surface>
      </TouchableOpacity>
    </Animated.View>
  );

  const renderHeaderButtons = () => (
    <View style={styles.headerButtons}>
      <IconButton
        icon="bell"
        size={24}
        color="#2196F3"
        style={styles.headerButton}
        onPress={() => navigation.navigate('Notifications')}
      />
      {studentData?.notifications?.length > 0 && (
        <Badge
          visible={true}
          size={16}
          style={styles.notificationBadge}
        >
          {studentData.notifications.length}
        </Badge>
      )}
      <IconButton
        icon="account-circle"
        size={24}
        color="#2196F3"
        style={styles.headerButton}
        onPress={() => navigation.navigate('ProfileManagement')}
      />
      <IconButton
        icon="logout"
        size={24}
        color="#2196F3"
        style={styles.headerButton}
        onPress={() => setLogoutDialogVisible(true)}
      />
    </View>
  );

  // Render today's schedule section
  const renderTodaySchedule = () => {
    if (isScheduleLoading) {
      return (
        <Surface style={styles.sectionCard}>
          <View style={styles.sectionHeader}>
            <Title style={styles.sectionTitle}>
              {translate('studentDashboard.todaySchedule') || "Today's Schedule"}
            </Title>
          </View>
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="small" color="#1976d2" />
            <Text style={styles.loadingText}>
              {translate('common.loading') || "Loading..."}
            </Text>
          </View>
        </Surface>
      );
    }

    if (todaySchedule.length === 0) {
      return (
        <Surface style={styles.sectionCard}>
          <View style={styles.sectionHeader}>
            <Title style={styles.sectionTitle}>
              {translate('studentDashboard.todaySchedule') || "Today's Schedule"}
            </Title>
          </View>
          <View style={styles.emptyContainer}>
            <IconButton icon="calendar-blank" size={40} color="#9e9e9e" />
            <Text style={styles.emptyText}>
              {translate('studentDashboard.noScheduleToday') || "No classes scheduled for today"}
            </Text>
          </View>
        </Surface>
      );
    }

    return (
      <Animatable.View animation="fadeIn" duration={800}>
        <Surface style={styles.sectionCard}>
          <View style={styles.sectionHeader}>
            <Title style={styles.sectionTitle}>
              {translate('studentDashboard.todaySchedule') || "Today's Schedule"}
            </Title>
            <Button
              mode="text"
              onPress={() => navigation.navigate('ScheduleManagement')}
              labelStyle={styles.viewAllButton}
            >
              {translate('common.viewAll') || "View All"}
            </Button>
          </View>

          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.scheduleScrollView}>
            {todaySchedule.map((schedule, index) => (
              <Animatable.View
                key={schedule.id || index}
                animation="fadeInRight"
                delay={index * 100}
                duration={500}
              >
                <Surface style={styles.scheduleCard}>
                  <View style={styles.scheduleTimeContainer}>
                    <Text style={styles.scheduleTime}>
                      {schedule.startTime || '00:00'} - {schedule.endTime || '00:00'}
                    </Text>
                  </View>

                  <View style={styles.scheduleDetails}>
                    <Text style={styles.scheduleSubject}>
                      {schedule.subjectName || schedule.subject || 'Unknown Subject'}
                    </Text>

                    <View style={styles.scheduleTeacherContainer}>
                      <IconButton icon="account" size={16} color="#757575" style={styles.scheduleTeacherIcon} />
                      <Text style={styles.scheduleTeacher}>
                        {schedule.teacherName || 'Unknown Teacher'}
                      </Text>
                    </View>

                    {schedule.room && (
                      <View style={styles.scheduleRoomContainer}>
                        <IconButton icon="door" size={16} color="#757575" style={styles.scheduleRoomIcon} />
                        <Text style={styles.scheduleRoom}>
                          {translate('common.room') || 'Room'}: {schedule.room}
                        </Text>
                      </View>
                    )}
                  </View>
                </Surface>
              </Animatable.View>
            ))}
          </ScrollView>
        </Surface>
      </Animatable.View>
    );
  };

  // Render homework section
  const renderHomeworkSection = () => {
    if (isHomeworkLoading) {
      return (
        <Surface style={styles.sectionCard}>
          <View style={styles.sectionHeader}>
            <Title style={styles.sectionTitle}>
              {translate('studentDashboard.homework') || "Homework"}
            </Title>
          </View>
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="small" color="#1976d2" />
            <Text style={styles.loadingText}>
              {translate('common.loading') || "Loading..."}
            </Text>
          </View>
        </Surface>
      );
    }

    if (homeworkData.length === 0) {
      return (
        <Surface style={styles.sectionCard}>
          <View style={styles.sectionHeader}>
            <Title style={styles.sectionTitle}>
              {translate('studentDashboard.homework') || "Homework"}
            </Title>
          </View>
          <View style={styles.emptyContainer}>
            <IconButton icon="book-check" size={40} color="#9e9e9e" />
            <Text style={styles.emptyText}>
              {translate('studentDashboard.noHomework') || "No homework assignments"}
            </Text>
          </View>
        </Surface>
      );
    }

    // Only show up to 3 homework items in the dashboard
    const displayHomework = homeworkData.slice(0, 3);

    return (
      <Animatable.View animation="fadeIn" duration={800} delay={200}>
        <Surface style={styles.sectionCard}>
          <View style={styles.sectionHeader}>
            <Title style={styles.sectionTitle}>
              {translate('studentDashboard.homework') || "Homework"}
            </Title>
            <Button
              mode="text"
              onPress={() => navigation.navigate('HomeworkView')}
              labelStyle={styles.viewAllButton}
            >
              {translate('common.viewAll') || "View All"}
            </Button>
          </View>

          {displayHomework.map((homework, index) => (
            <Animatable.View
              key={homework.id || index}
              animation="fadeInUp"
              delay={index * 100}
              duration={500}
            >
              <TouchableOpacity
                style={styles.homeworkItem}
                onPress={() => navigation.navigate('HomeworkView', { homeworkId: homework.id })}
              >
                <View style={styles.homeworkSubjectContainer}>
                  <IconButton
                    icon="book-open-variant"
                    size={20}
                    color="#1976d2"
                    style={styles.homeworkIcon}
                  />
                  <Text style={styles.homeworkSubject}>
                    {homework.subjectName || homework.subject || 'Unknown Subject'}
                  </Text>
                </View>

                <Text style={styles.homeworkTitle} numberOfLines={2}>
                  {homework.title || 'Untitled Assignment'}
                </Text>

                <View style={styles.homeworkFooter}>
                  <View style={styles.homeworkDueContainer}>
                    <IconButton icon="calendar" size={16} color="#757575" style={styles.homeworkDueIcon} />
                    <Text style={styles.homeworkDue}>
                      {translate('common.due') || 'Due'}: {
                        homework.dueDate instanceof Date
                          ? homework.dueDate.toLocaleDateString()
                          : new Date(homework.dueDate).toLocaleDateString()
                      }
                    </Text>
                  </View>

                  <Chip
                    style={[
                      styles.homeworkStatus,
                      {
                        backgroundColor:
                          homework.status === 'submitted' ? '#4CAF50' :
                          homework.status === 'graded' ? '#2196F3' :
                          homework.status === 'late' ? '#FF9800' : '#F44336'
                      }
                    ]}
                    textStyle={styles.homeworkStatusText}
                  >
                    {homework.status === 'submitted'
                      ? (translate('homework.submitted') || 'Submitted')
                      : homework.status === 'graded'
                        ? (translate('homework.graded') || 'Graded')
                        : homework.status === 'late'
                          ? (translate('homework.late') || 'Late')
                          : (translate('homework.pending') || 'Pending')
                    }
                  </Chip>
                </View>
              </TouchableOpacity>

              {index < displayHomework.length - 1 && <Divider style={styles.homeworkDivider} />}
            </Animatable.View>
          ))}
        </Surface>
      </Animatable.View>
    );
  };

  // Render behavior section
  const renderBehaviorSection = () => {
    if (isBehaviorLoading) {
      return (
        <Surface style={styles.sectionCard}>
          <View style={styles.sectionHeader}>
            <Title style={styles.sectionTitle}>
              {translate('studentDashboard.behavior') || "Behavior"}
            </Title>
          </View>
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="small" color="#1976d2" />
            <Text style={styles.loadingText}>
              {translate('common.loading') || "Loading..."}
            </Text>
          </View>
        </Surface>
      );
    }

    if (behaviorData.length === 0) {
      return (
        <Surface style={styles.sectionCard}>
          <View style={styles.sectionHeader}>
            <Title style={styles.sectionTitle}>
              {translate('studentDashboard.behavior') || "Behavior"}
            </Title>
          </View>
          <View style={styles.emptyContainer}>
            <IconButton icon="account-star" size={40} color="#9e9e9e" />
            <Text style={styles.emptyText}>
              {translate('studentDashboard.noBehavior') || "No behavior records"}
            </Text>
          </View>
        </Surface>
      );
    }

    // Only show up to 3 behavior items in the dashboard
    const displayBehavior = behaviorData.slice(0, 3);

    return (
      <Animatable.View animation="fadeIn" duration={800} delay={400}>
        <Surface style={styles.sectionCard}>
          <View style={styles.sectionHeader}>
            <Title style={styles.sectionTitle}>
              {translate('studentDashboard.behavior') || "Behavior"}
            </Title>
            <Button
              mode="text"
              onPress={() => navigation.navigate('BehaviorView')}
              labelStyle={styles.viewAllButton}
            >
              {translate('common.viewAll') || "View All"}
            </Button>
          </View>

          {displayBehavior.map((behavior, index) => (
            <Animatable.View
              key={behavior.id || index}
              animation="fadeInUp"
              delay={index * 100}
              duration={500}
            >
              <TouchableOpacity
                style={styles.behaviorItem}
                onPress={() => navigation.navigate('BehaviorView', { behaviorId: behavior.id })}
              >
                <View style={styles.behaviorHeader}>
                  <Chip
                    style={[
                      styles.behaviorType,
                      {
                        backgroundColor:
                          behavior.type === 'positive' ? '#4CAF50' :
                          behavior.type === 'negative' ? '#F44336' : '#FF9800'
                      }
                    ]}
                    textStyle={styles.behaviorTypeText}
                  >
                    {behavior.type === 'positive'
                      ? (translate('behavior.positive') || 'Positive')
                      : behavior.type === 'negative'
                        ? (translate('behavior.negative') || 'Negative')
                        : (translate('behavior.neutral') || 'Neutral')
                    }
                  </Chip>

                  <Text style={styles.behaviorDate}>
                    {behavior.timestamp instanceof Date
                      ? behavior.timestamp.toLocaleDateString()
                      : new Date(behavior.timestamp).toLocaleDateString()
                    }
                  </Text>
                </View>

                <Text style={styles.behaviorCategory}>
                  {behavior.category || 'Uncategorized'}
                </Text>

                <Text style={styles.behaviorDescription} numberOfLines={2}>
                  {behavior.description || 'No description provided'}
                </Text>

                <View style={styles.behaviorFooter}>
                  <View style={styles.behaviorPointsContainer}>
                    <IconButton icon="star" size={16} color="#FFD700" style={styles.behaviorPointsIcon} />
                    <Text style={styles.behaviorPoints}>
                      {translate('behavior.points') || 'Points'}: {behavior.points || '0'}
                    </Text>
                  </View>
                </View>
              </TouchableOpacity>

              {index < displayBehavior.length - 1 && <Divider style={styles.behaviorDivider} />}
            </Animatable.View>
          ))}
        </Surface>
      </Animatable.View>
    );
  };

  const renderDashboardContent = () => (
    <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
      <View style={styles.header}>
        {renderWelcomeCard()}
      </View>

      <Searchbar
        placeholder={translate('studentDashboard.searchPlaceholder') || "Search..."}
        onChangeText={onSearch}
        value={searchQuery}
        style={styles.searchBar}
        icon={isRTL ? "magnify" : "magnify"}
      />

      {/* Today's Schedule */}
      {renderTodaySchedule()}

      {/* Homework Section */}
      {renderHomeworkSection()}

      {/* Behavior Records */}
      {renderBehaviorSection()}

      {/* Class Status Widget */}
      {classInfo && (
        <Animated.View
          style={[
            styles.sectionCard,
            { transform: [{ scale: scaleAnim }] }
          ]}
        >
          <DashboardClassWidget
            role="student"
            userId={auth.currentUser.uid}
            classId={classInfo.id}
            section={classInfo.section}
            onPressViewSchedule={() => navigation.navigate('StudentClassSchedule')}
          />
        </Animated.View>
      )}

      {/* Today's Schedule Widget */}
      {classInfo && (
        <Animated.View
          style={[
            styles.sectionCard,
            { transform: [{ scale: scaleAnim }] }
          ]}
        >
          <Surface style={styles.sectionSurface}>
            <LinearGradient
              colors={['#2196F3', '#64B5F6']}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
              style={styles.sectionHeader}
            >
              <View style={styles.scheduleHeaderContent}>
                <Title style={styles.sectionTitle}>Today's Schedule</Title>
                <Button
                  mode="contained"
                  onPress={() => navigation.navigate('StudentClassSchedule')}
                  style={styles.viewScheduleButton}
                  labelStyle={styles.viewScheduleButtonLabel}
                >
                  Full Schedule
                </Button>
              </View>
            </LinearGradient>

            <View style={styles.scheduleContainer}>
              <ScrollView
                horizontal
                showsHorizontalScrollIndicator={false}
                style={styles.scheduleScrollView}
              >
                <View style={styles.scheduleContent}>
                  <TodayScheduleWidget
                    classId={classInfo.id}
                    section={classInfo.section}
                  />
                </View>
              </ScrollView>
            </View>
          </Surface>
        </Animated.View>
      )}

      {/* Exam Schedules Section */}
      {examSchedules.length > 0 && (
        <Animated.View
          style={[
            styles.sectionCard,
            { transform: [{ scale: scaleAnim }] }
          ]}
        >
          <Surface style={styles.sectionSurface}>
            <LinearGradient
              colors={['#673AB7', '#9575CD']}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
              style={styles.sectionHeader}
            >
              <Title style={styles.sectionTitle}>Upcoming Exams</Title>
            </LinearGradient>
            <View style={styles.examList}>
              {examSchedules.slice(0, 3).map((exam, index) => {
                const today = new Date().toISOString().split('T')[0];
                const isPast = exam.date < today;

                return (
                  <TouchableOpacity
                    key={exam.id}
                    onPress={() => navigation.navigate('StudentExamSchedule')}
                    style={styles.examItem}
                  >
                    <View style={styles.examContent}>
                      <View style={styles.examIconContainer}>
                        <IconButton
                          icon="calendar-clock"
                          size={24}
                          color={isPast ? '#4CAF50' : '#2196F3'}
                        />
                      </View>
                      <View style={styles.examDetails}>
                        <Text style={styles.examTitle}>{exam.title}</Text>
                        <Text style={styles.examSubtitle}>
                          {exam.date} at {exam.startTime} • {exam.subjectName || 'Unknown Subject'}
                        </Text>
                      </View>
                      <Chip
                        mode="flat"
                        style={{
                          backgroundColor: isPast ? '#E8F5E9' : '#E3F2FD',
                          height: 24
                        }}
                        textStyle={{ fontSize: 10 }}
                      >
                        {isPast ? "Completed" : "Upcoming"}
                      </Chip>
                    </View>
                    {index < examSchedules.length - 1 && <Divider />}
                  </TouchableOpacity>
                );
              })}
              {examSchedules.length > 3 && (
                <Button
                  mode="text"
                  onPress={() => navigation.navigate('StudentExamSchedule')}
                  style={styles.viewMoreButton}
                >
                  View All Exams
                </Button>
              )}
            </View>
          </Surface>
        </Animated.View>
      )}

      {menuItems.map((section, index) => (
        <Animated.View
          key={index}
          style={[
            styles.sectionCard,
            { transform: [{ scale: scaleAnim }] }
          ]}
        >
          <Surface style={styles.sectionSurface}>
            <LinearGradient
              colors={section.gradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
              style={styles.sectionHeader}
            >
              <Title style={styles.sectionTitle}>{section.category}</Title>
            </LinearGradient>
            <View style={styles.menuGrid}>
              {section.items.map((item) => renderMenuItem(item))}
            </View>
          </Surface>
        </Animated.View>
      ))}
    </ScrollView>
  );

  const renderAnalyticsContent = () => (
    <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
      <Surface style={styles.analyticsCard}>
        <Card.Content>
          <Title style={styles.chartTitle}>{translate('studentDashboard.academicPerformance') || 'Academic Performance'}</Title>
          <LineChart
            data={sanitizeChartDatasets({
              labels: [
                translate('studentDashboard.term1') || 'Term 1',
                translate('studentDashboard.term2') || 'Term 2',
                translate('studentDashboard.term3') || 'Term 3',
                translate('studentDashboard.term4') || 'Term 4'
              ],
              datasets: [{
                data: sanitizeChartData(studentData?.performance || [0, 0, 0, 0]),
                color: (opacity = 1) => `rgba(33, 150, 243, ${opacity})`,
                strokeWidth: 2
              }]
            })}
            width={screenWidth - 40}
            height={220}
            chartConfig={chartConfig}
            bezier
            style={styles.chart}
          />
        </Card.Content>
      </Surface>

      <Surface style={styles.analyticsCard}>
        <Card.Content>
          <Title style={styles.chartTitle}>Attendance Overview</Title>
          <PieChart
            data={[
              {
                name: translate('studentDashboard.present') || 'Present',
                value: studentData?.attendance?.present || 0,
                color: '#4CAF50',
                legendFontColor: '#7F7F7F',
                legendFontSize: 12
              },
              {
                name: translate('studentDashboard.absent') || 'Absent',
                value: studentData?.attendance?.absent || 0,
                color: '#F44336',
                legendFontColor: '#7F7F7F',
                legendFontSize: 12
              }
            ].map(item => ({
              ...item,
              value: isNaN(item.value) || item.value === undefined ||
                    item.value === null || item.value === Infinity ||
                    item.value === -Infinity ? 0 : item.value
            }))}
            width={screenWidth - 40}
            height={220}
            chartConfig={chartConfig}
            accessor="value"
            backgroundColor="transparent"
            paddingLeft="15"
            style={styles.chart}
          />
        </Card.Content>
      </Surface>
    </ScrollView>
  );

  const renderNotificationsContent = () => (
    <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
      <Surface style={styles.notificationsCard}>
        <Card.Content>
          <Title style={styles.notificationsTitle}>{translate('studentDashboard.recentNotifications') || 'Recent Notifications'}</Title>
          <List.Section>
            {studentData?.notifications?.map((notification, index) => (
              <List.Item
                key={index}
                title={notification.title}
                description={notification.description}
                left={props => (
                  <Avatar.Icon
                    {...props}
                    icon={notification.icon || 'bell'}
                    style={{ backgroundColor: notification.color || '#2196F3' }}
                  />
                )}
                style={styles.notificationItem}
              />
            ))}
          </List.Section>
        </Card.Content>
      </Surface>
    </ScrollView>
  );

  if (isLoading) {
    return (
      <View style={styles.container}>
        <StatusBar backgroundColor="#1976d2" barStyle="light-content" />
        <StudentAppHeader
          title={translate('studentDashboard.title') || "Student Dashboard"}
          showBackButton={false}
          onMenuPress={toggleDrawer}
        />
        <View style={[styles.container, styles.centerContent]}>
          <ActivityIndicator size="large" color="#1976d2" />
          <Text style={styles.loadingText}>
            {translate('common.loading') || "Loading..."}
          </Text>
        </View>
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar backgroundColor="#1976d2" barStyle="light-content" />

      <Portal>
        <Dialog
          visible={logoutDialogVisible}
          onDismiss={() => setLogoutDialogVisible(false)}
        >
          <Dialog.Title>{translate('auth.confirmLogout') || 'Confirm Logout'}</Dialog.Title>
          <Dialog.Content>
            <Paragraph>{translate('auth.logoutMessage') || 'Are you sure you want to logout?'}</Paragraph>
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={() => setLogoutDialogVisible(false)}>{translate('common.cancel') || 'Cancel'}</Button>
            <Button onPress={handleLogout}>{translate('auth.logout') || 'Logout'}</Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>

      <StudentAppHeader
        title={translate('studentDashboard.dashboard') || 'Dashboard'}
        subtitle={classInfo ? `${translate('common.class') || 'Class'} ${classInfo.name} - ${translate('common.section') || 'Section'} ${classInfo.section}` : ''}
        navigation={navigation}
        onMenuPress={toggleDrawer}
        unreadCount={unreadCount}
        onNotificationPress={() => navigation.navigate('Notifications')}
        onSearchPress={() => navigation.navigate('Search')}
      />

      <StudentSidebar
        visible={drawerVisible}
        onDismiss={() => setDrawerVisible(false)}
        onItemPress={(item) => {
          setDrawerVisible(false);
          setActiveSidebarItem(item.key);
          if (item.route) {
            navigation.navigate(item.route);
          }
        }}
        activeSidebarItem={activeSidebarItem}
        studentData={studentData}
        classInfo={classInfo}
      />

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={['#1976d2']}
            tintColor="#1976d2"
          />
        }
      >
        {renderWelcomeCard()}

        {/* Quick Access Menu */}
        <View style={styles.menuHeader}>
          <Title style={styles.menuTitle}>
            {translate('studentDashboard.quickAccess') || 'Quick Access'}
          </Title>
          <Button
            mode="text"
            icon={showAllItems ? "chevron-up" : "chevron-down"}
            onPress={() => setShowAllItems(!showAllItems)}
          >
            {showAllItems
              ? translate('studentDashboard.showLess') || 'Show Less'
              : translate('studentDashboard.showAll') || 'Show All'}
          </Button>
        </View>

        {/* Search bar for menu items */}
        <Searchbar
          placeholder={translate('studentDashboard.searchMenu') || 'Search menu items...'}
          onChangeText={setSearchQuery}
          value={searchQuery}
          style={styles.searchBar}
          iconColor="#757575"
        />

        {/* Filter menu items based on search query */}
        {searchQuery ? (
          // Show search results
          <Animatable.View
            key={`search-results-${language}`}
            animation="fadeIn"
            duration={500}
          >
            {filteredMenuItems.length > 0 ? (
              filteredMenuItems.map((category, index) => (
                <Card key={`${category.category}-${language}-${index}`} style={styles.menuCategoryCard}>
                  <Card.Content>
                    <Title style={styles.menuCategoryTitle}>{category.category}</Title>
                    <View style={styles.menuGrid}>
                      {category.items.map((item, itemIndex) => (
                        <TouchableOpacity
                          key={`${item.title}-${language}-${itemIndex}`}
                          style={styles.menuItem}
                          onPress={() => navigation.navigate(item.route)}
                        >
                          <Avatar.Icon
                            size={48}
                            icon={item.icon}
                            style={[styles.menuIcon, { backgroundColor: getRandomColor(itemIndex) }]}
                          />
                          <Text style={styles.menuItemText}>{item.title}</Text>
                        </TouchableOpacity>
                      ))}
                    </View>
                  </Card.Content>
                </Card>
              ))
            ) : (
              <View style={styles.emptySearchContainer}>
                <IconButton icon="magnify-close" size={40} color="#9e9e9e" />
                <Text style={styles.emptySearchText}>
                  {translate('studentDashboard.noSearchResults') || 'No menu items match your search'}
                </Text>
              </View>
            )}
          </Animatable.View>
        ) : (
          // Show regular menu categories
          <Animatable.View
            key={`menu-items-${language}`}
            animation="fadeIn"
            duration={500}
          >
            {menuItems
              .slice(0, showAllItems ? menuItems.length : 3)
              .map((category, categoryIndex) => (
                <Animatable.View
                  key={`${category.category}-${language}-${categoryIndex}`}
                  animation="fadeInUp"
                  duration={500}
                  delay={categoryIndex * 100}
                >
                  <Card style={styles.menuCategoryCard}>
                    <Card.Content>
                      <Title style={styles.menuCategoryTitle}>{category.category}</Title>
                      <View style={styles.menuGrid}>
                        {category.items.map((item, index) => (
                          <TouchableOpacity
                            key={`${item.title}-${language}-${index}`}
                            style={styles.menuItem}
                            onPress={() => navigation.navigate(item.route)}
                          >
                            <LinearGradient
                              colors={[`${item.color}20`, `${item.color}10`]}
                              style={styles.menuItemGradient}
                            >
                              <View style={[styles.menuIconContainer, { backgroundColor: `${item.color}30` }]}>
                                <IconButton
                                  icon={item.icon}
                                  color={item.color}
                                  size={24}
                                  style={styles.menuIconButton}
                                />
                              </View>
                              <Text style={styles.menuItemText}>{item.title}</Text>
                            </LinearGradient>
                          </TouchableOpacity>
                        ))}
                      </View>
                    </Card.Content>
                  </Card>
                </Animatable.View>
              ))}
          </Animatable.View>
        )}

        {/* Today's Schedule */}
        <Card style={styles.sectionCard}>
          <Card.Title
            title={translate('studentDashboard.todaySchedule') || "Today's Schedule"}
            left={(props) => <Avatar.Icon {...props} icon="calendar-today" style={styles.cardIcon} />}
            right={(props) => (
              <IconButton
                {...props}
                icon="arrow-right"
                onPress={() => navigation.navigate('StudentClassSchedule')}
              />
            )}
          />
          <Card.Content>
            <TodayScheduleWidget classId={classInfo?.id} section={classInfo?.section} />
          </Card.Content>
        </Card>

        {/* Upcoming Exams */}
        <Card style={styles.sectionCard}>
          <Card.Title
            title={translate('studentDashboard.upcomingExams') || 'Upcoming Exams'}
            left={(props) => <Avatar.Icon {...props} icon="calendar-text" style={styles.cardIcon} />}
            right={(props) => (
              <IconButton
                {...props}
                icon="arrow-right"
                onPress={() => navigation.navigate('StudentExamSchedule')}
              />
            )}
          />
          <Card.Content>
            {isLoading ? (
              <ActivityIndicator style={styles.loadingIndicator} />
            ) : examSchedules.length > 0 ? (
              <View>
                {examSchedules.slice(0, 3).map((exam) => (
                  <Surface key={exam.id} style={styles.examItem}>
                    <View style={styles.examDateContainer}>
                      <Text style={styles.examDate}>
                        {new Date(exam.date).toLocaleDateString()}
                      </Text>
                      <Text style={styles.examTime}>
                        {exam.startTime || '9:00 AM'}
                      </Text>
                    </View>
                    <View style={styles.examDetails}>
                      <Text style={styles.examTitle}>
                        {exam.title || exam.subjectName || 'Exam'}
                      </Text>
                      <Text style={styles.examSubject}>
                        {exam.subjectName || exam.subject || 'Subject'}
                      </Text>
                      {exam.location && (
                        <Text style={styles.examLocation}>
                          {translate('studentDashboard.location') || 'Location'}: {exam.location}
                        </Text>
                      )}
                    </View>
                  </Surface>
                ))}
                {examSchedules.length > 3 && (
                  <Button
                    mode="text"
                    onPress={() => navigation.navigate('StudentExamSchedule')}
                    style={styles.viewMoreButton}
                  >
                    {translate('common.viewMore') || 'View More'} ({examSchedules.length - 3})
                  </Button>
                )}
              </View>
            ) : (
              <View style={styles.emptyStateContainer}>
                <IconButton icon="calendar-blank" size={40} color="#9e9e9e" />
                <Text style={styles.emptyStateText}>
                  {translate('studentDashboard.noExams') || 'No upcoming exams scheduled'}
                </Text>
              </View>
            )}
          </Card.Content>
        </Card>

        {/* Homework */}
        <Card style={styles.sectionCard}>
          <Card.Title
            title={translate('studentDashboard.homework') || 'Homework'}
            left={(props) => <Avatar.Icon {...props} icon="book-check" style={styles.cardIcon} />}
            right={(props) => (
              <IconButton
                {...props}
                icon="arrow-right"
                onPress={() => navigation.navigate('HomeworkView')}
              />
            )}
          />
          <Card.Content>
            {isHomeworkLoading ? (
              <ActivityIndicator style={styles.loadingIndicator} />
            ) : homeworkData.length > 0 ? (
              <View>
                {homeworkData.slice(0, 3).map((homework) => (
                  <Surface key={homework.id} style={styles.homeworkItem}>
                    <View style={styles.homeworkStatus}>
                      <Badge
                        style={[
                          styles.homeworkBadge,
                          homework.status === 'submitted' && styles.submittedBadge,
                          homework.status === 'graded' && styles.gradedBadge,
                          homework.status === 'pending' && styles.pendingBadge,
                        ]}
                      >
                        {homework.status === 'submitted'
                          ? translate('studentDashboard.submitted') || 'Submitted'
                          : homework.status === 'graded'
                          ? translate('studentDashboard.graded') || 'Graded'
                          : translate('studentDashboard.pending') || 'Pending'}
                      </Badge>
                    </View>
                    <View style={styles.homeworkDetails}>
                      <Text style={styles.homeworkTitle}>{homework.title}</Text>
                      <Text style={styles.homeworkSubject}>
                        {homework.subjectName || homework.subject || 'Subject'}
                      </Text>
                      <Text style={styles.homeworkDueDate}>
                        {translate('studentDashboard.due') || 'Due'}: {new Date(homework.dueDate).toLocaleDateString()}
                      </Text>
                    </View>
                    <IconButton
                      icon="chevron-right"
                      size={24}
                      onPress={() => navigation.navigate('HomeworkDetails', { homeworkId: homework.id })}
                    />
                  </Surface>
                ))}
                {homeworkData.length > 3 && (
                  <Button
                    mode="text"
                    onPress={() => navigation.navigate('HomeworkView')}
                    style={styles.viewMoreButton}
                  >
                    {translate('common.viewMore') || 'View More'} ({homeworkData.length - 3})
                  </Button>
                )}
              </View>
            ) : (
              <View style={styles.emptyStateContainer}>
                <IconButton icon="book-open-page-variant" size={40} color="#9e9e9e" />
                <Text style={styles.emptyStateText}>
                  {translate('studentDashboard.noHomework') || 'No pending homework assignments'}
                </Text>
              </View>
            )}
          </Card.Content>
        </Card>
      </ScrollView>

      <MessagingFAB navigation={navigation} />

      <BottomNavigation
        style={styles.bottomNav}
        barStyle={styles.bottomNavBar}
        navigationState={{
          index: selectedIndex,
          routes: [
            { key: 'dashboard', title: translate('studentDashboard.dashboard') || 'Dashboard', icon: 'view-dashboard' },
            { key: 'analytics', title: translate('studentDashboard.analytics') || 'Analytics', icon: 'chart-bar' },
          ]
        }}
        onIndexChange={setSelectedIndex}
        renderScene={() => null}
      />

      <Portal>
        <Dialog
          visible={logoutDialogVisible}
          onDismiss={() => setLogoutDialogVisible(false)}
        >
          <Dialog.Title>{translate('common.confirmLogout') || 'Confirm Logout'}</Dialog.Title>
          <Dialog.Content>
            <Paragraph>{translate('common.logoutConfirmMessage') || 'Are you sure you want to logout?'}</Paragraph>
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={() => setLogoutDialogVisible(false)}>{translate('common.cancel') || 'Cancel'}</Button>
            <Button onPress={handleLogout}>{translate('common.logout') || 'Logout'}</Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  contentContainer: {
    flex: 1,
    marginBottom: 70, // Increased margin to account for safe area
  },
  content: {
    flex: 1,
    padding: 16,
    paddingBottom: 16,
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  scrollView: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 80, // Extra padding for FAB
  },
  menuHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
    marginTop: 8,
  },
  menuTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  searchBar: {
    marginBottom: 16,
    borderRadius: 8,
    elevation: 2,
    backgroundColor: '#fff',
  },
  menuCategoryCard: {
    marginBottom: 16,
    borderRadius: 12,
    overflow: 'hidden',
    elevation: 3,
  },
  menuCategoryTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
    color: '#333',
  },
  menuGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  menuItem: {
    width: '48%',
    marginBottom: 12,
    borderRadius: 8,
    overflow: 'hidden',
  },
  menuItemGradient: {
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    height: 100,
  },
  menuIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  menuIconButton: {
    margin: 0,
  },
  menuItemText: {
    textAlign: 'center',
    fontWeight: '500',
    fontSize: 14,
  },
  menuIcon: {
    marginBottom: 8,
  },
  emptySearchContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 24,
  },
  emptySearchText: {
    fontSize: 16,
    color: '#757575',
    textAlign: 'center',
    marginTop: 8,
  },
  welcomeCard: {
    marginBottom: 16,
    borderRadius: 12,
    overflow: 'hidden',
    elevation: 4,
    marginTop: 8,
  },
  welcomeGradient: {
    padding: 20,
  },
  welcomeContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  welcomeText: {
    marginLeft: 16,
    flex: 1,
  },
  welcomeName: {
    color: '#ffffff',
    fontSize: 24,
    fontWeight: 'bold',
  },
  roleContainer: {
    flexDirection: 'row',
    marginTop: 4,
  },
  roleBadge: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    color: 'white',
  },
  statsContainer: {
    flexDirection: 'row',
    marginTop: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 8,
    padding: 8,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 4,
  },
  statValue: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  statLabel: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 12,
    marginTop: 2,
  },
  avatar: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderWidth: 3,
    borderColor: 'rgba(255, 255, 255, 0.5)',
  },
  searchBar: {
    marginBottom: 16,
    elevation: 4,
    borderRadius: 12,
  },
  sectionCard: {
    marginBottom: 16,
    borderRadius: 12,
    overflow: 'hidden',
    elevation: 3,
  },
  sectionSurface: {
    borderRadius: 12,
    overflow: 'hidden',
    elevation: 4,
  },
  cardIcon: {
    backgroundColor: '#1976d2',
  },
  sectionHeader: {
    padding: 16,
  },
  sectionTitle: {
    color: '#ffffff',
    fontSize: 20,
    fontWeight: 'bold',
  },
  menuGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    padding: 8,
    backgroundColor: '#ffffff',
  },
  menuItem: {
    width: '48%',
    marginBottom: 16,
  },
  menuTouchable: {
    flex: 1,
  },
  menuCard: {
    borderRadius: 12,
    overflow: 'hidden',
    elevation: 2,
    borderLeftWidth: 4,
  },
  menuGradient: {
    padding: 16,
    alignItems: 'center',
  },
  menuIcon: {
    margin: 0,
    padding: 0,
  },
  menuTitle: {
    fontSize: 14,
    textAlign: 'center',
    marginTop: 4,
  },
  analyticsCard: {
    marginBottom: 16,
    borderRadius: 12,
    elevation: 4,
    backgroundColor: '#ffffff',
  },
  chartTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  chart: {
    marginVertical: 8,
    borderRadius: 16,
  },
  notificationsCard: {
    borderRadius: 12,
    elevation: 4,
    backgroundColor: '#ffffff',
  },
  notificationsTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  notificationItem: {
    borderRadius: 8,
    marginBottom: 8,
  },
  examList: {
    padding: 8,
  },
  examItem: {
    marginVertical: 4,
  },
  examContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
  },
  examIconContainer: {
    marginRight: 8,
  },
  examDetails: {
    flex: 1,
    marginRight: 8,
  },
  examTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  examSubtitle: {
    fontSize: 12,
    color: '#666',
  },
  viewMoreButton: {
    alignSelf: 'center',
    marginTop: 8,
  },
  bottomNav: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: '#ffffff',
    elevation: 8,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.1)',
  },
  bottomNavBar: {
    height: 60,
    paddingBottom: 10,
  },
  fab: {
    position: 'absolute',
    right: 16,
    bottom: 76,
    backgroundColor: '#2196F3',
    elevation: 8,
  },
  header: {
    marginBottom: 16,
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  headerButtons: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
    paddingHorizontal: 8,
    position: 'absolute',
    top: 16,
    right: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 20,
    elevation: 4,
  },
  headerButton: {
    margin: 0,
  },
  // Schedule Widget Styles
  scheduleHeaderContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
  },
  viewScheduleButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 20,
    height: 36,
  },
  viewScheduleButtonLabel: {
    fontSize: 12,
    marginVertical: 0,
    marginHorizontal: 8,
  },
  scheduleContainer: {
    backgroundColor: '#ffffff',
    padding: 8,
  },
  scheduleScrollView: {
    paddingVertical: 8,
  },
  scheduleContent: {
    paddingHorizontal: 8,
    paddingBottom: 8,
  },
  todayScheduleContainer: {
    minWidth: screenWidth - 64,
    paddingVertical: 8,
  },
  todayText: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 12,
    color: '#333',
  },
  scheduleItemCard: {
    marginBottom: 12,
    borderRadius: 8,
    elevation: 2,
    backgroundColor: '#f5f5f5',
    borderLeftWidth: 4,
    borderLeftColor: '#2196F3',
  },
  currentClassCard: {
    backgroundColor: '#E3F2FD',
    borderLeftColor: '#1976D2',
  },
  pastClassCard: {
    backgroundColor: '#F5F5F5',
    borderLeftColor: '#9E9E9E',
    opacity: 0.8,
  },
  scheduleItemContent: {
    padding: 12,
  },
  scheduleTimeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  scheduleTimeText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
  },
  scheduleStatusChip: {
    height: 24,
    backgroundColor: '#E3F2FD',
  },
  currentClassChip: {
    backgroundColor: '#1976D2',
  },
  pastClassChip: {
    backgroundColor: '#9E9E9E',
  },
  scheduleStatusText: {
    fontSize: 10,
    color: '#fff',
  },
  scheduleDetailsContainer: {
    marginTop: 4,
  },
  scheduleSubjectText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  scheduleTeacherText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 2,
  },
  scheduleRoomText: {
    fontSize: 12,
    color: '#666',
  },
  scheduleLoadingContainer: {
    minHeight: 150,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  scheduleLoadingText: {
    marginTop: 8,
    color: '#666',
    fontSize: 14,
  },
  scheduleErrorContainer: {
    minHeight: 150,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#FFEBEE',
    borderRadius: 8,
  },
  scheduleErrorText: {
    color: '#D32F2F',
    fontSize: 14,
    textAlign: 'center',
  },
  scheduleEmptyContainer: {
    minHeight: 150,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  scheduleEmptyText: {
    color: '#9E9E9E',
    fontSize: 16,
    marginVertical: 8,
    textAlign: 'center',
  },
  scheduleEmptyButton: {
    marginTop: 8,
  },
  notificationBadge: {
    position: 'absolute',
    top: 4,
    right: 85,
    backgroundColor: '#f44336',
  },
  notificationButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  badge: {
    position: 'absolute',
    top: 0,
    right: 0,
    backgroundColor: '#f44336',
  },
  // New styles for schedule, homework, and behavior sections
  sectionCard: {
    marginVertical: 8,
    borderRadius: 12,
    overflow: 'hidden',
    elevation: 2,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#1976d2',
  },
  sectionTitle: {
    color: 'white',
    fontSize: 18,
  },
  viewAllButton: {
    color: 'white',
    fontSize: 14,
  },
  loadingContainer: {
    padding: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyContainer: {
    padding: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyText: {
    color: '#757575',
    marginTop: 8,
    textAlign: 'center',
  },
  emptyStateContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 24,
  },
  loadingIndicator: {
    padding: 16,
  },
  scheduleScrollView: {
    paddingVertical: 12,
    paddingHorizontal: 8,
  },
  scheduleCard: {
    width: 220,
    marginHorizontal: 8,
    borderRadius: 8,
    overflow: 'hidden',
    elevation: 2,
  },
  scheduleTimeContainer: {
    backgroundColor: '#1976d2',
    padding: 8,
    alignItems: 'center',
  },
  scheduleTime: {
    color: 'white',
    fontWeight: 'bold',
  },
  scheduleDetails: {
    padding: 12,
  },
  scheduleSubject: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  scheduleTeacherContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  scheduleTeacherIcon: {
    margin: 0,
    padding: 0,
  },
  scheduleTeacher: {
    fontSize: 14,
    color: '#757575',
  },
  scheduleRoomContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  scheduleRoomIcon: {
    margin: 0,
    padding: 0,
  },
  scheduleRoom: {
    fontSize: 14,
    color: '#757575',
  },
  homeworkItem: {
    padding: 16,
  },
  homeworkSubjectContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  homeworkIcon: {
    margin: 0,
    padding: 0,
  },
  homeworkSubject: {
    fontSize: 14,
    color: '#1976d2',
    fontWeight: 'bold',
  },
  homeworkTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  homeworkFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 8,
  },
  homeworkDueContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  homeworkDueIcon: {
    margin: 0,
    padding: 0,
  },
  homeworkDue: {
    fontSize: 14,
    color: '#757575',
  },
  homeworkStatus: {
    height: 24,
  },
  homeworkStatusText: {
    color: 'white',
    fontSize: 12,
  },
  homeworkDivider: {
    marginHorizontal: 16,
  },
  behaviorItem: {
    padding: 16,
  },
  behaviorHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  behaviorType: {
    height: 24,
  },
  behaviorTypeText: {
    color: 'white',
    fontSize: 12,
  },
  behaviorDate: {
    fontSize: 14,
    color: '#757575',
  },
  behaviorCategory: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  behaviorDescription: {
    fontSize: 14,
    color: '#424242',
    marginBottom: 8,
  },
  behaviorFooter: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  behaviorPointsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  behaviorPointsIcon: {
    margin: 0,
    padding: 0,
  },
  behaviorPoints: {
    fontSize: 14,
    color: '#757575',
  },
  behaviorDivider: {
    marginHorizontal: 16,
  },
});

export default StudentDashboard;
