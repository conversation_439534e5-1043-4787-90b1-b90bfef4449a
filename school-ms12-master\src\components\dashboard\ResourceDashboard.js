import React, { useState } from 'react';
import {
    Grid,
    Paper,
    Typography,
    Box,
    Card,
    CardContent,
    IconButton,
    Button,
    List,
    ListItem,
    ListItemText,
    ListItemIcon,
    Divider,
    LinearProgress,
    Chip,
    Menu,
    MenuItem,
    Tabs,
    Tab,
    Fade,
    CircularProgress,
    Tooltip as MuiTooltip,
    useTheme
} from '@mui/material';
import {
    Inventory as InventoryIcon,
    Book as BookIcon,
    Computer as ComputerIcon,
    Science as ScienceIcon,
    Sports as SportsIcon,
    Warning as WarningIcon,
    Timeline as TimelineIcon,
    Assessment as AssessmentIcon,
    MoreVert as MoreVertIcon,
    Download as DownloadIcon,
    Print as PrintIcon,
    Share as ShareIcon,
    FilterList as FilterListIcon,
    Refresh as RefreshIcon
} from '@mui/icons-material';
import {
    BarChart,
    Bar,
    XAxis,
    YAxis,
    CartesianGrid,
    Tooltip,
    Legend,
    ResponsiveContainer,
    PieChart,
    Pie,
    Cell,
    LineChart,
    Line,
    Area,
    AreaChart
} from 'recharts';
import { useTranslation } from '../../hooks/useTranslation';
import { styled } from '@mui/material/styles';
import { sanitizeChartData, sanitizeChartDatasets } from '../../utils/ChartUtils';

const StyledCard = styled(Card)(({ theme }) => ({
    height: '100%',
    display: 'flex',
    flexDirection: 'column',
    '& .MuiCardContent-root': {
        flexGrow: 1
    }
}));

const ResourceIndicator = styled(Box)(({ theme, status }) => {
    const colors = {
        good: theme.palette.success.main,
        warning: theme.palette.warning.main,
        critical: theme.palette.error.main
    };

    return {
        width: 12,
        height: 12,
        borderRadius: '50%',
        backgroundColor: colors[status],
        marginRight: theme.spacing(1)
    };
});

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042'];

const ResourceDashboard = ({
    resourceStats,
    utilizationData,
    maintenanceSchedule,
    inventoryAlerts,
    budgetData
}) => {
    const { t, language, formatNumber, formatDate, formatCurrency } = useTranslation();
    // No theme needed
    const [currentTab, setCurrentTab] = useState(0);
    const [menuAnchorEl, setMenuAnchorEl] = useState(null);
    const [filterAnchorEl, setFilterAnchorEl] = useState(null);
    const [selectedPeriod, setSelectedPeriod] = useState('week');
    const [isLoading, setIsLoading] = useState(false);
    const [selectedResource, setSelectedResource] = useState(null);

    const handleMenuOpen = (event) => {
        setMenuAnchorEl(event.currentTarget);
    };

    const handleMenuClose = () => {
        setMenuAnchorEl(null);
    };

    const handleFilterOpen = (event) => {
        setFilterAnchorEl(event.currentTarget);
    };

    const handleFilterClose = () => {
        setFilterAnchorEl(null);
    };

    const handleTabChange = (event, newValue) => {
        setIsLoading(true);
        setCurrentTab(newValue);
        // Simulate loading
        setTimeout(() => setIsLoading(false), 500);
    };

    const handlePeriodChange = (period) => {
        setSelectedPeriod(period);
        handleFilterClose();
    };

    const handleResourceSelect = (resource) => {
        setSelectedResource(resource);
    };

    const getResourceStatus = (utilization) => {
        if (utilization >= 90) return 'critical';
        if (utilization >= 70) return 'warning';
        return 'good';
    };

    const renderResourceOverview = () => (
        <Fade in={!isLoading} timeout={500}>
            <Grid container spacing={3} mb={3}>
                <Grid item xs={12} sm={6} md={3}>
                    <StyledCard 
                        onClick={() => handleResourceSelect('library')}
                        sx={{ cursor: 'pointer', transition: '0.3s',
                            '&:hover': { transform: 'translateY(-4px)', boxShadow: 4 }
                        }}
                    >
                        <CardContent>
                            <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                                <Box display="flex" alignItems="center">
                                    <BookIcon color="primary" />
                                    <Typography variant="h6" ml={1}>
                                        {t('library_resources')}
                                    </Typography>
                                </Box>
                                <MuiTooltip title={t('more_options')}>
                                    <IconButton size="small" onClick={(e) => {
                                        e.stopPropagation();
                                        handleMenuOpen(e);
                                    }}>
                                        <MoreVertIcon />
                                    </IconButton>
                                </MuiTooltip>
                            </Box>
                            <Typography variant="h4">
                                {formatNumber(resourceStats.library.total)}
                            </Typography>
                            <Box display="flex" alignItems="center" mt={2}>
                                <Typography variant="body2" color="textSecondary" mr={1}>
                                    {t('utilization')}:
                                </Typography>
                                <Box flexGrow={1}>
                                    <LinearProgress
                                        variant="determinate"
                                        value={resourceStats.library.utilization}
                                        sx={{
                                            height: 8,
                                            borderRadius: 4,
                                            backgroundColor: theme.palette.grey[200],
                                            '& .MuiLinearProgress-bar': {
                                                borderRadius: 4,
                                                backgroundColor: resourceStats.library.utilization > 90 
                                                    ? theme.palette.error.main 
                                                    : resourceStats.library.utilization > 70
                                                    ? theme.palette.warning.main
                                                    : theme.palette.success.main
                                            }
                                        }}
                                    />
                                </Box>
                                <Typography variant="body2" ml={1} color={
                                    resourceStats.library.utilization > 90 
                                        ? 'error.main'
                                        : resourceStats.library.utilization > 70
                                        ? 'warning.main'
                                        : 'success.main'
                                }>
                                    {formatNumber(resourceStats.library.utilization)}%
                                </Typography>
                            </Box>
                        </CardContent>
                    </StyledCard>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                    <StyledCard 
                        onClick={() => handleResourceSelect('it')}
                        sx={{ cursor: 'pointer', transition: '0.3s',
                            '&:hover': { transform: 'translateY(-4px)', boxShadow: 4 }
                        }}
                    >
                        <CardContent>
                            <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                                <Box display="flex" alignItems="center">
                                    <ComputerIcon color="primary" />
                                    <Typography variant="h6" ml={1}>
                                        {t('it_resources')}
                                    </Typography>
                                </Box>
                                <MuiTooltip title={t('more_options')}>
                                    <IconButton size="small" onClick={(e) => {
                                        e.stopPropagation();
                                        handleMenuOpen(e);
                                    }}>
                                        <MoreVertIcon />
                                    </IconButton>
                                </MuiTooltip>
                            </Box>
                            <Typography variant="h4">
                                {formatNumber(resourceStats.it.total)}
                            </Typography>
                            <Box display="flex" alignItems="center" mt={2}>
                                <Typography variant="body2" color="textSecondary" mr={1}>
                                    {t('utilization')}:
                                </Typography>
                                <Box flexGrow={1}>
                                    <LinearProgress
                                        variant="determinate"
                                        value={resourceStats.it.utilization}
                                        sx={{
                                            height: 8,
                                            borderRadius: 4,
                                            backgroundColor: theme.palette.grey[200],
                                            '& .MuiLinearProgress-bar': {
                                                borderRadius: 4,
                                                backgroundColor: resourceStats.it.utilization > 90 
                                                    ? theme.palette.error.main 
                                                    : resourceStats.it.utilization > 70
                                                    ? theme.palette.warning.main
                                                    : theme.palette.success.main
                                            }
                                        }}
                                    />
                                </Box>
                                <Typography variant="body2" ml={1} color={
                                    resourceStats.it.utilization > 90 
                                        ? 'error.main'
                                        : resourceStats.it.utilization > 70
                                        ? 'warning.main'
                                        : 'success.main'
                                }>
                                    {formatNumber(resourceStats.it.utilization)}%
                                </Typography>
                            </Box>
                        </CardContent>
                    </StyledCard>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                    <StyledCard 
                        onClick={() => handleResourceSelect('lab')}
                        sx={{ cursor: 'pointer', transition: '0.3s',
                            '&:hover': { transform: 'translateY(-4px)', boxShadow: 4 }
                        }}
                    >
                        <CardContent>
                            <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                                <Box display="flex" alignItems="center">
                                    <ScienceIcon color="primary" />
                                    <Typography variant="h6" ml={1}>
                                        {t('lab_resources')}
                                    </Typography>
                                </Box>
                                <MuiTooltip title={t('more_options')}>
                                    <IconButton size="small" onClick={(e) => {
                                        e.stopPropagation();
                                        handleMenuOpen(e);
                                    }}>
                                        <MoreVertIcon />
                                    </IconButton>
                                </MuiTooltip>
                            </Box>
                            <Typography variant="h4">
                                {formatNumber(resourceStats.lab.total)}
                            </Typography>
                            <Box display="flex" alignItems="center" mt={2}>
                                <Typography variant="body2" color="textSecondary" mr={1}>
                                    {t('utilization')}:
                                </Typography>
                                <Box flexGrow={1}>
                                    <LinearProgress
                                        variant="determinate"
                                        value={resourceStats.lab.utilization}
                                        sx={{
                                            height: 8,
                                            borderRadius: 4,
                                            backgroundColor: theme.palette.grey[200],
                                            '& .MuiLinearProgress-bar': {
                                                borderRadius: 4,
                                                backgroundColor: resourceStats.lab.utilization > 90 
                                                    ? theme.palette.error.main 
                                                    : resourceStats.lab.utilization > 70
                                                    ? theme.palette.warning.main
                                                    : theme.palette.success.main
                                            }
                                        }}
                                    />
                                </Box>
                                <Typography variant="body2" ml={1} color={
                                    resourceStats.lab.utilization > 90 
                                        ? 'error.main'
                                        : resourceStats.lab.utilization > 70
                                        ? 'warning.main'
                                        : 'success.main'
                                }>
                                    {formatNumber(resourceStats.lab.utilization)}%
                                </Typography>
                            </Box>
                        </CardContent>
                    </StyledCard>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                    <StyledCard 
                        onClick={() => handleResourceSelect('sports')}
                        sx={{ cursor: 'pointer', transition: '0.3s',
                            '&:hover': { transform: 'translateY(-4px)', boxShadow: 4 }
                        }}
                    >
                        <CardContent>
                            <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                                <Box display="flex" alignItems="center">
                                    <SportsIcon color="primary" />
                                    <Typography variant="h6" ml={1}>
                                        {t('sports_resources')}
                                    </Typography>
                                </Box>
                                <MuiTooltip title={t('more_options')}>
                                    <IconButton size="small" onClick={(e) => {
                                        e.stopPropagation();
                                        handleMenuOpen(e);
                                    }}>
                                        <MoreVertIcon />
                                    </IconButton>
                                </MuiTooltip>
                            </Box>
                            <Typography variant="h4">
                                {formatNumber(resourceStats.sports.total)}
                            </Typography>
                            <Box display="flex" alignItems="center" mt={2}>
                                <Typography variant="body2" color="textSecondary" mr={1}>
                                    {t('utilization')}:
                                </Typography>
                                <Box flexGrow={1}>
                                    <LinearProgress
                                        variant="determinate"
                                        value={resourceStats.sports.utilization}
                                        sx={{
                                            height: 8,
                                            borderRadius: 4,
                                            backgroundColor: theme.palette.grey[200],
                                            '& .MuiLinearProgress-bar': {
                                                borderRadius: 4,
                                                backgroundColor: resourceStats.sports.utilization > 90 
                                                    ? theme.palette.error.main 
                                                    : resourceStats.sports.utilization > 70
                                                    ? theme.palette.warning.main
                                                    : theme.palette.success.main
                                            }
                                        }}
                                    />
                                </Box>
                                <Typography variant="body2" ml={1} color={
                                    resourceStats.sports.utilization > 90 
                                        ? 'error.main'
                                        : resourceStats.sports.utilization > 70
                                        ? 'warning.main'
                                        : 'success.main'
                                }>
                                    {formatNumber(resourceStats.sports.utilization)}%
                                </Typography>
                            </Box>
                        </CardContent>
                    </StyledCard>
                </Grid>
            </Grid>
        </Fade>
    );

    const renderUtilizationTrend = () => (
        <StyledCard>
            <CardContent>
                <Typography variant="h6" gutterBottom>
                    {t('resource_utilization_trend')}
                </Typography>
                <Box height={300}>
                    <ResponsiveContainer width="100%" height="100%">
                        <LineChart data={sanitizeChartDatasets(utilizationData)}>
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="month" />
                            <YAxis />
                            <Tooltip />
                            <Legend />
                            <Line
                                type="monotone"
                                dataKey="library"
                                name={t('library')}
                                stroke="#8884d8"
                            />
                            <Line
                                type="monotone"
                                dataKey="it"
                                name={t('it')}
                                stroke="#82ca9d"
                            />
                            <Line
                                type="monotone"
                                dataKey="lab"
                                name={t('lab')}
                                stroke="#ffc658"
                            />
                        </LineChart>
                    </ResponsiveContainer>
                </Box>
            </CardContent>
        </StyledCard>
    );

    const renderBudgetOverview = () => (
        <StyledCard>
            <CardContent>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                    <Typography variant="h6">
                        {t('resource_budget_overview')}
                    </Typography>
                    <IconButton>
                        <AssessmentIcon />
                    </IconButton>
                </Box>
                <Box height={200}>
                    <ResponsiveContainer width="100%" height="100%">
                        <PieChart>
                            <Pie
                                data={budgetData}
                                dataKey="amount"
                                nameKey="category"
                                cx="50%"
                                cy="50%"
                                label={({ name, percent }) => 
                                    `${name} ${(percent * 100).toFixed(0)}%`
                                }
                            >
                                {budgetData.map((entry, index) => (
                                    <Cell
                                        key={index}
                                        fill={COLORS[index % COLORS.length]}
                                    />
                                ))}
                            </Pie>
                            <Tooltip
                                formatter={(value) => formatCurrency(value)}
                            />
                        </PieChart>
                    </ResponsiveContainer>
                </Box>
                <Box mt={2}>
                    <Typography variant="body2" color="textSecondary" align="center">
                        {t('total_budget')}: {formatCurrency(
                            budgetData.reduce((sum, item) => sum + item.amount, 0)
                        )}
                    </Typography>
                </Box>
            </CardContent>
        </StyledCard>
    );

    const renderMaintenanceSchedule = () => (
        <StyledCard>
            <CardContent>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                    <Typography variant="h6">
                        {t('maintenance_schedule')}
                    </Typography>
                    <IconButton>
                        <TimelineIcon />
                    </IconButton>
                </Box>
                <List>
                    {maintenanceSchedule.map((item, index) => (
                        <React.Fragment key={index}>
                            <ListItem>
                                <ListItemIcon>
                                    {item.type === 'it' && <ComputerIcon />}
                                    {item.type === 'lab' && <ScienceIcon />}
                                    {item.type === 'sports' && <SportsIcon />}
                                </ListItemIcon>
                                <ListItemText
                                    primary={item.title[language]}
                                    secondary={formatDate(item.date)}
                                />
                                <Chip
                                    label={t(item.status)}
                                    color={
                                        item.status === 'pending' ? 'warning' :
                                        item.status === 'completed' ? 'success' :
                                        'error'
                                    }
                                    size="small"
                                />
                            </ListItem>
                            {index < maintenanceSchedule.length - 1 && <Divider />}
                        </React.Fragment>
                    ))}
                </List>
            </CardContent>
        </StyledCard>
    );

    const renderInventoryAlerts = () => (
        <StyledCard>
            <CardContent>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                    <Typography variant="h6">
                        {t('inventory_alerts')}
                    </Typography>
                    <IconButton>
                        <WarningIcon />
                    </IconButton>
                </Box>
                <List>
                    {inventoryAlerts.map((alert, index) => (
                        <React.Fragment key={index}>
                            <ListItem>
                                <ListItemIcon>
                                    <WarningIcon color={alert.severity} />
                                </ListItemIcon>
                                <ListItemText
                                    primary={alert.message[language]}
                                    secondary={
                                        <Box display="flex" alignItems="center">
                                            <ResourceIndicator status={alert.status} />
                                            <Typography variant="body2">
                                                {formatNumber(alert.quantity)} {t('items_remaining')}
                                            </Typography>
                                        </Box>
                                    }
                                />
                            </ListItem>
                            {index < inventoryAlerts.length - 1 && <Divider />}
                        </React.Fragment>
                    ))}
                </List>
            </CardContent>
        </StyledCard>
    );

    const renderChartSection = () => (
        <Paper sx={{ p: 3, mb: 3 }}>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                <Tabs value={currentTab} onChange={handleTabChange}>
                    <Tab label={t('utilization')} />
                    <Tab label={t('budget')} />
                    <Tab label={t('maintenance')} />
                </Tabs>
                <Box>
                    <MuiTooltip title={t('refresh')}>
                        <IconButton onClick={() => {
                            setIsLoading(true);
                            setTimeout(() => setIsLoading(false), 500);
                        }}>
                            <RefreshIcon />
                        </IconButton>
                    </MuiTooltip>
                    <MuiTooltip title={t('filter')}>
                        <IconButton onClick={handleFilterOpen}>
                            <FilterListIcon />
                        </IconButton>
                    </MuiTooltip>
                    <MuiTooltip title={t('download')}>
                        <IconButton>
                            <DownloadIcon />
                        </IconButton>
                    </MuiTooltip>
                </Box>
            </Box>

            {isLoading ? (
                <Box display="flex" justifyContent="center" alignItems="center" height={400}>
                    <CircularProgress />
                </Box>
            ) : (
                <Box height={400}>
                    {currentTab === 0 && renderUtilizationTrend()}
                    {currentTab === 1 && renderBudgetOverview()}
                    {currentTab === 2 && renderMaintenanceSchedule()}
                </Box>
            )}
        </Paper>
    );

    return (
        <Box p={3}>
            {renderResourceOverview()}
            {renderChartSection()}
            <Grid container spacing={3}>
                <Grid item xs={12} md={8}>
                    {renderInventoryAlerts()}
                </Grid>
                <Grid item xs={12} md={4}>
                    {renderBudgetOverview()}
                </Grid>
            </Grid>

            <Menu
                anchorEl={menuAnchorEl}
                open={Boolean(menuAnchorEl)}
                onClose={handleMenuClose}
            >
                <MenuItem onClick={handleMenuClose}>
                    <ListItemIcon>
                        <DownloadIcon fontSize="small" />
                    </ListItemIcon>
                    {t('download_report')}
                </MenuItem>
                <MenuItem onClick={handleMenuClose}>
                    <ListItemIcon>
                        <PrintIcon fontSize="small" />
                    </ListItemIcon>
                    {t('print_report')}
                </MenuItem>
                <MenuItem onClick={handleMenuClose}>
                    <ListItemIcon>
                        <ShareIcon fontSize="small" />
                    </ListItemIcon>
                    {t('share_report')}
                </MenuItem>
            </Menu>

            <Menu
                anchorEl={filterAnchorEl}
                open={Boolean(filterAnchorEl)}
                onClose={handleFilterClose}
            >
                <MenuItem 
                    onClick={() => handlePeriodChange('day')}
                    selected={selectedPeriod === 'day'}
                >
                    {t('daily')}
                </MenuItem>
                <MenuItem 
                    onClick={() => handlePeriodChange('week')}
                    selected={selectedPeriod === 'week'}
                >
                    {t('weekly')}
                </MenuItem>
                <MenuItem 
                    onClick={() => handlePeriodChange('month')}
                    selected={selectedPeriod === 'month'}
                >
                    {t('monthly')}
                </MenuItem>
                <MenuItem 
                    onClick={() => handlePeriodChange('year')}
                    selected={selectedPeriod === 'year'}
                >
                    {t('yearly')}
                </MenuItem>
            </Menu>
        </Box>
    );
};

export default ResourceDashboard;
