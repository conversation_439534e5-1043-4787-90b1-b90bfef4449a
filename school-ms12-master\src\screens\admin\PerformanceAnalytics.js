import React, { useState, useEffect, useCallback } from 'react';
import { View, ScrollView, StyleSheet, Dimensions, TouchableOpacity, RefreshControl, Platform } from 'react-native';
import { Card, Title, Text, DataTable, Portal, Modal, ActivityIndicator, Chip, Button, IconButton, Divider, Menu, Snackbar, useTheme, Surface, Avatar, FAB } from 'react-native-paper';
import { db } from '../../config/firebase';
import { collection, query, where, getDocs, doc, getDoc, orderBy, limit } from 'firebase/firestore';
import CustomButton from '../../components/common/CustomButton';
import { Picker } from '@react-native-picker/picker';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>hart } from 'react-native-chart-kit';
import EthiopianCalendar from '../../utils/EthiopianCalendar';
import { useLanguage } from '../../context/LanguageContext';

import * as Animatable from 'react-native-animatable';
import { LinearGradient } from 'expo-linear-gradient';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import AdminAppHeader from '../../components/common/AdminAppHeader';
import AdminSidebar from '../../components/common/AdminSidebar';
import { sanitizeChartData, sanitizeChartDatasets } from '../../utils/ChartUtils';

const PerformanceAnalytics = () => {
  // No theme needed
  const { language, translate } = useLanguage();
  const [classes, setClasses] = useState([]);
  const [selectedClass, setSelectedClass] = useState('');
  const [subjects, setSubjects] = useState([]);
  const [selectedSubject, setSelectedSubject] = useState('');
  const [timeRange, setTimeRange] = useState('semester'); // semester, year, quarter, month
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState(null);
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [exportMenuVisible, setExportMenuVisible] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [selectedData, setSelectedData] = useState(null);
  const [analytics, setAnalytics] = useState({
    gradeDistribution: {},
    averageScores: {},
    attendanceRate: {},
    performanceTrends: [],
    topPerformers: [],
    improvementAreas: [],
  });

  // Use the translate function from LanguageContext

  useEffect(() => {
    fetchClasses();
  }, []);

  useEffect(() => {
    if (selectedClass) {
      fetchSubjects();
      fetchAnalytics();
    }
  }, [selectedClass, selectedSubject, timeRange]);

  const fetchClasses = async () => {
    try {
      const classesRef = collection(db, 'classes');
      const querySnapshot = await getDocs(classesRef);

      const classData = [];
      querySnapshot.forEach((doc) => {
        classData.push({ id: doc.id, ...doc.data() });
      });

      setClasses(classData.sort((a, b) => a.name.localeCompare(b.name)));
      setLoading(false);
    } catch (error) {
      console.error('Error fetching classes:', error);
      setLoading(false);
    }
  };

  const fetchSubjects = async () => {
    try {
      const subjectsRef = collection(db, 'subjects');
      const q = query(subjectsRef, where('classId', '==', selectedClass));
      const querySnapshot = await getDocs(q);

      const subjectData = [];
      querySnapshot.forEach((doc) => {
        subjectData.push({ id: doc.id, ...doc.data() });
      });

      setSubjects(subjectData.sort((a, b) => a.name.localeCompare(b.name)));
    } catch (error) {
      console.error('Error fetching subjects:', error);
    }
  };

  const fetchAnalytics = async () => {
    try {
      setLoading(true);

      // Fetch grades
      const gradesRef = collection(db, 'grades');
      let q = query(gradesRef, where('classId', '==', selectedClass));
      if (selectedSubject) {
        q = query(q, where('subjectId', '==', selectedSubject));
      }
      const gradesSnapshot = await getDocs(q);

      // Fetch attendance
      const attendanceRef = collection(db, 'attendance');
      q = query(attendanceRef, where('classId', '==', selectedClass));
      const attendanceSnapshot = await getDocs(q);

      // Process grades data
      const grades = [];
      const gradeDistribution = { A: 0, B: 0, C: 0, D: 0, F: 0 };
      const averageScores = {};
      const studentScores = {};

      gradesSnapshot.forEach((doc) => {
        const grade = doc.data();
        grades.push(grade);

        // Grade distribution
        const score = parseInt(grade.score);
        if (score >= 90) gradeDistribution.A++;
        else if (score >= 80) gradeDistribution.B++;
        else if (score >= 70) gradeDistribution.C++;
        else if (score >= 60) gradeDistribution.D++;
        else gradeDistribution.F++;

        // Average scores by subject
        if (!averageScores[grade.subjectId]) {
          averageScores[grade.subjectId] = { total: 0, count: 0 };
        }
        averageScores[grade.subjectId].total += score;
        averageScores[grade.subjectId].count++;

        // Student scores
        if (!studentScores[grade.studentId]) {
          studentScores[grade.studentId] = { total: 0, count: 0 };
        }
        studentScores[grade.studentId].total += score;
        studentScores[grade.studentId].count++;
      });

      // Process attendance data
      const attendanceRate = {};
      attendanceSnapshot.forEach((doc) => {
        const attendance = doc.data();
        if (!attendanceRate[attendance.studentId]) {
          attendanceRate[attendance.studentId] = { present: 0, total: 0 };
        }
        attendanceRate[attendance.studentId].total++;
        if (attendance.status === 'present') {
          attendanceRate[attendance.studentId].present++;
        }
      });

      // Calculate performance trends
      const performanceTrends = Object.entries(averageScores).map(([subjectId, data]) => ({
        subjectId,
        average: data.total / data.count,
      }));

      // Calculate top performers
      const topPerformers = await Promise.all(
        Object.entries(studentScores)
          .map(async ([studentId, data]) => {
            const average = data.total / data.count;
            const studentDoc = await getDoc(doc(db, 'users', studentId));
            const studentData = studentDoc.data();
            return {
              studentId,
              name: studentData.displayName,
              average,
              attendance: attendanceRate[studentId]
                ? (attendanceRate[studentId].present / attendanceRate[studentId].total) * 100
                : 0,
            };
          })
      );

      topPerformers.sort((a, b) => b.average - a.average);

      // Calculate improvement areas
      const improvementAreas = Object.entries(averageScores)
        .map(([subjectId, data]) => ({
          subjectId,
          average: data.total / data.count,
        }))
        .sort((a, b) => a.average - b.average)
        .slice(0, 3);

      setAnalytics({
        gradeDistribution,
        averageScores,
        attendanceRate,
        performanceTrends,
        topPerformers: topPerformers.slice(0, 5),
        improvementAreas,
      });

      setLoading(false);
    } catch (error) {
      console.error('Error fetching analytics:', error);
      setLoading(false);
    }
  };

  const getGradeDistributionData = () => {
    const colors = ['#4CAF50', '#8BC34A', '#FFC107', '#FF9800', '#f44336'];
    return {
      labels: ['A', 'B', 'C', 'D', 'F'],
      datasets: [{
        data: sanitizeChartData([
          analytics.gradeDistribution.A || 0,
          analytics.gradeDistribution.B || 0,
          analytics.gradeDistribution.C || 0,
          analytics.gradeDistribution.D || 0,
          analytics.gradeDistribution.F || 0,
        ]),
      }],
      colors,
    };
  };

  const getPerformanceTrendData = () => {
    return {
      labels: analytics.performanceTrends.map(item => item.period),
      datasets: [{
        data: sanitizeChartData(analytics.performanceTrends.map(item => item.average)),
        color: (opacity = 1) => `rgba(33, 150, 243, ${opacity})`,
        strokeWidth: 2,
      }],
    };
  };

  const getAttendanceData = () => {
    // Calculate attendance percentage
    let totalPresent = 0;
    let totalAbsent = 0;

    // Handle different possible structures of attendanceRate
    if (analytics.attendanceRate) {
      if (typeof analytics.attendanceRate === 'object') {
        // If it's a direct object with present/absent properties
        if ('present' in analytics.attendanceRate && 'absent' in analytics.attendanceRate) {
          totalPresent = analytics.attendanceRate.present || 0;
          totalAbsent = analytics.attendanceRate.absent || 0;
        } else {
          // If it's a map of student IDs to attendance records
          Object.values(analytics.attendanceRate).forEach(record => {
            if (record && typeof record === 'object') {
              totalPresent += (record.present || 0);
              totalAbsent += ((record.total || 0) - (record.present || 0));
            }
          });
        }
      }
    }

    const total = totalPresent + totalAbsent;
    // Ensure we have a valid percentage (0-100)
    const presentPercentage = total > 0 ? Math.min(100, Math.max(0, (totalPresent / total) * 100)) : 0;
    const absentPercentage = Math.min(100, Math.max(0, 100 - presentPercentage));

    // Ensure we always have valid data for the chart
    if (presentPercentage === 0 && absentPercentage === 0) {
      return [
        {
          name: translate('admin.dashboard.present'),
          population: 1, // Use 1 instead of 0 to show something in the chart
          color: '#4CAF50',
          legendFontColor: '#7F7F7F',
        },
        {
          name: translate('admin.dashboard.absent'),
          population: 0,
          color: '#f44336',
          legendFontColor: '#7F7F7F',
        },
      ];
    }

    return [
      {
        name: translate('admin.dashboard.present'),
        population: presentPercentage,
        color: '#4CAF50',
        legendFontColor: '#7F7F7F',
      },
      {
        name: translate('admin.dashboard.absent'),
        population: absentPercentage,
        color: '#f44336',
        legendFontColor: '#7F7F7F',
      },
    ];
  };

  return (
    <View style={styles.container}>
      <AdminAppHeader
        title={translate('admin.navigation.performanceAnalytics')}
        showBack={true}
        showNotification={true}
      />
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={() => {
              setRefreshing(true);
              fetchClasses().then(() => {
                if (selectedClass) {
                  Promise.all([fetchSubjects(), fetchAnalytics()]).then(() => {
                    setRefreshing(false);
                  });
                } else {
                  setRefreshing(false);
                }
              });
            }}
          />
        }
      >
        <Animatable.View animation="fadeIn" duration={500}>
          <Surface style={styles.headerSurface}>
            <LinearGradient
              colors={['#2196f3', '#03a9f4']}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
              style={styles.headerGradient}
            >
              <View style={styles.headerContent}>
                <MaterialCommunityIcons name="chart-line" size={32} color="white" />
                <Title style={styles.headerTitle}>{translate('admin.navigation.performanceAnalytics')}</Title>
              </View>
            </LinearGradient>
          </Surface>

          <Card style={styles.card}>
            <Card.Content>
              <View style={styles.filterContainer}>
                <View style={styles.filterRow}>
                  <MaterialCommunityIcons name="filter-variant" size={20} color={'#1976d2'} style={styles.filterIcon} />
                  <Title style={styles.filterTitle}>{translate('common.filter')}</Title>
                </View>

                <Animatable.View animation="fadeInUp" duration={500} delay={100}>
                  <View style={styles.pickerContainer}>
                    <Text style={styles.pickerLabel}>{translate('admin.classManagement.classList')}</Text>
                    <View style={styles.pickerWrapper}>
                      <Picker
                        selectedValue={selectedClass}
                        onValueChange={(value) => {
                          setSelectedClass(value);
                          setSelectedSubject('');
                        }}
                        style={styles.picker}
                      >
                        <Picker.Item label={translate('common.select') + "..."} value="" />
                        {classes.map((cls) => (
                          <Picker.Item
                            key={cls.id}
                            label={cls.name}
                            value={cls.id}
                          />
                        ))}
                      </Picker>
                    </View>
                  </View>
                </Animatable.View>

                <Animatable.View animation="fadeInUp" duration={500} delay={200}>
                  <View style={styles.pickerContainer}>
                    <Text style={styles.pickerLabel}>{translate('admin.subjectManagement.subjectList')}</Text>
                    <View style={styles.pickerWrapper}>
                      <Picker
                        selectedValue={selectedSubject}
                        onValueChange={setSelectedSubject}
                        style={styles.picker}
                        enabled={!!selectedClass}
                      >
                        <Picker.Item label={translate('admin.subjectManagement.subjectList')} value="" />
                        {subjects.map((subject) => (
                          <Picker.Item
                            key={subject.id}
                            label={subject.name}
                            value={subject.id}
                          />
                        ))}
                      </Picker>
                    </View>
                  </View>
                </Animatable.View>

                <Animatable.View animation="fadeInUp" duration={500} delay={300}>
                  <View style={styles.pickerContainer}>
                    <Text style={styles.pickerLabel}>{translate('admin.examManagement.period')}</Text>
                    <View style={styles.pickerWrapper}>
                      <Picker
                        selectedValue={timeRange}
                        onValueChange={setTimeRange}
                        style={styles.picker}
                      >
                        <Picker.Item label={translate('admin.navigation.semester')} value="semester" />
                        <Picker.Item label={translate('admin.settings.academicYear')} value="year" />
                      </Picker>
                    </View>
                  </View>
                </Animatable.View>
              </View>
            </Card.Content>
          </Card>

          {loading ? (
            <ActivityIndicator style={styles.loader} />
          ) : selectedClass ? (
            <View>
              <Animatable.View animation="fadeInUp" duration={600} delay={100}>
                <Card style={styles.analyticsCard}>
                  <LinearGradient
                    colors={['#f5f5f5', '#ffffff']}
                    style={styles.cardGradient}
                  >
                    <Card.Content>
                      <View style={styles.cardHeader}>
                        <MaterialCommunityIcons name="chart-bar" size={24} color={'#1976d2'} />
                        <Title style={styles.cardTitle}>{translate('admin.dashboard.gradeDistribution')}</Title>
                      </View>
                      <Divider style={styles.divider} />
                      <BarChart
                        data={sanitizeChartDatasets(getGradeDistributionData())}
                        width={Dimensions.get('window').width - 80}
                        height={220}
                        chartConfig={{
                          backgroundColor: '#ffffff',
                          backgroundGradientFrom: '#ffffff',
                          backgroundGradientTo: '#ffffff',
                          decimalPlaces: 0,
                          color: (opacity = 1) => `rgba(33, 150, 243, ${opacity})`,
                          labelColor: (opacity = 1) => `rgba(0, 0, 0, ${opacity})`,
                          style: {
                            borderRadius: 16,
                          },
                          propsForLabels: {
                            fontWeight: 'bold',
                          },
                        }}
                        style={styles.chart}
                        showValuesOnTopOfBars={true}
                      />
                    </Card.Content>
                  </LinearGradient>
                </Card>
              </Animatable.View>

              <Animatable.View animation="fadeInUp" duration={600} delay={200}>
                <Card style={styles.analyticsCard}>
                  <LinearGradient
                    colors={['#f5f5f5', '#ffffff']}
                    style={styles.cardGradient}
                  >
                    <Card.Content>
                      <View style={styles.cardHeader}>
                        <MaterialCommunityIcons name="chart-line" size={24} color={'#1976d2'} />
                        <Title style={styles.cardTitle}>{translate('admin.dashboard.performanceTrends')}</Title>
                      </View>
                      <Divider style={styles.divider} />
                      <LineChart
                        data={sanitizeChartDatasets(getPerformanceTrendData())}
                        width={Dimensions.get('window').width - 80}
                        height={220}
                        chartConfig={{
                          backgroundColor: '#ffffff',
                          backgroundGradientFrom: '#ffffff',
                          backgroundGradientTo: '#ffffff',
                          decimalPlaces: 0,
                          color: (opacity = 1) => `rgba(33, 150, 243, ${opacity})`,
                          labelColor: (opacity = 1) => `rgba(0, 0, 0, ${opacity})`,
                          style: {
                            borderRadius: 16,
                          },
                          propsForLabels: {
                            fontWeight: 'bold',
                          },
                        }}
                        bezier
                        style={styles.chart}
                      />
                    </Card.Content>
                  </LinearGradient>
                </Card>
              </Animatable.View>

              <Animatable.View animation="fadeInUp" duration={600} delay={300}>
                <Card style={styles.analyticsCard}>
                  <LinearGradient
                    colors={['#f5f5f5', '#ffffff']}
                    style={styles.cardGradient}
                  >
                    <Card.Content>
                      <View style={styles.cardHeader}>
                        <MaterialCommunityIcons name="chart-pie" size={24} color={'#1976d2'} />
                        <Title style={styles.cardTitle}>{translate('admin.dashboard.attendanceRate')}</Title>
                      </View>
                      <Divider style={styles.divider} />
                      <PieChart
                        data={sanitizeChartData(getAttendanceData())}
                        width={Dimensions.get('window').width - 80}
                        height={220}
                        chartConfig={{
                          backgroundColor: '#ffffff',
                          backgroundGradientFrom: '#ffffff',
                          backgroundGradientTo: '#ffffff',
                          decimalPlaces: 0,
                          color: (opacity = 1) => `rgba(33, 150, 243, ${opacity})`,
                          labelColor: (opacity = 1) => `rgba(0, 0, 0, ${opacity})`,
                          style: {
                            borderRadius: 16,
                          },
                        }}
                        accessor="population"
                        backgroundColor="transparent"
                        paddingLeft="15"
                        style={styles.chart}
                        hasLegend={true}
                        center={[Dimensions.get('window').width / 6, 0]}
                      />
                    </Card.Content>
                  </LinearGradient>
                </Card>
              </Animatable.View>

              <Animatable.View animation="fadeInUp" duration={600} delay={400}>
                <Card style={styles.analyticsCard}>
                  <LinearGradient
                    colors={['#f5f5f5', '#ffffff']}
                    style={styles.cardGradient}
                  >
                    <Card.Content>
                      <View style={styles.cardHeader}>
                        <MaterialCommunityIcons name="account-star" size={24} color={'#1976d2'} />
                        <Title style={styles.cardTitle}>{translate('admin.dashboard.topPerformers')}</Title>
                      </View>
                      <Divider style={styles.divider} />
                      <DataTable style={styles.dataTable}>
                        <DataTable.Header style={styles.tableHeader}>
                          <DataTable.Title style={styles.tableHeaderCell}>{translate('admin.studentManagement.student')}</DataTable.Title>
                          <DataTable.Title numeric style={styles.tableHeaderCell}>{translate('admin.dashboard.average')}</DataTable.Title>
                          <DataTable.Title numeric style={styles.tableHeaderCell}>{translate('admin.dashboard.attendance')}</DataTable.Title>
                        </DataTable.Header>

                        {analytics.topPerformers.map((student, index) => (
                          <Animatable.View key={student.studentId} animation="fadeIn" duration={500} delay={index * 100}>
                            <DataTable.Row style={styles.tableRow}>
                              <DataTable.Cell style={styles.tableCell}>
                                <View style={styles.studentCell}>
                                  <Avatar.Text size={24} label={student.name.substring(0, 2)} style={styles.avatar} />
                                  <Text style={styles.studentName}>{student.name}</Text>
                                </View>
                              </DataTable.Cell>
                              <DataTable.Cell numeric style={styles.tableCell}>
                                <Chip mode="outlined" style={[styles.scoreChip, {backgroundColor: student.average >= 80 ? '#e8f5e9' : '#fff3e0'}]}>
                                  {student.average.toFixed(1)}%
                                </Chip>
                              </DataTable.Cell>
                              <DataTable.Cell numeric style={styles.tableCell}>
                                <Chip mode="outlined" style={[styles.scoreChip, {backgroundColor: student.attendance >= 80 ? '#e8f5e9' : '#fff3e0'}]}>
                                  {student.attendance.toFixed(1)}%
                                </Chip>
                              </DataTable.Cell>
                            </DataTable.Row>
                          </Animatable.View>
                        ))}
                      </DataTable>
                    </Card.Content>
                  </LinearGradient>
                </Card>
              </Animatable.View>

              <Animatable.View animation="fadeInUp" duration={600} delay={500}>
                <Card style={styles.analyticsCard}>
                  <LinearGradient
                    colors={['#f5f5f5', '#ffffff']}
                    style={styles.cardGradient}
                  >
                    <Card.Content>
                      <View style={styles.cardHeader}>
                        <MaterialCommunityIcons name="alert-circle-outline" size={24} color={'#1976d2'} />
                        <Title style={styles.cardTitle}>{translate('admin.dashboard.improvementAreas')}</Title>
                      </View>
                      <Divider style={styles.divider} />
                      <DataTable style={styles.dataTable}>
                        <DataTable.Header style={styles.tableHeader}>
                          <DataTable.Title style={styles.tableHeaderCell}>{translate('admin.subjectManagement.subject')}</DataTable.Title>
                          <DataTable.Title numeric style={styles.tableHeaderCell}>{translate('admin.dashboard.averageScore')}</DataTable.Title>
                        </DataTable.Header>

                        {analytics.improvementAreas.map((area, index) => (
                          <Animatable.View key={area.subjectId} animation="fadeIn" duration={500} delay={index * 100}>
                            <DataTable.Row style={styles.tableRow}>
                              <DataTable.Cell style={styles.tableCell}>
                                <View style={styles.subjectCell}>
                                  <Avatar.Text size={24} label={(subjects.find(s => s.id === area.subjectId)?.name || area.subjectId).substring(0, 2)} style={styles.avatar} />
                                  <Text style={styles.subjectName}>{subjects.find(s => s.id === area.subjectId)?.name || area.subjectId}</Text>
                                </View>
                              </DataTable.Cell>
                              <DataTable.Cell numeric style={styles.tableCell}>
                                <Chip mode="outlined" style={[styles.scoreChip, {backgroundColor: area.average >= 70 ? '#fff3e0' : '#ffebee'}]}>
                                  {area.average.toFixed(1)}%
                                </Chip>
                              </DataTable.Cell>
                            </DataTable.Row>
                          </Animatable.View>
                        ))}
                      </DataTable>
                    </Card.Content>
                  </LinearGradient>
                </Card>
              </Animatable.View>
            </View>
          ) : (
            <Text style={styles.selectPrompt}>
              Please select a class to view performance analytics.
            </Text>
          )}
        </Animatable.View>
      </ScrollView>

      <FAB
        style={styles.fab}
        icon="refresh"
        color="white"
        onPress={() => {
          setRefreshing(true);
          fetchClasses().then(() => {
            if (selectedClass) {
              Promise.all([fetchSubjects(), fetchAnalytics()]).then(() => {
                setRefreshing(false);
              });
            } else {
              setRefreshing(false);
            }
          });
        }}
      />

      <Snackbar
        visible={snackbarVisible}
        onDismiss={() => setSnackbarVisible(false)}
        duration={3000}
        style={styles.snackbar}
        action={{
          label: translate('common.dismiss'),
          onPress: () => setSnackbarVisible(false),
        }}
      >
        {snackbarMessage}
      </Snackbar>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollView: {
    flex: 1,
  },
  headerSurface: {
    elevation: 4,
    borderRadius: 0,
    marginBottom: 16,
  },
  headerGradient: {
    padding: 16,
    borderRadius: 0,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerTitle: {
    color: 'white',
    marginLeft: 16,
    fontSize: 20,
    fontWeight: 'bold',
  },
  card: {
    margin: 16,
    borderRadius: 12,
    overflow: 'hidden',
    elevation: 4,
  },
  filterContainer: {
    marginVertical: 8,
  },
  filterRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  filterIcon: {
    marginRight: 8,
  },
  filterTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  pickerContainer: {
    marginBottom: 16,
  },
  pickerLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
    fontWeight: 'bold',
  },
  pickerWrapper: {
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    overflow: 'hidden',
  },
  picker: {
    backgroundColor: '#f5f5f5',
    height: 50,
  },
  loader: {
    marginVertical: 40,
  },
  analyticsCard: {
    marginHorizontal: 16,
    marginVertical: 8,
    borderRadius: 12,
    overflow: 'hidden',
    elevation: 4,
  },
  cardGradient: {
    borderRadius: 12,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  cardTitle: {
    marginLeft: 8,
    fontSize: 18,
    fontWeight: 'bold',
  },
  divider: {
    marginVertical: 12,
    height: 1,
    backgroundColor: '#e0e0e0',
  },
  chart: {
    marginVertical: 16,
    borderRadius: 16,
    alignSelf: 'center',
  },
  dataTable: {
    marginTop: 8,
  },
  tableHeader: {
    backgroundColor: '#f5f5f5',
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,
  },
  tableHeaderCell: {
    padding: 8,
  },
  tableRow: {
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  tableCell: {
    padding: 8,
  },
  studentCell: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  subjectCell: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    backgroundColor: '#2196f3',
    marginRight: 8,
  },
  studentName: {
    marginLeft: 8,
    fontWeight: '500',
  },
  subjectName: {
    marginLeft: 8,
    fontWeight: '500',
  },
  scoreChip: {
    height: 28,
  },
  selectPrompt: {
    textAlign: 'center',
    marginTop: 40,
    marginBottom: 40,
    color: '#666',
    fontSize: 16,
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
    backgroundColor: '#1976d2',
  },
  snackbar: {
    bottom: 70,
  },
});

export default PerformanceAnalytics;
