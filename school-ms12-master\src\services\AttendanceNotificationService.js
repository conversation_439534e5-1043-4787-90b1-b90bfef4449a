import { db, auth } from '../config/firebase';
import {
  collection,
  query,
  where,
  getDocs,
  doc,
  getDoc,
  addDoc,
  updateDoc,
  serverTimestamp
} from 'firebase/firestore';

// Import Notifications if available, otherwise use a placeholder
let Notifications;
try {
  Notifications = require('expo-notifications');
} catch (error) {
  console.log('Expo Notifications not available');
  // Create a placeholder object
  Notifications = {
    scheduleNotificationAsync: async () => {}
  };
}

class AttendanceNotificationService {
  static instance = null;

  constructor() {
    if (AttendanceNotificationService.instance) {
      return AttendanceNotificationService.instance;
    }

    AttendanceNotificationService.instance = this;
  }

  /**
   * Send notification when teacher submits attendance
   * @param {Object} submission - The attendance submission object
   */
  async notifyAttendanceSubmission(submission) {
    try {
      if (!submission) return;

      // Format date for better display
      const formattedDate = submission.formattedDate || new Date(submission.date).toLocaleDateString();

      // Format class and section information
      const classInfo = submission.sectionInfo ||
        (submission.className ? (submission.sectionName ? `${submission.className}-${submission.sectionName}` : submission.className) : '');

      // Get attendance summary for better notification content
      const studentCount = submission.studentCount || submission.summary?.total || 0;
      const presentCount = submission.summary?.present || 0;
      const absentCount = submission.summary?.absent || 0;
      const attendanceRate = submission.attendanceRate ||
        (submission.summary?.percentage ? `${submission.summary.percentage.toFixed(1)}%` : '0%');

      // Create a more detailed message
      const detailedMessage = `Class: ${classInfo}, Date: ${formattedDate}, Students: ${studentCount}, Present: ${presentCount}, Absent: ${absentCount}, Rate: ${attendanceRate}`;

      // Notify admin about the submission
      await this.notifyAdmins({
        title: 'New Attendance Submission Requires Approval',
        body: `${submission.teacherName} has submitted attendance for ${classInfo} on ${formattedDate}. ${attendanceRate} of students present.`,
        data: {
          type: 'attendance_submission',
          submissionId: submission.id,
          classId: submission.classId,
          sectionName: submission.sectionName,
          date: submission.date,
          details: detailedMessage,
          viewPath: 'AdminAttendanceApproval',
          viewParams: {
            highlight: submission.id
          }
        }
      });

      console.log('Attendance submission notification sent to admins');
    } catch (error) {
      console.error('Error sending attendance submission notification:', error);
    }
  }

  /**
   * Send notification when admin approves attendance
   * @param {Object} submission - The approved attendance submission
   */
  async notifyAttendanceApproval(submission) {
    try {
      if (!submission) return;

      // Notify teacher about the approval
      await this.notifyTeacher(submission.teacherId, {
        title: 'Attendance Approved',
        body: `Your attendance submission for ${submission.className} - ${submission.sectionName} has been approved`,
        data: {
          type: 'attendance_approved',
          submissionId: submission.id,
          classId: submission.classId,
          sectionName: submission.sectionName,
          date: submission.date
        }
      });

      // Notify students and parents about their attendance
      await this.notifyStudentsAndParents(submission);

      console.log('Attendance approval notifications sent');
    } catch (error) {
      console.error('Error sending attendance approval notifications:', error);
    }
  }

  /**
   * Send notification when admin rejects attendance
   * @param {Object} submission - The rejected attendance submission
   */
  async notifyAttendanceRejection(submission) {
    try {
      if (!submission) return;

      // Notify teacher about the rejection
      await this.notifyTeacher(submission.teacherId, {
        title: 'Attendance Rejected',
        body: `Your attendance submission for ${submission.className} - ${submission.sectionName} has been rejected`,
        data: {
          type: 'attendance_rejected',
          submissionId: submission.id,
          classId: submission.classId,
          sectionName: submission.sectionName,
          date: submission.date,
          reason: submission.rejectionReason
        }
      });

      console.log('Attendance rejection notification sent to teacher');
    } catch (error) {
      console.error('Error sending attendance rejection notification:', error);
    }
  }

  /**
   * Notify students and their parents about their attendance
   * @param {Object} submission - The approved attendance submission
   */
  async notifyStudentsAndParents(submission) {
    try {
      if (!submission || !submission.attendanceIds || submission.attendanceIds.length === 0) {
        return;
      }

      // Fetch all attendance records
      const attendanceRef = collection(db, 'attendance');
      const attendancePromises = submission.attendanceIds.map(id => getDoc(doc(attendanceRef, id)));
      const attendanceDocs = await Promise.all(attendancePromises);

      // Format date for better display
      const attendanceDate = new Date(submission.date);
      const formattedDate = attendanceDate.toLocaleDateString();

      // Format class and section information
      const classInfo = submission.className ?
        (submission.sectionName ? `${submission.className}-${submission.sectionName}` : submission.className) : '';

      // Get attendance summary for better notification content
      const totalStudents = submission.summary?.total || 0;
      const presentCount = submission.summary?.present || 0;
      const absentCount = submission.summary?.absent || 0;
      const attendanceRate = totalStudents > 0 ? (presentCount / totalStudents) * 100 : 0;

      // Process each attendance record
      for (const attendanceDoc of attendanceDocs) {
        if (!attendanceDoc.exists()) continue;

        const attendance = attendanceDoc.data();
        const studentId = attendance.studentId;

        if (!studentId) continue;

        // Notify student
        await this.notifyStudent(studentId, {
          title: 'Attendance Approved',
          body: `Your attendance for ${classInfo} on ${formattedDate} has been approved. Status: ${attendance.status}`,
          data: {
            type: 'attendance_recorded',
            attendanceId: attendanceDoc.id,
            status: attendance.status,
            date: attendance.date,
            classId: attendance.classId,
            className: attendance.className,
            sectionName: attendance.sectionName,
            viewPath: 'StudentAttendance',
            viewParams: {
              date: attendance.date,
              highlight: attendanceDoc.id
            }
          }
        });

        // Fetch student to get parent ID
        const studentDoc = await getDoc(doc(db, 'users', studentId));
        if (!studentDoc.exists()) continue;

        const student = studentDoc.data();
        const studentName = student.firstName && student.lastName ?
          `${student.firstName} ${student.lastName}` :
          student.displayName || 'your child';
        const parentId = student.parent?.id;

        if (!parentId) continue;

        // Notify parent
        await this.notifyParent(parentId, {
          title: 'Child Attendance Approved',
          body: `${studentName}'s attendance for ${classInfo} on ${formattedDate} has been approved. Status: ${attendance.status}`,
          data: {
            type: 'child_attendance_recorded',
            attendanceId: attendanceDoc.id,
            studentId: studentId,
            studentName: studentName,
            status: attendance.status,
            date: attendance.date,
            classId: attendance.classId,
            className: attendance.className,
            sectionName: attendance.sectionName,
            viewPath: 'ParentAttendanceView',
            viewParams: {
              childId: studentId,
              date: attendance.date,
              highlight: attendanceDoc.id
            }
          }
        });
      }

      console.log('Attendance notifications sent to students and parents');
    } catch (error) {
      console.error('Error notifying students and parents:', error);
    }
  }

  /**
   * Send notification to all admin users
   * @param {Object} notification - The notification object
   */
  async notifyAdmins(notification) {
    try {
      const usersRef = collection(db, 'users');
      const adminsQuery = query(usersRef, where('role', '==', 'admin'));
      const adminsSnapshot = await getDocs(adminsQuery);

      if (adminsSnapshot.empty) return;

      const sendPromises = adminsSnapshot.docs.map(doc => {
        const admin = doc.data();
        return this.sendNotification(admin.id, notification);
      });

      await Promise.all(sendPromises);
    } catch (error) {
      console.error('Error notifying admins:', error);
    }
  }

  /**
   * Send notification to a teacher
   * @param {string} teacherId - The teacher's user ID
   * @param {Object} notification - The notification object
   */
  async notifyTeacher(teacherId, notification) {
    if (!teacherId) return;

    await this.sendNotification(teacherId, notification);
  }

  /**
   * Send notification to a student
   * @param {string} studentId - The student's user ID
   * @param {Object} notification - The notification object
   */
  async notifyStudent(studentId, notification) {
    if (!studentId) return;

    await this.sendNotification(studentId, notification);
  }

  /**
   * Send notification to a parent
   * @param {string} parentId - The parent's user ID
   * @param {Object} notification - The notification object
   */
  async notifyParent(parentId, notification) {
    if (!parentId) return;

    await this.sendNotification(parentId, notification);
  }

  /**
   * Send a notification to a user
   * @param {string} userId - The user's ID
   * @param {Object} notification - The notification object
   */
  async sendNotification(userId, notification) {
    try {
      if (!userId || !notification) return;

      // Save notification to database
      const notificationRef = collection(db, 'notifications');
      const notificationData = {
        userId,
        title: notification.title,
        body: notification.body,
        data: notification.data || {},
        read: false,
        createdAt: serverTimestamp(),
        type: notification.data?.type || 'general'
      };

      const docRef = await addDoc(notificationRef, notificationData);

      // Get user's push token
      const userDoc = await getDoc(doc(db, 'users', userId));
      if (!userDoc.exists()) return;

      const user = userDoc.data();
      const expoPushToken = user.expoPushToken;

      if (!expoPushToken) return;

      // Send push notification
      await Notifications.scheduleNotificationAsync({
        content: {
          title: notification.title,
          body: notification.body,
          data: {
            ...notification.data,
            notificationId: docRef.id
          }
        },
        trigger: null // Send immediately
      });

      console.log(`Notification sent to user ${userId}`);
    } catch (error) {
      console.error('Error sending notification:', error);
    }
  }
}

export default new AttendanceNotificationService();
