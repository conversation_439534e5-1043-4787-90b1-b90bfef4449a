import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../context/language_context.dart';

class LanguageSelector extends StatelessWidget {
  final bool showLabel;
  final bool isCompact;

  const LanguageSelector({
    super.key,
    this.showLabel = false,
    this.isCompact = true,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<LanguageContext>(
      builder: (context, languageContext, child) {
        if (isCompact) {
          return _buildCompactSelector(context, languageContext);
        } else {
          return _buildFullSelector(context, languageContext);
        }
      },
    );
  }

  Widget _buildCompactSelector(BuildContext context, LanguageContext languageContext) {
    return PopupMenuButton<String>(
      icon: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.language,
            size: 20,
            color: Theme.of(context).colorScheme.primary,
          ),
          const SizedBox(width: 4),
          Text(
            languageContext.currentLanguageCode.toUpperCase(),
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
          Icon(
            Icons.arrow_drop_down,
            size: 16,
            color: Theme.of(context).colorScheme.primary,
          ),
        ],
      ),
      onSelected: (languageCode) {
        languageContext.changeLanguage(languageCode, context);
      },
      itemBuilder: (context) {
        return LanguageContext.supportedLanguages.map((language) {
          final isSelected = language.code == languageContext.currentLanguageCode;
          
          return PopupMenuItem<String>(
            value: language.code,
            child: Row(
              children: [
                if (isSelected)
                  Icon(
                    Icons.check,
                    size: 16,
                    color: Theme.of(context).colorScheme.primary,
                  )
                else
                  const SizedBox(width: 16),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        language.nativeName,
                        style: TextStyle(
                          fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                          color: isSelected 
                              ? Theme.of(context).colorScheme.primary
                              : null,
                        ),
                      ),
                      if (language.name != language.nativeName)
                        Text(
                          language.name,
                          style: TextStyle(
                            fontSize: 12,
                            color: Theme.of(context).textTheme.bodySmall?.color,
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            ),
          );
        }).toList();
      },
    );
  }

  Widget _buildFullSelector(BuildContext context, LanguageContext languageContext) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (showLabel)
          Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Text(
              languageContext.translate('language'),
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        Container(
          decoration: BoxDecoration(
            border: Border.all(
              color: Theme.of(context).colorScheme.outline,
            ),
            borderRadius: BorderRadius.circular(12),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<String>(
              value: languageContext.currentLanguageCode,
              isExpanded: true,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              icon: Icon(
                Icons.arrow_drop_down,
                color: Theme.of(context).colorScheme.primary,
              ),
              onChanged: (languageCode) {
                if (languageCode != null) {
                  languageContext.changeLanguage(languageCode, context);
                }
              },
              items: LanguageContext.supportedLanguages.map((language) {
                return DropdownMenuItem<String>(
                  value: language.code,
                  child: Row(
                    children: [
                      Icon(
                        Icons.language,
                        size: 20,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              language.nativeName,
                              style: const TextStyle(
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            if (language.name != language.nativeName)
                              Text(
                                language.name,
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Theme.of(context).textTheme.bodySmall?.color,
                                ),
                              ),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
            ),
          ),
        ),
      ],
    );
  }
}
