// Ethiopian Calendar Utility
class EthiopianCalendar {
  // Month names in different languages
  static MONTHS_EN = [
    'Meskerem', 'Tikimt', 'Hi<PERSON>', '<PERSON><PERSON><PERSON>',
    'Tir', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>'
  ];

  static MONTHS_AM = [
    'መስከረም', 'ጥቅምት', 'ህዳር', 'ታህሳስ',
    'ጥር', 'የካቲት', 'መጋቢት', 'ሚያዚያ',
    'ግንቦት', 'ሰኔ', 'ሐምሌ', 'ነሐሴ',
    'ጳጉሜ'
  ];

  static MONTHS_OR = [
    'Fulba<PERSON>', 'Onko<PERSON>lee<PERSON>', '<PERSON>aa<PERSON>', 'Muddee',
    '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON>ootessa', 'Ebla',
    '<PERSON>aa<PERSON>aa', 'Wax<PERSON><PERSON><PERSON>i', '<PERSON>oolessa', 'Hagayya',
    'Qaammee'
  ];

  static WEEKDAYS_EN = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
  static WEEKDAYS_AM = ['እሑድ', 'ሰኞ', 'ማክሰኞ', 'ረቡዕ', 'ሐሙስ', 'ዓርብ', 'ቅዳሜ'];
  static WEEKDAYS_OR = ['Dilbata', 'Wiixata', 'Kibxata', 'Roobi<PERSON>', 'Kamiisa', 'Jimaata', 'Sanbata'];

  static NEW_YEAR_MONTH = 0; // Meskerem
  static NEW_YEAR_DAY = 1;   // 1st of Meskerem
  static DAYS_IN_MONTH = 30;
  static DAYS_IN_PAGUME = 5; // 6 in leap year

  /**
   * Convert Ethiopian date to Gregorian date
   * @param {number} year - Ethiopian year
   * @param {number} month - Ethiopian month (0-12, 0 is Meskerem, 12 is Pagume)
   * @param {number} day - Ethiopian day
   * @returns {Date} - JavaScript Date object representing the Gregorian date
   */
  static toGregorian(year, month, day) {
    // Ethiopian calendar is 7-8 years behind Gregorian
    const gregorianYear = year + 7;

    // Calculate total days from Ethiopian date
    let totalDays = (month * this.DAYS_IN_MONTH) + day;

    // Adjust for Pagume if necessary
    if (month === 12) {
      totalDays = (12 * this.DAYS_IN_MONTH) + day;
    }

    // Create Gregorian date (approximate conversion)
    const gregorianDate = new Date(gregorianYear, 8, 11); // September 11
    gregorianDate.setDate(gregorianDate.getDate() + totalDays - 1);

    return gregorianDate;
  }

  /**
   * Convert Gregorian date to Ethiopian date
   * @param {Date} date - JavaScript Date object
   * @returns {Object} - Ethiopian date object with year, month, day, and monthName
   */
  static fromGregorian(date) {
    if (!(date instanceof Date)) {
      try {
        date = new Date(date);
      } catch (error) {
        console.error('Invalid date provided to fromGregorian:', error);
        return { year: 0, month: 0, day: 0, monthName: '' };
      }
    }

    const gregorianYear = date.getFullYear();
    const ethiopianYear = gregorianYear - 7;

    // September 11 is the Ethiopian New Year
    const newYear = new Date(gregorianYear, 8, 11);
    const dayDiff = Math.floor((date - newYear) / (1000 * 60 * 60 * 24));

    let month = Math.floor(dayDiff / this.DAYS_IN_MONTH);
    let day = (dayDiff % this.DAYS_IN_MONTH) + 1;

    // Adjust for dates before Ethiopian New Year
    if (dayDiff < 0) {
      const prevYear = ethiopianYear - 1;
      // Check if it's a leap year
      const isLeapYear = (prevYear % 4 === 3);
      const pagumeDays = isLeapYear ? 6 : 5;

      // Calculate days before new year
      const daysBeforeNewYear = Math.abs(dayDiff);

      if (daysBeforeNewYear <= pagumeDays) {
        // It's in Pagume
        month = 12;
        day = pagumeDays - daysBeforeNewYear + 1;
      } else {
        // It's in Nehase or earlier
        const remainingDays = daysBeforeNewYear - pagumeDays;
        month = 11 - Math.floor(remainingDays / this.DAYS_IN_MONTH);
        day = this.DAYS_IN_MONTH - (remainingDays % this.DAYS_IN_MONTH);
      }
    }

    return {
      year: month < 0 ? ethiopianYear - 1 : ethiopianYear,
      month: month < 0 ? month + 13 : month,
      day: day,
      monthName: this.getMonthName(month, 'en')
    };
  }

  /**
   * Get month name in specified language
   * @param {number} month - Month index (0-12)
   * @param {string} language - Language code ('en', 'am', 'or')
   * @returns {string} - Month name in specified language
   */
  static getMonthName(month, language = 'en') {
    if (month < 0 || month > 12) {
      console.error('Invalid month index:', month);
      return '';
    }

    switch (language) {
      case 'am':
        return this.MONTHS_AM[month];
      case 'or':
        return this.MONTHS_OR[month];
      default:
        return this.MONTHS_EN[month];
    }
  }

  /**
   * Get weekday name in specified language
   * @param {number} day - Day of week (0-6, 0 is Sunday)
   * @param {string} language - Language code ('en', 'am', 'or')
   * @returns {string} - Weekday name in specified language
   */
  static getWeekdayName(day, language = 'en') {
    if (day < 0 || day > 6) {
      console.error('Invalid weekday index:', day);
      return '';
    }

    switch (language) {
      case 'am':
        return this.WEEKDAYS_AM[day];
      case 'or':
        return this.WEEKDAYS_OR[day];
      default:
        return this.WEEKDAYS_EN[day];
    }
  }

  /**
   * Format date in Ethiopian calendar
   * @param {Date|string} date - JavaScript Date object or date string
   * @param {string} language - Language code ('en', 'am', 'or')
   * @param {Object} options - Formatting options
   * @returns {string} - Formatted date string
   */
  static formatDate(date, language = 'en', options = {}) {
    try {
      if (!(date instanceof Date)) {
        date = new Date(date);
      }

      const ethiopianDate = this.fromGregorian(date);
      const monthName = this.getMonthName(ethiopianDate.month, language);
      const weekdayName = options.weekday ? this.getWeekdayName(date.getDay(), language) + ', ' : '';

      return `${weekdayName}${ethiopianDate.day} ${monthName} ${ethiopianDate.year}`;
    } catch (error) {
      console.error('Error formatting date:', error);
      return String(date);
    }
  }

  /**
   * Format date in short format (DD/MM/YYYY)
   * @param {Date|string} date - JavaScript Date object or date string
   * @returns {string} - Formatted date string in DD/MM/YYYY format
   */
  static formatShortDate(date) {
    try {
      if (!(date instanceof Date)) {
        date = new Date(date);
      }

      const ethiopianDate = this.fromGregorian(date);
      return `${ethiopianDate.day.toString().padStart(2, '0')}/${(ethiopianDate.month + 1).toString().padStart(2, '0')}/${ethiopianDate.year}`;
    } catch (error) {
      console.error('Error formatting short date:', error);
      return String(date);
    }
  }

  /**
   * Get academic year based on Ethiopian calendar
   * @param {Date} date - JavaScript Date object
   * @returns {number} - Academic year
   */
  static getAcademicYear(date) {
    const ethiopianDate = this.fromGregorian(date);
    // Academic year starts in Meskerem (September)
    if (ethiopianDate.month >= this.NEW_YEAR_MONTH) {
      return ethiopianDate.year;
    }
    return ethiopianDate.year - 1;
  }

  /**
   * Get semester based on Ethiopian calendar
   * @param {Date} date - JavaScript Date object
   * @returns {number} - Semester (1, 2, or 3)
   */
  static getSemester(date) {
    const ethiopianDate = this.fromGregorian(date);
    // First semester: Meskerem to Tahsas (September to January)
    if (ethiopianDate.month >= 0 && ethiopianDate.month <= 3) {
      return 1;
    }
    // Second semester: Tir to Sene (January to June)
    else if (ethiopianDate.month >= 4 && ethiopianDate.month <= 9) {
      return 2;
    }
    // Summer semester: Hamle to Nehase (July to August)
    return 3;
  }

  /**
   * Check if date is a school day
   * @param {Date} date - JavaScript Date object
   * @returns {boolean} - True if school day, false otherwise
   */
  static isSchoolDay(date) {
    const day = date.getDay();
    // Assuming Saturday (6) and Sunday (0) are weekends
    return day !== 0 && day !== 6;
  }

  /**
   * Get week number in Ethiopian calendar
   * @param {Date} date - JavaScript Date object
   * @returns {number} - Week number
   */
  static getWeekNumber(date) {
    const ethiopianDate = this.fromGregorian(date);
    const newYear = this.toGregorian(ethiopianDate.year, 0, 1);
    const dayDiff = Math.floor((date - newYear) / (1000 * 60 * 60 * 24));
    return Math.ceil(dayDiff / 7);
  }

  /**
   * Check if Ethiopian year is a leap year
   * @param {number} year - Ethiopian year
   * @returns {boolean} - True if leap year, false otherwise
   */
  static isLeapYear(year) {
    return (year % 4 === 3);
  }

  /**
   * Parse date string in Ethiopian format (DD/MM/YYYY)
   * @param {string} dateStr - Date string in DD/MM/YYYY format
   * @returns {Date} - JavaScript Date object
   */
  static parseDate(dateStr) {
    try {
      const parts = dateStr.split('/');
      if (parts.length !== 3) {
        throw new Error('Invalid date format. Expected DD/MM/YYYY');
      }

      const day = parseInt(parts[0], 10);
      const month = parseInt(parts[1], 10) - 1; // Convert to 0-based month
      const year = parseInt(parts[2], 10);

      return this.toGregorian(year, month, day);
    } catch (error) {
      console.error('Error parsing Ethiopian date:', error);
      return new Date();
    }
  }
}

export default EthiopianCalendar;
