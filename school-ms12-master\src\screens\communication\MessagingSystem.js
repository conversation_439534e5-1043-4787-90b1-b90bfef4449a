import React, { useState, useEffect, useRef } from 'react';
import { View, ScrollView, StyleSheet } from 'react-native';
import { Card, Title, FAB, Portal, Modal, List, Chip, Searchbar, IconButton, Text, Avatar, ActivityIndicator } from 'react-native-paper';
import { useAuth } from '../../context/AuthContext';
import MessageService from '../../services/MessageService';
import CustomButton from '../../components/common/CustomButton';
import CustomInput from '../../components/common/CustomInput';

const MessagingSystem = () => {
  const { user } = useAuth();
  const [conversations, setConversations] = useState([]);
  const [messages, setMessages] = useState([]);
  const [users, setUsers] = useState([]);
  const [selectedConversation, setSelectedConversation] = useState(null);
  const [composeVisible, setComposeVisible] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(false);
  const scrollViewRef = useRef();
  
  const [formData, setFormData] = useState({
    recipientId: '',
    subject: '',
    content: '',
    attachments: [],
  });

  useEffect(() => {
    fetchConversations();
    const interval = setInterval(fetchConversations, 30000); // Refresh every 30 seconds
    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    if (selectedConversation) {
      fetchMessages(selectedConversation.id);
      const interval = setInterval(() => fetchMessages(selectedConversation.id), 10000); // Refresh messages every 10 seconds
      return () => clearInterval(interval);
    }
  }, [selectedConversation]);

  const fetchConversations = async () => {
    try {
      setLoading(true);
      const conversationsData = await MessageService.getConversations(user.uid);
      setConversations(conversationsData);
    } catch (error) {
      console.error('Error fetching conversations:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchMessages = async (conversationId) => {
    try {
      const messagesData = await MessageService.getMessages(conversationId);
      setMessages(messagesData);
      // Mark messages as read
      messagesData.forEach(message => {
        if (!message.readBy.includes(user.uid)) {
          MessageService.markAsRead(message.id, user.uid);
        }
      });
      setTimeout(() => {
        scrollViewRef.current?.scrollToEnd({ animated: true });
      }, 100);
    } catch (error) {
      console.error('Error fetching messages:', error);
    }
  };

  const handleSendMessage = async () => {
    try {
      setLoading(true);
      await MessageService.sendMessage({
        senderId: user.uid,
        recipientId: formData.recipientId,
        subject: formData.subject,
        content: formData.content,
        attachments: formData.attachments
      });
      
      setFormData({
        recipientId: '',
        subject: '',
        content: '',
        attachments: []
      });
      
      setComposeVisible(false);
      fetchConversations();
    } catch (error) {
      console.error('Error sending message:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = async () => {
    try {
      setLoading(true);
      const results = await MessageService.searchMessages(user.uid, searchQuery);
      setConversations(results);
    } catch (error) {
      console.error('Error searching messages:', error);
    } finally {
      setLoading(false);
    }
  };

  const renderConversation = (conversation) => {
    const otherParticipant = conversation.participants.find(p => p !== user.uid);
    const unreadCount = conversation.unreadCount[user.uid] || 0;

    return (
      <Card
        key={conversation.id}
        style={[
          styles.conversationCard,
          selectedConversation?.id === conversation.id && styles.selectedCard
        ]}
        onPress={() => setSelectedConversation(conversation)}
      >
        <Card.Content>
          <View style={styles.conversationHeader}>
            <Avatar.Text 
              size={40} 
              label={otherParticipant.substring(0, 2).toUpperCase()} 
            />
            <View style={styles.conversationInfo}>
              <Title>{otherParticipant}</Title>
              <Text numberOfLines={1}>{conversation.lastMessage}</Text>
            </View>
            {unreadCount > 0 && (
              <Chip mode="outlined">{unreadCount}</Chip>
            )}
          </View>
        </Card.Content>
      </Card>
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.sidebar}>
        <Searchbar
          placeholder="Search messages..."
          onChangeText={setSearchQuery}
          value={searchQuery}
          onSubmitEditing={handleSearch}
          style={styles.searchbar}
        />
        <ScrollView>
          {conversations.map(renderConversation)}
        </ScrollView>
        <FAB
          style={styles.fab}
          icon="plus"
          onPress={() => setComposeVisible(true)}
        />
      </View>

      <View style={styles.messageArea}>
        {selectedConversation ? (
          <>
            <View style={styles.messageHeader}>
              <Title>{selectedConversation.participants.find(p => p !== user.uid)}</Title>
            </View>
            <ScrollView 
              ref={scrollViewRef}
              style={styles.messageList}
              contentContainerStyle={styles.messageListContent}
            >
              {messages.map(message => (
                <View 
                  key={message.id}
                  style={[
                    styles.messageContainer,
                    message.senderId === user.uid ? styles.sentMessage : styles.receivedMessage
                  ]}
                >
                  <Card style={styles.messageCard}>
                    <Card.Content>
                      <Text style={styles.messageSubject}>{message.subject}</Text>
                      <Text>{message.content}</Text>
                      {message.attachments?.length > 0 && (
                        <View style={styles.attachments}>
                          {message.attachments.map((attachment, index) => (
                            <Chip key={index} icon="attachment">{attachment.name}</Chip>
                          ))}
                        </View>
                      )}
                    </Card.Content>
                  </Card>
                </View>
              ))}
            </ScrollView>
            <View style={styles.messageInput}>
              <CustomInput
                placeholder="Type a message..."
                value={formData.content}
                onChangeText={(text) => setFormData(prev => ({ ...prev, content: text }))}
                multiline
              />
              <IconButton
                icon="send"
                size={24}
                onPress={handleSendMessage}
                disabled={!formData.content.trim()}
              />
            </View>
          </>
        ) : (
          <View style={styles.noSelection}>
            <Text>Select a conversation to start messaging</Text>
          </View>
        )}
      </View>

      <Portal>
        <Modal
          visible={composeVisible}
          onDismiss={() => setComposeVisible(false)}
          contentContainerStyle={styles.modal}
        >
          <Title>New Message</Title>
          <CustomInput
            label="Recipient ID"
            value={formData.recipientId}
            onChangeText={(text) => setFormData(prev => ({ ...prev, recipientId: text }))}
          />
          <CustomInput
            label="Subject"
            value={formData.subject}
            onChangeText={(text) => setFormData(prev => ({ ...prev, subject: text }))}
          />
          <CustomInput
            label="Message"
            value={formData.content}
            onChangeText={(text) => setFormData(prev => ({ ...prev, content: text }))}
            multiline
            numberOfLines={4}
          />
          <View style={styles.modalActions}>
            <CustomButton
              mode="outlined"
              onPress={() => setComposeVisible(false)}
            >
              Cancel
            </CustomButton>
            <CustomButton
              mode="contained"
              onPress={handleSendMessage}
              loading={loading}
              disabled={!formData.recipientId || !formData.content.trim()}
            >
              Send
            </CustomButton>
          </View>
        </Modal>
      </Portal>

      {loading && (
        <View style={styles.loadingOverlay}>
          <ActivityIndicator size="large" />
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'row',
    backgroundColor: '#f5f5f5',
  },
  sidebar: {
    width: 300,
    borderRightWidth: 1,
    borderRightColor: '#e0e0e0',
    backgroundColor: '#ffffff',
  },
  messageArea: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  searchbar: {
    margin: 8,
  },
  conversationCard: {
    margin: 8,
    elevation: 2,
  },
  selectedCard: {
    backgroundColor: '#e3f2fd',
  },
  conversationHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  conversationInfo: {
    flex: 1,
    marginLeft: 12,
  },
  messageHeader: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  messageList: {
    flex: 1,
  },
  messageListContent: {
    padding: 16,
  },
  messageContainer: {
    maxWidth: '70%',
    marginVertical: 8,
  },
  sentMessage: {
    alignSelf: 'flex-end',
  },
  receivedMessage: {
    alignSelf: 'flex-start',
  },
  messageCard: {
    elevation: 2,
  },
  messageSubject: {
    fontWeight: 'bold',
    marginBottom: 4,
  },
  attachments: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 8,
  },
  messageInput: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  noSelection: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modal: {
    backgroundColor: '#ffffff',
    padding: 20,
    margin: 20,
    borderRadius: 8,
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 16,
  },
  fab: {
    position: 'absolute',
    right: 16,
    bottom: 16,
  },
  loadingOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(255, 255, 255, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default MessagingSystem;
