import React, { useState, useEffect } from 'react';
import { View, ScrollView, StyleSheet } from 'react-native';
import { Card, Title, Text, Searchbar, List, Chip, DataTable } from 'react-native-paper';
import { db } from '../../config/firebase';
import { collection, query, getDocs, where } from 'firebase/firestore';
import { useAuth } from '../../context/AuthContext';

const LibraryAccess = () => {
  const { user } = useAuth();
  const [books, setBooks] = useState([]);
  const [categories, setCategories] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [borrowedBooks, setBorrowedBooks] = useState([]);

  useEffect(() => {
    fetchCategories();
    fetchBooks();
    fetchBorrowedBooks();
  }, []);

  const fetchCategories = async () => {
    try {
      const categoriesRef = collection(db, 'bookCategories');
      const querySnapshot = await getDocs(categoriesRef);
      
      const categoriesData = [];
      querySnapshot.forEach((doc) => {
        categoriesData.push({ id: doc.id, ...doc.data() });
      });
      
      setCategories(categoriesData);
    } catch (error) {
      console.error('Error fetching categories:', error);
    }
  };

  const fetchBooks = async () => {
    try {
      const booksRef = collection(db, 'books');
      const querySnapshot = await getDocs(booksRef);
      
      const booksData = [];
      querySnapshot.forEach((doc) => {
        booksData.push({ id: doc.id, ...doc.data() });
      });
      
      setBooks(booksData);
    } catch (error) {
      console.error('Error fetching books:', error);
    }
  };

  const fetchBorrowedBooks = async () => {
    try {
      const borrowingsRef = collection(db, 'bookBorrowings');
      const q = query(
        borrowingsRef,
        where('userId', '==', user.uid),
        where('status', 'in', ['borrowed', 'overdue'])
      );
      const querySnapshot = await getDocs(q);
      
      const borrowedData = [];
      querySnapshot.forEach((doc) => {
        borrowedData.push({ id: doc.id, ...doc.data() });
      });
      
      setBorrowedBooks(borrowedData);
    } catch (error) {
      console.error('Error fetching borrowed books:', error);
    }
  };

  const filteredBooks = books.filter(book =>
    (book.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    book.author.toLowerCase().includes(searchQuery.toLowerCase()) ||
    book.isbn.includes(searchQuery)) &&
    (!selectedCategory || book.category === selectedCategory)
  );

  const getBookStatus = (bookId) => {
    const borrowing = borrowedBooks.find(b => b.bookId === bookId);
    return borrowing ? borrowing.status : null;
  };

  return (
    <View style={styles.container}>
      <Card style={styles.statsCard}>
        <Card.Content>
          <Title>My Library Stats</Title>
          <View style={styles.statsContainer}>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{borrowedBooks.length}</Text>
              <Text style={styles.statLabel}>Books Borrowed</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>
                {borrowedBooks.filter(b => b.status === 'overdue').length}
              </Text>
              <Text style={styles.statLabel}>Overdue</Text>
            </View>
          </View>
        </Card.Content>
      </Card>

      <View style={styles.searchContainer}>
        <Searchbar
          placeholder="Search books..."
          onChangeText={setSearchQuery}
          value={searchQuery}
          style={styles.searchBar}
        />

        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <Chip
            selected={!selectedCategory}
            onPress={() => setSelectedCategory('')}
            style={styles.categoryChip}
          >
            All
          </Chip>
          {categories.map((category) => (
            <Chip
              key={category.id}
              selected={selectedCategory === category.id}
              onPress={() => setSelectedCategory(category.id)}
              style={styles.categoryChip}
            >
              {category.name}
            </Chip>
          ))}
        </ScrollView>
      </View>

      <Card style={styles.booksCard}>
        <Card.Content>
          <DataTable>
            <DataTable.Header>
              <DataTable.Title>Title</DataTable.Title>
              <DataTable.Title>Author</DataTable.Title>
              <DataTable.Title>Category</DataTable.Title>
              <DataTable.Title>Status</DataTable.Title>
            </DataTable.Header>

            {filteredBooks.map((book) => {
              const bookStatus = getBookStatus(book.id);
              return (
                <DataTable.Row
                  key={book.id}
                  onPress={() => {/* Navigate to book details */}}
                >
                  <DataTable.Cell>{book.title}</DataTable.Cell>
                  <DataTable.Cell>{book.author}</DataTable.Cell>
                  <DataTable.Cell>
                    {categories.find(c => c.id === book.category)?.name}
                  </DataTable.Cell>
                  <DataTable.Cell>
                    <Chip
                      mode="outlined"
                      selectedColor={
                        bookStatus === 'borrowed' ? '#2196F3' :
                        bookStatus === 'overdue' ? '#F44336' :
                        book.status === 'available' ? '#4CAF50' :
                        '#9E9E9E'
                      }
                    >
                      {bookStatus || book.status}
                    </Chip>
                  </DataTable.Cell>
                </DataTable.Row>
              );
            })}
          </DataTable>
        </Card.Content>
      </Card>

      {borrowedBooks.length > 0 && (
        <Card style={styles.borrowedCard}>
          <Card.Content>
            <Title>My Borrowed Books</Title>
            {borrowedBooks.map((borrowing) => {
              const book = books.find(b => b.id === borrowing.bookId);
              return (
                <List.Item
                  key={borrowing.id}
                  title={book?.title}
                  description={`Due: ${new Date(borrowing.dueDate).toLocaleDateString()}`}
                  left={props => <List.Icon {...props} icon="book" />}
                  right={() => (
                    <Chip
                      mode="outlined"
                      selectedColor={
                        borrowing.status === 'overdue' ? '#F44336' : '#2196F3'
                      }
                    >
                      {borrowing.status}
                    </Chip>
                  )}
                />
              );
            })}
          </Card.Content>
        </Card>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  statsCard: {
    margin: 10,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 10,
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  statLabel: {
    color: '#666',
    marginTop: 5,
  },
  searchContainer: {
    backgroundColor: 'white',
    padding: 10,
    elevation: 2,
  },
  searchBar: {
    marginBottom: 10,
    elevation: 0,
  },
  categoryChip: {
    margin: 4,
  },
  booksCard: {
    margin: 10,
    flex: 1,
  },
  borrowedCard: {
    margin: 10,
  },
});

export default LibraryAccess;
