import React, { useState, useEffect, useRef, useCallback } from 'react';
import { View, ScrollView, StyleSheet, Animated, TouchableOpacity, RefreshControl, Platform } from 'react-native';
import { Title, Card, DataTable, FAB, Portal, Modal, List, Provider, Text, IconButton, Surface, Divider, Avatar, Chip, Button, Snackbar, ProgressBar, Menu, Searchbar, ActivityIndicator, Badge, useTheme } from 'react-native-paper';
import { collection, addDoc, getDocs, updateDoc, doc, query, where, orderBy, limit, startAfter, deleteDoc, Timestamp } from 'firebase/firestore';
import { db } from '../../config/firebase';
import CustomButton from '../../components/common/CustomButton';
import CustomInput from '../../components/common/CustomInput';
import { useLanguage } from '../../context/LanguageContext';
import { LinearGradient } from 'expo-linear-gradient';
import * as Animatable from 'react-native-animatable';
import { useNavigation } from '@react-navigation/native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import AdminAppHeader from '../../components/common/AdminAppHeader';
import AdminSidebar from '../../components/common/AdminSidebar';
import SidebarBackdrop from '../../components/common/SidebarBackdrop';
import ActivityService from '../../services/ActivityService';

const LibraryManagement = () => {
  const { translate, language } = useLanguage();
  // No theme needed
  const navigation = useNavigation();

  // Data state
  const [books, setBooks] = useState([]);
  const [categories, setCategories] = useState([]);
  const [borrowings, setBorrowings] = useState([]);
  const [visible, setVisible] = useState(false);
  const [modalType, setModalType] = useState('');
  const [fabOpen, setFabOpen] = useState(false);
  const [loading, setLoading] = useState(false);

  // Search state
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState('books'); // 'books', 'categories', 'borrowings'
  const [showFilters, setShowFilters] = useState(true);

  // Sidebar state
  const [sidebarVisible, setSidebarVisible] = useState(false);
  const [activeSidebarItem, setActiveSidebarItem] = useState('LibraryManagement');
  const drawerAnim = useRef(new Animated.Value(-300)).current;

  // Enhanced UI state
  const [refreshing, setRefreshing] = useState(false);
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');

  // Confirmation dialog state
  const [confirmDialogVisible, setConfirmDialogVisible] = useState(false);
  const [confirmDialogAction, setConfirmDialogAction] = useState(null);
  const [confirmDialogParams, setConfirmDialogParams] = useState(null);
  const [confirmDialogTitle, setConfirmDialogTitle] = useState('');
  const [confirmDialogMessage, setConfirmDialogMessage] = useState('');

  const [formData, setFormData] = useState({
    title: '',
    author: '',
    isbn: '',
    category: '',
    quantity: '',
    location: '',
    description: ''
  });

  // Toggle sidebar
  const toggleSidebar = () => {
    const toValue = sidebarVisible ? -300 : 0;
    Animated.timing(drawerAnim, {
      toValue,
      duration: 250,
      useNativeDriver: true,
    }).start();
    setSidebarVisible(!sidebarVisible);
  };

  // Toggle filter visibility
  const toggleFilters = () => {
    setShowFilters(!showFilters);
  };

  // Toggle active tab
  const handleTabChange = (tab) => {
    setActiveTab(tab);
  };

  // Show confirmation dialog
  const showConfirmDialog = (action, params, title, message) => {
    setConfirmDialogAction(() => action);
    setConfirmDialogParams(params);
    setConfirmDialogTitle(title);
    setConfirmDialogMessage(message);
    setConfirmDialogVisible(true);
  };

  // Handle confirmation dialog confirm action
  const handleConfirmDialogConfirm = () => {
    if (confirmDialogAction) {
      confirmDialogAction(confirmDialogParams);
    }
    setConfirmDialogVisible(false);
  };

  // Handle confirmation dialog dismiss
  const handleConfirmDialogDismiss = () => {
    setConfirmDialogVisible(false);
  };

  // Refresh function for pull-to-refresh
  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      await Promise.all([
        fetchBooks(),
        fetchCategories(),
        fetchBorrowings()
      ]);
      setSnackbarMessage(translate('library.refreshSuccess') || 'Library data refreshed');
      setSnackbarVisible(true);
    } catch (error) {
      console.error('Error refreshing library data:', error);
      setSnackbarMessage(translate('library.refreshError') || 'Failed to refresh library data');
      setSnackbarVisible(true);
    } finally {
      setRefreshing(false);
    }
  }, []);

  useEffect(() => {
    // Hide header in this screen as we're using our own
    navigation.setOptions({
      headerShown: false,
    });

    // Set active sidebar item
    setActiveSidebarItem('LibraryManagement');

    // Fetch initial data
    fetchBooks();
    fetchCategories();
    fetchBorrowings();
  }, [navigation]);

  const fetchBooks = async () => {
    try {
      setLoading(true);
      const booksRef = collection(db, 'books');
      const q = query(booksRef, orderBy('createdAt', 'desc'));
      const snapshot = await getDocs(q);
      const booksData = [];
      snapshot.forEach(doc => booksData.push({ id: doc.id, ...doc.data() }));
      setBooks(booksData);
      return booksData;
    } catch (error) {
      console.error('Error fetching books:', error);
      setSnackbarMessage(translate('library.fetchBooksError') || 'Failed to load books');
      setSnackbarVisible(true);
      return [];
    } finally {
      setLoading(false);
    }
  };

  const fetchCategories = async () => {
    try {
      const categoriesRef = collection(db, 'bookCategories');
      const q = query(categoriesRef, orderBy('name', 'asc'));
      const snapshot = await getDocs(q);
      const categoriesData = [];
      snapshot.forEach(doc => categoriesData.push({ id: doc.id, ...doc.data() }));
      setCategories(categoriesData);
      return categoriesData;
    } catch (error) {
      console.error('Error fetching categories:', error);
      setSnackbarMessage(translate('library.fetchCategoriesError') || 'Failed to load categories');
      setSnackbarVisible(true);
      return [];
    }
  };

  const fetchBorrowings = async () => {
    try {
      const borrowingsRef = collection(db, 'bookBorrowings');
      const q = query(borrowingsRef, orderBy('borrowDate', 'desc'));
      const snapshot = await getDocs(q);
      const borrowingsData = [];
      snapshot.forEach(doc => borrowingsData.push({ id: doc.id, ...doc.data() }));
      setBorrowings(borrowingsData);
      return borrowingsData;
    } catch (error) {
      console.error('Error fetching borrowings:', error);
      setSnackbarMessage(translate('library.fetchBorrowingsError') || 'Failed to load borrowings');
      setSnackbarVisible(true);
      return [];
    }
  };

  // Filter books based on search query
  const filteredBooks = books.filter(book => {
    if (!searchQuery.trim()) return true;
    const query = searchQuery.toLowerCase();
    return (
      book.title?.toLowerCase().includes(query) ||
      book.author?.toLowerCase().includes(query) ||
      book.isbn?.toLowerCase().includes(query) ||
      book.category?.toLowerCase().includes(query) ||
      book.location?.toLowerCase().includes(query)
    );
  });

  // Filter categories based on search query
  const filteredCategories = categories.filter(category => {
    if (!searchQuery.trim()) return true;
    const query = searchQuery.toLowerCase();
    return (
      category.name?.toLowerCase().includes(query) ||
      category.description?.toLowerCase().includes(query)
    );
  });

  // Filter borrowings based on search query
  const filteredBorrowings = borrowings.filter(borrowing => {
    if (!searchQuery.trim()) return true;
    const query = searchQuery.toLowerCase();
    return (
      borrowing.bookTitle?.toLowerCase().includes(query) ||
      borrowing.borrowerName?.toLowerCase().includes(query) ||
      borrowing.status?.toLowerCase().includes(query)
    );
  });

  const addBook = async () => {
    try {
      setLoading(true);

      // Validate form data
      if (!formData.title || !formData.author || !formData.quantity) {
        setSnackbarMessage(translate('library.missingFields') || 'Please fill in all required fields');
        setSnackbarVisible(true);
        return;
      }

      // Add the book to Firestore
      const newBook = {
        ...formData,
        quantity: parseInt(formData.quantity),
        available: parseInt(formData.quantity),
        createdAt: new Date().toISOString()
      };

      await addDoc(collection(db, 'books'), newBook);

      // Log activity
      await ActivityService.logActivity(
        'admin',
        'library',
        translate('activities.addBook') || 'Added new book',
        translate('activities.addBookDesc', { title: formData.title }) || `Added book: ${formData.title}`
      );

      // Refresh books and show success message
      await fetchBooks();
      setVisible(false);
      setFormData({
        title: '',
        author: '',
        isbn: '',
        category: '',
        quantity: '',
        location: '',
        description: ''
      });

      setSnackbarMessage(translate('library.bookAdded') || 'Book added successfully');
      setSnackbarVisible(true);
    } catch (error) {
      console.error('Error adding book:', error);
      setSnackbarMessage(translate('library.addBookError') || 'Failed to add book');
      setSnackbarVisible(true);
    } finally {
      setLoading(false);
    }
  };

  const addCategory = async () => {
    try {
      setLoading(true);

      // Validate form data
      if (!formData.name) {
        setSnackbarMessage(translate('library.missingCategoryName') || 'Please enter a category name');
        setSnackbarVisible(true);
        return;
      }

      // Add the category to Firestore
      const newCategory = {
        name: formData.name,
        description: formData.description || '',
        createdAt: new Date().toISOString()
      };

      await addDoc(collection(db, 'bookCategories'), newCategory);

      // Log activity
      await ActivityService.logActivity(
        'admin',
        'library',
        translate('activities.addCategory') || 'Added new category',
        translate('activities.addCategoryDesc', { name: formData.name }) || `Added category: ${formData.name}`
      );

      // Refresh categories and show success message
      await fetchCategories();
      setVisible(false);
      setFormData({ name: '', description: '' });

      setSnackbarMessage(translate('library.categoryAdded') || 'Category added successfully');
      setSnackbarVisible(true);
    } catch (error) {
      console.error('Error adding category:', error);
      setSnackbarMessage(translate('library.addCategoryError') || 'Failed to add category');
      setSnackbarVisible(true);
    } finally {
      setLoading(false);
    }
  };

  // Function to delete a book
  const deleteBook = async (bookId, bookTitle) => {
    try {
      showConfirmDialog(
        async () => {
          try {
            setLoading(true);
            await deleteDoc(doc(db, 'books', bookId));

            // Log activity
            await ActivityService.logActivity(
              'admin',
              'library',
              translate('activities.deleteBook') || 'Deleted book',
              translate('activities.deleteBookDesc', { title: bookTitle }) || `Deleted book: ${bookTitle}`
            );

            await fetchBooks();
            setSnackbarMessage(translate('library.bookDeleted') || 'Book deleted successfully');
            setSnackbarVisible(true);
          } catch (error) {
            console.error('Error deleting book:', error);
            setSnackbarMessage(translate('library.deleteBookError') || 'Failed to delete book');
            setSnackbarVisible(true);
          } finally {
            setLoading(false);
          }
        },
        bookId,
        translate('library.confirmDeleteBook') || 'Confirm Delete Book',
        translate('library.confirmDeleteBookMessage', { title: bookTitle }) || `Are you sure you want to delete the book "${bookTitle}"? This action cannot be undone.`
      );
    } catch (error) {
      console.error('Error preparing to delete book:', error);
    }
  };

  // Function to delete a category
  const deleteCategory = async (categoryId, categoryName) => {
    try {
      showConfirmDialog(
        async () => {
          try {
            setLoading(true);
            await deleteDoc(doc(db, 'bookCategories', categoryId));

            // Log activity
            await ActivityService.logActivity(
              'admin',
              'library',
              translate('activities.deleteCategory') || 'Deleted category',
              translate('activities.deleteCategoryDesc', { name: categoryName }) || `Deleted category: ${categoryName}`
            );

            await fetchCategories();
            setSnackbarMessage(translate('library.categoryDeleted') || 'Category deleted successfully');
            setSnackbarVisible(true);
          } catch (error) {
            console.error('Error deleting category:', error);
            setSnackbarMessage(translate('library.deleteCategoryError') || 'Failed to delete category');
            setSnackbarVisible(true);
          } finally {
            setLoading(false);
          }
        },
        categoryId,
        translate('library.confirmDeleteCategory') || 'Confirm Delete Category',
        translate('library.confirmDeleteCategoryMessage', { name: categoryName }) || `Are you sure you want to delete the category "${categoryName}"? This action cannot be undone.`
      );
    } catch (error) {
      console.error('Error preparing to delete category:', error);
    }
  };

  const renderModalContent = () => {
    switch (modalType) {
      case 'book':
        return (
          <ScrollView style={styles.modalScrollView}>
            <View style={styles.modalHeader}>
              <Title style={styles.modalTitle}>{translate('library.addBook') || "Add New Book"}</Title>
              <IconButton
                icon="close"
                size={24}
                onPress={() => setVisible(false)}
              />
            </View>

            <Divider style={styles.modalDivider} />

            <View style={styles.formContainer}>
              <CustomInput
                label={translate('library.title') || "Title"}
                value={formData.title}
                onChangeText={(text) => setFormData({ ...formData, title: text })}
                style={styles.formInput}
                required
              />
              <CustomInput
                label={translate('library.author') || "Author"}
                value={formData.author}
                onChangeText={(text) => setFormData({ ...formData, author: text })}
                style={styles.formInput}
                required
              />
              <CustomInput
                label={translate('library.isbn') || "ISBN"}
                value={formData.isbn}
                onChangeText={(text) => setFormData({ ...formData, isbn: text })}
                style={styles.formInput}
              />

              <Menu
                visible={showCategoryMenu}
                onDismiss={() => setShowCategoryMenu(false)}
                anchor={
                  <TouchableOpacity onPress={() => setShowCategoryMenu(true)}>
                    <CustomInput
                      label={translate('library.category') || "Category"}
                      value={formData.category}
                      editable={false}
                      style={styles.formInput}
                      right={<IconButton icon="menu-down" />}
                    />
                  </TouchableOpacity>
                }
                contentStyle={styles.menuContent}
              >
                <ScrollView style={styles.menuScrollView} contentContainerStyle={styles.menuScrollContent}>
                  {categories.map(category => (
                    <Menu.Item
                      key={category.id}
                      title={category.name}
                      onPress={() => {
                        setFormData({ ...formData, category: category.name });
                        setShowCategoryMenu(false);
                      }}
                    />
                  ))}
                </ScrollView>
              </Menu>

              <CustomInput
                label={translate('library.quantity') || "Quantity"}
                value={formData.quantity}
                onChangeText={(text) => setFormData({ ...formData, quantity: text })}
                keyboardType="numeric"
                style={styles.formInput}
                required
              />
              <CustomInput
                label={translate('library.location') || "Location"}
                value={formData.location}
                onChangeText={(text) => setFormData({ ...formData, location: text })}
                style={styles.formInput}
              />
              <CustomInput
                label={translate('library.description') || "Description"}
                value={formData.description}
                onChangeText={(text) => setFormData({ ...formData, description: text })}
                multiline
                numberOfLines={3}
                style={styles.formInput}
              />

              <View style={styles.modalButtons}>
                <CustomButton
                  mode="outlined"
                  onPress={() => setVisible(false)}
                  style={styles.cancelButton}
                  icon="close"
                >
                  {translate('common.cancel') || "Cancel"}
                </CustomButton>
                <CustomButton
                  mode="contained"
                  onPress={addBook}
                  loading={loading}
                  style={styles.submitButton}
                  icon="book-plus"
                >
                  {translate('library.addBook') || "Add Book"}
                </CustomButton>
              </View>
            </View>
          </ScrollView>
        );
      case 'category':
        return (
          <ScrollView style={styles.modalScrollView}>
            <View style={styles.modalHeader}>
              <Title style={styles.modalTitle}>{translate('library.addCategory') || "Add New Category"}</Title>
              <IconButton
                icon="close"
                size={24}
                onPress={() => setVisible(false)}
              />
            </View>

            <Divider style={styles.modalDivider} />

            <View style={styles.formContainer}>
              <CustomInput
                label={translate('library.categoryName') || "Category Name"}
                value={formData.name}
                onChangeText={(text) => setFormData({ ...formData, name: text })}
                style={styles.formInput}
                required
              />
              <CustomInput
                label={translate('library.description') || "Description"}
                value={formData.description}
                onChangeText={(text) => setFormData({ ...formData, description: text })}
                multiline
                numberOfLines={3}
                style={styles.formInput}
              />

              <View style={styles.modalButtons}>
                <CustomButton
                  mode="outlined"
                  onPress={() => setVisible(false)}
                  style={styles.cancelButton}
                  icon="close"
                >
                  {translate('common.cancel') || "Cancel"}
                </CustomButton>
                <CustomButton
                  mode="contained"
                  onPress={addCategory}
                  loading={loading}
                  style={styles.submitButton}
                  icon="shape-plus"
                >
                  {translate('library.addCategory') || "Add Category"}
                </CustomButton>
              </View>
            </View>
          </ScrollView>
        );
      default:
        return null;
    }
  };

  // State for category menu
  const [showCategoryMenu, setShowCategoryMenu] = useState(false);

  return (
    <Provider>
      <SafeAreaView style={styles.container}>
        {/* Admin App Header */}
        <AdminAppHeader
          title={translate('library.title') || "Library Management"}
          onMenuPress={toggleSidebar}
        />

        {/* Sidebar Backdrop - only visible when sidebar is open */}
        {sidebarVisible && (
          <SidebarBackdrop onPress={toggleSidebar} />
        )}

        {/* Admin Sidebar */}
        <AdminSidebar
          drawerAnim={drawerAnim}
          activeSidebarItem={activeSidebarItem}
          setActiveSidebarItem={setActiveSidebarItem}
          toggleDrawer={toggleSidebar}
        />

        {/* Main Content */}
        <View style={styles.content}>
          {/* Search Bar */}
          <Animatable.View animation="fadeIn" duration={500}>
            <View style={styles.searchContainer}>
              <Searchbar
                placeholder={translate('library.searchPlaceholder') || "Search library..."}
                onChangeText={query => setSearchQuery(query)}
                value={searchQuery}
                style={styles.searchBar}
                icon="magnify"
                iconColor={'#1976d2'}
              />
              <IconButton
                icon={showFilters ? "filter-variant" : "filter-variant-plus"}
                size={24}
                style={styles.filterToggleButton}
                onPress={toggleFilters}
                color={'#1976d2'}
              />
            </View>
          </Animatable.View>

          {/* Filter Section */}
          {showFilters && (
            <Animatable.View
              animation={showFilters ? "fadeIn" : "fadeOut"}
              duration={300}
              style={styles.filterContainer}
            >
              <View style={styles.filterHeader}>
                <Text style={styles.filterTitle}>{translate('library.filters') || "Filters"}</Text>
                <IconButton
                  icon="chevron-up"
                  size={24}
                  onPress={toggleFilters}
                  color={'#1976d2'}
                />
              </View>

              <View style={styles.tabContainer}>
                <Chip
                  selected={activeTab === 'books'}
                  onPress={() => handleTabChange('books')}
                  style={styles.tabChip}
                  icon="book-multiple"
                >
                  {translate('library.books') || "Books"}
                </Chip>
                <Chip
                  selected={activeTab === 'categories'}
                  onPress={() => handleTabChange('categories')}
                  style={styles.tabChip}
                  icon="shape"
                >
                  {translate('library.categories') || "Categories"}
                </Chip>
                <Chip
                  selected={activeTab === 'borrowings'}
                  onPress={() => handleTabChange('borrowings')}
                  style={styles.tabChip}
                  icon="account-clock"
                >
                  {translate('library.borrowings') || "Borrowings"}
                </Chip>
              </View>
            </Animatable.View>
          )}

          <ScrollView
            style={styles.scrollView}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={onRefresh}
                colors={['#1976d2']}
                tintColor={'#1976d2'}
              />
            }
          >
            {/* Books Section */}
            {(activeTab === 'books' || activeTab === 'all') && (
              <Animatable.View animation="fadeIn" duration={500}>
                <Card style={styles.card}>
                  <Card.Content>
                    <View style={styles.cardHeader}>
                      <Title style={styles.cardTitle}>{translate('library.books') || "Books"}</Title>
                      <Chip
                        icon="information"
                        mode="outlined"
                        style={styles.countChip}
                      >
                        {filteredBooks.length} {translate('library.booksCount') || "books"}
                      </Chip>
                    </View>

                    {loading ? (
                      <View style={styles.loadingContainer}>
                        <ActivityIndicator size="large" color={'#1976d2'} />
                        <Text style={styles.loadingText}>{translate('common.loading') || "Loading..."}</Text>
                      </View>
                    ) : filteredBooks.length === 0 ? (
                      <View style={styles.emptyContainer}>
                        <IconButton icon="book-remove" size={50} color={'#9e9e9e'} />
                        <Text style={styles.emptyText}>{translate('library.noBooks') || "No books found"}</Text>
                        <Text style={styles.emptySubtext}>
                          {searchQuery ?
                            (translate('library.noBooksSearch') || "No books match your search criteria") :
                            (translate('library.addNewBook') || "Add a new book using the + button")}
                        </Text>
                      </View>
                    ) : (
                      <DataTable style={styles.dataTable}>
                        <DataTable.Header style={styles.tableHeader}>
                          <DataTable.Title style={styles.tableHeaderCell}>{translate('library.title') || "Title"}</DataTable.Title>
                          <DataTable.Title style={styles.tableHeaderCell}>{translate('library.author') || "Author"}</DataTable.Title>
                          <DataTable.Title style={styles.tableHeaderCell} numeric>{translate('library.available') || "Available"}</DataTable.Title>
                          <DataTable.Title style={styles.tableHeaderCell}>{translate('library.category') || "Category"}</DataTable.Title>
                          <DataTable.Title style={styles.tableHeaderCell}>{translate('common.actions') || "Actions"}</DataTable.Title>
                        </DataTable.Header>

                        {filteredBooks.map((book) => (
                          <DataTable.Row key={book.id} style={styles.tableRow}>
                            <DataTable.Cell style={styles.tableCell}>{book.title}</DataTable.Cell>
                            <DataTable.Cell style={styles.tableCell}>{book.author}</DataTable.Cell>
                            <DataTable.Cell style={styles.tableCell} numeric>{book.available}</DataTable.Cell>
                            <DataTable.Cell style={styles.tableCell}>{book.category}</DataTable.Cell>
                            <DataTable.Cell style={styles.tableCell}>
                              <IconButton
                                icon="delete"
                                size={20}
                                color={'#B00020'}
                                onPress={() => deleteBook(book.id, book.title)}
                              />
                            </DataTable.Cell>
                          </DataTable.Row>
                        ))}
                      </DataTable>
                    )}
                  </Card.Content>
                </Card>
              </Animatable.View>
            )}

            {/* Categories Section */}
            {(activeTab === 'categories' || activeTab === 'all') && (
              <Animatable.View animation="fadeIn" duration={500} delay={100}>
                <Card style={styles.card}>
                  <Card.Content>
                    <View style={styles.cardHeader}>
                      <Title style={styles.cardTitle}>{translate('library.categories') || "Categories"}</Title>
                      <Chip
                        icon="information"
                        mode="outlined"
                        style={styles.countChip}
                      >
                        {filteredCategories.length} {translate('library.categoriesCount') || "categories"}
                      </Chip>
                    </View>

                    {loading ? (
                      <View style={styles.loadingContainer}>
                        <ActivityIndicator size="large" color={'#1976d2'} />
                        <Text style={styles.loadingText}>{translate('common.loading') || "Loading..."}</Text>
                      </View>
                    ) : filteredCategories.length === 0 ? (
                      <View style={styles.emptyContainer}>
                        <IconButton icon="shape-outline" size={50} color={'#9e9e9e'} />
                        <Text style={styles.emptyText}>{translate('library.noCategories') || "No categories found"}</Text>
                        <Text style={styles.emptySubtext}>
                          {searchQuery ?
                            (translate('library.noCategoriesSearch') || "No categories match your search criteria") :
                            (translate('library.addNewCategory') || "Add a new category using the + button")}
                        </Text>
                      </View>
                    ) : (
                      <DataTable style={styles.dataTable}>
                        <DataTable.Header style={styles.tableHeader}>
                          <DataTable.Title style={styles.tableHeaderCell}>{translate('library.name') || "Name"}</DataTable.Title>
                          <DataTable.Title style={styles.tableHeaderCell}>{translate('library.description') || "Description"}</DataTable.Title>
                          <DataTable.Title style={styles.tableHeaderCell}>{translate('common.actions') || "Actions"}</DataTable.Title>
                        </DataTable.Header>

                        {filteredCategories.map((category) => (
                          <DataTable.Row key={category.id} style={styles.tableRow}>
                            <DataTable.Cell style={styles.tableCell}>{category.name}</DataTable.Cell>
                            <DataTable.Cell style={styles.tableCell}>{category.description}</DataTable.Cell>
                            <DataTable.Cell style={styles.tableCell}>
                              <IconButton
                                icon="delete"
                                size={20}
                                color={'#B00020'}
                                onPress={() => deleteCategory(category.id, category.name)}
                              />
                            </DataTable.Cell>
                          </DataTable.Row>
                        ))}
                      </DataTable>
                    )}
                  </Card.Content>
                </Card>
              </Animatable.View>
            )}

            {/* Borrowings Section */}
            {(activeTab === 'borrowings' || activeTab === 'all') && (
              <Animatable.View animation="fadeIn" duration={500} delay={200}>
                <Card style={[styles.card, styles.lastCard]}>
                  <Card.Content>
                    <View style={styles.cardHeader}>
                      <Title style={styles.cardTitle}>{translate('library.borrowings') || "Recent Borrowings"}</Title>
                      <Chip
                        icon="information"
                        mode="outlined"
                        style={styles.countChip}
                      >
                        {filteredBorrowings.length} {translate('library.borrowingsCount') || "borrowings"}
                      </Chip>
                    </View>

                    {loading ? (
                      <View style={styles.loadingContainer}>
                        <ActivityIndicator size="large" color={'#1976d2'} />
                        <Text style={styles.loadingText}>{translate('common.loading') || "Loading..."}</Text>
                      </View>
                    ) : filteredBorrowings.length === 0 ? (
                      <View style={styles.emptyContainer}>
                        <IconButton icon="account-clock-outline" size={50} color={'#9e9e9e'} />
                        <Text style={styles.emptyText}>{translate('library.noBorrowings') || "No borrowings found"}</Text>
                        <Text style={styles.emptySubtext}>
                          {searchQuery ?
                            (translate('library.noBorrowingsSearch') || "No borrowings match your search criteria") :
                            (translate('library.noBorrowingsYet') || "No borrowing records yet")}
                        </Text>
                      </View>
                    ) : (
                      <DataTable style={styles.dataTable}>
                        <DataTable.Header style={styles.tableHeader}>
                          <DataTable.Title style={styles.tableHeaderCell}>{translate('library.book') || "Book"}</DataTable.Title>
                          <DataTable.Title style={styles.tableHeaderCell}>{translate('library.borrower') || "Borrower"}</DataTable.Title>
                          <DataTable.Title style={styles.tableHeaderCell}>{translate('library.status') || "Status"}</DataTable.Title>
                        </DataTable.Header>

                        {filteredBorrowings.map((borrowing) => (
                          <DataTable.Row key={borrowing.id} style={styles.tableRow}>
                            <DataTable.Cell style={styles.tableCell}>{borrowing.bookTitle}</DataTable.Cell>
                            <DataTable.Cell style={styles.tableCell}>{borrowing.borrowerName}</DataTable.Cell>
                            <DataTable.Cell style={styles.tableCell}>
                              <Chip
                                mode="flat"
                                style={{
                                  backgroundColor:
                                    borrowing.status === 'Borrowed' ? '#E3F2FD' :
                                    borrowing.status === 'Returned' ? '#E8F5E9' :
                                    borrowing.status === 'Overdue' ? '#FFEBEE' : '#F5F5F5'
                                }}
                                textStyle={{ fontSize: 12 }}
                                icon={
                                  borrowing.status === 'Borrowed' ? 'book-clock' :
                                  borrowing.status === 'Returned' ? 'book-check' :
                                  borrowing.status === 'Overdue' ? 'book-alert' : 'book'
                                }
                              >
                                {borrowing.status}
                              </Chip>
                            </DataTable.Cell>
                          </DataTable.Row>
                        ))}
                      </DataTable>
                    )}
                  </Card.Content>
                </Card>
              </Animatable.View>
            )}
          </ScrollView>
        </View>

        <Portal>
          <Modal
            visible={visible}
            onDismiss={() => setVisible(false)}
            contentContainerStyle={styles.modalContent}
          >
            {renderModalContent()}
          </Modal>

          {/* Confirmation Dialog */}
          <Modal
            visible={confirmDialogVisible}
            onDismiss={handleConfirmDialogDismiss}
            contentContainerStyle={styles.confirmDialog}
          >
            <View style={styles.confirmDialogContent}>
              <Title style={styles.confirmDialogTitle}>{confirmDialogTitle}</Title>
              <Text style={styles.confirmDialogMessage}>{confirmDialogMessage}</Text>

              <View style={styles.confirmDialogActions}>
                <Button
                  mode="outlined"
                  onPress={handleConfirmDialogDismiss}
                  style={styles.confirmDialogButton}
                >
                  {translate('common.cancel') || 'Cancel'}
                </Button>
                <Button
                  mode="contained"
                  onPress={handleConfirmDialogConfirm}
                  style={[styles.confirmDialogButton, { marginLeft: 8 }]}
                >
                  {translate('common.confirm') || 'Confirm'}
                </Button>
              </View>
            </View>
          </Modal>
        </Portal>

        <FAB.Group
          open={fabOpen}
          icon={fabOpen ? 'close' : 'plus'}
          actions={[
            {
              icon: 'book',
              label: translate('library.addBook') || 'Add Book',
              onPress: () => {
                setModalType('book');
                setVisible(true);
                setFabOpen(false);
              },
            },
            {
              icon: 'shape',
              label: translate('library.addCategory') || 'Add Category',
              onPress: () => {
                setModalType('category');
                setVisible(true);
                setFabOpen(false);
              },
            },
          ]}
          onStateChange={({ open }) => setFabOpen(open)}
          fabStyle={styles.fab}
        />

        {/* Snackbar for notifications */}
        <Snackbar
          visible={snackbarVisible}
          onDismiss={() => setSnackbarVisible(false)}
          duration={3000}
          action={{
            label: translate('common.dismiss') || 'Dismiss',
            onPress: () => setSnackbarVisible(false),
          }}
          style={styles.snackbar}
        >
          {snackbarMessage}
        </Snackbar>
      </SafeAreaView>
    </Provider>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
    paddingTop: 8,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    margin: 16,
    marginBottom: 8,
  },
  searchBar: {
    flex: 1,
    elevation: 4,
    borderRadius: 25,
    height: 50,
  },
  filterToggleButton: {
    marginLeft: 8,
    backgroundColor: '#E3F2FD',
  },
  filterContainer: {
    marginHorizontal: 16,
    marginBottom: 8,
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 16,
    elevation: 2,
  },
  filterHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  filterTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  tabContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 8,
  },
  tabChip: {
    marginRight: 8,
    marginBottom: 8,
  },
  scrollView: {
    flex: 1,
  },
  card: {
    margin: 16,
    borderRadius: 8,
    elevation: 4,
    overflow: 'hidden',
    backgroundColor: '#ffffff',
  },
  lastCard: {
    marginBottom: 80,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  cardTitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  countChip: {
    backgroundColor: '#E3F2FD',
  },
  loadingContainer: {
    padding: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  emptyContainer: {
    padding: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyText: {
    marginTop: 16,
    fontSize: 18,
    fontWeight: 'bold',
    color: '#666',
  },
  emptySubtext: {
    marginTop: 8,
    fontSize: 14,
    color: '#888',
    textAlign: 'center',
  },
  dataTable: {
    borderRadius: 8,
  },
  tableHeader: {
    backgroundColor: '#f5f5f5',
  },
  tableHeaderCell: {
    padding: 8,
  },
  tableRow: {
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  tableCell: {
    padding: 8,
  },
  modalContent: {
    backgroundColor: '#ffffff',
    padding: 20,
    margin: 20,
    borderRadius: 10,
    maxHeight: '80%',
  },
  modalScrollView: {
    maxHeight: '100%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  modalDivider: {
    height: 1,
    marginBottom: 16,
  },
  formContainer: {
    padding: 8,
  },
  formInput: {
    marginBottom: 12,
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
  },
  submitButton: {
    flex: 1,
    marginLeft: 8,
  },
  cancelButton: {
    flex: 1,
    marginRight: 8,
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
    backgroundColor: '#2196F3',
  },
  menuContent: {
    maxHeight: 300,
    width: 250,
  },
  menuScrollView: {
    maxHeight: 300,
  },
  menuScrollContent: {
    paddingVertical: 0,
  },
  confirmDialog: {
    backgroundColor: '#ffffff',
    borderRadius: 8,
    padding: 20,
    margin: 20,
    maxWidth: 500,
    alignSelf: 'center',
  },
  confirmDialogContent: {
    alignItems: 'center',
  },
  confirmDialogTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
    textAlign: 'center',
  },
  confirmDialogMessage: {
    fontSize: 14,
    marginBottom: 20,
    textAlign: 'center',
    color: '#666',
  },
  confirmDialogActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    width: '100%',
    marginTop: 16,
  },
  confirmDialogButton: {
    minWidth: 100,
  },
  snackbar: {
    bottom: 16,
  },
});

export default LibraryManagement;