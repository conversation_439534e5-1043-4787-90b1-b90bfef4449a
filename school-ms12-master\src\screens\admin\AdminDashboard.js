import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  Dimensions,
  Animated,
  Text,
  TouchableOpacity,
  ActivityIndicator,
  Platform,
  RefreshControl
} from 'react-native';
import {
  Card,
  Avatar,
  Button,
  Surface,
  IconButton,
  Dialog,
  Portal,
  FAB,
  Divider,
  List,
  TouchableRipple,
  Snackbar,
  Text as PaperText
} from 'react-native-paper';
import * as Animatable from 'react-native-animatable';
import { LinearGradient } from 'expo-linear-gradient';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useLanguage } from '../../context/LanguageContext';
import { useAuth } from '../../context/AuthContext';
import { useNotifications } from '../../context/NotificationContext';
import { useAdminSidebar } from '../../context/AdminSidebarContext';
import AdminScreenWrapper from '../../components/common/AdminScreenWrapper';
import { sanitizeChartData, sanitizeChartDatasets } from '../../utils/ChartUtils';
import { BarChart, LineChart, PieChart } from 'react-native-chart-kit';
import { collection, query, where, getDocs, orderBy, limit } from 'firebase/firestore';
import ActivityService from '../../services/ActivityService';
import { signOut } from 'firebase/auth';
import { auth, db } from '../../config/firebase';
import NetInfo from '@react-native-community/netinfo';
import MobileAdminDashboard from './MobileAdminDashboard';

const screenWidth = Dimensions.get('window').width;
const screenHeight = Dimensions.get('window').height;

const chartConfig = {
  backgroundColor: '#ffffff',
  backgroundGradientFrom: '#ffffff',
  backgroundGradientTo: '#ffffff',
  decimalPlaces: 0,
  color: (opacity = 1) => `rgba(33, 150, 243, ${opacity})`,
  labelColor: (opacity = 1) => `rgba(0, 0, 0, ${opacity})`,
  style: {
    borderRadius: 16
  }
};

// Default chart data (will be replaced with real data)
const defaultEnrollmentData = {
  labels: ['Grade 9', 'Grade 10', 'Grade 11', 'Grade 12'],
  datasets: [{
    data: sanitizeChartData([0, 0, 0, 0]),
    color: (opacity = 1) => `rgba(33, 150, 243, ${opacity})`,
    strokeWidth: 2
  }],
  legend: ['Students per Grade']
};

const defaultPerformanceTrendData = {
  labels: ['Q1', 'Q2', 'Q3', 'Q4'],
  datasets: [
    {
      data: sanitizeChartData([0, 0, 0, 0]),
      color: (opacity = 1) => `rgba(76, 175, 80, ${opacity})`,
      strokeWidth: 2
    }
  ],
  legend: ['Average Performance']
};

const defaultResourceAllocationData = [
  {
    name: 'Academic',
    value: 0,
    color: '#2196f3',
    legendFontColor: '#7F7F7F',
    legendFontSize: 12
  },
  {
    name: 'Administrative',
    value: 0,
    color: '#4caf50',
    legendFontColor: '#7F7F7F',
    legendFontSize: 12
  },
  {
    name: 'Infrastructure',
    value: 0,
    color: '#ff9800',
    legendFontColor: '#7F7F7F',
    legendFontSize: 12
  },
  {
    name: 'Other',
    value: 0,
    color: '#f44336',
    legendFontColor: '#7F7F7F',
    legendFontSize: 12
  }
];

const AdminDashboard = () => {
  // Check if we're on a mobile device
  const isMobile = Platform.OS === 'android' || Platform.OS === 'ios' || (Platform.OS === 'web' && Dimensions.get('window').width < 768);

  // If on mobile, render the mobile-optimized dashboard
  if (isMobile) {
    return <MobileAdminDashboard />;
  }

  // Desktop version continues below
  const navigation = useNavigation();
  // No theme needed
  const { translate, language } = useLanguage();
  const { user } = useAuth();
  const [logoutDialogVisible, setLogoutDialogVisible] = useState(false);
  const [currentTab, setCurrentTab] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [showAllItems, setShowAllItems] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [stats, setStats] = useState({
    totalStudents: 0,
    totalTeachers: 0,
    totalClasses: 0,
    activeUsers: 0
  });
  const [fadeAnim] = useState(new Animated.Value(0));
  const [slideAnim] = useState(new Animated.Value(50));
  const [recentActivities, setRecentActivities] = useState([]);
  const [activitiesLoading, setActivitiesLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [isConnected, setIsConnected] = useState(true);
  const [errorMessage, setErrorMessage] = useState('');
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [lastUpdated, setLastUpdated] = useState(null);

  // Chart data state
  const [enrollmentData, setEnrollmentData] = useState(defaultEnrollmentData);
  const [performanceTrendData, setPerformanceTrendData] = useState(defaultPerformanceTrendData);
  const [resourceAllocationData, setResourceAllocationData] = useState(defaultResourceAllocationData);
  // Define routes for tabs
  const tabRoutes = [
    { key: 'overview', title: translate('admin.dashboard.overview') },
    { key: 'students', title: translate('admin.navigation.students') },
    { key: 'teachers', title: translate('admin.navigation.teachers') },
    { key: 'classes', title: translate('admin.navigation.classManagement') }
  ];

  const adminMenuItems = [
    {
      category: translate('admin.navigation.academicManagement'),
      items: [
        { title: translate('admin.navigation.academicCalendar'), icon: 'calendar', route: 'AcademicCalendar' },
        { title: translate('admin.navigation.semester'), icon: 'calendar-text', route: 'SemesterManagement' },
        { title: translate('admin.navigation.subjectManagement'), icon: 'book', route: 'SubjectManagement' },
      ]
    },
    {
      category: translate('admin.navigation.userManagement'),
      items: [
        { title: translate('admin.navigation.students'), icon: 'account-multiple', route: 'StudentManagement' },
        { title: translate('admin.navigation.teachers'), icon: 'account-tie', route: 'TeacherManagement' },
        { title: translate('admin.navigation.parents'), icon: 'account-child', route: 'ParentManagement' },
        { title: translate('admin.navigation.userManagement'), icon: 'account-cog', route: 'UserManagement' },
      ]
    },
    {
      category: translate('admin.navigation.classExam'),
      items: [
        { title: translate('admin.navigation.classManagement'), icon: 'school', route: 'ClassManagement' },
        { title: translate('admin.navigation.examTimetable'), icon: 'file-document-edit', route: 'ExamManagement' },
        { title: translate('admin.navigation.classSchedule'), icon: 'calendar-check', route: 'ExamRoutineManager' },
        { title: translate('admin.navigation.results'), icon: 'chart-box', route: 'ResultsManagement' },
        { title: translate('admin.navigation.resultApproval'), icon: 'check-circle', route: 'ResultApproval' },
        { title: translate('admin.navigation.gradeApproval'), icon: 'clipboard-check', route: 'AdminGradeApproval' },
      ]
    },
    {
      category: translate('admin.navigation.teacherTools'),
      items: [
        { title: translate('admin.navigation.teacherSchedule'), icon: 'clock-outline', route: 'TeacherSchedule' },
        { title: translate('admin.navigation.teacherEvaluation'), icon: 'star-outline', route: 'TeacherEvaluation' },
        { title: translate('admin.navigation.teacherDocuments'), icon: 'file-document', route: 'TeacherDocuments' },
        { title: translate('admin.navigation.teacherAttendance'), icon: 'calendar-check', route: 'TeacherAttendance' },
        { title: translate('admin.navigation.teacherPermissions'), icon: 'shield-account', route: 'TeacherPermissions' },
      ]
    },
    {
      category: translate('admin.navigation.resourcesLibrary'),
      items: [
        { title: translate('admin.navigation.libraryManagement'), icon: 'library', route: 'LibraryManagement' },
        { title: translate('admin.navigation.librarySettings'), icon: 'cog', route: 'LibrarySettings' },
        { title: translate('admin.navigation.resourceManagement'), icon: 'folder', route: 'ResourceManagement' },
      ]
    },
    {
      category: translate('admin.navigation.communication'),
      items: [
        { title: translate('admin.navigation.messageCenter'), icon: 'message-text', route: 'CommunicationCenter' },
        { title: translate('admin.navigation.announcements'), icon: 'bullhorn', route: 'AnnouncementManagement' },
        { title: translate('admin.navigation.notifications'), icon: 'bell', route: 'NotificationSystem' },
      ]
    },
    {
      category: translate('admin.navigation.systemReports'),
      items: [
        { title: translate('admin.navigation.schoolSettings'), icon: 'cog', route: 'SchoolSettings' },
        { title: translate('admin.navigation.timeSettings'), icon: 'clock', route: 'TimeSettings' },
        { title: translate('admin.navigation.systemReports'), icon: 'file-chart', route: 'SystemReports' },
      ]
    },
  ];

  // Map all menu items to a flat structure for the sidebar
  const sidebarItems = [
    { title: translate('admin.navigation.dashboard'), icon: 'view-dashboard', key: 'dashboard' },
    ...adminMenuItems.flatMap(category => {
      return category.items.map(item => ({
        title: item.title,
        icon: item.icon,
        key: item.route,
        route: item.route
      }));
    })
  ];

  // Function to fetch all dashboard data
  const fetchAllData = useCallback(async () => {
    try {
      setIsLoading(true);
      setErrorMessage('');

      // Check network connectivity
      const netInfo = await NetInfo.fetch();
      setIsConnected(netInfo.isConnected);

      if (!netInfo.isConnected) {
        setErrorMessage(translate('common.noInternetConnection'));
        setSnackbarVisible(true);
        return;
      }

      // Fetch all data in parallel for better performance
      await Promise.all([
        fetchStats(),
        fetchNotifications(),
        fetchEnrollmentData(),
        fetchPerformanceData(),
        fetchResourceData(),
        fetchRecentActivities()
      ]);

      // Update last updated timestamp
      setLastUpdated(new Date());
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      setErrorMessage(translate('common.errorFetchingData'));
      setSnackbarVisible(true);
    } finally {
      setIsLoading(false);
      setRefreshing(false);
    }
  }, [translate]);

  // Pull-to-refresh handler
  const onRefresh = useCallback(() => {
    setRefreshing(true);
    fetchAllData();
  }, [fetchAllData]);

  // Initial data loading
  useEffect(() => {
    fetchAllData();

    // Set up network connectivity listener
    const unsubscribe = NetInfo.addEventListener(state => {
      setIsConnected(state.isConnected);

      // If connection is restored, refresh data
      if (state.isConnected && !isConnected) {
        fetchAllData();
      }
    });

    // Clean up listener on unmount
    return () => unsubscribe();
  }, [fetchAllData]);

  // Refresh data when screen comes into focus
  useFocusEffect(
    useCallback(() => {
      fetchAllData();
    }, [fetchAllData])
  );

  useEffect(() => {
    navigation.setOptions({
      headerShown: false,
    });
  }, [navigation]);

  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      })
    ]).start();
  }, []);

  // Use the notifications context
  const { fetchUserNotifications, notifications } = useNotifications();

  // Fetch notifications using the context
  const fetchNotifications = async () => {
    try {
      setIsLoading(true);
      const notificationData = await fetchUserNotifications();
      // No need to call setNotifications here as it's handled by the context
      return notificationData;
    } catch (error) {
      console.error('Error fetching notifications:', error);
      return [];
    } finally {
      setIsLoading(false);
    }
  };

  const fetchStats = useCallback(async () => {
    try {
      // Use a more efficient query with limit if we just need counts
      const studentsQuery = query(collection(db, 'users'), where('role', '==', 'student'));
      const teachersQuery = query(collection(db, 'users'), where('role', '==', 'teacher'));
      const classesQuery = query(collection(db, 'classes'));
      const activeUsersQuery = query(collection(db, 'users'), where('lastActive', '>=', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)));

      const [studentsSnap, teachersSnap, classesSnap, activeUsersSnap] = await Promise.all([
        getDocs(studentsQuery),
        getDocs(teachersQuery),
        getDocs(classesQuery),
        getDocs(activeUsersQuery)
      ]);

      // Calculate active users more accurately based on last activity
      const activeUsers = activeUsersSnap.size > 0 ? activeUsersSnap.size : studentsSnap.size + teachersSnap.size;

      setStats({
        totalStudents: studentsSnap.size,
        totalTeachers: teachersSnap.size,
        totalClasses: classesSnap.size,
        activeUsers: activeUsers
      });

      return true;
    } catch (error) {
      console.error('Error fetching stats:', error);
      setErrorMessage(translate('admin.dashboard.errorFetchingStats'));

      // Use cached data if available, otherwise set defaults
      if (!stats.totalStudents && !stats.totalTeachers) {
        setStats({
          totalStudents: 0,
          totalTeachers: 0,
          totalClasses: 0,
          activeUsers: 0
        });
      }

      return false;
    }
  }, [translate, stats]);

  const handleLogout = async () => {
    try {
      await signOut(auth);
      navigation.reset({
        index: 0,
        routes: [{ name: 'Login' }],
      });
    } catch (error) {
      console.error('Error logging out:', error);
    }
  };

  const getActivityIcon = (type) => {
    switch (type) {
      case 'user':
        return 'account';
      case 'staff':
        return 'account-tie';
      case 'class':
        return 'school';
      case 'exam':
        return 'file-document-edit';
      case 'academic':
        return 'calendar';
      case 'attendance':
        return 'calendar-check';
      case 'grade':
        return 'chart-box';
      case 'announcement':
        return 'bullhorn';
      case 'system':
        return 'cog';
      case 'finance':
        return 'currency-usd';
      default:
        return 'information';
    }
  };

  const getActivityColor = (type) => {
    switch (type) {
      case 'user':
        return '#2196F3';
      case 'staff':
        return '#2196F3';
      case 'class':
        return '#4CAF50';
      case 'exam':
        return '#FF9800';
      case 'academic':
        return '#9C27B0';
      case 'attendance':
        return '#00BCD4';
      case 'grade':
        return '#F44336';
      case 'announcement':
        return '#E91E63';
      case 'system':
        return '#607D8B';
      case 'finance':
        return '#FF9800';
      default:
        return '#757575';
    }
  };

  const getCategoryColor = (index) => {
    const colors = ['#2196f3', '#4caf50', '#ff9800', '#f44336', '#9c27b0', '#00bcd4'];
    return colors[index % colors.length];
  };

  // toggleDrawer function removed - now handled by AdminScreenWrapper

  // This function is now handled by the AdminSidebar component

  // We're now using the AdminSidebar component instead of this function

  const renderQuickStats = useCallback(() => {
    // Memoize the stats data
    const statsData = useMemo(() => [
      {
        title: translate('admin.dashboard.totalStudents'),
        value: stats.totalStudents,
        icon: 'account-group',
        color: '#2196f3',
        gradient: ['#2196f3', '#03a9f4'],
        route: 'StudentManagement'
      },
      {
        title: translate('admin.dashboard.totalTeachers'),
        value: stats.totalTeachers,
        icon: 'account-tie',
        color: '#4caf50',
        gradient: ['#4caf50', '#8bc34a'],
        route: 'TeacherManagement'
      },
      {
        title: translate('admin.dashboard.totalClasses'),
        value: stats.totalClasses,
        icon: 'school',
        color: '#ff9800',
        gradient: ['#ff9800', '#ffb74d'],
        route: 'ClassManagement'
      },
      {
        title: translate('admin.dashboard.activeUsers'),
        value: stats.activeUsers,
        icon: 'account-check',
        color: '#f44336',
        gradient: ['#f44336', '#e57373'],
        route: 'UserManagement'
      }
    ], [stats, translate]);

    return (
      <View>
        <Animated.View
          style={[
            styles.quickStats,
            {
              opacity: fadeAnim,
              transform: [{ translateY: slideAnim }]
            }
          ]}
        >
          <View style={styles.statsHeader}>
            <PaperText style={styles.statsHeaderTitle}>{translate('admin.dashboard.schoolOverview')}</PaperText>
            {lastUpdated && (
              <Text style={styles.lastUpdatedText}>
                <MaterialCommunityIcons name="clock-outline" size={12} color="#757575" />
                {' '}{translate('admin.dashboard.lastUpdated')}: {getRelativeTime(lastUpdated)}
              </Text>
            )}
          </View>

          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            decelerationRate="fast"
            snapToInterval={screenWidth * 0.38}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={onRefresh}
                colors={['#2196f3']}
                tintColor={'#2196f3'}
              />
            }
          >
            {statsData.map((stat, index) => (
              <Animatable.View
                key={index}
                animation="fadeInRight"
                duration={500}
                delay={index * 200}
              >
                <TouchableOpacity
                  onPress={() => navigation.navigate(stat.route)}
                  activeOpacity={0.8}
                >
                  <Surface style={[styles.statCard, { borderLeftColor: stat.color, borderLeftWidth: 4 }]}>
                    <LinearGradient
                      colors={stat.gradient}
                      style={styles.statGradient}
                      start={{ x: 0, y: 0 }}
                      end={{ x: 1, y: 1 }}
                    >
                      <Avatar.Icon size={48} icon={stat.icon} style={{ backgroundColor: 'transparent' }} />
                      <PaperText style={styles.statValue}>{isConnected ? stat.value : '...'}</PaperText>
                      <PaperText style={styles.statTitle}>{stat.title}</PaperText>
                    </LinearGradient>
                  </Surface>
                </TouchableOpacity>
              </Animatable.View>
            ))}
          </ScrollView>
        </Animated.View>

        {!isConnected && (
          <View style={styles.offlineWarning}>
            <MaterialCommunityIcons name="wifi-off" size={16} color="#f44336" />
            <Text style={styles.offlineWarningText}>{translate('common.offlineMode')}</Text>
          </View>
        )}
      </View>
    );
  }, [
    fadeAnim,
    slideAnim,
    stats,
    translate,
    navigation,
    isConnected,
    refreshing,
    onRefresh,
    lastUpdated
  ]);

  const renderEnrollmentSection = () => (
    <Card style={styles.card}>
      <Card.Content>
        <PaperText style={styles.sectionTitle}>{translate('admin.dashboard.enrollment')}</PaperText>
        <BarChart
          data={sanitizeChartDatasets(enrollmentData)}
          width={screenWidth - 64}
          height={220}
          chartConfig={chartConfig}
          style={styles.chart}
          showValuesOnTopOfBars
          fromZero
          withInnerLines={false}
        />
      </Card.Content>
    </Card>
  );

  const renderPerformanceSection = () => (
    <Card style={styles.card}>
      <Card.Content>
        <PaperText style={styles.sectionTitle}>{translate('admin.dashboard.performance')}</PaperText>
        <LineChart
          data={sanitizeChartDatasets(performanceTrendData)}
          width={screenWidth - 64}
          height={220}
          chartConfig={chartConfig}
          style={styles.chart}
          bezier
          withInnerLines={false}
          withDots={true}
        />
      </Card.Content>
    </Card>
  );

  const renderResourceSection = () => (
    <Card style={styles.card}>
      <Card.Content>
        <PaperText style={styles.sectionTitle}>{translate('admin.dashboard.resources')}</PaperText>
        <PieChart
          data={resourceAllocationData.map(item => ({
            ...item,
            value: isNaN(item.value) || item.value === undefined ||
                   item.value === null || item.value === Infinity ||
                   item.value === -Infinity ? 0 : item.value
          }))}
          width={screenWidth - 64}
          height={220}
          chartConfig={chartConfig}
          accessor="value"
          backgroundColor="transparent"
          paddingLeft="15"
          style={styles.chart}
          hasLegend={true}
        />
      </Card.Content>
    </Card>
  );

  const fetchRecentActivities = useCallback(async () => {
    try {
      setActivitiesLoading(true);

      // Use ActivityService to get real activities with a timeout
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Request timeout')), 10000)
      );

      const activitiesPromise = ActivityService.getRecentActivities(5);

      // Race between the actual request and the timeout
      const activities = await Promise.race([activitiesPromise, timeoutPromise]);

      if (activities && activities.length > 0) {
        setRecentActivities(activities);
        return true;
      } else {
        // Fallback to querying directly if the service doesn't return data
        const activitiesRef = collection(db, 'activities');
        const q = query(
          activitiesRef,
          orderBy('timestamp', 'desc'),
          limit(5)
        );

        const querySnapshot = await getDocs(q);
        const activitiesData = [];

        querySnapshot.forEach((doc) => {
          const data = doc.data();
          const timestamp = data.timestamp?.toDate?.()
            ? data.timestamp.toDate()
            : data.timestamp ? new Date(data.timestamp) : new Date();

          // Calculate relative time
          const relativeTime = getRelativeTime(timestamp);

          activitiesData.push({
            id: doc.id,
            ...data,
            timestamp: timestamp,
            relativeTime: relativeTime
          });
        });

        if (activitiesData.length > 0) {
          setRecentActivities(activitiesData);
          return true;
        } else {
          throw new Error('No activities found');
        }
      }
    } catch (error) {
      console.error('Error fetching recent activities:', error);

      // Only set default activities if we don't already have any
      if (recentActivities.length === 0) {
        setRecentActivities([
          {
            id: '1',
            title: translate('admin.dashboard.systemStarted'),
            description: translate('admin.dashboard.systemInitialized'),
            relativeTime: translate('admin.dashboard.justNow'),
            timestamp: new Date(),
            type: 'system',
            read: false,
            userId: 'system'
          }
        ]);
      }

      return false;
    } finally {
      setActivitiesLoading(false);
    }
  }, [translate, recentActivities.length]);

  // Helper function to calculate relative time
  const getRelativeTime = (date) => {
    const now = new Date();
    const diffMs = now - date;
    const diffSec = Math.floor(diffMs / 1000);
    const diffMin = Math.floor(diffSec / 60);
    const diffHour = Math.floor(diffMin / 60);
    const diffDay = Math.floor(diffHour / 24);

    if (diffSec < 60) {
      return 'Just now';
    } else if (diffMin < 60) {
      return `${diffMin} minute${diffMin > 1 ? 's' : ''} ago`;
    } else if (diffHour < 24) {
      return `${diffHour} hour${diffHour > 1 ? 's' : ''} ago`;
    } else if (diffDay < 30) {
      return `${diffDay} day${diffDay > 1 ? 's' : ''} ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  // Fetch enrollment data from Firestore
  const fetchEnrollmentData = async () => {
    try {
      // Query students by grade
      const usersRef = collection(db, 'users');
      const studentsQuery = query(usersRef, where('role', '==', 'student'));
      const studentsSnapshot = await getDocs(studentsQuery);

      // Count students by grade
      const gradeCount = {
        'Grade 9': 0,
        'Grade 10': 0,
        'Grade 11': 0,
        'Grade 12': 0
      };

      studentsSnapshot.forEach(doc => {
        const student = doc.data();
        if (student.grade && gradeCount.hasOwnProperty(student.grade)) {
          gradeCount[student.grade]++;
        }
      });

      // If no data found, use some realistic data
      if (Object.values(gradeCount).every(count => count === 0)) {
        const totalStudents = studentsSnapshot.size;
        if (totalStudents > 0) {
          // Distribute students across grades
          const avgPerGrade = Math.floor(totalStudents / 4);
          gradeCount['Grade 9'] = avgPerGrade + Math.floor(Math.random() * 10);
          gradeCount['Grade 10'] = avgPerGrade + Math.floor(Math.random() * 8);
          gradeCount['Grade 11'] = avgPerGrade + Math.floor(Math.random() * 6);
          gradeCount['Grade 12'] = totalStudents - gradeCount['Grade 9'] - gradeCount['Grade 10'] - gradeCount['Grade 11'];
        } else {
          // Use default data if no students found
          gradeCount['Grade 9'] = 120;
          gradeCount['Grade 10'] = 115;
          gradeCount['Grade 11'] = 105;
          gradeCount['Grade 12'] = 95;
        }
      }

      // Update enrollment data
      setEnrollmentData({
        labels: Object.keys(gradeCount),
        datasets: [{
          data: sanitizeChartData(Object.values(gradeCount)),
          color: (opacity = 1) => `rgba(33, 150, 243, ${opacity})`,
          strokeWidth: 2
        }],
        legend: ['Students per Grade']
      });
    } catch (error) {
      console.error('Error fetching enrollment data:', error);
      // Use default data on error
      setEnrollmentData(defaultEnrollmentData);
    }
  };

  // Fetch performance data from Firestore
  const fetchPerformanceData = async () => {
    try {
      // Query grades collection
      const gradesRef = collection(db, 'grades');
      const gradesSnapshot = await getDocs(gradesRef);

      // Calculate average performance by quarter
      const quarterPerformance = {
        'Q1': [],
        'Q2': [],
        'Q3': [],
        'Q4': []
      };

      gradesSnapshot.forEach(doc => {
        const grade = doc.data();
        if (grade.quarter && grade.score) {
          const quarter = `Q${grade.quarter}`;
          if (quarterPerformance.hasOwnProperty(quarter)) {
            quarterPerformance[quarter].push(grade.score);
          }
        }
      });

      // Calculate averages
      const averages = Object.keys(quarterPerformance).map(quarter => {
        const scores = quarterPerformance[quarter];
        if (scores.length > 0) {
          return scores.reduce((sum, score) => sum + score, 0) / scores.length;
        }
        return 0;
      });

      // If no data found, use some realistic data
      if (averages.every(avg => avg === 0)) {
        // Use default data
        setPerformanceTrendData({
          labels: ['Q1', 'Q2', 'Q3', 'Q4'],
          datasets: [{
            data: sanitizeChartData([78, 82, 85, 88]),
            color: (opacity = 1) => `rgba(76, 175, 80, ${opacity})`,
            strokeWidth: 2
          }],
          legend: ['Average Performance']
        });
        return;
      }

      // Update performance data
      setPerformanceTrendData({
        labels: Object.keys(quarterPerformance),
        datasets: [{
          data: sanitizeChartData(averages),
          color: (opacity = 1) => `rgba(76, 175, 80, ${opacity})`,
          strokeWidth: 2
        }],
        legend: ['Average Performance']
      });
    } catch (error) {
      console.error('Error fetching performance data:', error);
      // Use default data on error
      setPerformanceTrendData(defaultPerformanceTrendData);
    }
  };

  // Fetch resource allocation data from Firestore
  const fetchResourceData = async () => {
    try {
      // Query resources collection
      const resourcesRef = collection(db, 'resources');
      const resourcesSnapshot = await getDocs(resourcesRef);

      // Calculate resource allocation by category
      const categoryAllocation = {
        'Academic': 0,
        'Administrative': 0,
        'Infrastructure': 0,
        'Other': 0
      };

      resourcesSnapshot.forEach(doc => {
        const resource = doc.data();
        if (resource.category && resource.allocation) {
          if (categoryAllocation.hasOwnProperty(resource.category)) {
            categoryAllocation[resource.category] += resource.allocation;
          } else {
            categoryAllocation['Other'] += resource.allocation;
          }
        }
      });

      // If no data found, use some realistic data
      if (Object.values(categoryAllocation).every(allocation => allocation === 0)) {
        // Use default data
        setResourceAllocationData([
          {
            name: 'Academic',
            value: 45,
            color: '#2196f3',
            legendFontColor: '#7F7F7F',
            legendFontSize: 12
          },
          {
            name: 'Administrative',
            value: 25,
            color: '#4caf50',
            legendFontColor: '#7F7F7F',
            legendFontSize: 12
          },
          {
            name: 'Infrastructure',
            value: 20,
            color: '#ff9800',
            legendFontColor: '#7F7F7F',
            legendFontSize: 12
          },
          {
            name: 'Other',
            value: 10,
            color: '#f44336',
            legendFontColor: '#7F7F7F',
            legendFontSize: 12
          }
        ]);
        return;
      }

      // Update resource allocation data
      const newResourceData = Object.keys(categoryAllocation).map((category, index) => {
        const colors = ['#2196f3', '#4caf50', '#ff9800', '#f44336'];

        return {
          name: category,
          value: categoryAllocation[category],
          color: colors[index % colors.length],
          legendFontColor: '#7F7F7F',
          legendFontSize: 12
        };
      });

      setResourceAllocationData(newResourceData);
    } catch (error) {
      console.error('Error fetching resource data:', error);
      // Use default data on error
      setResourceAllocationData(defaultResourceAllocationData);
    }
  };

  useEffect(() => {
    fetchRecentActivities();
    // Removed auto-refresh interval that was causing disturbance
  }, []);

  const handleActivityPress = async (activity) => {
    try {
      // Navigate to activity detail screen
      navigation.navigate('ActivityDetailScreen', { activityId: activity.id });
    } catch (error) {
      console.error('Error handling activity press:', error);
    }
  };

  const renderActivities = () => (
    <Animatable.View
      animation="fadeInUp"
      duration={800}
      delay={400}
    >
      <Surface style={styles.card}>
        <Card.Content>
          <View style={styles.cardHeader}>
            <View style={styles.cardHeaderLeft}>
              <MaterialCommunityIcons
                name="bell-ring-outline"
                size={24}
                color={'#1976d2'}
                style={styles.headerIcon}
              />
              <PaperText style={styles.sectionTitle}>{translate('admin.dashboard.recentActivities')}</PaperText>
            </View>
            <Button
              mode="text"
              compact
              onPress={() => navigation.navigate('ActivityManagement')}
              icon="chevron-right"
              contentStyle={styles.viewAllButtonContent}
            >
              {translate('common.viewAll')}
            </Button>
          </View>

          <Divider style={styles.sectionDivider} />

          {activitiesLoading ? (
            <View style={styles.loaderContainer}>
              <ActivityIndicator style={styles.loader} size="small" color={'#1976d2'} />
              <Text style={styles.loaderText}>{translate('common.loading')}</Text>
            </View>
          ) : recentActivities.length > 0 ? (
            <List.Section style={styles.activitiesList}>
              {recentActivities.map((activity, index) => (
                <Animatable.View
                  key={activity.id}
                  animation="fadeInRight"
                  duration={500}
                  delay={index * 100}
                >
                  <TouchableRipple
                    onPress={() => handleActivityPress(activity)}
                    style={[styles.activityItem, !activity.read && styles.unreadActivity]}
                    rippleColor="rgba(0, 0, 0, 0.05)"
                  >
                    <View style={styles.activityItemContent}>
                      <View style={[styles.activityIconContainer, { backgroundColor: getActivityColor(activity.type) + '20' }]}>
                        <MaterialCommunityIcons
                          name={getActivityIcon(activity.type)}
                          size={24}
                          color={getActivityColor(activity.type)}
                        />
                      </View>
                      <View style={styles.activityTextContainer}>
                        <View style={styles.activityHeader}>
                          <Text style={styles.activityTitle} numberOfLines={1}>{activity.title}</Text>
                          {!activity.read && <View style={styles.unreadDot} />}
                        </View>
                        <Text style={styles.activityDescription} numberOfLines={2}>
                          {activity.description}
                        </Text>
                        <View style={styles.activityFooter}>
                          <Text style={styles.activityTimestamp}>
                            <MaterialCommunityIcons name="clock-outline" size={12} color={'#9E9E9E'} />
                            {' '}{activity.relativeTime || (typeof activity.timestamp === 'string' ? activity.timestamp : getRelativeTime(activity.timestamp))}
                          </Text>
                          {activity.userId && activity.userId !== 'system' && (
                            <Text style={styles.activityUser}>
                              <MaterialCommunityIcons name="account" size={12} color={'#9E9E9E'} />
                              {' '}{activity.userId}
                            </Text>
                          )}
                        </View>
                      </View>
                      <IconButton
                        icon="chevron-right"
                        size={20}
                        color={'#9E9E9E'}
                        onPress={() => handleActivityPress(activity)}
                        style={styles.activityArrow}
                      />
                    </View>
                  </TouchableRipple>
                  {index < recentActivities.length - 1 && <Divider style={styles.activityDivider} />}
                </Animatable.View>
              ))}
            </List.Section>
          ) : (
            <View style={styles.emptyState}>
              <MaterialCommunityIcons
                name="bell-off-outline"
                size={48}
                color="#757575"
              />
              <PaperText style={styles.emptyStateText}>
                {translate('admin.dashboard.noActivities')}
              </PaperText>
            </View>
          )}
        </Card.Content>
      </Surface>
    </Animatable.View>
  );

  const filterMenuItems = (items, query) => {
    if (!query) return items;
    return items.filter(item =>
      translate(item.title).toLowerCase().includes(query.toLowerCase()) ||
      item.route.toLowerCase().includes(query.toLowerCase())
    );
  };

  const getAllMenuItems = () => {
    return adminMenuItems.reduce((acc, category) => {
      return [...acc, ...category.items.map(item => ({
        ...item,
        category: category.category
      }))];
    }, []);
  };

  const renderSearchResults = () => {
    const allItems = getAllMenuItems();
    const filtered = filterMenuItems(allItems, searchQuery);

    return (
      <Card style={styles.searchResultsCard}>
        <Card.Content>
          <PaperText style={styles.searchResultsTitle}>
            {translate('admin.dashboard.searchResults')} ({filtered.length})
          </PaperText>
          <View style={styles.searchResultsGrid}>
            {filtered.map((item, index) => (
              <TouchableOpacity
                key={index}
                style={styles.searchResultItem}
                onPress={() => navigation.navigate(item.route)}
              >
                <Avatar.Icon
                  size={40}
                  icon={item.icon}
                  style={{ backgroundColor: getRandomColor(index) }}
                />
                <Text style={styles.menuText}>{translate(item.title)}</Text>
                <Text style={styles.categoryText}>{item.category}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </Card.Content>
      </Card>
    );
  };

  const renderAdminMenuSection = (category) => (
    <Animatable.View
      key={category.category}
      animation="fadeInUp"
      duration={800}
      delay={200}
    >
      <Surface style={styles.sectionCard}>
      <Card.Content>
        <PaperText style={styles.sectionTitle}>{category.category}</PaperText>
        <View style={styles.menuGrid}>
          {category.items.map((item, index) => (
              <Animatable.View
              key={index}
                animation="fadeIn"
                duration={500}
                delay={index * 100}
              >
                <TouchableOpacity
              style={styles.menuItem}
              onPress={() => navigation.navigate(item.route)}
              activeOpacity={0.7}
              delayPressIn={0}
                >
                  <LinearGradient
                    colors={[getRandomColor(index), getRandomColor(index + 1)]}
                    style={styles.menuIconContainer}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 1 }}
            >
              <Avatar.Icon
                size={40}
                icon={item.icon}
                      style={styles.menuIcon}
              />
                  </LinearGradient>
              <Text style={styles.menuText}>{translate(item.title)}</Text>
            </TouchableOpacity>
              </Animatable.View>
          ))}
        </View>
      </Card.Content>
      </Surface>
    </Animatable.View>
  );

  const getRandomColor = (index) => {
    const colors = ['#2196f3', '#4caf50', '#ff9800', '#f44336', '#9c27b0', '#00bcd4'];
    return colors[index % colors.length];
  };

  const showMoreButton = (
    <Button onPress={() => setShowAllItems(!showAllItems)}>
      {showAllItems ? translate('admin.dashboard.showLess') : translate('admin.dashboard.showMore')}
    </Button>
  );

  // Use the admin sidebar context
  const {
    activeSidebarItem,
    setActiveSidebarItem
  } = useAdminSidebar();

  return (
    <AdminScreenWrapper
      title={translate('admin.navigation.adminDashboard')}
      showNotification={true}
    >
      {/* Main Content */}
      <View style={styles.mainContainer}>
        <ScrollView style={styles.content}>
          <Animatable.View
            animation="fadeInDown"
            duration={800}
            delay={300}
            style={styles.welcomeCard}
          >
            <LinearGradient
              colors={['#1976d215', '#f5f5f5']}
              style={styles.welcomeGradient}
            >
              <PaperText style={styles.welcomeTitle}>
                {translate('admin.dashboard.welcome', { name: user?.displayName || translate('admin.dashboard.administrator') })}
              </PaperText>
              <PaperText style={styles.welcomeSubtitle}>
                {new Date().toLocaleDateString(language === 'en' ? 'en-US' : language === 'am' ? 'am-ET' : 'om-ET', {
                  weekday: 'long',
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                })}
              </PaperText>
            </LinearGradient>
          </Animatable.View>

          {/* Quick Stats */}
          {renderQuickStats()}

          {/* Main Dashboard Content */}
          <Animatable.View
            animation="fadeInUp"
            duration={800}
            delay={500}
            style={styles.dashboardContent}
          >
            <View style={styles.tabButtons}>
              {['enrollment', 'performance', 'resources'].map((tab, index) => (
                <Animatable.View
                  key={index}
                  animation="fadeInUp"
                  duration={500}
                  delay={index * 100}
                >
                  <Button
                    mode={currentTab === index ? 'contained' : 'outlined'}
                    onPress={() => setCurrentTab(index)}
                    style={styles.tabButton}
                    labelStyle={styles.tabButtonLabel}
                  >
                    {translate(`admin.dashboard.${tab}`)}
                  </Button>
                </Animatable.View>
              ))}
            </View>

            {isLoading ? (
              <ActivityIndicator style={styles.loader} size="large" />
            ) : (
              <>
                {currentTab === 0 && renderEnrollmentSection()}
                {currentTab === 1 && renderPerformanceSection()}
                {currentTab === 2 && renderResourceSection()}
              </>
            )}

          </Animatable.View>

          {/* Quick Access */}
          <Animatable.View
            animation="fadeInUp"
            duration={800}
            delay={600}
            style={styles.quickAccessContainer}
          >
            <View style={styles.sectionHeader}>
              <PaperText style={styles.sectionTitle}>{translate('admin.dashboard.quickAccess')}</PaperText>
              <Button
                mode="text"
                compact
                onPress={() => toggleDrawer()}
                style={styles.viewAllButton}
                labelStyle={styles.viewAllButtonLabel}
              >
                {translate('common.viewAll')}
              </Button>
            </View>

            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              style={styles.quickAccessScroll}
              contentContainerStyle={styles.quickAccessContent}
            >
              {adminMenuItems.flatMap(section =>
                section.items.slice(0, 2).map((item, index) => (
                  <Animatable.View
                    key={`${section.category}-${index}`}
                    animation="fadeIn"
                    duration={500}
                    delay={700 + (index * 100)}
                  >
                    <TouchableOpacity
                      activeOpacity={0.7}
                      onPress={() => navigation.navigate(item.route)}
                    >
                      <Surface style={styles.quickAccessCard}>
                        <LinearGradient
                          colors={[getCategoryColor(index) + '20', getCategoryColor(index) + '10']}
                          style={styles.quickAccessGradient}
                        >
                          <IconButton
                            icon={item.icon}
                            size={32}
                            color={getCategoryColor(index)}
                            style={styles.quickAccessIcon}
                          />
                          <PaperText style={[styles.quickAccessTitle, { color: getCategoryColor(index) }]}>
                            {item.title}
                          </PaperText>
                        </LinearGradient>
                      </Surface>
                    </TouchableOpacity>
                  </Animatable.View>
                ))
              )}
            </ScrollView>
          </Animatable.View>

          {/* Recent Activities */}
          {renderActivities()}
        </ScrollView>
      </View>

      <FAB
        style={styles.fab}
        icon="plus"
        color="white"
        onPress={() => {}}
        actions={[
          {
            icon: 'message',
            label: translate('admin.actions.newMessage'),
            onPress: () => navigation.navigate('CommunicationCenter'),
          },
          {
            icon: 'bell',
            label: translate('admin.actions.newAnnouncement'),
            onPress: () => navigation.navigate('AnnouncementManagement'),
          },
          {
            icon: 'account-plus',
            label: translate('admin.actions.addUser'),
            onPress: () => navigation.navigate('UserManagement'),
          }
        ]}
      />

      <Portal>
        <Dialog
          visible={logoutDialogVisible}
          onDismiss={() => setLogoutDialogVisible(false)}
          style={styles.dialog}
        >
          <LinearGradient
            colors={['#1976d2', '#e3f2fd']}
            style={styles.dialogHeader}
          >
            <Dialog.Title style={styles.dialogTitle}>{translate('admin.actions.logout')}</Dialog.Title>
          </LinearGradient>
          <Dialog.Content style={styles.dialogContent}>
            <Animatable.View animation={logoutDialogVisible ? 'fadeIn' : 'fadeOut'} duration={300}>
              <PaperText style={styles.dialogText}>{translate('admin.messages.logoutConfirmation')}</PaperText>
            </Animatable.View>
          </Dialog.Content>
          <Dialog.Actions style={styles.dialogActions}>
            <Button
              mode="outlined"
              onPress={() => setLogoutDialogVisible(false)}
              style={styles.dialogButton}
            >
              {translate('admin.actions.cancel')}
            </Button>
            <Button
              mode="contained"
              onPress={handleLogout}
              style={styles.dialogButton}
            >
              {translate('admin.actions.logout')}
            </Button>
          </Dialog.Actions>
        </Dialog>

        {/* Error message snackbar */}
        <Snackbar
          visible={snackbarVisible}
          onDismiss={() => setSnackbarVisible(false)}
          action={{
            label: translate('common.dismiss'),
            onPress: () => setSnackbarVisible(false),
          }}
          duration={3000}
          style={styles.snackbar}
        >
          {errorMessage}
        </Snackbar>
      </Portal>
    </AdminScreenWrapper>
  );
};

const styles = StyleSheet.create({
  // Loading overlay
  loadingOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#2196f3',
  },

  // Snackbar
  snackbar: {
    backgroundColor: '#323232',
  },

  // Offline warning
  offlineWarning: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#ffebee',
    padding: 8,
    borderRadius: 4,
    marginBottom: 16,
  },
  offlineWarningText: {
    color: '#f44336',
    marginLeft: 8,
    fontSize: 12,
  },

  // Stats header
  statsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  statsHeaderTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  lastUpdatedText: {
    fontSize: 12,
    color: '#757575',
  },

  // Footer
  footerContainer: {
    padding: 16,
    alignItems: 'center',
    marginTop: 16,
  },
  footerText: {
    fontSize: 12,
    color: '#757575',
  },

  // New sidebar styles
  sidebar: {
    position: 'absolute',
    left: 0,
    top: 0,
    bottom: 0,
    width: 280,
    backgroundColor: 'white',
    zIndex: 1000,
    elevation: 16,
    borderTopRightRadius: 0,
    borderBottomRightRadius: 30,
    overflow: 'hidden',
    marginTop: 56, // Height of the app bar
  },
  sidebarHeader: {
    padding: 20,
    paddingTop: 40,
    paddingBottom: 20,
  },
  sidebarProfile: {
    alignItems: 'center',
  },
  sidebarAvatar: {
    marginBottom: 10,
  },
  sidebarName: {
    color: 'white',
    fontWeight: 'bold',
    marginTop: 10,
  },
  sidebarRole: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 14,
  },
  sidebarMenu: {
    flex: 1,
    paddingBottom: 20,
    width: '100%',
  },
  sidebarSubheader: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#757575',
    marginTop: 16,
    paddingLeft: 16,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  sidebarItem: {
    borderRadius: 8,
    marginHorizontal: 8,
    marginVertical: 4,
    height: 56,
    width: '100%',
    paddingLeft: 0,
    justifyContent: 'center',
  },
  sidebarItemActive: {
    backgroundColor: 'rgba(33, 150, 243, 0.1)',
  },
  sidebarItemText: {
    fontSize: 16,
    color: '#000000',
    marginLeft: 8,
    fontWeight: '500',
    flex: 1,
  },
  sidebarItemTextActive: {
    color: '#2196F3',
    fontWeight: 'bold',
  },
  sidebarDivider: {
    marginVertical: 8,
  },
  sidebarFooter: {
    padding: 16,
    paddingTop: 8,
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    zIndex: 999,
  },
  mainContainer: {
    flex: 1,
    marginLeft: Dimensions.get('window').width >= 768 ? 280 : 0,
    marginTop: 0, // No need for margin top as the app bar is outside the container
  },
  appbar: {
    elevation: 4,
    backgroundColor: '#2196F3',
    zIndex: 1,
  },
  appbarTitle: {
    color: 'white',
    fontWeight: 'bold',
  },
  appbarAction: {
    marginRight: 8,
  },
  notificationBadge: {
    position: 'absolute',
    top: 5,
    right: 5,
    backgroundColor: '#F44336',
  },
  welcomeCard: {
    marginBottom: 16,
    borderRadius: 12,
    overflow: 'hidden',
  },
  welcomeGradient: {
    padding: 20,
  },
  welcomeTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  welcomeSubtitle: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  quickAccessContainer: {
    marginTop: 16,
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  viewAllButton: {
    marginRight: -8,
  },
  viewAllButtonLabel: {
    fontSize: 12,
  },
  quickAccessScroll: {
    marginHorizontal: -16,
  },
  quickAccessContent: {
    paddingHorizontal: 16,
  },
  quickAccessCard: {
    width: 120,
    height: 120,
    borderRadius: 12,
    marginRight: 12,
    overflow: 'hidden',
    elevation: 2,
  },
  quickAccessGradient: {
    width: '100%',
    height: '100%',
    padding: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  quickAccessIcon: {
    marginBottom: 8,
  },
  quickAccessTitle: {
    fontSize: 12,
    textAlign: 'center',
    fontWeight: '500',
  },
  dialog: {
    borderRadius: 12,
    backgroundColor: 'white',
  },
  dialogHeader: {
    padding: 16,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
  },
  dialogTitle: {
    color: 'white',
    fontWeight: 'bold',
  },
  dialogContent: {
    paddingTop: 20,
  },
  dialogText: {
    fontSize: 16,
  },
  dialogActions: {
    padding: 16,
    justifyContent: 'space-between',
  },
  dialogButton: {
    minWidth: 100,
  },
  safeArea: {
    flex: 1,
    backgroundColor: '#2196F3',
  },
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  sidebarOverlay: {
    ...StyleSheet.absoluteFillObject,
    flexDirection: 'row',
    zIndex: 1000,
  },
  sidebarDismiss: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  sidebarContainer: {
    width: 280,
    height: '100%',
    backgroundColor: '#1565C0',
    elevation: 10,
    shadowColor: '#000',
    shadowOffset: { width: 5, height: 0 },
    shadowOpacity: 0.3,
    shadowRadius: 10,
    paddingVertical: 20,
    flexDirection: 'column',
  },
  sidebarHeader: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  sidebarLogo: {
    backgroundColor: '#0D47A1',
    marginBottom: 10,
  },
  sidebarTitle: {
    color: '#fff',
    fontSize: 18,
  },
  sidebarItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginVertical: 2,
  },

  sidebarItemIcon: {
    backgroundColor: 'transparent',
    marginRight: 16,
  },

  sidebarFooter: {
    marginTop: 'auto',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: 'rgba(255,255,255,0.1)',
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
  },
  logoutIcon: {
    backgroundColor: 'transparent',
    marginRight: 16,
  },
  logoutText: {
    color: '#fff',
    fontSize: 16,
  },
  mainContent: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 16,
    backgroundColor: '#2196F3',
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
    marginLeft: 8,
  },
  notificationButton: {
    marginRight: 16,
    position: 'relative',
  },
  profileButton: {
    marginRight: 16,
  },
  avatarText: {
    backgroundColor: '#0D47A1',
  },
  searchContainer: {
    flexDirection: 'row',
    margin: 16,
    alignItems: 'center',
  },
  searchBar: {
    flex: 0.85,
    elevation: 4,
    borderRadius: 8,
    marginRight: 8,
  },
  languageSelectorContainer: {
    flex: 0.15,
  },
  languageSelector: {
    width: '100%',
  },
  badge: {
    position: 'absolute',
    top: -4,
    right: -4,
    backgroundColor: '#F44336',
  },
  content: {
    flex: 1,
    padding: 16,
    paddingBottom: 80,
  },
  quickStats: {
    marginBottom: 24,
  },
  statContent: {
    padding: 16,
    alignItems: 'center',
  },
  tabButtons: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  tabButton: {
    flex: 1,
    marginHorizontal: 4,
    borderRadius: 8,
  },
  tabButtonLabel: {
    fontSize: 12,
  },
  card: {
    marginBottom: 24,
    elevation: 4,
    borderRadius: 12,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  cardHeaderLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerIcon: {
    marginRight: 8,
  },
  sectionDivider: {
    marginBottom: 16,
  },
  loaderContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 24,
  },
  loaderText: {
    marginTop: 8,
    color: '#757575',
  },
  activitiesList: {
    marginHorizontal: -16,
  },
  activityItem: {
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  activityItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  activityIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  activityTextContainer: {
    flex: 1,
  },
  activityHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  activityTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    flex: 1,
  },
  activityDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 6,
  },
  activityFooter: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  activityTimestamp: {
    fontSize: 12,
    color: '#999',
    marginRight: 16,
  },
  activityUser: {
    fontSize: 12,
    color: '#999',
  },
  activityArrow: {
    margin: 0,
  },
  activityDivider: {
    marginHorizontal: 16,
  },
  unreadDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#2196F3',
    marginLeft: 8,
  },
  viewAllButtonContent: {
    flexDirection: 'row-reverse',
  },
  chart: {
    marginVertical: 8,
    borderRadius: 16,
  },
  loader: {
    marginVertical: 32,
  },
  menuTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
  },
  sectionCard: {
    marginBottom: 16,
    elevation: 4,
    borderRadius: 12,
  },
  sectionTitle: {
    fontSize: 18,
    marginBottom: 16,
  },
  menuGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'flex-start',
    marginHorizontal: -4,
  },
  menuItem: {
    width: '33.33%',
    padding: 8,
    alignItems: 'center',
    ...Platform.select({
      android: {
        elevation: 2,
        backgroundColor: '#fff',
        margin: 4,
        borderRadius: 8,
        paddingVertical: 12,
      },
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.2,
        shadowRadius: 2,
        backgroundColor: '#fff',
        margin: 4,
        borderRadius: 8,
        paddingVertical: 12,
      },
    }),
  },
  menuIconContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  menuIcon: {
    backgroundColor: 'transparent',
  },
  menuText: {
    fontSize: 13,
    textAlign: 'center',
    color: '#333',
    marginTop: 4,
    fontWeight: '500',
  },
  searchResultsCard: {
    marginBottom: 16,
    elevation: 4,
    borderRadius: 12,
  },
  searchResultsTitle: {
    fontSize: 18,
    marginBottom: 16,
  },
  searchResultsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'flex-start',
    marginHorizontal: -8,
  },
  searchResultItem: {
    width: '50%',
    padding: 12,
    alignItems: 'center',
    ...Platform.select({
      android: {
        elevation: 2,
        backgroundColor: '#fff',
        margin: 4,
        borderRadius: 8,
        paddingVertical: 16,
      },
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.2,
        shadowRadius: 2,
        backgroundColor: '#fff',
        margin: 4,
        borderRadius: 8,
        paddingVertical: 16,
      },
    }),
  },
  categoryText: {
    fontSize: 10,
    color: '#666',
    marginTop: 2,
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
    backgroundColor: '#2196F3',
  },
  logoutDialog: {
    borderRadius: 12,
  },
  headerGradient: {
    flex: 1,
  },
  statGradient: {
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  statCard: {
    width: screenWidth * 0.35,
    marginRight: 16,
    borderRadius: 8,
    elevation: 4,
    overflow: 'hidden',
  },
  statValue: {
    fontSize: 28,
    fontWeight: 'bold',
    marginTop: 10,
    color: '#fff',
  },
  statTitle: {
    fontSize: 14,
    textAlign: 'center',
    marginTop: 5,
    color: '#fff',
  },
  menuIconContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  menuIcon: {
    backgroundColor: 'transparent',
  },
  unreadActivity: {
    backgroundColor: 'rgba(33, 150, 243, 0.05)',
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 24,
  },
  emptyStateText: {
    textAlign: 'center',
    color: '#757575',
    marginTop: 8,
  },
});

export default AdminDashboard;
