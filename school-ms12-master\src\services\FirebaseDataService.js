import {
  db,
  storage,
  collection,
  doc,
  addDoc,
  getDoc,
  getDocs,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit,
} from '../config/firebase';
import {
  startAfter,
  serverTimestamp,
  writeBatch,
} from '@firebase/firestore';
import {
  ref,
  uploadBytes,
  getDownloadURL,
  deleteObject,
} from '@firebase/storage';
import CloudinaryService from './CloudinaryService';

class FirebaseDataService {
  // Collection Operations
  static async addDocument(collectionName, data) {
    try {
      const docRef = await addDoc(collection(db, collectionName), {
        ...data,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      });
      return { id: docRef.id, ...data };
    } catch (error) {
      throw new Error(`Failed to add document to ${collectionName}: ${error.message}`);
    }
  }

  static async getDocument(collectionName, documentId) {
    try {
      const docRef = doc(db, collectionName, documentId);
      const docSnap = await getDoc(docRef);
      if (!docSnap.exists()) throw new Error('Document not found');
      return { id: docSnap.id, ...docSnap.data() };
    } catch (error) {
      throw new Error(`Failed to get document from ${collectionName}: ${error.message}`);
    }
  }

  static async updateDocument(collectionName, documentId, data) {
    try {
      const docRef = doc(db, collectionName, documentId);
      await updateDoc(docRef, {
        ...data,
        updatedAt: serverTimestamp(),
      });
      return { id: documentId, ...data };
    } catch (error) {
      throw new Error(`Failed to update document in ${collectionName}: ${error.message}`);
    }
  }

  static async deleteDocument(collectionName, documentId) {
    try {
      await deleteDoc(doc(db, collectionName, documentId));
      return true;
    } catch (error) {
      throw new Error(`Failed to delete document from ${collectionName}: ${error.message}`);
    }
  }

  // Query Operations
  static async queryDocuments(collectionName, constraints = [], orderByField = 'createdAt', orderDirection = 'desc', limitCount = 50, startAfterDoc = null) {
    try {
      let queryConstraints = [...constraints];
      if (orderByField) {
        queryConstraints.push(orderBy(orderByField, orderDirection));
      }
      if (limitCount) {
        queryConstraints.push(limit(limitCount));
      }
      if (startAfterDoc) {
        queryConstraints.push(startAfter(startAfterDoc));
      }

      const q = query(collection(db, collectionName), ...queryConstraints);
      const querySnapshot = await getDocs(q);

      const documents = [];
      querySnapshot.forEach((doc) => {
        documents.push({ id: doc.id, ...doc.data() });
      });

      return documents;
    } catch (error) {
      throw new Error(`Failed to query documents from ${collectionName}: ${error.message}`);
    }
  }

  // Batch Operations
  static async batchOperation(operations) {
    try {
      const batch = writeBatch(db);

      operations.forEach(operation => {
        const docRef = doc(db, operation.collection, operation.id);
        switch (operation.type) {
          case 'set':
            batch.set(docRef, { ...operation.data, updatedAt: serverTimestamp() });
            break;
          case 'update':
            batch.update(docRef, { ...operation.data, updatedAt: serverTimestamp() });
            break;
          case 'delete':
            batch.delete(docRef);
            break;
          default:
            throw new Error(`Invalid operation type: ${operation.type}`);
        }
      });

      await batch.commit();
      return true;
    } catch (error) {
      throw new Error(`Batch operation failed: ${error.message}`);
    }
  }

  // File Storage Operations (using Cloudinary)
  static async uploadFile(path, file) {
    try {
      // Extract folder from path
      const folder = path.split('/')[0] || 'general';

      // Use CloudinaryService for upload
      const result = await CloudinaryService.uploadFile(file, {
        folder: folder,
        publicId: path.replace(/\//g, '_')
      });

      return {
        path,
        downloadURL: result.url,
        publicId: result.publicId
      };
    } catch (error) {
      throw new Error(`Failed to upload file: ${error.message}`);
    }
  }

  static async deleteFile(path) {
    try {
      // Check if path is a Cloudinary public ID
      if (path.includes('/')) {
        // Convert path to Cloudinary public ID format
        const publicId = path.replace(/\//g, '_');
        await CloudinaryService.deleteAsset(publicId);
      } else {
        // Assume it's already a Cloudinary public ID
        await CloudinaryService.deleteAsset(path);
      }
      return true;
    } catch (error) {
      throw new Error(`Failed to delete file: ${error.message}`);
    }
  }

  // Role-specific Operations
  static async getTeacherClasses(teacherId) {
    return this.queryDocuments('classes', [where('teacherId', '==', teacherId)]);
  }

  static async getStudentsByClass(classId) {
    return this.queryDocuments('students', [where('classId', '==', classId)]);
  }

  static async getParentsByClass(classId) {
    return this.queryDocuments('parents', [where('classId', '==', classId)]);
  }

  static async getStudentGrades(studentId) {
    return this.queryDocuments('grades', [where('studentId', '==', studentId)]);
  }

  static async getStudentAttendance(studentId) {
    return this.queryDocuments('attendance', [where('studentId', '==', studentId)]);
  }

  // Communication Operations
  static async sendMessage(message) {
    return this.addDocument('messages', {
      ...message,
      status: 'sent',
      sentAt: serverTimestamp(),
    });
  }

  static async getMessages(userId, role) {
    const sent = await this.queryDocuments('messages', [
      where('senderId', '==', userId),
      where('senderRole', '==', role),
    ]);
    const received = await this.queryDocuments('messages', [
      where('recipientId', '==', userId),
      where('recipientRole', '==', role),
    ]);
    return { sent, received };
  }

  static async createAnnouncement(announcement) {
    return this.addDocument('announcements', {
      ...announcement,
      status: 'active',
      createdAt: serverTimestamp(),
    });
  }

  static async getAnnouncements(filters = []) {
    return this.queryDocuments('announcements', [
      where('status', '==', 'active'),
      ...filters,
    ]);
  }
}

export default FirebaseDataService;
