import React, { useState, useEffect } from 'react';
import { View, StyleSheet } from 'react-native';
import { Card, Title, IconButton, ActivityIndicator, Text, Button } from 'react-native-paper';
import { useLanguage } from '../../context/LanguageContext';
import { db } from '../../config/firebase';
import { collection, query, where, getDocs, onSnapshot, doc, getDoc } from 'firebase/firestore';
import OngoingClassTracker from './OngoingClassTracker';
import * as Animatable from 'react-native-animatable';

const DashboardClassWidget = ({
  role,
  userId,
  classId,
  section,
  childId,
  onPressViewSchedule,
  style
}) => {
  const { translate } = useLanguage();
  const [schedule, setSchedule] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchSchedule();
  }, [role, userId, classId, section, childId]);

  const fetchSchedule = async () => {
    try {
      setLoading(true);
      setError(null);

      let scheduleQuery;
      const timetablesRef = collection(db, 'timetables');

      switch (role) {
        case 'teacher':
          // For teachers, get all classes they teach
          scheduleQuery = query(
            timetablesRef,
            where('teacherId', '==', userId),
            where('published', '==', true)
          );
          break;

        case 'student':
          // For students, get their class schedule
          if (!classId || !section) {
            console.log('Student missing classId or section:', { classId, section });
            setError('Your class information is incomplete. Please contact your administrator.');
            setLoading(false);
            return null;
          }

          // Verify that the class exists
          try {
            const classRef = doc(db, 'classes', classId);
            const classDoc = await getDoc(classRef);

            if (!classDoc.exists()) {
              console.log(`Class with ID ${classId} does not exist`);
              setError('Your assigned class does not exist. Please contact your administrator.');
              setLoading(false);
              return null;
            }

            console.log('Class found:', classDoc.data());
          } catch (error) {
            console.error('Error verifying class:', error);
            // Continue anyway, as the class might exist but we just can't verify it
          }

          scheduleQuery = query(
            timetablesRef,
            where('classId', '==', classId),
            where('sectionName', '==', section),
            where('published', '==', true)
          );
          break;

        case 'parent':
          if (childId) {
            try {
              // First get the child's class and section
              const studentsRef = collection(db, 'students');
              const studentQuery = query(studentsRef, where('id', '==', childId));
              const studentSnapshot = await getDocs(studentQuery);

              if (!studentSnapshot.empty) {
                const studentData = studentSnapshot.docs[0].data();

                if (!studentData.classId || !studentData.section) {
                  throw new Error('Child class information is incomplete');
                }

                // Then get the schedule for that class and section
                scheduleQuery = query(
                  timetablesRef,
                  where('classId', '==', studentData.classId),
                  where('sectionName', '==', studentData.section),
                  where('published', '==', true)
                );
              } else {
                throw new Error('Child information not found');
              }
            } catch (error) {
              console.error('Error fetching child data:', error);
              setError('Failed to load child information');
              setLoading(false);
              return;
            }
          } else {
            throw new Error('Child ID is required for parent role');
          }
          break;

        default:
          throw new Error('Invalid role specified');
      }

      // Set up real-time listener for schedule updates
      try {
        const unsubscribe = onSnapshot(scheduleQuery, (snapshot) => {
          const scheduleData = [];
          snapshot.forEach((doc) => {
            scheduleData.push({ id: doc.id, ...doc.data() });
          });

          setSchedule(scheduleData);
          setLoading(false);
        }, (error) => {
          console.error('Error fetching schedule:', error);
          setError(`Failed to load schedule: ${error.message}`);
          setLoading(false);
        });

        // Return unsubscribe function for cleanup
        return unsubscribe;
      } catch (error) {
        console.error('Error setting up schedule listener:', error);
        setError(`Failed to load schedule: ${error.message}`);
        setLoading(false);
      }
    } catch (error) {
      console.error('Error setting up schedule listener:', error);
      setError(`Failed to load schedule: ${error.message}`);
      setLoading(false);
    }
  };

  // Render loading state
  const renderLoading = () => (
    <View style={styles.loadingContainer}>
      <ActivityIndicator size="small" />
      <Text style={styles.loadingText}>{translate('common.loading') || 'Loading...'}</Text>
    </View>
  );

  // Retry fetching schedule
  const handleRetry = () => {
    setError(null);
    fetchSchedule();
  };

  // Render error state
  const renderError = () => (
    <View style={styles.errorContainer}>
      <IconButton icon="alert-circle" size={24} color="#F44336" />
      <Text style={styles.errorText}>{error}</Text>
      <Button
        mode="contained"
        onPress={handleRetry}
        style={styles.retryButton}
        icon="refresh"
      >
        {translate('common.retry') || 'Retry'}
      </Button>
    </View>
  );

  return (
    <Card style={[styles.container, style]}>
      <Card.Content>
        <View style={styles.headerContainer}>
          <Title style={styles.title}>
            {role === 'parent'
              ? translate('dashboard.childClassStatus') || "Child's Class Status"
              : translate('dashboard.classStatus') || 'Class Status'
            }
          </Title>
          <IconButton
            icon="refresh"
            size={20}
            onPress={fetchSchedule}
            disabled={loading}
          />
        </View>

        {loading ? (
          renderLoading()
        ) : error ? (
          renderError()
        ) : schedule.length === 0 ? (
          <View style={styles.noScheduleContainer}>
            <IconButton icon="calendar-blank" size={32} color="#9E9E9E" />
            <Text style={styles.noScheduleText}>
              {translate('schedule.noScheduleFound') || 'No schedule found'}
            </Text>
            <Text style={styles.noScheduleSubText}>
              {role === 'teacher'
                ? (translate('schedule.teacherNoSchedule') || 'You have no classes scheduled at this time')
                : role === 'student'
                ? (translate('schedule.studentNoSchedule') || 'You have no classes scheduled at this time')
                : (translate('schedule.parentNoSchedule') || 'Your child has no classes scheduled at this time')
              }
            </Text>
            <Button
              mode="outlined"
              onPress={onPressViewSchedule}
              style={styles.viewScheduleButton}
              icon="calendar"
            >
              {translate('schedule.viewFullSchedule') || 'View Full Schedule'}
            </Button>
          </View>
        ) : (
          <OngoingClassTracker
            schedule={schedule}
            role={role}
            classId={classId}
            section={section}
            teacherId={userId}
            onPressViewSchedule={onPressViewSchedule}
          />
        )}
      </Card.Content>
    </Card>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
    borderRadius: 8,
    elevation: 2,
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  loadingContainer: {
    padding: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    marginTop: 8,
    color: '#757575',
  },
  errorContainer: {
    padding: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  errorText: {
    marginTop: 8,
    marginBottom: 16,
    color: '#F44336',
    textAlign: 'center',
  },
  retryButton: {
    marginTop: 8,
    backgroundColor: '#2196F3',
  },
  noScheduleContainer: {
    padding: 16,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 200,
  },
  noScheduleText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#616161',
    marginTop: 8,
    textAlign: 'center',
  },
  noScheduleSubText: {
    fontSize: 14,
    color: '#9E9E9E',
    marginTop: 8,
    marginBottom: 16,
    textAlign: 'center',
  },
  viewScheduleButton: {
    marginTop: 16,
  },
});

export default DashboardClassWidget;
