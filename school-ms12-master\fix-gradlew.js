const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Ensure the gradlew file is executable
try {
  console.log('Making gradlew executable...');
  if (process.platform === 'win32') {
    // On Windows, we don't need to change permissions
    console.log('Windows detected, skipping chmod');
  } else {
    execSync('chmod +x ./android/gradlew');
    console.log('Made gradlew executable');
  }
} catch (error) {
  console.error('Error making gradlew executable:', error);
}

// Create a symbolic link to the gradlew file in the expected location for EAS
try {
  console.log('Creating symbolic link for gradlew...');
  
  // Ensure the build/android directory exists
  const buildDir = path.join(__dirname, 'build');
  const androidBuildDir = path.join(buildDir, 'android');
  
  if (!fs.existsSync(buildDir)) {
    fs.mkdirSync(buildDir);
    console.log('Created build directory');
  }
  
  if (!fs.existsSync(androidBuildDir)) {
    fs.mkdirSync(androidBuildDir);
    console.log('Created build/android directory');
  }
  
  // Copy gradlew files to the build/android directory
  const gradlewSource = path.join(__dirname, 'android', 'gradlew');
  const gradlewDest = path.join(androidBuildDir, 'gradlew');
  
  fs.copyFileSync(gradlewSource, gradlewDest);
  console.log('Copied gradlew to build/android directory');
  
  // Copy gradlew.bat if it exists
  const gradlewBatSource = path.join(__dirname, 'android', 'gradlew.bat');
  const gradlewBatDest = path.join(androidBuildDir, 'gradlew.bat');
  
  if (fs.existsSync(gradlewBatSource)) {
    fs.copyFileSync(gradlewBatSource, gradlewBatDest);
    console.log('Copied gradlew.bat to build/android directory');
  }
  
  console.log('Successfully prepared gradlew files for EAS build');
} catch (error) {
  console.error('Error creating symbolic link:', error);
}
