import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  TouchableOpacity,
} from 'react-native';
import {
  Card,
  Title,
  Text,
  TextInput,
  IconButton,
  Avatar,
  Badge,
  Menu,
  Portal,
  Modal,
  ActivityIndicator,
  useTheme,
  Chip,
} from 'react-native-paper';
import * as ImagePicker from 'expo-image-picker';
import * as DocumentPicker from 'expo-document-picker';
import { storage } from '../../config/firebase';
import { ref, uploadBytes, getDownloadURL } from 'firebase/storage';
import MessagingService from '../../utils/MessagingService';
import EthiopianCalendar from '../../utils/EthiopianCalendar';
import { useAuth } from '../../context/AuthContext';

const MessageBubble = ({ message, isOwnMessage, onLongPress }) => {
  // No theme needed
  
  return (
    <TouchableOpacity
      onLongPress={() => onLongPress(message)}
      style={[
        styles.messageBubble,
        isOwnMessage ? styles.ownMessage : styles.otherMessage,
        { backgroundColor: isOwnMessage ? '#1976d2' : '#e0e0e0' }
      ]}
    >
      {message.type === 'image' && (
        <Image
          source={{ uri: message.content }}
          style={styles.messageImage}
          resizeMode="contain"
        />
      )}
      {message.type === 'file' && (
        <View style={styles.fileContainer}>
          <IconButton icon="file" size={24} />
          <Text style={styles.fileName}>{message.content.name}</Text>
        </View>
      )}
      {message.type === 'text' && (
        <Text style={[
          styles.messageText,
          { color: isOwnMessage ? 'white' : 'black' }
        ]}>
          {message.content}
        </Text>
      )}
      <Text style={[
        styles.messageTime,
        { color: isOwnMessage ? 'rgba(255,255,255,0.7)' : 'rgba(0,0,0,0.5)' }
      ]}>
        {EthiopianCalendar.formatTime(message.timestamp)}
      </Text>
      {Object.keys(message.reactions).length > 0 && (
        <View style={styles.reactionsContainer}>
          {Object.entries(message.reactions).map(([reaction, users]) => (
            <Chip key={reaction} style={styles.reactionChip}>
              {reaction} {users.length}
            </Chip>
          ))}
        </View>
      )}
    </TouchableOpacity>
  );
};

const Messaging = ({ route }) => {
  const { chatId, recipientId } = route.params;
  const { user } = useAuth();
  // No theme needed
  const scrollViewRef = useRef();
  
  const [messages, setMessages] = useState([]);
  const [messageText, setMessageText] = useState('');
  const [loading, setLoading] = useState(true);
  const [menuVisible, setMenuVisible] = useState(false);
  const [selectedMessage, setSelectedMessage] = useState(null);
  const [attachmentMenuVisible, setAttachmentMenuVisible] = useState(false);
  const [recipientDetails, setRecipientDetails] = useState(null);

  useEffect(() => {
    let chatSubscription;

    const initializeChat = async () => {
      try {
        setLoading(true);
        
        // Create chat if it doesn't exist
        if (!chatId) {
          const newChatId = await MessagingService.createChat([user.uid, recipientId]);
          route.params.chatId = newChatId;
        }

        // Subscribe to messages
        chatSubscription = MessagingService.subscribeToChat(
          chatId || route.params.chatId,
          handleNewMessages
        );

        // Get recipient details
        const recipientDoc = await getDoc(doc(db, 'users', recipientId));
        setRecipientDetails(recipientDoc.data());
      } catch (error) {
        console.error('Error initializing chat:', error);
      } finally {
        setLoading(false);
      }
    };

    initializeChat();

    return () => {
      if (chatSubscription) {
        MessagingService.unsubscribeFromChat(chatId);
      }
    };
  }, [chatId, recipientId]);

  const handleNewMessages = (newMessages) => {
    setMessages(newMessages);
    // Mark new messages as read
    newMessages.forEach(message => {
      if (!message.readBy.includes(user.uid)) {
        MessagingService.markMessageAsRead(message.id);
      }
    });
    // Scroll to bottom
    scrollViewRef.current?.scrollToEnd({ animated: true });
  };

  const handleSendMessage = async () => {
    if (!messageText.trim()) return;

    try {
      await MessagingService.sendMessage(
        chatId || route.params.chatId,
        messageText.trim()
      );
      setMessageText('');
    } catch (error) {
      console.error('Error sending message:', error);
    }
  };

  const handleAttachment = async (type) => {
    try {
      let result;
      if (type === 'image') {
        result = await ImagePicker.launchImageLibraryAsync({
          mediaTypes: ImagePicker.MediaTypeOptions.Images,
          quality: 0.8,
        });
      } else {
        result = await DocumentPicker.getDocumentAsync();
      }

      if (!result.canceled) {
        setLoading(true);
        const file = result.assets[0];
        const response = await fetch(file.uri);
        const blob = await response.blob();

        const storageRef = ref(storage, `chat/${chatId}/${Date.now()}_${file.name}`);
        await uploadBytes(storageRef, blob);
        const downloadURL = await getDownloadURL(storageRef);

        await MessagingService.sendMessage(
          chatId || route.params.chatId,
          type === 'image' ? downloadURL : { url: downloadURL, name: file.name },
          type
        );
      }
    } catch (error) {
      console.error('Error handling attachment:', error);
    } finally {
      setLoading(false);
      setAttachmentMenuVisible(false);
    }
  };

  const handleMessageLongPress = (message) => {
    setSelectedMessage(message);
    setMenuVisible(true);
  };

  const handleReaction = async (reaction) => {
    try {
      await MessagingService.addReaction(selectedMessage.id, reaction);
    } catch (error) {
      console.error('Error adding reaction:', error);
    } finally {
      setMenuVisible(false);
      setSelectedMessage(null);
    }
  };

  const handleDeleteMessage = async () => {
    try {
      await MessagingService.deleteMessage(selectedMessage.id);
    } catch (error) {
      console.error('Error deleting message:', error);
    } finally {
      setMenuVisible(false);
      setSelectedMessage(null);
    }
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={'#1976d2'} />
      </View>
    );
  }

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0}
    >
      <View style={styles.header}>
        <Avatar.Text
          size={40}
          label={recipientDetails?.displayName?.charAt(0) || '?'}
        />
        <Title style={styles.headerTitle}>
          {recipientDetails?.displayName || 'Chat'}
        </Title>
      </View>

      <ScrollView
        ref={scrollViewRef}
        style={styles.messagesContainer}
        onContentSizeChange={() => scrollViewRef.current?.scrollToEnd()}
      >
        {messages.map((message) => (
          <MessageBubble
            key={message.id}
            message={message}
            isOwnMessage={message.senderId === user.uid}
            onLongPress={handleMessageLongPress}
          />
        ))}
      </ScrollView>

      <View style={styles.inputContainer}>
        <IconButton
          icon="paperclip"
          size={24}
          onPress={() => setAttachmentMenuVisible(true)}
        />
        <TextInput
          style={styles.input}
          value={messageText}
          onChangeText={setMessageText}
          placeholder="Type a message..."
          multiline
        />
        <IconButton
          icon="send"
          size={24}
          onPress={handleSendMessage}
          disabled={!messageText.trim()}
        />
      </View>

      <Portal>
        <Menu
          visible={menuVisible}
          onDismiss={() => setMenuVisible(false)}
          anchor={{ x: 0, y: 0 }}
        >
          <Menu.Item
            onPress={() => handleReaction('👍')}
            title="👍 Like"
          />
          <Menu.Item
            onPress={() => handleReaction('❤️')}
            title="❤️ Love"
          />
          <Menu.Item
            onPress={() => handleReaction('😊')}
            title="😊 Smile"
          />
          {selectedMessage?.senderId === user.uid && (
            <Menu.Item
              onPress={handleDeleteMessage}
              title="Delete"
              titleStyle={{ color: 'red' }}
            />
          )}
        </Menu>

        <Menu
          visible={attachmentMenuVisible}
          onDismiss={() => setAttachmentMenuVisible(false)}
          anchor={{ x: 0, y: 0 }}
        >
          <Menu.Item
            onPress={() => handleAttachment('image')}
            title="Image"
            icon="image"
          />
          <Menu.Item
            onPress={() => handleAttachment('file')}
            title="File"
            icon="file"
          />
        </Menu>
      </Portal>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: 'white',
    elevation: 4,
  },
  headerTitle: {
    marginLeft: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  messagesContainer: {
    flex: 1,
    padding: 16,
  },
  messageBubble: {
    maxWidth: '80%',
    padding: 12,
    borderRadius: 16,
    marginBottom: 8,
  },
  ownMessage: {
    alignSelf: 'flex-end',
    borderBottomRightRadius: 4,
  },
  otherMessage: {
    alignSelf: 'flex-start',
    borderBottomLeftRadius: 4,
  },
  messageText: {
    fontSize: 16,
  },
  messageTime: {
    fontSize: 12,
    marginTop: 4,
  },
  messageImage: {
    width: 200,
    height: 200,
    borderRadius: 8,
  },
  fileContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  fileName: {
    marginLeft: 8,
    flex: 1,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    backgroundColor: 'white',
    elevation: 4,
  },
  input: {
    flex: 1,
    marginHorizontal: 8,
    maxHeight: 100,
  },
  reactionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 4,
  },
  reactionChip: {
    marginRight: 4,
    marginTop: 4,
  },
});

export default Messaging;
