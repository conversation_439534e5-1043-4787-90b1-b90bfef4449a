import React, { useState, useEffect } from 'react';
import { View, ScrollView, StyleSheet } from 'react-native';
import { Card, Title, Text, DataTable, List, Chip } from 'react-native-paper';
import { db } from '../../config/firebase';
import { collection, query, getDocs, where } from 'firebase/firestore';
import { useAuth } from '../../context/AuthContext';

const ResultManagement = () => {
  const { user } = useAuth();
  const [results, setResults] = useState([]);
  const [marksheets, setMarksheets] = useState([]);
  const [selectedExam, setSelectedExam] = useState(null);
  const [studentInfo, setStudentInfo] = useState(null);

  useEffect(() => {
    fetchStudentInfo();
    fetchResults();
    fetchMarksheets();
  }, []);

  const fetchStudentInfo = async () => {
    try {
      const studentsRef = collection(db, 'students');
      const q = query(studentsRef, where('userId', '==', user.uid));
      const querySnapshot = await getDocs(q);
      
      if (!querySnapshot.empty) {
        setStudentInfo(querySnapshot.docs[0].data());
      }
    } catch (error) {
      console.error('Error fetching student info:', error);
    }
  };

  const fetchResults = async () => {
    try {
      const resultsRef = collection(db, 'results');
      const q = query(
        resultsRef,
        where('studentId', '==', user.uid),
        where('status', '==', 'published')
      );
      const querySnapshot = await getDocs(q);
      
      const resultsData = [];
      querySnapshot.forEach((doc) => {
        resultsData.push({ id: doc.id, ...doc.data() });
      });
      
      setResults(resultsData);
    } catch (error) {
      console.error('Error fetching results:', error);
    }
  };

  const fetchMarksheets = async () => {
    try {
      const marksheetsRef = collection(db, 'marksheets');
      const q = query(
        marksheetsRef,
        where('studentId', '==', user.uid),
        where('status', '==', 'published')
      );
      const querySnapshot = await getDocs(q);
      
      const marksheetsData = [];
      querySnapshot.forEach((doc) => {
        marksheetsData.push({ id: doc.id, ...doc.data() });
      });
      
      setMarksheets(marksheetsData);
    } catch (error) {
      console.error('Error fetching marksheets:', error);
    }
  };

  const calculateGPA = (marks) => {
    // Implement your GPA calculation logic here
    if (marks >= 90) return 4.0;
    if (marks >= 80) return 3.5;
    if (marks >= 70) return 3.0;
    if (marks >= 60) return 2.5;
    if (marks >= 50) return 2.0;
    return 0.0;
  };

  const getGradeColor = (grade) => {
    if (grade >= 3.5) return '#4CAF50';
    if (grade >= 3.0) return '#2196F3';
    if (grade >= 2.5) return '#FFC107';
    if (grade >= 2.0) return '#FF9800';
    return '#F44336';
  };

  return (
    <ScrollView style={styles.container}>
      {studentInfo && (
        <Card style={styles.infoCard}>
          <Card.Content>
            <Title>{studentInfo.name}</Title>
            <Text>Class: {studentInfo.className}</Text>
            <Text>Roll No: {studentInfo.rollNo}</Text>
            <Text>Admission No: {studentInfo.admissionNumber}</Text>
          </Card.Content>
        </Card>
      )}

      <Card style={styles.resultsCard}>
        <Card.Content>
          <Title>Published Results</Title>
          
          {results.map((result) => (
            <List.Accordion
              key={result.id}
              title={result.examName}
              description={`Term: ${result.term} | Year: ${result.year}`}
              expanded={selectedExam === result.id}
              onPress={() => setSelectedExam(selectedExam === result.id ? null : result.id)}
            >
              <DataTable>
                <DataTable.Header>
                  <DataTable.Title>Subject</DataTable.Title>
                  <DataTable.Title numeric>Marks</DataTable.Title>
                  <DataTable.Title numeric>Grade</DataTable.Title>
                  <DataTable.Title>Remarks</DataTable.Title>
                </DataTable.Header>

                {result.subjects.map((subject) => {
                  const grade = calculateGPA(subject.marks);
                  return (
                    <DataTable.Row key={subject.name}>
                      <DataTable.Cell>{subject.name}</DataTable.Cell>
                      <DataTable.Cell numeric>{subject.marks}</DataTable.Cell>
                      <DataTable.Cell numeric>
                        <Chip
                          mode="outlined"
                          selectedColor={getGradeColor(grade)}
                        >
                          {grade.toFixed(1)}
                        </Chip>
                      </DataTable.Cell>
                      <DataTable.Cell>{subject.remarks}</DataTable.Cell>
                    </DataTable.Row>
                  );
                })}

                <DataTable.Row style={styles.totalRow}>
                  <DataTable.Cell>
                    <Text style={styles.boldText}>Total</Text>
                  </DataTable.Cell>
                  <DataTable.Cell numeric>
                    <Text style={styles.boldText}>
                      {result.subjects.reduce((sum, subject) => sum + subject.marks, 0)}
                    </Text>
                  </DataTable.Cell>
                  <DataTable.Cell numeric>
                    <Text style={styles.boldText}>
                      {(result.subjects.reduce((sum, subject) => 
                        sum + calculateGPA(subject.marks), 0) / result.subjects.length).toFixed(2)}
                    </Text>
                  </DataTable.Cell>
                  <DataTable.Cell>
                    <Text style={styles.boldText}>{result.overallRemarks}</Text>
                  </DataTable.Cell>
                </DataTable.Row>
              </DataTable>

              {result.teacherComments && (
                <Card style={styles.commentsCard}>
                  <Card.Content>
                    <Title>Teacher's Comments</Title>
                    <Text>{result.teacherComments}</Text>
                  </Card.Content>
                </Card>
              )}
            </List.Accordion>
          ))}
        </Card.Content>
      </Card>

      <Card style={styles.progressCard}>
        <Card.Content>
          <Title>Academic Progress</Title>
          
          {marksheets.map((marksheet) => (
            <List.Item
              key={marksheet.id}
              title={marksheet.subject}
              description={`Teacher: ${marksheet.teacherName}`}
              right={() => (
                <View style={styles.progressInfo}>
                  <Chip
                    mode="outlined"
                    selectedColor={getGradeColor(calculateGPA(marksheet.totalMarks))}
                  >
                    {marksheet.totalMarks}%
                  </Chip>
                  <Text style={styles.progressText}>
                    Rank: {marksheet.rank}/{marksheet.totalStudents}
                  </Text>
                </View>
              )}
            />
          ))}
        </Card.Content>
      </Card>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  infoCard: {
    margin: 10,
  },
  resultsCard: {
    margin: 10,
  },
  progressCard: {
    margin: 10,
  },
  commentsCard: {
    marginTop: 10,
    backgroundColor: '#f9f9f9',
  },
  totalRow: {
    backgroundColor: '#f5f5f5',
  },
  boldText: {
    fontWeight: 'bold',
  },
  progressInfo: {
    alignItems: 'flex-end',
  },
  progressText: {
    marginTop: 5,
    color: '#666',
  },
});

export default ResultManagement;
