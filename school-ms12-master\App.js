import 'react-native-gesture-handler';
import React, { useState, useEffect } from 'react';
import { Provider as PaperProvider, DefaultTheme } from 'react-native-paper';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { View, Text, StyleSheet, ActivityIndicator, LogBox, Platform, UIManager, AppState, Dimensions } from 'react-native';
import { AuthProvider } from './src/context/AuthContext';
import { LanguageProvider } from './src/context/LanguageContext';
import { NotificationProvider } from './src/context/NotificationContext';
import AppNavigator from './src/navigation/AppNavigator';
import ToastNotificationHandler from './src/components/common/ToastNotificationHandler';
import { firebaseApp } from './src/config/firebase';
import { ensureFirebaseInitialized } from './src/utils/FirebaseInitializer';
import mobileTheme from './src/theme/mobileTheme';

// Import polyfills early to ensure they're available
import './src/utils/uuidPolyfill';
import './src/utils/platformPolyfill';

// Enable layout animations on Android
if (Platform.OS === 'android') {
  if (UIManager.setLayoutAnimationEnabledExperimental) {
    UIManager.setLayoutAnimationEnabledExperimental(true);
  }
}

// Log platform information to help with debugging
console.log('Platform OS:', Platform?.OS || 'unknown');

// Ignore specific warnings that might be coming from dependencies
LogBox.ignoreLogs([
  'Require cycle:',
  'AsyncStorage has been extracted from react-native',
  'Setting a timer',
  'Property \'theme\' doesn\'t exist',
  'Property \'Platform\' doesn\'t exist',
  'ReferenceError: Property \'Platform\' doesn\'t exist',
  'Cannot read property \'classManagement\' of undefined',
  'Cannot read property \'_context\' of undefined',
  'Requiring unknown module',
  'Failed to load local translations',
  'TypeError: Cannot read property',
  'Error accessing language context',
  'Possible Unhandled Promise Rejection',
  'Non-serializable values were found in the navigation state',
  'crypto.getRandomValues() not supported',
  'Firebase: Error (auth/invalid-credential)',
  'Firebase: Error (auth/invalid-email)',
  'Error sending verification email',
  'Invariant Violation: "main" has not been registered',
  'Cannot read property \'origin\' of undefined',
  'Falling back to token verification'
]);

// Error boundary component
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null, errorInfo: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    this.setState({ error, errorInfo });
    console.error('App Error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <View style={styles.errorContainer}>
          <Text style={styles.errorTitle}>Something went wrong</Text>
          <Text style={styles.errorMessage}>{this.state.error?.toString()}</Text>
          <Text style={styles.errorHint}>Please restart the application</Text>
        </View>
      );
    }
    return this.props.children;
  }
}

export default function App() {
  const [isReady, setIsReady] = useState(false);
  const [initError, setInitError] = useState(null);

  useEffect(() => {
    // Initialize the app and Firebase
    const initializeApp = async () => {
      try {
        // Ensure Firebase is initialized
        if (!firebaseApp) {
          throw new Error('Firebase app is not available');
        }

        console.log('Firebase app name in App.js:', firebaseApp.name);

        // Ensure Firebase is fully initialized
        const isFirebaseInitialized = await ensureFirebaseInitialized();
        if (!isFirebaseInitialized) {
          throw new Error('Failed to initialize Firebase');
        }

        // Add a longer delay to ensure the UI Manager is initialized
        // This is critical for Android
        const delay = Platform.OS === 'android' ? 2000 : 500;
        await new Promise(resolve => setTimeout(resolve, delay));

        setIsReady(true);
      } catch (error) {
        console.error('Failed to initialize app:', error);
        setInitError(error);
      }
    };

    initializeApp();
  }, []);

  if (!isReady && !initError) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#0000ff" />
        <Text style={styles.loadingText}>Loading...</Text>
      </View>
    );
  }

  if (initError) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorTitle}>Initialization Error</Text>
        <Text style={styles.errorMessage}>{initError.toString()}</Text>
        <Text style={styles.errorHint}>Please check your connection and restart the app</Text>
      </View>
    );
  }

  // Wrap the entire app in a try-catch to prevent crashes
  // Check if we're on a mobile device
  const isMobile = Platform.OS === 'android' || Platform.OS === 'ios' || (Platform.OS === 'web' && Dimensions.get('window').width < 768);

  try {
    return (
      <ErrorBoundary>
        <SafeAreaProvider>
          <PaperProvider theme={isMobile ? mobileTheme : DefaultTheme}>
            <LanguageProvider>
              <AuthProvider>
                <NotificationProvider>
                  <AppNavigator />
                  <ToastNotificationHandler />
                </NotificationProvider>
              </AuthProvider>
            </LanguageProvider>
          </PaperProvider>
        </SafeAreaProvider>
      </ErrorBoundary>
    );
  } catch (error) {
    console.error('Critical error in App render:', error);
    // Fallback UI for critical errors
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorTitle}>Critical Error</Text>
        <Text style={styles.errorMessage}>{error.toString()}</Text>
        <Text style={styles.errorHint}>Please restart the application</Text>
      </View>
    );
  }
}

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#333',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f8d7da',
    padding: 20,
  },
  errorTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#721c24',
    marginBottom: 10,
  },
  errorMessage: {
    fontSize: 16,
    color: '#721c24',
    textAlign: 'center',
    marginBottom: 20,
  },
  errorHint: {
    fontSize: 14,
    color: '#721c24',
    opacity: 0.8,
  },
});
