import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Avatar } from 'react-native-paper';
import CloudinaryImage from './CloudinaryImage';

/**
 * CloudinaryAvatar component for displaying user avatars from Cloudinary
 * This component supports both Cloudinary public IDs and Firebase Storage URLs
 * 
 * @param {Object} props - Component props
 * @param {string} props.source - Cloudinary public ID or Firebase Storage URL
 * @param {string} props.label - Text to display if no image is available
 * @param {number} props.size - Size of the avatar
 * @param {Object} props.style - Style object for the avatar
 * @param {string} props.backgroundColor - Background color for text avatar
 * @param {string} props.color - Text color for text avatar
 * @param {Object} props.labelStyle - Style object for the label text
 */
const CloudinaryAvatar = ({
  source,
  label = '',
  size = 40,
  style = {},
  backgroundColor = '#1976d2',
  color = 'white',
  labelStyle = {},
  ...props
}) => {
  // If no source is provided, use Avatar.Text
  if (!source) {
    return (
      <Avatar.Text
        size={size}
        label={label}
        style={[{ backgroundColor }, style]}
        color={color}
        labelStyle={labelStyle}
        {...props}
      />
    );
  }

  // Use CloudinaryImage for the avatar
  return (
    <View style={[styles.container, { width: size, height: size, borderRadius: size / 2 }, style]}>
      <CloudinaryImage
        source={source}
        width={size}
        height={size}
        circle={true}
        style={styles.image}
        fallbackUri={`https://ui-avatars.com/api/?name=${label}&background=${backgroundColor.replace('#', '')}&color=${color.replace('#', '')}&size=${size}`}
        {...props}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    overflow: 'hidden',
  },
  image: {
    width: '100%',
    height: '100%',
  },
});

export default CloudinaryAvatar;
