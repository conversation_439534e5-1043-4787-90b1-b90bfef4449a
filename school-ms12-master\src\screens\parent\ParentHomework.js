import React, { useState, useEffect, useRef } from 'react';
import { View, ScrollView, StyleSheet, TouchableOpacity, Animated, RefreshControl, Alert } from 'react-native';
import { Card, Title, Text, Chip, Divider, useTheme, IconButton, ActivityIndicator, Searchbar, SegmentedButtons, Surface, Badge, Menu, Button } from 'react-native-paper';
import { db, auth } from '../../config/firebase';
import { collection, query, getDocs, where, doc, getDoc, orderBy } from 'firebase/firestore';
import { useNavigation } from '@react-navigation/native';
import { useLanguage } from '../../context/LanguageContext';
import { useAuth } from '../../context/AuthContext';
import * as Animatable from 'react-native-animatable';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { SafeAreaView } from 'react-native-safe-area-context';
import ParentAppHeader from '../../components/common/ParentAppHeader';

const ParentHomework = () => {
  const navigation = useNavigation();
  const theme = useTheme();
  const { translate, language, isRTL } = useLanguage();
  const { user } = useAuth();

  // State variables
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState(null);
  const [children, setChildren] = useState([]);
  const [selectedChild, setSelectedChild] = useState(null);
  const [homework, setHomework] = useState([]);
  const [filteredHomework, setFilteredHomework] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [viewMode, setViewMode] = useState('list');
  const [statusFilter, setStatusFilter] = useState('all');
  const [childMenuVisible, setChildMenuVisible] = useState(false);

  // Animation
  const fadeAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Hide header in this screen as we're using our own
    navigation.setOptions({
      headerShown: false,
    });

    // Start fade-in animation
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 1000,
      useNativeDriver: true,
    }).start();

    // Fetch parent's children
    fetchChildren();
  }, [navigation]);

  // Fetch parent's children
  const fetchChildren = async () => {
    try {
      setLoading(true);

      // Get parent data to check for children array
      const parentRef = doc(db, 'users', auth.currentUser.uid);
      const parentDoc = await getDoc(parentRef);

      if (!parentDoc.exists()) {
        console.error(translate('parent.dashboard.errorLoadingChildData') || 'Parent document not found');
        setLoading(false);
        return;
      }

      const parentData = parentDoc.data();
      const childrenIds = parentData.children || [];

      if (childrenIds.length === 0) {
        console.log(translate('parent.dashboard.noChildrenRegistered') || 'No children IDs found for this parent');
        setHomework([]);
        setLoading(false);
        return;
      }

      // Fetch each child's data
      const childrenData = [];

      for (const childId of childrenIds) {
        try {
          const childRef = doc(db, 'users', childId);
          const childDoc = await getDoc(childRef);

          if (childDoc.exists()) {
            const childData = { id: childId, ...childDoc.data() };

            // Ensure child has a name property for display
            if (!childData.name) {
              childData.name = `${childData.firstName || ''} ${childData.lastName || ''}`.trim() || translate('common.unnamed');
            }

            childrenData.push(childData);
          }
        } catch (childError) {
          console.error(`Error fetching child ${childId}:`, childError);
        }
      }

      setChildren(childrenData);

      // Select first child by default if available
      if (childrenData.length > 0) {
        setSelectedChild(childrenData[0]);

        // Get class and section from the child data
        const classId = childrenData[0].classId || childrenData[0].class || childrenData[0].className;
        const section = childrenData[0].section || childrenData[0].sectionName;

        // Fetch homework for the first child
        if (classId && section) {
          console.log('Fetching homework for child:', childrenData[0].name, 'Class:', classId, 'Section:', section);
          await fetchHomework(classId, section, childrenData[0].id);
        } else {
          console.log('Child missing classId or section:', childrenData[0]);
          setHomework([]);
        }
      } else {
        console.log('No children found for this parent');
        setHomework([]);
      }
    } catch (error) {
      console.error('Error fetching children:', error);
      setHomework([]);
    } finally {
      setLoading(false);
    }
  };

  // Fetch homework for a specific child's class and section
  const fetchHomework = async (classId, section, studentId) => {
    try {
      if (!classId || !section) {
        console.log('Missing classId or section:', { classId, section });
        setHomework([]);
        setLoading(false);
        setRefreshing(false);
        return;
      }

      setLoading(true);

      // Query homework for the child's class and section
      const homeworkRef = collection(db, 'homework');

      // First, get all active homework
      const homeworkQuery = query(
        homeworkRef,
        where('status', '==', 'active')
      );

      const homeworkSnapshot = await getDocs(homeworkQuery);

      // Filter locally for class and section to avoid compound query issues
      const homeworkData = [];
      homeworkSnapshot.forEach((doc) => {
        const data = doc.data();
        if (data.classId === classId && data.sectionName === section) {
          // Format due date
          let dueDate = data.dueDate;
          if (dueDate) {
            if (dueDate.toDate) {
              dueDate = dueDate.toDate();
            } else if (typeof dueDate === 'string') {
              dueDate = new Date(dueDate);
            }
          } else {
            dueDate = new Date();
          }

          // Check if this student has submitted this homework
          const isSubmitted = data.submissions && data.submissions[studentId];

          homeworkData.push({
            id: doc.id,
            ...data,
            dueDate,
            isSubmitted: !!isSubmitted,
            submissionDate: isSubmitted ? isSubmitted.submittedAt : null,
            submissionStatus: isSubmitted ? isSubmitted.status : 'pending'
          });
        }
      });

      // Sort by due date (most recent first)
      homeworkData.sort((a, b) => b.dueDate - a.dueDate);

      setHomework(homeworkData);
      setFilteredHomework(homeworkData);
    } catch (error) {
      console.error('Error fetching homework:', error);
      setHomework([]);
      setFilteredHomework([]);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Handle refresh
  const onRefresh = async () => {
    setRefreshing(true);
    if (selectedChild) {
      const classId = selectedChild.classId || selectedChild.class || selectedChild.className;
      const section = selectedChild.section || selectedChild.sectionName;

      if (classId && section) {
        await fetchHomework(classId, section, selectedChild.id);
      } else {
        await fetchChildren();
      }
    } else {
      await fetchChildren();
    }
  };

  // Handle child selection
  const handleChildSelect = async (child) => {
    setSelectedChild(child);
    setChildMenuVisible(false);

    // Get class and section from the child data
    const classId = child.classId || child.class || child.className;
    const section = child.section || child.sectionName;

    if (classId && section) {
      console.log('Fetching homework for selected child:', child.name || `${child.firstName || ''} ${child.lastName || ''}`.trim() || translate('common.unnamed'), 'Class:', classId, 'Section:', section);
      await fetchHomework(classId, section, child.id);
    } else {
      console.log('Selected child missing classId or section:', child);
      setHomework([]);
      setFilteredHomework([]);
    }
  };

  // Toggle view mode
  const toggleViewMode = (mode) => {
    setViewMode(mode);
  };

  // Handle search
  const handleSearch = (query) => {
    setSearchQuery(query);

    if (!query.trim()) {
      setFilteredHomework(homework);
      return;
    }

    const filtered = homework.filter(item =>
      item.title?.toLowerCase().includes(query.toLowerCase()) ||
      item.description?.toLowerCase().includes(query.toLowerCase()) ||
      item.subject?.toLowerCase().includes(query.toLowerCase())
    );

    setFilteredHomework(filtered);
  };

  // Handle status filter
  const handleStatusFilter = (status) => {
    setStatusFilter(status);

    if (status === 'all') {
      setFilteredHomework(homework);
      return;
    }

    let filtered = [];

    if (status === 'pending') {
      filtered = homework.filter(item => !item.isSubmitted);
    } else if (status === 'submitted') {
      filtered = homework.filter(item => item.isSubmitted);
    } else if (status === 'overdue') {
      const now = new Date();
      filtered = homework.filter(item => !item.isSubmitted && item.dueDate < now);
    }

    setFilteredHomework(filtered);
  };

  // Format date for display
  const formatDate = (date) => {
    if (!date) return 'No date';

    try {
      return date.toLocaleDateString(language === 'en' ? 'en-US' : 'am-ET', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    } catch (error) {
      console.error('Error formatting date:', error);
      return 'Invalid date';
    }
  };

  // Get status color
  const getStatusColor = (homework) => {
    const now = new Date();

    if (homework.isSubmitted) {
      return '#4CAF50'; // Submitted - green
    } else if (homework.dueDate < now) {
      return '#F44336'; // Overdue - red
    } else if ((homework.dueDate - now) < (24 * 60 * 60 * 1000)) {
      return '#FF9800'; // Due soon (within 24 hours) - orange
    } else {
      return '#2196F3'; // Upcoming - blue
    }
  };

  // Get status text
  const getStatusText = (homework) => {
    const now = new Date();

    if (homework.isSubmitted) {
      return translate('homework.submitted') || 'Submitted';
    } else if (homework.dueDate < now) {
      return translate('homework.overdue') || 'Overdue';
    } else if ((homework.dueDate - now) < (24 * 60 * 60 * 1000)) {
      return translate('homework.dueSoon') || 'Due Soon';
    } else {
      return translate('homework.upcoming') || 'Upcoming';
    }
  };

  // Render homework card
  const renderHomeworkCard = (homework) => {
    const statusColor = getStatusColor(homework);
    const statusText = getStatusText(homework);

    return (
      <Surface key={homework.id} style={styles.homeworkCard} elevation={1}>
        <View style={styles.homeworkCardContent}>
          <View style={styles.homeworkCardHeader}>
            <Text style={styles.homeworkCardTitle}>{homework.title}</Text>
            <Chip
              mode="flat"
              style={{
                backgroundColor: `${statusColor}20`,
                height: 24
              }}
              textStyle={{ fontSize: 10, color: statusColor }}
              icon={homework.isSubmitted ? 'check-circle' : 'clock-outline'}
            >
              {statusText}
            </Chip>
          </View>

          <Divider style={styles.divider} />

          <View style={styles.homeworkCardDetails}>
            <View style={styles.detailRow}>
              <MaterialCommunityIcons name="book-open-variant" size={16} color="#757575" />
              <Text style={styles.detailText}>{translate('homework.subject')}: {homework.subject}</Text>
            </View>

            <View style={styles.detailRow}>
              <MaterialCommunityIcons name="calendar" size={16} color="#757575" />
              <Text style={styles.detailText}>{translate('homework.dueDate')}: {formatDate(homework.dueDate)}</Text>
            </View>

            {homework.maxScore && (
              <View style={styles.detailRow}>
                <MaterialCommunityIcons name="star" size={16} color="#757575" />
                <Text style={styles.detailText}>{translate('homework.maxScore')}: {homework.maxScore}</Text>
              </View>
            )}
          </View>

          {homework.description && (
            <View style={styles.descriptionContainer}>
              <Text style={styles.descriptionTitle}>{translate('homework.description')}:</Text>
              <Text style={styles.descriptionText}>{homework.description}</Text>
            </View>
          )}

          <View style={styles.homeworkCardActions}>
            {homework.isSubmitted ? (
              <Button
                mode="outlined"
                icon="eye"
                onPress={() => Alert.alert(
                  translate('homework.submissionDetails') || 'Submission Details',
                  `${translate('homework.submittedOn') || 'Submitted on'}: ${formatDate(homework.submissionDate)}\n${translate('homework.status') || 'Status'}: ${homework.submissionStatus}`
                )}
              >
                {translate('homework.viewSubmission') || 'View Submission'}
              </Button>
            ) : (
              <Button
                mode="contained"
                icon="upload"
                onPress={() => Alert.alert(
                  translate('homework.notImplemented') || 'Not Implemented',
                  translate('homework.submissionFeatureMessage') || 'The submission feature will be implemented in a future update.'
                )}
              >
                {translate('homework.submit') || 'Submit'}
              </Button>
            )}
          </View>
        </View>
      </Surface>
    );
  };

  // Render grid card
  const renderGridCard = (homework) => {
    const statusColor = getStatusColor(homework);
    const statusText = getStatusText(homework);

    return (
      <Surface key={homework.id} style={styles.gridCard} elevation={2}>
        <View style={styles.gridCardContent}>
          <View style={styles.gridCardHeader}>
            <View style={styles.gridCardBadge}>
              <Badge
                size={24}
                style={{
                  backgroundColor: statusColor
                }}
              >{homework.subject ? homework.subject.charAt(0) : 'H'}</Badge>
            </View>
            <Text style={styles.gridCardTitle} numberOfLines={1}>{homework.title}</Text>
          </View>

          <View style={styles.gridCardDetails}>
            <Text style={styles.gridCardSubject} numberOfLines={1}>{homework.subject}</Text>
            <Text style={styles.gridCardDate}>{formatDate(homework.dueDate)}</Text>
          </View>

          <View style={styles.gridCardFooter}>
            <Chip
              mode="flat"
              style={{
                backgroundColor: `${statusColor}20`,
                height: 24
              }}
              textStyle={{ fontSize: 10, color: statusColor }}
              icon={homework.isSubmitted ? 'check-circle' : 'clock-outline'}
            >
              {statusText}
            </Chip>
          </View>
        </View>
      </Surface>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Parent App Header */}
      <ParentAppHeader
        title={translate('homework.title') || "Homework"}
      />

      {/* Main Content */}
      <Animated.View
        style={[
          styles.content,
          { opacity: fadeAnim }
        ]}
      >
        {/* Child Selector */}
        <View style={styles.childSelectorContainer}>
          <View style={styles.childSelectorHeader}>
            <Text style={styles.childSelectorTitle}>{translate('common.selectChild') || "Select Child"}</Text>
            <Menu
              visible={childMenuVisible}
              onDismiss={() => setChildMenuVisible(false)}
              anchor={
                <TouchableOpacity
                  style={styles.childSelectorButton}
                  onPress={() => setChildMenuVisible(true)}
                >
                  <Text style={styles.childSelectorButtonText}>
                    {selectedChild ? (selectedChild.name || `${selectedChild.firstName || ''} ${selectedChild.lastName || ''}`.trim() || translate('common.unnamed')) : (translate('common.selectChild'))}
                  </Text>
                  <MaterialCommunityIcons name="chevron-down" size={20} color="#757575" />
                </TouchableOpacity>
              }
            >
              {children.map((child) => (
                <Menu.Item
                  key={child.id}
                  title={child.name || `${child.firstName || ''} ${child.lastName || ''}`.trim() || translate('common.unnamed')}
                  onPress={() => handleChildSelect(child)}
                />
              ))}
            </Menu>
          </View>
        </View>

        {/* Search and Filters */}
        <View style={styles.filtersContainer}>
          <Searchbar
            placeholder={translate('homework.searchHomework') || "Search homework..."}
            onChangeText={handleSearch}
            value={searchQuery}
            style={styles.searchBar}
          />

          <SegmentedButtons
            value={statusFilter}
            onValueChange={handleStatusFilter}
            buttons={[
              {
                value: 'all',
                label: translate('homework.all') || 'All',
              },
              {
                value: 'pending',
                label: translate('homework.pending') || 'Pending',
              },
              {
                value: 'submitted',
                label: translate('homework.submitted') || 'Submitted',
              },
              {
                value: 'overdue',
                label: translate('homework.overdue') || 'Overdue',
              },
            ]}
            style={styles.segmentedButtons}
          />

          <View style={styles.viewToggleContainer}>
            <IconButton
              icon="view-list"
              size={24}
              onPress={() => toggleViewMode('list')}
              style={[
                styles.viewToggleButton,
                viewMode === 'list' && styles.activeViewToggleButton
              ]}
            />
            <IconButton
              icon="view-grid"
              size={24}
              onPress={() => toggleViewMode('grid')}
              style={[
                styles.viewToggleButton,
                viewMode === 'grid' && styles.activeViewToggleButton
              ]}
            />
          </View>
        </View>

        {/* Homework List */}
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollViewContent}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[theme.colors.primary]}
            />
          }
        >
          {loading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={theme.colors.primary} />
              <Text style={styles.loadingText}>{translate('common.loading') || "Loading..."}</Text>
            </View>
          ) : error ? (
            <View style={styles.errorContainer}>
              <MaterialCommunityIcons name="alert-circle-outline" size={48} color="#F44336" />
              <Text style={styles.errorText}>{error}</Text>
              <Button mode="contained" onPress={onRefresh} style={styles.retryButton}>
                {translate('common.retry') || "Retry"}
              </Button>
            </View>
          ) : filteredHomework.length === 0 ? (
            <View style={styles.emptyContainer}>
              <MaterialCommunityIcons name="book-open-page-variant" size={48} color="#9E9E9E" />
              <Text style={styles.emptyText}>{translate('homework.noHomework') || "No homework found"}</Text>
              <Text style={styles.emptySubtext}>{translate('homework.tryDifferentFilters') || "Try different filters"}</Text>
            </View>
          ) : viewMode === 'grid' ? (
            <View style={styles.gridContainer}>
              {filteredHomework.map(homework => renderGridCard(homework))}
            </View>
          ) : (
            <View style={styles.listContainer}>
              {filteredHomework.map(homework => renderHomeworkCard(homework))}
            </View>
          )}
        </ScrollView>
      </Animated.View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
  },
  childSelectorContainer: {
    padding: 16,
    backgroundColor: '#fff',
    elevation: 2,
  },
  childSelectorHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  childSelectorTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#424242',
  },
  childSelectorButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    borderRadius: 4,
    backgroundColor: '#f0f0f0',
  },
  childSelectorButtonText: {
    marginRight: 8,
    color: '#424242',
  },
  filtersContainer: {
    padding: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  searchBar: {
    marginBottom: 16,
    elevation: 2,
  },
  segmentedButtons: {
    marginBottom: 16,
  },
  viewToggleContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  viewToggleButton: {
    margin: 0,
  },
  activeViewToggleButton: {
    backgroundColor: '#e3f2fd',
  },
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    padding: 16,
    paddingBottom: 32,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#757575',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  errorText: {
    marginTop: 16,
    marginBottom: 16,
    fontSize: 16,
    color: '#F44336',
    textAlign: 'center',
  },
  retryButton: {
    marginTop: 8,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  emptyText: {
    marginTop: 16,
    fontSize: 18,
    color: '#757575',
    textAlign: 'center',
  },
  emptySubtext: {
    marginTop: 8,
    fontSize: 14,
    color: '#9E9E9E',
    textAlign: 'center',
  },
  listContainer: {
    flex: 1,
  },
  homeworkCard: {
    marginBottom: 16,
    borderRadius: 8,
    overflow: 'hidden',
  },
  homeworkCardContent: {
    padding: 16,
  },
  homeworkCardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  homeworkCardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#212121',
    flex: 1,
    marginRight: 8,
  },
  divider: {
    marginVertical: 8,
  },
  homeworkCardDetails: {
    marginBottom: 8,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  detailText: {
    marginLeft: 8,
    fontSize: 14,
    color: '#616161',
  },
  descriptionContainer: {
    marginVertical: 8,
    padding: 8,
    backgroundColor: '#f5f5f5',
    borderRadius: 4,
  },
  descriptionTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#424242',
    marginBottom: 4,
  },
  descriptionText: {
    fontSize: 14,
    color: '#616161',
  },
  homeworkCardActions: {
    marginTop: 8,
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  gridContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  gridCard: {
    width: '48%',
    marginBottom: 16,
    borderRadius: 8,
    overflow: 'hidden',
  },
  gridCardContent: {
    padding: 12,
    height: 140,
    justifyContent: 'space-between',
  },
  gridCardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  gridCardBadge: {
    marginRight: 8,
  },
  gridCardTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#212121',
    flex: 1,
  },
  gridCardDetails: {
    flex: 1,
  },
  gridCardSubject: {
    fontSize: 14,
    color: '#616161',
    marginBottom: 4,
  },
  gridCardDate: {
    fontSize: 12,
    color: '#9E9E9E',
  },
  gridCardFooter: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    marginTop: 8,
  },
});

export default ParentHomework;
