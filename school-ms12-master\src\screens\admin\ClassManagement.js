import React, { useState, useEffect, useRef } from 'react';
import { useNavigation } from '@react-navigation/native';
import { View, ScrollView, StyleSheet, TouchableOpacity, Animated } from 'react-native';
import { FAB, Portal, Modal, Text, IconButton, Menu, Divider, Card, Title, ActivityIndicator, useTheme, DataTable, Button, Surface, Provider as PaperProvider, DefaultTheme, Chip, TextInput, Badge, Avatar } from 'react-native-paper';
import { db } from '../../config/firebase';
import { collection, addDoc, query, getDocs, doc, updateDoc, deleteDoc, where, getDoc, writeBatch } from 'firebase/firestore';
import CustomButton from '../../components/common/CustomButton';
import CustomInput from '../../components/common/CustomInput';
import SectionStudentsList from '../../components/SectionStudentsList';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useLanguage } from '../../context/LanguageContext';
import * as Animatable from 'react-native-animatable';
import { LinearGradient } from 'expo-linear-gradient';
import { StatusBar } from 'expo-status-bar';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import AdminAppHeader from '../../components/common/AdminAppHeader';
import AdminSidebar from '../../components/common/AdminSidebar';
import SidebarBackdrop from '../../components/common/SidebarBackdrop';

const defaultFormData = {
  name: '',
  description: '',
  academicYear: new Date().getFullYear().toString(),
  sections: [{ name: '', capacity: '', teachers: [] }],
  groups: [{ name: '' }],
};

const ClassManagement = () => {
  // No theme needed - using hardcoded colors
  const navigation = useNavigation();
  const { translate: t, language, getTextStyle, isRTL } = useLanguage();

  // State variables
  const [classes, setClasses] = useState([]);
  const [visible, setVisible] = useState(false);
  const [selectedClass, setSelectedClass] = useState(null);
  const [selectedSection, setSelectedSection] = useState(null);
  const [formData, setFormData] = useState(defaultFormData);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [menuVisible, setMenuVisible] = useState(false);
  const [selectedMenuItem, setSelectedMenuItem] = useState(null);
  const [showTeacherAssignModal, setShowTeacherAssignModal] = useState(false);
  const [selectedTeacher, setSelectedTeacher] = useState(null);
  const [teacherSearchQuery, setTeacherSearchQuery] = useState('');
  const [assignedTeachers, setAssignedTeachers] = useState({});
  const [sectionStudentsMap, setSectionStudentsMap] = useState({});
  const [teachers, setTeachers] = useState([]);
  const [showStudentsModal, setShowStudentsModal] = useState(false);
  const [selectedStudents, setSelectedStudents] = useState([]);
  const [studentsLoading, setStudentsLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  // Animation state variables
  const [fadeAnim] = useState(new Animated.Value(0));
  const [scaleAnim] = useState(new Animated.Value(0.95));
  const [slideAnim] = useState(new Animated.Value(50));

  // Sidebar state
  const [drawerOpen, setDrawerOpen] = useState(false);
  const drawerAnim = useRef(new Animated.Value(-300)).current;
  const backdropFadeAnim = useRef(new Animated.Value(0)).current;
  const [activeSidebarItem, setActiveSidebarItem] = useState('ClassManagement');

  // Subject management state
  const [selectedSubject, setSelectedSubject] = useState('');
  const [showSubjectModal, setShowSubjectModal] = useState(false);
  const [subjects, setSubjects] = useState([]);
  const [subjectsLoading, setSubjectsLoading] = useState(false);
  const [showSectionStudentsModal, setShowSectionStudentsModal] = useState(false);
  const [selectedSectionData, setSelectedSectionData] = useState(null);

  // Helper function to translate text using namespaced keys
  const translate = (key, params = {}) => {
    try {
      // Use the global translate function with the admin.classManagement namespace
      return t(`admin.classManagement.${key}`, params);
    } catch (error) {
      console.error('Translation error:', error);
      return key;
    }
  };

  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        friction: 8,
        tension: 40,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }),
    ]).start();

    // Hide the default header
    navigation.setOptions({
      headerShown: false,
    });
  }, [navigation]);

  const toggleDrawer = () => {
    if (drawerOpen) {
      // Close drawer
      Animated.parallel([
        Animated.timing(drawerAnim, {
          toValue: -300,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(backdropFadeAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        })
      ]).start(() => setDrawerOpen(false));
    } else {
      // Open drawer
      setDrawerOpen(true);
      Animated.parallel([
        Animated.timing(drawerAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(backdropFadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        })
      ]).start();
    }
  };

  const fetchClasses = async (retryCount = 3, delay = 2000) => {
    try {
      setLoading(true);
      setError(null);

      for (let attempt = 0; attempt < retryCount; attempt++) {
        try {
          const classesRef = collection(db, 'classes');
          const q = query(classesRef);
          const querySnapshot = await getDocs(q);

          const classesData = [];
          querySnapshot.forEach((doc) => {
            classesData.push({ id: doc.id, ...doc.data() });
          });

          setClasses(classesData);
          return; // Success, exit the function
        } catch (error) {
          console.log(`Attempt ${attempt + 1} failed:`, error);
          if (attempt < retryCount - 1) {
            await new Promise(resolve => setTimeout(resolve, delay));
          } else {
            throw error; // Throw on final attempt
          }
        }
      }
    } catch (error) {
      console.error('Error fetching classes:', error);
      setError(translate('errorFetchingClasses'));
    } finally {
      setLoading(false);
    }
  };

  const fetchTeachers = async () => {
    try {
      const teachersRef = collection(db, 'users');
      const q = query(teachersRef, where('role', '==', 'teacher'));
      const querySnapshot = await getDocs(q);

      const teachersData = [];
      querySnapshot.forEach((doc) => {
        const data = doc.data();
        teachersData.push({
          id: doc.id,
          displayName: data.displayName,
          email: data.email,
          sections: data.sections || []
        });
      });

      setTeachers(teachersData);
    } catch (error) {
      console.error('Error fetching teachers:', error);
      setError(translate('errorFetchingTeachers'));
    }
  };

  const fetchStudentsForSection = async (classId, sectionName) => {
    try {
      console.log('Fetching students for section:', { classId, sectionName });
      const studentsRef = collection(db, 'users');
      const q = query(
        studentsRef,
        where('role', '==', 'student'),
        where('classId', '==', classId),
        where('sectionName', '==', sectionName)
      );

      const querySnapshot = await getDocs(q);
      console.log('Found students:', querySnapshot.size);

      const studentsData = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      // Update the sectionStudentsMap with the new data
      setSectionStudentsMap(prev => ({
        ...prev,
        [`${classId}-${sectionName}`]: studentsData
      }));

      return studentsData;
    } catch (error) {
      console.error('Error fetching students for section:', error);
      throw error;
    }
  };



  const handleSectionClick = (classData, section) => {
    setSelectedSectionData({
      classId: classData.id,
      sectionName: section.name
    });
    setShowSectionStudentsModal(true);
  };



  const handleAddClass = async () => {
    if (!validateForm()) return;

    try {
      setLoading(true);
      setError(null);

      // Format sections and groups data
      const validSections = formData.sections
        .filter(section => section.name.trim())
        .map(section => ({
          name: section.name.trim(),
          capacity: parseInt(section.capacity || '0', 10) // Convert string to number for storage
        }));

      const validGroups = formData.groups
        .filter(group => group.name.trim())
        .map(group => ({
          name: group.name.trim()
        }));

      const classData = {
        name: formData.name.trim().toUpperCase(),
        description: formData.description.trim(),
        academicYear: formData.academicYear,
        teacher: formData.teacher || '',
        sections: validSections,
        groups: validGroups,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      // Retry logic for adding class
      let retryCount = 3;
      let lastError = null;

      while (retryCount > 0) {
        try {
          const classesRef = collection(db, 'classes');
          await addDoc(classesRef, classData);
          console.log('Class added successfully');

          // Show success message
          alert(translate('classAddedSuccess'));

          setVisible(false);
          resetForm();
          await fetchClasses();
          return; // Success - exit the function
        } catch (error) {
          console.error(`Add class attempt ${4 - retryCount} failed:`, error);
          lastError = error;
          retryCount--;
          if (retryCount > 0) {
            await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second before retrying
          }
        }
      }

      // If we get here, all retries failed
      throw lastError;
    } catch (error) {
      console.error('Error adding class:', error);
      setError(translate('errorAddingClass'));
      // Show more detailed error in console
      if (error.code === 'permission-denied') {
        console.error('Permission denied error');
      } else if (error.code === 'unavailable') {
        console.error('Service unavailable error');
      } else {
        console.error('General error:', error.message);
      }
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateClass = async () => {
    if (!validateForm()) return;
    if (!selectedClass?.id) {
      setError(translate('errorUpdatingClass'));
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Format sections and groups data
      const validSections = formData.sections
        .filter(section => section.name.trim())
        .map(section => ({
          name: section.name.trim(),
          capacity: parseInt(section.capacity || '0', 10) // Convert string to number for storage
        }));

      const validGroups = formData.groups
        .filter(group => group.name.trim())
        .map(group => ({
          name: group.name.trim()
        }));

      const classData = {
        name: formData.name.trim().toUpperCase(),
        description: formData.description.trim(),
        academicYear: formData.academicYear,
        teacher: formData.teacher || '',
        sections: validSections,
        groups: validGroups,
        updatedAt: new Date().toISOString()
      };

      // Retry logic for updating class
      let retryCount = 3;
      let lastError = null;

      while (retryCount > 0) {
        try {
          const classRef = doc(db, 'classes', selectedClass.id);
          await updateDoc(classRef, classData);
          console.log('Class updated successfully');

          setVisible(false);
          resetForm();
          await fetchClasses();
          return; // Success - exit the function
        } catch (error) {
          console.error(`Update class attempt ${4 - retryCount} failed:`, error);
          lastError = error;
          retryCount--;
          if (retryCount > 0) {
            await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second before retrying
          }
        }
      }

      // If we get here, all retries failed
      throw lastError;
    } catch (error) {
      console.error('Error updating class:', error);
      setError(translate('errorUpdatingClass'));
      // Show more detailed error in console
      if (error.code === 'permission-denied') {
        console.error('Permission denied error');
      } else if (error.code === 'unavailable') {
        console.error('Service unavailable error');
      } else {
        console.error('General error:', error.message);
      }
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteClass = async (classId) => {
    try {
      setLoading(true);
      const studentsRef = collection(db, 'users');
      const q = query(studentsRef, where('classId', '==', classId));
      const querySnapshot = await getDocs(q);

      if (!querySnapshot.empty) {
        setError(translate('cannotDeleteClassWithStudents'));
        return;
      }

      await deleteDoc(doc(db, 'classes', classId));
      fetchClasses();
    } catch (error) {
      console.error('Error deleting class:', error);
      setError(translate('errorDeletingClass'));
    }
  };

  const handleAssignTeacher = async () => {
    if (!selectedClass || !selectedSection || !selectedTeacher || !selectedSubject) {
      setError(translate('errorAssigningTeacher'));
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Update teacher's sections in users collection
      const teacherRef = doc(db, 'users', selectedTeacher.id);
      const teacherDoc = await getDoc(teacherRef);

      if (!teacherDoc.exists()) {
        throw new Error('Teacher not found');
      }

      const teacherData = teacherDoc.data();
      const currentSections = teacherData.sections || [];

      // Add new section if not already assigned for this subject
      const newTeacherSection = {
        classId: selectedClass.id,
        className: selectedClass.name || '',
        sectionName: selectedSection.name || '',
        subject: selectedSubject
      };

      const existingTeacherSectionIndex = currentSections.findIndex(s =>
        s.classId === newTeacherSection.classId &&
        s.sectionName === newTeacherSection.sectionName &&
        s.subject === newTeacherSection.subject
      );

      let updatedTeacherSections;
      if (existingTeacherSectionIndex === -1) {
        // Add new section
        updatedTeacherSections = [...currentSections, newTeacherSection];
      } else {
        // Update existing section
        updatedTeacherSections = [...currentSections];
        updatedTeacherSections[existingTeacherSectionIndex] = newTeacherSection;
      }

      await updateDoc(teacherRef, {
        sections: updatedTeacherSections
      });

      // Update class section with teacher info
      const classRef = doc(db, 'classes', selectedClass.id);
      const classDoc = await getDoc(classRef);

      if (!classDoc.exists()) {
        throw new Error('Class not found');
      }

      const currentClass = classDoc.data();
      const updatedSections = currentClass.sections.map(section => {
        if (section.name === selectedSection.name) {
          const currentTeachers = Array.isArray(section.teachers) ? section.teachers : [];
          // Remove any existing teacher for this subject
          const filteredTeachers = currentTeachers.filter(t => t && t.subject !== selectedSubject);
          // Add the new teacher
          const updatedTeachers = [
            ...filteredTeachers,
            {
              id: selectedTeacher.id || '',
              name: selectedTeacher.displayName || '',
              subject: selectedSubject,
              email: selectedTeacher.email || ''
            }
          ];

          return {
            ...section,
            name: section.name || '',
            capacity: section.capacity || '0',
            teachers: updatedTeachers
          };
        }
        return {
          ...section,
          name: section.name || '',
          capacity: section.capacity || '0',
          teachers: Array.isArray(section.teachers) ? section.teachers : []
        };
      });

      const updatedClass = {
        ...currentClass,
        sections: updatedSections,
        updatedAt: new Date().toISOString()
      };

      await updateDoc(classRef, updatedClass);

      // Show success message
      alert(translate('teacherAssignedSuccess'));

      // Refresh data
      await Promise.all([
        fetchClasses(),
        fetchTeachers()
      ]);

      setShowTeacherAssignModal(false);
      setSelectedTeacher(null);
      setTeacherSearchQuery('');
      setSelectedSubject('');
    } catch (error) {
      console.error('Error assigning teacher:', error);
      setError(translate('errorAssigningTeacher'));
    } finally {
      setLoading(false);
    }
  };

  const validateForm = () => {
    if (!formData.name.trim()) {
      setError(translate('classNameRequired'));
      return false;
    }
    if (!formData.description.trim()) {
      setError(translate('descriptionRequired'));
      return false;
    }
    if (!formData.academicYear.trim()) {
      setError(translate('academicYearRequired'));
      return false;
    }
    if (!formData.sections.some(section => section.name.trim())) {
      setError(translate('sectionRequired'));
      return false;
    }
    if (!formData.sections.every(section => section.name.trim() && section.capacity.trim())) {
      setError(translate('sectionsValidation'));
      return false;
    }
    if (!formData.groups.some(group => group.name.trim())) {
      setError(translate('groupRequired'));
      return false;
    }
    return true;
  };

  const resetForm = () => {
    setFormData(defaultFormData);
    setSelectedClass(null);
    setError(null);
  };



  const renderSectionCard = (section, index, classItem) => {
    const sectionKey = `${classItem.id}-${section.name}`;
    const students = sectionStudentsMap[sectionKey] || [];
    const occupancyPercentage = (students.length / section.capacity) * 100;
    const occupancyColor = occupancyPercentage > 90 ? '#ff6b6b' :
                          occupancyPercentage > 70 ? '#ffd93d' : '#51cf66';

    // Set different border colors based on occupancy
    const borderColor = occupancyPercentage > 90 ? '#ff6b6b' :
                        occupancyPercentage > 70 ? '#ffd93d' : '#51cf66';

    // Status text and icon based on occupancy
    let statusText = translate('lowOccupancy');
    let statusIcon = 'check-circle';

    if (occupancyPercentage > 90) {
      statusText = translate('highOccupancy');
      statusIcon = 'alert-circle';
    } else if (occupancyPercentage > 70) {
      statusText = translate('mediumOccupancy');
      statusIcon = 'alert';
    }

    // Get top students in this section (if any)
    const topStudents = [...(students || [])]
      .sort((a, b) => {
        const aAvg = a.average || 0;
        const bAvg = b.average || 0;
        return bAvg - aAvg;
      })
      .slice(0, 3);

    return (
      <TouchableOpacity
        key={section.name}
        onPress={() => handleSectionClick(classItem, section)}
        activeOpacity={0.7}
      >
        <Animatable.View animation="fadeInUp" duration={500} delay={index * 100}>
          <Surface style={[styles.sectionCard, { borderLeftColor: borderColor }]}>
            <View style={[styles.sectionHeader, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
              <View style={[styles.sectionHeaderLeft, { alignItems: isRTL ? 'flex-end' : 'flex-start' }]}>
                <View style={styles.sectionNameContainer}>
                  <MaterialCommunityIcons name="view-grid" size={20} color={'#1976d2'} style={styles.sectionIcon} />
                  <Text style={[styles.sectionName, getTextStyle()]}>
                    {translate('section')} {section.name}
                  </Text>
                </View>

                <View style={styles.capacityContainer}>
                  <View style={styles.capacityLabelContainer}>
                    <Text style={[styles.capacityLabel, getTextStyle()]}>
                      {translate('capacity')}
                    </Text>
                    <Badge style={[styles.statusBadge, { backgroundColor: 'rgba(240, 240, 240, 0.8)', color: occupancyColor }]}>
                      <MaterialCommunityIcons name={statusIcon} size={12} color={occupancyColor} />
                      {' '}{statusText}
                    </Badge>
                  </View>

                  <View style={styles.progressBarContainer}>
                    <View style={styles.progressBar}>
                      <Animatable.View
                        animation="fadeIn"
                        duration={1000}
                        style={[
                          styles.progressFill,
                          {
                            width: `${Math.min(occupancyPercentage, 100)}%`,
                            backgroundColor: occupancyColor
                          }
                        ]}
                      />
                    </View>
                    <Text style={[styles.capacityText, getTextStyle()]}>
                      {translate('studentsCount', { current: students.length, capacity: section.capacity })}
                    </Text>
                  </View>
                </View>
              </View>

              <View style={[styles.sectionActions, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
                <Animatable.View animation="fadeIn" duration={500} delay={300}>
                  <IconButton
                    icon="account-group"
                    size={24}
                    color={'#1976d2'}
                    onPress={() => handleSectionClick(classItem, section)}
                    style={styles.actionButton}
                    animated={true}
                  />
                </Animatable.View>
                <Animatable.View animation="fadeIn" duration={500} delay={400}>
                  <IconButton
                    icon="account-plus"
                    size={24}
                    color={'#1976d2'}
                    onPress={(e) => {
                      e.stopPropagation();
                      setSelectedClass(classItem);
                      setSelectedSection(section);
                      setShowTeacherAssignModal(true);
                    }}
                    style={styles.actionButton}
                    animated={true}
                  />
                </Animatable.View>
              </View>
            </View>

            {/* Student preview section */}
            {students.length > 0 && (
              <Animatable.View animation="fadeIn" duration={800} delay={500}>
                <View style={styles.studentPreviewContainer}>
                  <View style={styles.studentPreviewHeader}>
                    <MaterialCommunityIcons name="account-group" size={18} color={'#1976d2'} />
                    <Text style={[styles.studentPreviewTitle, getTextStyle()]}>{translate('students')}</Text>
                    <Badge style={styles.studentCountBadge}>{students.length}</Badge>
                  </View>

                  <View style={styles.studentAvatarsContainer}>
                    {students.slice(0, 5).map((student, idx) => (
                      <Animatable.View
                        key={student.id}
                        animation="zoomIn"
                        duration={300}
                        delay={idx * 100 + 200}
                        style={[styles.studentAvatarWrapper, { zIndex: 10 - idx }]}
                      >
                        <View style={styles.studentAvatar}>
                          <Text style={styles.avatarText}>
                            {student.firstName?.[0]}{student.lastName?.[0]}
                          </Text>
                        </View>
                      </Animatable.View>
                    ))}

                    {students.length > 5 && (
                      <Animatable.View
                        animation="zoomIn"
                        duration={300}
                        delay={700}
                        style={[styles.studentAvatarWrapper, { zIndex: 5 }]}
                      >
                        <View style={[styles.studentAvatar, styles.moreStudentsAvatar]}>
                          <Text style={styles.avatarText}>+{students.length - 5}</Text>
                        </View>
                      </Animatable.View>
                    )}
                  </View>

                  {topStudents.length > 0 && (
                    <View style={styles.topStudentsContainer}>
                      <Text style={[styles.topStudentsTitle, getTextStyle()]}>{translate('topPerformers')}:</Text>
                      <View style={styles.topStudentsList}>
                        {topStudents.map((student, idx) => (
                          <Chip
                            key={student.id}
                            style={styles.topStudentChip}
                            icon="star"
                            mode="outlined"
                          >
                            {student.firstName} {student.lastName}
                          </Chip>
                        ))}
                      </View>
                    </View>
                  )}
                </View>
              </Animatable.View>
            )}

            <View style={styles.teachersContainer}>
              <View style={styles.teachersTitleContainer}>
                <MaterialCommunityIcons name="school" size={18} color={'#1976d2'} />
                <Text style={[styles.teachersTitle, getTextStyle()]}>{translate('teachersTitle')}</Text>
                <Badge style={styles.teacherCountBadge}>{section.teachers?.length || 0}</Badge>
              </View>

              <ScrollView
                horizontal
                showsHorizontalScrollIndicator={false}
                style={styles.teacherChips}
                contentContainerStyle={{ flexDirection: isRTL ? 'row-reverse' : 'row' }}
              >
                {section.teachers?.map((teacher, idx) => (
                  <Animatable.View key={`${teacher.id}-${idx}`} animation="fadeIn" duration={300} delay={idx * 100}>
                    <Chip
                      style={styles.teacherChip}
                      icon="school"
                      mode="outlined"
                      onPress={() => alert(`${teacher.name} - ${teacher.subject}`)}
                    >
                      {`${teacher.name} (${teacher.subject})`}
                    </Chip>
                  </Animatable.View>
                ))}
                {(!section.teachers || section.teachers.length === 0) && (
                  <Animatable.View animation="fadeIn" duration={500}>
                    <Surface style={styles.noTeachersContainer}>
                      <MaterialCommunityIcons name="school-outline" size={24} color={'#9e9e9e'} />
                      <Text style={[styles.noTeachers, getTextStyle()]}>{translate('noTeachers')}</Text>
                      <Button
                        mode="text"
                        compact
                        icon="plus"
                        onPress={(e) => {
                          e.stopPropagation();
                          setSelectedClass(classItem);
                          setSelectedSection(section);
                          setShowTeacherAssignModal(true);
                        }}
                        style={styles.addTeacherButton}
                      >
                        {translate('addTeacher')}
                      </Button>
                    </Surface>
                  </Animatable.View>
                )}
              </ScrollView>
            </View>

            <View style={styles.viewAllButtonContainer}>
              <Button
                mode="text"
                icon="chevron-right"
                onPress={() => handleSectionClick(classItem, section)}
                contentStyle={{ flexDirection: isRTL ? 'row-reverse' : 'row' }}
              >
                {translate('viewStudents')}
              </Button>
            </View>
          </Surface>
        </Animatable.View>
      </TouchableOpacity>
    );
  };

  const renderClassCard = (classItem) => {
    if (!classItem) return null;

    const totalStudents = classItem.sections?.reduce((total, section) =>
      total + (sectionStudentsMap[`${classItem.id}-${section.name}`]?.length || 0), 0);
    const totalTeachers = classItem.sections?.filter(s => s.teachers?.length > 0).length || 0;

    return (
      <Animatable.View animation="fadeIn" duration={500}>
        <Surface style={styles.classCard}>
          <LinearGradient
            colors={['rgba(76, 175, 80, 0.2)', '#ffffff']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
            style={{ borderRadius: 12 }}
          >
            <View style={styles.cardBadge}>
              <Text style={styles.cardBadgeText}>{classItem.academicYear}</Text>
            </View>

            <View style={[styles.cardHeader, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
              <View style={[styles.headerLeft, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
                <Title style={[styles.className, getTextStyle()]}>
                  {translate('class')} {classItem.name}
                </Title>
              </View>
              <View style={styles.headerActions}>
                <IconButton
                  icon="pencil"
                  size={20}
                  color={'#1976d2'}
                  onPress={() => {
                    setSelectedClass(classItem);
                    setFormData({
                      name: classItem.name || '',
                      description: classItem.description || '',
                      academicYear: classItem.academicYear || new Date().getFullYear().toString(),
                      teacher: classItem.teacher || '',
                      sections: classItem.sections || [],
                      groups: classItem.groups || []
                    });
                    setVisible(true);
                  }}
                  style={styles.actionIcon}
                />
                <IconButton
                  icon="dots-vertical"
                  size={20}
                  onPress={() => {
                    setSelectedMenuItem(classItem);
                    setMenuVisible(true);
                  }}
                  style={styles.menuButton}
                />
              </View>
            </View>

            <View style={styles.cardContent}>
              <View style={[styles.statsContainer, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
                <Animatable.View animation="fadeInUp" duration={500} delay={100} style={styles.statItemWrapper}>
                  <Surface style={styles.statItemSurface}>
                  <MaterialCommunityIcons name="account-group" size={24} color={'#1976d2'} style={styles.statIcon} />
                    <View style={[styles.statItem, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
                      <View style={styles.statTextContainer}>
                        <Text style={styles.statValue}>{totalStudents}</Text>
                        <Text style={[styles.statLabel, getTextStyle()]}>{translate('students')}</Text>
                      </View>
                    </View>
                  </Surface>
                </Animatable.View>

                <Animatable.View animation="fadeInUp" duration={500} delay={200} style={styles.statItemWrapper}>
                  <Surface style={styles.statItemSurface}>
                  <MaterialCommunityIcons name="school" size={24} color={'#1976d2'} style={styles.statIcon} />
                    <View style={[styles.statItem, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
                      <View style={styles.statTextContainer}>
                        <Text style={styles.statValue}>{totalTeachers}</Text>
                        <Text style={[styles.statLabel, getTextStyle()]}>{translate('teachers')}</Text>
                      </View>
                    </View>
                  </Surface>
                </Animatable.View>

                <Animatable.View animation="fadeInUp" duration={500} delay={300} style={styles.statItemWrapper}>
                  <Surface style={styles.statItemSurface}>
                  <MaterialCommunityIcons name="book-open-variant" size={24} color={'#1976d2'} style={styles.statIcon} />
                    <View style={[styles.statItem, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
                      <View style={styles.statTextContainer}>
                        <Text style={styles.statValue}>{classItem.sections?.length || 0}</Text>
                        <Text style={[styles.statLabel, getTextStyle()]}>{translate('sections')}</Text>
                      </View>
                    </View>
                  </Surface>
                </Animatable.View>
              </View>

              {classItem.description && (
                <Animatable.View animation="fadeIn" duration={500} delay={400}>
                  <Surface style={styles.descriptionSurface}>
                    <View style={styles.descriptionHeader}>
                      <MaterialCommunityIcons name="information" size={20} color={'#1976d2'} />
                      <Text style={styles.descriptionTitle}>{translate('description')}</Text>
                    </View>
                    <Text style={[styles.description, getTextStyle()]}>{classItem.description}</Text>
                  </Surface>
                </Animatable.View>
              )}

              <Animatable.View animation="fadeIn" duration={500} delay={500}>
                <View style={styles.sectionsHeader}>
                  <View style={styles.sectionTitleContainer}>
                    <MaterialCommunityIcons name="view-grid" size={20} color={'#1976d2'} />
                    <Text style={[styles.sectionTitle, getTextStyle()]}>{translate('sections')}</Text>
                  </View>
                  <Badge style={styles.sectionCountBadge}>{classItem.sections?.length || 0}</Badge>
                </View>
                <View style={styles.sectionGrid}>
                  {Array.isArray(classItem.sections) && classItem.sections.map((section, index) => (
                    renderSectionCard(section, index, classItem)
                  ))}
                </View>
                {(!Array.isArray(classItem.sections) || classItem.sections.length === 0) && (
                  <Surface style={styles.emptyStateContainer}>
                    <MaterialCommunityIcons name="view-grid-plus" size={40} color={'#9e9e9e'} />
                    <Text style={[styles.noSectionsText, getTextStyle()]}>{translate('noSections')}</Text>
                    <Button
                      mode="outlined"
                      icon="plus"
                      onPress={() => {
                        setSelectedClass(classItem);
                        setFormData({
                          name: classItem.name || '',
                          description: classItem.description || '',
                          academicYear: classItem.academicYear || new Date().getFullYear().toString(),
                          teacher: classItem.teacher || '',
                          sections: classItem.sections || [],
                          groups: classItem.groups || []
                        });
                        setVisible(true);
                      }}
                      style={styles.addSectionButton}
                    >
                      {translate('addSection')}
                    </Button>
                  </Surface>
                )}
              </Animatable.View>
            </View>
          </LinearGradient>
        </Surface>
      </Animatable.View>
    );
  };

  const initializeDefaultSubjects = async () => {
    try {
      const subjectsRef = collection(db, 'subjects');
      const subjectsSnapshot = await getDocs(subjectsRef);

      if (subjectsSnapshot.empty) {
        const defaultSubjects = [
          { name: 'Mathematics', code: 'MATH', description: 'Mathematics curriculum' },
          { name: 'English', code: 'ENG', description: 'English language and literature' },
          { name: 'Science', code: 'SCI', description: 'General Science' },
          { name: 'History', code: 'HIST', description: 'History studies' },
          { name: 'Geography', code: 'GEO', description: 'Geography studies' },
          { name: 'Physics', code: 'PHY', description: 'Physics curriculum' },
          { name: 'Chemistry', code: 'CHEM', description: 'Chemistry studies' },
          { name: 'Biology', code: 'BIO', description: 'Biology curriculum' },
          { name: 'Computer Science', code: 'CS', description: 'Computer Science and programming' },
          { name: 'Physical Education', code: 'PE', description: 'Physical Education and sports' },
          { name: 'Art', code: 'ART', description: 'Art and creative studies' },
          { name: 'Music', code: 'MUS', description: 'Music education' }
        ];

        const batch = writeBatch(db);
        defaultSubjects.forEach(subject => {
          const newSubjectRef = doc(subjectsRef);
          batch.set(newSubjectRef, {
            ...subject,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          });
        });

        await batch.commit();
        console.log('Default subjects initialized');
        await fetchSubjects(); // Refresh the subjects list
      }
    } catch (error) {
      console.error('Error initializing default subjects:', error);
      setError('Failed to initialize default subjects');
    }
  };

  const fetchSubjects = async () => {
    try {
      setSubjectsLoading(true);
      const subjectsRef = collection(db, 'subjects');
      const subjectsSnapshot = await getDocs(subjectsRef);
      const subjectsList = subjectsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      if (subjectsList.length === 0) {
        await initializeDefaultSubjects();
      } else {
        setSubjects(subjectsList);
      }
    } catch (error) {
      console.error('Error fetching subjects:', error);
      setError('Failed to fetch subjects');
    } finally {
      setSubjectsLoading(false);
    }
  };

  useEffect(() => {
    fetchClasses();
    fetchTeachers();
    fetchSubjects();
  }, []);

  useEffect(() => {
    const loadAllSectionStudents = async () => {
      for (const classItem of classes) {
        if (Array.isArray(classItem.sections)) {
          for (const section of classItem.sections) {
            await fetchStudentsForSection(classItem.id, section.name);
          }
        }
      }
    };

    if (classes.length > 0) {
      loadAllSectionStudents();
    }
  }, [classes]);

  const filteredTeachers = teachers.filter(teacher =>
    teacher.displayName?.toLowerCase().includes(teacherSearchQuery.toLowerCase()) ||
    teacher.email?.toLowerCase().includes(teacherSearchQuery.toLowerCase())
  );

  const renderTeacherAssignModal = () => {
    return (
      <Portal>
        <Modal
          visible={showTeacherAssignModal}
          onDismiss={() => {
            setShowTeacherAssignModal(false);
            setSelectedTeacher(null);
            setTeacherSearchQuery('');
            setSelectedSubject('');
          }}
          contentContainerStyle={styles.modalContent}
        >
          <Animatable.View animation="fadeIn" duration={500}>
            <Title style={[styles.modalTitle, getTextStyle()]}>{translate('assignTeacherToSection')}</Title>
            <Text style={[styles.modalSubtitle, getTextStyle()]}>
              {selectedClass?.name} - {translate('section')} {selectedSection?.name}
            </Text>

            <View style={styles.subjectSelection}>
              <Text style={[styles.sectionLabel, getTextStyle()]}>{translate('selectSubject')}</Text>
              {subjectsLoading ? (
                <ActivityIndicator size="small" color={'#1976d2'} style={styles.subjectLoader} />
              ) : subjects.length > 0 ? (
                <ScrollView
                  horizontal
                  showsHorizontalScrollIndicator={false}
                  style={styles.subjectList}
                  contentContainerStyle={{ flexDirection: isRTL ? 'row-reverse' : 'row' }}
                >
                  {subjects.map((subject, index) => (
                    <Animatable.View
                      key={subject.id}
                      animation="fadeIn"
                      duration={300}
                      delay={index * 50}
                    >
                      <Chip
                        selected={selectedSubject === subject.name}
                        onPress={() => setSelectedSubject(subject.name)}
                        style={styles.subjectChip}
                        mode={selectedSubject === subject.name ? 'flat' : 'outlined'}
                      >
                        {subject.name}
                      </Chip>
                    </Animatable.View>
                  ))}
                </ScrollView>
              ) : (
                <View style={styles.noSubjectsContainer}>
                  <Text style={[styles.noSubjectsText, getTextStyle()]}>{translate('noSubjectsAvailable')}</Text>
                  <Button
                    mode="contained"
                    onPress={fetchSubjects}
                    style={styles.retryButton}
                    icon="refresh"
                  >
                    {translate('retry')}
                  </Button>
                </View>
              )}
            </View>

            <CustomInput
              label={translate('searchTeachers')}
              value={teacherSearchQuery}
              onChangeText={setTeacherSearchQuery}
              style={styles.searchInput}
              left={<TextInput.Icon icon="magnify" />}
            />

            <ScrollView style={styles.teachersList}>
              {filteredTeachers.map((teacher, index) => (
                <Animatable.View
                  key={teacher.id}
                  animation="fadeInUp"
                  duration={300}
                  delay={index * 50}
                >
                  <TouchableOpacity
                    onPress={() => setSelectedTeacher(teacher)}
                    style={[
                      styles.teacherItem,
                      selectedTeacher?.id === teacher.id && styles.selectedTeacherItem
                    ]}
                  >
                    <View>
                      <Text style={[styles.teacherName, getTextStyle()]}>{teacher.displayName}</Text>
                      <Text style={[styles.teacherEmail, getTextStyle()]}>{teacher.email}</Text>
                      {teacher.sections?.length > 0 && (
                        <Text style={[styles.teacherSections, getTextStyle()]}>
                          {translate('teachingCount', { count: teacher.sections.length })}
                        </Text>
                      )}
                    </View>
                    {selectedTeacher?.id === teacher.id && (
                      <IconButton icon="check" size={20} color={'#1976d2'} />
                    )}
                  </TouchableOpacity>
                </Animatable.View>
              ))}

              {filteredTeachers.length === 0 && (
                <View style={styles.noSubjectsContainer}>
                  <Text style={[styles.noSubjectsText, getTextStyle()]}>{translate('noTeachers')}</Text>
                </View>
              )}
            </ScrollView>

            <View style={[styles.modalActions, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
              <Button
                onPress={() => {
                  setShowTeacherAssignModal(false);
                  setSelectedTeacher(null);
                  setTeacherSearchQuery('');
                  setSelectedSubject('');
                }}
                style={styles.modalButton}
                icon="close"
              >
                {translate('cancel')}
              </Button>
              <Button
                mode="contained"
                onPress={handleAssignTeacher}
                disabled={!selectedTeacher || !selectedSubject || loading}
                loading={loading}
                style={styles.modalButton}
                icon="account-plus"
              >
                {translate('assign')}
              </Button>
            </View>
          </Animatable.View>
        </Modal>
      </Portal>
    );
  };

  const renderStudentsModal = () => {
    if (!showStudentsModal) return null;

    return (
      <Portal>
        <Modal
          visible={showStudentsModal}
          onDismiss={() => setShowStudentsModal(false)}
          contentContainerStyle={styles.fullScreenModal}
        >
          <View style={[styles.modalHeader, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
            <View style={styles.modalHeaderLeft}>
              <Title style={[styles.modalTitle, getTextStyle()]}>{selectedClass?.name}</Title>
              <Text style={[styles.modalSubtitle, getTextStyle()]}>
                {translate('section')} {selectedSection?.name}
              </Text>
            </View>
            <IconButton
              icon="close"
              size={24}
              onPress={() => setShowStudentsModal(false)}
            />
          </View>

          {studentsLoading ? (
            <View style={styles.loaderContainer}>
              <ActivityIndicator size="large" color={'#1976d2'} />
              <Text style={[styles.loadingText, getTextStyle()]}>{translate('loadingStudents')}</Text>
            </View>
          ) : (
            <Animatable.View
              animation="fadeIn"
              duration={500}
              style={styles.modalContent}
            >
              <View style={[styles.searchContainer, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
                <IconButton icon="magnify" size={24} />
                <TextInput
                  placeholder={translate('searchStudents')}
                  value={searchQuery}
                  onChangeText={setSearchQuery}
                  style={[styles.searchInput, getTextStyle()]}
                />
              </View>

              <ScrollView style={styles.studentList}>
                <DataTable>
                  <DataTable.Header style={styles.tableHeader}>
                    <DataTable.Title style={{ flexDirection: isRTL ? 'row-reverse' : 'row' }}>
                      <Text style={getTextStyle()}>{translate('name')} <Text style={{ color: '#1976d2', fontSize: 10 }}>(Tap for details)</Text></Text>
                    </DataTable.Title>
                    <DataTable.Title style={{ flexDirection: isRTL ? 'row-reverse' : 'row' }}>
                      <Text style={getTextStyle()}>{translate('rollNumber')}</Text>
                    </DataTable.Title>
                    <DataTable.Title style={{ flexDirection: isRTL ? 'row-reverse' : 'row' }}>
                      <Text style={getTextStyle()}>{translate('email')}</Text>
                    </DataTable.Title>
                    <DataTable.Title numeric style={{ flexDirection: isRTL ? 'row-reverse' : 'row' }}>
                      <Text style={getTextStyle()}>{translate('averageScore')}</Text>
                    </DataTable.Title>
                    <DataTable.Title style={{ width: 50 }}>
                      <Text style={getTextStyle()}>{translate('actions')}</Text>
                    </DataTable.Title>
                  </DataTable.Header>

                  {selectedStudents
                    .filter(student =>
                      `${student.firstName} ${student.lastName} ${student.email}`
                        .toLowerCase()
                        .includes(searchQuery.toLowerCase())
                    )
                    .map((student, index) => (
                      <Animatable.View
                        key={student.id}
                        animation="fadeInUp"
                        duration={300}
                        delay={index * 50}
                      >
                        <DataTable.Row
                          style={[styles.tableRow, styles.clickableRow]}
                          onPress={() => {
                            console.log('Navigating to student details:', student.id);
                            navigation.navigate('StudentDetails', { studentId: student.id });
                          }}
                        >
                          <DataTable.Cell>
                            <View style={[styles.studentNameCell, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
                              <View style={styles.studentAvatar}>
                                <Text style={styles.avatarText}>
                                  {student.firstName[0]}{student.lastName[0]}
                                </Text>
                              </View>
                              <Text style={[styles.studentName, getTextStyle()]}>
                                {`${student.firstName} ${student.lastName}`}
                              </Text>
                            </View>
                          </DataTable.Cell>
                          <DataTable.Cell>
                            <Text style={getTextStyle()}>{student.rollNumber || 'N/A'}</Text>
                          </DataTable.Cell>
                          <DataTable.Cell>
                            <Text style={getTextStyle()}>{student.email}</Text>
                          </DataTable.Cell>
                          <DataTable.Cell numeric>
                            <Text style={[
                              styles.averageScore,
                              getTextStyle(),
                              { color: getScoreColor(student.average) }
                            ]}>
                              {student.average || 'N/A'}
                            </Text>
                          </DataTable.Cell>
                          <DataTable.Cell style={{ width: 50 }}>
                            <IconButton
                              icon="chevron-right"
                              size={20}
                              color={'#1976d2'}
                              onPress={() => navigation.navigate('StudentDetails', { studentId: student.id })}
                            />
                          </DataTable.Cell>
                        </DataTable.Row>
                      </Animatable.View>
                    ))}

                  {selectedStudents.length === 0 && (
                    <DataTable.Row style={styles.emptyRow}>
                      <DataTable.Cell style={styles.emptyCell}>
                        <Text style={[styles.emptyText, getTextStyle()]}>{translate('noStudentsFound')}</Text>
                      </DataTable.Cell>
                    </DataTable.Row>
                  )}
                </DataTable>
              </ScrollView>
            </Animatable.View>
          )}
        </Modal>
      </Portal>
    );
  };

  const getScoreColor = (score) => {
    if (!score || score === 'N/A') return '#666';
    const numScore = parseFloat(score);
    if (numScore >= 90) return '#51cf66';
    if (numScore >= 70) return '#ffd93d';
    return '#ff6b6b';
  };



  return (
    <PaperProvider theme={DefaultTheme}>
      <StatusBar style="auto" />
      <SafeAreaView style={styles.safeArea}>
        {/* Sidebar */}
        <AdminSidebar
          drawerAnim={drawerAnim}
          activeSidebarItem={activeSidebarItem}
          setActiveSidebarItem={setActiveSidebarItem}
          toggleDrawer={toggleDrawer}
        />

        {/* Backdrop */}
        <SidebarBackdrop
          visible={drawerOpen}
          onPress={toggleDrawer}
          fadeAnim={backdropFadeAnim}
        />

        {/* Admin App Header */}
        <AdminAppHeader
          title={t('classManagement.title')}
          onMenuPress={toggleDrawer}
        />

        <View style={styles.container}>
          {loading ? (
            <View style={styles.loaderContainer}>
              <ActivityIndicator size="large" color={'#1976d2'} style={styles.loader} />
              <Text style={styles.loadingText}>{translate('loading')}</Text>
            </View>
          ) : error ? (
            <Animatable.View
              animation="fadeIn"
              duration={500}
              style={styles.errorContainer}
            >
              <Text style={styles.errorText}>{error}</Text>
              <CustomButton
                mode="contained"
                onPress={fetchClasses}
              >
                {translate('retry')}
              </CustomButton>
            </Animatable.View>
          ) : (
            <ScrollView style={styles.list}>
              {classes.map((classItem, index) => (
                <Animatable.View
                  key={classItem.id}
                  animation="fadeInUp"
                  duration={500}
                  delay={index * 100}
                >
                  {renderClassCard(classItem)}
                </Animatable.View>
              ))}
            </ScrollView>
          )}

          <Portal>
            <Modal
              visible={visible}
              onDismiss={() => {
                setVisible(false);
                resetForm();
              }}
              contentContainerStyle={styles.modalContent}
            >
              <Animatable.View animation="fadeIn" duration={500}>
                <ScrollView>
                  <Title style={[styles.modalTitle, getTextStyle()]}>
                    {selectedClass ? translate('editClass') : translate('addNewClass')}
                  </Title>

                  <CustomInput
                    label={translate('className')}
                    value={formData.name}
                    onChangeText={(text) => setFormData({ ...formData, name: text })}
                    autoCapitalize="characters"
                    placeholder={translate('className')}
                    left={<TextInput.Icon icon="format-title" />}
                  />

                  <CustomInput
                    label={translate('classDescription')}
                    value={formData.description}
                    onChangeText={(text) => setFormData({ ...formData, description: text })}
                    multiline
                    placeholder={translate('classDescription')}
                    left={<TextInput.Icon icon="text-box-outline" />}
                  />

                  <CustomInput
                    label={translate('classAcademicYear')}
                    value={formData.academicYear}
                    onChangeText={(text) => {
                      const numericValue = text.replace(/[^0-9]/g, '');
                      setFormData({ ...formData, academicYear: numericValue });
                    }}
                    keyboardType="numeric"
                    placeholder="YYYY"
                    left={<TextInput.Icon icon="calendar" />}
                  />

                  <Title style={[styles.sectionTitle, getTextStyle()]}>{translate('sections')}</Title>
                  {formData.sections.map((section, index) => (
                    <Animatable.View
                      key={index}
                      animation="fadeInRight"
                      duration={300}
                      delay={index * 100}
                      style={styles.sectionForm}
                    >
                      <CustomInput
                        label={`${translate('sectionName')} ${index + 1}`}
                        value={section.name}
                        onChangeText={(text) => {
                          const newSections = [...formData.sections];
                          newSections[index].name = text;
                          setFormData({ ...formData, sections: newSections });
                        }}
                        style={styles.sectionInput}
                        placeholder={translate('sectionName')}
                        left={<TextInput.Icon icon="alpha-s-box" />}
                      />
                      <CustomInput
                        label={translate('capacity')}
                        value={section.capacity}
                        onChangeText={(text) => {
                          const newSections = [...formData.sections];
                          newSections[index].capacity = text;
                          setFormData({ ...formData, sections: newSections });
                        }}
                        keyboardType="numeric"
                        style={styles.capacityInput}
                        placeholder="0"
                        left={<TextInput.Icon icon="account-group" />}
                      />
                    </Animatable.View>
                  ))}
                  <CustomButton
                    mode="outlined"
                    onPress={() => {
                      setFormData({
                        ...formData,
                        sections: [...formData.sections, { name: '', capacity: '', teachers: [] }]
                      });
                    }}
                    style={styles.addButton}
                    icon="plus"
                  >
                    {translate('addSection')}
                  </CustomButton>

                  <Title style={[styles.sectionTitle, getTextStyle()]}>{translate('groups')}</Title>
                  {formData.groups.map((group, index) => (
                    <Animatable.View
                      key={index}
                      animation="fadeInRight"
                      duration={300}
                      delay={index * 100}
                      style={styles.groupForm}
                    >
                      <CustomInput
                        label={`${translate('groupName')} ${index + 1}`}
                        value={group.name}
                        onChangeText={(text) => {
                          const newGroups = [...formData.groups];
                          newGroups[index].name = text;
                          setFormData({ ...formData, groups: newGroups });
                        }}
                        style={styles.groupInput}
                        placeholder={translate('groupName')}
                        left={<TextInput.Icon icon="account-group" />}
                      />
                    </Animatable.View>
                  ))}
                  <CustomButton
                    mode="outlined"
                    onPress={() => {
                      setFormData({
                        ...formData,
                        groups: [...formData.groups, { name: '' }]
                      });
                    }}
                    style={styles.addButton}
                    icon="plus"
                  >
                    {translate('addGroup')}
                  </CustomButton>

                  <View style={styles.modalButtons}>
                    <CustomButton
                      mode="contained"
                      onPress={selectedClass ? handleUpdateClass : handleAddClass}
                      loading={loading}
                      disabled={loading}
                      icon={selectedClass ? "content-save-edit" : "content-save"}
                    >
                      {selectedClass ? translate('update') : translate('save')}
                    </CustomButton>

                    <CustomButton
                      mode="outlined"
                      onPress={() => {
                        setVisible(false);
                        resetForm();
                      }}
                      disabled={loading}
                      icon="close"
                    >
                      {translate('cancel')}
                    </CustomButton>
                  </View>
                </ScrollView>
              </Animatable.View>
            </Modal>
          </Portal>

          {renderTeacherAssignModal()}
          {renderStudentsModal()}
          <Portal>
            <Modal
              visible={showSectionStudentsModal}
              onDismiss={() => setShowSectionStudentsModal(false)}
              contentContainerStyle={styles.modalContainer}
            >
              {selectedSectionData && (
                <SectionStudentsList
                  classId={selectedSectionData.classId}
                  sectionName={selectedSectionData.sectionName}
                  onClose={() => setShowSectionStudentsModal(false)}
                />
              )}
            </Modal>
          </Portal>
          <Menu
            visible={menuVisible}
            onDismiss={() => setMenuVisible(false)}
            anchor={selectedMenuItem ? { x: 0, y: 0 } : undefined}
          >
            <Menu.Item
              onPress={() => {
                setSelectedClass(selectedMenuItem);
                setFormData({
                  name: selectedMenuItem.name || '',
                  description: selectedMenuItem.description || '',
                  academicYear: selectedMenuItem.academicYear || new Date().getFullYear().toString(),
                  teacher: selectedMenuItem.teacher || '',
                  sections: selectedMenuItem.sections || [],
                  groups: selectedMenuItem.groups || []
                });
                setMenuVisible(false);
                setVisible(true);
              }}
              title={translate('editClass')}
              leadingIcon="pencil"
            />
            <Menu.Item
              onPress={() => {
                handleDeleteClass(selectedMenuItem.id);
                setMenuVisible(false);
              }}
              title={translate('deleteClass')}
              leadingIcon="delete"
            />
          </Menu>

          <FAB
            style={styles.fab}
            icon="plus"
            label={translate('addNewClass')}
            onPress={() => {
              resetForm();
              setVisible(true);
            }}
            disabled={loading}
            color="white"
            animated={true}
          />
        </View>
      </SafeAreaView>
    </PaperProvider>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  container: {
    flex: 1,
    padding: 16,
    paddingTop: 0,
  },
  title: {
    marginBottom: 16,
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    color: '#333',
  },
  list: {
    flex: 1,
  },
  classCard: {
    marginBottom: 16,
    elevation: 4,
    borderRadius: 12,
    overflow: 'hidden',
  },
  cardBadge: {
    position: 'absolute',
    top: 0,
    right: 0,
    backgroundColor: '#4CAF50',
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderBottomLeftRadius: 12,
    zIndex: 1,
  },
  cardBadgeText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 12,
  },
  cardContent: {
    padding: 16,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionIcon: {
    marginHorizontal: 4,
  },
  statItemWrapper: {
    flex: 1,
    marginHorizontal: 4,
  },
  statItem: {
    justifyContent: 'center',
  },
  statItemSurface: {
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 2,
    borderRadius: 8,
    elevation: 2,
    backgroundColor: '#fff',

  },
  statIcon: {
    marginRight: 4,
  },
  statTextContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  statValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginLeft: 30,

  },
  descriptionSurface: {
    marginTop: 16,
    padding: 16,
    borderRadius: 8,
    elevation: 2,
    backgroundColor: '#fff',
  },
  descriptionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  descriptionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
    color: '#333',
  },
  sectionsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 16,
    marginBottom: 8,
  },
  sectionTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  sectionCountBadge: {
    backgroundColor: '#4CAF50',
    color: 'white',
  },
  emptyStateContainer: {
    padding: 24,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,
    backgroundColor: '#f9f9f9',
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderStyle: 'dashed',
  },
  addSectionButton: {
    marginTop: 16,
  },
  sectionCard: {
    padding: 16,
    elevation: 3,
    backgroundColor: '#fff',
    borderRadius: 8,
    width: '100%',
    marginBottom: 12,
    borderLeftWidth: 4,
    borderLeftColor: '#4CAF50',
  },
  sectionNameContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  sectionIcon: {
    marginRight: 8,
  },
  capacityLabelContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  capacityLabel: {
    fontSize: 12,
    color: '#666',
  },
  statusBadge: {
    fontSize: 10,
    height: 20,
    paddingHorizontal: 8,
  },
  progressBarContainer: {
    width: '100%',
  },
  teachersTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  teacherCountBadge: {
    marginLeft: 8,
    backgroundColor: '#4CAF50',
    color: 'white',
  },
  noTeachersContainer: {
    padding: 16,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,
    backgroundColor: '#f9f9f9',
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderStyle: 'dashed',
    marginVertical: 8,
  },
  addTeacherButton: {
    marginTop: 8,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
  },
  sectionHeaderLeft: {
    flex: 1,
    marginRight: 16,
  },
  sectionName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
    color: '#333',
  },
  capacityContainer: {
    width: '100%',
  },
  progressBar: {
    height: 6,
    backgroundColor: '#e9ecef',
    borderRadius: 3,
    overflow: 'hidden',
    width: '100%',
    marginBottom: 4,
  },
  progressFill: {
    height: '100%',
    borderRadius: 3,
  },
  capacityText: {
    fontSize: 12,
    color: '#666',
  },
  sectionActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  teachersContainer: {
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#e9ecef',
    width: '100%',
  },
  teachersTitle: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
    color: '#555',
  },
  teacherChips: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingBottom: 4,
  },
  studentPreviewContainer: {
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#e9ecef',
    width: '100%',
  },
  studentPreviewHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  studentPreviewTitle: {
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 8,
    color: '#555',
  },
  studentCountBadge: {
    marginLeft: 8,
    backgroundColor: '#4CAF50',
    color: 'white',
  },
  studentAvatarsContainer: {
    flexDirection: 'row',
    marginVertical: 8,
    paddingHorizontal: 4,
  },
  studentAvatarWrapper: {
    marginLeft: -8,
    borderRadius: 20,
    borderWidth: 2,
    borderColor: 'white',
  },
  studentAvatar: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#4CAF50',
    justifyContent: 'center',
    alignItems: 'center',
  },
  moreStudentsAvatar: {
    backgroundColor: '#9e9e9e',
  },
  avatarText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 12,
  },
  topStudentsContainer: {
    marginTop: 8,
  },
  topStudentsTitle: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
  },
  topStudentsList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  topStudentChip: {
    marginRight: 8,
    marginBottom: 4,
    backgroundColor: '#f0f8ff',
  },
  viewAllButtonContainer: {
    alignItems: 'center',
    marginTop: 8,
    borderTopWidth: 1,
    borderTopColor: '#e9ecef',
    paddingTop: 8,
  },
  fullScreenModal: {
    backgroundColor: '#fff',
    margin: 0,
    flex: 1,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e9ecef',
    backgroundColor: '#f8f9fa',
  },
  modalHeaderLeft: {
    flex: 1,
  },
  modalTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2d3436',
  },
  modalSubtitle: {
    fontSize: 16,
    color: '#636e72',
    marginBottom: 16,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    margin: 16,
    elevation: 2,
  },
  searchInput: {
    flex: 1,
    marginLeft: 8,
    fontSize: 16,
    marginBottom: 16,
  },
  studentList: {
    flex: 1,
  },
  tableHeader: {
    backgroundColor: '#f8f9fa',
  },
  tableRow: {
    borderBottomWidth: 1,
    borderBottomColor: '#e9ecef',
    backgroundColor: '#ffffff',
    // Add a subtle highlight effect
    borderRadius: 4,
  },
  clickableRow: {
    backgroundColor: '#f8f9fa',
    borderLeftWidth: 3,
    borderLeftColor: '#4CAF50',
    marginVertical: 2,
    elevation: 1,
    // Add a hover effect
    transform: [{ scale: 1.01 }],
  },
  studentNameCell: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  studentAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: DefaultTheme.colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  avatarText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 14,
  },
  studentName: {
    fontSize: 14,
    fontWeight: '500',
  },
  averageScore: {
    fontWeight: '500',
  },
  emptyRow: {
    height: 200,
  },
  emptyCell: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyText: {
    color: '#636e72',
    fontSize: 16,
    fontStyle: 'italic',
  },
  modalContent: {
    backgroundColor: 'white',
    padding: 20,
    margin: 20,
    borderRadius: 12,
    maxHeight: '80%',
    elevation: 5,
  },
  modalContainer: {
    flex: 1,
    margin: 0,
    backgroundColor: 'white',
  },
  sectionForm: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  sectionInput: {
    flex: 2,
    marginRight: 10,
  },
  capacityInput: {
    flex: 1,
    marginRight: 10,
  },
  groupForm: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  groupInput: {
    flex: 1,
    marginRight: 10,
  },
  addButton: {
    marginVertical: 10,
  },
  modalButtons: {
    marginTop: 20,
    marginBottom: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 10,
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
    backgroundColor: '#4CAF50',
  },
  searchInput: {
    marginBottom: 16,
  },
  teachersList: {
    maxHeight: 300,
  },
  teacherItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
    backgroundColor: '#f5f5f5',
    elevation: 1,
  },
  selectedTeacherItem: {
    backgroundColor: '#e3f2fd',
    borderLeftWidth: 4,
    borderLeftColor: '#2196F3',
  },
  teacherName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  teacherEmail: {
    fontSize: 14,
    opacity: 0.7,
    color: '#666',
  },
  teacherSections: {
    fontSize: 12,
    opacity: 0.5,
    marginTop: 4,
    color: '#888',
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 16,
    gap: 8,
  },
  modalButton: {
    minWidth: 100,
  },
  loader: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loaderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    color: '#666',
  },
  animatedContainer: {
    marginVertical: 12,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#f8f9fa',
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  className: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  academicYearContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 8,
  },
  academicYear: {
    fontSize: 14,
    color: '#666',
  },
  menuButton: {
    marginRight: -8,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: '#fff',
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statLabel: {
    fontSize: 14,
    marginLeft: 4,
    color: '#555',
  },
  statDivider: {
    marginHorizontal: 8,
    height: 24,
    width: 1,
    backgroundColor: '#e0e0e0',
  },
  description: {
    fontSize: 14,
    color: '#666',
    padding: 16,
    lineHeight: 20,
  },
  divider: {
    marginVertical: 8,
  },
  sectionsContainer: {
    padding: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
    color: '#333',
  },
  sectionGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  noSectionsText: {
    textAlign: 'center',
    color: '#888',
    fontStyle: 'italic',
    marginTop: 16,
  },
  actionButton: {
    marginLeft: 8,
  },
  subjectSelection: {
    marginVertical: 16,
  },
  sectionLabel: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
    color: '#333',
  },
  subjectList: {
    flexDirection: 'row',
  },
  subjectChip: {
    marginRight: 8,
    marginBottom: 8,
  },
  sectionItem: {
    width: '100%',
    marginBottom: 12,
  },
  teacherChip: {
    marginRight: 8,
    marginBottom: 4,
    backgroundColor: '#e3f2fd',
  },
  noTeachers: {
    color: '#888',
    fontStyle: 'italic',
  },
  subjectLoader: {
    marginVertical: 16,
  },
  noSubjectsContainer: {
    alignItems: 'center',
    padding: 16,
  },
  noSubjectsText: {
    marginBottom: 8,
    color: '#666',
  },
  retryButton: {
    marginTop: 8,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    color: '#f44336',
    textAlign: 'center',
    marginBottom: 16,
    fontSize: 16,
  },
  pageTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
    marginVertical: 16,
  },
  headerContainer: {
    backgroundColor: '#fff',
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    marginBottom: 16,
  },
  languageSelector: {
    position: 'absolute',
    top: 16,
    right: 16,
    zIndex: 100,
  },
});
export default ClassManagement;