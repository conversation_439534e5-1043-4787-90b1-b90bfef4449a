import React, { useState, useEffect } from 'react';
import { View, ScrollView, StyleSheet } from 'react-native';
import { Card, Title, Text, DataTable, Portal, Modal, TextInput, ActivityIndicator } from 'react-native-paper';
import { db, auth } from '../../config/firebase';
import { collection, query, where, getDocs, addDoc, updateDoc, doc, getDoc, onSnapshot } from 'firebase/firestore';
import CustomButton from '../../components/common/CustomButton';
import { Picker } from '@react-native-picker/picker';
import EthiopianCalendar from '../../utils/EthiopianCalendar';
import { useLanguage } from '../../context/LanguageContext';
import EthiopianDatePicker from '../../components/common/EthiopianDatePicker';
import EthiopianTimePicker from '../../components/common/EthiopianTimePicker';

const MeetingScheduler = () => {
  const { language } = useLanguage();
  const [meetings, setMeetings] = useState([]);
  const [userRole, setUserRole] = useState('');
  const [students, setStudents] = useState([]);
  const [selectedStudent, setSelectedStudent] = useState('');
  const [teachers, setTeachers] = useState([]);
  const [selectedTeacher, setSelectedTeacher] = useState('');
  const [loading, setLoading] = useState(true);
  const [modalVisible, setModalVisible] = useState(false);
  const [meetingForm, setMeetingForm] = useState({
    date: '',
    time: '',
    duration: '30',
    reason: '',
    location: 'School Office',
    status: 'pending',
  });

  useEffect(() => {
    fetchUserRole();
    setupMeetingListener();
  }, []);

  useEffect(() => {
    if (userRole === 'parent') {
      fetchStudents();
    } else if (userRole === 'teacher') {
      fetchTeacherStudents();
    }
  }, [userRole]);

  const fetchUserRole = async () => {
    try {
      if (!auth.currentUser) {
        console.error('No authenticated user found');
        return;
      }

      const userDocRef = doc(db, 'users', auth.currentUser.uid);
      const userDocSnap = await getDoc(userDocRef);

      if (userDocSnap.exists()) {
        setUserRole(userDocSnap.data().role);
      } else {
        console.error('User document not found');
      }
    } catch (error) {
      console.error('Error fetching user role:', error);
    }
  };

  const setupMeetingListener = () => {
    const meetingsRef = collection(db, 'meetings');
    const q = query(
      meetingsRef,
      where('participants', 'array-contains', auth.currentUser.uid)
    );

    const unsubscribe = onSnapshot(q, (snapshot) => {
      const meetingData = [];
      snapshot.forEach((doc) => {
        meetingData.push({ id: doc.id, ...doc.data() });
      });
      setMeetings(meetingData);
      setLoading(false);
    });

    return unsubscribe;
  };

  const fetchStudents = async () => {
    try {
      const studentsRef = collection(db, 'users');
      const q = query(
        studentsRef,
        where('role', '==', 'student'),
        where('parentId', '==', auth.currentUser.uid)
      );
      const querySnapshot = await getDocs(q);

      const studentData = [];
      querySnapshot.forEach((doc) => {
        studentData.push({ id: doc.id, ...doc.data() });
      });

      setStudents(studentData);
    } catch (error) {
      console.error('Error fetching students:', error);
    }
  };

  const fetchTeacherStudents = async () => {
    try {
      const teacherDoc = await doc(db, 'users', auth.currentUser.uid).get();
      const assignedClasses = teacherDoc.data().assignedClasses || [];

      const studentsRef = collection(db, 'users');
      const q = query(
        studentsRef,
        where('role', '==', 'student'),
        where('classId', 'in', assignedClasses)
      );
      const querySnapshot = await getDocs(q);

      const studentData = [];
      querySnapshot.forEach((doc) => {
        studentData.push({ id: doc.id, ...doc.data() });
      });

      setStudents(studentData);
    } catch (error) {
      console.error('Error fetching teacher students:', error);
    }
  };

  const fetchTeachers = async () => {
    try {
      const student = students.find(s => s.id === selectedStudent);
      if (student && student.classId) {
        const teachersRef = collection(db, 'users');
        const q = query(
          teachersRef,
          where('role', '==', 'teacher'),
          where('assignedClasses', 'array-contains', student.classId)
        );
        const querySnapshot = await getDocs(q);

        const teacherData = [];
        querySnapshot.forEach((doc) => {
          teacherData.push({ id: doc.id, ...doc.data() });
        });

        setTeachers(teacherData);
      }
    } catch (error) {
      console.error('Error fetching teachers:', error);
    }
  };

  const handleScheduleMeeting = async () => {
    try {
      const meetingData = {
        ...meetingForm,
        studentId: selectedStudent,
        teacherId: selectedTeacher || auth.currentUser.uid,
        parentId: userRole === 'parent' ? auth.currentUser.uid : null,
        participants: [
          selectedStudent,
          selectedTeacher || auth.currentUser.uid,
          userRole === 'parent' ? auth.currentUser.uid : null,
        ].filter(Boolean),
        createdAt: new Date().toISOString(),
        createdBy: auth.currentUser.uid,
      };

      await addDoc(collection(db, 'meetings'), meetingData);
      setModalVisible(false);
      resetForm();
    } catch (error) {
      console.error('Error scheduling meeting:', error);
    }
  };

  const handleUpdateMeetingStatus = async (meetingId, newStatus) => {
    try {
      const meetingRef = doc(db, 'meetings', meetingId);
      await updateDoc(meetingRef, {
        status: newStatus,
        updatedAt: new Date().toISOString(),
        updatedBy: auth.currentUser.uid,
      });
    } catch (error) {
      console.error('Error updating meeting status:', error);
    }
  };

  const resetForm = () => {
    setMeetingForm({
      date: '',
      time: '',
      duration: '30',
      reason: '',
      location: 'School Office',
      status: 'pending',
    });
    setSelectedStudent('');
    setSelectedTeacher('');
  };

  const formatDateTime = (date, time) => {
    const dateTime = new Date(date + 'T' + time);
    return EthiopianCalendar.formatDate(dateTime) + ' ' +
           dateTime.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' });
  };

  return (
    <ScrollView style={styles.container}>
      <Card style={styles.card}>
        <Card.Content>
          <View style={styles.headerContainer}>
            <Title>Meeting Scheduler</Title>
            <CustomButton
              mode="contained"
              onPress={() => setModalVisible(true)}
            >
              Schedule Meeting
            </CustomButton>
          </View>

          {loading ? (
            <ActivityIndicator style={styles.loader} />
          ) : (
            <DataTable>
              <DataTable.Header>
                <DataTable.Title>Date & Time</DataTable.Title>
                <DataTable.Title>{userRole === 'parent' ? 'Teacher' : 'Student'}</DataTable.Title>
                <DataTable.Title>Status</DataTable.Title>
                <DataTable.Title>Actions</DataTable.Title>
              </DataTable.Header>

              {meetings.map((meeting) => (
                <DataTable.Row key={meeting.id}>
                  <DataTable.Cell>
                    {formatDateTime(meeting.date, meeting.time)}
                  </DataTable.Cell>
                  <DataTable.Cell>
                    {userRole === 'parent' ?
                      teachers.find(t => t.id === meeting.teacherId)?.displayName :
                      students.find(s => s.id === meeting.studentId)?.displayName}
                  </DataTable.Cell>
                  <DataTable.Cell>
                    <Text style={[
                      styles.status,
                      { color: meeting.status === 'approved' ? '#4CAF50' :
                              meeting.status === 'rejected' ? '#f44336' : '#FF9800' }
                    ]}>
                      {meeting.status}
                    </Text>
                  </DataTable.Cell>
                  <DataTable.Cell>
                    {meeting.status === 'pending' && (
                      <View style={styles.actionButtons}>
                        {userRole !== 'parent' && (
                          <>
                            <CustomButton
                              mode="outlined"
                              onPress={() => handleUpdateMeetingStatus(meeting.id, 'approved')}
                              style={[styles.actionButton, styles.approveButton]}
                            >
                              Approve
                            </CustomButton>
                            <CustomButton
                              mode="outlined"
                              onPress={() => handleUpdateMeetingStatus(meeting.id, 'rejected')}
                              style={[styles.actionButton, styles.rejectButton]}
                            >
                              Reject
                            </CustomButton>
                          </>
                        )}
                      </View>
                    )}
                  </DataTable.Cell>
                </DataTable.Row>
              ))}
            </DataTable>
          )}
        </Card.Content>
      </Card>

      <Portal>
        <Modal
          visible={modalVisible}
          onDismiss={() => setModalVisible(false)}
          contentContainerStyle={styles.modalContent}
        >
          <ScrollView>
            <Title>Schedule New Meeting</Title>

            <View style={styles.pickerContainer}>
              <Text style={styles.pickerLabel}>
                {userRole === 'parent' ? 'Select Child' : 'Select Student'}
              </Text>
              <Picker
                selectedValue={selectedStudent}
                onValueChange={(value) => {
                  setSelectedStudent(value);
                  if (userRole === 'parent') {
                    fetchTeachers();
                  }
                }}
                style={styles.picker}
              >
                <Picker.Item label="Select..." value="" />
                {students.map((student) => (
                  <Picker.Item
                    key={student.id}
                    label={student.displayName}
                    value={student.id}
                  />
                ))}
              </Picker>
            </View>

            {userRole === 'parent' && (
              <View style={styles.pickerContainer}>
                <Text style={styles.pickerLabel}>Select Teacher</Text>
                <Picker
                  selectedValue={selectedTeacher}
                  onValueChange={setSelectedTeacher}
                  style={styles.picker}
                >
                  <Picker.Item label="Select..." value="" />
                  {teachers.map((teacher) => (
                    <Picker.Item
                      key={teacher.id}
                      label={teacher.displayName}
                      value={teacher.id}
                    />
                  ))}
                </Picker>
              </View>
            )}

            <View style={styles.datePickerContainer}>
              <Text style={styles.datePickerLabel}>Date</Text>
              <EthiopianDatePicker
                value={meetingForm.date ? new Date(meetingForm.date) : new Date()}
                onChange={(date) => {
                  if (date) {
                    setMeetingForm({ ...meetingForm, date: date.toISOString().split('T')[0] });
                  }
                }}
                label="Select Meeting Date"
                language={language}
                display="spinner"
                themeType="light"
                buttonMode="outlined"
                showIcon={true}
                iconPosition="right"
              />
            </View>

            <View style={styles.datePickerContainer}>
              <Text style={styles.datePickerLabel}>Time</Text>
              <EthiopianTimePicker
                value={meetingForm.time ? new Date(`2023-01-01T${meetingForm.time}:00`) : new Date()}
                onChange={(time) => {
                  if (time) {
                    const hours = time.getHours().toString().padStart(2, '0');
                    const minutes = time.getMinutes().toString().padStart(2, '0');
                    setMeetingForm({ ...meetingForm, time: `${hours}:${minutes}` });
                  }
                }}
                label="Select Meeting Time"
                display="default"
                is24Hour={false}
                themeType="light"
                showIcon={true}
                iconPosition="left"
              />
            </View>

            <TextInput
              label="Duration (minutes)"
              value={meetingForm.duration}
              onChangeText={(text) => setMeetingForm({ ...meetingForm, duration: text })}
              keyboardType="numeric"
              style={styles.input}
            />

            <TextInput
              label="Location"
              value={meetingForm.location}
              onChangeText={(text) => setMeetingForm({ ...meetingForm, location: text })}
              style={styles.input}
            />

            <TextInput
              label="Reason for Meeting"
              value={meetingForm.reason}
              onChangeText={(text) => setMeetingForm({ ...meetingForm, reason: text })}
              multiline
              numberOfLines={3}
              style={styles.input}
            />

            <View style={styles.buttonContainer}>
              <CustomButton
                mode="outlined"
                onPress={() => setModalVisible(false)}
                style={[styles.button, styles.cancelButton]}
              >
                Cancel
              </CustomButton>
              <CustomButton
                mode="contained"
                onPress={handleScheduleMeeting}
                style={[styles.button, styles.submitButton]}
              >
                Schedule
              </CustomButton>
            </View>
          </ScrollView>
        </Modal>
      </Portal>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  card: {
    margin: 16,
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  loader: {
    marginVertical: 20,
  },
  pickerContainer: {
    marginBottom: 16,
  },
  pickerLabel: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
  },
  datePickerContainer: {
    marginBottom: 16,
  },
  datePickerLabel: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
  },
  picker: {
    backgroundColor: '#f5f5f5',
    borderRadius: 4,
  },
  input: {
    marginBottom: 16,
  },
  modalContent: {
    backgroundColor: 'white',
    padding: 20,
    margin: 20,
    borderRadius: 8,
    maxHeight: '80%',
  },
  status: {
    textTransform: 'capitalize',
    fontWeight: 'bold',
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    marginHorizontal: 4,
  },
  approveButton: {
    borderColor: '#4CAF50',
  },
  rejectButton: {
    borderColor: '#f44336',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: 8,
    marginTop: 16,
  },
  button: {
    minWidth: 100,
  },
  cancelButton: {
    borderColor: '#666',
  },
  submitButton: {
    backgroundColor: '#2196F3',
  },
});

export default MeetingScheduler;
