rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Helper functions
    function isAdmin() {
      return request.auth != null &&
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }

    function isTeacher() {
      return request.auth != null &&
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'teacher';
    }

    function isStudent() {
      return request.auth != null &&
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'student';
    }

    function isParent() {
      return request.auth != null &&
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'parent';
    }

    function isAuthenticated() {
      return request.auth != null;
    }

    // Allow users to read their own profile and update their online status
    match /users/{userId} {
      allow read: if isAuthenticated() && (
        request.auth.uid == userId ||
        isAdmin() ||
        (
          exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
          get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'parent' &&
          get(/databases/$(database)/documents/users/$(request.auth.uid)).data.children != null &&
          get(/databases/$(database)/documents/users/$(request.auth.uid)).data.children.hasAny([userId])
        )
      );
      allow write: if isAuthenticated() && (request.auth.uid == userId || isAdmin());
      allow update: if isAuthenticated() &&
        (request.auth.uid == userId || isAdmin() ||
         (request.resource.data.diff(resource.data).affectedKeys().hasOnly(['isOnline', 'lastSeen', 'expoPushToken', 'lastTokenUpdate', 'devicePlatform', 'deviceModel'])));
    }

    // Allow admins to read and write all documents
    match /{document=**} {
      allow read, write: if isAdmin();
    }

    // Allow teachers to read their own data and classes they teach
    match /teachers/{teacherId} {
      allow read: if isAuthenticated() && (request.auth.uid == teacherId || isAdmin());
    }

    match /classes/{classId} {
      allow read: if isAuthenticated() && (
        get(/databases/$(database)/documents/classes/$(classId)).data.teacherId == request.auth.uid ||
        exists(/databases/$(database)/documents/students/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/students/$(request.auth.uid)).data.classId == classId ||
        isAdmin()
      );
    }

    // Allow students to read their own data and class data
    match /students/{studentId} {
      allow read: if isAuthenticated() && (
        request.auth.uid == studentId ||
        isParentOf(studentId) ||
        isAdmin()
      );
    }

    // Allow parents to read their children's data
    match /parents/{parentId} {
      allow read: if isAuthenticated() && (request.auth.uid == parentId || isAdmin());
      allow write: if isAuthenticated() && (request.auth.uid == parentId || isAdmin());
    }

    // Helper function to check if a user is a parent of a student
    function isParentOf(studentId) {
      let parentData = get(/databases/$(database)/documents/users/$(request.auth.uid)).data;
      return request.auth != null &&
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
        parentData.role == 'parent' &&
        parentData.children != null &&
        parentData.children.hasAny([studentId]);
    }

    // Allow access to timetables
    match /timetables/{timetableId} {
      allow read: if isAuthenticated();
    }

    // Allow access to activities
    match /activities/{activityId} {
      allow read: if isAuthenticated();
      allow write: if isAuthenticated(); // Allow all authenticated users to write activities
    }

    // Allow access to exam schedules
    match /examSchedules/{scheduleId} {
      allow read, write: if isAuthenticated();
    }

    // Allow access to teacher's classes assignment
    match /teacherClasses/{teacherId} {
      allow read: if isAuthenticated() && (request.auth.uid == teacherId || isAdmin());
      allow write: if isAuthenticated() && isAdmin();
    }

    // Allow access to notifications
    match /notifications/{notificationId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated();
      allow update: if isAuthenticated() && (
        resource.data.userId == request.auth.uid ||
        isAdmin() ||
        request.resource.data.diff(resource.data).affectedKeys().hasOnly(['read', 'status', 'readAt'])
      );
      allow delete: if isAuthenticated() && (
        resource.data.userId == request.auth.uid ||
        isAdmin()
      );
    }

    // Allow access to schedules and exam schedules
    match /schedules/{scheduleId} {
      allow read: if isAuthenticated();
    }

    // Allow access to password reset logs
    match /password_reset_logs/{logId} {
      allow read: if isAdmin();
      allow create: if true; // Allow unauthenticated creation for password reset requests
      allow update: if isAdmin();
    }

    // Allow access to verification logs
    match /verification_logs/{logId} {
      allow read: if isAdmin();
      allow create: if true; // Allow unauthenticated creation for email verification
      allow update: if isAdmin();
    }

    // Allow access to announcements
    match /announcements/{announcementId} {
      allow read: if request.auth != null;
    }

    // Allow access to backward compatibility checks
    match /backward_compatibility/{docId} {
      allow read, write: if request.auth != null;
    }

    // Allow access to behavior records
    match /behavior/{behaviorId} {
      allow read: if isAuthenticated() && (
        resource.data.studentId == request.auth.uid || // Student can read their own behavior
        isTeacher() || // Teachers can read behavior records
        isAdmin() || // Admins can read behavior records
        (isParent() && resource.data.studentId in get(/databases/$(database)/documents/users/$(request.auth.uid)).data.children) // Parents can read their children's behavior
      );
      allow write: if isAuthenticated() && (isAdmin() || isTeacher());
    }

    // Allow access to exams
    match /exams/{examId} {
      allow read: if isAuthenticated();
      allow write: if isAuthenticated() && (isAdmin() || isTeacher());
    }
  }
}
