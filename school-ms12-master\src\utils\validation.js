export const validateAdminAction = (data, actionType) => {
  const validations = {
    createUser: {
      required: ['email', 'password', 'role', 'firstName', 'lastName'],
      email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
      password: /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]{8,}$/
    },
    updateConfig: {
      required: ['schoolName', 'academicYear', 'gradeSettings']
    }
  };
  
  // Perform validation based on actionType
  // Return validation result
};

export const validateSchoolSettings = (settings) => {
  const required = [
    'schoolName',
    'email',
    'phone',
    'address',
    'city',
    'state',
    'country'
  ];

  const errors = {};

  required.forEach(field => {
    if (!settings[field]) {
      errors[field] = 'This field is required';
    }
  });

  if (settings.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(settings.email)) {
    errors.email = 'Invalid email format';
  }

  if (settings.phone && !/^\+?[\d\s-]{10,}$/.test(settings.phone)) {
    errors.phone = 'Invalid phone number';
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
}; 