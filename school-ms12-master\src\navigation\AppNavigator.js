import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { NavigationContainer } from '@react-navigation/native';
import { useAuth } from '../context/AuthContext';
import { useTranslation } from '../hooks/useTranslation';
import TeacherNavigator from './TeacherNavigator';
import StudentNavigator from './StudentNavigator';
import AdminNavigator from './AdminNavigator';
import ParentNavigator from './ParentNavigator';
import AuthNavigator from './AuthNavigator';
import { ActivityIndicator, View, Text } from 'react-native';
import AppHeader from '../components/common/AppHeader';
import NotificationHandler from '../components/common/NotificationHandler';
import GradeNotificationHandler from '../components/common/GradeNotificationHandler';
import NotificationCenter from '../screens/common/NotificationCenter';

const Stack = createStackNavigator();

// HOC to wrap screens with translation context
const withLanguageSupport = (Component) => (props) => {
  const { t, language } = useTranslation();
  return <Component {...props} t={t} language={language} />;
};

const LoadingScreen = () => {
  const { t } = useTranslation();
  return (
    <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
      <ActivityIndicator size="large" color="#2196F3" />
      <Text style={{ marginTop: 10, fontSize: 16 }}>{t('common.loading')}</Text>
    </View>
  );
};

const AppNavigator = () => {
  const { user, userRole, loading } = useAuth();
  const { t } = useTranslation();

  if (loading) {
    return <LoadingScreen />;
  }

  console.log('AppNavigator - Current user:', user?.email);
  console.log('AppNavigator - User role:', userRole);

  const getInitialRouteName = () => {
    if (!user || !userRole) return 'Auth';
    switch (userRole) {
      case 'admin':
        return 'AdminHome';
      case 'teacher':
        return 'TeacherHome';
      case 'student':
        return 'StudentHome';
      case 'parent':
        return 'ParentHome';
      default:
        return 'Auth';
    }
  };

  // Wrap all navigators with language support
  const EnhancedTeacherNavigator = withLanguageSupport(TeacherNavigator);
  const EnhancedStudentNavigator = withLanguageSupport(StudentNavigator);
  const EnhancedAdminNavigator = withLanguageSupport(AdminNavigator);
  const EnhancedParentNavigator = withLanguageSupport(ParentNavigator);
  const EnhancedAuthNavigator = withLanguageSupport(AuthNavigator);
  const EnhancedNotificationCenter = withLanguageSupport(NotificationCenter);

  return (
    <NavigationContainer>
      {user && <NotificationHandler />}
      {user && <GradeNotificationHandler />}
      <Stack.Navigator
        initialRouteName={getInitialRouteName()}
        screenOptions={{
          header: (props) => <AppHeader {...props} />,
        }}
      >
        {!user || !userRole ? (
          <Stack.Screen
            name="Auth"
            component={EnhancedAuthNavigator}
            options={{ headerShown: false }}
          />
        ) : (
          <>
            {userRole === 'admin' && (
              <Stack.Screen
                name="AdminHome"
                component={EnhancedAdminNavigator}
                options={{ headerShown: false }}
              />
            )}
            {userRole === 'teacher' && (
              <Stack.Screen
                name="TeacherHome"
                component={EnhancedTeacherNavigator}
                options={{ headerShown: false }}
              />
            )}
            {userRole === 'student' && (
              <Stack.Screen
                name="StudentHome"
                component={EnhancedStudentNavigator}
                options={{ headerShown: false }}
              />
            )}
            {userRole === 'parent' && (
              <Stack.Screen
                name="ParentHome"
                component={EnhancedParentNavigator}
                options={{ headerShown: false }}
              />
            )}

            {/* Common screens accessible from any role */}
            <Stack.Screen
              name="NotificationCenter"
              component={EnhancedNotificationCenter}
              options={{ headerShown: false }}
            />
          </>
        )}
      </Stack.Navigator>
    </NavigationContainer>
  );
};

export default AppNavigator;
