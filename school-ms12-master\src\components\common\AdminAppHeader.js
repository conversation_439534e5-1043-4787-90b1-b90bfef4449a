import React, { useState, useEffect } from 'react';
import { View, StyleSheet, TouchableOpacity, Platform, Dimensions } from 'react-native';
import { Appbar, Menu, useTheme, IconButton, Portal, Dialog, Button, Text, Badge, Divider, Avatar } from 'react-native-paper';
import CloudinaryAvatar from './CloudinaryAvatar';
import { useNavigation } from '@react-navigation/native';
import { useAuth } from '../../context/AuthContext';
import { useLanguage } from '../../context/LanguageContext';
import NotificationBadge from './NotificationBadge';
import LanguageSelector from './LanguageSelector';
import * as Animatable from 'react-native-animatable';
import { LinearGradient } from 'expo-linear-gradient';
import { MaterialCommunityIcons } from '@expo/vector-icons';

const AdminAppHeader = ({
  title,
  subtitle,
  showBack = false,
  showMenu = true,
  onMenuPress,
  showNotification = true
}) => {
  const navigation = useNavigation();
  const theme = useTheme();
  const { user, logout } = useAuth();
  const { translate, language, isRTL, getTextStyle } = useLanguage();
  const [menuVisible, setMenuVisible] = useState(false);
  const [profileMenuVisible, setProfileMenuVisible] = useState(false);
  const [logoutDialogVisible, setLogoutDialogVisible] = useState(false);
  const [unreadCount, setUnreadCount] = useState(0);
  const screenWidth = Dimensions.get('window').width;
  const isTablet = screenWidth >= 768;

  // Get user initials for avatar
  const getUserInitials = () => {
    if (!user || !user.displayName) return 'A';

    const nameParts = user.displayName.split(' ');
    if (nameParts.length >= 2) {
      return `${nameParts[0][0]}${nameParts[1][0]}`;
    }
    return nameParts[0][0];
  };

  // Function to safely handle drawer toggle
  const handleMenuPress = () => {
    if (onMenuPress) {
      // Use the provided onMenuPress function
      onMenuPress();
    } else {
      // Fallback to toggleDrawer if available in navigation
      try {
        if (navigation.toggleDrawer) {
          navigation.toggleDrawer();
        } else {
          console.log('Drawer navigation not available');
        }
      } catch (error) {
        console.error('Error toggling drawer:', error);
      }
    }
  };

  return (
    <Animatable.View animation="fadeIn" duration={500}>
      <LinearGradient
        colors={[theme.colors.primary, '#1565C0']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={styles.gradientHeader}
      >
        <Appbar.Header style={[styles.appbar, { backgroundColor: 'transparent' }]}>
          {showBack ? (
            <Animatable.View animation={isRTL ? "fadeInRight" : "fadeInLeft"} duration={500}>
              <Appbar.BackAction
                color="white"
                onPress={() => navigation.goBack()}
                style={isRTL ? styles.rightIcon : styles.backButton}
                size={26}
              />
            </Animatable.View>
          ) : showMenu ? (
            <Animatable.View animation={isRTL ? "fadeInRight" : "fadeInLeft"} duration={500}>
              <IconButton
                icon="menu"
                size={26}
                color="white"
                onPress={handleMenuPress}
                style={isRTL ? styles.rightIcon : styles.menuButton}
              />
            </Animatable.View>
          ) : null}

          <Animatable.View animation="fadeInDown" duration={600} style={styles.titleContainer}>
            <Appbar.Content
              title={title}
              subtitle={subtitle}
              titleStyle={[styles.appbarTitle, isRTL && styles.rtlText, getTextStyle({ fontSize: 18, color: 'white', fontWeight: 'bold' })]}
              subtitleStyle={[styles.appbarSubtitle, isRTL && styles.rtlText, getTextStyle({ fontSize: 14, color: 'rgba(255, 255, 255, 0.8)' })]}
            />
          </Animatable.View>

          <Animatable.View animation={isRTL ? "fadeInLeft" : "fadeInRight"} duration={500} style={styles.actionsContainer}>
            {/* Language Selector */}
            <LanguageSelector />

            {/* Notifications */}
            {showNotification && (
              <View style={styles.notificationsContainer}>
                <NotificationBadge
                  size={24}
                  color="white"
                  style={styles.appbarAction}
                  containerStyle={styles.notificationContainer}
                  onPress={() => navigation.navigate('NotificationCenter')}
                  includeApprovals={true}
                />
              </View>
            )}

            {/* Profile Menu */}
            <View>
              <TouchableOpacity
                onPress={() => setProfileMenuVisible(true)}
                style={styles.avatarContainer}
              >
                <CloudinaryAvatar
                  source={user?.photoURL}
                  label={getUserInitials()}
                  size={36}
                  style={styles.avatar}
                  color="white"
                  labelStyle={styles.avatarLabel}
                />
              </TouchableOpacity>

              <Menu
                visible={profileMenuVisible}
                onDismiss={() => setProfileMenuVisible(false)}
                anchor={{
                  x: isRTL ? 20 : Platform.OS === 'web' ? window.innerWidth - 60 : Dimensions.get('window').width - 60,
                  y: Platform.OS === 'ios' ? 90 : 70
                }}
                contentStyle={styles.menuContent}
              >
                <View style={styles.profileMenuHeader}>
                  <Avatar.Text
                    label={getUserInitials()}
                    size={50}
                    style={styles.menuAvatar}
                  />
                  <View style={styles.profileInfo}>
                    <Text style={styles.profileName}>{user?.displayName || translate('administrative.roles.admin')}</Text>
                    <Text style={styles.profileEmail}>{user?.email}</Text>
                    <Text style={styles.profileRole}>{translate('administrative.roles.admin')}</Text>
                  </View>
                </View>
                <Divider style={styles.menuDivider} />
                <Menu.Item
                  icon="account"
                  onPress={() => {
                    setProfileMenuVisible(false);
                    navigation.navigate('ProfileManagement');
                  }}
                  title={translate('common.profile')}
                />
                <Menu.Item
                  icon="cog"
                  onPress={() => {
                    setProfileMenuVisible(false);
                    navigation.navigate('SchoolSettings');
                  }}
                  title={translate('common.settings')}
                />
                <Menu.Item
                  icon="help-circle"
                  onPress={() => {
                    setProfileMenuVisible(false);
                    navigation.navigate('SystemReports');
                  }}
                  title={translate('common.help')}
                />
                <Divider style={styles.menuDivider} />
                <Menu.Item
                  icon="logout"
                  onPress={() => {
                    setProfileMenuVisible(false);
                    setLogoutDialogVisible(true);
                  }}
                  title={translate('common.logout')}
                  titleStyle={styles.logoutText}
                />
              </Menu>
            </View>
          </Animatable.View>
        </Appbar.Header>
      </LinearGradient>

      {/* Logout Confirmation Dialog */}
      <Portal>
        <Dialog
          visible={logoutDialogVisible}
          onDismiss={() => setLogoutDialogVisible(false)}
          style={styles.logoutDialog}
        >
          <Dialog.Title style={styles.dialogTitle}>
            {translate('common.logout') || 'Logout'}
          </Dialog.Title>
          <Dialog.Content>
            <Text style={styles.dialogContent}>
              {translate('common.confirmLogout') || 'Are you sure you want to logout?'}
            </Text>
          </Dialog.Content>
          <Dialog.Actions>
            <Button
              onPress={() => setLogoutDialogVisible(false)}
              labelStyle={styles.cancelButton}
            >
              {translate('common.cancel') || 'Cancel'}
            </Button>
            <Button
              onPress={() => {
                setLogoutDialogVisible(false);
                logout();
              }}
              mode="contained"
              style={styles.confirmButton}
            >
              {translate('common.confirm') || 'Confirm'}
            </Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>
    </Animatable.View>
  );
};

const styles = StyleSheet.create({
  gradientHeader: {
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
  },
  appbar: {
    height: 60,
    elevation: 0,
    justifyContent: 'space-between',
  },
  appbarTitle: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 18,
  },
  appbarSubtitle: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 14,
  },
  rtlText: {
    textAlign: 'right',
  },
  rightIcon: {
    transform: [{ scaleX: -1 }],
  },
  titleContainer: {
    flex: 1,
  },
  actionsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  notificationsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  notificationContainer: {
    marginRight: 8,
  },
  badgeContainer: {
    marginRight: 8,
  },
  appbarAction: {
    marginRight: 4,
  },
  avatarContainer: {
    marginHorizontal: 8,
  },
  avatar: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.5)',
  },
  avatarLabel: {
    fontWeight: 'bold',
  },
  backButton: {
    marginLeft: 0,
  },
  menuButton: {
    marginLeft: 0,
  },
  menuContent: {
    width: 280,
    borderRadius: 8,
    marginTop: 8,
  },
  profileMenuHeader: {
    flexDirection: 'row',
    padding: 16,
    alignItems: 'center',
  },
  menuAvatar: {
    backgroundColor: '#1976d2',
  },
  profileInfo: {
    marginLeft: 16,
    flex: 1,
  },
  profileName: {
    fontWeight: 'bold',
    fontSize: 16,
  },
  profileEmail: {
    fontSize: 12,
    opacity: 0.7,
    marginTop: 2,
  },
  profileRole: {
    fontSize: 12,
    color: '#1976d2',
    fontWeight: 'bold',
    marginTop: 2,
  },
  menuDivider: {
    marginVertical: 8,
  },
  logoutText: {
    color: '#F44336',
  },
  logoutDialog: {
    borderRadius: 12,
  },
  dialogTitle: {
    textAlign: 'center',
  },
  dialogContent: {
    textAlign: 'center',
  },
  cancelButton: {
    color: '#757575',
  },
  confirmButton: {
    backgroundColor: '#F44336',
  },
  actions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
});

export default AdminAppHeader;
