import React, { useState } from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import { Button, Text, Card, Title, Paragraph, ActivityIndicator } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { testNotifications, isPhysicalDevice, testPushToken } from '../../utils/NotificationTest';
import { useNotifications } from '../../context/NotificationContext';

const NotificationTestScreen = () => {
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState([]);
  const { permissionStatus, requestPermissionAgain } = useNotifications();
  
  const addResult = (message) => {
    setResults(prev => [...prev, { id: Date.now(), message }]);
  };
  
  const runTests = async () => {
    setLoading(true);
    setResults([]);
    
    try {
      // Test 1: Check if device is physical
      addResult('Testing if device is physical...');
      const physical = await isPhysicalDevice();
      addResult(`Device is ${physical ? 'physical' : 'emulator/simulator'}`);
      
      // Test 2: Check notification permissions
      addResult('Checking notification permissions...');
      addResult(`Current permission status: ${permissionStatus}`);
      
      if (permissionStatus !== 'granted') {
        addResult('Requesting notification permissions...');
        const granted = await requestPermissionAgain();
        addResult(`Permission request result: ${granted ? 'granted' : 'denied'}`);
      }
      
      // Test 3: Send a test notification
      addResult('Sending test notification...');
      const notificationResult = await testNotifications();
      addResult(`Test notification result: ${notificationResult ? 'success' : 'failed'}`);
      
      // Test 4: Get push token
      addResult('Testing push token...');
      const tokenResult = await testPushToken();
      addResult(`Push token test result: ${tokenResult ? 'success' : 'failed'}`);
      
    } catch (error) {
      addResult(`Error running tests: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <Card style={styles.card}>
          <Card.Content>
            <Title>Notification Test</Title>
            <Paragraph>This screen tests the notification functionality.</Paragraph>
            
            <Button 
              mode="contained" 
              onPress={runTests} 
              loading={loading}
              disabled={loading}
              style={styles.button}
            >
              Run Tests
            </Button>
            
            <View style={styles.resultsContainer}>
              <Title style={styles.resultsTitle}>Test Results</Title>
              {results.length === 0 ? (
                <Text style={styles.noResults}>No tests run yet</Text>
              ) : (
                results.map((result, index) => (
                  <Text key={result.id} style={styles.resultItem}>
                    {index + 1}. {result.message}
                  </Text>
                ))
              )}
              {loading && <ActivityIndicator style={styles.loader} />}
            </View>
          </Card.Content>
        </Card>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollContent: {
    padding: 16,
  },
  card: {
    marginBottom: 16,
  },
  button: {
    marginTop: 16,
    marginBottom: 16,
  },
  resultsContainer: {
    marginTop: 16,
    padding: 16,
    backgroundColor: '#f0f0f0',
    borderRadius: 8,
  },
  resultsTitle: {
    marginBottom: 8,
  },
  noResults: {
    fontStyle: 'italic',
    color: '#666',
  },
  resultItem: {
    marginBottom: 8,
    fontSize: 14,
  },
  loader: {
    marginTop: 16,
  },
});

export default NotificationTestScreen;
