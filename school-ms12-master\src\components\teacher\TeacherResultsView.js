import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import {
  Card,
  Title,
  Text,
  Button,
  DataTable,
  Chip,
  ActivityIndicator,
  Searchbar,
  Menu,
  Divider,
  Portal,
  Modal,
  useTheme,
  IconButton,
  Avatar
} from 'react-native-paper';
import ScrollableTable from '../../components/common/ScrollableTable';
import { tableStyles } from '../../styles/tableStyles';
import { db } from '../../config/firebase';
import {
  collection,
  query,
  where,
  getDocs,
  doc,
  getDoc,
  orderBy,
  limit
} from 'firebase/firestore';
import { useLanguage } from '../../context/LanguageContext';
import * as Animatable from 'react-native-animatable';

const TeacherResultsView = ({ classId, className, sectionName, subject }) => {
  const theme = useTheme();
  const { translate, isRTL } = useLanguage();

  // State variables
  const [loading, setLoading] = useState(true);
  const [students, setStudents] = useState([]);
  const [filteredStudents, setFilteredStudents] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [sortColumn, setSortColumn] = useState('rank');
  const [sortDirection, setSortDirection] = useState('asc');
  const [selectedStudent, setSelectedStudent] = useState(null);
  const [detailsModalVisible, setDetailsModalVisible] = useState(false);
  const [error, setError] = useState(null);
  const [classMenuVisible, setClassMenuVisible] = useState(false);
  const [sectionMenuVisible, setSectionMenuVisible] = useState(false);
  const [classes, setClasses] = useState([]);
  const [sections, setSections] = useState([]);
  const [selectedClass, setSelectedClass] = useState(className);
  const [selectedSection, setSelectedSection] = useState(sectionName);
  const [subjects, setSubjects] = useState([]);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Fetch data on component mount
  useEffect(() => {
    fetchClasses();
  }, []);

  useEffect(() => {
    if (selectedClass) {
      fetchSections(selectedClass);
    }
  }, [selectedClass]);

  useEffect(() => {
    if (selectedClass && selectedSection) {
      fetchResults();
    }
  }, [selectedClass, selectedSection]);

  // Fetch available classes
  const fetchClasses = async () => {
    try {
      const classesRef = collection(db, 'classes');
      const snapshot = await getDocs(classesRef);
      const classList = snapshot.docs.map(doc => ({
        id: doc.id,
        name: doc.data().name
      }));
      setClasses(classList);
    } catch (error) {
      console.error('Error fetching classes:', error);
      setError('Failed to load classes');
    }
  };

  // Fetch sections for a class
  const fetchSections = async (className) => {
    try {
      const sectionsRef = collection(db, 'sections');
      const q = query(sectionsRef, where('className', '==', className));
      const snapshot = await getDocs(q);
      const sectionList = snapshot.docs.map(doc => ({
        id: doc.id,
        name: doc.data().name
      }));
      setSections(sectionList);
    } catch (error) {
      console.error('Error fetching sections:', error);
      setError('Failed to load sections');
    }
  };

  // Calculate ranks for students
  const calculateRanks = (students) => {
    if (!students || students.length === 0) return [];

    // Sort by total percentage in descending order
    const ranked = [...students].sort((a, b) => b.totalPercentage - a.totalPercentage);

    // Assign ranks
    ranked.forEach((student, index) => {
      student.rank = index + 1;
    });

    return ranked;
  };

  // Get all unique subjects from students
  const getAllSubjects = (students) => {
    if (!students || students.length === 0) return [];

    const subjectsSet = new Set();

    students.forEach(student => {
      if (student.subjectScores) {
        Object.keys(student.subjectScores).forEach(subject => {
          subjectsSet.add(subject);
        });
      }
    });

    return Array.from(subjectsSet).sort();
  };

  // Fetch results for selected class and section
  const fetchResults = async () => {
    try {
      setLoading(true);
      setError(null);
      setIsRefreshing(true);

      // Fetch students
      const usersRef = collection(db, 'users');
      const studentsQuery = query(
        usersRef,
        where('role', '==', 'student'),
        where('className', '==', selectedClass),
        where('sectionName', '==', selectedSection),
        orderBy('rollNumber', 'asc')
      );

      const studentsSnapshot = await getDocs(studentsQuery);

      if (studentsSnapshot.empty) {
        setStudents([]);
        setFilteredStudents([]);
        setLoading(false);
        setIsRefreshing(false);
        return;
      }

      // Get all students with their basic info
      const studentsList = studentsSnapshot.docs.map(doc => {
        const data = doc.data();
        return {
          id: doc.id,
          uid: data.uid,
          name: data.firstName && data.lastName
            ? `${data.firstName} ${data.lastName}`
            : data.displayName || data.username || 'No Name',
          rollNumber: data.rollNumber || 'N/A',
          className: data.className,
          sectionName: data.sectionName,
          profileImage: data.profileImage || null,
          subjectScores: {},
          totalPoints: 0,
          totalMaxPoints: 0,
          totalPercentage: 0,
          average: 0
        };
      });

      // Fetch all subjects for this class and section
      const subjectsRef = collection(db, 'subjects');
      const subjectsQuery = query(
        subjectsRef,
        where('className', '==', selectedClass),
        where('sectionName', '==', selectedSection),
        orderBy('name', 'asc')
      );
      const subjectsSnapshot = await getDocs(subjectsQuery);

      const subjectsList = subjectsSnapshot.docs.map(doc => ({
        id: doc.id,
        name: doc.data().name,
        fullMarks: doc.data().fullMarks || 100
      }));

      setSubjects(subjectsList);

      // Fetch all grade submissions for this class, section, and subject
      const submissionsRef = collection(db, 'gradeSubmissions');
      const submissionsQuery = query(
        submissionsRef,
        where('className', '==', selectedClass),
        where('sectionName', '==', selectedSection),
        where('status', '==', 'approved') // Only get approved submissions
      );

      const submissionsSnapshot = await getDocs(submissionsQuery);

      // Process each student
      const studentPromises = studentsList.map(async (student) => {
        // Initialize subject scores
        const subjectScores = {};
        let totalPoints = 0;
        let totalMaxPoints = 0;
        let subjectCount = 0;

        // Process each submission
        submissionsSnapshot.docs.forEach(submissionDoc => {
          const submissionData = submissionDoc.data();
          const submissionSubject = submissionData.subject;

          // Find this student in the submission
          const studentData = submissionData.students.find(s =>
            s.id === student.id || s.uid === student.uid || s.uid === student.id || s.id === student.uid
          );

          if (studentData) {
            // Get or initialize subject score
            if (!subjectScores[submissionSubject]) {
              subjectScores[submissionSubject] = {
                totalScore: '0',
                assessmentScores: []
              };
            }

            // Update subject score
            const score = parseFloat(studentData.totalScore) || 0;
            subjectScores[submissionSubject].totalScore = score.toFixed(1);

            // Add assessment details if available
            if (submissionData.assessments && submissionData.assessments.length > 0) {
              submissionData.assessments.forEach(assessment => {
                // Try to find the student's score for this assessment
                const studentAssessmentScore = studentData.assessmentScores?.find(a =>
                  a.assessmentId === assessment.id
                );

                if (studentAssessmentScore) {
                  subjectScores[submissionSubject].assessmentScores.push({
                    assessmentId: assessment.id,
                    assessmentTitle: assessment.title,
                    assessmentType: assessment.type,
                    score: parseFloat(studentAssessmentScore.score) || 0,
                    maxPoints: parseFloat(assessment.points) || 100
                  });
                }
              });
            }

            // Add to total points
            totalPoints += score;
            subjectCount++;
          }
        });

        // If we didn't find any scores in submissions, try to fetch from scores collection
        if (subjectCount === 0) {
          const scoresRef = collection(db, 'scores');
          const studentScoresQuery = query(
            scoresRef,
            where('studentId', '==', student.uid || student.id),
            where('className', '==', selectedClass),
            where('sectionName', '==', selectedSection)
          );

          const scoresSnapshot = await getDocs(studentScoresQuery);

          // Group scores by subject
          const scoresBySubject = {};

          scoresSnapshot.docs.forEach(scoreDoc => {
            const scoreData = scoreDoc.data();
            const subjectName = scoreData.subject;

            if (!scoresBySubject[subjectName]) {
              scoresBySubject[subjectName] = [];
            }

            scoresBySubject[subjectName].push({
              id: scoreDoc.id,
              ...scoreData
            });
          });

          // Process scores for each subject
          Object.entries(scoresBySubject).forEach(([subjectName, scores]) => {
            if (scores.length > 0) {
              // Calculate total score for this subject
              let subjectTotalScore = 0;
              let subjectMaxScore = 0;

              const assessmentScores = scores.map(score => {
                const scoreValue = parseFloat(score.points) || 0;
                const maxPoints = parseFloat(score.maxPoints) || 100;

                subjectTotalScore += scoreValue;
                subjectMaxScore += maxPoints;

                return {
                  assessmentId: score.assessmentId,
                  assessmentTitle: score.assessmentTitle || 'Unknown Assessment',
                  assessmentType: score.assessmentType || 'Unknown Type',
                  score: scoreValue,
                  maxPoints: maxPoints
                };
              });

              // Calculate percentage for this subject
              const subjectPercentage = subjectMaxScore > 0
                ? (subjectTotalScore / subjectMaxScore) * 100
                : 0;

              // Add to student's subject scores
              subjectScores[subjectName] = {
                totalScore: subjectPercentage.toFixed(1),
                assessmentScores: assessmentScores
              };

              // Add to total points
              totalPoints += subjectPercentage;
              subjectCount++;
            }
          });
        }

        // Calculate average
        const average = subjectCount > 0 ? totalPoints / subjectCount : 0;

        return {
          ...student,
          subjectScores,
          totalPoints,
          average,
          totalPercentage: average // For compatibility with existing code
        };
      });

      // Wait for all student data to be processed
      const processedStudents = await Promise.all(studentPromises);

      // Calculate ranks
      const rankedStudents = calculateRanks(processedStudents);

      setStudents(rankedStudents);
      setFilteredStudents(sortStudents(rankedStudents, sortColumn, sortDirection));

    } catch (error) {
      console.error('Error fetching results:', error);
      setError('Failed to load results: ' + error.message);
    } finally {
      setLoading(false);
      setIsRefreshing(false);
    }
  };

  // Handle search
  const handleSearch = (query) => {
    setSearchQuery(query);

    if (!query.trim()) {
      setFilteredStudents(sortStudents(students, sortColumn, sortDirection));
      return;
    }

    const filtered = students.filter(student =>
      student.name?.toLowerCase().includes(query.toLowerCase()) ||
      student.rollNumber?.toLowerCase().includes(query.toLowerCase())
    );

    setFilteredStudents(sortStudents(filtered, sortColumn, sortDirection));
  };

  // Sort students
  const sortStudents = (studentsToSort, column, direction) => {
    if (!studentsToSort || studentsToSort.length === 0) return [];

    return [...studentsToSort].sort((a, b) => {
      let valueA, valueB;

      switch (column) {
        case 'name':
          valueA = a.name?.toLowerCase() || '';
          valueB = b.name?.toLowerCase() || '';
          return direction === 'asc'
            ? valueA.localeCompare(valueB)
            : valueB.localeCompare(valueA);

        case 'rollNumber':
          valueA = a.rollNumber?.toLowerCase() || '';
          valueB = b.rollNumber?.toLowerCase() || '';
          return direction === 'asc'
            ? valueA.localeCompare(valueB)
            : valueB.localeCompare(valueA);

        case 'totalPercentage':
          valueA = a.totalPercentage || 0;
          valueB = b.totalPercentage || 0;
          return direction === 'asc'
            ? valueA - valueB
            : valueB - valueA;

        case 'rank':
          valueA = a.rank || 0;
          valueB = b.rank || 0;
          return direction === 'asc'
            ? valueA - valueB
            : valueB - valueA;

        default:
          return 0;
      }
    });
  };

  // Handle sort
  const handleSort = (column) => {
    if (sortColumn === column) {
      // Toggle direction if same column
      const newDirection = sortDirection === 'asc' ? 'desc' : 'asc';
      setSortDirection(newDirection);
      setFilteredStudents(sortStudents(filteredStudents, column, newDirection));
    } else {
      // Set new column and default to ascending
      setSortColumn(column);
      setSortDirection('asc');
      setFilteredStudents(sortStudents(filteredStudents, column, 'asc'));
    }
  };

  // Get sort icon
  const getSortIcon = (column) => {
    if (sortColumn !== column) return null;
    return sortDirection === 'asc' ? 'arrow-up' : 'arrow-down';
  };

  // View student details
  const viewStudentDetails = (student) => {
    setSelectedStudent(student);
    setDetailsModalVisible(true);
  };

  // Get performance color
  const getPerformanceColor = (percentage) => {
    if (percentage >= 80) return '#4CAF50';
    if (percentage >= 70) return '#8BC34A';
    if (percentage >= 60) return '#FFC107';
    if (percentage >= 50) return '#FF9800';
    return '#F44336';
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#1976d2" />
        <Text style={styles.loadingText}>Loading results...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>{error}</Text>
        <Button mode="contained" onPress={fetchResults}>
          Retry
        </Button>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Animatable.View animation="fadeIn" duration={500}>
        <Card style={styles.filterCard}>
          <Card.Content>
            <View style={styles.filterRow}>
              <View style={styles.filterItem}>
                <Text style={styles.filterLabel}>{translate('common.class') || 'Class'}:</Text>
                <Menu
                  visible={classMenuVisible}
                  onDismiss={() => setClassMenuVisible(false)}
                  anchor={
                    <Button
                      mode="outlined"
                      onPress={() => setClassMenuVisible(true)}
                      style={styles.filterButton}
                      icon="school"
                    >
                      {selectedClass || translate('common.selectClass') || 'Select Class'}
                    </Button>
                  }
                >
                  {classes.map(cls => (
                    <Menu.Item
                      key={cls.id}
                      onPress={() => {
                        setSelectedClass(cls.name);
                        setClassMenuVisible(false);
                      }}
                      title={cls.name}
                    />
                  ))}
                </Menu>
              </View>

              <View style={styles.filterItem}>
                <Text style={styles.filterLabel}>{translate('common.section') || 'Section'}:</Text>
                <Menu
                  visible={sectionMenuVisible}
                  onDismiss={() => setSectionMenuVisible(false)}
                  anchor={
                    <Button
                      mode="outlined"
                      onPress={() => setSectionMenuVisible(true)}
                      style={styles.filterButton}
                      icon="account-group"
                      disabled={!selectedClass}
                    >
                      {selectedSection || translate('common.selectSection') || 'Select Section'}
                    </Button>
                  }
                >
                  {sections.map(section => (
                    <Menu.Item
                      key={section.id}
                      onPress={() => {
                        setSelectedSection(section.name);
                        setSectionMenuVisible(false);
                      }}
                      title={section.name}
                    />
                  ))}
                </Menu>
              </View>
            </View>

            <Searchbar
              placeholder={translate('common.searchNameOrRoll') || "Search by name or roll number"}
              onChangeText={handleSearch}
              value={searchQuery}
              style={styles.searchBar}
              icon="magnify"
            />
          </Card.Content>
        </Card>

        <Card style={styles.summaryCard}>
          <Card.Content>
            <View style={styles.summaryHeader}>
              <Title style={styles.summaryTitle}>
                {translate('gradeManagement.resultsFor') || 'Results for'} {selectedClass} - {selectedSection}
              </Title>
              <Chip icon="account" style={styles.summaryChip}>
                {filteredStudents.length} {translate('common.students') || 'Students'}
              </Chip>
            </View>
            <Text style={styles.summaryText}>
              {translate('gradeManagement.resultsDescription') ||
                'This table shows the academic performance of students. Click on a student to view detailed results.'}
            </Text>
          </Card.Content>
        </Card>

        {filteredStudents.length === 0 ? (
          <Animatable.View animation="fadeIn" duration={500}>
            <Card style={styles.noDataCard}>
              <Card.Content style={styles.noDataContent}>
                <IconButton icon="alert" size={40} color={theme.colors.error} />
                <Text style={styles.noDataText}>
                  {translate('gradeManagement.noResults') || 'No results found for the selected criteria.'}
                </Text>
                <Button
                  mode="contained"
                  onPress={fetchResults}
                  style={styles.retryButton}
                  icon="refresh"
                >
                  {translate('common.refresh') || 'Refresh'}
                </Button>
              </Card.Content>
            </Card>
          </Animatable.View>
        ) : (
          <Animatable.View animation="fadeIn" duration={500} style={styles.tableContainer}>
            <ScrollableTable
              header={
                <DataTable.Header style={tableStyles.tableHeader}>
                  <DataTable.Title
                    sortDirection={sortColumn === 'rank' ? sortDirection : null}
                    onPress={() => handleSort('rank')}
                    style={{ width: 60 }}
                  >
                    {translate('common.rank') || 'Rank'}
                  </DataTable.Title>
                  <DataTable.Title
                    sortDirection={sortColumn === 'rollNumber' ? sortDirection : null}
                    onPress={() => handleSort('rollNumber')}
                    style={{ width: 80 }}
                  >
                    {translate('common.roll') || 'Roll No'}
                  </DataTable.Title>
                  <DataTable.Title
                    sortDirection={sortColumn === 'name' ? sortDirection : null}
                    onPress={() => handleSort('name')}
                    style={{ width: 180 }}
                  >
                    {translate('common.name') || 'Name'}
                  </DataTable.Title>

                  {/* Dynamic subject columns */}
                  {subjects.map(subject => (
                    <DataTable.Title
                      key={subject.id}
                      numeric
                      style={{ width: 80 }}
                    >
                      {subject.name}
                    </DataTable.Title>
                  ))}

                  {/* Sum column */}
                  <DataTable.Title
                    numeric
                    style={{ width: 80, backgroundColor: '#E3F2FD' }}
                  >
                    {translate('common.sum') || 'Sum'}
                  </DataTable.Title>

                  {/* Average column */}
                  <DataTable.Title
                    numeric
                    sortDirection={sortColumn === 'totalPercentage' ? sortDirection : null}
                    onPress={() => handleSort('totalPercentage')}
                    style={{ width: 80, backgroundColor: '#E3F2FD' }}
                  >
                    {translate('common.average') || 'Avg %'}
                  </DataTable.Title>

                  {/* Rank column (again for clarity) */}
                  <DataTable.Title
                    numeric
                    sortDirection={sortColumn === 'rank' ? sortDirection : null}
                    onPress={() => handleSort('rank')}
                    style={{ width: 80, backgroundColor: '#E8F5E9' }}
                  >
                    {translate('common.rank') || 'Rank'}
                  </DataTable.Title>

                  {/* Actions column */}
                  <DataTable.Title style={{ width: 100 }}>
                    {translate('common.actions') || 'Actions'}
                  </DataTable.Title>
                </DataTable.Header>
              }
              style={styles.resultsTable}
              maxHeight={450}
              showVerticalScrollIndicator={true}
              showHorizontalScrollIndicator={true}
            >
              {filteredStudents.map((student, index) => (
                <DataTable.Row
                  key={student.id}
                  style={[
                    tableStyles.row,
                    index % 2 === 0 ? styles.evenRow : styles.oddRow
                  ]}
                  onPress={() => viewStudentDetails(student)}
                >
                  {/* Rank */}
                  <DataTable.Cell style={{ width: 60 }}>
                    <Chip
                      mode="outlined"
                      style={[
                        styles.rankChip,
                        student.rank <= 3 ? styles.topRankChip : null
                      ]}
                      textStyle={student.rank <= 3 ? styles.topRankText : null}
                    >
                      {student.rank}
                    </Chip>
                  </DataTable.Cell>

                  {/* Roll Number */}
                  <DataTable.Cell style={{ width: 80 }}>
                    <Text style={tableStyles.cellText}>{student.rollNumber}</Text>
                  </DataTable.Cell>

                  {/* Name */}
                  <DataTable.Cell style={{ width: 180 }}>
                    <View style={styles.nameCell}>
                      {student.profileImage ? (
                        <Avatar.Image
                          size={24}
                          source={{ uri: student.profileImage }}
                          style={styles.avatar}
                        />
                      ) : (
                        <Avatar.Text
                          size={24}
                          label={student.name.substring(0, 2).toUpperCase()}
                          style={styles.avatar}
                        />
                      )}
                      <Text style={[tableStyles.cellText, styles.nameText]}>{student.name}</Text>
                    </View>
                  </DataTable.Cell>

                  {/* Dynamic subject scores */}
                  {subjects.map(subject => (
                    <DataTable.Cell
                      key={`${student.id}-${subject.id}`}
                      numeric
                      style={{ width: 80 }}
                    >
                      {student.subjectScores && student.subjectScores[subject.name] ? (
                        <Text style={{
                          ...tableStyles.cellTextBold,
                          color: getPerformanceColor(parseFloat(student.subjectScores[subject.name].totalScore))
                        }}>
                          {student.subjectScores[subject.name].totalScore}
                        </Text>
                      ) : (
                        <Text style={styles.noScoreText}>-</Text>
                      )}
                    </DataTable.Cell>
                  ))}

                  {/* Sum */}
                  <DataTable.Cell
                    numeric
                    style={{ width: 80, backgroundColor: '#E3F2FD' }}
                  >
                    <Text style={[tableStyles.cellTextBold, styles.sumText]}>
                      {student.totalPoints.toFixed(0)}
                    </Text>
                  </DataTable.Cell>

                  {/* Average */}
                  <DataTable.Cell
                    numeric
                    style={{ width: 80, backgroundColor: '#E3F2FD' }}
                  >
                    <Text style={{
                      ...tableStyles.cellTextBold,
                      color: getPerformanceColor(student.average)
                    }}>
                      {student.average.toFixed(1)}%
                    </Text>
                  </DataTable.Cell>

                  {/* Rank (again) */}
                  <DataTable.Cell
                    numeric
                    style={{ width: 80, backgroundColor: '#E8F5E9' }}
                  >
                    <Chip
                      mode="outlined"
                      style={[
                        styles.rankChip,
                        student.rank <= 3 ? styles.topRankChip : null
                      ]}
                      textStyle={student.rank <= 3 ? styles.topRankText : null}
                    >
                      {student.rank}
                    </Chip>
                  </DataTable.Cell>

                  {/* Actions */}
                  <DataTable.Cell style={{ width: 100 }}>
                    <Button
                      mode="contained"
                      compact
                      onPress={() => viewStudentDetails(student)}
                      style={styles.viewButton}
                      icon="eye"
                    >
                      {translate('common.view') || 'View'}
                    </Button>
                  </DataTable.Cell>
                </DataTable.Row>
              ))}
            </ScrollableTable>
          </Animatable.View>
        )}
      </Animatable.View>

      {/* Student Details Modal */}
      <Portal>
        <Modal
          visible={detailsModalVisible}
          onDismiss={() => setDetailsModalVisible(false)}
          contentContainerStyle={styles.modalContainer}
        >
          {selectedStudent && (
            <Animatable.View animation="fadeIn" duration={300}>
              <ScrollView>
                <Card style={styles.detailsCard}>
                  <Card.Content>
                    <View style={styles.studentHeaderRow}>
                      {selectedStudent.profileImage ? (
                        <Avatar.Image
                          size={60}
                          source={{ uri: selectedStudent.profileImage }}
                          style={styles.studentAvatar}
                        />
                      ) : (
                        <Avatar.Text
                          size={60}
                          label={selectedStudent.name.substring(0, 2).toUpperCase()}
                          style={styles.studentAvatar}
                        />
                      )}
                      <View style={styles.studentHeaderInfo}>
                        <Title style={styles.detailsTitle}>{selectedStudent.name}</Title>
                        <View style={styles.studentMetaRow}>
                          <Chip icon="id-card" style={styles.studentMetaChip}>
                            {translate('common.roll') || 'Roll'}: {selectedStudent.rollNumber}
                          </Chip>
                          <Chip
                            icon="trophy"
                            style={[
                              styles.studentMetaChip,
                              selectedStudent.rank <= 3 ? styles.topRankChip : null
                            ]}
                          >
                            {translate('common.rank') || 'Rank'}: {selectedStudent.rank}
                          </Chip>
                        </View>
                      </View>
                    </View>

                    <Divider style={styles.divider} />

                    <View style={styles.detailsGrid}>
                      <View style={styles.detailsGridItem}>
                        <Text style={styles.detailsLabel}>{translate('common.class') || 'Class'}:</Text>
                        <Text style={styles.detailsValue}>{selectedStudent.className}</Text>
                      </View>
                      <View style={styles.detailsGridItem}>
                        <Text style={styles.detailsLabel}>{translate('common.section') || 'Section'}:</Text>
                        <Text style={styles.detailsValue}>{selectedStudent.sectionName}</Text>
                      </View>
                      <View style={styles.detailsGridItem}>
                        <Text style={styles.detailsLabel}>{translate('common.average') || 'Average'}:</Text>
                        <Text
                          style={[
                            styles.detailsValue,
                            { color: getPerformanceColor(selectedStudent.average) }
                          ]}
                        >
                          {selectedStudent.average.toFixed(1)}%
                        </Text>
                      </View>
                      <View style={styles.detailsGridItem}>
                        <Text style={styles.detailsLabel}>{translate('common.totalPoints') || 'Total Points'}:</Text>
                        <Text style={styles.detailsValue}>
                          {selectedStudent.totalPoints.toFixed(0)}
                        </Text>
                      </View>
                    </View>
                  </Card.Content>
                </Card>

                <Card style={styles.performanceCard}>
                  <Card.Content>
                    <View style={styles.performanceHeader}>
                      <Title style={styles.performanceTitle}>
                        {translate('gradeManagement.performanceSummary') || 'Performance Summary'}
                      </Title>
                      <IconButton
                        icon="chart-bar"
                        size={24}
                        color={theme.colors.primary}
                      />
                    </View>

                    <View style={styles.performanceMetrics}>
                      <View style={styles.performanceMetric}>
                        <Text style={styles.metricLabel}>{translate('common.average') || 'Average'}</Text>
                        <Text style={[styles.metricValue, { color: getPerformanceColor(selectedStudent.average) }]}>
                          {selectedStudent.average.toFixed(1)}%
                        </Text>
                      </View>
                      <View style={styles.performanceMetric}>
                        <Text style={styles.metricLabel}>{translate('common.rank') || 'Rank'}</Text>
                        <Text style={styles.metricValue}>
                          {selectedStudent.rank}/{filteredStudents.length}
                        </Text>
                      </View>
                      <View style={styles.performanceMetric}>
                        <Text style={styles.metricLabel}>{translate('common.subjects') || 'Subjects'}</Text>
                        <Text style={styles.metricValue}>
                          {Object.keys(selectedStudent.subjectScores || {}).length}
                        </Text>
                      </View>
                    </View>
                  </Card.Content>
                </Card>

                <Card style={styles.subjectsCard}>
                  <Card.Title
                    title={translate('gradeManagement.subjectScores') || "Subject Scores"}
                    titleStyle={styles.cardTitle}
                    left={(props) => <Avatar.Icon {...props} icon="book-open-variant" />}
                  />
                  <Card.Content>
                    {/* Subject Summary Table */}
                    {Object.keys(selectedStudent.subjectScores || {}).length === 0 ? (
                      <View style={styles.noDataContent}>
                        <IconButton icon="alert" size={40} color={theme.colors.error} />
                        <Text style={styles.noDataText}>
                          {translate('gradeManagement.noSubjectScores') || 'No subject scores available.'}
                        </Text>
                      </View>
                    ) : (
                      <View style={styles.summaryTableContainer}>
                        <ScrollableTable
                          header={
                            <DataTable.Header style={tableStyles.tableHeader}>
                              <DataTable.Title style={{ width: 180 }}>
                                {translate('common.subject') || 'Subject'}
                              </DataTable.Title>
                              <DataTable.Title numeric style={{ width: 100 }}>
                                {translate('common.score') || 'Score (%)'}
                              </DataTable.Title>
                              <DataTable.Title numeric style={{ width: 100 }}>
                                {translate('common.grade') || 'Grade'}
                              </DataTable.Title>
                            </DataTable.Header>
                          }
                          style={styles.summaryTable}
                          maxHeight={250}
                          showVerticalScrollIndicator={true}
                        >
                          {Object.entries(selectedStudent.subjectScores).map(([subjectName, subjectData], index) => {
                            const score = parseFloat(subjectData.totalScore) || 0;
                            const grade = getGradeFromScore(score);

                            return (
                              <DataTable.Row
                                key={subjectName}
                                style={[
                                  tableStyles.row,
                                  index % 2 === 0 ? styles.evenRow : styles.oddRow
                                ]}
                              >
                                <DataTable.Cell style={{ width: 180 }}>{subjectName}</DataTable.Cell>
                                <DataTable.Cell numeric style={{ width: 100 }}>
                                  <Text style={{
                                    color: getPerformanceColor(score),
                                    fontWeight: 'bold'
                                  }}>
                                    {subjectData.totalScore}%
                                  </Text>
                                </DataTable.Cell>
                                <DataTable.Cell numeric style={{ width: 100 }}>
                                  <Chip
                                    mode="outlined"
                                    style={{
                                      backgroundColor: getPerformanceColor(score),
                                      width: 40,
                                      height: 30,
                                      alignItems: 'center',
                                      justifyContent: 'center'
                                    }}
                                    textStyle={{ color: '#fff', fontWeight: 'bold' }}
                                  >
                                    {grade}
                                  </Chip>
                                </DataTable.Cell>
                              </DataTable.Row>
                            );
                          })}

                          {/* Summary row */}
                          <DataTable.Row style={[tableStyles.row, styles.summaryRow]}>
                            <DataTable.Cell style={{ width: 180 }}>
                              <Text style={{ fontWeight: 'bold' }}>
                                {translate('common.overall') || 'Overall'}
                              </Text>
                            </DataTable.Cell>
                            <DataTable.Cell numeric style={{ width: 100 }}>
                              <Text style={{
                                color: getPerformanceColor(selectedStudent.average),
                                fontWeight: 'bold'
                              }}>
                                {selectedStudent.average.toFixed(1)}%
                              </Text>
                            </DataTable.Cell>
                            <DataTable.Cell numeric style={{ width: 100 }}>
                              <Chip
                                mode="outlined"
                                style={{
                                  backgroundColor: getPerformanceColor(selectedStudent.average),
                                  width: 40,
                                  height: 30,
                                  alignItems: 'center',
                                  justifyContent: 'center'
                                }}
                                textStyle={{ color: '#fff', fontWeight: 'bold' }}
                              >
                                {getGradeFromScore(selectedStudent.average)}
                              </Chip>
                            </DataTable.Cell>
                          </DataTable.Row>
                        </ScrollableTable>
                      </View>
                    )}
                  </Card.Content>
                </Card>

                <Card style={styles.assessmentsCard}>
                  <Card.Title
                    title={translate('gradeManagement.assessmentDetails') || "Assessment Details"}
                    titleStyle={styles.cardTitle}
                    left={(props) => <Avatar.Icon {...props} icon="clipboard-list" />}
                  />
                  <Card.Content>
                    {Object.keys(selectedStudent.subjectScores || {}).length === 0 ? (
                      <View style={styles.noDataContent}>
                        <IconButton icon="alert" size={40} color={theme.colors.error} />
                        <Text style={styles.noDataText}>
                          {translate('gradeManagement.noAssessments') || 'No assessment details available.'}
                        </Text>
                      </View>
                    ) : (
                      Object.entries(selectedStudent.subjectScores)
                        .filter(([_, subjectData]) =>
                          subjectData.assessmentScores && subjectData.assessmentScores.length > 0
                        )
                        .map(([subjectName, subjectData]) => (
                          <Animatable.View key={subjectName} animation="fadeIn" duration={300}>
                            <Card style={styles.subjectCard} mode="outlined">
                              <Card.Content>
                                <View style={styles.subjectHeader}>
                                  <Title style={styles.subjectName}>{subjectName}</Title>
                                  <Chip
                                    mode="outlined"
                                    style={{
                                      backgroundColor: getPerformanceColor(parseFloat(subjectData.totalScore))
                                    }}
                                    textStyle={{ color: '#fff' }}
                                  >
                                    {subjectData.totalScore}%
                                  </Chip>
                                </View>

                                {subjectData.assessmentScores && subjectData.assessmentScores.length > 0 ? (
                                  <View style={styles.assessmentTableContainer}>
                                    <ScrollableTable
                                      header={
                                        <DataTable.Header style={tableStyles.tableHeader}>
                                          <DataTable.Title style={{ width: 180 }}>
                                            {translate('common.assessment') || 'Assessment'}
                                          </DataTable.Title>
                                          <DataTable.Title style={{ width: 120 }}>
                                            {translate('common.type') || 'Type'}
                                          </DataTable.Title>
                                          <DataTable.Title numeric style={{ width: 80 }}>
                                            {translate('common.score') || 'Score'}
                                          </DataTable.Title>
                                          <DataTable.Title numeric style={{ width: 80 }}>
                                            {translate('common.max') || 'Max'}
                                          </DataTable.Title>
                                        </DataTable.Header>
                                      }
                                      style={styles.assessmentTable}
                                      maxHeight={150}
                                      showVerticalScrollIndicator={true}
                                    >
                                      {subjectData.assessmentScores.map((assessment, index) => (
                                        <DataTable.Row
                                          key={assessment.assessmentId || index}
                                          style={[
                                            tableStyles.row,
                                            index % 2 === 0 ? styles.evenRow : styles.oddRow
                                          ]}
                                        >
                                          <DataTable.Cell style={{ width: 180 }}>
                                            {assessment.assessmentTitle}
                                          </DataTable.Cell>
                                          <DataTable.Cell style={{ width: 120 }}>
                                            {assessment.assessmentType}
                                          </DataTable.Cell>
                                          <DataTable.Cell numeric style={{ width: 80 }}>
                                            <Text style={{
                                              color: getPerformanceColor((assessment.score / assessment.maxPoints) * 100),
                                              fontWeight: 'bold'
                                            }}>
                                              {assessment.score}
                                            </Text>
                                          </DataTable.Cell>
                                          <DataTable.Cell numeric style={{ width: 80 }}>
                                            {assessment.maxPoints}
                                          </DataTable.Cell>
                                        </DataTable.Row>
                                      ))}
                                    </ScrollableTable>
                                  </View>
                                ) : (
                                  <Text style={styles.noAssessmentsText}>
                                    {translate('gradeManagement.noAssessmentDetails') || 'No assessment details available'}
                                  </Text>
                                )}
                              </Card.Content>
                            </Card>
                          </Animatable.View>
                        ))
                    )}
                  </Card.Content>
                </Card>

                <View style={styles.buttonContainer}>
                  <Button
                    mode="contained"
                    onPress={() => setDetailsModalVisible(false)}
                    style={styles.closeButton}
                    icon="close-circle"
                  >
                    {translate('common.close') || 'Close'}
                  </Button>
                </View>
              </ScrollView>
            </Animatable.View>
          )}
        </Modal>
      </Portal>
    </View>
  );
};

// Helper function to get grade from score
const getGradeFromScore = (score) => {
  if (score >= 90) return 'A+';
  if (score >= 80) return 'A';
  if (score >= 70) return 'B';
  if (score >= 60) return 'C';
  if (score >= 50) return 'D';
  return 'F';
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    display: 'flex',
    flexDirection: 'column'
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666'
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24
  },
  errorText: {
    color: '#D32F2F',
    marginBottom: 16,
    textAlign: 'center',
    fontSize: 16
  },
  // Filter section
  filterCard: {
    marginBottom: 16,
    elevation: 2,
    borderRadius: 8
  },
  filterRow: {
    flexDirection: 'row',
    marginBottom: 16,
    flexWrap: 'wrap'
  },
  filterItem: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 8,
    minWidth: 200
  },
  filterLabel: {
    marginRight: 8,
    fontWeight: 'bold',
    fontSize: 14
  },
  filterButton: {
    flex: 1,
    borderRadius: 4
  },
  searchBar: {
    marginBottom: 8,
    elevation: 0,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 8
  },
  // Summary card
  summaryCard: {
    marginBottom: 16,
    elevation: 2,
    borderRadius: 8
  },
  summaryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: 'bold'
  },
  summaryChip: {
    backgroundColor: '#E3F2FD'
  },
  summaryText: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20
  },
  // No data states
  noDataCard: {
    padding: 16,
    marginVertical: 16,
    borderRadius: 8,
    elevation: 2
  },
  noDataContent: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 24
  },
  noDataText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginVertical: 16
  },
  retryButton: {
    marginTop: 16
  },
  // Table styles
  dataTable: {
    marginBottom: 16,
    backgroundColor: '#ffffff',
  },
  evenRow: {
    backgroundColor: '#f5f5f5'
  },
  oddRow: {
    backgroundColor: '#ffffff'
  },
  rankChip: {
    width: 36,
    height: 24,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 0
  },
  topRankChip: {
    backgroundColor: '#FFC107',
    borderColor: '#FFA000'
  },
  topRankText: {
    color: '#fff',
    fontWeight: 'bold'
  },
  nameCell: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  avatar: {
    marginRight: 8
  },
  nameText: {
    marginLeft: 8
  },
  noScoreText: {
    color: '#9E9E9E',
    fontStyle: 'italic'
  },
  sumText: {
    color: '#1976D2'
  },
  viewButton: {
    borderRadius: 4,
    height: 36
  },
  // Table container
  tableWrapper: {
    marginVertical: 16,
    borderRadius: 8,
    overflow: 'hidden',
    elevation: 2,
    backgroundColor: '#ffffff',
  },
  tableContainer: {
    flex: 1,
    height: 500, // Fixed height to ensure vertical scrolling works
    marginBottom: 16,
  },
  resultsTable: {
    flex: 1,
    borderRadius: 8,
    overflow: 'hidden',
  },
  // Summary styles
  subjectColumn: {
    backgroundColor: '#f9f9f9',
  },
  summaryColumn: {
    backgroundColor: '#E3F2FD',
    fontWeight: 'bold',
  },
  summaryRow: {
    backgroundColor: '#E3F2FD',
    borderTopWidth: 1,
    borderTopColor: '#90CAF9',
  },
  summaryTableContainer: {
    height: 250, // Fixed height to ensure vertical scrolling works
  },
  summaryTable: {
    borderRadius: 8,
    overflow: 'hidden',
  },
  // Assessment table
  assessmentTableContainer: {
    height: 200, // Fixed height to ensure vertical scrolling works
    marginTop: 16,
  },
  assessmentTable: {
    borderRadius: 8,
    overflow: 'hidden',
  },
  // Modal styles
  modalContainer: {
    backgroundColor: 'white',
    padding: 20,
    margin: 20,
    maxHeight: '90%',
    borderRadius: 8
  },
  // Student details
  studentHeaderRow: {
    flexDirection: 'row',
    marginBottom: 16
  },
  studentAvatar: {
    marginRight: 16
  },
  studentHeaderInfo: {
    flex: 1,
    justifyContent: 'center'
  },
  studentMetaRow: {
    flexDirection: 'row',
    marginTop: 8
  },
  studentMetaChip: {
    marginRight: 8
  },
  detailsCard: {
    marginBottom: 16,
    borderRadius: 8,
    elevation: 3
  },
  detailsTitle: {
    fontSize: 20,
    fontWeight: 'bold'
  },
  detailsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 8
  },
  detailsGridItem: {
    width: '50%',
    paddingVertical: 8,
    paddingRight: 16
  },
  detailsLabel: {
    fontWeight: 'bold',
    fontSize: 14,
    color: '#666',
    marginBottom: 4
  },
  detailsValue: {
    fontSize: 16
  },
  divider: {
    marginVertical: 16
  },
  // Performance card
  performanceCard: {
    marginBottom: 16,
    borderRadius: 8,
    elevation: 3
  },
  performanceHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16
  },
  performanceTitle: {
    fontSize: 18,
    fontWeight: 'bold'
  },
  performanceMetrics: {
    flexDirection: 'row',
    justifyContent: 'space-between'
  },
  performanceMetric: {
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    flex: 1,
    marginHorizontal: 4
  },
  metricLabel: {
    fontSize: 12,
    color: '#666',
    marginBottom: 8
  },
  metricValue: {
    fontSize: 18,
    fontWeight: 'bold'
  },
  // Subject cards
  subjectsCard: {
    marginBottom: 16,
    borderRadius: 8,
    elevation: 3
  },
  assessmentsCard: {
    marginBottom: 16,
    borderRadius: 8,
    elevation: 3
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: 'bold'
  },
  subjectCard: {
    marginBottom: 8,
    borderRadius: 8
  },
  subjectHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16
  },
  subjectName: {
    fontSize: 16,
    fontWeight: 'bold'
  },
  noAssessmentsText: {
    fontStyle: 'italic',
    color: '#666',
    textAlign: 'center',
    marginTop: 8
  },
  // Button container
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 16,
    marginBottom: 32
  },
  closeButton: {
    paddingHorizontal: 24,
    borderRadius: 4
  }
});

export default TeacherResultsView;
