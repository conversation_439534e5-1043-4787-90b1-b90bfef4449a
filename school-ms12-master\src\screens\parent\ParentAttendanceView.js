import React, { useState, useEffect, useRef } from 'react';
import { View, ScrollView, StyleSheet, StatusBar, SafeAreaView, TouchableOpacity, Animated, Dimensions } from 'react-native';
import {
  Card,
  Title,
  Text,
  List,
  Divider,
  ActivityIndicator,
  Chip,
  Button,
  IconButton,
  Snackbar,
  Surface,
  useTheme,
  Avatar,
  ProgressBar,
  Menu,
  Searchbar,
  DataTable,
  SegmentedButtons
} from 'react-native-paper';
import { db } from '../../config/firebase';
import {
  collection,
  query,
  where,
  getDocs,
  orderBy,
  limit,
  doc,
  getDoc
} from 'firebase/firestore';
import { useAuth } from '../../context/AuthContext';
import { useLanguage } from '../../context/LanguageContext';
import * as Animatable from 'react-native-animatable';
import { LinearGradient } from 'expo-linear-gradient';
import { Calendar } from 'react-native-calendars';
import ParentAppHeader from '../../components/common/ParentAppHeader';

const ParentAttendanceView = ({ navigation, route }) => {
  const { user } = useAuth();
  const { translate, language, isRTL } = useLanguage();
  // No theme needed
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const { width } = Dimensions.get('window');

  // State variables
  const [children, setChildren] = useState([]);
  const [selectedChild, setSelectedChild] = useState(null);
  const [attendance, setAttendance] = useState([]);
  const [loading, setLoading] = useState(true);
  const [childrenLoading, setChildrenLoading] = useState(true);
  const [error, setError] = useState(null);
  const [statistics, setStatistics] = useState({
    present: 0,
    absent: 0,
    total: 0,
    percentage: 0,
    lastMonth: {
      present: 0,
      absent: 0,
      total: 0,
      percentage: 0,
    }
  });
  const [viewMode, setViewMode] = useState('list'); // 'list', 'calendar', 'stats'
  const [selectedDate, setSelectedDate] = useState(null);
  const [markedDates, setMarkedDates] = useState({});
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredAttendance, setFilteredAttendance] = useState([]);
  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth());
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());
  const [childMenuVisible, setChildMenuVisible] = useState(false);

  // Start animations when component mounts
  useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 1000,
      useNativeDriver: true,
    }).start();

    // Hide the default header
    navigation.setOptions({
      headerShown: false,
    });
  }, []);

  // Fetch children data
  useEffect(() => {
    fetchChildren();

    // Check if we need to highlight a specific child's attendance from notification
    if (route?.params?.childId) {
      const childId = route.params.childId;
      // We'll set the selected child after fetching children
    }
  }, []);

  // Handle route params after children are loaded
  useEffect(() => {
    if (route?.params?.childId && children.length > 0) {
      const childId = route.params.childId;
      const child = children.find(c => c.id === childId);

      if (child) {
        setSelectedChild(child);

        if (route?.params?.highlight) {
          setSnackbarMessage('Viewing attendance details from notification');
          setSnackbarVisible(true);
          setViewMode('list');
        }
      }
    }
  }, [children, route?.params]);

  // Fetch attendance data when selected child changes
  useEffect(() => {
    if (selectedChild) {
      fetchAttendance(selectedChild.id);
    }
  }, [selectedChild]);

  // Filter attendance when search query changes
  useEffect(() => {
    filterAttendance();
  }, [attendance, searchQuery, selectedMonth, selectedYear]);

  // Fetch parent's children
  const fetchChildren = async () => {
    try {
      setChildrenLoading(true);

      const usersRef = collection(db, 'users');
      const q = query(
        usersRef,
        where('parent.id', '==', user.uid),
        where('role', '==', 'student')
      );

      const querySnapshot = await getDocs(q);

      if (querySnapshot.empty) {
        setChildren([]);
        setChildrenLoading(false);
        return;
      }

      const childrenData = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      setChildren(childrenData);

      // Select the first child by default
      if (childrenData.length > 0) {
        setSelectedChild(childrenData[0]);
      }

    } catch (error) {
      console.error('Error fetching children:', error);
      setError(translate('parentAttendance.fetchChildrenError'));
      setSnackbarMessage(translate('parentAttendance.fetchChildrenError'));
      setSnackbarVisible(true);
    } finally {
      setChildrenLoading(false);
    }
  };

  // Fetch attendance data for a child
  const fetchAttendance = async (childId) => {
    try {
      setLoading(true);
      setError(null);

      const attendanceRef = collection(db, 'attendance');
      const q = query(
        attendanceRef,
        where('studentId', '==', childId),
        orderBy('date', 'desc')
      );

      const querySnapshot = await getDocs(q);

      if (querySnapshot.empty) {
        setAttendance([]);
        setStatistics({
          present: 0,
          absent: 0,
          total: 0,
          percentage: 0,
          lastMonth: {
            present: 0,
            absent: 0,
            total: 0,
            percentage: 0,
          }
        });
        setLoading(false);
        return;
      }

      const attendanceData = [];
      let presentCount = 0;
      let absentCount = 0;
      let totalCount = 0;

      // Last month statistics
      const today = new Date();
      const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
      const lastMonthEnd = new Date(today.getFullYear(), today.getMonth(), 0);
      let lastMonthPresent = 0;
      let lastMonthAbsent = 0;
      let lastMonthTotal = 0;

      // Calendar marked dates
      const markedDatesObj = {};

      querySnapshot.forEach((doc) => {
        const data = doc.data();
        const attendanceDate = data.date ?
          (typeof data.date === 'string' ? new Date(data.date) : new Date(data.date.seconds * 1000)) :
          new Date();

        const formattedDate = attendanceDate.toISOString().split('T')[0];

        // Add to marked dates for calendar
        markedDatesObj[formattedDate] = {
          selected: false,
          marked: true,
          dotColor: data.status === 'present' ? '#4CAF50' : '#F44336',
          customStyles: {
            container: {
              backgroundColor: data.status === 'present' ? 'rgba(76, 175, 80, 0.1)' : 'rgba(244, 67, 54, 0.1)',
            },
            text: {
              color: data.status === 'present' ? '#4CAF50' : '#F44336',
              fontWeight: 'bold',
            },
          },
        };

        // Format the record
        const record = {
          id: doc.id,
          ...data,
          date: attendanceDate,
          formattedDate,
          month: attendanceDate.getMonth(),
          year: attendanceDate.getFullYear(),
          dayName: attendanceDate.toLocaleDateString(language === 'en' ? 'en-US' : 'am-ET', { weekday: 'long' }),
          monthName: attendanceDate.toLocaleDateString(language === 'en' ? 'en-US' : 'am-ET', { month: 'long' }),
        };

        attendanceData.push(record);

        // Update statistics
        if (data.status === 'present') {
          presentCount++;
        } else if (data.status === 'absent') {
          absentCount++;
        }
        totalCount++;

        // Update last month statistics
        if (attendanceDate >= lastMonth && attendanceDate <= lastMonthEnd) {
          lastMonthTotal++;
          if (data.status === 'present') {
            lastMonthPresent++;
          } else if (data.status === 'absent') {
            lastMonthAbsent++;
          }
        }
      });

      setAttendance(attendanceData);
      setMarkedDates(markedDatesObj);

      setStatistics({
        present: presentCount,
        absent: absentCount,
        total: totalCount,
        percentage: totalCount > 0 ? (presentCount / totalCount) * 100 : 0,
        lastMonth: {
          present: lastMonthPresent,
          absent: lastMonthAbsent,
          total: lastMonthTotal,
          percentage: lastMonthTotal > 0 ? (lastMonthPresent / lastMonthTotal) * 100 : 0,
        }
      });

    } catch (error) {
      console.error('Error fetching attendance:', error);
      setError(translate('parentAttendance.fetchError'));
      setSnackbarMessage(translate('parentAttendance.fetchError'));
      setSnackbarVisible(true);
    } finally {
      setLoading(false);
    }
  };

  // Filter attendance based on search query and date filters
  const filterAttendance = () => {
    let filtered = [...attendance];

    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(record =>
        record.className?.toLowerCase().includes(query) ||
        record.sectionName?.toLowerCase().includes(query) ||
        record.status?.toLowerCase().includes(query) ||
        record.dayName?.toLowerCase().includes(query) ||
        record.monthName?.toLowerCase().includes(query)
      );
    }

    // Apply month and year filter
    if (selectedMonth !== null && selectedYear !== null) {
      filtered = filtered.filter(record =>
        record.month === selectedMonth && record.year === selectedYear
      );
    }

    setFilteredAttendance(filtered);
  };

  // Render loading state
  const renderLoading = () => (
    <View style={styles.loadingContainer}>
      <ActivityIndicator size="large" color={'#1976d2'} />
      <Text style={styles.loadingText}>{translate('common.loading')}</Text>
    </View>
  );

  // Render error state
  const renderError = () => (
    <View style={styles.errorContainer}>
      <IconButton
        icon="alert-circle"
        size={48}
        color={'#B00020'}
      />
      <Text style={styles.errorText}>{error}</Text>
      <Button
        mode="contained"
        onPress={() => selectedChild ? fetchAttendance(selectedChild.id) : fetchChildren()}
        style={styles.retryButton}
      >
        {translate('common.retry')}
      </Button>
    </View>
  );

  // Render empty state
  const renderEmpty = () => (
    <View style={styles.emptyContainer}>
      <IconButton
        icon="calendar-check"
        size={48}
        color={'#9e9e9e'}
      />
      <Text style={styles.emptyText}>
        {children.length === 0
          ? translate('parentAttendance.noChildren')
          : translate('parentAttendance.noAttendance')}
      </Text>
    </View>
  );

  // Render child selector
  const renderChildSelector = () => (
    <Card style={styles.childSelectorCard}>
      <Card.Content>
        <View style={styles.childSelectorContainer}>
          <Text style={styles.childSelectorLabel}>{translate('parentAttendance.selectChild')}</Text>

          <Menu
            visible={childMenuVisible}
            onDismiss={() => setChildMenuVisible(false)}
            anchor={
              <Button
                mode="outlined"
                onPress={() => setChildMenuVisible(true)}
                icon="account-child"
                style={styles.childSelectorButton}
              >
                {selectedChild
                  ? `${selectedChild.firstName} ${selectedChild.lastName}`
                  : translate('parentAttendance.selectChild')}
              </Button>
            }
          >
            {children.map(child => (
              <Menu.Item
                key={child.id}
                title={`${child.firstName} ${child.lastName}`}
                onPress={() => {
                  setSelectedChild(child);
                  setChildMenuVisible(false);
                }}
                leadingIcon="account-child"
              />
            ))}
          </Menu>
        </View>
      </Card.Content>
    </Card>
  );

  // Render statistics
  const renderStatistics = () => (
    <Card style={styles.statsCard}>
      <Card.Content>
        <Title style={styles.cardTitle}>{translate('parentAttendance.overview')}</Title>

        <View style={styles.statsContainer}>
          <View style={styles.statItem}>
            <Surface style={[styles.statBadge, { backgroundColor: '#E8F5E9' }]}>
              <IconButton icon="check-circle" size={24} color="#4CAF50" />
            </Surface>
            <Text style={styles.statLabel}>{translate('parentAttendance.present')}</Text>
            <Text style={[styles.statValue, { color: '#4CAF50' }]}>{statistics.present}</Text>
          </View>

          <View style={styles.statItem}>
            <Surface style={[styles.statBadge, { backgroundColor: '#FFEBEE' }]}>
              <IconButton icon="close-circle" size={24} color="#F44336" />
            </Surface>
            <Text style={styles.statLabel}>{translate('parentAttendance.absent')}</Text>
            <Text style={[styles.statValue, { color: '#F44336' }]}>{statistics.absent}</Text>
          </View>

          <View style={styles.statItem}>
            <Surface style={[styles.statBadge, { backgroundColor: '#E3F2FD' }]}>
              <IconButton icon="percent" size={24} color="#2196F3" />
            </Surface>
            <Text style={styles.statLabel}>{translate('parentAttendance.percentage')}</Text>
            <Text style={[
              styles.statValue,
              { color: statistics.percentage >= 75 ? '#4CAF50' : '#F44336' }
            ]}>
              {statistics.percentage.toFixed(1)}%
            </Text>
          </View>
        </View>

        <View style={styles.progressContainer}>
          <View style={styles.progressLabelContainer}>
            <Text style={styles.progressLabel}>{translate('parentAttendance.attendanceRate')}</Text>
            <Text style={[
              styles.progressValue,
              { color: statistics.percentage >= 75 ? '#4CAF50' : '#F44336' }
            ]}>
              {statistics.percentage.toFixed(1)}%
            </Text>
          </View>
          <ProgressBar
            progress={statistics.percentage / 100}
            color={statistics.percentage >= 75 ? '#4CAF50' : '#F44336'}
            style={styles.progressBar}
          />
        </View>

        {statistics.percentage < 75 && (
          <View style={styles.warningContainer}>
            <IconButton icon="alert" size={24} color="#FFC107" />
            <Text style={styles.warningText}>
              {translate('parentAttendance.attendanceWarning')}
            </Text>
          </View>
        )}
      </Card.Content>
    </Card>
  );

  // Render calendar view
  const renderCalendarView = () => (
    <Card style={styles.calendarCard}>
      <Card.Content>
        <Title style={styles.cardTitle}>{translate('parentAttendance.calendar')}</Title>

        <Calendar
          markedDates={markedDates}
          onDayPress={(day) => {
            setSelectedDate(day.dateString);

            // Find attendance for this date
            const record = attendance.find(r => r.formattedDate === day.dateString);
            if (record) {
              setSnackbarMessage(`${record.dayName}, ${record.formattedDate}: ${record.status === 'present' ? translate('parentAttendance.present') : translate('parentAttendance.absent')}`);
              setSnackbarVisible(true);
            }
          }}
          theme={{
            selectedDayBackgroundColor: '#1976d2',
            todayTextColor: '#1976d2',
            arrowColor: '#1976d2',
          }}
          style={styles.calendar}
        />

        <View style={styles.calendarLegend}>
          <View style={styles.legendItem}>
            <View style={[styles.legendDot, { backgroundColor: '#4CAF50' }]} />
            <Text style={styles.legendText}>{translate('parentAttendance.present')}</Text>
          </View>
          <View style={styles.legendItem}>
            <View style={[styles.legendDot, { backgroundColor: '#F44336' }]} />
            <Text style={styles.legendText}>{translate('parentAttendance.absent')}</Text>
          </View>
        </View>
      </Card.Content>
    </Card>
  );

  // Group attendance by month
  const groupedAttendance = filteredAttendance.reduce((acc, record) => {
    const monthYear = `${record.monthName} ${record.year}`;
    if (!acc[monthYear]) {
      acc[monthYear] = [];
    }
    acc[monthYear].push(record);
    return acc;
  }, {});

  // Render list view
  const renderListView = () => (
    <>
      <Card style={styles.searchCard}>
        <Card.Content>
          <Searchbar
            placeholder={translate('parentAttendance.search')}
            onChangeText={setSearchQuery}
            value={searchQuery}
            style={styles.searchBar}
          />

          <View style={styles.monthSelector}>
            <IconButton
              icon="chevron-left"
              onPress={() => {
                if (selectedMonth === 0) {
                  setSelectedMonth(11);
                  setSelectedYear(selectedYear - 1);
                } else {
                  setSelectedMonth(selectedMonth - 1);
                }
              }}
            />
            <Text style={styles.monthYearText}>
              {new Date(selectedYear, selectedMonth).toLocaleString(language === 'en' ? 'en-US' : 'am-ET', { month: 'long', year: 'numeric' })}
            </Text>
            <IconButton
              icon="chevron-right"
              onPress={() => {
                if (selectedMonth === 11) {
                  setSelectedMonth(0);
                  setSelectedYear(selectedYear + 1);
                } else {
                  setSelectedMonth(selectedMonth + 1);
                }
              }}
            />
          </View>
        </Card.Content>
      </Card>

      {Object.entries(groupedAttendance).length === 0 ? (
        <Card style={styles.monthCard}>
          <Card.Content style={styles.emptyMonthContent}>
            <IconButton
              icon="calendar-blank"
              size={48}
              color={'#9e9e9e'}
            />
            <Text style={styles.emptyMonthText}>
              {translate('parentAttendance.noAttendanceForMonth')}
            </Text>
          </Card.Content>
        </Card>
      ) : (
        Object.entries(groupedAttendance).map(([month, records]) => (
          <Card key={month} style={styles.monthCard}>
            <Card.Content>
              <Title style={styles.monthTitle}>{month}</Title>
              <Divider style={styles.divider} />

              {records.map((record, index) => (
                <List.Item
                  key={index}
                  title={`${record.dayName}, ${record.date.toLocaleDateString()}`}
                  description={`${record.className || ''} ${record.sectionName ? `- ${record.sectionName}` : ''}`}
                  left={props => (
                    <Avatar.Icon
                      {...props}
                      icon={record.status === 'present' ? 'check-circle' : 'close-circle'}
                      color="white"
                      style={{
                        backgroundColor: record.status === 'present' ? '#4CAF50' : '#F44336',
                      }}
                      size={40}
                    />
                  )}
                  right={() => (
                    <Chip
                      mode="outlined"
                      style={[
                        styles.statusChip,
                        record.status === 'present' ? styles.presentChip : styles.absentChip
                      ]}
                    >
                      {record.status === 'present'
                        ? translate('parentAttendance.present')
                        : translate('parentAttendance.absent')}
                    </Chip>
                  )}
                  style={styles.listItem}
                />
              ))}
            </Card.Content>
          </Card>
        ))
      )}
    </>
  );

  // Render statistics view
  const renderStatsView = () => (
    <Card style={styles.statsViewCard}>
      <Card.Content>
        <Title style={styles.cardTitle}>{translate('parentAttendance.statistics')}</Title>

        <View style={styles.statsSection}>
          <Text style={styles.statsSectionTitle}>{translate('parentAttendance.overall')}</Text>

          <View style={styles.statsRow}>
            <View style={styles.statCol}>
              <Text style={styles.statColLabel}>{translate('parentAttendance.totalDays')}</Text>
              <Text style={styles.statColValue}>{statistics.total}</Text>
            </View>
            <View style={styles.statCol}>
              <Text style={styles.statColLabel}>{translate('parentAttendance.presentDays')}</Text>
              <Text style={[styles.statColValue, { color: '#4CAF50' }]}>{statistics.present}</Text>
            </View>
            <View style={styles.statCol}>
              <Text style={styles.statColLabel}>{translate('parentAttendance.absentDays')}</Text>
              <Text style={[styles.statColValue, { color: '#F44336' }]}>{statistics.absent}</Text>
            </View>
          </View>

          <View style={styles.progressContainer}>
            <View style={styles.progressLabelContainer}>
              <Text style={styles.progressLabel}>{translate('parentAttendance.attendanceRate')}</Text>
              <Text style={[
                styles.progressValue,
                { color: statistics.percentage >= 75 ? '#4CAF50' : '#F44336' }
              ]}>
                {statistics.percentage.toFixed(1)}%
              </Text>
            </View>
            <ProgressBar
              progress={statistics.percentage / 100}
              color={statistics.percentage >= 75 ? '#4CAF50' : '#F44336'}
              style={styles.progressBar}
            />
          </View>
        </View>

        <Divider style={styles.statsDivider} />

        <View style={styles.statsSection}>
          <Text style={styles.statsSectionTitle}>{translate('parentAttendance.lastMonth')}</Text>

          <View style={styles.statsRow}>
            <View style={styles.statCol}>
              <Text style={styles.statColLabel}>{translate('parentAttendance.totalDays')}</Text>
              <Text style={styles.statColValue}>{statistics.lastMonth.total}</Text>
            </View>
            <View style={styles.statCol}>
              <Text style={styles.statColLabel}>{translate('parentAttendance.presentDays')}</Text>
              <Text style={[styles.statColValue, { color: '#4CAF50' }]}>{statistics.lastMonth.present}</Text>
            </View>
            <View style={styles.statCol}>
              <Text style={styles.statColLabel}>{translate('parentAttendance.absentDays')}</Text>
              <Text style={[styles.statColValue, { color: '#F44336' }]}>{statistics.lastMonth.absent}</Text>
            </View>
          </View>

          <View style={styles.progressContainer}>
            <View style={styles.progressLabelContainer}>
              <Text style={styles.progressLabel}>{translate('parentAttendance.attendanceRate')}</Text>
              <Text style={[
                styles.progressValue,
                { color: statistics.lastMonth.percentage >= 75 ? '#4CAF50' : '#F44336' }
              ]}>
                {statistics.lastMonth.percentage.toFixed(1)}%
              </Text>
            </View>
            <ProgressBar
              progress={statistics.lastMonth.percentage / 100}
              color={statistics.lastMonth.percentage >= 75 ? '#4CAF50' : '#F44336'}
              style={styles.progressBar}
            />
          </View>
        </View>

        <Divider style={styles.statsDivider} />

        <View style={styles.policyContainer}>
          <Text style={styles.policyTitle}>{translate('parentAttendance.policy')}</Text>
          <Text style={styles.policyText}>
            {translate('parentAttendance.minimumRequired')}: 75%{'\n'}
            {translate('parentAttendance.currentAttendance')}: {statistics.percentage.toFixed(1)}%{'\n'}
            {statistics.percentage < 75 ?
              translate('parentAttendance.warningMessage') :
              translate('parentAttendance.goodJobMessage')}
          </Text>
        </View>
      </Card.Content>
    </Card>
  );

  return (
    <SafeAreaView style={styles.safeArea}>
      <StatusBar backgroundColor={'#1976d2'} barStyle="light-content" />

      {/* Parent App Header */}
      <ParentAppHeader
        title={translate('parentAttendance.title')}
        navigation={navigation}
        showNotification={true}
      />

      {/* Main Content */}
      <Animated.View
        style={[
          styles.content,
          { opacity: fadeAnim }
        ]}
      >
        <ScrollView style={styles.scrollView}>
          {childrenLoading ? (
            renderLoading()
          ) : error ? (
            renderError()
          ) : children.length === 0 ? (
            renderEmpty()
          ) : (
            <>
              {renderChildSelector()}

              {loading ? (
                renderLoading()
              ) : error ? (
                renderError()
              ) : attendance.length === 0 ? (
                <View style={styles.emptyContainer}>
                  <IconButton
                    icon="calendar-check"
                    size={48}
                    color={'#9e9e9e'}
                  />
                  <Text style={styles.emptyText}>
                    {translate('parentAttendance.noAttendance')}
                  </Text>
                </View>
              ) : (
                <>
                  {renderStatistics()}

                  <View style={styles.viewModeContainer}>
                    <SegmentedButtons
                      value={viewMode}
                      onValueChange={setViewMode}
                      buttons={[
                        {
                          value: 'list',
                          icon: 'format-list-bulleted',
                          label: translate('parentAttendance.listView')
                        },
                        {
                          value: 'calendar',
                          icon: 'calendar',
                          label: translate('parentAttendance.calendarView')
                        },
                        {
                          value: 'stats',
                          icon: 'chart-bar',
                          label: translate('parentAttendance.statsView')
                        }
                      ]}
                      style={styles.segmentedButtons}
                    />
                  </View>

                  {viewMode === 'calendar' && renderCalendarView()}
                  {viewMode === 'list' && renderListView()}
                  {viewMode === 'stats' && renderStatsView()}
                </>
              )}
            </>
          )}
        </ScrollView>
      </Animated.View>

      {/* Snackbar for notifications */}
      <Snackbar
        visible={snackbarVisible}
        onDismiss={() => setSnackbarVisible(false)}
        duration={3000}
        style={styles.snackbar}
      >
        {snackbarMessage}
      </Snackbar>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  scrollView: {
    flex: 1,
  },
  loadingContainer: {
    padding: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#666',
  },
  errorContainer: {
    padding: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  errorText: {
    marginTop: 12,
    marginBottom: 16,
    fontSize: 16,
    color: '#F44336',
    textAlign: 'center',
  },
  retryButton: {
    marginTop: 8,
  },
  emptyContainer: {
    padding: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyText: {
    marginTop: 12,
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  childSelectorCard: {
    marginBottom: 16,
    borderRadius: 8,
  },
  childSelectorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  childSelectorLabel: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  childSelectorButton: {
    minWidth: 150,
  },
  placeholderText: {
    textAlign: 'center',
    padding: 24,
    color: '#666',
  },
  statsCard: {
    marginBottom: 16,
    borderRadius: 8,
  },
  cardTitle: {
    fontSize: 18,
    marginBottom: 12,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 16,
    marginBottom: 16,
  },
  statItem: {
    alignItems: 'center',
  },
  statBadge: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  statLabel: {
    color: '#666',
    fontSize: 14,
    marginBottom: 4,
  },
  statValue: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  progressContainer: {
    marginVertical: 16,
  },
  progressLabelContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  progressLabel: {
    fontSize: 14,
    color: '#666',
  },
  progressValue: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  progressBar: {
    height: 8,
    borderRadius: 4,
  },
  warningContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFF8E1',
    padding: 8,
    borderRadius: 8,
    marginTop: 16,
  },
  warningText: {
    color: '#F57F17',
    flex: 1,
  },
  viewModeContainer: {
    marginBottom: 16,
  },
  segmentedButtons: {
    marginHorizontal: 16,
  },
  calendarCard: {
    marginBottom: 16,
    borderRadius: 8,
  },
  calendar: {
    marginBottom: 16,
  },
  calendarLegend: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 8,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 12,
  },
  legendDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  legendText: {
    fontSize: 14,
    color: '#666',
  },
  searchCard: {
    marginBottom: 16,
    borderRadius: 8,
  },
  searchBar: {
    marginBottom: 12,
    elevation: 0,
    backgroundColor: '#f0f0f0',
  },
  monthSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  monthYearText: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  monthCard: {
    marginBottom: 16,
    borderRadius: 8,
  },
  monthTitle: {
    fontSize: 18,
    marginBottom: 8,
  },
  divider: {
    marginVertical: 8,
  },
  listItem: {
    paddingVertical: 8,
  },
  statusChip: {
    height: 28,
  },
  presentChip: {
    backgroundColor: '#E8F5E9',
    borderColor: '#4CAF50',
  },
  absentChip: {
    backgroundColor: '#FFEBEE',
    borderColor: '#F44336',
  },
  emptyMonthContent: {
    alignItems: 'center',
    padding: 24,
  },
  emptyMonthText: {
    marginTop: 12,
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  statsViewCard: {
    marginBottom: 16,
    borderRadius: 8,
  },
  statsSection: {
    marginBottom: 16,
  },
  statsSectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  statCol: {
    alignItems: 'center',
    flex: 1,
  },
  statColLabel: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
    textAlign: 'center',
  },
  statColValue: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  statsDivider: {
    marginVertical: 16,
  },
  policyContainer: {
    backgroundColor: '#FFF8E1',
    padding: 16,
    borderRadius: 8,
    marginTop: 8,
  },
  policyTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  policyText: {
    lineHeight: 24,
  },
  snackbar: {
    bottom: 16,
  },
});

export default ParentAttendanceView;
