import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:easy_localization/easy_localization.dart';

class LanguageContext extends ChangeNotifier {
  static const String _languageKey = 'selected_language';
  
  Locale _currentLocale = const Locale('en', 'US');
  bool _isRTL = false;
  
  // Supported languages
  static const List<LanguageModel> supportedLanguages = [
    LanguageModel(
      code: 'en',
      countryCode: 'US',
      name: 'English',
      nativeName: 'English',
      isRTL: false,
    ),
    LanguageModel(
      code: 'am',
      countryCode: 'ET',
      name: 'Amharic',
      nativeName: 'አማርኛ',
      isRTL: false,
    ),
    LanguageModel(
      code: 'om',
      countryCode: 'ET',
      name: 'Oromo',
      nativeName: 'Afaan Oromoo',
      isRTL: false,
    ),
    LanguageModel(
      code: 'or',
      countryCode: 'ET',
      name: 'Oromo (Alternative)',
      nativeName: 'Afaan <PERSON>mo<PERSON>',
      isRTL: false,
    ),
  ];

  // Getters
  Locale get currentLocale => _currentLocale;
  bool get isRTL => _isRTL;
  String get currentLanguageCode => _currentLocale.languageCode;
  String get currentLanguageName => getCurrentLanguage().name;
  String get currentLanguageNativeName => getCurrentLanguage().nativeName;

  LanguageContext() {
    _loadSavedLanguage();
  }

  LanguageModel getCurrentLanguage() {
    return supportedLanguages.firstWhere(
      (lang) => lang.code == _currentLocale.languageCode,
      orElse: () => supportedLanguages.first,
    );
  }

  Future<void> _loadSavedLanguage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedLanguageCode = prefs.getString(_languageKey);
      
      if (savedLanguageCode != null) {
        final language = supportedLanguages.firstWhere(
          (lang) => lang.code == savedLanguageCode,
          orElse: () => supportedLanguages.first,
        );
        
        _currentLocale = Locale(language.code, language.countryCode);
        _isRTL = language.isRTL;
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error loading saved language: $e');
    }
  }

  Future<void> changeLanguage(String languageCode, BuildContext context) async {
    try {
      final language = supportedLanguages.firstWhere(
        (lang) => lang.code == languageCode,
        orElse: () => supportedLanguages.first,
      );

      _currentLocale = Locale(language.code, language.countryCode);
      _isRTL = language.isRTL;

      // Save to SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_languageKey, languageCode);

      // Update EasyLocalization
      await context.setLocale(_currentLocale);

      notifyListeners();
    } catch (e) {
      debugPrint('Error changing language: $e');
    }
  }

  String translate(String key, {Map<String, dynamic>? args}) {
    try {
      return key.tr(args: args);
    } catch (e) {
      debugPrint('Translation error for key "$key": $e');
      return key; // Return the key itself if translation fails
    }
  }

  String translateWithFallback(String key, String fallback, {Map<String, dynamic>? args}) {
    try {
      final translation = key.tr(args: args);
      return translation == key ? fallback : translation;
    } catch (e) {
      debugPrint('Translation error for key "$key": $e');
      return fallback;
    }
  }

  bool isLanguageSupported(String languageCode) {
    return supportedLanguages.any((lang) => lang.code == languageCode);
  }

  List<LanguageModel> getAvailableLanguages() {
    return List.from(supportedLanguages);
  }

  String getLanguageDisplayName(String languageCode) {
    final language = supportedLanguages.firstWhere(
      (lang) => lang.code == languageCode,
      orElse: () => supportedLanguages.first,
    );
    return language.nativeName;
  }

  TextDirection getTextDirection() {
    return _isRTL ? TextDirection.rtl : TextDirection.ltr;
  }

  // Format numbers according to locale
  String formatNumber(num number) {
    final formatter = NumberFormat.decimalPattern(_currentLocale.toString());
    return formatter.format(number);
  }

  // Format currency according to locale
  String formatCurrency(num amount, {String? symbol}) {
    final formatter = NumberFormat.currency(
      locale: _currentLocale.toString(),
      symbol: symbol ?? 'ETB', // Ethiopian Birr as default
    );
    return formatter.format(amount);
  }

  // Format date according to locale
  String formatDate(DateTime date, {String? pattern}) {
    final formatter = DateFormat(pattern ?? 'MMM dd, yyyy', _currentLocale.toString());
    return formatter.format(date);
  }

  // Format time according to locale
  String formatTime(DateTime time) {
    final formatter = DateFormat.jm(_currentLocale.toString());
    return formatter.format(time);
  }
}

class LanguageModel {
  final String code;
  final String countryCode;
  final String name;
  final String nativeName;
  final bool isRTL;

  const LanguageModel({
    required this.code,
    required this.countryCode,
    required this.name,
    required this.nativeName,
    required this.isRTL,
  });

  Locale get locale => Locale(code, countryCode);

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LanguageModel && other.code == code;
  }

  @override
  int get hashCode => code.hashCode;
}
