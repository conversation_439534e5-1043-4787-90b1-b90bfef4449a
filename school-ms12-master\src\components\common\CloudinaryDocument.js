import React, { useState, useEffect } from 'react';
import { View, StyleSheet, TouchableOpacity, Text, ActivityIndicator, Linking, Alert } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import CloudinaryService from '../../services/CloudinaryService';
import CloudinaryImage from './CloudinaryImage';
import { useLanguage } from '../../context/LanguageContext';

/**
 * CloudinaryDocument component for displaying documents from Cloudinary
 * This component supports both Cloudinary public IDs and Firebase Storage URLs
 * 
 * @param {Object} props - Component props
 * @param {string} props.source - Cloudinary public ID or Firebase Storage URL
 * @param {string} props.fileName - Name of the document to display
 * @param {string} props.fileType - Type of the document (pdf, doc, docx, etc.)
 * @param {Object} props.style - Style object for the component
 * @param {number} props.width - Width of the component
 * @param {number} props.height - Height of the component
 * @param {number} props.borderRadius - Border radius for the component
 * @param {boolean} props.showPreview - Whether to show a preview of the document
 * @param {boolean} props.showLoadingIndicator - Whether to show a loading indicator while the document is loading
 * @param {Function} props.onPress - Callback function to call when the document is pressed
 * @param {Function} props.onError - Callback function to call when the document fails to load
 */
const CloudinaryDocument = ({
  source,
  fileName = 'Document',
  fileType,
  style = {},
  width = 300,
  height = 150,
  borderRadius = 8,
  showPreview = true,
  showLoadingIndicator = true,
  onPress,
  onError,
  ...props
}) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);
  const [documentUrl, setDocumentUrl] = useState(null);
  const [previewUrl, setPreviewUrl] = useState(null);
  const { translate } = useLanguage();

  // Determine file type from fileName if not provided
  const getFileType = () => {
    if (fileType) return fileType.toLowerCase();
    
    if (fileName) {
      const parts = fileName.split('.');
      if (parts.length > 1) {
        return parts[parts.length - 1].toLowerCase();
      }
    }
    
    // Default to pdf if can't determine
    return 'pdf';
  };

  // Get icon for file type
  const getFileIcon = () => {
    const type = getFileType();
    
    switch (type) {
      case 'pdf':
        return 'picture-as-pdf';
      case 'doc':
      case 'docx':
        return 'description';
      case 'xls':
      case 'xlsx':
        return 'table-chart';
      case 'ppt':
      case 'pptx':
        return 'slideshow';
      case 'txt':
        return 'text-snippet';
      case 'zip':
      case 'rar':
        return 'folder-zip';
      default:
        return 'insert-drive-file';
    }
  };

  useEffect(() => {
    if (!source) {
      setError(true);
      setLoading(false);
      return;
    }

    try {
      // Generate Cloudinary URL for the document
      let docUrl;
      if (typeof source === 'string' && 
          (source.includes('firebasestorage.googleapis.com') || 
           source.includes('storage.googleapis.com'))) {
        // For Firebase Storage URLs, use the fetch capability
        docUrl = source; // Use original URL for download
      } else {
        // For Cloudinary public IDs
        docUrl = CloudinaryService.getUrl(source, {
          resourceType: 'raw',
          flags: 'attachment'
        });
      }
      
      setDocumentUrl(docUrl);
      
      // Generate preview URL if showing preview
      if (showPreview) {
        const type = getFileType();
        if (['pdf', 'doc', 'docx', 'ppt', 'pptx'].includes(type)) {
          // Generate a preview image for the first page
          const previewOptions = {
            page: 1,
            width: width * 2, // Higher resolution for better quality
            format: 'jpg'
          };
          
          if (typeof source === 'string' && 
              (source.includes('firebasestorage.googleapis.com') || 
               source.includes('storage.googleapis.com'))) {
            // For Firebase Storage URLs, we can't generate previews directly
            // Use a placeholder based on file type
            setPreviewUrl(null);
          } else {
            // For Cloudinary public IDs
            const preview = CloudinaryService.getDocumentPreviewUrl(source, previewOptions);
            setPreviewUrl(preview);
          }
        }
      }
      
      setLoading(false);
    } catch (err) {
      console.error('Error generating Cloudinary document URL:', err);
      setError(true);
      setLoading(false);
      if (onError) onError(err);
    }
  }, [source, width, height, showPreview]);

  // Handle document press
  const handlePress = async () => {
    if (!documentUrl) return;
    
    try {
      if (onPress) {
        onPress(documentUrl);
        return;
      }
      
      // Open the document URL
      const supported = await Linking.canOpenURL(documentUrl);
      
      if (supported) {
        await Linking.openURL(documentUrl);
      } else {
        Alert.alert(
          translate('errors.openDocumentTitle') || 'Cannot Open Document',
          translate('errors.openDocumentMessage') || 'Unable to open this document. Please try again later.',
          [{ text: translate('common.ok') || 'OK' }]
        );
      }
    } catch (error) {
      console.error('Error opening document:', error);
      Alert.alert(
        translate('errors.openDocumentTitle') || 'Error',
        translate('errors.openDocumentMessage') || 'Failed to open document. Please try again.',
        [{ text: translate('common.ok') || 'OK' }]
      );
    }
  };

  // Calculate container style
  const containerStyle = {
    width,
    height,
    borderRadius,
    ...style,
  };

  return (
    <TouchableOpacity
      style={[styles.container, containerStyle]}
      onPress={handlePress}
      activeOpacity={0.8}
      disabled={loading || error || !documentUrl}
      {...props}
    >
      {loading && showLoadingIndicator ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="small" color="#999" />
          <Text style={styles.loadingText}>
            {translate('common.loading') || 'Loading...'}
          </Text>
        </View>
      ) : error ? (
        <View style={styles.errorContainer}>
          <MaterialIcons name="error-outline" size={32} color="#f44336" />
          <Text style={styles.errorText}>
            {translate('errors.documentLoadFailed') || 'Failed to load document'}
          </Text>
        </View>
      ) : (
        <View style={styles.documentContainer}>
          {showPreview && previewUrl ? (
            // Show document preview
            <View style={styles.previewContainer}>
              <CloudinaryImage
                source={previewUrl}
                style={styles.preview}
                width={width}
                height={height - 50}
                resizeMode="contain"
                borderRadius={borderRadius}
              />
            </View>
          ) : (
            // Show file type icon
            <View style={styles.iconContainer}>
              <MaterialIcons name={getFileIcon()} size={48} color="#555" />
            </View>
          )}
          
          <View style={styles.infoContainer}>
            <Text style={styles.fileName} numberOfLines={1} ellipsizeMode="middle">
              {fileName}
            </Text>
            <Text style={styles.fileType}>
              {getFileType().toUpperCase()}
            </Text>
          </View>
          
          <View style={styles.downloadButton}>
            <MaterialIcons name="file-download" size={24} color="#fff" />
          </View>
        </View>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#f5f5f5',
    borderWidth: 1,
    borderColor: '#ddd',
    overflow: 'hidden',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 8,
    color: '#999',
    fontSize: 14,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  errorText: {
    marginTop: 8,
    color: '#f44336',
    fontSize: 14,
    textAlign: 'center',
  },
  documentContainer: {
    flex: 1,
    position: 'relative',
  },
  previewContainer: {
    flex: 1,
    marginBottom: 40,
  },
  preview: {
    width: '100%',
    height: '100%',
  },
  iconContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 40,
  },
  infoContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 40,
    backgroundColor: '#eee',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingRight: 50,
  },
  fileName: {
    flex: 1,
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
  },
  fileType: {
    fontSize: 12,
    color: '#666',
    marginLeft: 8,
  },
  downloadButton: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 40,
    height: 40,
    backgroundColor: '#2196F3',
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default CloudinaryDocument;
