// Script to create a parent user
const { initializeApp } = require('firebase/app');
const {
  getAuth,
  createUserWithEmailAndPassword,
  signOut,
  updateProfile
} = require('firebase/auth');
const {
  getFirestore,
  doc,
  setDoc
} = require('firebase/firestore');

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyBHkGAhm80xBIECyaHbZ_DbeNzu3jnVuDg",
  authDomain: "schoolmn-16cbc.firebaseapp.com",
  projectId: "schoolmn-16cbc",
  storageBucket: "schoolmn-16cbc.appspot.com",
  messagingSenderId: "999485613068",
  appId: "1:999485613068:web:e9c0c3e0a4c7f4e4c8b8b8"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const auth = getAuth(app);
const db = getFirestore(app);

// Parent user data
const parentData = {
  email: '<EMAIL>',
  password: '1234qwer',
  role: 'parent',
  firstName: 'Parent',
  lastName: 'User',
  displayName: 'Parent User',
  phone: '4567890123',
  address: '101 Parent Rd',
  status: 'active',
  occupation: 'Engineer',
  relation: 'Father'
};

// Function to create a parent user
async function createParentUser() {
  try {
    console.log(`Creating parent user: ${parentData.email}`);

    // Create user with Firebase Auth
    const userCredential = await createUserWithEmailAndPassword(
      auth,
      parentData.email,
      parentData.password
    );

    const user = userCredential.user;

    // Update user profile
    await updateProfile(user, {
      displayName: parentData.displayName
    });

    console.log(`Created auth user with UID: ${user.uid}`);

    // Create user document in Firestore
    await setDoc(doc(db, 'users', user.uid), {
      uid: user.uid,
      email: parentData.email,
      firstName: parentData.firstName,
      lastName: parentData.lastName,
      displayName: parentData.displayName,
      phone: parentData.phone,
      address: parentData.address,
      role: parentData.role,
      status: parentData.status,
      emailVerified: true, // Set to true to avoid verification
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    });

    console.log(`Created user document in 'users' collection`);

    // Create parent-specific document
    const roleData = { ...parentData };
    delete roleData.password; // Remove password from data

    await setDoc(doc(db, 'parents', user.uid), {
      userId: user.uid,
      ...roleData,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    });

    console.log(`Created document in 'parents' collection`);

    // Sign out after creating the user
    await signOut(auth);

    console.log(`Successfully created parent user with UID: ${user.uid}`);
    return user.uid;
  } catch (error) {
    console.error(`Error creating parent user:`, error.message);
    if (error.code === 'auth/email-already-in-use') {
      console.log(`User with email ${parentData.email} already exists`);
    }
    return null;
  }
}

// Run the script
createParentUser().then(() => {
  console.log('Parent user creation completed');
  process.exit(0);
}).catch(error => {
  console.error('Error in script execution:', error);
  process.exit(1);
});
