import React, { useState } from 'react';
import { View, StyleSheet } from 'react-native';
import { Button, Dialog, Portal, Text, Paragraph, ActivityIndicator } from 'react-native-paper';
import { db, auth } from '../../config/firebase';
import { doc, updateDoc, collection, query, where, getDocs, serverTimestamp } from 'firebase/firestore';
import { useLanguage } from '../../context/LanguageContext';
import { useNotifications } from '../../context/NotificationContext';

const ResultApprovalButton = ({ classId, className, sectionName, students, onSuccess }) => {
  const { translate } = useLanguage();
  const { sendBulkNotifications } = useNotifications();
  
  const [approvalDialogVisible, setApprovalDialogVisible] = useState(false);
  const [processing, setProcessing] = useState(false);
  const [error, setError] = useState(null);

  // Handle approve button click
  const handleApproveClick = () => {
    setApprovalDialogVisible(true);
  };

  // Approve results and notify users
  const approveResults = async () => {
    try {
      setProcessing(true);
      setError(null);

      if (!students || students.length === 0) {
        throw new Error('No students found to approve results');
      }

      // 1. Update results status to approved
      const resultsRef = collection(db, 'results');
      const resultsQuery = query(
        resultsRef,
        where('className', '==', className),
        where('sectionName', '==', sectionName),
        where('status', '==', 'pending')
      );

      const resultsSnapshot = await getDocs(resultsQuery);
      
      if (resultsSnapshot.empty) {
        throw new Error('No pending results found to approve');
      }

      // Update all results to approved
      const updatePromises = [];
      resultsSnapshot.forEach(doc => {
        updatePromises.push(
          updateDoc(doc.ref, {
            status: 'approved',
            approvedBy: auth.currentUser?.uid || '',
            approvedByName: auth.currentUser?.displayName || auth.currentUser?.email || 'Unknown Admin',
            approvedAt: serverTimestamp()
          })
        );
      });

      await Promise.all(updatePromises);

      // 2. Collect user IDs for notifications
      const studentIds = students.map(student => student.uid || student.id);
      
      // Get parent IDs
      const parentIds = [];
      for (const student of students) {
        if (student.parentId) {
          parentIds.push(student.parentId);
        }
      }
      
      // Get teacher IDs
      const teacherIds = [];
      const teachersRef = collection(db, 'users');
      const teachersQuery = query(
        teachersRef,
        where('role', '==', 'teacher'),
        where('assignedClasses', 'array-contains', classId)
      );
      
      const teachersSnapshot = await getDocs(teachersQuery);
      teachersSnapshot.forEach(doc => {
        teacherIds.push(doc.id);
      });

      // 3. Send notifications to all users
      const allUserIds = [...new Set([...studentIds, ...parentIds, ...teacherIds])];
      
      await sendBulkNotifications(allUserIds, {
        title: translate('notifications.results.approvedTitle') || 'Results Approved',
        body: translate('notifications.results.approvedBody', { className, sectionName }) || 
              `Results for ${className}-${sectionName} have been approved and are now available.`,
        data: {
          type: 'RESULTS_APPROVED',
          className,
          sectionName,
          timestamp: new Date().toISOString()
        }
      });

      // 4. Call success callback
      if (onSuccess) {
        onSuccess();
      }

      setApprovalDialogVisible(false);
    } catch (error) {
      console.error('Error approving results:', error);
      setError(error.message);
    } finally {
      setProcessing(false);
    }
  };

  return (
    <View style={styles.container}>
      <Button
        mode="contained"
        icon="check-circle"
        onPress={handleApproveClick}
        style={styles.approveButton}
        disabled={processing}
      >
        {translate('admin.results.approve') || 'Approve & Notify'}
      </Button>

      <Portal>
        <Dialog
          visible={approvalDialogVisible}
          onDismiss={() => !processing && setApprovalDialogVisible(false)}
          style={styles.dialog}
        >
          <Dialog.Title>{translate('admin.results.approveResults') || 'Approve Results'}</Dialog.Title>
          <Dialog.Content>
            {error ? (
              <Text style={styles.errorText}>{error}</Text>
            ) : (
              <>
                <Paragraph>
                  {translate('admin.results.approveConfirmation') || 
                   'Are you sure you want to approve the results for this class?'}
                </Paragraph>
                <Paragraph style={styles.warningText}>
                  {translate('admin.results.approveWarning') || 
                   'This will notify all students, parents, and teachers about the approved results.'}
                </Paragraph>
              </>
            )}
            
            {processing && (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="small" color="#1976d2" />
                <Text style={styles.loadingText}>
                  {translate('admin.results.processing') || 'Processing...'}
                </Text>
              </View>
            )}
          </Dialog.Content>
          <Dialog.Actions>
            <Button 
              onPress={() => setApprovalDialogVisible(false)}
              disabled={processing}
            >
              {translate('common.cancel') || 'Cancel'}
            </Button>
            <Button
              mode="contained"
              onPress={approveResults}
              disabled={processing}
              style={styles.confirmButton}
            >
              {translate('admin.results.confirm') || 'Confirm'}
            </Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 16,
  },
  approveButton: {
    backgroundColor: '#4CAF50',
  },
  dialog: {
    borderRadius: 8,
  },
  errorText: {
    color: '#F44336',
    marginBottom: 8,
  },
  warningText: {
    color: '#FF9800',
    marginTop: 8,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 16,
  },
  loadingText: {
    marginLeft: 8,
  },
  confirmButton: {
    backgroundColor: '#4CAF50',
  },
});

export default ResultApprovalButton;
