const fs = require('fs');
const path = require('path');

// Function to recursively find all JS files in a directory
function findJsFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);

  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);

    if (stat.isDirectory() && file !== 'node_modules' && file !== '.git') {
      findJsFiles(filePath, fileList);
    } else if (stat.isFile() && (file.endsWith('.js') || file.endsWith('.jsx'))) {
      fileList.push(filePath);
    }
  });

  return fileList;
}

// Function to fix dark card issues in a file
function fixDarkCardIssues(filePath) {
  console.log(`Processing ${filePath}...`);
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;

  // Replace any dark background colors in Card components
  const darkCardRegex = /<Card[^>]*style=\{[^}]*backgroundColor:\s*['"]?(#000000|#000|black|#111|#222|#333)['"]?[^}]*\}/g;
  if (content.match(darkCardRegex)) {
    content = content.replace(darkCardRegex, (match) => {
      return match.replace(/(backgroundColor:\s*['"]?)(#000000|#000|black|#111|#222|#333)(['"]?)/, '$1#ffffff$3');
    });
    modified = true;
  }

  // Replace any dark theme settings
  if (content.includes('theme: \'dark\'')) {
    content = content.replace(/theme:\s*['"]dark['"]/g, 'theme: \'light\'');
    modified = true;
  }

  // Replace any PaperProvider without theme prop
  if (content.includes('<PaperProvider>')) {
    content = content.replace(/<PaperProvider>/g, '<PaperProvider theme={DefaultTheme}>');
    
    // Add import for DefaultTheme if not already present
    if (!content.includes('import { DefaultTheme }')) {
      content = content.replace(
        /import {([^}]*)}/,
        (match, p1) => `import { DefaultTheme, ${p1.trim()} }`
      );
      
      // If no import with curly braces, add it after the first import
      if (!content.includes('import { DefaultTheme }')) {
        content = content.replace(
          /(import [^;]*;)/,
          '$1\nimport { DefaultTheme } from \'react-native-paper\';'
        );
      }
    }
    
    modified = true;
  }

  // Save the file if modified
  if (modified) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`Fixed dark card issues in ${filePath}`);
    return true;
  }

  return false;
}

// Main function
function main() {
  const srcDir = path.join(__dirname, 'src');
  const jsFiles = findJsFiles(srcDir);
  let fixedCount = 0;

  jsFiles.forEach(file => {
    if (fixDarkCardIssues(file)) {
      fixedCount++;
    }
  });

  console.log(`\nFixed dark card issues in ${fixedCount} files.`);
}

main();
