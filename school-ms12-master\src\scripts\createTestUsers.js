const { initializeApp } = require('@firebase/app');
const { getAuth, createUserWithEmailAndPassword } = require('@firebase/auth');
const { getFirestore, doc, setDoc } = require('@firebase/firestore');

// Initialize Firebase
const firebaseConfig = {
  apiKey: "AIzaSyBHkGAhm80xBIECyaHbZ_DbeNzu3jnVuDg",
  authDomain: "schoolmn-16cbc.firebaseapp.com",
  projectId: "schoolmn-16cbc",
  storageBucket: "schoolmn-16cbc.firebasestorage.app",
  messagingSenderId: "999485613068",
  appId: "G-LLCT41RL38"
};

const app = initializeApp(firebaseConfig);
const auth = getAuth(app);
const db = getFirestore(app);

const testUsers = [
  {
    email: '<EMAIL>',
    password: 'Admin123!',
    role: 'admin',
    name: 'Admin User'
  },
  {
    email: '<EMAIL>',
    password: 'Teacher123!',
    role: 'teacher',
    name: 'Teacher User'
  },
  {
    email: '<EMAIL>',
    password: 'Student123!',
    role: 'student',
    name: 'Student User'
  },
  {
    email: '<EMAIL>',
    password: 'Parent123!',
    role: 'parent',
    name: 'Parent User'
  }
];

const createTestUser = async (userData) => {
  try {
    // Create the user in Firebase Auth
    const userCredential = await createUserWithEmailAndPassword(
      auth,
      userData.email,
      userData.password
    );

    // Create the user document in Firestore
    await setDoc(doc(db, 'users', userCredential.user.uid), {
      email: userData.email,
      role: userData.role,
      name: userData.name,
      createdAt: new Date().toISOString()
    });

    console.log(`Created ${userData.role} user:`, userData.email);
  } catch (error) {
    if (error.code === 'auth/email-already-in-use') {
      console.log(`User ${userData.email} already exists`);
    } else {
      console.error(`Error creating ${userData.role} user:`, error);
    }
  }
};

const createAllTestUsers = async () => {
  for (const userData of testUsers) {
    await createTestUser(userData);
  }
};

// Run the script
createAllTestUsers().then(() => {
  console.log('Finished creating test users');
}).catch((error) => {
  console.error('Error in createAllTestUsers:', error);
});
