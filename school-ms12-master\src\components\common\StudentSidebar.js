import React, { useState, useEffect } from 'react';
import { View, ScrollView, StyleSheet, Animated, Dimensions, TouchableOpacity, Text } from 'react-native';
import { Title, Paragraph, Divider, List, useTheme, Badge, Surface } from 'react-native-paper';
import CloudinaryAvatar from './CloudinaryAvatar';
import { LinearGradient } from 'expo-linear-gradient';
import { useLanguage } from '../../context/LanguageContext';
import { useAuth } from '../../context/AuthContext';
import LanguageSelector from './LanguageSelector';
import * as Animatable from 'react-native-animatable';
import { MaterialCommunityIcons } from '@expo/vector-icons';

const StudentSidebar = ({ visible, onClose, onDismiss, navigation, activeSidebarItem, setActiveSidebarItem, onItemPress }) => {
  const theme = useTheme();
  const { translate, language, getTextStyle, isRTL } = useLanguage();
  const { user, logout } = useAuth();

  // Debug log for translation
  useEffect(() => {
    console.log('Student Sidebar - Current language:', language);
    console.log('Student Sidebar - Translation test:', translate('studentDashboard.dashboard'));
  }, [language, translate]);

  const drawerAnim = React.useRef(new Animated.Value(visible ? 0 : -300)).current;

  React.useEffect(() => {
    Animated.timing(drawerAnim, {
      toValue: visible ? 0 : -300,
      duration: 250,
      useNativeDriver: true,
    }).start();
  }, [visible, drawerAnim]);

  const handleNavigation = (screen) => {
    if (screen === 'logout') {
      logout();
      return;
    }

    // Support both callback patterns
    if (onItemPress) {
      onItemPress({ key: screen, route: screen });
    } else if (setActiveSidebarItem) {
      setActiveSidebarItem(screen);
      navigation.navigate(screen);
    }

    if (Dimensions.get('window').width < 768) {
      // Use the appropriate close method based on what was provided
      if (onDismiss) {
        onDismiss();
      } else if (onClose) {
        onClose();
      }
    }
  };

  return (
    <Animated.View
      style={[styles.sidebar, { transform: [{ translateX: drawerAnim }] }]}
    >
      <LinearGradient
        colors={['#1976d2', '#1565C0']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.sidebarHeader}
      >
        <Animatable.View
          animation="fadeIn"
          duration={800}
          style={styles.sidebarProfile}
        >
          <TouchableOpacity onPress={() => handleNavigation('ProfileManagement')}>
            <CloudinaryAvatar
              size={80}
              source={user?.photoURL}
              label={user?.displayName?.substring(0, 2) || 'ST'}
              style={styles.sidebarAvatar}
              backgroundColor="#2196F3"
            />
            <Animatable.View animation="fadeInUp" delay={300} duration={800}>
              <Title style={[styles.sidebarName, getTextStyle({ fontSize: 18 })]}>
                {user?.displayName || translate('roles.student')}
              </Title>
              <View style={styles.roleContainer}>
                <Badge style={styles.roleBadge}>
                  {translate('roles.student')}
                </Badge>
              </View>
            </Animatable.View>
          </TouchableOpacity>
        </Animatable.View>
      </LinearGradient>

      <ScrollView style={styles.sidebarMenu}>
        <Animatable.View animation="fadeInUp" delay={400} duration={800}>
          <Surface style={styles.menuSurface}>
            <List.Section>
              <List.Subheader style={styles.menuHeader}>
                {translate('studentDashboard.mainMenu') || 'Main Menu'}
              </List.Subheader>

              <Animatable.View animation="fadeInLeft" delay={500} duration={500}>
                <List.Item
                  title={translate('studentDashboard.dashboard')}
                  left={props => (
                    <View style={[styles.iconContainer, activeSidebarItem === 'StudentDashboard' && styles.activeIconContainer]}>
                      <MaterialCommunityIcons
                        name="view-dashboard"
                        size={22}
                        color={activeSidebarItem === 'StudentDashboard' ? '#1976d2' : '#757575'}
                      />
                    </View>
                  )}
                  onPress={() => handleNavigation('StudentDashboard')}
                  style={[styles.sidebarItem, activeSidebarItem === 'StudentDashboard' && styles.sidebarItemActive]}
                  titleStyle={[
                    styles.sidebarItemText,
                    getTextStyle({ fontSize: 14 }),
                    activeSidebarItem === 'StudentDashboard' && styles.sidebarItemTextActive
                  ]}
                />

                <List.Item
                  title={translate('studentDashboard.courses')}
                  left={props => (
                    <View style={[styles.iconContainer, activeSidebarItem === 'Courses' && styles.activeIconContainer]}>
                      <MaterialCommunityIcons
                        name="book-open-variant"
                        size={22}
                        color={activeSidebarItem === 'Courses' ? '#1976d2' : '#757575'}
                      />
                    </View>
                  )}
                  onPress={() => handleNavigation('Courses')}
                  style={[styles.sidebarItem, activeSidebarItem === 'Courses' && styles.sidebarItemActive]}
                  titleStyle={[
                    styles.sidebarItemText,
                    getTextStyle({ fontSize: 14 }),
                    activeSidebarItem === 'Courses' && styles.sidebarItemTextActive
                  ]}
                />

                <List.Item
                  title={translate('studentDashboard.assignments')}
                  left={props => (
                    <View style={[styles.iconContainer, activeSidebarItem === 'Assignments' && styles.activeIconContainer]}>
                      <MaterialCommunityIcons
                        name="file-document-edit"
                        size={22}
                        color={activeSidebarItem === 'Assignments' ? '#1976d2' : '#757575'}
                      />
                    </View>
                  )}
                  onPress={() => handleNavigation('Assignments')}
                  style={[styles.sidebarItem, activeSidebarItem === 'Assignments' && styles.sidebarItemActive]}
                  titleStyle={[
                    styles.sidebarItemText,
                    getTextStyle({ fontSize: 14 }),
                    activeSidebarItem === 'Assignments' && styles.sidebarItemTextActive
                  ]}
                />

                <List.Item
                  title={translate('studentDashboard.grades')}
                  left={props => (
                    <View style={[styles.iconContainer, activeSidebarItem === 'Grades' && styles.activeIconContainer]}>
                      <MaterialCommunityIcons
                        name="chart-line"
                        size={22}
                        color={activeSidebarItem === 'Grades' ? '#1976d2' : '#757575'}
                      />
                    </View>
                  )}
                  onPress={() => handleNavigation('Grades')}
                  style={[styles.sidebarItem, activeSidebarItem === 'Grades' && styles.sidebarItemActive]}
                  titleStyle={[
                    styles.sidebarItemText,
                    getTextStyle({ fontSize: 14 }),
                    activeSidebarItem === 'Grades' && styles.sidebarItemTextActive
                  ]}
                />

                <List.Item
                  title={translate('studentDashboard.attendance')}
                  left={props => (
                    <View style={[styles.iconContainer, activeSidebarItem === 'StudentAttendance' && styles.activeIconContainer]}>
                      <MaterialCommunityIcons
                        name="calendar-check"
                        size={22}
                        color={activeSidebarItem === 'StudentAttendance' ? '#1976d2' : '#757575'}
                      />
                    </View>
                  )}
                  onPress={() => handleNavigation('StudentAttendance')}
                  style={[styles.sidebarItem, activeSidebarItem === 'StudentAttendance' && styles.sidebarItemActive]}
                  titleStyle={[
                    styles.sidebarItemText,
                    getTextStyle({ fontSize: 14 }),
                    activeSidebarItem === 'StudentAttendance' && styles.sidebarItemTextActive
                  ]}
                />

                <List.Item
                  title={translate('studentDashboard.calendar')}
                  left={props => (
                    <View style={[styles.iconContainer, activeSidebarItem === 'Calendar' && styles.activeIconContainer]}>
                      <MaterialCommunityIcons
                        name="calendar-month"
                        size={22}
                        color={activeSidebarItem === 'Calendar' ? '#1976d2' : '#757575'}
                      />
                    </View>
                  )}
                  onPress={() => handleNavigation('Calendar')}
                  style={[styles.sidebarItem, activeSidebarItem === 'Calendar' && styles.sidebarItemActive]}
                  titleStyle={[
                    styles.sidebarItemText,
                    getTextStyle({ fontSize: 14 }),
                    activeSidebarItem === 'Calendar' && styles.sidebarItemTextActive
                  ]}
                />

                <List.Item
                  title={translate('studentDashboard.messaging')}
                  left={props => (
                    <View style={[styles.iconContainer, activeSidebarItem === 'StudentMessaging' && styles.activeIconContainer]}>
                      <MaterialCommunityIcons
                        name="message-text"
                        size={22}
                        color={activeSidebarItem === 'StudentMessaging' ? '#1976d2' : '#757575'}
                      />
                    </View>
                  )}
                  onPress={() => handleNavigation('StudentMessaging')}
                  style={[styles.sidebarItem, activeSidebarItem === 'StudentMessaging' && styles.sidebarItemActive]}
                  titleStyle={[
                    styles.sidebarItemText,
                    getTextStyle({ fontSize: 14 }),
                    activeSidebarItem === 'StudentMessaging' && styles.sidebarItemTextActive
                  ]}
                />
              </Animatable.View>
            </List.Section>
          </Surface>
        </Animatable.View>

        <Divider style={styles.sidebarDivider} />

        <Animatable.View animation="fadeInUp" delay={600} duration={800}>
          <Surface style={styles.menuSurface}>
            <List.Section>
              <List.Subheader style={styles.menuHeader}>
                {translate('studentDashboard.settings') || 'Settings'}
              </List.Subheader>

              <List.Item
                title={translate('studentDashboard.profile')}
                left={props => (
                  <View style={[styles.iconContainer, activeSidebarItem === 'ProfileManagement' && styles.activeIconContainer]}>
                    <MaterialCommunityIcons
                      name="account"
                      size={22}
                      color={activeSidebarItem === 'ProfileManagement' ? '#1976d2' : '#757575'}
                    />
                  </View>
                )}
                onPress={() => handleNavigation('ProfileManagement')}
                style={[styles.sidebarItem, activeSidebarItem === 'ProfileManagement' && styles.sidebarItemActive]}
                titleStyle={[
                  styles.sidebarItemText,
                  getTextStyle({ fontSize: 14 }),
                  activeSidebarItem === 'ProfileManagement' && styles.sidebarItemTextActive
                ]}
              />

              <List.Item
                title={translate('studentDashboard.logout')}
                left={props => (
                  <View style={[styles.iconContainer, { backgroundColor: 'rgba(244, 67, 54, 0.1)' }]}>
                    <MaterialCommunityIcons
                      name="logout"
                      size={22}
                      color="#F44336"
                    />
                  </View>
                )}
                onPress={() => handleNavigation('logout')}
                style={styles.sidebarItem}
                titleStyle={[
                  styles.sidebarItemText,
                  getTextStyle({ fontSize: 14 }),
                  { color: '#F44336' }
                ]}
              />
            </List.Section>
          </Surface>
        </Animatable.View>

        <View style={styles.sidebarFooter}>
          <Animatable.View animation="fadeIn" delay={800} duration={1000}>
            <Surface style={styles.languageSelectorContainer}>
              <Text style={styles.languageLabel}>
                {translate('common.selectLanguage') || 'Select Language'}
              </Text>
              <LanguageSelector />
            </Surface>

            <View style={styles.versionContainer}>
              <Text style={styles.versionText}>
                {translate('common.version') || 'Version'} 1.0.0
              </Text>
            </View>
          </Animatable.View>
        </View>
      </ScrollView>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  sidebar: {
    position: 'absolute',
    left: 0,
    top: 0,
    bottom: 0,
    width: 300,
    backgroundColor: '#f5f5f5',
    zIndex: 1000,
    elevation: 16,
    borderTopRightRadius: 0,
    borderBottomRightRadius: 30,
    overflow: 'hidden',
  },
  sidebarHeader: {
    padding: 16,
    paddingTop: 40,
    paddingBottom: 24,
  },
  sidebarProfile: {
    alignItems: 'center',
    marginBottom: 8,
  },
  sidebarAvatar: {
    marginBottom: 12,
    borderWidth: 3,
    borderColor: 'rgba(255, 255, 255, 0.5)',
  },
  sidebarName: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  roleContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 8,
  },
  roleBadge: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    color: 'white',
  },
  sidebarMenu: {
    flex: 1,
    paddingBottom: 20,
    paddingHorizontal: 12,
  },
  menuSurface: {
    marginVertical: 8,
    borderRadius: 12,
    overflow: 'hidden',
    elevation: 1,
  },
  menuHeader: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#1976d2',
  },
  sidebarItem: {
    marginVertical: 0,
    borderRadius: 8,
    paddingVertical: 4,
  },
  sidebarItemActive: {
    backgroundColor: 'rgba(33, 150, 243, 0.1)',
  },
  sidebarItemText: {
    fontSize: 14,
    color: '#424242',
  },
  sidebarItemTextActive: {
    color: '#1976d2',
    fontWeight: 'bold',
  },
  iconContainer: {
    width: 36,
    height: 36,
    borderRadius: 8,
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
    marginLeft: 8,
  },
  activeIconContainer: {
    backgroundColor: 'rgba(33, 150, 243, 0.15)',
  },
  sidebarDivider: {
    marginVertical: 12,
  },
  sidebarFooter: {
    padding: 16,
    alignItems: 'center',
  },
  languageSelectorContainer: {
    padding: 12,
    borderRadius: 12,
    width: '100%',
    alignItems: 'center',
  },
  languageLabel: {
    fontSize: 12,
    color: '#757575',
    marginBottom: 8,
  },
  versionContainer: {
    marginTop: 16,
    alignItems: 'center',
  },
  versionText: {
    fontSize: 12,
    color: '#9e9e9e',
  },
});

export default StudentSidebar;
