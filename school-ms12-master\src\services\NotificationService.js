import { db, auth } from '../config/firebase';
import { collection, addDoc, query, where, getDocs, updateDoc, doc, orderBy, limit, Timestamp, writeBatch, getDoc } from 'firebase/firestore';
import axios from 'axios';
import { Platform } from 'react-native';
import * as Device from 'expo-device';
import * as Notifications from 'expo-notifications';
import Constants from 'expo-constants';

// Configure notification behavior
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: true,
  }),
});

class NotificationService {
    static instance = null;

    constructor() {
        if (NotificationService.instance) {
            return NotificationService.instance;
        }
        NotificationService.instance = this;

        // Configure SMS gateway (using Ethiopian telecom service)
        this.smsConfig = {
            apiUrl: process.env.REACT_APP_SMS_API_URL,
            apiKey: process.env.REACT_APP_SMS_API_KEY,
            sender: process.env.REACT_APP_SMS_SENDER_ID
        };

        // Configure email service
        this.emailConfig = {
            apiKey: process.env.REACT_APP_EMAIL_API_KEY,
            sender: process.env.REACT_APP_EMAIL_SENDER,
            templates: {
                gradeReport: 'template_grade_report',
                attendance: 'template_attendance',
                announcement: 'template_announcement',
                reminder: 'template_reminder'
            }
        };

        // Initialize message templates in Amharic and English
        this.initializeTemplates();
    }

    // Initialize message templates
    async initializeTemplates() {
        this.templates = {
            gradeAlert: {
                amharic: 'ውድ {parentName}, የልጅዎ {studentName} በ{subject} ፈተና {grade} አግኝቷል።',
                english: 'Dear {parentName}, your child {studentName} has received a grade of {grade} in {subject}.'
            },
            attendanceAlert: {
                amharic: 'ውድ {parentName}, {studentName} ዛሬ {date} ትምህርት ቤት አልተገኘም።',
                english: 'Dear {parentName}, {studentName} was absent from school today {date}.'
            },
            homeworkReminder: {
                amharic: 'ውድ {studentName}, የ{subject} የቤት ስራ {dueDate} መጨረስ አለበት።',
                english: 'Dear {studentName}, {subject} homework is due on {dueDate}.'
            }
        };
    }

    // Send SMS notification
    async sendSMS(phoneNumber, messageType, data, language = 'amharic') {
        try {
            // Format phone number to Ethiopian format
            const formattedPhone = this.formatEthiopianPhone(phoneNumber);

            // Get message template and replace placeholders
            const message = this.formatMessage(messageType, data, language);

            // Send SMS through Ethiopian telecom API
            const response = await axios.post(this.smsConfig.apiUrl, {
                apiKey: this.smsConfig.apiKey,
                sender: this.smsConfig.sender,
                recipient: formattedPhone,
                message: message
            });

            // Log the notification
            await this.logNotification('sms', {
                recipient: formattedPhone,
                messageType,
                message,
                status: response.status === 200 ? 'delivered' : 'failed'
            });

            return response.status === 200;
        } catch (error) {
            console.error('SMS sending failed:', error);
            throw new Error('Failed to send SMS notification');
        }
    }

    // Send email notification
    async sendEmail(email, subject, messageType, data, language = 'amharic') {
        try {
            // Get message template and replace placeholders
            const message = this.formatMessage(messageType, data, language);

            // Send email through email service
            const response = await axios.post(this.emailConfig.apiUrl, {
                apiKey: this.emailConfig.apiKey,
                from: this.emailConfig.sender,
                to: email,
                subject: subject,
                template: this.emailConfig.templates[messageType],
                templateData: {
                    message,
                    ...data
                }
            });

            // Log the notification
            await this.logNotification('email', {
                recipient: email,
                messageType,
                subject,
                status: response.status === 200 ? 'delivered' : 'failed'
            });

            return response.status === 200;
        } catch (error) {
            console.error('Email sending failed:', error);
            throw new Error('Failed to send email notification');
        }
    }

    // Send bulk notifications
    async sendBulkNotifications(recipients, messageType, data, language = 'amharic') {
        const results = {
            successful: [],
            failed: []
        };

        for (const recipient of recipients) {
            try {
                if (recipient.phoneNumber) {
                    const smsResult = await this.sendSMS(
                        recipient.phoneNumber,
                        messageType,
                        { ...data, ...recipient },
                        language
                    );
                    if (smsResult) results.successful.push({ type: 'sms', recipient: recipient.phoneNumber });
                    else results.failed.push({ type: 'sms', recipient: recipient.phoneNumber });
                }

                if (recipient.email) {
                    const emailResult = await this.sendEmail(
                        recipient.email,
                        data.subject,
                        messageType,
                        { ...data, ...recipient },
                        language
                    );
                    if (emailResult) results.successful.push({ type: 'email', recipient: recipient.email });
                    else results.failed.push({ type: 'email', recipient: recipient.email });
                }
            } catch (error) {
                results.failed.push({
                    type: 'both',
                    recipient: recipient.phoneNumber || recipient.email,
                    error: error.message
                });
            }
        }

        return results;
    }

    // Format message using template
    formatMessage(messageType, data, language) {
        let template = this.templates[messageType][language];

        // Replace all placeholders in template
        Object.keys(data).forEach(key => {
            template = template.replace(`{${key}}`, data[key]);
        });

        return template;
    }

    // Format Ethiopian phone number
    formatEthiopianPhone(phone) {
        // Remove any non-digit characters
        let cleaned = phone.replace(/\D/g, '');

        // Add country code if not present
        if (!cleaned.startsWith('251')) {
            cleaned = cleaned.startsWith('0') ? `251${cleaned.slice(1)}` : `251${cleaned}`;
        }

        return cleaned;
    }

    // Log notification to database
    async logNotification(type, data) {
        await addDoc(collection(db, 'notification_logs'), {
            type,
            ...data,
            timestamp: new Date(),
            status: data.status || 'sent'
        });
    }

    // Send grade notification to parent
    async sendGradeNotification(studentId, subject, grade) {
        try {
            const student = await this.getStudentInfo(studentId);
            const parent = await this.getParentInfo(student.parentId);

            const data = {
                parentName: parent.name,
                studentName: student.name,
                subject,
                grade
            };

            // Send SMS if phone number is available
            if (parent.phoneNumber) {
                await this.sendSMS(parent.phoneNumber, 'gradeAlert', data, parent.preferredLanguage);
            }

            // Send email if email is available
            if (parent.email) {
                await this.sendEmail(
                    parent.email,
                    'Grade Report',
                    'gradeAlert',
                    data,
                    parent.preferredLanguage
                );
            }

            // Send push notification if push token is available
            if (parent.pushToken) {
                // Create notification in the database
                const notificationRef = collection(db, 'notifications');
                await addDoc(notificationRef, {
                    title: 'Grade Report',
                    body: `Your child ${student.name} has received a grade of ${grade} in ${subject}.`,
                    type: 'grade_published',
                    role: 'parent',
                    userId: parent.id,
                    read: false,
                    data: {
                        studentId,
                        studentName: student.name,
                        subject,
                        grade,
                        viewPath: 'ParentChildGrades',
                        viewParams: {
                            childId: studentId,
                            subject
                        }
                    },
                    createdAt: new Date()
                });

                // Send local notification
                await this.sendLocalNotification(
                    'Grade Report',
                    `Your child ${student.name} has received a grade of ${grade} in ${subject}.`,
                    {
                        type: 'grade_published',
                        studentId,
                        subject
                    }
                );
            }

            // Also send notification to the student if they have a push token
            const studentUser = await getDoc(doc(db, 'users', studentId));
            if (studentUser.exists() && studentUser.data().pushToken) {
                // Create notification in the database
                const notificationRef = collection(db, 'notifications');
                await addDoc(notificationRef, {
                    title: 'Grade Report',
                    body: `You have received a grade of ${grade} in ${subject}.`,
                    type: 'grade_published',
                    role: 'student',
                    userId: studentId,
                    read: false,
                    data: {
                        subject,
                        grade,
                        viewPath: 'StudentGrades',
                        viewParams: {
                            subject
                        }
                    },
                    createdAt: new Date()
                });

                // Send local notification
                await this.sendLocalNotification(
                    'Grade Report',
                    `You have received a grade of ${grade} in ${subject}.`,
                    {
                        type: 'grade_published',
                        subject
                    }
                );
            }
        } catch (error) {
            console.error('Error sending grade notification:', error);
        }
    }

    // Send attendance notification
    async sendAttendanceNotification(studentId, date, status) {
        const student = await this.getStudentInfo(studentId);
        const parent = await this.getParentInfo(student.parentId);

        const data = {
            parentName: parent.name,
            studentName: student.name,
            date,
            status
        };

        if (parent.phoneNumber) {
            await this.sendSMS(parent.phoneNumber, 'attendanceAlert', data, parent.preferredLanguage);
        }

        if (parent.email) {
            await this.sendEmail(
                parent.email,
                'Attendance Alert',
                'attendanceAlert',
                data,
                parent.preferredLanguage
            );
        }
    }

    // Get student information
    async getStudentInfo(studentId) {
        const studentDoc = await getDocs(
            query(collection(db, 'students'), where('id', '==', studentId))
        );
        return studentDoc.docs[0].data();
    }

    // Get parent information
    async getParentInfo(parentId) {
        const parentDoc = await getDocs(
            query(collection(db, 'parents'), where('id', '==', parentId))
        );
        return parentDoc.docs[0].data();
    }

    // Send grade approval notification to teacher
    async sendGradeApprovalNotification(teacherId, submissionId, classId, className, sectionName, subject) {
        try {
            // Get teacher information
            const teacherDoc = await getDoc(doc(db, 'users', teacherId));

            if (!teacherDoc.exists()) {
                console.error('Teacher not found:', teacherId);
                return;
            }

            const teacher = teacherDoc.data();

            // Format class and section information
            const classInfo = className ? (sectionName ? `${className}-${sectionName}` : className) : '';

            // Create notification in the database
            const notificationRef = collection(db, 'notifications');
            await addDoc(notificationRef, {
                title: 'Grade Submission Approved',
                body: `Your grade submission for ${classInfo} ${subject} has been approved.`,
                type: 'grade_approved',
                role: 'teacher',
                userId: teacherId,
                read: false,
                data: {
                    submissionId,
                    classId,
                    className,
                    sectionName,
                    subject,
                    viewPath: 'TeacherGradeManagement',
                    viewParams: {
                        classId,
                        subject,
                        sectionName
                    }
                },
                createdAt: new Date()
            });

            // Send local notification if teacher has a push token
            if (teacher.pushToken) {
                await this.sendLocalNotification(
                    'Grade Submission Approved',
                    `Your grade submission for ${classInfo} ${subject} has been approved.`,
                    {
                        type: 'grade_approved',
                        submissionId,
                        classId,
                        subject,
                        sectionName
                    }
                );
            }
        } catch (error) {
            console.error('Error sending grade approval notification:', error);
        }
    }

    // Send grade rejection notification to teacher
    async sendGradeRejectionNotification(teacherId, submissionId, classId, className, sectionName, subject, reason) {
        try {
            // Get teacher information
            const teacherDoc = await getDoc(doc(db, 'users', teacherId));

            if (!teacherDoc.exists()) {
                console.error('Teacher not found:', teacherId);
                return;
            }

            const teacher = teacherDoc.data();

            // Format class and section information
            const classInfo = className ? (sectionName ? `${className}-${sectionName}` : className) : '';

            // Create notification in the database
            const notificationRef = collection(db, 'notifications');
            await addDoc(notificationRef, {
                title: 'Grade Submission Rejected',
                body: `Your grade submission for ${classInfo} ${subject} has been rejected. Reason: ${reason || 'No reason provided'}`,
                type: 'grade_rejected',
                role: 'teacher',
                userId: teacherId,
                read: false,
                data: {
                    submissionId,
                    classId,
                    className,
                    sectionName,
                    subject,
                    reason,
                    viewPath: 'TeacherGradeManagement',
                    viewParams: {
                        classId,
                        subject,
                        sectionName,
                        showRejectionReason: true,
                        rejectionReason: reason || 'No reason provided'
                    }
                },
                createdAt: new Date()
            });

            // Send local notification if teacher has a push token
            if (teacher.pushToken) {
                await this.sendLocalNotification(
                    'Grade Submission Rejected',
                    `Your grade submission for ${classInfo} ${subject} has been rejected. Reason: ${reason || 'No reason provided'}`,
                    {
                        type: 'grade_rejected',
                        submissionId,
                        classId,
                        subject,
                        sectionName,
                        reason
                    }
                );
            }
        } catch (error) {
            console.error('Error sending grade rejection notification:', error);
        }
    }

    // Get notifications for admin dashboard
    async getAdminNotifications() {
        try {
            const notificationsRef = collection(db, 'notifications');
            const q = query(
                notificationsRef,
                where('role', '==', 'admin'),
                orderBy('createdAt', 'desc'),
                limit(20)
            );

            const querySnapshot = await getDocs(q);
            const notifications = [];

            querySnapshot.forEach((doc) => {
                notifications.push({
                    id: doc.id,
                    ...doc.data(),
                    createdAt: doc.data().createdAt?.toDate() || new Date()
                });
            });

            return notifications;
        } catch (error) {
            console.error('Error fetching admin notifications:', error);
            throw error;
        }
    }

    // Send grade published notifications to students and parents
    async sendGradePublishedNotifications(classId, className, sectionName, subject, students, assessments, additionalInfo = {}) {
        try {
            if (!students || students.length === 0) {
                console.error('No students provided for grade notifications');
                return { success: 0, failed: 0 };
            }

            const notificationsRef = collection(db, 'notifications');
            const batch = writeBatch(db);
            let successCount = 0;
            let failedCount = 0;

            // Get assessment details for better notification content
            const assessmentDetails = assessments && assessments.length > 0
                ? `Includes ${assessments.length} assessments: ${assessments.map(a => a.title).join(', ')}`
                : '';

            // Format class and section information
            const classInfo = additionalInfo.classInfo ||
                (className ? (sectionName ? `${className}-${sectionName}` : className) : '');

            // Get assessment types if provided
            const assessmentTypes = additionalInfo.assessmentTypes || '';

            // Create a more detailed message
            const detailedMessage = assessmentTypes
                ? `Assessment types: ${assessmentTypes}. ${assessmentDetails}`
                : assessmentDetails;

            // Current timestamp for all notifications
            const notificationTimestamp = Timestamp.now();

            // For each student in the submission
            for (const student of students) {
                if (!student || !student.uid) continue;

                const studentId = student.uid;
                const studentName = student.name || 'Unknown Student';
                const totalScore = student.totalScore || '0';

                try {
                    // Student notification
                    const studentNotificationRef = doc(collection(db, 'notifications'));
                    batch.set(studentNotificationRef, {
                        title: 'New Grades Available',
                        body: `Your grades for ${classInfo} ${subject} have been published. Your total score is ${totalScore}%. ${detailedMessage}`,
                        type: 'grade_published',
                        role: 'student',
                        userId: studentId,
                        read: false,
                        data: {
                            classId,
                            className,
                            sectionName,
                            subject,
                            totalScore,
                            viewPath: 'StudentGrades',
                            viewParams: {
                                classId,
                                subject,
                                sectionName
                            }
                        },
                        createdAt: notificationTimestamp
                    });

                    // Find parent for this student
                    const usersRef = collection(db, 'users');
                    const q = query(usersRef, where('role', '==', 'parent'), where('children', 'array-contains', studentId));
                    const querySnapshot = await getDocs(q);

                    if (!querySnapshot.empty) {
                        // Send notification to each parent
                        querySnapshot.forEach(parentDoc => {
                            const parentId = parentDoc.id;
                            const parentData = parentDoc.data();
                            const parentName = parentData.displayName || 'Parent';

                            const parentNotificationRef = doc(collection(db, 'notifications'));
                            batch.set(parentNotificationRef, {
                                title: 'New Grades Available',
                                body: `Grades for your child ${studentName} in ${classInfo} ${subject} have been published. Total score: ${totalScore}%. ${detailedMessage}`,
                                type: 'grade_published',
                                role: 'parent',
                                userId: parentId,
                                read: false,
                                data: {
                                    studentId,
                                    studentName,
                                    classId,
                                    className,
                                    sectionName,
                                    subject,
                                    totalScore,
                                    viewPath: 'ParentChildGrades',
                                    viewParams: {
                                        childId: studentId,
                                        classId,
                                        subject,
                                        sectionName
                                    }
                                },
                                createdAt: notificationTimestamp
                            });
                        });
                    }

                    // Send local notification to student if they're the current user
                    try {
                        if (auth.currentUser && auth.currentUser.uid === studentId) {
                            await this.sendLocalNotification(
                                'New Grades Available',
                                `Your grades for ${classInfo} ${subject} have been published. Your total score is ${totalScore}%.`,
                                {
                                    type: 'grade_published',
                                    classId,
                                    subject,
                                    sectionName
                                }
                            );
                        }
                    } catch (err) {
                        console.error('Error sending local notification to student:', err);
                    }

                    successCount++;
                } catch (error) {
                    console.error(`Error creating notification for student ${studentId}:`, error);
                    failedCount++;
                }
            }

            // Commit all notifications in a batch
            await batch.commit();

            return { success: successCount, failed: failedCount };
        } catch (error) {
            console.error('Error sending grade published notifications:', error);
            throw error;
        }
    }

    // Get notifications for a specific user
    async getNotifications(limitCount = 10) {
        try {
            if (!auth.currentUser) {
                console.error('User not authenticated');
                return [];
            }

            const targetUserId = auth.currentUser.uid;

            const notificationsRef = collection(db, 'notifications');

            // First try with createdAt field
            try {
                const q = query(
                    notificationsRef,
                    where('userId', '==', targetUserId),
                    orderBy('createdAt', 'desc'),
                    limit(limitCount)
                );

                const querySnapshot = await getDocs(q);

                return querySnapshot.docs.map(doc => {
                    const data = doc.data();
                    return {
                        id: doc.id,
                        ...data,
                        createdAt: data.createdAt ? new Date(data.createdAt) : new Date()
                    };
                });
            } catch (createdAtError) {
                // Fallback to timestamp field
                const q = query(
                    notificationsRef,
                    where('userId', '==', targetUserId),
                    limit(limitCount)
                );

                const querySnapshot = await getDocs(q);

                // Sort manually by createdAt or timestamp
                const notifications = querySnapshot.docs.map(doc => {
                    const data = doc.data();
                    return {
                        id: doc.id,
                        ...data,
                        createdAt: data.createdAt ? new Date(data.createdAt) :
                                  data.timestamp ? new Date(data.timestamp) : new Date()
                    };
                });

                // Sort by createdAt in descending order
                return notifications.sort((a, b) => b.createdAt - a.createdAt);
            }
        } catch (error) {
            console.error('Error fetching notifications:', error);
            return [];
        }
    }

    // Get unread notification count for the current user
    async getUnreadCount() {
        try {
            if (!auth.currentUser) {
                console.error('User not authenticated');
                return 0;
            }

            const notificationsRef = collection(db, 'notifications');

            // First try with read field
            try {
                const q = query(
                    notificationsRef,
                    where('userId', '==', auth.currentUser.uid),
                    where('read', '==', false)
                );

                const querySnapshot = await getDocs(q);
                return querySnapshot.size;
            } catch (readError) {
                // Fallback to status field
                const q = query(
                    notificationsRef,
                    where('userId', '==', auth.currentUser.uid),
                    where('status', '==', 'unread')
                );

                const querySnapshot = await getDocs(q);
                return querySnapshot.size;
            }
        } catch (error) {
            console.error('Error getting unread count:', error);
            return 0;
        }
    }

    // Mark a notification as read
    async markAsRead(notificationId) {
        try {
            console.log(`Marking notification as read: ${notificationId}`);

            const notificationRef = doc(db, 'notifications', notificationId);

            // Get the current notification data
            const notificationDoc = await getDoc(notificationRef);
            if (!notificationDoc.exists()) {
                console.error(`Notification ${notificationId} does not exist`);
                return false;
            }

            // Update both read and status fields to ensure compatibility
            await updateDoc(notificationRef, {
                read: true,
                status: 'read',
                readAt: new Date().toISOString()
            });

            console.log(`Successfully marked notification ${notificationId} as read`);
            return true;
        } catch (error) {
            console.error('Error marking notification as read:', error);
            return false;
        }
    }

    // Mark all notifications as read for the current user
    async markAllAsRead() {
        try {
            if (!auth.currentUser) {
                console.error('User not authenticated');
                return false;
            }

            // Get all unread notifications for the user
            const notificationsRef = collection(db, 'notifications');
            let unreadNotifications = [];

            // Try with read field first
            try {
                const readQuery = query(
                    notificationsRef,
                    where('userId', '==', auth.currentUser.uid),
                    where('read', '==', false)
                );
                const readSnapshot = await getDocs(readQuery);

                if (!readSnapshot.empty) {
                    unreadNotifications = readSnapshot.docs;
                } else {
                    // Try with status field
                    const statusQuery = query(
                        notificationsRef,
                        where('userId', '==', auth.currentUser.uid),
                        where('status', '==', 'unread')
                    );
                    const statusSnapshot = await getDocs(statusQuery);
                    unreadNotifications = statusSnapshot.docs;
                }
            } catch (error) {
                // If there's an error with the compound query, try getting all user notifications
                const userQuery = query(
                    notificationsRef,
                    where('userId', '==', auth.currentUser.uid)
                );
                const userSnapshot = await getDocs(userQuery);

                // Filter unread notifications manually
                unreadNotifications = userSnapshot.docs.filter(doc => {
                    const data = doc.data();
                    return data.read === false || data.status === 'unread';
                });
            }

            if (unreadNotifications.length === 0) {
                return true;
            }

            // Update all unread notifications
            const batch = writeBatch(db);
            unreadNotifications.forEach(doc => {
                batch.update(doc.ref, {
                    read: true,
                    status: 'read',
                    readAt: new Date().toISOString()
                });
            });

            await batch.commit();
            return true;
        } catch (error) {
            console.error('Error marking all notifications as read:', error);
            return false;
        }
    }

    // Register for push notifications
    async registerForPushNotifications() {
        if (!Device.isDevice) {
            console.log('Push notifications are not available on emulators/simulators');
            return null;
        }

        try {
            // Check if we have permission
            const { status: existingStatus } = await Notifications.getPermissionsAsync();
            let finalStatus = existingStatus;

            // If we don't have permission, ask for it
            if (existingStatus !== 'granted') {
                const { status } = await Notifications.requestPermissionsAsync();
                finalStatus = status;
            }

            // If we still don't have permission, return
            if (finalStatus !== 'granted') {
                console.log('Permission for push notifications was denied');
                return null;
            }

            // Get the token
            const token = await Notifications.getExpoPushTokenAsync({
                projectId: Constants.expoConfig?.extra?.eas?.projectId,
            });

            // Save the token to the user's document
            if (auth.currentUser) {
                const userRef = doc(db, 'users', auth.currentUser.uid);
                const userDoc = await getDoc(userRef);

                if (userDoc.exists()) {
                    await updateDoc(userRef, {
                        pushToken: token.data,
                        deviceType: Platform.OS,
                        lastTokenUpdate: new Date()
                    });
                }
            }

            // Configure for Android
            if (Platform.OS === 'android') {
                Notifications.setNotificationChannelAsync('default', {
                    name: 'default',
                    importance: Notifications.AndroidImportance.MAX,
                    vibrationPattern: [0, 250, 250, 250],
                    lightColor: '#FF231F7C',
                });
            }

            return token.data;
        } catch (error) {
            console.error('Error registering for push notifications:', error);
            return null;
        }
    }

    // Send a local notification immediately
    async sendLocalNotification(title, body, data = {}) {
        try {
            await Notifications.scheduleNotificationAsync({
                content: {
                    title,
                    body,
                    data,
                },
                trigger: null, // Send immediately
            });
        } catch (error) {
            console.error('Error sending local notification:', error);
        }
    }

    // Schedule a local notification for a future time
    async scheduleLocalNotification(title, body, data = {}, secondsFromNow = 0) {
        try {
            const identifier = await Notifications.scheduleNotificationAsync({
                content: {
                    title,
                    body,
                    data,
                },
                trigger: secondsFromNow > 0
                    ? { seconds: secondsFromNow }
                    : null, // Immediately if 0 seconds
            });

            return identifier;
        } catch (error) {
            console.error('Error scheduling local notification:', error);
            return null;
        }
    }

    // Cancel a scheduled notification
    async cancelScheduledNotification(identifier) {
        try {
            await Notifications.cancelScheduledNotificationAsync(identifier);
            return true;
        } catch (error) {
            console.error('Error canceling scheduled notification:', error);
            return false;
        }
    }

    // Get all scheduled notifications
    async getAllScheduledNotifications() {
        try {
            return await Notifications.getAllScheduledNotificationsAsync();
        } catch (error) {
            console.error('Error getting scheduled notifications:', error);
            return [];
        }
    }

    // Add a notification listener
    addNotificationListener(handler) {
        return Notifications.addNotificationReceivedListener(handler);
    }

    // Add a notification response listener
    addNotificationResponseListener(handler) {
        return Notifications.addNotificationResponseReceivedListener(handler);
    }

    // Remove a notification listener
    removeNotificationListener(subscription) {
        if (subscription) {
            Notifications.removeNotificationSubscription(subscription);
        }
    }

    // Get the badge count
    async getBadgeCount() {
        return await Notifications.getBadgeCountAsync();
    }

    // Set the badge count
    async setBadgeCount(count) {
        await Notifications.setBadgeCountAsync(count);
    }

    // Increment the badge count
    async incrementBadgeCount() {
        const currentCount = await Notifications.getBadgeCountAsync();
        await Notifications.setBadgeCountAsync(currentCount + 1);
    }

    // Reset the badge count
    async resetBadgeCount() {
        await Notifications.setBadgeCountAsync(0);
    }

    // Handle exam schedule notification
    async handleExamScheduleNotification(notification) {
        try {
            console.log('Handling exam schedule notification:', notification);

            if (!notification || !notification.data) {
                console.error('Invalid notification data');
                return null;
            }

            const { data } = notification;
            console.log('Notification data:', data);

            // Navigate to the appropriate screen based on user role
            if (data.viewPath) {
                console.log(`Navigating to ${data.viewPath} with params:`, {
                    examId: data.examId,
                    date: data.date
                });

                // Return the navigation action to be executed by the component
                return {
                    screen: data.viewPath,
                    params: {
                        examId: data.examId,
                        date: data.date,
                        ...data
                    }
                };
            } else {
                console.log('No viewPath specified in notification data');

                // Try to determine the appropriate screen based on the notification type
                if (data.type === 'exam_schedule') {
                    // Determine user role and return appropriate screen
                    const userDoc = await getDoc(doc(db, 'users', auth.currentUser.uid));
                    if (userDoc.exists()) {
                        const userData = userDoc.data();
                        const userRole = userData.role;

                        console.log(`User role: ${userRole}`);

                        if (userRole === 'teacher') {
                            return {
                                screen: 'TeacherExamSchedule',
                                params: {
                                    examId: data.examId,
                                    date: data.date,
                                    ...data
                                }
                            };
                        } else if (userRole === 'student') {
                            return {
                                screen: 'StudentExamSchedule',
                                params: {
                                    examId: data.examId,
                                    date: data.date,
                                    ...data
                                }
                            };
                        } else if (userRole === 'parent') {
                            return {
                                screen: 'ParentExamSchedule',
                                params: {
                                    examId: data.examId,
                                    date: data.date,
                                    ...data
                                }
                            };
                        }
                    }
                }
            }
        } catch (error) {
            console.error('Error handling exam schedule notification:', error);
        }
        return null;
    }

    // Handle class schedule notification
    async handleClassScheduleNotification(notification) {
        try {
            console.log('Handling class schedule notification:', notification);

            if (!notification || !notification.data) {
                console.error('Invalid notification data');
                return null;
            }

            const { data } = notification;
            console.log('Notification data:', data);

            // Navigate to the appropriate screen based on user role
            if (data.viewPath) {
                console.log(`Navigating to ${data.viewPath} with params:`, data);

                // Return the navigation action to be executed by the component
                return {
                    screen: data.viewPath,
                    params: data
                };
            } else {
                console.log('No viewPath specified in notification data');

                // Try to determine the appropriate screen based on the notification type
                if (data.type === 'class_schedule') {
                    // Determine user role and return appropriate screen
                    const userDoc = await getDoc(doc(db, 'users', auth.currentUser.uid));
                    if (userDoc.exists()) {
                        const userData = userDoc.data();
                        const userRole = userData.role;

                        console.log(`User role: ${userRole}`);

                        if (userRole === 'teacher') {
                            return {
                                screen: 'TeacherClassSchedule',
                                params: data
                            };
                        } else if (userRole === 'student') {
                            return {
                                screen: 'StudentClassSchedule',
                                params: data
                            };
                        } else if (userRole === 'parent') {
                            return {
                                screen: 'ParentClassSchedule',
                                params: data
                            };
                        }
                    }
                }
            }
        } catch (error) {
            console.error('Error handling class schedule notification:', error);
        }
        return null;
    }

    // Send upcoming class notification
    async sendUpcomingClassNotification(userId, role, periodData) {
        try {
            if (!userId || !role || !periodData) {
                console.error('Missing required parameters for upcoming class notification');
                return false;
            }

            // Get user information
            const userDoc = await getDoc(doc(db, 'users', userId));
            if (!userDoc.exists()) {
                console.error('User not found:', userId);
                return false;
            }

            const userData = userDoc.data();

            // Create notification title and body based on role
            let title, body;

            if (role === 'teacher') {
                title = 'Upcoming Class';
                body = `You have ${periodData.subjectName || periodData.subjectId} class with ${periodData.className || periodData.classId}-${periodData.sectionName} in 5 minutes (Room: ${periodData.roomNumber || 'N/A'})`;
            } else if (role === 'student') {
                title = 'Upcoming Class';
                body = `You have ${periodData.subjectName || periodData.subjectId} class with ${periodData.teacherName || periodData.teacherId} in 5 minutes (Room: ${periodData.roomNumber || 'N/A'})`;
            } else if (role === 'parent') {
                title = 'Child\'s Upcoming Class';
                body = `Your child has ${periodData.subjectName || periodData.subjectId} class with ${periodData.teacherName || periodData.teacherId} in 5 minutes (Room: ${periodData.roomNumber || 'N/A'})`;
            } else {
                console.error('Invalid role for upcoming class notification:', role);
                return false;
            }

            // Create notification in the database
            const notificationRef = collection(db, 'notifications');
            await addDoc(notificationRef, {
                title,
                body,
                type: 'upcoming_class',
                role,
                userId,
                read: false,
                data: {
                    ...periodData,
                    viewPath: role === 'teacher'
                        ? 'TeacherClassSchedule'
                        : role === 'student'
                        ? 'StudentClassSchedule'
                        : 'ParentClassSchedule'
                },
                createdAt: new Date()
            });

            // Send local notification if user has a push token
            if (userData.pushToken) {
                await this.sendLocalNotification(
                    title,
                    body,
                    {
                        type: 'upcoming_class',
                        ...periodData
                    }
                );
            }

            return true;
        } catch (error) {
            console.error('Error sending upcoming class notification:', error);
            return false;
        }
    }
}

export default new NotificationService();
