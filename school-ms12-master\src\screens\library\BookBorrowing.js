import React, { useState, useEffect } from 'react';
import { View, ScrollView, StyleSheet } from 'react-native';
import { Card, Title, FAB, Portal, Modal, DataTable, Searchbar, List } from 'react-native-paper';
import { db } from '../../config/firebase';
import { collection, addDoc, query, getDocs, doc, updateDoc, where, increment } from 'firebase/firestore';
import CustomButton from '../../components/common/CustomButton';
import CustomInput from '../../components/common/CustomInput';

const BookBorrowing = () => {
  const [borrowings, setBorrowings] = useState([]);
  const [books, setBooks] = useState([]);
  const [users, setUsers] = useState([]);
  const [visible, setVisible] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(false);
  
  const [formData, setFormData] = useState({
    userId: '',
    bookId: '',
    borrowDate: new Date(),
    dueDate: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000), // 14 days from now
    status: 'borrowed',
    notes: '',
  });

  useEffect(() => {
    fetchBorrowings();
    fetchBooks();
    fetchUsers();
  }, []);

  const fetchBorrowings = async () => {
    try {
      const borrowingsRef = collection(db, 'bookBorrowings');
      const q = query(borrowingsRef);
      const querySnapshot = await getDocs(q);
      
      const borrowingsData = [];
      querySnapshot.forEach((doc) => {
        borrowingsData.push({ id: doc.id, ...doc.data() });
      });
      
      setBorrowings(borrowingsData);
    } catch (error) {
      console.error('Error fetching borrowings:', error);
    }
  };

  const fetchBooks = async () => {
    try {
      const booksRef = collection(db, 'books');
      const q = query(booksRef, where('availableQuantity', '>', 0));
      const querySnapshot = await getDocs(q);
      
      const booksData = [];
      querySnapshot.forEach((doc) => {
        booksData.push({ id: doc.id, ...doc.data() });
      });
      
      setBooks(booksData);
    } catch (error) {
      console.error('Error fetching books:', error);
    }
  };

  const fetchUsers = async () => {
    try {
      const usersRef = collection(db, 'users');
      const q = query(usersRef);
      const querySnapshot = await getDocs(q);
      
      const usersData = [];
      querySnapshot.forEach((doc) => {
        usersData.push({ id: doc.id, ...doc.data() });
      });
      
      setUsers(usersData);
    } catch (error) {
      console.error('Error fetching users:', error);
    }
  };

  const handleBorrowBook = async () => {
    try {
      setLoading(true);
      
      // Add borrowing record
      const borrowingsRef = collection(db, 'bookBorrowings');
      await addDoc(borrowingsRef, {
        ...formData,
        borrowDate: formData.borrowDate.toISOString(),
        dueDate: formData.dueDate.toISOString(),
        createdAt: new Date().toISOString(),
      });

      // Update book available quantity
      const bookRef = doc(db, 'books', formData.bookId);
      await updateDoc(bookRef, {
        availableQuantity: increment(-1),
      });

      // Add to library activities
      const activitiesRef = collection(db, 'libraryActivities');
      const user = users.find(u => u.id === formData.userId);
      const book = books.find(b => b.id === formData.bookId);
      await addDoc(activitiesRef, {
        type: 'borrow',
        action: `${user.displayName} borrowed ${book.title}`,
        user: user.displayName,
        timestamp: new Date().toISOString(),
      });
      
      setVisible(false);
      resetForm();
      fetchBorrowings();
      fetchBooks();
    } catch (error) {
      console.error('Error borrowing book:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleReturnBook = async (borrowingId, bookId) => {
    try {
      setLoading(true);
      
      // Update borrowing status
      const borrowingRef = doc(db, 'bookBorrowings', borrowingId);
      await updateDoc(borrowingRef, {
        status: 'returned',
        returnDate: new Date().toISOString(),
      });

      // Update book available quantity
      const bookRef = doc(db, 'books', bookId);
      await updateDoc(bookRef, {
        availableQuantity: increment(1),
      });

      // Add to library activities
      const borrowing = borrowings.find(b => b.id === borrowingId);
      const user = users.find(u => u.id === borrowing.userId);
      const book = books.find(b => b.id === bookId);
      const activitiesRef = collection(db, 'libraryActivities');
      await addDoc(activitiesRef, {
        type: 'return',
        action: `${user.displayName} returned ${book.title}`,
        user: user.displayName,
        timestamp: new Date().toISOString(),
      });

      fetchBorrowings();
      fetchBooks();
    } catch (error) {
      console.error('Error returning book:', error);
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setFormData({
      userId: '',
      bookId: '',
      borrowDate: new Date(),
      dueDate: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000),
      status: 'borrowed',
      notes: '',
    });
  };

  const filteredBorrowings = borrowings.filter(borrowing => {
    const user = users.find(u => u.id === borrowing.userId);
    const book = books.find(b => b.id === borrowing.bookId);
    return (
      user?.displayName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      book?.title.toLowerCase().includes(searchQuery.toLowerCase())
    );
  });

  return (
    <View style={styles.container}>
      <Searchbar
        placeholder="Search borrowings..."
        onChangeText={setSearchQuery}
        value={searchQuery}
        style={styles.searchBar}
      />

      <ScrollView>
        <DataTable>
          <DataTable.Header>
            <DataTable.Title>User</DataTable.Title>
            <DataTable.Title>Book</DataTable.Title>
            <DataTable.Title>Due Date</DataTable.Title>
            <DataTable.Title>Status</DataTable.Title>
          </DataTable.Header>

          {filteredBorrowings.map((borrowing) => {
            const user = users.find(u => u.id === borrowing.userId);
            const book = books.find(b => b.id === borrowing.bookId);
            return (
              <DataTable.Row key={borrowing.id}>
                <DataTable.Cell>{user?.displayName}</DataTable.Cell>
                <DataTable.Cell>{book?.title}</DataTable.Cell>
                <DataTable.Cell>
                  {new Date(borrowing.dueDate).toLocaleDateString()}
                </DataTable.Cell>
                <DataTable.Cell>
                  {borrowing.status === 'borrowed' ? (
                    <CustomButton
                      mode="contained"
                      onPress={() => handleReturnBook(borrowing.id, borrowing.bookId)}
                      loading={loading}
                    >
                      Return
                    </CustomButton>
                  ) : (
                    'Returned'
                  )}
                </DataTable.Cell>
              </DataTable.Row>
            );
          })}
        </DataTable>
      </ScrollView>

      <Portal>
        <Modal
          visible={visible}
          onDismiss={() => {
            setVisible(false);
            resetForm();
          }}
          contentContainerStyle={styles.modalContent}
        >
          <ScrollView>
            <Title>Issue Book</Title>

            <List.Section title="Select User">
              {users.map((user) => (
                <List.Item
                  key={user.id}
                  title={user.displayName}
                  description={user.email}
                  left={props => <List.Icon {...props} icon="account" />}
                  onPress={() => setFormData({ ...formData, userId: user.id })}
                  style={formData.userId === user.id ? styles.selectedItem : null}
                />
              ))}
            </List.Section>

            <List.Section title="Select Book">
              {books.map((book) => (
                <List.Item
                  key={book.id}
                  title={book.title}
                  description={`By ${book.author} - Available: ${book.availableQuantity}`}
                  left={props => <List.Icon {...props} icon="book" />}
                  onPress={() => setFormData({ ...formData, bookId: book.id })}
                  style={formData.bookId === book.id ? styles.selectedItem : null}
                />
              ))}
            </List.Section>

            <CustomInput
              label="Notes"
              value={formData.notes}
              onChangeText={(text) => setFormData({ ...formData, notes: text })}
              multiline
              numberOfLines={3}
            />

            <View style={styles.modalButtons}>
              <CustomButton
                mode="contained"
                onPress={handleBorrowBook}
                loading={loading}
                disabled={!formData.userId || !formData.bookId}
              >
                Issue Book
              </CustomButton>
              
              <CustomButton
                mode="outlined"
                onPress={() => {
                  setVisible(false);
                  resetForm();
                }}
              >
                Cancel
              </CustomButton>
            </View>
          </ScrollView>
        </Modal>
      </Portal>

      <FAB
        style={styles.fab}
        icon="plus"
        onPress={() => setVisible(true)}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  searchBar: {
    margin: 10,
    elevation: 2,
  },
  modalContent: {
    backgroundColor: 'white',
    padding: 20,
    margin: 20,
    borderRadius: 10,
    maxHeight: '80%',
  },
  selectedItem: {
    backgroundColor: '#e8f4f8',
  },
  modalButtons: {
    marginTop: 20,
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
  },
});

export default BookBorrowing;
