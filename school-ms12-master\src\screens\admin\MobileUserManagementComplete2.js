  // Filter users based on search query
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredUsers(users);
      return;
    }
    
    const query = searchQuery.toLowerCase().trim();
    const filtered = users.filter(user => {
      // Search in multiple fields
      return (
        (user.firstName?.toLowerCase() || '').includes(query) ||
        (user.lastName?.toLowerCase() || '').includes(query) ||
        (user.email?.toLowerCase() || '').includes(query) ||
        (user.phoneNumber?.toLowerCase() || '').includes(query) ||
        (`${user.firstName} ${user.lastName}`.toLowerCase()).includes(query) ||
        (user.role?.toLowerCase() || '').includes(query)
      );
    });
    
    setFilteredUsers(filtered);
  }, [searchQuery, users]);
  
  // Handle user selection for viewing details
  const handleUserPress = (user) => {
    setSelectedUser(user);
    setShowUserDetailsModal(true);
  };
  
  // Handle status change (activate/deactivate)
  const handleStatusChange = async (userId, newStatus) => {
    try {
      setLoading(true);
      
      // Get user data to determine role
      const userRef = doc(db, 'users', userId);
      const userDoc = await getDoc(userRef);
      
      if (!userDoc.exists()) {
        throw new Error('User not found');
      }
      
      const userData = userDoc.data();
      
      // Update user document
      await updateDoc(userRef, {
        status: newStatus,
        updatedAt: new Date().toISOString()
      });
      
      // Update role-specific document if it exists
      if (userData.role) {
        try {
          const roleRef = doc(db, `${userData.role}s`, userId);
          const roleDoc = await getDoc(roleRef);
          
          if (roleDoc.exists()) {
            await updateDoc(roleRef, {
              status: newStatus,
              updatedAt: new Date().toISOString()
            });
          }
        } catch (roleError) {
          console.error('Error updating role document:', roleError);
          // Continue even if role document update fails
        }
      }
      
      // Update local state
      setUsers(users.map(user =>
        user.id === userId ? {...user, status: newStatus} : user
      ));
      
      // Update selected user if it's the one being modified
      if (selectedUser && selectedUser.id === userId) {
        setSelectedUser({...selectedUser, status: newStatus});
      }
      
      // Show success message
      const successMessage = newStatus === 'active'
        ? (translate('userManagement.userActivated') || 'User activated successfully')
        : (translate('userManagement.userDeactivated') || 'User deactivated successfully');
      
      setErrorMessage(successMessage);
      setSnackbarVisible(true);
      
      // Log the status change
      try {
        await addDoc(collection(db, 'user_status_logs'), {
          userId,
          adminId: auth.currentUser?.uid,
          oldStatus: userData.status,
          newStatus,
          timestamp: new Date().toISOString()
        });
      } catch (logError) {
        console.warn('Failed to log status change:', logError.message);
        // Continue even if logging fails
      }
      
    } catch (error) {
      console.error('Error updating user status:', error);
      setErrorMessage(translate('userManagement.errorUpdating') || 'Error updating user status');
      setSnackbarVisible(true);
    } finally {
      setLoading(false);
    }
  };
  
  // Handle user deletion
  const handleDeleteUser = async (userId) => {
    try {
      setLoading(true);
      
      // Get user data to determine role
      const userRef = doc(db, 'users', userId);
      const userDoc = await getDoc(userRef);
      
      if (!userDoc.exists()) {
        throw new Error('User not found');
      }
      
      const userData = userDoc.data();
      
      // Create a batch to delete all related documents
      const batch = writeBatch(db);
      
      // Delete user document
      batch.delete(userRef);
      
      // Delete role-specific document if it exists
      if (userData.role) {
        try {
          const roleRef = doc(db, `${userData.role}s`, userId);
          const roleDoc = await getDoc(roleRef);
          
          if (roleDoc.exists()) {
            batch.delete(roleRef);
          }
        } catch (roleError) {
          console.error('Error preparing role document for deletion:', roleError);
        }
      }
      
      // Commit the batch to delete Firestore documents
      await batch.commit();
      
      // Log this action for future server-side cleanup of Auth
      await addDoc(collection(db, 'auth_deletion_queue'), {
        userId: userId,
        email: userData.email,
        requestedBy: auth.currentUser?.uid || 'unknown',
        timestamp: new Date().toISOString(),
        processed: false
      });
      
      // Update local state
      setUsers(users.filter(user => user.id !== userId));
      
      // Close modal if the deleted user was selected
      if (selectedUser && selectedUser.id === userId) {
        setShowUserDetailsModal(false);
      }
      
      // Show success message
      const successMessage = translate('userManagement.userDeleted') || 'User deleted successfully';
      setErrorMessage(successMessage);
      setSnackbarVisible(true);
      
    } catch (error) {
      console.error('Error deleting user:', error);
      setErrorMessage(translate('userManagement.errorDeleting') || 'Error deleting user');
      setSnackbarVisible(true);
    } finally {
      setLoading(false);
      setShowDeleteConfirmDialog(false);
    }
  };
  
  // Handle password reset
  const handlePasswordReset = async (email) => {
    try {
      setPasswordResetLoading(true);
      
      if (!email) {
        throw new Error('Email is required');
      }
      
      // Use Firebase's password reset functionality
      await sendPasswordResetEmail(auth, email);
      
      // Close the dialog
      setShowPasswordResetDialog(false);
      
      // Show success message
      setErrorMessage(translate('userManagement.passwordResetSent') || 'Password reset email sent successfully');
      setSnackbarVisible(true);
      
      // Log the password reset request
      try {
        await addDoc(collection(db, 'password_reset_logs'), {
          email,
          adminId: auth.currentUser?.uid,
          timestamp: new Date().toISOString(),
          status: 'requested'
        });
      } catch (logError) {
        console.warn('Failed to log password reset request:', logError.message);
        // Continue even if logging fails
      }
      
    } catch (error) {
      console.error('Error sending password reset:', error);
      
      let errorMessage = translate('userManagement.errorPasswordReset') || 'Failed to send password reset email';
      
      // Provide more specific error messages
      if (error.code === 'auth/user-not-found') {
        errorMessage = translate('auth.errors.userNotFound') || 'User not found with this email';
      } else if (error.code === 'auth/invalid-email') {
        errorMessage = translate('auth.errors.invalidEmail') || 'Invalid email format';
      } else if (error.message) {
        errorMessage += ': ' + error.message;
      }
      
      setErrorMessage(errorMessage);
      setSnackbarVisible(true);
    } finally {
      setPasswordResetLoading(false);
    }
  };
