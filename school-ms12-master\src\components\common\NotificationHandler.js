import React, { useEffect } from 'react';
import { useNavigation } from '@react-navigation/native';
import { db, auth } from '../../config/firebase';
import { collection, query, where, onSnapshot, updateDoc, doc } from 'firebase/firestore';

const NotificationHandler = () => {
  const navigation = useNavigation();

  useEffect(() => {
    if (!auth.currentUser) return;

    const userId = auth.currentUser.uid;
    const userRole = auth.currentUser.displayName?.toLowerCase().includes('parent') ? 'parent' : 
                    auth.currentUser.displayName?.toLowerCase().includes('teacher') ? 'teacher' : 
                    auth.currentUser.displayName?.toLowerCase().includes('admin') ? 'admin' : 'student';

    // Listen for new notifications
    const notificationsRef = collection(db, 'notifications');
    const q = query(
      notificationsRef, 
      where('userId', '==', userId),
      where('read', '==', false)
    );

    const unsubscribe = onSnapshot(q, (snapshot) => {
      snapshot.docChanges().forEach((change) => {
        if (change.type === 'added') {
          const notification = {
            id: change.doc.id,
            ...change.doc.data()
          };
          
          // Handle grade notifications
          if (notification.type === 'grade_published') {
            handleGradeNotification(notification, userRole);
          }
        }
      });
    }, (error) => {
      console.error('Error listening to notifications:', error);
    });

    return () => unsubscribe();
  }, []);

  const handleGradeNotification = async (notification, userRole) => {
    try {
      // Mark notification as read
      const notificationRef = doc(db, 'notifications', notification.id);
      await updateDoc(notificationRef, {
        read: true
      });

      // Navigate based on user role
      if (userRole === 'student') {
        // Navigate to student grades
        if (notification.data?.viewPath === 'StudentGrades') {
          navigation.navigate('StudentGrades', {
            classId: notification.data.classId,
            subject: notification.data.subject
          });
        }
      } else if (userRole === 'parent') {
        // Navigate to parent child grades
        if (notification.data?.viewPath === 'ParentChildGrades') {
          navigation.navigate('ParentChildGrades', {
            childId: notification.data.studentId,
            classId: notification.data.classId,
            subject: notification.data.subject
          });
        }
      }
    } catch (error) {
      console.error('Error handling grade notification:', error);
    }
  };

  return null; // This component doesn't render anything
};

export default NotificationHandler;
