@echo off
echo ===== School Management System - Clear Metro Cache =====

echo Step 1: Stopping any running Metro servers...
taskkill /f /im node.exe >nul 2>&1

echo Step 2: Cleaning temporary files...
if exist node_modules\.cache rmdir /s /q node_modules\.cache
if exist .expo rmdir /s /q .expo
if exist android\app\build\intermediates\incremental rmdir /s /q android\app\build\intermediates\incremental

echo Step 3: Clearing watchman watches...
watchman watch-del-all 2>nul

echo Step 4: Clearing Metro bundler cache...
if exist node_modules\.cache\metro rmdir /s /q node_modules\.cache\metro

echo Step 5: Restarting Metro server with increased buffer size...
set NODE_OPTIONS=--max_old_space_size=4096
start cmd /k "npm start -- --reset-cache"

echo Done! Metro cache has been cleared and server restarted with increased memory.
