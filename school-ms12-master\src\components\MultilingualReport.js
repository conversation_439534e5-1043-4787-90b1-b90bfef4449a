import React from 'react';
import { 
    Paper, 
    Typography, 
    Table, 
    TableBody, 
    TableCell, 
    TableHead, 
    TableRow,
    Box
} from '@mui/material';
import { useTranslation } from '../hooks/useTranslation';
import { styled } from '@mui/material/styles';

const StyledPaper = styled(Paper)(({ theme, direction }) => ({
    padding: theme.spacing(3),
    margin: theme.spacing(2),
    direction: direction,
    '& .MuiTypography-root': {
        marginBottom: theme.spacing(2),
    },
}));

const MultilingualReport = ({ 
    reportType, 
    data, 
    schoolInfo,
    reportDate 
}) => {
    const { 
        t, 
        language, 
        getDirection, 
        formatDate, 
        formatNumber,
        getGradeName,
        getSubjectName 
    } = useTranslation();

    const direction = getDirection();

    const renderHeader = () => (
        <Box textAlign="center" mb={3}>
            <Typography variant="h4" component="h1">
                {schoolInfo.name[language]}
            </Typography>
            <Typography variant="h5" component="h2">
                {t(`reports.headers.${reportType}`)}
            </Typography>
            <Typography variant="body1">
                {formatDate(reportDate)}
            </Typography>
        </Box>
    );

    const renderStudentInfo = () => (
        <Box mb={3}>
            <Typography variant="h6">
                {t('reports.sections.personal_info')}
            </Typography>
            <Table size="small">
                <TableBody>
                    <TableRow>
                        <TableCell>{t('ui.forms.labels.name')}:</TableCell>
                        <TableCell>{data.student.name[language]}</TableCell>
                    </TableRow>
                    <TableRow>
                        <TableCell>{t('grade')}:</TableCell>
                        <TableCell>{getGradeName(data.student.grade)}</TableCell>
                    </TableRow>
                    <TableRow>
                        <TableCell>{t('academic.terms.semester')}:</TableCell>
                        <TableCell>{t(`academic.terms.${data.semester}`)}</TableCell>
                    </TableRow>
                </TableBody>
            </Table>
        </Box>
    );

    const renderAcademicPerformance = () => (
        <Box mb={3}>
            <Typography variant="h6">
                {t('reports.sections.academic_performance')}
            </Typography>
            <Table>
                <TableHead>
                    <TableRow>
                        <TableCell>{t('subject')}</TableCell>
                        <TableCell align="center">{t('academic.terms.midterm')}</TableCell>
                        <TableCell align="center">{t('academic.terms.final')}</TableCell>
                        <TableCell align="center">{t('total')}</TableCell>
                        <TableCell align="center">{t('grade')}</TableCell>
                    </TableRow>
                </TableHead>
                <TableBody>
                    {data.grades.map((grade, index) => (
                        <TableRow key={index}>
                            <TableCell>{getSubjectName(grade.subject)}</TableCell>
                            <TableCell align="center">{formatNumber(grade.midterm)}</TableCell>
                            <TableCell align="center">{formatNumber(grade.final)}</TableCell>
                            <TableCell align="center">{formatNumber(grade.total)}</TableCell>
                            <TableCell align="center">
                                {t(`academic.grades.${grade.grade.toLowerCase()}`)}
                            </TableCell>
                        </TableRow>
                    ))}
                </TableBody>
            </Table>
        </Box>
    );

    const renderAttendance = () => (
        <Box mb={3}>
            <Typography variant="h6">
                {t('reports.sections.attendance_summary')}
            </Typography>
            <Table size="small">
                <TableBody>
                    <TableRow>
                        <TableCell>{t('total_days')}:</TableCell>
                        <TableCell>{formatNumber(data.attendance.totalDays)}</TableCell>
                    </TableRow>
                    <TableRow>
                        <TableCell>{t('present_days')}:</TableCell>
                        <TableCell>{formatNumber(data.attendance.presentDays)}</TableCell>
                    </TableRow>
                    <TableRow>
                        <TableCell>{t('absent_days')}:</TableCell>
                        <TableCell>{formatNumber(data.attendance.absentDays)}</TableCell>
                    </TableRow>
                    <TableRow>
                        <TableCell>{t('attendance_percentage')}:</TableCell>
                        <TableCell>
                            {formatNumber(
                                (data.attendance.presentDays / data.attendance.totalDays) * 100
                            )}%
                        </TableCell>
                    </TableRow>
                </TableBody>
            </Table>
        </Box>
    );

    const renderSignatures = () => (
        <Box mt={4} display="flex" justifyContent="space-between">
            <Box>
                <Typography>{t('class_teacher')}:</Typography>
                <Box mt={4} borderTop={1} width={200} />
            </Box>
            <Box>
                <Typography>{t('principal')}:</Typography>
                <Box mt={4} borderTop={1} width={200} />
            </Box>
        </Box>
    );

    return (
        <StyledPaper direction={direction}>
            {renderHeader()}
            {renderStudentInfo()}
            {renderAcademicPerformance()}
            {renderAttendance()}
            {renderSignatures()}
        </StyledPaper>
    );
};

export default MultilingualReport;
