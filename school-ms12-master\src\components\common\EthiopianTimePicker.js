import React, { useState, useEffect } from 'react';
import { View, StyleSheet, Modal, TouchableOpacity, ScrollView, Platform, Pressable } from 'react-native';
import { Button, Text, Surface, Portal, IconButton, Divider } from 'react-native-paper';
import DateTimePicker from '@react-native-community/datetimepicker';

/**
 * Ethiopian Time Picker Component
 * A time picker component that mimics React Native's DateTimePicker for time selection
 * while working with the Ethiopian calendar system
 */
const EthiopianTimePicker = ({
  value = new Date(),
  onChange,
  label = 'Select Time',
  buttonMode = 'outlined', // outlined, contained - for the button style
  display = Platform.OS === 'ios' ? 'spinner' : 'default', // 'default', 'spinner', 'clock', 'compact'
  is24Hour = false,
  minuteInterval = 5,
  disabled = false,
  themeType = 'light', // 'light' or 'dark'
  language = 'en', // Default to English if no language provided
  showIcon = true,
  iconPosition = 'right',
  allowClear = true,
  theme = {
    primaryColor: '#2196F3',
    textColor: '#333',
    borderColor: '#ccc',
    backgroundColor: '#fff',
    disabledColor: '#e0e0e0',
    disabledTextColor: '#9e9e9e'
  }
}) => {
  const [modalVisible, setModalVisible] = useState(false);
  const [tempTime, setTempTime] = useState(value || new Date());
  const [selectedHour, setSelectedHour] = useState(value ? value.getHours() : new Date().getHours());
  const [selectedMinute, setSelectedMinute] = useState(value ? value.getMinutes() : new Date().getMinutes());
  const [selectedPeriod, setSelectedPeriod] = useState(selectedHour >= 12 ? 'PM' : 'AM');
  const [showNativePicker, setShowNativePicker] = useState(false);
  const [pickerMode, setPickerMode] = useState('time');

  // Update internal state when value prop changes
  useEffect(() => {
    if (value) {
      setTempTime(value);
      setSelectedHour(value.getHours());
      setSelectedMinute(value.getMinutes());
      setSelectedPeriod(value.getHours() >= 12 ? 'PM' : 'AM');
    }
  }, [value]);

  // Format the time for display
  const getFormattedTime = () => {
    if (!value) return '';
    try {
      let hours = value.getHours();
      const minutes = value.getMinutes().toString().padStart(2, '0');

      if (!is24Hour) {
        const period = hours >= 12 ? 'PM' : 'AM';
        hours = hours % 12 || 12; // Convert to 12-hour format
        return `${hours}:${minutes} ${period}`;
      }

      return `${hours.toString().padStart(2, '0')}:${minutes}`;
    } catch (error) {
      console.error('Error formatting time:', error);
      return '';
    }
  };

  // Clear the selected time
  const clearTime = () => {
    if (onChange) {
      onChange(null);
    }
  };

  // Handle native picker change
  const handleNativePickerChange = (event, selectedTime) => {
    setShowNativePicker(Platform.OS === 'ios'); // Keep open on iOS, close on Android

    if (selectedTime) {
      setTempTime(selectedTime);
      setSelectedHour(selectedTime.getHours());
      setSelectedMinute(selectedTime.getMinutes());
      setSelectedPeriod(selectedTime.getHours() >= 12 ? 'PM' : 'AM');

      // On Android, we auto-confirm the selection
      if (Platform.OS === 'android') {
        if (onChange) {
          onChange(selectedTime);
        }
      }
    }
  };

  // Show the appropriate picker based on platform and display mode
  const showPicker = () => {
    if (disabled) return;

    // Use native picker for default, clock, and spinner modes
    if (display === 'default' || display === 'clock' || display === 'spinner') {
      setShowNativePicker(true);
    } else {
      // Use custom modal picker for compact mode or fallback
      setModalVisible(true);
    }
  };

  // Generate hours for the picker
  const generateHours = () => {
    const hours = [];
    const maxHour = is24Hour ? 23 : 12;
    const minHour = is24Hour ? 0 : 1;

    for (let i = minHour; i <= maxHour; i++) {
      hours.push(i);
    }

    return hours;
  };

  // Generate minutes for the picker
  const generateMinutes = () => {
    const minutes = [];
    for (let i = 0; i < 60; i += minuteInterval) {
      minutes.push(i);
    }
    return minutes;
  };

  // Handle hour selection
  const handleHourSelect = (hour) => {
    setSelectedHour(hour);
    updateTempTime(hour, selectedMinute, selectedPeriod);
  };

  // Handle minute selection
  const handleMinuteSelect = (minute) => {
    setSelectedMinute(minute);
    updateTempTime(selectedHour, minute, selectedPeriod);
  };

  // Handle period selection (AM/PM)
  const handlePeriodSelect = (period) => {
    setSelectedPeriod(period);
    updateTempTime(selectedHour, selectedMinute, period);
  };

  // Update the temporary time
  const updateTempTime = (hour, minute, period) => {
    const newTime = new Date(tempTime);

    if (!is24Hour && period === 'PM' && hour < 12) {
      hour += 12;
    } else if (!is24Hour && period === 'AM' && hour === 12) {
      hour = 0;
    }

    newTime.setHours(hour);
    newTime.setMinutes(minute);
    setTempTime(newTime);
  };

  // Confirm time selection
  const confirmSelection = () => {
    if (onChange) {
      onChange(tempTime);
    }
    setModalVisible(false);
  };

  // Cancel time selection
  const cancelSelection = () => {
    // Reset to the original value
    setSelectedHour(value.getHours());
    setSelectedMinute(value.getMinutes());
    setSelectedPeriod(value.getHours() >= 12 ? 'PM' : 'AM');
    setTempTime(value);
    setModalVisible(false);
  };

  // Render hour picker
  const renderHourPicker = () => {
    const hours = generateHours();
    return (
      <View style={styles.pickerColumn}>
        <Text style={styles.pickerLabel}>Hour</Text>
        <ScrollView style={styles.pickerScrollView}>
          {hours.map((hour) => (
            <TouchableOpacity
              key={`hour-${hour}`}
              style={[
                styles.pickerItem,
                selectedHour === hour && { backgroundColor: theme.primaryColor }
              ]}
              onPress={() => handleHourSelect(hour)}
            >
              <Text
                style={[
                  styles.pickerItemText,
                  selectedHour === hour && { color: '#fff' }
                ]}
              >
                {hour.toString().padStart(2, '0')}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>
    );
  };

  // Render minute picker
  const renderMinutePicker = () => {
    const minutes = generateMinutes();
    return (
      <View style={styles.pickerColumn}>
        <Text style={styles.pickerLabel}>Minute</Text>
        <ScrollView style={styles.pickerScrollView}>
          {minutes.map((minute) => (
            <TouchableOpacity
              key={`minute-${minute}`}
              style={[
                styles.pickerItem,
                selectedMinute === minute && { backgroundColor: theme.primaryColor }
              ]}
              onPress={() => handleMinuteSelect(minute)}
            >
              <Text
                style={[
                  styles.pickerItemText,
                  selectedMinute === minute && { color: '#fff' }
                ]}
              >
                {minute.toString().padStart(2, '0')}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>
    );
  };

  // Render period picker (AM/PM)
  const renderPeriodPicker = () => {
    if (is24Hour) return null;

    return (
      <View style={styles.pickerColumn}>
        <Text style={styles.pickerLabel}>Period</Text>
        <View style={styles.periodContainer}>
          <TouchableOpacity
            style={[
              styles.periodButton,
              selectedPeriod === 'AM' && { backgroundColor: theme.primaryColor }
            ]}
            onPress={() => handlePeriodSelect('AM')}
          >
            <Text
              style={[
                styles.periodButtonText,
                selectedPeriod === 'AM' && { color: '#fff' }
              ]}
            >
              AM
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              styles.periodButton,
              selectedPeriod === 'PM' && { backgroundColor: theme.primaryColor }
            ]}
            onPress={() => handlePeriodSelect('PM')}
          >
            <Text
              style={[
                styles.periodButtonText,
                selectedPeriod === 'PM' && { color: '#fff' }
              ]}
            >
              PM
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  // Render quick time buttons
  const renderQuickTimeButtons = () => {
    const quickTimes = [
      { label: 'Now', value: new Date() },
      { label: 'Morning', value: new Date().setHours(9, 0, 0, 0) },
      { label: 'Noon', value: new Date().setHours(12, 0, 0, 0) },
      { label: 'Evening', value: new Date().setHours(18, 0, 0, 0) }
    ];

    return (
      <View style={styles.quickTimeContainer}>
        {quickTimes.map((time, index) => (
          <Button
            key={`quick-${index}`}
            mode="outlined"
            compact
            style={styles.quickTimeButton}
            onPress={() => {
              const newTime = new Date(time.value);
              setSelectedHour(newTime.getHours());
              setSelectedMinute(newTime.getMinutes());
              setSelectedPeriod(newTime.getHours() >= 12 ? 'PM' : 'AM');
              setTempTime(newTime);
            }}
          >
            {time.label}
          </Button>
        ))}
      </View>
    );
  };

  // Render the native picker
  const renderNativePicker = () => {
    if (!showNativePicker) return null;

    return (
      <DateTimePicker
        value={tempTime}
        mode="time"
        is24Hour={is24Hour}
        display={display}
        onChange={handleNativePickerChange}
        minuteInterval={minuteInterval}
        themeVariant={themeType}
      />
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.inputContainer}>
        {iconPosition === 'left' && showIcon && (
          <IconButton
            icon="clock"
            size={20}
            color={disabled ? theme.disabledTextColor : theme.primaryColor}
            onPress={disabled ? null : showPicker}
            style={styles.leftIcon}
          />
        )}

        <Button
          mode={buttonMode}
          onPress={showPicker}
          icon={showIcon && iconPosition !== 'left' ? "clock" : null}
          style={[
            styles.button,
            buttonMode === 'outlined' && { borderColor: theme.borderColor },
            disabled && { backgroundColor: theme.disabledColor, borderColor: theme.disabledColor }
          ]}
          labelStyle={{
            color: disabled ? theme.disabledTextColor : (buttonMode === 'contained' ? '#fff' : theme.textColor),
            textAlign: 'left'
          }}
          color={theme.primaryColor}
          disabled={disabled}
        >
          {getFormattedTime() || label}
        </Button>

        {allowClear && value && !disabled && (
          <IconButton
            icon="close-circle"
            size={16}
            color={theme.textColor}
            onPress={clearTime}
            style={styles.clearButton}
          />
        )}
      </View>

      {/* Native Picker (iOS/Android) */}
      {renderNativePicker()}

      {/* Custom Modal Picker (for 'compact' mode or fallback) */}
      <Portal>
        <Modal
          visible={modalVisible}
          onDismiss={cancelSelection}
          transparent
          animationType="fade"
        >
          <TouchableOpacity
            style={styles.modalOverlay}
            activeOpacity={1}
            onPress={cancelSelection}
          >
            <Surface style={[styles.modalContent, { backgroundColor: themeType === 'dark' ? '#333' : '#fff' }]}>
              <View style={styles.modalHeader}>
                <Text style={[styles.modalTitle, { color: themeType === 'dark' ? '#fff' : '#333' }]}>{label}</Text>
                <IconButton
                  icon="close"
                  size={20}
                  onPress={cancelSelection}
                  color={themeType === 'dark' ? '#fff' : '#333'}
                />
              </View>

              {display === 'compact' ? (
                <View style={styles.compactPickerContainer}>
                  <View style={styles.compactTimeDisplay}>
                    <Text style={[styles.compactTimeText, { color: themeType === 'dark' ? '#fff' : '#333' }]}>
                      {is24Hour
                        ? `${selectedHour.toString().padStart(2, '0')}:${selectedMinute.toString().padStart(2, '0')}`
                        : `${selectedHour % 12 || 12}:${selectedMinute.toString().padStart(2, '0')} ${selectedPeriod}`
                      }
                    </Text>
                  </View>

                  <View style={styles.compactControls}>
                    <Button
                      mode="text"
                      onPress={() => {
                        const now = new Date();
                        setTempTime(now);
                        setSelectedHour(now.getHours());
                        setSelectedMinute(now.getMinutes());
                        setSelectedPeriod(now.getHours() >= 12 ? 'PM' : 'AM');
                      }}
                      color={theme.primaryColor}
                    >
                      Now
                    </Button>

                    <Button
                      mode="contained"
                      onPress={confirmSelection}
                      color={theme.primaryColor}
                    >
                      OK
                    </Button>
                  </View>
                </View>
              ) : (
                <>
                  {renderQuickTimeButtons()}

                  <View style={styles.pickerContainer}>
                    {renderHourPicker()}
                    <Text style={[styles.timeSeparator, { color: themeType === 'dark' ? '#fff' : '#333' }]}>:</Text>
                    {renderMinutePicker()}
                    {renderPeriodPicker()}
                  </View>

                  <View style={[styles.currentTimeContainer, { backgroundColor: themeType === 'dark' ? '#444' : '#f5f5f5' }]}>
                    <Text style={[styles.currentTimeLabel, { color: themeType === 'dark' ? '#ccc' : '#666' }]}>Selected Time:</Text>
                    <Text style={[styles.currentTimeValue, { color: themeType === 'dark' ? '#fff' : '#333' }]}>
                      {is24Hour
                        ? `${selectedHour.toString().padStart(2, '0')}:${selectedMinute.toString().padStart(2, '0')}`
                        : `${selectedHour % 12 || 12}:${selectedMinute.toString().padStart(2, '0')} ${selectedPeriod}`
                      }
                    </Text>
                  </View>

                  <View style={styles.buttonContainer}>
                    <Button
                      mode="outlined"
                      onPress={cancelSelection}
                      style={styles.actionButton}
                      color={themeType === 'dark' ? '#fff' : theme.primaryColor}
                    >
                      Cancel
                    </Button>
                    <Button
                      mode="contained"
                      onPress={confirmSelection}
                      style={styles.actionButton}
                      color={theme.primaryColor}
                    >
                      Confirm
                    </Button>
                  </View>
                </>
              )}
            </Surface>
          </TouchableOpacity>
        </Modal>
      </Portal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 8,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    position: 'relative',
  },
  button: {
    justifyContent: 'flex-start',
    flex: 1,
  },
  leftIcon: {
    margin: 0,
  },
  clearButton: {
    position: 'absolute',
    right: 5,
    margin: 0,
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    width: '90%',
    maxWidth: 400,
    padding: 20,
    borderRadius: 8,
    elevation: 5,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  compactPickerContainer: {
    padding: 10,
  },
  compactTimeDisplay: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 20,
  },
  compactTimeText: {
    fontSize: 36,
    fontWeight: 'bold',
  },
  compactControls: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
  },
  pickerContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'flex-start',
    marginBottom: 20,
  },
  pickerColumn: {
    alignItems: 'center',
    marginHorizontal: 5,
  },
  pickerLabel: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 5,
    color: '#666',
  },
  pickerScrollView: {
    height: 150,
    width: 60,
  },
  pickerItem: {
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 20,
    marginVertical: 2,
  },
  pickerItemText: {
    fontSize: 16,
  },
  timeSeparator: {
    fontSize: 24,
    fontWeight: 'bold',
    marginTop: 30,
  },
  periodContainer: {
    marginTop: 10,
  },
  periodButton: {
    width: 50,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 20,
    marginVertical: 5,
  },
  periodButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  currentTimeContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
    padding: 10,
    backgroundColor: '#f5f5f5',
    borderRadius: 5,
  },
  currentTimeLabel: {
    fontSize: 14,
    marginRight: 10,
    color: '#666',
  },
  currentTimeValue: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  actionButton: {
    marginLeft: 10,
  },
  quickTimeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 15,
    flexWrap: 'wrap',
  },
  quickTimeButton: {
    marginHorizontal: 5,
    marginBottom: 5,
  },
});

export default EthiopianTimePicker;
