  // Render user item for SwipeListView
  const renderUserItem = useCallback(({ item }) => {
    const initials = `${item.firstName?.[0] || ''}${item.lastName?.[0] || ''}`.toUpperCase();
    const fullName = `${item.firstName || ''} ${item.lastName || ''}`;
    
    // Get role-specific color
    const getRoleColor = (role) => {
      switch(role) {
        case 'admin': return mobileTheme.colors.admin;
        case 'teacher': return mobileTheme.colors.teacher;
        case 'student': return mobileTheme.colors.student;
        case 'parent': return mobileTheme.colors.parent;
        default: return mobileTheme.colors.primary;
      }
    };
    
    return (
      <Animatable.View 
        animation="fadeIn" 
        duration={500}
      >
        <Surface style={styles.userCard}>
          <View style={styles.userCardContent}>
            <View style={styles.userInfo}>
              {item.photoURL ? (
                <Avatar.Image 
                  source={{ uri: item.photoURL }} 
                  size={50} 
                  style={styles.avatar}
                />
              ) : (
                <Avatar.Text 
                  label={initials} 
                  size={50} 
                  style={[styles.avatar, { backgroundColor: getRoleColor(item.role) }]}
                />
              )}
              
              <View style={styles.userDetails}>
                <Text style={styles.userName}>{fullName}</Text>
                <Text style={styles.userEmail}>{item.email}</Text>
                
                <View style={styles.userChips}>
                  <Chip 
                    mode="outlined" 
                    style={[styles.roleChip, { borderColor: getRoleColor(item.role) }]}
                    textStyle={{ color: getRoleColor(item.role) }}
                  >
                    {translate(`userManagement.roles.${item.role}`) || item.role}
                  </Chip>
                  
                  <Chip 
                    mode="outlined" 
                    style={[
                      styles.statusChip, 
                      { 
                        borderColor: item.status === 'active' ? mobileTheme.colors.success : mobileTheme.colors.error,
                        backgroundColor: item.status === 'active' ? mobileTheme.colors.success + '20' : mobileTheme.colors.error + '20'
                      }
                    ]}
                    textStyle={{ 
                      color: item.status === 'active' ? mobileTheme.colors.success : mobileTheme.colors.error 
                    }}
                  >
                    {translate(`userManagement.status.${item.status}`) || item.status}
                  </Chip>
                </View>
              </View>
            </View>
          </View>
        </Surface>
      </Animatable.View>
    );
  }, [translate]);
  
  // Render hidden item with swipe actions
  const renderHiddenItem = useCallback(({ item }) => (
    <View style={styles.rowBack}>
      {/* Left swipe actions */}
      <View style={[styles.backLeftBtn, styles.backLeftBtnLeft]}>
        <IconButton
          icon="pencil"
          color="white"
          size={24}
          onPress={() => navigation.navigate('UserEdit', { userId: item.id })}
        />
        <Text style={styles.backTextWhite}>{translate('common.edit')}</Text>
      </View>
      
      {/* Right swipe actions */}
      <View style={[styles.backRightBtn, styles.backRightBtnLeft]}>
        <IconButton
          icon={item.status === 'active' ? 'account-off' : 'account-check'}
          color="white"
          size={24}
          onPress={() => {
            setSelectedUser(item);
            if (item.status === 'active') {
              setShowDeactivateConfirmDialog(true);
            } else {
              setShowActivateConfirmDialog(true);
            }
          }}
        />
        <Text style={styles.backTextWhite}>
          {item.status === 'active' 
            ? translate('userManagement.deactivate') 
            : translate('userManagement.activate')}
        </Text>
      </View>
      
      <View style={[styles.backRightBtn, styles.backRightBtnRight]}>
        <IconButton
          icon="delete"
          color="white"
          size={24}
          onPress={() => {
            setSelectedUser(item);
            setShowDeleteConfirmDialog(true);
          }}
        />
        <Text style={styles.backTextWhite}>{translate('common.delete')}</Text>
      </View>
    </View>
  ), [translate, navigation]);
  
  // Render user list with swipe actions
  const renderUserList = useCallback(() => {
    // Show loading indicator when initially loading
    if (loading && users.length === 0) {
      return (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={mobileTheme.colors.admin} />
          <Text style={styles.loadingText}>
            {translate('userManagement.loadingUsers') || 'Loading users...'}
          </Text>
          <ProgressBar 
            progress={loadingProgress} 
            color={mobileTheme.colors.admin} 
            style={styles.progressBar} 
          />
        </View>
      );
    }
    
    // Show a message when no users are found
    if (users.length === 0 && !loading) {
      return (
        <View style={styles.noResultsContainer}>
          <MaterialCommunityIcons name="account-question" size={48} color="#9e9e9e" />
          <Text style={styles.noResultsText}>
            {translate('userManagement.noUsersFound') || 'No users found'}
          </Text>
          <Button
            mode="contained"
            onPress={() => navigation.navigate('UserRegistration')}
            style={styles.addUserButton}
            icon="account-plus"
          >
            {translate('userManagement.addFirstUser') || 'Add Your First User'}
          </Button>
        </View>
      );
    }

    // Show a message when no users match the search criteria
    if (searchQuery && filteredUsers.length === 0) {
      return (
        <View style={styles.noResultsContainer}>
          <MaterialCommunityIcons name="magnify-close" size={48} color="#9e9e9e" />
          <Text style={styles.noResultsText}>
            {translate('userManagement.noSearchResults') || 'No users match your search criteria'}
          </Text>
          <Button
            mode="outlined"
            onPress={() => setSearchQuery('')}
            style={styles.clearSearchButton}
          >
            {translate('userManagement.clearSearch') || 'Clear Search'}
          </Button>
        </View>
      );
    }
    
    return (
      <SwipeListView
        data={filteredUsers}
        renderItem={renderUserItem}
        renderHiddenItem={renderHiddenItem}
        leftOpenValue={75}
        rightOpenValue={-150}
        previewRowKey={'0'}
        previewOpenValue={-40}
        previewOpenDelay={3000}
        onRowDidOpen={(rowKey) => console.log('Row opened:', rowKey)}
        keyExtractor={item => item.id}
        refreshing={refreshing}
        onRefresh={onRefresh}
        contentContainerStyle={styles.listContent}
        ListHeaderComponent={renderStatsHeader}
      />
    );
  }, [
    loading, 
    users, 
    filteredUsers, 
    searchQuery,
    loadingProgress,
    translate,
    renderUserItem,
    renderHiddenItem,
    refreshing,
    onRefresh
  ]);
  
  // Render stats header
  const renderStatsHeader = useCallback(() => (
    <View style={styles.statsContainer}>
      <Animatable.View 
        animation="fadeInUp" 
        duration={500} 
        delay={100}
        style={styles.statCardContainer}
      >
        <Surface style={styles.statCard}>
          <MaterialCommunityIcons name="account-group" size={24} color={mobileTheme.colors.admin} />
          <Text style={styles.statValue}>{userStats.total}</Text>
          <Text style={styles.statLabel}>{translate('userManagement.totalUsers')}</Text>
        </Surface>
      </Animatable.View>
      
      <Animatable.View 
        animation="fadeInUp" 
        duration={500} 
        delay={200}
        style={styles.statCardContainer}
      >
        <Surface style={styles.statCard}>
          <MaterialCommunityIcons name="account-check" size={24} color={mobileTheme.colors.success} />
          <Text style={styles.statValue}>{userStats.active}</Text>
          <Text style={styles.statLabel}>{translate('userManagement.activeUsers')}</Text>
        </Surface>
      </Animatable.View>
      
      <Animatable.View 
        animation="fadeInUp" 
        duration={500} 
        delay={300}
        style={styles.statCardContainer}
      >
        <Surface style={styles.statCard}>
          <MaterialCommunityIcons name="account-off" size={24} color={mobileTheme.colors.error} />
          <Text style={styles.statValue}>{userStats.inactive}</Text>
          <Text style={styles.statLabel}>{translate('userManagement.inactiveUsers')}</Text>
        </Surface>
      </Animatable.View>
      
      <Animatable.View 
        animation="fadeInUp" 
        duration={500} 
        delay={400}
        style={styles.statCardContainer}
      >
        <Surface style={styles.statCard}>
          <MaterialCommunityIcons name="account-plus" size={24} color={mobileTheme.colors.info} />
          <Text style={styles.statValue}>{userStats.new}</Text>
          <Text style={styles.statLabel}>{translate('userManagement.newUsers')}</Text>
        </Surface>
      </Animatable.View>
    </View>
  ), [userStats, translate]);
