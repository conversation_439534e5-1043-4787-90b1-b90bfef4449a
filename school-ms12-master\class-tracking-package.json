{"name": "class-tracking-system", "version": "1.0.0", "description": "A comprehensive class tracking system for the school management application", "main": "setup-class-tracking.js", "scripts": {"setup": "node setup-class-tracking.js", "deploy-rules": "node deploy-firestore-rules.js", "update-data": "node update-timetable-data.js", "test": "node test-class-tracking.js", "update-timetables": "node src/scripts/updateTimetables.js", "generate-sample": "node src/scripts/generateSampleTimetables.js"}, "keywords": ["class", "tracking", "schedule", "timetable", "school", "management"], "author": "School Management App Team", "license": "MIT", "dependencies": {"firebase": "^9.15.0", "firebase-admin": "^11.5.0", "firebase-tools": "^11.24.0"}, "devDependencies": {"jest": "^29.5.0", "react-test-renderer": "^18.2.0"}, "engines": {"node": ">=14.0.0"}, "private": true, "readme": "README_CLASS_TRACKING_SYSTEM.md", "documentation": {"user": "USER_GUIDE_CLASS_TRACKING.md", "developer": "DEVELOPER_GUIDE_CLASS_TRACKING.md", "features": "README_CLASS_SCHEDULE.md"}}