import React, { useState } from 'react';
import { View, StyleSheet } from 'react-native';
import { Button, Dialog, Portal, Text, Paragraph, ActivityIndicator } from 'react-native-paper';
import { db, auth } from '../../config/firebase';
import { doc, setDoc, collection, query, where, getDocs, serverTimestamp } from 'firebase/firestore';
import { useLanguage } from '../../context/LanguageContext';

const SaveResultsButton = ({ classId, className, sectionName, students, onSuccess }) => {
  const { translate } = useLanguage();
  
  const [saveDialogVisible, setSaveDialogVisible] = useState(false);
  const [processing, setProcessing] = useState(false);
  const [error, setError] = useState(null);

  // Handle save button click
  const handleSaveClick = () => {
    setSaveDialogVisible(true);
  };

  // Save results to the database
  const saveResults = async () => {
    try {
      setProcessing(true);
      setError(null);

      if (!students || students.length === 0) {
        throw new Error('No students found to save results');
      }

      // Create a batch of results to save
      const resultsPromises = students.map(async (student) => {
        // Generate a unique result ID
        const resultId = `${classId}_${student.uid || student.id}_${Date.now()}`;
        
        // Create result document
        const resultData = {
          id: resultId,
          studentId: student.uid || student.id,
          studentName: student.name,
          rollNumber: student.rollNumber,
          className: className,
          sectionName: sectionName,
          classId: classId,
          subjectScores: student.subjectScores || {},
          totalPoints: student.totalPoints || 0,
          totalMaxPoints: student.totalMaxPoints || 0,
          totalPercentage: student.totalPercentage || 0,
          rank: student.rank || 0,
          status: 'pending', // Initial status is pending until approved
          createdBy: auth.currentUser?.uid || '',
          createdByName: auth.currentUser?.displayName || auth.currentUser?.email || 'Unknown Admin',
          createdAt: serverTimestamp(),
          term: 'Current', // You might want to make this configurable
          academicYear: new Date().getFullYear().toString(),
        };
        
        // Save to Firestore
        await setDoc(doc(db, 'results', resultId), resultData);
        
        return resultId;
      });
      
      await Promise.all(resultsPromises);

      // Call success callback
      if (onSuccess) {
        onSuccess();
      }

      setSaveDialogVisible(false);
    } catch (error) {
      console.error('Error saving results:', error);
      setError(error.message);
    } finally {
      setProcessing(false);
    }
  };

  return (
    <View style={styles.container}>
      <Button
        mode="contained"
        icon="content-save"
        onPress={handleSaveClick}
        style={styles.saveButton}
        disabled={processing}
      >
        {translate('admin.results.save') || 'Save Results'}
      </Button>

      <Portal>
        <Dialog
          visible={saveDialogVisible}
          onDismiss={() => !processing && setSaveDialogVisible(false)}
          style={styles.dialog}
        >
          <Dialog.Title>{translate('admin.results.saveResults') || 'Save Results'}</Dialog.Title>
          <Dialog.Content>
            {error ? (
              <Text style={styles.errorText}>{error}</Text>
            ) : (
              <>
                <Paragraph>
                  {translate('admin.results.saveConfirmation') || 
                   'Are you sure you want to save the results for this class?'}
                </Paragraph>
                <Paragraph style={styles.infoText}>
                  {translate('admin.results.saveInfo') || 
                   'This will save the current results to the database. You can approve them later to make them visible to students and parents.'}
                </Paragraph>
              </>
            )}
            
            {processing && (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="small" color="#1976d2" />
                <Text style={styles.loadingText}>
                  {translate('admin.results.processing') || 'Processing...'}
                </Text>
              </View>
            )}
          </Dialog.Content>
          <Dialog.Actions>
            <Button 
              onPress={() => setSaveDialogVisible(false)}
              disabled={processing}
            >
              {translate('common.cancel') || 'Cancel'}
            </Button>
            <Button
              mode="contained"
              onPress={saveResults}
              disabled={processing}
              style={styles.confirmButton}
            >
              {translate('admin.results.confirm') || 'Confirm'}
            </Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 16,
  },
  saveButton: {
    backgroundColor: '#1976d2',
  },
  dialog: {
    borderRadius: 8,
  },
  errorText: {
    color: '#F44336',
    marginBottom: 8,
  },
  infoText: {
    color: '#1976d2',
    marginTop: 8,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 16,
  },
  loadingText: {
    marginLeft: 8,
  },
  confirmButton: {
    backgroundColor: '#1976d2',
  },
});

export default SaveResultsButton;
