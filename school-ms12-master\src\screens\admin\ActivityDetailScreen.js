import React, { useState, useEffect } from 'react';
import { View, ScrollView, StyleSheet } from 'react-native';
import { Card, Title, Paragraph, Text, Avatar, Divider, Button, ActivityIndicator, Surface, IconButton, useTheme } from 'react-native-paper';
import { LinearGradient } from 'expo-linear-gradient';
import * as Animatable from 'react-native-animatable';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useLanguage } from '../../context/LanguageContext';
import { useAuth } from '../../context/AuthContext';
import { db } from '../../config/firebase';
import { doc, getDoc, collection, query, where, getDocs } from 'firebase/firestore';
import { useEthiopianCalendar } from '../../hooks/useEthiopianCalendar';
import ActivityService from '../../services/ActivityService';
import { translateActivity } from '../../utils/TranslationUtils';

const ActivityDetailScreen = ({ route, navigation }) => {
  const { activityId } = route.params;
  const { translate: t, language } = useLanguage();
  const { user } = useAuth();
  const { formatDate } = useEthiopianCalendar();

  const [activity, setActivity] = useState(null);
  const [translatedActivity, setTranslatedActivity] = useState(null);
  const [actorDetails, setActorDetails] = useState(null);
  const [relatedData, setRelatedData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchActivityDetails();
  }, [activityId, language]);

  useEffect(() => {
    if (activity) {
      // Translate activity when activity data changes or language changes
      const translated = translateActivity(activity, t);
      setTranslatedActivity(translated);
    }
  }, [activity, language, t]);

  const fetchActivityDetails = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch activity details
      const activityRef = doc(db, 'activities', activityId);
      const activityDoc = await getDoc(activityRef);

      if (!activityDoc.exists()) {
        throw new Error('Activity not found');
      }

      const activityData = activityDoc.data();
      const activityWithId = {
        id: activityDoc.id,
        ...activityData,
        timestamp: activityData.timestamp?.toDate?.() || new Date(activityData.timestamp)
      };
      
      setActivity(activityWithId);
      
      // Also translate it immediately
      setTranslatedActivity(translateActivity(activityWithId, t));

      // Mark activity as read
      await ActivityService.markActivityAsRead(activityId);

      // Fetch actor details (user who performed the action)
      if (activityData.userId) {
        await fetchActorDetails(activityData.userId);
      }

      // Fetch related data based on activity type
      if (activityData.type && activityData.referenceId) {
        await fetchRelatedData(activityData.type, activityData.referenceId);
      }
    } catch (error) {
      console.error('Error fetching activity details:', error);
      setError(t('activities.error.loading', 'Failed to load activity details. Please try again.'));
    } finally {
      setLoading(false);
    }
  };

  const fetchActorDetails = async (userId) => {
    try {
      const userRef = doc(db, 'users', userId);
      const userDoc = await getDoc(userRef);

      if (userDoc.exists()) {
        setActorDetails({
          id: userDoc.id,
          ...userDoc.data()
        });
      }
    } catch (error) {
      console.error('Error fetching actor details:', error);
    }
  };

  const fetchRelatedData = async (type, referenceId) => {
    try {
      let collectionName;

      switch (type) {
        case 'user':
          collectionName = 'users';
          break;
        case 'class':
          collectionName = 'classes';
          break;
        case 'exam':
          collectionName = 'exams';
          break;
        case 'academic':
          collectionName = 'academicYears';
          break;
        case 'attendance':
          collectionName = 'attendance';
          break;
        case 'grade':
          collectionName = 'grades';
          break;
        case 'announcement':
          collectionName = 'announcements';
          break;
        default:
          return;
      }

      const dataRef = doc(db, collectionName, referenceId);
      const dataDoc = await getDoc(dataRef);

      if (dataDoc.exists()) {
        setRelatedData({
          id: dataDoc.id,
          ...dataDoc.data(),
          type
        });
      }
    } catch (error) {
      console.error('Error fetching related data:', error);
    }
  };

  const getActivityIcon = (type) => {
    switch (type) {
      case 'login':
      case 'logout':
        return 'account-key';
      case 'user':
        return 'account';
      case 'class':
        return 'school';
      case 'exam':
        return 'file-document-edit';
      case 'academic':
        return 'calendar';
      case 'attendance':
        return 'calendar-check';
      case 'grade':
        return 'chart-box';
      case 'announcement':
        return 'bullhorn';
      case 'system':
        return 'cog';
      case 'profile':
        return 'account-edit';
      case 'document':
        return 'file-document';
      default:
        return 'information';
    }
  };

  const getActivityColor = (type) => {
    switch (type) {
      case 'login':
      case 'logout':
        return '#42a5f5'; // light blue
      case 'user':
        return '#2196F3'; // blue
      case 'class':
        return '#4CAF50'; // green
      case 'exam':
        return '#FF9800'; // orange
      case 'academic':
        return '#9C27B0'; // purple
      case 'attendance':
        return '#00BCD4'; // cyan
      case 'grade':
        return '#F44336'; // red
      case 'announcement':
        return '#E91E63'; // pink
      case 'system':
        return '#607D8B'; // blue grey
      case 'profile':
        return '#3F51B5'; // indigo
      case 'document':
        return '#795548'; // brown
      default:
        return '#757575'; // grey
    }
  };

  const navigateToRelatedScreen = () => {
    if (!activity || !activity.type) return;

    switch (activity.type) {
      case 'user':
        navigation.navigate('UserManagement');
        break;
      case 'class':
        navigation.navigate('ClassManagement');
        break;
      case 'exam':
        navigation.navigate('ExamManagement');
        break;
      case 'academic':
        navigation.navigate('AcademicYearManagement');
        break;
      case 'attendance':
        navigation.navigate('AttendanceManagement');
        break;
      case 'grade':
        navigation.navigate('GradeSettings');
        break;
      case 'announcement':
        navigation.navigate('AnnouncementManagement');
        break;
      default:
        // If no specific navigation, go back to main activity list
        navigation.navigate('ActivityManagement');
        break;
    }
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#2196F3" />
        <Text style={styles.loadingText}>{t('common.loading', 'Loading...')}</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <MaterialCommunityIcons name="alert-circle-outline" size={64} color="#F44336" />
        <Text style={styles.errorText}>{error}</Text>
        <Button 
          mode="contained" 
          onPress={fetchActivityDetails} 
          style={styles.retryButton}
        >
          {t('common.retry', 'Retry')}
        </Button>
      </View>
    );
  }

  if (!activity) {
    return (
      <View style={styles.errorContainer}>
        <MaterialCommunityIcons name="file-search-outline" size={64} color="#FF9800" />
        <Text style={styles.errorText}>{t('activities.notFound', 'Activity not found')}</Text>
        <Button 
          mode="contained" 
          onPress={() => navigation.goBack()} 
          style={styles.retryButton}
        >
          {t('common.back', 'Back')}
        </Button>
      </View>
    );
  }

  const activityDate = activity.timestamp ? new Date(activity.timestamp) : new Date();
  const formattedDate = formatDate(activityDate);
  const activityType = translatedActivity?.typeLabel || activity.type;
  const activityTitle = translatedActivity?.title || activity.title;
  const activityDescription = translatedActivity?.description || activity.description;

  return (
    <ScrollView style={styles.container}>
      <Animatable.View 
        animation="fadeIn" 
        duration={500} 
        style={styles.headerContainer}
      >
        <LinearGradient
          colors={['rgba(33, 150, 243, 0.8)', 'rgba(33, 150, 243, 0.6)']}
          style={styles.headerGradient}
        >
          <IconButton
            icon="arrow-left"
            color="#fff"
            size={24}
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          />
          <Title style={styles.headerTitle}>{t('activities.detail', 'Activity Detail')}</Title>
        </LinearGradient>
      </Animatable.View>

      <View style={styles.content}>
        <Animatable.View 
          animation="fadeInUp" 
          duration={600} 
          delay={100}
        >
          <Surface style={styles.activityCard}>
            <View style={styles.activityHeader}>
              <Avatar.Icon 
                icon={getActivityIcon(activity.type)} 
                size={60} 
                style={{ backgroundColor: getActivityColor(activity.type) }} 
              />
              <View style={styles.activityHeaderText}>
                <Title>{activityTitle}</Title>
                <Text style={styles.activityType}>{activityType}</Text>
                <Text style={styles.activityDate}>{formattedDate}</Text>
              </View>
            </View>
            <Divider style={styles.divider} />
            <View style={styles.activityContent}>
              <Paragraph style={styles.activityDescription}>
                {activityDescription}
              </Paragraph>
            </View>
            <Divider style={styles.divider} />
            <View style={styles.activityDetails}>
              <Text style={styles.detailLabel}>{t('activities.detailLabels.performed', 'Performed by')}:</Text>
              <Text style={styles.detailValue}>
                {actorDetails ? (
                  `${actorDetails.firstName || ''} ${actorDetails.lastName || ''} (${actorDetails.role || 'User'})`
                ) : (
                  activity.userId || 'System'
                )}
              </Text>
              
              {activity.metadata && Object.keys(activity.metadata).length > 0 && (
                <>
                  <Text style={[styles.detailLabel, { marginTop: 12 }]}>{t('activities.detailLabels.details', 'Details')}:</Text>
                  {Object.entries(activity.metadata).map(([key, value]) => (
                    <View key={key} style={styles.metadataItem}>
                      <Text style={styles.metadataKey}>{key}:</Text>
                      <Text style={styles.metadataValue}>
                        {typeof value === 'object' ? JSON.stringify(value) : value.toString()}
                      </Text>
                    </View>
                  ))}
                </>
              )}
            </View>
            
            {activity.referenceId && (
              <Button 
                mode="contained" 
                onPress={navigateToRelatedScreen}
                style={styles.viewRelatedButton}
              >
                {t('activities.viewRelated', 'View Related Information')}
              </Button>
            )}
          </Surface>
        </Animatable.View>

        {relatedData && (
          <Animatable.View 
            animation="fadeInUp" 
            duration={600} 
            delay={200}
          >
            <Surface style={[styles.activityCard, { marginTop: 16 }]}>
              <View style={styles.relatedHeader}>
                <Title style={styles.relatedTitle}>
                  {t('activities.relatedInformation', 'Related Information')}
                </Title>
                <Text style={styles.relatedSubtitle}>
                  {t(`activities.entityTypes.${relatedData.type}`, relatedData.type)}
                </Text>
              </View>
              <Divider style={styles.divider} />
              <View style={styles.relatedContent}>
                {Object.entries(relatedData)
                  .filter(([key]) => !['id', 'type'].includes(key))
                  .map(([key, value], index) => (
                    <View key={key} style={styles.relatedItem}>
                      <Text style={styles.relatedKey}>{key}:</Text>
                      <Text style={styles.relatedValue}>
                        {typeof value === 'object' ? JSON.stringify(value) : value.toString()}
                      </Text>
                    </View>
                  ))}
              </View>
            </Surface>
          </Animatable.View>
        )}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5'
  },
  headerContainer: {
    width: '100%',
    marginBottom: 16
  },
  headerGradient: {
    padding: 16,
    paddingTop: 48,
    paddingBottom: 24,
    flexDirection: 'row',
    alignItems: 'center'
  },
  headerTitle: {
    color: '#fff',
    fontSize: 20,
    fontWeight: 'bold',
    marginLeft: 16
  },
  backButton: {
    margin: 0,
    padding: 0
  },
  content: {
    paddingHorizontal: 16,
    paddingBottom: 16
  },
  activityCard: {
    borderRadius: 8,
    padding: 16,
    elevation: 4
  },
  activityHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16
  },
  activityHeaderText: {
    marginLeft: 16,
    flex: 1
  },
  activityType: {
    color: '#757575',
    fontSize: 14,
    marginTop: 4
  },
  activityDate: {
    color: '#757575',
    fontSize: 12,
    marginTop: 2
  },
  divider: {
    marginVertical: 8
  },
  activityContent: {
    paddingVertical: 8
  },
  activityDescription: {
    fontSize: 16,
    lineHeight: 24
  },
  activityDetails: {
    paddingVertical: 8
  },
  detailLabel: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#424242',
    marginBottom: 4
  },
  detailValue: {
    fontSize: 14,
    color: '#616161'
  },
  metadataItem: {
    flexDirection: 'row',
    marginVertical: 2
  },
  metadataKey: {
    fontSize: 14,
    color: '#616161',
    fontWeight: 'bold',
    marginRight: 8
  },
  metadataValue: {
    fontSize: 14,
    color: '#616161',
    flex: 1
  },
  viewRelatedButton: {
    marginTop: 16
  },
  relatedHeader: {
    marginBottom: 8
  },
  relatedTitle: {
    fontSize: 18
  },
  relatedSubtitle: {
    fontSize: 14,
    color: '#757575'
  },
  relatedContent: {
    paddingVertical: 8
  },
  relatedItem: {
    flexDirection: 'row',
    marginVertical: 4
  },
  relatedKey: {
    fontSize: 14,
    color: '#616161',
    fontWeight: 'bold',
    marginRight: 8,
    minWidth: 100
  },
  relatedValue: {
    fontSize: 14,
    color: '#616161',
    flex: 1
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5'
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#757575'
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    padding: 16
  },
  errorText: {
    marginTop: 16,
    fontSize: 16,
    color: '#757575',
    textAlign: 'center',
    marginBottom: 16
  },
  retryButton: {
    marginTop: 8
  }
});

export default ActivityDetailScreen;
