import { db, auth } from '../config/firebase';
import {
  collection,
  addDoc,
  query,
  where,
  getDocs,
  updateDoc,
  doc,
  orderBy,
  serverTimestamp,
  limit,
  deleteDoc,
  getDoc,
  onSnapshot
} from 'firebase/firestore';
// Import Notifications if available, otherwise use a placeholder
let Notifications;
try {
  Notifications = require('expo-notifications');
} catch (error) {
  console.log('Expo Notifications not available');
  // Create a placeholder object
  Notifications = {
    scheduleNotificationAsync: undefined
  };
}

class EnhancedMessageService {
  static chatListeners = new Map();
  static userStatusListeners = new Map();

  // Get users by role
  static async getUsersByRole(role, currentUserRole = 'admin') {
    try {
      const usersRef = collection(db, 'users');
      let q;

      if (role === 'all') {
        q = query(usersRef);
      } else if (Array.isArray(role)) {
        // If role is an array, filter by multiple roles
        q = query(usersRef, where('role', 'in', role));
      } else {
        q = query(usersRef, where('role', '==', role));
      }

      const querySnapshot = await getDocs(q);
      const users = [];

      for (const docSnapshot of querySnapshot.docs) {
        // Don't include the current user in the list
        if (docSnapshot.id === auth.currentUser?.uid) continue;

        const userData = docSnapshot.data();

        // Apply additional filtering based on current user's role
        let includeUser = true;

        // Teachers should only see their assigned students and parents
        if (currentUserRole === 'teacher' &&
            (userData.role === 'student' || userData.role === 'parent')) {
          // Get teacher's assigned classes
          const teacherDoc = await getDoc(doc(db, 'users', auth.currentUser.uid));
          const teacherData = teacherDoc.data();
          const assignedClasses = teacherData.assignedClasses || [];

          // For students, check if they're in teacher's classes
          if (userData.role === 'student') {
            includeUser = assignedClasses.includes(userData.classId);
          }
          // For parents, check if their children are in teacher's classes
          else if (userData.role === 'parent') {
            // Get parent's children
            const childrenIds = userData.children || [];
            let hasChildInClass = false;

            for (const childId of childrenIds) {
              const childDoc = await getDoc(doc(db, 'users', childId));
              if (childDoc.exists()) {
                const childData = childDoc.data();
                if (assignedClasses.includes(childData.classId)) {
                  hasChildInClass = true;
                  break;
                }
              }
            }

            includeUser = hasChildInClass;
          }
        }

        // Students should only see their teachers
        if (currentUserRole === 'student' && userData.role === 'teacher') {
          // Get student's class
          const studentDoc = await getDoc(doc(db, 'users', auth.currentUser.uid));
          const studentData = studentDoc.data();
          const studentClassId = studentData.classId;

          // Check if teacher teaches this class
          const teacherAssignedClasses = userData.assignedClasses || [];
          includeUser = teacherAssignedClasses.includes(studentClassId);
        }

        // Parents should only see their children's teachers
        if (currentUserRole === 'parent' && userData.role === 'teacher') {
          // Get parent's children
          const parentDoc = await getDoc(doc(db, 'users', auth.currentUser.uid));
          const parentData = parentDoc.data();
          const childrenIds = parentData.children || [];
          let hasTeacherForChild = false;

          for (const childId of childrenIds) {
            const childDoc = await getDoc(doc(db, 'users', childId));
            if (childDoc.exists()) {
              const childData = childDoc.data();
              const childClassId = childData.classId;

              // Check if teacher teaches this class
              const teacherAssignedClasses = userData.assignedClasses || [];
              if (teacherAssignedClasses.includes(childClassId)) {
                hasTeacherForChild = true;
                break;
              }
            }
          }

          includeUser = hasTeacherForChild;
        }

        if (includeUser) {
          users.push({ id: docSnapshot.id, ...userData });
        }
      }

      // Sort users by displayName
      return users.sort((a, b) => {
        const nameA = a.displayName || a.email || a.id;
        const nameB = b.displayName || b.email || b.id;
        return nameA.localeCompare(nameB);
      });
    } catch (error) {
      console.error('Error getting users by role:', error);
      throw error;
    }
  }

  // Search users
  static async searchUsers(searchQuery, roleFilter = 'all', currentUserRole = 'admin') {
    try {
      // If search query is empty, return all users (filtered by role if specified)
      if (!searchQuery.trim()) {
        return this.getUsersByRole(roleFilter, currentUserRole);
      }

      const usersRef = collection(db, 'users');
      let q;

      if (roleFilter === 'all') {
        q = query(usersRef);
      } else if (Array.isArray(roleFilter)) {
        // If roleFilter is an array, filter by multiple roles
        q = query(usersRef, where('role', 'in', roleFilter));
      } else {
        q = query(usersRef, where('role', '==', roleFilter));
      }

      const querySnapshot = await getDocs(q);
      const users = [];
      const searchLower = searchQuery.toLowerCase();

      for (const docSnapshot of querySnapshot.docs) {
        // Don't include the current user in the list
        if (docSnapshot.id === auth.currentUser?.uid) continue;

        const userData = docSnapshot.data();
        const displayName = (userData.displayName || '').toLowerCase();
        const email = (userData.email || '').toLowerCase();
        const phone = (userData.phone || '').toLowerCase();
        const fullName = ((userData.firstName || '') + ' ' + (userData.lastName || '')).toLowerCase();

        // Check if user matches search query
        const matchesSearch =
          displayName.includes(searchLower) ||
          email.includes(searchLower) ||
          phone.includes(searchLower) ||
          fullName.includes(searchLower);

        if (!matchesSearch) continue;

        // Apply additional filtering based on current user's role
        let includeUser = true;

        // Teachers should only see their assigned students and parents
        if (currentUserRole === 'teacher' &&
            (userData.role === 'student' || userData.role === 'parent')) {
          // Get teacher's assigned classes
          const teacherDoc = await getDoc(doc(db, 'users', auth.currentUser.uid));
          const teacherData = teacherDoc.data();
          const assignedClasses = teacherData.assignedClasses || [];

          // For students, check if they're in teacher's classes
          if (userData.role === 'student') {
            includeUser = assignedClasses.includes(userData.classId);
          }
          // For parents, check if their children are in teacher's classes
          else if (userData.role === 'parent') {
            // Get parent's children
            const childrenIds = userData.children || [];
            let hasChildInClass = false;

            for (const childId of childrenIds) {
              const childDoc = await getDoc(doc(db, 'users', childId));
              if (childDoc.exists()) {
                const childData = childDoc.data();
                if (assignedClasses.includes(childData.classId)) {
                  hasChildInClass = true;
                  break;
                }
              }
            }

            includeUser = hasChildInClass;
          }
        }

        // Students should only see their teachers
        if (currentUserRole === 'student' && userData.role === 'teacher') {
          // Get student's class
          const studentDoc = await getDoc(doc(db, 'users', auth.currentUser.uid));
          const studentData = studentDoc.data();
          const studentClassId = studentData.classId;

          // Check if teacher teaches this class
          const teacherAssignedClasses = userData.assignedClasses || [];
          includeUser = teacherAssignedClasses.includes(studentClassId);
        }

        // Parents should only see their children's teachers
        if (currentUserRole === 'parent' && userData.role === 'teacher') {
          // Get parent's children
          const parentDoc = await getDoc(doc(db, 'users', auth.currentUser.uid));
          const parentData = parentDoc.data();
          const childrenIds = parentData.children || [];
          let hasTeacherForChild = false;

          for (const childId of childrenIds) {
            const childDoc = await getDoc(doc(db, 'users', childId));
            if (childDoc.exists()) {
              const childData = childDoc.data();
              const childClassId = childData.classId;

              // Check if teacher teaches this class
              const teacherAssignedClasses = userData.assignedClasses || [];
              if (teacherAssignedClasses.includes(childClassId)) {
                hasTeacherForChild = true;
                break;
              }
            }
          }

          includeUser = hasTeacherForChild;
        }

        if (includeUser) {
          users.push({ id: docSnapshot.id, ...userData });
        }
      }

      // Sort users by displayName
      return users.sort((a, b) => {
        const nameA = a.displayName || a.email || a.id;
        const nameB = b.displayName || b.email || b.id;
        return nameA.localeCompare(nameB);
      });
    } catch (error) {
      console.error('Error searching users:', error);
      throw error;
    }
  }

  // Send message
  static async sendMessage(messageData) {
    try {
      const { senderId, recipientId, subject, content, attachments = [] } = messageData;

      // Create conversation if it doesn't exist
      const conversationId = await this.getOrCreateConversation([senderId, recipientId]);

      // Add message
      const messageRef = collection(db, 'messages');
      const message = {
        conversationId,
        senderId,
        content,
        subject: subject || '',
        attachments,
        status: 'sent',
        createdAt: serverTimestamp(),
        readBy: [senderId]
      };

      const newMessageRef = await addDoc(messageRef, message);

      // Update conversation's last message
      const conversationRef = doc(db, 'conversations', conversationId);
      const conversationDoc = await getDoc(conversationRef);
      const conversationData = conversationDoc.data();

      const unreadCount = { ...conversationData.unreadCount };
      unreadCount[recipientId] = (unreadCount[recipientId] || 0) + 1;

      await updateDoc(conversationRef, {
        lastMessageAt: serverTimestamp(),
        lastMessage: content.substring(0, 100),
        lastMessageDate: serverTimestamp(),
        unreadCount
      });

      // Send push notification to recipient
      await this.sendPushNotification(recipientId, senderId, content);

      return { id: newMessageRef.id, ...message };
    } catch (error) {
      console.error('Error sending message:', error);
      throw error;
    }
  }

  // Get or create conversation
  static async getOrCreateConversation(participants) {
    try {
      // Check if conversation exists
      const conversationsRef = collection(db, 'conversations');
      const q = query(
        conversationsRef,
        where('participants', 'array-contains', participants[0])
      );
      const querySnapshot = await getDocs(q);

      let conversation = null;
      querySnapshot.forEach(doc => {
        const data = doc.data();
        if (data.participants.includes(participants[1])) {
          conversation = { id: doc.id, ...data };
        }
      });

      if (conversation) {
        return conversation.id;
      }

      // Create new conversation
      const newConversation = {
        participants,
        createdAt: serverTimestamp(),
        lastMessageAt: serverTimestamp(),
        lastMessageDate: serverTimestamp(),
        lastMessage: '',
        unreadCount: {
          [participants[0]]: 0,
          [participants[1]]: 0
        }
      };

      const docRef = await addDoc(conversationsRef, newConversation);
      return docRef.id;
    } catch (error) {
      console.error('Error getting or creating conversation:', error);
      throw error;
    }
  }

  // Get conversations for a user
  static async getConversations(userId) {
    try {
      const conversationsRef = collection(db, 'conversations');
      const q = query(
        conversationsRef,
        where('participants', 'array-contains', userId),
        orderBy('lastMessageAt', 'desc')
      );

      const querySnapshot = await getDocs(q);
      const conversations = [];

      querySnapshot.forEach(doc => {
        conversations.push({ id: doc.id, ...doc.data() });
      });

      // Enhance conversations with user details
      const enhancedConversations = await Promise.all(
        conversations.map(async (conversation) => {
          const otherParticipantId = conversation.participants.find(p => p !== userId);
          const userDoc = await getDoc(doc(db, 'users', otherParticipantId));

          // Get user display information
          let displayName = 'Unknown User';
          let avatarUrl = null;
          let role = 'unknown';

          if (userDoc.exists()) {
            const userData = userDoc.data();
            // Prefer full name if available, fall back to display name, email, then ID
            displayName =
              (userData.firstName && userData.lastName)
                ? `${userData.firstName} ${userData.lastName}`
                : userData.displayName || userData.email || otherParticipantId;

            avatarUrl = userData.avatarUrl || null;
            role = userData.role || 'unknown';
          }

          return {
            ...conversation,
            otherParticipant: {
              id: otherParticipantId,
              displayName,
              avatarUrl,
              role
            }
          };
        })
      );

      return enhancedConversations;
    } catch (error) {
      console.error('Error getting conversations:', error);
      throw error;
    }
  }

  // Get messages for a conversation
  static async getMessages(conversationId, limitCount = 50) {
    try {
      const messagesRef = collection(db, 'messages');
      const q = query(
        messagesRef,
        where('conversationId', '==', conversationId),
        orderBy('createdAt', 'desc'),
        limit(limitCount)
      );

      const querySnapshot = await getDocs(q);
      const messages = [];

      querySnapshot.forEach(doc => {
        messages.push({ id: doc.id, ...doc.data() });
      });

      return messages.reverse();
    } catch (error) {
      console.error('Error getting messages:', error);
      throw error;
    }
  }

  // Subscribe to messages in a conversation
  static subscribeToMessages(conversationId, callback) {
    try {
      // Unsubscribe from existing listener if any
      this.unsubscribeFromMessages(conversationId);

      const messagesRef = collection(db, 'messages');
      const q = query(
        messagesRef,
        where('conversationId', '==', conversationId),
        orderBy('createdAt', 'desc'),
        limit(50)
      );

      const unsubscribe = onSnapshot(q, (snapshot) => {
        const messages = [];
        snapshot.forEach(doc => {
          messages.push({ id: doc.id, ...doc.data() });
        });

        callback(messages.reverse());
      });

      this.chatListeners.set(conversationId, unsubscribe);
      return unsubscribe;
    } catch (error) {
      console.error('Error subscribing to messages:', error);
      throw error;
    }
  }

  // Unsubscribe from messages
  static unsubscribeFromMessages(conversationId) {
    const unsubscribe = this.chatListeners.get(conversationId);
    if (unsubscribe) {
      unsubscribe();
      this.chatListeners.delete(conversationId);
    }
  }

  // Mark message as read
  static async markAsRead(messageId, userId) {
    try {
      const messageRef = doc(db, 'messages', messageId);
      const messageDoc = await getDoc(messageRef);

      if (!messageDoc.exists()) {
        throw new Error('Message not found');
      }

      const messageData = messageDoc.data();
      const readBy = [...(messageData.readBy || [])];

      if (!readBy.includes(userId)) {
        readBy.push(userId);
        await updateDoc(messageRef, { readBy });

        // Update conversation unread count
        const conversationRef = doc(db, 'conversations', messageData.conversationId);
        const conversationDoc = await getDoc(conversationRef);

        if (conversationDoc.exists()) {
          const conversationData = conversationDoc.data();
          const unreadCount = { ...conversationData.unreadCount };

          if (unreadCount[userId] > 0) {
            unreadCount[userId] -= 1;
            await updateDoc(conversationRef, { unreadCount });
          }
        }
      }

      return true;
    } catch (error) {
      console.error('Error marking message as read:', error);
      throw error;
    }
  }

  // Mark all messages in a conversation as read
  static async markAllAsRead(conversationId, userId) {
    try {
      const messagesRef = collection(db, 'messages');
      const q = query(
        messagesRef,
        where('conversationId', '==', conversationId)
      );

      const querySnapshot = await getDocs(q);
      const batch = [];

      querySnapshot.forEach(doc => {
        const messageData = doc.data();
        if (!messageData.readBy?.includes(userId)) {
          const readBy = [...(messageData.readBy || []), userId];
          batch.push(updateDoc(doc.ref, { readBy }));
        }
      });

      if (batch.length > 0) {
        await Promise.all(batch);
      }

      // Reset unread count for this user in the conversation
      const conversationRef = doc(db, 'conversations', conversationId);
      const conversationDoc = await getDoc(conversationRef);

      if (conversationDoc.exists()) {
        const conversationData = conversationDoc.data();
        const unreadCount = { ...conversationData.unreadCount };
        unreadCount[userId] = 0;
        await updateDoc(conversationRef, { unreadCount });
      }

      return true;
    } catch (error) {
      console.error('Error marking all messages as read:', error);
      throw error;
    }
  }

  // Delete message
  static async deleteMessage(messageId) {
    try {
      await deleteDoc(doc(db, 'messages', messageId));
      return true;
    } catch (error) {
      console.error('Error deleting message:', error);
      throw error;
    }
  }

  // Search messages
  static async searchMessages(userId, searchQuery) {
    try {
      // First get all conversations for this user
      const conversations = await this.getConversations(userId);

      // Then search for messages in these conversations
      const results = [];

      for (const conversation of conversations) {
        const messagesRef = collection(db, 'messages');
        const q = query(
          messagesRef,
          where('conversationId', '==', conversation.id)
        );

        const querySnapshot = await getDocs(q);
        let found = false;

        querySnapshot.forEach(doc => {
          const messageData = doc.data();
          if (
            messageData.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
            (messageData.subject && messageData.subject.toLowerCase().includes(searchQuery.toLowerCase()))
          ) {
            found = true;
          }
        });

        if (found || conversation.lastMessage.toLowerCase().includes(searchQuery.toLowerCase())) {
          results.push(conversation);
        }
      }

      return results;
    } catch (error) {
      console.error('Error searching messages:', error);
      throw error;
    }
  }

  // Update user online status
  static async updateUserStatus(userId, isOnline) {
    try {
      const userRef = doc(db, 'users', userId);
      await updateDoc(userRef, {
        isOnline,
        lastSeen: serverTimestamp()
      });

      return true;
    } catch (error) {
      console.error('Error updating user status:', error);
      // Don't throw the error, just return false to prevent app crashes
      return false;
    }
  }

  // Subscribe to user status
  static subscribeToUserStatus(userId, callback) {
    try {
      // Unsubscribe from existing listener if any
      this.unsubscribeFromUserStatus(userId);

      const userRef = doc(db, 'users', userId);
      const unsubscribe = onSnapshot(userRef, (doc) => {
        if (doc.exists()) {
          const userData = doc.data();
          callback({
            isOnline: userData.isOnline || false,
            lastSeen: userData.lastSeen
          });
        }
      });

      this.userStatusListeners.set(userId, unsubscribe);
      return unsubscribe;
    } catch (error) {
      console.error('Error subscribing to user status:', error);
      throw error;
    }
  }

  // Unsubscribe from user status
  static unsubscribeFromUserStatus(userId) {
    const unsubscribe = this.userStatusListeners.get(userId);
    if (unsubscribe) {
      unsubscribe();
      this.userStatusListeners.delete(userId);
    }
  }

  // Send push notification
  static async sendPushNotification(recipientId, senderId, message) {
    try {
      // Get recipient's push token
      const recipientDoc = await getDoc(doc(db, 'users', recipientId));
      if (!recipientDoc.exists()) return false;

      const recipientData = recipientDoc.data();
      const pushToken = recipientData.expoPushToken;

      if (!pushToken) {
        console.log('Recipient does not have a push token');
        return false;
      }

      // Get sender's name
      const senderDoc = await getDoc(doc(db, 'users', senderId));
      if (!senderDoc.exists()) return false;

      const senderData = senderDoc.data();
      const senderName = senderData.displayName || 'Someone';

      try {
        // For local notifications only - this won't send to other devices
        // This is just a fallback for development
        if (typeof Notifications.scheduleNotificationAsync === 'function') {
          await Notifications.scheduleNotificationAsync({
            content: {
              title: `New message from ${senderName}`,
              body: message.length > 50 ? message.substring(0, 47) + '...' : message,
              data: {
                type: 'message',
                senderId,
                recipientId
              }
            },
            trigger: null
          });
        }

        // In a production app, you would use a push notification service
        // like Firebase Cloud Messaging or Expo's push service
        console.log('Would send push notification to token:', pushToken);

        return true;
      } catch (notificationError) {
        console.log('Error with notification:', notificationError);
        return false;
      }
    } catch (error) {
      console.error('Error sending push notification:', error);
      // Don't throw error here, just log it
      return false;
    }
  }
}

export default EnhancedMessageService;
