import React, { useState } from 'react';
import { View, StyleSheet, Text, Image, ActivityIndicator, Alert, TouchableOpacity } from 'react-native';
import { Button } from 'react-native-paper';
import * as ImagePicker from 'expo-image-picker';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import CloudinaryService from '../../services/CloudinaryService';
import { cloudinaryConfig } from '../../config/cloudinary';
import { useLanguage } from '../../context/LanguageContext';

/**
 * A test component to verify Cloudinary configuration
 */
const CloudinaryTest = () => {
  const [loading, setLoading] = useState(false);
  const [imageUrl, setImageUrl] = useState(null);
  const [uploadResult, setUploadResult] = useState(null);
  const [error, setError] = useState(null);
  const { translate } = useLanguage();

  // Request camera roll permissions
  const requestPermission = async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert(
        'Permission Required',
        'Please grant camera roll permissions to upload images.',
        [{ text: 'OK' }]
      );
      return false;
    }
    return true;
  };

  // Pick an image from the camera roll
  const pickImage = async () => {
    try {
      setError(null);
      const hasPermission = await requestPermission();
      if (!hasPermission) return;

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        const selectedImage = result.assets[0];
        uploadImage(selectedImage.uri);
      }
    } catch (error) {
      console.error('Error picking image:', error);
      setError('Failed to pick image: ' + error.message);
    }
  };

  // Upload the selected image to Cloudinary
  const uploadImage = async (uri) => {
    try {
      setLoading(true);
      setError(null);

      console.log('Uploading to Cloudinary with config:', {
        cloudName: cloudinaryConfig.cloudName,
        apiKey: cloudinaryConfig.apiKey.substring(0, 5) + '...',
      });

      // Create a direct upload using fetch to get more detailed error information
      const formData = new FormData();

      // Prepare the image file
      const filename = uri.split('/').pop();
      const match = /\.(\w+)$/.exec(filename);
      const type = match ? `image/${match[1]}` : 'image/jpeg';

      formData.append('file', {
        uri,
        name: filename,
        type
      });

      // Add upload parameters
      formData.append('upload_preset', 'ml_default'); // Try the default preset
      formData.append('cloud_name', cloudinaryConfig.cloudName);
      formData.append('folder', 'test_uploads');
      formData.append('tags', 'test');

      console.log('Uploading with params:', {
        preset: 'ml_default',
        cloudName: cloudinaryConfig.cloudName,
        folder: 'test_uploads'
      });

      // Make the upload request
      const response = await fetch(
        `https://api.cloudinary.com/v1_1/${cloudinaryConfig.cloudName}/image/upload`,
        {
          method: 'POST',
          body: formData,
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'multipart/form-data'
          }
        }
      );

      // Parse the response
      const responseData = await response.json();

      if (!response.ok) {
        console.error('Upload failed with response:', responseData);
        throw new Error(responseData.error?.message || `Upload failed with status: ${response.status}`);
      }

      console.log('Upload result:', responseData);
      setUploadResult(responseData);
      setImageUrl(responseData.url || responseData.secure_url);
    } catch (error) {
      console.error('Error uploading image:', error);
      setError('Upload failed: ' + error.message);

      // Show a more detailed error alert
      Alert.alert(
        'Upload Failed',
        `Error: ${error.message}\n\nPlease check the console for more details.`,
        [{ text: 'OK' }]
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>{translate('Cloudinary Test')}</Text>

      <View style={styles.configContainer}>
        <Text style={styles.configTitle}>Cloudinary Configuration:</Text>
        <Text style={styles.configText}>
          Cloud Name: <Text style={styles.configValue}>{cloudinaryConfig.cloudName}</Text>
        </Text>
        <Text style={styles.configText}>
          API Key: <Text style={styles.configValue}>{cloudinaryConfig.apiKey.substring(0, 5)}...</Text>
        </Text>
        <Text style={styles.configText}>
          Upload Preset: <Text style={styles.configValue}>ml_default</Text>
        </Text>
        <Text style={styles.configText}>
          Secure: <Text style={styles.configValue}>{cloudinaryConfig.secure ? 'Yes' : 'No'}</Text>
        </Text>
        <Text style={styles.configText}>
          API Base URL: <Text style={styles.configValue}>https://api.cloudinary.com/v1_1/{cloudinaryConfig.cloudName}</Text>
        </Text>
      </View>

      <View style={styles.infoContainer}>
        <Text style={styles.infoText}>
          Note: Make sure you have created an unsigned upload preset named 'ml_default' in your Cloudinary dashboard.
        </Text>
      </View>

      <Button
        mode="contained"
        icon="cloud-upload"
        onPress={pickImage}
        disabled={loading}
        style={styles.uploadButton}
        labelStyle={styles.uploadButtonText}
      >
        Pick and Upload Image
      </Button>

      {loading && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#0000ff" />
          <Text style={styles.loadingText}>Uploading...</Text>
        </View>
      )}

      {error && (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
        </View>
      )}

      {imageUrl && (
        <View style={styles.resultContainer}>
          <View style={styles.successBanner}>
            <MaterialCommunityIcons name="check-circle" size={24} color="white" />
            <Text style={styles.successText}>Upload Successful!</Text>
          </View>

          <Image source={{ uri: imageUrl }} style={styles.image} />

          <View style={styles.resultDetails}>
            <View style={styles.resultRow}>
              <MaterialCommunityIcons name="identifier" size={20} color="#3448C5" />
              <Text style={styles.resultLabel}>Public ID:</Text>
              <Text style={styles.resultValue}>{uploadResult?.publicId}</Text>
            </View>

            <View style={styles.resultRow}>
              <MaterialCommunityIcons name="file-image" size={20} color="#3448C5" />
              <Text style={styles.resultLabel}>Format:</Text>
              <Text style={styles.resultValue}>{uploadResult?.format}</Text>
            </View>

            <View style={styles.resultRow}>
              <MaterialCommunityIcons name="resize" size={20} color="#3448C5" />
              <Text style={styles.resultLabel}>Size:</Text>
              <Text style={styles.resultValue}>{uploadResult?.width}x{uploadResult?.height}</Text>
            </View>

            <View style={styles.resultRow}>
              <MaterialCommunityIcons name="link-variant" size={20} color="#3448C5" />
              <Text style={styles.resultLabel}>URL:</Text>
              <Text style={styles.resultValue} numberOfLines={1} ellipsizeMode="middle">
                {imageUrl}
              </Text>
            </View>
          </View>

          <TouchableOpacity
            style={styles.copyButton}
            onPress={() => {
              Alert.alert('URL Copied', 'Image URL has been copied to clipboard');
            }}
          >
            <MaterialCommunityIcons name="content-copy" size={16} color="white" />
            <Text style={styles.copyButtonText}>Copy URL</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
  },
  configContainer: {
    backgroundColor: '#f5f5f5',
    padding: 16,
    borderRadius: 8,
    marginBottom: 20,
    width: '100%',
  },
  configTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333',
  },
  configText: {
    fontSize: 16,
    marginBottom: 8,
    color: '#555',
  },
  configValue: {
    fontWeight: 'bold',
    color: '#2196F3',
  },
  infoContainer: {
    backgroundColor: '#FFF9C4',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
    borderLeftWidth: 4,
    borderLeftColor: '#FBC02D',
  },
  infoText: {
    fontSize: 14,
    color: '#5D4037',
  },
  loadingContainer: {
    marginTop: 20,
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
  },
  errorContainer: {
    marginTop: 20,
    padding: 10,
    backgroundColor: '#ffeeee',
    borderRadius: 5,
    width: '100%',
  },
  errorText: {
    color: 'red',
  },
  resultContainer: {
    marginTop: 20,
    alignItems: 'center',
    width: '100%',
    borderRadius: 12,
    overflow: 'hidden',
    backgroundColor: 'white',
    elevation: 3,
  },
  successBanner: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#4CAF50',
    width: '100%',
    padding: 12,
  },
  successText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  image: {
    width: 250,
    height: 250,
    marginVertical: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  resultDetails: {
    backgroundColor: '#f8f8f8',
    padding: 16,
    borderRadius: 8,
    width: '100%',
    marginBottom: 16,
  },
  resultRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  resultLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#555',
    marginLeft: 8,
    width: 80,
  },
  resultValue: {
    fontSize: 16,
    color: '#333',
    flex: 1,
  },
  copyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#3448C5',
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 4,
    marginBottom: 16,
  },
  copyButtonText: {
    color: 'white',
    fontWeight: 'bold',
    marginLeft: 8,
  },
  uploadButton: {
    marginVertical: 16,
    paddingVertical: 6,
    backgroundColor: '#3448C5',
  },
  uploadButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default CloudinaryTest;
