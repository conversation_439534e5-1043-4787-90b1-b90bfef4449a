import React, { createContext, useState, useContext, useEffect, useRef } from 'react';
import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';
import Constants from 'expo-constants';
import { Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { auth, db, firebaseApp } from '../config/firebase';
import { ensureFirebaseInitialized } from '../utils/FirebaseInitializer';
import { doc, updateDoc, getDoc, collection, addDoc, query, where, getDocs, orderBy } from 'firebase/firestore';
import { useLanguage } from './LanguageContext';

// Create the notification context
const NotificationContext = createContext();

// Define notification channels
const notificationChannels = {
  academic: {
    name: 'Academic',
    importance: Notifications.AndroidImportance.HIGH,
    vibrationPattern: [0, 250, 250, 250],
    lightColor: '#4CAF50',
  },
  attendance: {
    name: 'Attendance',
    importance: Notifications.AndroidImportance.HIGH,
    vibrationPattern: [0, 250, 250, 250],
    lightColor: '#2196F3',
  },
  behavioral: {
    name: 'Behavioral',
    importance: Notifications.AndroidImportance.HIGH,
    vibrationPattern: [0, 250, 250, 250],
    lightColor: '#FF9800',
  },
  event: {
    name: 'Events',
    importance: Notifications.AndroidImportance.DEFAULT,
    vibrationPattern: [0, 250, 250, 250],
    lightColor: '#9C27B0',
  },
  message: {
    name: 'Messages',
    importance: Notifications.AndroidImportance.HIGH,
    vibrationPattern: [0, 250, 250, 250],
    lightColor: '#2196F3',
  },
  grade: {
    name: 'Grades',
    importance: Notifications.AndroidImportance.HIGH,
    vibrationPattern: [0, 250, 250, 250],
    lightColor: '#E91E63',
  },
  grade_submission: {
    name: 'Grade Submissions',
    importance: Notifications.AndroidImportance.HIGH,
    vibrationPattern: [0, 250, 250, 250],
    lightColor: '#FF5722',
  },
  grade_approval: {
    name: 'Grade Approvals',
    importance: Notifications.AndroidImportance.HIGH,
    vibrationPattern: [0, 250, 250, 250],
    lightColor: '#4CAF50',
  },
  general: {
    name: 'General',
    importance: Notifications.AndroidImportance.DEFAULT,
    vibrationPattern: [0, 250, 250, 250],
    lightColor: '#607D8B',
  },
};

export const NotificationProvider = ({ children }) => {
  const [expoPushToken, setExpoPushToken] = useState('');
  const [notification, setNotification] = useState(null);
  const [notifications, setNotifications] = useState([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [permissionStatus, setPermissionStatus] = useState(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const notificationListener = useRef();
  const responseListener = useRef();
  const { translate, language } = useLanguage();

  // Initialize notifications
  useEffect(() => {
    const initializeNotifications = async () => {
      try {
        // Ensure Firebase is initialized
        const isFirebaseInitialized = await ensureFirebaseInitialized();
        if (!isFirebaseInitialized) {
          console.error('Failed to initialize Firebase in NotificationContext');
          return;
        }

        console.log('Initializing notifications with Firebase app:', firebaseApp.name);

        // Set up notification handler
        Notifications.setNotificationHandler({
          handleNotification: async (notification) => {
            const channel = notification.request.content.data?.channel || 'general';
            return {
              shouldShowAlert: true,
              shouldPlaySound: true,
              shouldSetBadge: true,
              priority: notificationChannels[channel]?.importance || Notifications.AndroidImportance.DEFAULT,
            };
          },
        });

        // Listen for incoming notifications
        notificationListener.current = Notifications.addNotificationReceivedListener(
          handleNotificationReceived
        );

        // Listen for user interaction with notifications
        responseListener.current = Notifications.addNotificationResponseReceivedListener(
          handleNotificationResponse
        );

        // Wait a moment to ensure Firebase is fully initialized
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Register for push notifications
        await registerForPushNotifications();

        setIsInitialized(true);
      } catch (error) {
        console.error('Error initializing notifications:', error);
      }
    };

    initializeNotifications();

    // Clean up listeners on unmount
    return () => {
      if (notificationListener.current) {
        Notifications.removeNotificationSubscription(notificationListener.current);
      }
      if (responseListener.current) {
        Notifications.removeNotificationSubscription(responseListener.current);
      }
    };
  }, []);

  // Fetch user's notifications when auth state changes
  useEffect(() => {
    if (auth.currentUser) {
      fetchUserNotifications();
    }
  }, [auth.currentUser]);

  // Register for push notifications
  const registerForPushNotifications = async () => {
    try {
      // Check if device is physical (not simulator/emulator)
      if (!Device.isDevice) {
        console.log('Must use physical device for Push Notifications');
        setPermissionStatus('unavailable');
        return;
      }

      // Check if previously granted
      const storedPermission = await AsyncStorage.getItem('notificationPermission');
      if (storedPermission === 'denied') {
        console.log('Notifications previously denied by user');
        setPermissionStatus('denied');
        return;
      }

      // Request permission
      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;

      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync({
          ios: {
            allowAlert: true,
            allowBadge: true,
            allowSound: true,
            allowAnnouncements: true,
          },
        });
        finalStatus = status;
        await AsyncStorage.setItem('notificationPermission', status);
      }

      setPermissionStatus(finalStatus);

      if (finalStatus !== 'granted') {
        console.log('Failed to get push token for push notification!');
        return;
      }

      // Ensure Firebase is initialized
      const isFirebaseInitialized = await ensureFirebaseInitialized();
      if (!isFirebaseInitialized) {
        console.error('Failed to initialize Firebase in registerForPushNotifications');
        return;
      }

      // Get or generate push token
      let token;
      try {
        // Add a delay to ensure Firebase is fully initialized
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Check if we're running in development mode
        const isDevelopment = __DEV__;
        if (isDevelopment) {
          // In development, use a mock token to avoid Firebase initialization issues
          console.log('Using mock push token in development mode');
          token = `ExponentPushToken[${Math.random().toString(36).substring(2, 15)}]`;
          setExpoPushToken(token);
        } else {
          // In production, try to get a real token
          try {
            token = (await Notifications.getExpoPushTokenAsync({
              projectId: Constants.expoConfig?.extra?.eas?.projectId || 'schoolmn-16cbc',
            })).data;
            setExpoPushToken(token);
          } catch (tokenError) {
            console.error('Error getting Expo push token:', tokenError);
            // Try alternative approach
            try {
              console.log('Trying alternative approach for push token...');
              token = (await Notifications.getDevicePushTokenAsync()).data;
              setExpoPushToken(token);
            } catch (deviceTokenError) {
              console.error('Alternative approach also failed:', deviceTokenError);
              // Use a fallback token
              token = `MockPushToken[${Math.random().toString(36).substring(2, 15)}]`;
              setExpoPushToken(token);
              console.log('Using fallback mock token:', token);
            }
          }
        }
      } catch (error) {
        console.error('Error in push token generation process:', error);
        // Use a fallback token as last resort
        token = `EmergencyFallbackToken[${Math.random().toString(36).substring(2, 15)}]`;
        setExpoPushToken(token);
        console.log('Using emergency fallback token:', token);
      }

      // Save token to user document if logged in
      if (auth.currentUser) {
        const userRef = doc(db, 'users', auth.currentUser.uid);
        await updateDoc(userRef, {
          expoPushToken: token,
          lastTokenUpdate: new Date().toISOString(),
          devicePlatform: Platform.OS,
          deviceModel: Device.modelName || 'Unknown',
        });
      }

      // Set up notification channels for Android
      if (Platform.OS === 'android') {
        await Promise.all(
          Object.entries(notificationChannels).map(
            ([id, channel]) =>
              Notifications.setNotificationChannelAsync(id, {
                name: channel.name,
                importance: channel.importance,
                vibrationPattern: channel.vibrationPattern,
                lightColor: channel.lightColor,
                enableVibrate: true,
                enableLights: true,
              })
          )
        );
      }

      setIsInitialized(true);
    } catch (error) {
      console.error('Error initializing notifications:', error);
    }
  };

  // Handle received notification
  const handleNotificationReceived = (notification) => {
    setNotification(notification);

    // Update notifications list and unread count
    const notificationData = notification.request.content.data;
    if (notificationData?.notificationId) {
      fetchUserNotifications();
    }
  };

  // Handle notification response (when user taps on notification)
  const handleNotificationResponse = async (response) => {
    const data = response.notification.request.content.data;

    // Mark notification as read
    if (data?.notificationId) {
      await markAsRead(data.notificationId);
    }

    // Handle navigation or other actions based on notification data
    // This will be handled by the navigation service or screen components
  };

  // Fetch user's notifications
  const fetchUserNotifications = async () => {
    try {
      if (!auth.currentUser) {
        console.warn('No authenticated user found when fetching notifications');
        return [];
      }

      const notificationsRef = collection(db, 'notifications');
      const q = query(
        notificationsRef,
        where('userId', '==', auth.currentUser.uid),
        orderBy('timestamp', 'desc')
      );

      const querySnapshot = await getDocs(q);
      const notificationsData = [];

      querySnapshot.forEach((doc) => {
        notificationsData.push({
          id: doc.id,
          ...doc.data(),
        });
      });

      setNotifications(notificationsData);
      const unreadNotifications = notificationsData.filter(n => !n.read);
      setUnreadCount(unreadNotifications.length);

      return notificationsData;
    } catch (error) {
      console.error('Error fetching notifications:', error);
      return [];
    }
  };

  // Mark notification as read
  const markAsRead = async (notificationId) => {
    try {
      if (!notificationId) return false;

      const notificationRef = doc(db, 'notifications', notificationId);
      const notificationDoc = await getDoc(notificationRef);

      if (!notificationDoc.exists()) {
        console.warn(`Notification ${notificationId} does not exist`);
        return false;
      }

      console.log(`Marking notification ${notificationId} as read`);

      await updateDoc(notificationRef, {
        read: true,
        readAt: new Date().toISOString(),
      });

      // Update local state
      setNotifications(prev =>
        prev.map(n =>
          n.id === notificationId
            ? { ...n, read: true, readAt: new Date().toISOString() }
            : n
        )
      );

      setUnreadCount(prev => Math.max(0, prev - 1));
      console.log(`Notification ${notificationId} marked as read, unread count: ${unreadCount - 1}`);

      return true;
    } catch (error) {
      console.error('Error marking notification as read:', error);
      return false;
    }
  };

  // Mark all notifications as read
  const markAllAsRead = async () => {
    try {
      if (!auth.currentUser) return false;

      // Get unread notifications from state
      const unreadNotifications = notifications.filter(n => !n.read);

      if (unreadNotifications.length === 0) return true;

      console.log(`Marking ${unreadNotifications.length} notifications as read`);

      // Update each unread notification
      await Promise.all(
        unreadNotifications.map(async (notification) => {
          try {
            const notificationRef = doc(db, 'notifications', notification.id);
            await updateDoc(notificationRef, {
              read: true,
              readAt: new Date().toISOString(),
            });
            console.log(`Marked notification ${notification.id} as read`);
          } catch (updateError) {
            console.error(`Error updating notification ${notification.id}:`, updateError);
          }
        })
      );

      // Update local state
      setNotifications(prev =>
        prev.map(n => ({
          ...n,
          read: true,
          readAt: !n.read ? new Date().toISOString() : n.readAt,
        }))
      );

      setUnreadCount(0);
      console.log('All notifications marked as read');

      return true;
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      return false;
    }
  };

  // Send a notification to a specific user
  const sendNotification = async (userId, notification, retryCount = 3) => {
    try {
      // Get user's push token
      const userRef = doc(db, 'users', userId);
      const userDoc = await getDoc(userRef);

      if (!userDoc.exists()) {
        console.log('User not found:', userId);
        return false;
      }

      const userData = userDoc.data();
      const pushToken = userData?.expoPushToken;

      if (!pushToken) {
        console.log('No push token found for user:', userId);
        return false;
      }

      // Check if it's a mock token
      const isMockToken = pushToken.includes('MockPushToken') ||
                         pushToken.includes('EmergencyFallbackToken') ||
                         (__DEV__ && pushToken.includes('ExponentPushToken'));

      if (isMockToken) {
        console.log('Using mock token for notification, skipping actual push delivery');
        // We'll still save the notification to the database, but won't try to send it via Expo
      }

      // Save notification to database
      const notificationData = {
        userId,
        title: notification.title,
        body: notification.body,
        data: notification.data || {},
        type: notification.type || 'general',
        priority: notification.priority || 'normal',
        status: 'unread',
        timestamp: new Date().toISOString(),
        channel: notification.type || 'general',
        retryCount: 0,
        devicePlatform: userData.devicePlatform,
      };

      const notificationRef = await addDoc(
        collection(db, 'notifications'),
        notificationData
      );

      // Send push notification with retry mechanism
      let success = false;

      // If it's a mock token, mark as successful without actually sending
      if (isMockToken) {
        success = true;
        await updateDoc(notificationRef, {
          status: 'sent',
          sentAt: new Date().toISOString(),
          isMockDelivery: true,
        });
      } else {
        // Real token - attempt to send the notification
        for (let attempt = 0; attempt < retryCount && !success; attempt++) {
          try {
            // Use Expo's push notification service
            const message = {
              to: pushToken,
              sound: 'default',
              title: notification.title,
              body: notification.body,
              data: {
                ...notification.data,
                notificationId: notificationRef.id,
                channel: notification.type || 'general',
              },
              badge: 1,
            };

            // Send the notification
            const response = await fetch('https://exp.host/--/api/v2/push/send', {
              method: 'POST',
              headers: {
                Accept: 'application/json',
                'Accept-encoding': 'gzip, deflate',
                'Content-Type': 'application/json',
              },
              body: JSON.stringify(message),
            });

            const responseData = await response.json();

            if (responseData.data && responseData.data.status === 'ok') {
              success = true;

              // Update notification status
              await updateDoc(notificationRef, {
                status: 'sent',
                sentAt: new Date().toISOString(),
              });
            } else {
              // Update retry count
              await updateDoc(notificationRef, {
                retryCount: attempt + 1,
                lastRetryAt: new Date().toISOString(),
                lastError: JSON.stringify(responseData),
              });

              // Wait before retrying
              await new Promise(resolve => setTimeout(resolve, 1000));
            }
          } catch (error) {
            console.error(`Error sending notification (attempt ${attempt + 1}):`, error);

            // Update retry count
            await updateDoc(notificationRef, {
              retryCount: attempt + 1,
              lastRetryAt: new Date().toISOString(),
              lastError: error.message,
            });

            // Wait before retrying
            await new Promise(resolve => setTimeout(resolve, 1000));
          }
        }
      }

      if (!success) {
        // Mark as failed after all retries
        await updateDoc(notificationRef, {
          status: 'failed',
          failedAt: new Date().toISOString(),
        });

        return false;
      }

      return true;
    } catch (error) {
      console.error('Error sending notification:', error);
      return false;
    }
  };

  // Send a local notification (doesn't require internet)
  const sendLocalNotification = async (notification) => {
    try {
      // Check if we're in development mode
      if (__DEV__) {
        console.log('Simulating local notification in development mode:', notification.title);
        // In development, just log the notification
        console.log('Local notification content:', {
          title: notification.title,
          body: notification.body,
          data: notification.data || {},
        });
        return true;
      }

      // In production, try to schedule the notification
      try {
        await Notifications.scheduleNotificationAsync({
          content: {
            title: notification.title,
            body: notification.body,
            data: notification.data || {},
            sound: true,
            badge: 1,
            priority: Notifications.AndroidNotificationPriority.HIGH,
          },
          trigger: null, // null means send immediately
        });
        return true;
      } catch (scheduleError) {
        console.error('Error scheduling notification:', scheduleError);

        // Try an alternative approach
        try {
          // Use a simpler notification format
          await Notifications.presentNotificationAsync({
            title: notification.title,
            body: notification.body,
            android: {
              channelId: 'default',
              priority: 'high',
            },
          });
          return true;
        } catch (presentError) {
          console.error('Alternative notification approach also failed:', presentError);
          return false;
        }
      }
    } catch (error) {
      console.error('Error sending local notification:', error);
      return false;
    }
  };

  // Send notification to multiple users
  const sendBulkNotifications = async (userIds, notification) => {
    try {
      // Get users' push tokens
      const usersRef = collection(db, 'users');
      const q = query(usersRef, where('__name__', 'in', userIds));
      const usersSnapshot = await getDocs(q);

      if (usersSnapshot.empty) {
        console.log('No users found with the provided IDs');
        return false;
      }

      const pushTokens = [];
      const mockTokens = [];
      const userTokenMap = {};
      const userMockTokenMap = {};

      usersSnapshot.forEach(doc => {
        if (doc.data().expoPushToken) {
          const token = doc.data().expoPushToken;
          const isMockToken = token.includes('MockPushToken') ||
                             token.includes('EmergencyFallbackToken') ||
                             (__DEV__ && token.includes('ExponentPushToken'));

          if (isMockToken) {
            mockTokens.push(token);
            userMockTokenMap[doc.id] = token;
          } else {
            pushTokens.push(token);
            userTokenMap[doc.id] = token;
          }
        }
      });

      if (pushTokens.length === 0 && mockTokens.length === 0) {
        console.log('No push tokens found for the provided users');
        return false;
      }

      if (mockTokens.length > 0) {
        console.log(`Found ${mockTokens.length} mock tokens, will simulate delivery`);
      }

      // Save notifications to database
      const batch = [];

      for (const userId of userIds) {
        // Handle both real and mock tokens
        const hasRealToken = userTokenMap[userId] !== undefined;
        const hasMockToken = userMockTokenMap[userId] !== undefined;

        if (!hasRealToken && !hasMockToken) continue;

        const notificationData = {
          userId,
          title: notification.title,
          body: notification.body,
          data: notification.data || {},
          type: notification.type || 'general',
          priority: notification.priority || 'normal',
          status: 'unread',
          timestamp: new Date().toISOString(),
          channel: notification.type || 'general',
          isMockDelivery: hasMockToken && !hasRealToken,
        };

        batch.push(addDoc(collection(db, 'notifications'), notificationData));
      }

      const results = await Promise.all(batch);

      // Only send push notifications if we have real tokens
      if (pushTokens.length > 0) {
        // Send push notifications
        const messages = pushTokens.map(token => ({
          to: token,
          sound: 'default',
          title: notification.title,
          body: notification.body,
          data: {
            ...notification.data,
            channel: notification.type || 'general',
          },
          badge: 1,
        }));

        // Send in chunks of 100 (Expo limit)
        const chunkSize = 100;
        for (let i = 0; i < messages.length; i += chunkSize) {
          const chunk = messages.slice(i, i + chunkSize);

          try {
            await fetch('https://exp.host/--/api/v2/push/send', {
              method: 'POST',
              headers: {
                Accept: 'application/json',
                'Accept-encoding': 'gzip, deflate',
                'Content-Type': 'application/json',
              },
              body: JSON.stringify(chunk),
            });
          } catch (error) {
            console.error('Error sending bulk notifications chunk:', error);
            // Continue with next chunk even if this one fails
          }
        }
      } else {
        console.log('No real tokens to send, only mock tokens available');
      }

      return true;
    } catch (error) {
      console.error('Error sending bulk notifications:', error);
      return false;
    }
  };

  // Request permission again if previously denied
  const requestPermissionAgain = async () => {
    try {
      // Ensure Firebase is initialized
      const isFirebaseInitialized = await ensureFirebaseInitialized();
      if (!isFirebaseInitialized) {
        console.error('Failed to initialize Firebase in requestPermissionAgain');
        return false;
      }

      const { status } = await Notifications.requestPermissionsAsync({
        ios: {
          allowAlert: true,
          allowBadge: true,
          allowSound: true,
          allowAnnouncements: true,
        },
      });

      setPermissionStatus(status);
      await AsyncStorage.setItem('notificationPermission', status);

      if (status === 'granted') {
        // Wait a moment to ensure Firebase is fully initialized
        await new Promise(resolve => setTimeout(resolve, 1000));
        await registerForPushNotifications();
        return true;
      }

      return false;
    } catch (error) {
      console.error('Error requesting notification permission:', error);
      return false;
    }
  };

  // Get badge count
  const getBadgeCount = async () => {
    try {
      return await Notifications.getBadgeCountAsync();
    } catch (error) {
      console.error('Error getting badge count:', error);
      return 0;
    }
  };

  // Set badge count
  const setBadgeCount = async (count) => {
    try {
      await Notifications.setBadgeCountAsync(count);
      return true;
    } catch (error) {
      console.error('Error setting badge count:', error);
      return false;
    }
  };

  // Reset badge count
  const resetBadgeCount = async () => {
    try {
      await Notifications.setBadgeCountAsync(0);
      return true;
    } catch (error) {
      console.error('Error resetting badge count:', error);
      return false;
    }
  };

  // Show a toast notification
  const showToast = async (title, message, type = 'info', navigateTo = null, navigationParams = {}) => {
    // Create a local notification that will trigger the toast
    const notificationContent = {
      title,
      body: message,
      data: {
        type,
        screen: navigateTo,
        params: navigationParams,
        isToast: true
      }
    };

    // Send a local notification
    await sendLocalNotification(notificationContent);

    // Also save to Firestore if user is logged in
    if (user) {
      try {
        await addDoc(collection(db, 'notifications'), {
          userId: user.uid,
          title,
          body: message,
          type,
          status: 'unread',
          timestamp: new Date().toISOString(),
          data: {
            screen: navigateTo,
            params: navigationParams,
            isToast: true
          }
        });
      } catch (error) {
        console.error('Error saving toast notification to Firestore:', error);
      }
    }
  };

  // Context value
  const value = {
    expoPushToken,
    notification,
    notifications,
    unreadCount,
    permissionStatus,
    isInitialized,
    fetchUserNotifications,
    markAsRead,
    markAllAsRead,
    sendNotification,
    sendLocalNotification,
    sendBulkNotifications,
    requestPermissionAgain,
    getBadgeCount,
    setBadgeCount,
    resetBadgeCount,
    showToast,
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
    </NotificationContext.Provider>
  );
};

// Custom hook to use the notification context
export const useNotifications = () => {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
};
