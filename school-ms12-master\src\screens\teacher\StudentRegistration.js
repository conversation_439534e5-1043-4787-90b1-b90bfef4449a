import React, { useState, useEffect } from 'react';
import { View, ScrollView, StyleSheet, Text } from 'react-native';
import { Card, Title, FAB, Portal, Modal, DataTable, Searchbar, List, Chip, HelperText, Menu, Button, Dialog, ActivityIndicator } from 'react-native-paper';
import { db, auth } from '../../config/firebase';
import { collection, addDoc, query, getDocs, doc, updateDoc, deleteDoc, where, setDoc, getDoc } from 'firebase/firestore';
import { createUserWithEmailAndPassword, deleteUser } from 'firebase/auth';
import CustomButton from '../../components/common/CustomButton';
import CustomInput from '../../components/common/CustomInput';
import { StudentAdmission } from '../admin/StudentAdmission';
import usePermissions from '../../hooks/usePermissions';
import { useNavigation } from '@react-navigation/native';
import TeacherAppHeader from '../../components/common/TeacherAppHeader';

const StudentManagement = () => {
  const [students, setStudents] = useState([]);
  const [classes, setClasses] = useState([]);
  const [sections, setSections] = useState([]);
  const [parents, setParents] = useState([]);
  const [teachers, setTeachers] = useState([]);
  const [visible, setVisible] = useState(false);
  const [showAdmissionModal, setShowAdmissionModal] = useState(false);
  const [selectedStudent, setSelectedStudent] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [showSectionMenu, setShowSectionMenu] = useState(false);
  const [showTeacherMenu, setShowTeacherMenu] = useState(false);
  const [showSectionAssignDialog, setShowSectionAssignDialog] = useState(false);
  const [selectedClass, setSelectedClass] = useState(null);
  const [selectedSection, setSelectedSection] = useState(null);

  useEffect(() => {
    fetchStudents();
    fetchClasses();
    fetchParents();
    fetchTeachers();
  }, []);

  const fetchTeachers = async () => {
    try {
      const teachersRef = collection(db, 'users');
      const q = query(teachersRef, where('role', '==', 'teacher'));
      const querySnapshot = await getDocs(q);

      const teachersData = [];
      querySnapshot.forEach((doc) => {
        teachersData.push({ id: doc.id, ...doc.data() });
      });

      setTeachers(teachersData);
    } catch (error) {
      console.error('Error fetching teachers:', error);
    }
  };

  const fetchSectionsForClass = async (classId) => {
    try {
      const classRef = doc(db, 'classes', classId);
      const classDoc = await getDoc(classRef);
      if (classDoc.exists()) {
        const classData = classDoc.data();
        // Ensure sections is an array of strings
        const sectionNames = Array.isArray(classData.sections)
          ? classData.sections.map(section => typeof section === 'object' ? section.name : section)
          : [];
        setSections(sectionNames);
      }
    } catch (error) {
      console.error('Error fetching sections:', error);
    }
  };

  const handleAssignSection = async () => {
    if (!selectedStudent || !selectedClass || !selectedSection) {
      setError('Please select a class and section');
      return;
    }

    try {
      setLoading(true);
      const studentRef = doc(db, 'users', selectedStudent.id);

      await updateDoc(studentRef, {
        classId: selectedClass,
        section: selectedSection,
        updatedAt: new Date().toISOString()
      });

      // Update local state
      setStudents(students.map(student =>
        student.id === selectedStudent.id
          ? { ...student, classId: selectedClass, section: selectedSection }
          : student
      ));

      setShowSectionAssignDialog(false);
      setSelectedClass(null);
      setSelectedSection(null);
      setSelectedStudent(null);

    } catch (error) {
      console.error('Error assigning section:', error);
      setError('Failed to assign section');
    } finally {
      setLoading(false);
    }
  };

  const fetchStudents = async () => {
    try {
      setLoading(true);
      const studentsRef = collection(db, 'users');
      const q = query(studentsRef, where('role', '==', 'student'));
      const querySnapshot = await getDocs(q);

      const studentsData = [];
      querySnapshot.forEach((doc) => {
        const data = doc.data();
        studentsData.push({
          id: doc.id,
          ...data,
          dateOfBirth: data.dateOfBirth || new Date().toISOString(),
          address: data.address || {},
          parent: data.parent || {},
          academicDetails: data.academicDetails || {},
        });
      });

      setStudents(studentsData);
    } catch (error) {
      console.error('Error fetching students:', error);
      setError('Failed to fetch students');
    } finally {
      setLoading(false);
    }
  };

  const fetchClasses = async () => {
    try {
      const classesRef = collection(db, 'classes');
      const querySnapshot = await getDocs(classesRef);

      const classesData = [];
      querySnapshot.forEach((doc) => {
        const data = doc.data();
        classesData.push({
          id: doc.id,
          name: data.name,
          capacity: data.capacity
        });
      });

      setClasses(classesData);
    } catch (error) {
      console.error('Error fetching classes:', error);
    }
  };

  const fetchParents = async () => {
    try {
      const parentsRef = collection(db, 'users');
      const q = query(parentsRef, where('role', '==', 'parent'));
      const querySnapshot = await getDocs(q);

      const parentsData = [];
      querySnapshot.forEach((doc) => {
        parentsData.push({ id: doc.id, ...doc.data() });
      });

      setParents(parentsData);
    } catch (error) {
      console.error('Error fetching parents:', error);
    }
  };

  const handleEditStudent = (student) => {
    // Ensure all required nested objects exist
    const preparedStudent = {
      ...student,
      address: student.address || {},
      parent: student.parent || {},
      academicDetails: student.academicDetails || {},
    };
    setSelectedStudent(preparedStudent);
    setShowAdmissionModal(true);
  };

  const renderStudentList = () => {
    const filteredStudents = students.filter(student =>
      student.firstName?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      student.lastName?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      student.email?.toLowerCase().includes(searchQuery.toLowerCase())
    );

    return (
      <DataTable>
        <DataTable.Header>
          <DataTable.Title>Name</DataTable.Title>
          <DataTable.Title>Email</DataTable.Title>
          <DataTable.Title >Class</DataTable.Title>
          <DataTable.Title>Section</DataTable.Title>
        </DataTable.Header>

        {filteredStudents.map((student) => (
          <DataTable.Row key={student.id}
          onPress={() => handleEditStudent(student)}>
            <DataTable.Cell>{`${student.firstName || ''} ${student.lastName || ''}`}</DataTable.Cell>
            <DataTable.Cell>{student.email || ''}</DataTable.Cell>
            <DataTable.Cell>
              {classes.find(c => c.id === student.classId)?.name || 'Not Assigned'}
            </DataTable.Cell>
            <DataTable.Cell>{student.sectionName || 'Not Assigned'}</DataTable.Cell>
          </DataTable.Row>
        ))}
      </DataTable>
    );
  };

  const getClassName = (classId) => {
    const classItem = classes.find(c => c.id === classId);
    return classItem ? classItem.name : 'Select Class';
  };

  useEffect(() => {
    if (selectedClass) {
      fetchSectionsForClass(selectedClass);
    } else {
      setSections([]);
    }
  }, [selectedClass]);

  return (
    <View style={styles.container}>
      <ScrollView>
        <Card style={styles.card}>
          <Card.Content>
            <Searchbar
              placeholder="Search students..."
              onChangeText={setSearchQuery}
              value={searchQuery}
              style={styles.searchBar}
            />
            {renderStudentList()}
          </Card.Content>
        </Card>
      </ScrollView>

      <StudentAdmission
        visible={showAdmissionModal}
        onClose={() => {
          setShowAdmissionModal(false);
          setSelectedStudent(null);
        }}
        onSuccess={() => {
          setShowAdmissionModal(false);
          setSelectedStudent(null);
          fetchStudents();
        }}
        student={selectedStudent}
      />

      <Dialog
        visible={showSectionAssignDialog}
        onDismiss={() => setShowSectionAssignDialog(false)}
      >
        <Dialog.Title>Assign Section</Dialog.Title>
        <Dialog.Content>
          <View style={styles.dialogContent}>
            <Menu
              visible={showSectionMenu}
              onDismiss={() => setShowSectionMenu(false)}
              anchor={
                <Button
                  mode="outlined"
                  onPress={() => setShowSectionMenu(true)}
                  style={styles.menuButton}
                >
                  {getClassName(selectedClass)}
                </Button>
              }
            >
              {classes.map((classItem) => (
                <Menu.Item
                  key={classItem.id}
                  onPress={() => {
                    setSelectedClass(classItem.id);
                    setShowSectionMenu(false);
                  }}
                  title={classItem.name || ''}
                />
              ))}
            </Menu>

            <Menu
              visible={showTeacherMenu}
              onDismiss={() => setShowTeacherMenu(false)}
              anchor={
                <Button
                  mode="outlined"
                  onPress={() => setShowTeacherMenu(true)}
                  style={styles.menuButton}
                  disabled={!selectedClass || sections.length === 0}
                >
                  {selectedSection || 'Select Section'}
                </Button>
              }
            >
              {sections.map((section, index) => (
                <Menu.Item
                  key={index}
                  onPress={() => {
                    setSelectedSection(section);
                    setShowTeacherMenu(false);
                  }}
                  title={section || ''}
                />
              ))}
            </Menu>
          </View>
        </Dialog.Content>
        <Dialog.Actions>
          <Button onPress={() => setShowSectionAssignDialog(false)}>Cancel</Button>
          <Button
            mode="contained"
            onPress={handleAssignSection}
            loading={loading}
            disabled={!selectedClass || !selectedSection}
          >
            Assign
          </Button>
        </Dialog.Actions>
      </Dialog>

      <FAB
        style={styles.fab}
        icon="plus"
        onPress={() => setShowAdmissionModal(true)}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  card: {
    margin: 16,
  },
  searchBar: {
    marginBottom: 16,
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
  },
  dialogContent: {
    gap: 16,
  },
  menuButton: {
    marginVertical: 8,
  },
  actionButton: {
    marginHorizontal: 4,
  },
  modalContainer: {
    backgroundColor: 'white',
    padding: 20,
    margin: 20,
    borderRadius: 8,
    flex: 1,
  },
});

export default StudentManagement;
