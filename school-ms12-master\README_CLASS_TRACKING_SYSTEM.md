# Class Tracking System

This package provides a comprehensive class tracking system for the school management application. It allows users to track ongoing classes, view upcoming classes, and receive notifications for class changes.

## Table of Contents

1. [Overview](#overview)
2. [Features](#features)
3. [Components](#components)
4. [Setup](#setup)
5. [Usage](#usage)
6. [Testing](#testing)
7. [Troubleshooting](#troubleshooting)
8. [Customization](#customization)
9. [Security](#security)
10. [Scripts](#scripts)

## Overview

The class tracking system is designed to provide real-time information about ongoing and upcoming classes for all user roles (teachers, students, and parents). It integrates with the existing school management application and uses Firebase Firestore for data storage.

## Features

- **Real-time Class Tracking**: Shows the current ongoing class and next upcoming class for each user role
- **Role-based Views**: Different views for teachers, students, and parents
- **Notifications**: Automatic notifications for upcoming classes
- **Dashboard Integration**: Class tracking widgets on all user dashboards
- **Schedule Navigation**: Quick access to detailed schedule information

## Components

### OngoingClassTracker

The `OngoingClassTracker` component displays the current and next class periods. It includes:

- Current class information (subject, teacher/class, room, period)
- Next class information with countdown timer
- Session indicators (morning/afternoon)
- Period indicators

### DashboardClassWidget

The `DashboardClassWidget` component is a dashboard widget that shows the current class status. It:

- Fetches the appropriate schedule based on user role
- Displays the OngoingClassTracker component
- Handles loading and error states
- Provides retry functionality for failed data fetching

### NotificationService

The `NotificationService` provides methods for sending and scheduling notifications, including:

- `sendUpcomingClassNotification`: Sends notifications for upcoming classes
- `scheduleLocalNotification`: Schedules notifications for future delivery
- `handleClassScheduleNotification`: Handles navigation when a notification is tapped

## Setup

To set up the class tracking system, follow these steps:

1. **Run the Setup Script**:
   ```
   node setup-class-tracking.js
   ```
   This script will guide you through the setup process, including:
   - Deploying Firestore security rules
   - Updating timetable data
   - Running tests

2. **Manual Setup**:
   If you prefer to set up each component manually, follow these steps:

   a. **Deploy Firestore Security Rules**:
   ```
   node deploy-firestore-rules.js
   ```

   b. **Update Timetable Data**:
   ```
   node update-timetable-data.js
   ```

   c. **Run Tests**:
   ```
   node test-class-tracking.js
   ```

## Usage

### For Administrators

1. **Create Class Schedules**:
   - Navigate to the Class Schedule Management screen
   - Create schedules for each class and section
   - Assign teachers, subjects, and rooms
   - Publish the schedules to make them visible to users

2. **Update Schedules**:
   - Edit existing schedules as needed
   - Changes will be reflected in real-time for all users

### For Teachers

1. **View Current Class**:
   - The dashboard shows your current teaching assignment
   - See which class, subject, and room you're teaching in

2. **View Full Schedule**:
   - Click "View Schedule" to see your complete teaching schedule
   - Filter by day or time as needed

### For Students

1. **Track Classes**:
   - The dashboard shows your current and next class
   - See subject, teacher, room, and time information

2. **View Full Schedule**:
   - Click "View Schedule" to see your complete class schedule
   - Filter by day or time as needed

### For Parents

1. **Track Child's Classes**:
   - The dashboard shows your child's current and next class
   - If you have multiple children, select the child to view their schedule

2. **View Full Schedule**:
   - Click "View Child's Schedule" to see the complete class schedule
   - Filter by day or time as needed

## Testing

To test the class tracking system, follow these steps:

1. **Run the Test Script**:
   ```
   node test-class-tracking.js
   ```
   This script will run tests for the class tracking components and provide a summary of the results.

2. **Manual Testing**:
   - Log in as different user roles (teacher, student, parent)
   - Check the dashboard for the class tracking widget
   - Verify that the current and next class information is displayed correctly
   - Test the "View Schedule" button to ensure it navigates to the correct screen
   - Wait for an upcoming class to test the notification system

## Troubleshooting

### Permission Errors

If you encounter "Missing or insufficient permissions" errors:

1. Deploy the Firestore security rules in `firestore.rules`
2. Run the `src/scripts/updateTimetables.js` script to ensure all timetable entries have the required fields
3. Verify that users have the correct roles assigned

### No Schedule Found

If no schedule is displayed:

1. Check that timetable entries exist for the user's class/section or teaching assignments
2. Verify that the entries have the `published` field set to `true`
3. Check that the day and time fields match the expected format

### Notification Issues

If notifications are not working:

1. Ensure the user has granted notification permissions
2. Check that the user has a valid push token in their user document
3. Verify that the NotificationService is properly initialized

## Customization

### Time Slots

The system uses predefined time slots for morning and afternoon sessions:

- **Morning Session**: 6 periods from 08:00 to 12:25
- **Afternoon Session**: 6 periods from 12:30 to 16:55

To customize these time slots, modify the `morningTimeSlots` and `afternoonTimeSlots` arrays in:

1. `src/scripts/updateTimetables.js`
2. `src/scripts/generateSampleTimetables.js`

### UI Customization

To customize the appearance of the class tracking components:

1. Modify the styles in `src/components/common/OngoingClassTracker.js`
2. Modify the styles in `src/components/common/DashboardClassWidget.js`

## Security

The Firestore security rules in `firestore.rules` implement the following permissions:

- **Teachers** can access timetable entries where they are the assigned teacher
- **Students** can access timetable entries for their class and section
- **Parents** can access timetable entries for their children's classes
- **Admins** have full access to all collections

## Scripts

The following scripts are included in this package:

- **setup-class-tracking.js**: Main setup script that guides you through the entire setup process
- **deploy-firestore-rules.js**: Deploys Firestore security rules
- **update-timetable-data.js**: Updates timetable data and generates sample data if needed
- **test-class-tracking.js**: Runs tests for the class tracking components
- **src/scripts/updateTimetables.js**: Updates existing timetable entries to ensure they have the required fields
- **src/scripts/generateSampleTimetables.js**: Generates sample timetable data for testing
