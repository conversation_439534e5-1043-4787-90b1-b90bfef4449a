# Class Schedule Tracking System

This document provides an overview of the class schedule tracking system implemented in the application. The system allows users to track ongoing classes, view upcoming classes, and receive notifications for class changes.

## Features

1. **Real-time Class Tracking**: Shows the current ongoing class and next upcoming class for each user role
2. **Role-based Views**: Different views for teachers, students, and parents
3. **Notifications**: Automatic notifications for upcoming classes
4. **Dashboard Integration**: Class tracking widgets on all user dashboards
5. **Schedule Navigation**: Quick access to detailed schedule information

## Components

### 1. OngoingClassTracker

The `OngoingClassTracker` component displays the current and next class periods. It includes:

- Current class information (subject, teacher/class, room, period)
- Next class information with countdown timer
- Session indicators (morning/afternoon)
- Period indicators

### 2. DashboardClassWidget

The `DashboardClassWidget` component is a dashboard widget that shows the current class status. It:

- Fetches the appropriate schedule based on user role
- Displays the OngoingClassTracker component
- Handles loading and error states
- Provides retry functionality for failed data fetching

### 3. NotificationService

The `NotificationService` provides methods for sending and scheduling notifications, including:

- `sendUpcomingClassNotification`: Sends notifications for upcoming classes
- `scheduleLocalNotification`: Schedules notifications for future delivery
- `handleClassScheduleNotification`: Handles navigation when a notification is tapped

## Data Structure

The timetable entries in Firestore should have the following structure:

```javascript
{
  classId: "class123",           // ID of the class
  sectionName: "A",              // Section name
  day: "Monday",                 // Day of the week
  periodNumber: 1,               // Period number (1-6)
  startTime: "08:00",            // Start time (24-hour format)
  endTime: "08:40",              // End time (24-hour format)
  session: "morning",            // Session (morning/afternoon)
  subjectId: "math",             // Subject ID
  subjectName: "Mathematics",    // Subject name
  teacherId: "teacher123",       // Teacher ID
  teacherName: "John Doe",       // Teacher name
  roomNumber: "101",             // Room number
  published: true,               // Whether the entry is published
  createdAt: "2023-06-01T12:00:00Z" // Creation timestamp
}
```

## Usage Instructions

### For Administrators

1. **Create Class Schedules**:
   - Navigate to the Class Schedule Management screen
   - Create schedules for each class and section
   - Assign teachers, subjects, and rooms
   - Publish the schedules to make them visible to users

2. **Update Schedules**:
   - Edit existing schedules as needed
   - Changes will be reflected in real-time for all users

3. **Generate Sample Data**:
   - Use the `src/scripts/generateSampleTimetables.js` script to generate sample timetable data
   - Run `node src/scripts/generateSampleTimetables.js` to execute the script

### For Teachers

1. **View Current Class**:
   - The dashboard shows your current teaching assignment
   - See which class, subject, and room you're teaching in

2. **View Full Schedule**:
   - Click "View Schedule" to see your complete teaching schedule
   - Filter by day or time as needed

### For Students

1. **Track Classes**:
   - The dashboard shows your current and next class
   - See subject, teacher, room, and time information

2. **View Full Schedule**:
   - Click "View Schedule" to see your complete class schedule
   - Filter by day or time as needed

### For Parents

1. **Track Child's Classes**:
   - The dashboard shows your child's current and next class
   - If you have multiple children, select the child to view their schedule

2. **View Full Schedule**:
   - Click "View Child's Schedule" to see the complete class schedule
   - Filter by day or time as needed

## Troubleshooting

### Permission Errors

If you encounter "Missing or insufficient permissions" errors:

1. Deploy the Firestore security rules in `firestore.rules`
2. Run the `src/scripts/updateTimetables.js` script to ensure all timetable entries have the required fields
3. Verify that users have the correct roles assigned

### No Schedule Found

If no schedule is displayed:

1. Check that timetable entries exist for the user's class/section or teaching assignments
2. Verify that the entries have the `published` field set to `true`
3. Check that the day and time fields match the expected format

### Notification Issues

If notifications are not working:

1. Ensure the user has granted notification permissions
2. Check that the user has a valid push token in their user document
3. Verify that the NotificationService is properly initialized

## Customization

### Time Slots

The system uses predefined time slots for morning and afternoon sessions:

- **Morning Session**: 6 periods from 08:00 to 12:25
- **Afternoon Session**: 6 periods from 12:30 to 16:55

To customize these time slots, modify the `morningTimeSlots` and `afternoonTimeSlots` arrays in:

1. `src/scripts/updateTimetables.js`
2. `src/scripts/generateSampleTimetables.js`

### UI Customization

To customize the appearance of the class tracking components:

1. Modify the styles in `src/components/common/OngoingClassTracker.js`
2. Modify the styles in `src/components/common/DashboardClassWidget.js`

## Security Rules

The Firestore security rules in `firestore.rules` implement the following permissions:

- **Teachers** can access timetable entries where they are the assigned teacher
- **Students** can access timetable entries for their class and section
- **Parents** can access timetable entries for their children's classes
- **Admins** have full access to all collections
