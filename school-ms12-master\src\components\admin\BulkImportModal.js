import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView, Alert, Platform } from 'react-native';
import {
  Portal,
  Modal,
  Title,
  Text,
  Button,
  IconButton,
  Divider,
  DataTable,
  ActivityIndicator,
  Surface,
  Chip,
  ProgressBar,
} from 'react-native-paper';
import * as DocumentPicker from 'expo-document-picker';
import * as FileSystem from 'expo-file-system';
import * as XLSX from 'xlsx';
import { useLanguage } from '../../context/LanguageContext';
import { LinearGradient } from 'expo-linear-gradient';
import { db, auth } from '../../config/firebase';
import {
  collection,
  addDoc,
  query,
  getDocs,
  doc,
  updateDoc,
  setDoc,
  where,
  serverTimestamp,
} from 'firebase/firestore';
import { createUserWithEmailAndPassword } from 'firebase/auth';
import { MaterialCommunityIcons } from '@expo/vector-icons';

const BulkImportModal = ({ visible, onClose, onSuccess }) => {
  const { translate, isRTL } = useLanguage();
  const [loading, setLoading] = useState(false);
  const [fileData, setFileData] = useState(null);
  const [parsedData, setParsedData] = useState([]);
  const [validationErrors, setValidationErrors] = useState([]);
  const [importStep, setImportStep] = useState(1);
  const [importProgress, setImportProgress] = useState(0);
  const [processedCount, setProcessedCount] = useState(0);
  const [totalCount, setTotalCount] = useState(0);
  const [existingParents, setExistingParents] = useState({});
  const [classes, setClasses] = useState([]);

  useEffect(() => {
    if (visible) {
      fetchClasses();
      fetchExistingParents();
    } else {
      resetState();
    }
  }, [visible]);

  const resetState = () => {
    setFileData(null);
    setParsedData([]);
    setValidationErrors([]);
    setImportStep(1);
    setImportProgress(0);
    setProcessedCount(0);
    setTotalCount(0);
  };

  const fetchClasses = async () => {
    try {
      const classesRef = collection(db, 'classes');
      const querySnapshot = await getDocs(classesRef);

      const classesData = {};
      querySnapshot.forEach((doc) => {
        const data = doc.data();
        classesData[data.name] = {
          id: doc.id,
          name: data.name,
          sections: data.sections || [],
        };
      });

      setClasses(classesData);
    } catch (error) {
      console.error('Error fetching classes:', error);
    }
  };

  const fetchExistingParents = async () => {
    try {
      const parentsRef = collection(db, 'users');
      const q = query(parentsRef, where('role', '==', 'parent'));
      const querySnapshot = await getDocs(q);

      const parentsData = {};
      querySnapshot.forEach((doc) => {
        const data = doc.data();
        if (data.email) {
          parentsData[data.email.toLowerCase()] = {
            id: doc.id,
            ...data,
          };
        }
      });

      setExistingParents(parentsData);
    } catch (error) {
      console.error('Error fetching existing parents:', error);
    }
  };

  const pickExcelFile = async () => {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        copyToCacheDirectory: true,
      });

      if (result.canceled) {
        return;
      }

      setFileData(result.assets[0]);
      parseExcelFile(result.assets[0].uri);
    } catch (error) {
      console.error('Error picking file:', error);
      Alert.alert(
        translate('bulkImport.error') || 'Error',
        translate('bulkImport.errorPickingFile') || 'Error picking file'
      );
    }
  };

  const parseExcelFile = async (fileUri) => {
    try {
      setLoading(true);

      const fileContent = await FileSystem.readAsStringAsync(fileUri, {
        encoding: FileSystem.EncodingType.Base64,
      });

      const workbook = XLSX.read(fileContent, { type: 'base64' });
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];
      const jsonData = XLSX.utils.sheet_to_json(worksheet);

      // Validate and transform data
      const { validData, errors } = validateData(jsonData);
      setParsedData(validData);
      setValidationErrors(errors);
      setTotalCount(validData.length);

      setLoading(false);
    } catch (error) {
      console.error('Error parsing Excel file:', error);
      setLoading(false);
      Alert.alert(
        translate('bulkImport.error') || 'Error',
        translate('bulkImport.errorParsingFile') || 'Error parsing Excel file'
      );
    }
  };

  const validateData = (data) => {
    const validData = [];
    const errors = [];

    data.forEach((row, index) => {
      const rowErrors = [];

      // Required student fields
      if (!row.StudentFirstName) rowErrors.push('Student first name is required');
      if (!row.StudentLastName) rowErrors.push('Student last name is required');
      if (!row.StudentEmail) rowErrors.push('Student email is required');
      if (!row.StudentPassword) rowErrors.push('Student password is required');
      if (!row.Class) rowErrors.push('Class is required');
      if (!row.Section) rowErrors.push('Section is required');

      // Check if class exists
      if (row.Class && !classes[row.Class]) {
        rowErrors.push(`Class "${row.Class}" does not exist`);
      }

      // Check if section exists for the class
      if (row.Class && row.Section && classes[row.Class]) {
        const sectionExists = classes[row.Class].sections.some(
          s => (typeof s === 'object' ? s.name : s) === row.Section
        );
        if (!sectionExists) {
          rowErrors.push(`Section "${row.Section}" does not exist in class "${row.Class}"`);
        }
      }

      // Required parent fields if parent email is provided
      if (row.ParentEmail) {
        if (!row.ParentFirstName) rowErrors.push('Parent first name is required');
        if (!row.ParentLastName) rowErrors.push('Parent last name is required');
        if (!existingParents[row.ParentEmail.toLowerCase()] && !row.ParentPassword) {
          rowErrors.push('Parent password is required for new parents');
        }
      }

      if (rowErrors.length > 0) {
        errors.push({
          row: index + 2, // +2 because Excel is 1-indexed and we have a header row
          errors: rowErrors,
        });
      } else {
        // Transform data to our format
        validData.push({
          student: {
            firstName: row.StudentFirstName,
            lastName: row.StudentLastName,
            email: row.StudentEmail,
            password: row.StudentPassword,
            gender: row.StudentGender || 'Other',
            dateOfBirth: row.StudentDateOfBirth || new Date().toISOString(),
            phone: row.StudentPhone || '',
            emergencyContact: row.EmergencyContact || '',
            address: {
              street: row.StudentStreet || '',
              city: row.StudentCity || '',
              state: row.StudentState || 'Oromia',
              country: row.StudentCountry || 'Ethiopia',
            },
            className: row.Class,
            sectionName: row.Section,
            academicDetails: {
              previousSchool: row.PreviousSchool || '',
              previousGrade: row.PreviousGrade || '',
              reasonForLeaving: row.ReasonForLeaving || '',
            },
          },
          parent: row.ParentEmail ? {
            firstName: row.ParentFirstName,
            lastName: row.ParentLastName,
            email: row.ParentEmail,
            password: row.ParentPassword || '123qwe',
            phone: row.ParentPhone || '',
            alternatePhone: row.ParentAlternatePhone || '',
            relationship: row.ParentRelationship || 'parent',
            occupation: row.ParentOccupation || '',
            workAddress: row.ParentWorkAddress || '',
            workPhone: row.ParentWorkPhone || '',
            address: {
              street: row.ParentStreet || '',
              city: row.ParentCity || '',
              state: row.ParentState || 'Oromia',
              country: row.ParentCountry || 'Ethiopia',
            },
          } : null,
          existingParent: row.ParentEmail ? existingParents[row.ParentEmail.toLowerCase()] : null,
        });
      }
    });

    return { validData, errors };
  };

  const importStudents = async () => {
    if (parsedData.length === 0) {
      Alert.alert(
        translate('bulkImport.error') || 'Error',
        translate('bulkImport.noValidData') || 'No valid data to import'
      );
      return;
    }

    try {
      setLoading(true);
      setImportStep(2);
      setProcessedCount(0);
      setImportProgress(0);

      for (let i = 0; i < parsedData.length; i++) {
        const item = parsedData[i];
        try {
          // Process parent first if needed
          let parentData = null;
          if (item.parent) {
            if (item.existingParent) {
              // Use existing parent
              parentData = item.existingParent;
            } else {
              // Create new parent
              parentData = await createParent(item.parent);
            }
          }

          // Process student
          await createStudent(item.student, parentData);

          // Update progress
          setProcessedCount(i + 1);
          setImportProgress((i + 1) / parsedData.length);
        } catch (error) {
          console.error(`Error processing item ${i}:`, error);
          // Continue with next item
        }
      }

      // Import completed
      setImportStep(3);
      setLoading(false);
      
      // Refresh data
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error('Error importing students:', error);
      setLoading(false);
      Alert.alert(
        translate('bulkImport.error') || 'Error',
        translate('bulkImport.errorImporting') || 'Error importing students'
      );
    }
  };

  const createParent = async (parentData) => {
    try {
      // Create auth account
      const userCredential = await createUserWithEmailAndPassword(
        auth,
        parentData.email,
        parentData.password
      );

      const parentId = userCredential.user.uid;

      // Create parent document
      const parentDoc = {
        ...parentData,
        id: parentId,
        role: 'parent',
        status: 'active',
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        children: []
      };
      delete parentDoc.password; // Remove password from document

      await setDoc(doc(db, 'users', parentId), parentDoc);
      
      return { id: parentId, ...parentDoc };
    } catch (error) {
      console.error('Error creating parent:', error);
      throw error;
    }
  };

  const createStudent = async (studentData, parentData) => {
    try {
      // Find class ID
      const classId = classes[studentData.className]?.id;
      if (!classId) {
        throw new Error(`Class "${studentData.className}" not found`);
      }

      // Create auth account
      const userCredential = await createUserWithEmailAndPassword(
        auth,
        studentData.email,
        studentData.password
      );

      const studentId = userCredential.user.uid;

      // Prepare student document
      const studentDoc = {
        ...studentData,
        id: studentId,
        role: 'student',
        classId: classId,
        status: 'active',
        parent: parentData ? {
          id: parentData.id,
          firstName: parentData.firstName || '',
          lastName: parentData.lastName || '',
          email: parentData.email || '',
          phone: parentData.phone || '',
          relationship: parentData.relationship || '',
        } : null,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      };
      delete studentDoc.password; // Remove password from document

      // Save student document
      await setDoc(doc(db, 'users', studentId), studentDoc);

      // Update class section
      if (classId && studentData.sectionName) {
        const classRef = doc(db, 'classes', classId);
        const classDoc = await getDocs(classRef);
        
        if (classDoc.exists) {
          const sections = classDoc.data().sections || [];
          const sectionIndex = sections.findIndex(s => 
            (typeof s === 'object' ? s.name : s) === studentData.sectionName
          );

          if (sectionIndex !== -1) {
            const section = sections[sectionIndex];
            const students = typeof section === 'object' ? (section.students || []) : [];
            
            if (!students.includes(studentId)) {
              students.push(studentId);
              
              if (typeof section === 'object') {
                sections[sectionIndex] = { ...section, students };
              } else {
                sections[sectionIndex] = { name: section, students };
              }
              
              await updateDoc(classRef, { sections });
            }
          }
        }
      }

      // Update parent's children array if parent exists
      if (parentData) {
        const parentRef = doc(db, 'users', parentData.id);
        const children = parentData.children || [];
        
        if (!children.includes(studentId)) {
          children.push(studentId);
          await updateDoc(parentRef, { 
            children,
            updatedAt: serverTimestamp()
          });
        }
      }

      return studentId;
    } catch (error) {
      console.error('Error creating student:', error);
      throw error;
    }
  };

  const renderStep1 = () => (
    <>
      <Text style={styles.description}>
        {translate('bulkImport.description') || 
          'Upload an Excel file with student and parent information. The file should have the following columns:'}
      </Text>
      
      <Surface style={styles.infoSurface}>
        <Text style={styles.infoTitle}>
          {translate('bulkImport.requiredColumns') || 'Required Columns:'}
        </Text>
        <Text style={styles.infoText}>
          StudentFirstName, StudentLastName, StudentEmail, StudentPassword, Class, Section
        </Text>
        
        <Text style={styles.infoTitle}>
          {translate('bulkImport.optionalColumns') || 'Optional Columns:'}
        </Text>
        <Text style={styles.infoText}>
          StudentGender, StudentDateOfBirth, StudentPhone, EmergencyContact, StudentStreet, StudentCity, StudentState, StudentCountry, 
          PreviousSchool, PreviousGrade, ReasonForLeaving
        </Text>
        
        <Text style={styles.infoTitle}>
          {translate('bulkImport.parentColumns') || 'Parent Columns (required if ParentEmail is provided):'}
        </Text>
        <Text style={styles.infoText}>
          ParentEmail, ParentFirstName, ParentLastName, ParentPassword (required for new parents), 
          ParentPhone, ParentAlternatePhone, ParentRelationship, ParentOccupation, ParentWorkAddress, 
          ParentWorkPhone, ParentStreet, ParentCity, ParentState, ParentCountry
        </Text>
      </Surface>
      
      <Button
        mode="contained"
        icon="file-upload"
        onPress={pickExcelFile}
        style={styles.uploadButton}
        disabled={loading}
      >
        {translate('bulkImport.selectFile') || 'Select Excel File'}
      </Button>
      
      {fileData && (
        <View style={styles.fileInfo}>
          <MaterialCommunityIcons name="file-excel" size={24} color="#4CAF50" />
          <Text style={styles.fileName}>{fileData.name}</Text>
        </View>
      )}
      
      {loading && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#1976d2" />
          <Text style={styles.loadingText}>
            {translate('bulkImport.parsing') || 'Parsing file...'}
          </Text>
        </View>
      )}
      
      {parsedData.length > 0 && (
        <View style={styles.dataPreview}>
          <Text style={styles.previewTitle}>
            {translate('bulkImport.preview') || 'Data Preview'}
          </Text>
          <Text style={styles.previewSubtitle}>
            {translate('bulkImport.validRecords', { count: parsedData.length }) || 
              `${parsedData.length} valid records found`}
          </Text>
          
          <ScrollView style={styles.previewTable}>
            <DataTable>
              <DataTable.Header>
                <DataTable.Title>Student Name</DataTable.Title>
                <DataTable.Title>Email</DataTable.Title>
                <DataTable.Title>Class</DataTable.Title>
                <DataTable.Title>Parent</DataTable.Title>
              </DataTable.Header>
              
              {parsedData.slice(0, 5).map((item, index) => (
                <DataTable.Row key={index}>
                  <DataTable.Cell>
                    {`${item.student.firstName} ${item.student.lastName}`}
                  </DataTable.Cell>
                  <DataTable.Cell>{item.student.email}</DataTable.Cell>
                  <DataTable.Cell>{`${item.student.className} - ${item.student.sectionName}`}</DataTable.Cell>
                  <DataTable.Cell>
                    {item.parent ? (
                      <Chip 
                        icon={item.existingParent ? "account-check" : "account-plus"}
                        style={item.existingParent ? styles.existingParentChip : styles.newParentChip}
                      >
                        {item.existingParent ? "Existing" : "New"}
                      </Chip>
                    ) : "None"}
                  </DataTable.Cell>
                </DataTable.Row>
              ))}
              
              {parsedData.length > 5 && (
                <DataTable.Row>
                  <DataTable.Cell>...</DataTable.Cell>
                  <DataTable.Cell>...</DataTable.Cell>
                  <DataTable.Cell>...</DataTable.Cell>
                  <DataTable.Cell>...</DataTable.Cell>
                </DataTable.Row>
              )}
            </DataTable>
          </ScrollView>
          
          <Button
            mode="contained"
            icon="database-import"
            onPress={importStudents}
            style={styles.importButton}
            disabled={loading || parsedData.length === 0}
          >
            {translate('bulkImport.import') || 'Import Students'}
          </Button>
        </View>
      )}
      
      {validationErrors.length > 0 && (
        <View style={styles.errorsContainer}>
          <Text style={styles.errorsTitle}>
            {translate('bulkImport.validationErrors') || 'Validation Errors'}
          </Text>
          
          <ScrollView style={styles.errorsList}>
            {validationErrors.map((error, index) => (
              <Surface key={index} style={styles.errorItem}>
                <Text style={styles.errorRow}>
                  {translate('bulkImport.row', { row: error.row }) || `Row ${error.row}:`}
                </Text>
                {error.errors.map((err, i) => (
                  <Text key={i} style={styles.errorText}>• {err}</Text>
                ))}
              </Surface>
            ))}
          </ScrollView>
        </View>
      )}
    </>
  );

  const renderStep2 = () => (
    <View style={styles.progressContainer}>
      <Text style={styles.progressTitle}>
        {translate('bulkImport.importing') || 'Importing Students...'}
      </Text>
      
      <ProgressBar
        progress={importProgress}
        color="#4CAF50"
        style={styles.progressBar}
      />
      
      <Text style={styles.progressText}>
        {translate('bulkImport.progress', { current: processedCount, total: totalCount }) || 
          `Processing ${processedCount} of ${totalCount} records`}
      </Text>
      
      <ActivityIndicator size="large" color="#1976d2" style={styles.progressIndicator} />
    </View>
  );

  const renderStep3 = () => (
    <View style={styles.successContainer}>
      <MaterialCommunityIcons name="check-circle" size={80} color="#4CAF50" style={styles.successIcon} />
      
      <Text style={styles.successTitle}>
        {translate('bulkImport.importComplete') || 'Import Complete!'}
      </Text>
      
      <Text style={styles.successText}>
        {translate('bulkImport.importedCount', { count: processedCount }) || 
          `Successfully imported ${processedCount} students.`}
      </Text>
      
      <Button
        mode="contained"
        onPress={onClose}
        style={styles.doneButton}
      >
        {translate('common.done') || 'Done'}
      </Button>
    </View>
  );

  return (
    <Portal>
      <Modal
        visible={visible}
        onDismiss={importStep === 2 ? null : onClose}
        contentContainerStyle={styles.modalContainer}
        dismissable={importStep !== 2}
      >
        <LinearGradient
          colors={['#1976d2', '#f5f5f5']}
          style={styles.headerGradient}
        >
          <View style={styles.header}>
            <Title style={styles.headerTitle}>
              {translate('bulkImport.title') || 'Bulk Import Students'}
            </Title>
            {importStep !== 2 && (
              <IconButton
                icon="close"
                size={24}
                color="white"
                onPress={onClose}
              />
            )}
          </View>
        </LinearGradient>
        
        <ScrollView style={styles.content}>
          {importStep === 1 && renderStep1()}
          {importStep === 2 && renderStep2()}
          {importStep === 3 && renderStep3()}
        </ScrollView>
      </Modal>
    </Portal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    backgroundColor: 'white',
    margin: 20,
    borderRadius: 8,
    maxHeight: '90%',
    width: '90%',
    alignSelf: 'center',
  },
  headerGradient: {
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  headerTitle: {
    color: 'white',
    fontSize: 20,
    fontWeight: 'bold',
  },
  content: {
    padding: 16,
  },
  description: {
    fontSize: 14,
    color: '#555',
    marginBottom: 16,
  },
  infoSurface: {
    padding: 16,
    borderRadius: 8,
    backgroundColor: '#f5f5f5',
    marginBottom: 16,
  },
  infoTitle: {
    fontWeight: 'bold',
    fontSize: 14,
    marginTop: 8,
    marginBottom: 4,
  },
  infoText: {
    fontSize: 12,
    color: '#555',
    marginBottom: 8,
  },
  uploadButton: {
    marginVertical: 16,
  },
  fileInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    backgroundColor: '#e8f5e9',
    borderRadius: 4,
    marginBottom: 16,
  },
  fileName: {
    marginLeft: 8,
    fontSize: 14,
    color: '#333',
  },
  loadingContainer: {
    alignItems: 'center',
    padding: 16,
  },
  loadingText: {
    marginTop: 8,
    fontSize: 14,
    color: '#555',
  },
  dataPreview: {
    marginTop: 16,
  },
  previewTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  previewSubtitle: {
    fontSize: 14,
    color: '#555',
    marginBottom: 16,
  },
  previewTable: {
    maxHeight: 300,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 4,
  },
  importButton: {
    marginTop: 16,
    backgroundColor: '#4CAF50',
  },
  errorsContainer: {
    marginTop: 24,
  },
  errorsTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#f44336',
    marginBottom: 8,
  },
  errorsList: {
    maxHeight: 200,
  },
  errorItem: {
    padding: 12,
    borderRadius: 4,
    marginBottom: 8,
    backgroundColor: '#ffebee',
  },
  errorRow: {
    fontWeight: 'bold',
    marginBottom: 4,
  },
  errorText: {
    fontSize: 12,
    color: '#d32f2f',
    marginLeft: 8,
  },
  progressContainer: {
    alignItems: 'center',
    padding: 24,
  },
  progressTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 24,
  },
  progressBar: {
    height: 8,
    width: '100%',
    borderRadius: 4,
    marginBottom: 16,
  },
  progressText: {
    fontSize: 14,
    color: '#555',
    marginBottom: 24,
  },
  progressIndicator: {
    marginTop: 16,
  },
  successContainer: {
    alignItems: 'center',
    padding: 24,
  },
  successIcon: {
    marginBottom: 16,
  },
  successTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#4CAF50',
    marginBottom: 16,
  },
  successText: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 24,
  },
  doneButton: {
    backgroundColor: '#4CAF50',
    paddingHorizontal: 32,
  },
  existingParentChip: {
    backgroundColor: '#e3f2fd',
  },
  newParentChip: {
    backgroundColor: '#e8f5e9',
  },
});

export default BulkImportModal;
