import React, { createContext, useState, useContext, useEffect } from 'react';
import {
  signInWithEmailAndPassword,
  signOut,
  onAuthStateChanged
} from 'firebase/auth';
import { doc, getDoc } from 'firebase/firestore';
import { getAuth, getFirestore } from 'firebase/app';
import { auth, db } from '../config/firebase';
import NetInfo from '@react-native-community/netinfo';
import ActivityService from '../services/ActivityService';

const AuthContext = createContext();

export const useAuth = () => useContext(AuthContext);

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [userRole, setUserRole] = useState(null);
  const [loading, setLoading] = useState(true);
  const [networkError, setNetworkError] = useState(false);

  useEffect(() => {
    console.log('AuthProvider mounted');
    // Subscribe to network state updates
    const unsubscribeNet = NetInfo.addEventListener(state => {
      setNetworkError(!state.isConnected);
    });

    const unsubscribeAuth = onAuthStateChanged(auth, async (user) => {
      console.log('Auth state changed:', user?.email);
      try {
        if (user) {
          // Check if this is an admin creating a new user
          // If the current user in context is an admin and still valid, don't update
          if (userRole === 'admin' && user.email !== auth.currentUser?.email) {
            console.log('Admin creating a new user. Maintaining admin session.');
            // Don't update the context state
            setLoading(false);
            return;
          }

          const userRef = doc(db, 'users', user.uid);
          const userDoc = await getDoc(userRef);

          if (userDoc.exists()) {
            console.log('User document found:', userDoc.data());
            setUser(user);
            setUserRole(userDoc.data().role);
          } else {
            console.error('User document not found');
            // Don't automatically sign out - this could be a new user being created
            // Instead, check if we're in the middle of user creation
            if (!userRole) {
              // Only sign out if we're not already signed in as someone else
              await signOut(auth);
              setUser(null);
              setUserRole(null);
            }
          }
        } else {
          console.log('No user signed in');
          setUser(null);
          setUserRole(null);
        }
      } catch (error) {
        console.error('Auth state change error:', error);
        if (error.code === 'auth/network-request-failed') {
          setNetworkError(true);
        }
      } finally {
        setLoading(false);
      }
    });

    return () => {
      unsubscribeAuth();
      unsubscribeNet();
    };
  }, []);

  const login = async (email, password) => {
    console.log('Login function called with email:', email);
    try {
      const networkState = await NetInfo.fetch();
      if (!networkState.isConnected) {
        console.log('No internet connection');
        return {
          success: false,
          error: 'No internet connection. Please check your network.'
        };
      }

      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      console.log('Firebase auth successful:', userCredential.user.email);

      const userRef = doc(db, 'users', userCredential.user.uid);
      const userDoc = await getDoc(userRef);

      if (!userDoc.exists()) {
        console.log('No user document found');
        await signOut(auth);
        return {
          success: false,
          error: 'User account not found. Please contact administrator.'
        };
      }

      const userData = userDoc.data();
      console.log('User data:', userData);

      setUser(userCredential.user);
      setUserRole(userData.role);

      // Log the login activity
      try {
        await ActivityService.logLogin(
          userCredential.user.uid, 
          userData.displayName || userData.firstName + ' ' + userData.lastName || email
        );
      } catch (activityError) {
        console.error('Error logging login activity:', activityError);
        // Don't fail the login if activity logging fails
      }

      return {
        success: true,
        user: userCredential.user,
        role: userData.role
      };
    } catch (error) {
      console.error('Login error:', error);

      let errorMessage = 'Failed to login. ';
      switch (error.code) {
        case 'auth/invalid-credential':
          errorMessage += 'Invalid email or password.';
          break;
        case 'auth/user-disabled':
          errorMessage += 'This account has been disabled.';
          break;
        case 'auth/invalid-email':
          errorMessage += 'Invalid email address.';
          break;
        case 'auth/user-not-found':
          errorMessage += 'No account found with this email.';
          break;
        case 'auth/wrong-password':
          errorMessage += 'Incorrect password.';
          break;
        case 'auth/network-request-failed':
          errorMessage += 'Network error. Please check your connection.';
          break;
        default:
          errorMessage += 'An unexpected error occurred.';
      }

      return {
        success: false,
        error: errorMessage
      };
    }
  };

  const logout = async () => {
    try {
      // Log the logout activity before actually signing out
      if (user) {
        try {
          await ActivityService.logLogout(
            user.uid, 
            user.displayName || user.email
          );
        } catch (activityError) {
          console.error('Error logging logout activity:', activityError);
          // Don't fail the logout if activity logging fails
        }
      }
      
      await signOut(auth);
      setUser(null);
      setUserRole(null);
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  const value = {
    user,
    userRole,
    loading,
    networkError,
    login,
    logout
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export default AuthContext;
