import { db, auth } from '../config/firebase';
import { 
  collection, 
  addDoc, 
  query, 
  where, 
  getDocs, 
  updateDoc, 
  doc, 
  orderBy, 
  limit, 
  batch,
  getDoc 
} from '@firebase/firestore';
import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';
import Constants from 'expo-constants';
import { Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

class NotificationService {
  static notificationChannels = {
    academic: {
      name: 'Academic',
      importance: Notifications.AndroidImportance.HIGH,
      vibrationPattern: [0, 250, 250, 250],
      lightColor: '#4CAF50',
    },
    attendance: {
      name: 'Attendance',
      importance: Notifications.AndroidImportance.HIGH,
      vibrationPattern: [0, 250, 250, 250],
      lightColor: '#2196F3',
    },
    behavioral: {
      name: 'Behavioral',
      importance: Notifications.AndroidImportance.HIGH,
      vibrationPattern: [0, 250, 250, 250],
      lightColor: '#FF9800',
    },
    event: {
      name: 'Events',
      importance: Notifications.AndroidImportance.DEFAULT,
      vibrationPattern: [0, 250, 250, 250],
      lightColor: '#9C27B0',
    },
    message: {
      name: 'Messages',
      importance: Notifications.AndroidImportance.DEFAULT,
      vibrationPattern: [0, 250, 250, 250],
      lightColor: '#00BCD4',
    },
    default: {
      name: 'General',
      importance: Notifications.AndroidImportance.DEFAULT,
      vibrationPattern: [0, 250, 250, 250],
      lightColor: '#FF231F7C',
    },
  };

  static async initialize() {
    try {
      if (!Device.isDevice) {
        console.log('Must use physical device for Push Notifications');
        return;
      }

      // Check if previously granted
      const storedPermission = await AsyncStorage.getItem('notificationPermission');
      if (storedPermission === 'denied') {
        console.log('Notifications previously denied by user');
        return;
      }

      // Request permission
      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;
      
      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync({
          ios: {
            allowAlert: true,
            allowBadge: true,
            allowSound: true,
            allowAnnouncements: true,
          },
        });
        finalStatus = status;
        await AsyncStorage.setItem('notificationPermission', status);
      }
      
      if (finalStatus !== 'granted') {
        console.log('Failed to get push token for push notification!');
        return;
      }

      // Get or generate push token
      let token;
      try {
        token = (await Notifications.getExpoPushTokenAsync({
          projectId: Constants.expoConfig.extra.eas.projectId,
        })).data;
      } catch (error) {
        console.error('Error getting push token:', error);
        return;
      }

      // Save token to user document
      if (auth.currentUser) {
        const userRef = doc(db, 'users', auth.currentUser.uid);
        await updateDoc(userRef, {
          expoPushToken: token,
          lastTokenUpdate: new Date().toISOString(),
          devicePlatform: Platform.OS,
          deviceModel: Device.modelName,
        });
      }

      // Configure notification handler
      Notifications.setNotificationHandler({
        handleNotification: async (notification) => {
          const channel = notification.request.content.data?.channel || 'default';
          return {
            shouldShowAlert: true,
            shouldPlaySound: true,
            shouldSetBadge: true,
            priority: NotificationService.notificationChannels[channel].importance,
          };
        },
      });

      // Set up notification channels for Android
      if (Platform.OS === 'android') {
        await Promise.all(
          Object.entries(NotificationService.notificationChannels).map(
            ([id, channel]) =>
              Notifications.setNotificationChannelAsync(id, {
                name: channel.name,
                importance: channel.importance,
                vibrationPattern: channel.vibrationPattern,
                lightColor: channel.lightColor,
                enableVibrate: true,
                enableLights: true,
              })
          )
        );
      }

      // Set up notification listeners
      const subscription = Notifications.addNotificationReceivedListener(
        this.handleNotificationReceived
      );
      const responseSubscription = Notifications.addNotificationResponseReceivedListener(
        this.handleNotificationResponse
      );

      return () => {
        subscription.remove();
        responseSubscription.remove();
      };
    } catch (error) {
      console.error('Error initializing notifications:', error);
    }
  }

  static async sendNotification(userId, notification, retryCount = 3) {
    try {
      // Get user's push token
      const userRef = doc(db, 'users', userId);
      const userDoc = await getDoc(userRef);
      const userData = userDoc.data();
      const pushToken = userData?.expoPushToken;

      if (!pushToken) {
        console.log('No push token found for user:', userId);
        return;
      }

      // Save notification to database
      const notificationData = {
        userId,
        title: notification.title,
        body: notification.body,
        data: notification.data || {},
        type: notification.type || 'general',
        priority: notification.priority || 'normal',
        status: 'unread',
        timestamp: new Date().toISOString(),
        channel: notification.type || 'default',
        retryCount: 0,
        devicePlatform: userData.devicePlatform,
      };

      const notificationRef = await addDoc(
        collection(db, 'notifications'),
        notificationData
      );

      // Send push notification with retry mechanism
      for (let attempt = 0; attempt < retryCount; attempt++) {
        try {
          await Notifications.scheduleNotificationAsync({
            identifier: notificationRef.id,
            content: {
              title: notification.title,
              body: notification.body,
              data: {
                ...notification.data,
                notificationId: notificationRef.id,
                channel: notification.type || 'default',
              },
              sound: true,
              badge: 1,
              priority: Notifications.AndroidNotificationPriority.HIGH,
            },
            trigger: null,
          });
          
          // Update notification status
          await updateDoc(notificationRef, {
            status: 'sent',
            sentAt: new Date().toISOString(),
          });
          
          return;
        } catch (error) {
          console.error(`Attempt ${attempt + 1} failed:`, error);
          
          // Update retry count
          await updateDoc(notificationRef, {
            retryCount: attempt + 1,
            lastError: error.message,
            lastErrorAt: new Date().toISOString(),
          });

          if (attempt === retryCount - 1) {
            throw error;
          }
          
          // Wait before retrying
          await new Promise(resolve => setTimeout(resolve, 1000 * (attempt + 1)));
        }
      }
    } catch (error) {
      console.error('Error sending notification:', error);
      throw error;
    }
  }

  static async handleNotificationReceived(notification) {
    try {
      const { notificationId } = notification.request.content.data;
      if (notificationId) {
        const notificationRef = doc(db, 'notifications', notificationId);
        await updateDoc(notificationRef, {
          receivedAt: new Date().toISOString(),
          status: 'delivered',
        });
      }
    } catch (error) {
      console.error('Error handling received notification:', error);
    }
  }

  static async handleNotificationResponse(response) {
    try {
      const { notificationId } = response.notification.request.content.data;
      if (notificationId) {
        const notificationRef = doc(db, 'notifications', notificationId);
        await updateDoc(notificationRef, {
          openedAt: new Date().toISOString(),
          status: 'opened',
        });
      }
    } catch (error) {
      console.error('Error handling notification response:', error);
    }
  }

  static async markAsRead(notificationId) {
    try {
      const notificationRef = doc(db, 'notifications', notificationId);
      await updateDoc(notificationRef, {
        status: 'read',
        readAt: new Date().toISOString(),
      });
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  }

  static async getNotifications(limitCount = 50) {
    try {
      const notificationsRef = collection(db, 'notifications');
      const q = query(
        notificationsRef,
        where('userId', '==', auth.currentUser.uid),
        orderBy('timestamp', 'desc'),
        limit(limitCount)
      );

      const querySnapshot = await getDocs(q);
      const notifications = [];
      querySnapshot.forEach(doc => {
        notifications.push({ id: doc.id, ...doc.data() });
      });

      return notifications;
    } catch (error) {
      console.error('Error getting notifications:', error);
      return [];
    }
  }

  static async getUnreadCount() {
    try {
      const notificationsRef = collection(db, 'notifications');
      const q = query(
        notificationsRef,
        where('userId', '==', auth.currentUser.uid),
        where('status', '==', 'unread')
      );

      const querySnapshot = await getDocs(q);
      return querySnapshot.size;
    } catch (error) {
      console.error('Error getting unread count:', error);
      return 0;
    }
  }

  static async clearAll() {
    try {
      const notificationsRef = collection(db, 'notifications');
      const q = query(
        notificationsRef,
        where('userId', '==', auth.currentUser.uid),
        where('status', '==', 'unread')
      );
      
      const querySnapshot = await getDocs(q);
      const batch = db.batch();
      
      querySnapshot.forEach((doc) => {
        batch.update(doc.ref, {
          status: 'read',
          readAt: new Date().toISOString(),
        });
      });
      
      await batch.commit();
    } catch (error) {
      console.error('Error clearing notifications:', error);
    }
  }

  static async sendBulkNotifications(userIds, notification) {
    try {
      const notifications = userIds.map(userId => ({
        userId,
        title: notification.title,
        body: notification.body,
        data: notification.data || {},
        type: notification.type || 'general',
        priority: notification.priority || 'normal',
        status: 'unread',
        timestamp: new Date().toISOString(),
        channel: notification.type || 'default',
        retryCount: 0,
        devicePlatform: Platform.OS,
      }));

      // Save notifications to database
      await Promise.all(
        notifications.map(notification =>
          addDoc(collection(db, 'notifications'), notification)
        )
      );

      // Get users' push tokens
      const usersSnapshot = await getDocs(
        query(collection(db, 'users'), where('__name__', 'in', userIds))
      );

      const pushTokens = [];
      usersSnapshot.forEach(doc => {
        if (doc.data().expoPushToken) {
          pushTokens.push(doc.data().expoPushToken);
        }
      });

      // Send push notifications
      await Promise.all(
        pushTokens.map(token =>
          Notifications.scheduleNotificationAsync({
            content: {
              title: notification.title,
              body: notification.body,
              data: {
                ...notification.data,
                channel: notification.type || 'default',
              },
              sound: true,
              badge: 1,
              priority: Notifications.AndroidNotificationPriority.HIGH,
            },
            trigger: null,
          })
        )
      );
    } catch (error) {
      console.error('Error sending bulk notifications:', error);
    }
  }
}

export default NotificationService;
