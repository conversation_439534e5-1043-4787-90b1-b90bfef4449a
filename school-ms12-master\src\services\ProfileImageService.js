import { auth } from '../config/firebase';
import { updateProfile } from 'firebase/auth';
import { doc, updateDoc } from 'firebase/firestore';
import { db } from '../config/firebase';
import CloudinaryService from './CloudinaryService';

/**
 * Service for handling profile image uploads and updates
 * This service provides a consistent way to handle profile images across the app
 */
class ProfileImageService {
  /**
   * Upload a profile image to Cloudinary and update user profile
   * 
   * @param {string} uri - Local URI of the image to upload
   * @param {Object} user - Firebase user object
   * @param {string} role - User role (admin, teacher, student, parent)
   * @returns {Promise<string>} - URL of the uploaded image
   */
  static async uploadProfileImage(uri, user, role) {
    try {
      if (!uri) return null;
      if (!user) throw new Error('User is required for profile image upload');
      
      // Skip upload if the URI is already a URL (already uploaded)
      if (uri.startsWith('http')) return uri;

      // Upload image to Cloudinary
      const result = await CloudinaryService.uploadFromUri(uri, {
        folder: 'profile_pictures',
        tags: ['profile', role, user.uid],
        publicId: `profile_${user.uid}`,
        overwrite: true, // Overwrite existing image with same public ID
      });

      if (!result || !result.url) {
        throw new Error('Failed to upload image to Cloudinary');
      }

      return result.url;
    } catch (error) {
      console.error('Error uploading profile image:', error);
      throw error;
    }
  }

  /**
   * Update user profile with new image URL
   * This updates both Firebase Auth and Firestore
   * 
   * @param {string} imageUrl - URL of the uploaded image
   * @param {Object} user - Firebase user object
   * @param {string} role - User role (admin, teacher, student, parent)
   * @returns {Promise<void>}
   */
  static async updateProfileImage(imageUrl, user, role) {
    try {
      if (!imageUrl) return;
      if (!user) throw new Error('User is required for profile image update');

      // Update Firebase Auth profile
      await updateProfile(user, {
        photoURL: imageUrl,
      });

      // Update Firestore user document
      const userRef = doc(db, 'users', user.uid);
      await updateDoc(userRef, {
        photoURL: imageUrl,
        updatedAt: new Date().toISOString(),
      });

      // Update role-specific document if it exists
      if (['admin', 'teacher', 'student', 'parent'].includes(role)) {
        const roleRef = doc(db, `${role}s`, user.uid);
        await updateDoc(roleRef, {
          photoURL: imageUrl,
          updatedAt: new Date().toISOString(),
        });
      }
    } catch (error) {
      console.error('Error updating profile image in user profile:', error);
      throw error;
    }
  }

  /**
   * Upload and update profile image in one operation
   * 
   * @param {string} uri - Local URI of the image to upload
   * @param {Object} user - Firebase user object
   * @param {string} role - User role (admin, teacher, student, parent)
   * @returns {Promise<string>} - URL of the uploaded image
   */
  static async uploadAndUpdateProfileImage(uri, user, role) {
    try {
      // Upload image to Cloudinary
      const imageUrl = await this.uploadProfileImage(uri, user, role);
      
      if (imageUrl) {
        // Update user profile with new image URL
        await this.updateProfileImage(imageUrl, user, role);
      }
      
      return imageUrl;
    } catch (error) {
      console.error('Error in upload and update profile image:', error);
      throw error;
    }
  }
}

export default ProfileImageService;
