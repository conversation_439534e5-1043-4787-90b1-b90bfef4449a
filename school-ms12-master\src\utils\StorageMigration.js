import { storage } from '../config/firebase';
import { ref, getDownloadURL, listAll } from 'firebase/storage';
import CloudinaryService from '../services/CloudinaryService';

/**
 * Utility functions for migrating from Firebase Storage to Cloudinary
 */
class StorageMigration {
  /**
   * Migrate a single file from Firebase Storage to Cloudinary
   * 
   * @param {string} firebasePath - Path to the file in Firebase Storage
   * @param {Object} options - Migration options
   * @param {string} options.folder - Cloudinary folder to store the file in
   * @param {Array<string>} options.tags - Tags to assign to the file
   * @param {Function} options.onProgress - Progress callback function
   * @returns {Promise<Object>} - Migration result with publicId and url
   */
  static async migrateFile(firebasePath, options = {}) {
    try {
      console.log(`Migrating file from Firebase Storage: ${firebasePath}`);
      
      // Get download URL from Firebase Storage
      const fileRef = ref(storage, firebasePath);
      const downloadUrl = await getDownloadURL(fileRef);
      
      // Upload to Cloudinary
      const result = await CloudinaryService.uploadFile(downloadUrl, {
        ...options,
        publicId: this.generatePublicId(firebasePath)
      });
      
      return {
        publicId: result.public_id,
        url: result.secure_url,
        originalPath: firebasePath
      };
    } catch (error) {
      console.error(`Error migrating file ${firebasePath}:`, error);
      throw error;
    }
  }
  
  /**
   * Migrate all files in a Firebase Storage directory to Cloudinary
   * 
   * @param {string} firebaseDir - Directory path in Firebase Storage
   * @param {Object} options - Migration options
   * @param {string} options.folder - Cloudinary folder to store the files in
   * @param {Array<string>} options.tags - Tags to assign to the files
   * @param {Function} options.onProgress - Progress callback function
   * @param {Function} options.onFileComplete - Callback function called after each file is migrated
   * @returns {Promise<Array<Object>>} - Migration results
   */
  static async migrateDirectory(firebaseDir, options = {}) {
    try {
      console.log(`Migrating directory from Firebase Storage: ${firebaseDir}`);
      
      // List all files in the directory
      const dirRef = ref(storage, firebaseDir);
      const listResult = await listAll(dirRef);
      
      const results = [];
      let completed = 0;
      
      // Process files
      for (const fileRef of listResult.items) {
        try {
          // Migrate the file
          const result = await this.migrateFile(fileRef.fullPath, {
            ...options,
            folder: options.folder || `migrated/${firebaseDir}`
          });
          
          results.push(result);
          
          // Update progress
          completed++;
          if (options.onProgress) {
            options.onProgress(completed, listResult.items.length);
          }
          
          // Call onFileComplete callback
          if (options.onFileComplete) {
            options.onFileComplete(result);
          }
        } catch (fileError) {
          console.error(`Error migrating file ${fileRef.fullPath}:`, fileError);
          results.push({
            originalPath: fileRef.fullPath,
            error: fileError.message
          });
        }
      }
      
      // Process subdirectories recursively
      for (const prefixRef of listResult.prefixes) {
        try {
          const subResults = await this.migrateDirectory(prefixRef.fullPath, {
            ...options,
            folder: options.folder ? `${options.folder}/${prefixRef.name}` : `migrated/${prefixRef.fullPath}`
          });
          
          results.push(...subResults);
        } catch (dirError) {
          console.error(`Error migrating directory ${prefixRef.fullPath}:`, dirError);
        }
      }
      
      return results;
    } catch (error) {
      console.error(`Error migrating directory ${firebaseDir}:`, error);
      throw error;
    }
  }
  
  /**
   * Generate a Cloudinary public ID from a Firebase Storage path
   * 
   * @param {string} firebasePath - Path to the file in Firebase Storage
   * @returns {string} - Cloudinary public ID
   */
  static generatePublicId(firebasePath) {
    // Remove file extension
    const pathWithoutExt = firebasePath.replace(/\.[^/.]+$/, '');
    
    // Replace special characters and spaces
    return pathWithoutExt
      .replace(/[^a-zA-Z0-9_\-/]/g, '_') // Replace special chars with underscore
      .replace(/\//g, '_'); // Replace slashes with underscores
  }
  
  /**
   * Update a document in Firestore to use Cloudinary instead of Firebase Storage
   * 
   * @param {Object} docRef - Firestore document reference
   * @param {string} fieldName - Field name containing the Firebase Storage URL
   * @param {string} cloudinaryPublicId - Cloudinary public ID
   * @returns {Promise<void>}
   */
  static async updateDocumentField(docRef, fieldName, cloudinaryPublicId) {
    try {
      await docRef.update({
        [fieldName]: cloudinaryPublicId,
        [`${fieldName}_migrated`]: true
      });
      
      console.log(`Updated document ${docRef.id} field ${fieldName} to use Cloudinary`);
    } catch (error) {
      console.error(`Error updating document ${docRef.id}:`, error);
      throw error;
    }
  }
}

export default StorageMigration;
