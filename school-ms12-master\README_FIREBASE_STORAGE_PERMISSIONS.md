# Firebase Storage Permissions Fix

This document provides instructions for fixing the "Missing or insufficient permissions" error that occurs when trying to upload files to Firebase Storage or update profiles.

## Problem

The error occurs because the Firebase Storage security rules are not properly configured to allow access for different user roles (teachers, students, parents).

## Solution

We've implemented several fixes to address this issue:

1. Created Firebase Storage security rules that properly allow access to storage buckets
2. Updated the Firebase configuration to include Storage rules
3. Created a script to deploy both Firestore and Storage rules

## Implementation Steps

### 1. Deploy Firebase Security Rules

The `storage.rules` file contains the security rules for Firebase Storage. To deploy these rules along with the Firestore rules:

```bash
# For Windows users
deploy-firebase-rules.bat

# For macOS/Linux users
node deploy-firebase-rules.js
```

### 2. Verify the Changes

After deploying the security rules, the "Missing or insufficient permissions" error should be resolved. You can verify this by:

1. Logging in as a teacher, student, or parent
2. Navigating to the profile page
3. Updating your profile information and photo

## Security Rules Explanation

The security rules in `storage.rules` implement the following permissions:

- **All authenticated users** can read public resources
- **Users** can read and write their own profile images
- **Teachers** can read and write their own files
- **Students** can read and write their own files
- **Parents** can read and write their own files
- **Admins** have full access to all files

## Troubleshooting

If you still encounter permission issues after deploying the rules:

1. Make sure you are properly authenticated in the application
2. Check that your user account has the correct role assigned (admin, teacher, student, or parent)
3. Verify that you're using the correct storage paths when uploading files
4. Check the Firebase console for any error messages

## Migration to Cloudinary

Note that the application is in the process of migrating from Firebase Storage to Cloudinary for file storage. Some components have already been migrated, while others still use Firebase Storage. The security rules implemented here will ensure that both systems work correctly during the transition period.
