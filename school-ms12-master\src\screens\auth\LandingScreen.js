import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  StyleSheet,
  Dimensions,
  ScrollView,
  ImageBackground,
  Animated,
  TouchableOpacity,
  StatusBar,
  Text
} from 'react-native';
import { Button, Surface, useTheme, Menu, Card, Title, Paragraph, Divider } from 'react-native-paper';
import { LinearGradient } from 'expo-linear-gradient';
import { useLanguage } from '../../context/LanguageContext';
import { MaterialIcons, FontAwesome5 } from '@expo/vector-icons';

const { width, height } = Dimensions.get('window');

const LandingScreen = ({ navigation }) => {
  const { language, setLanguage, translate } = useLanguage();

  // State variables
  const [menuVisible, setMenuVisible] = useState(false);
  const [activeFeatureIndex, setActiveFeatureIndex] = useState(0);

  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;
  const scaleAnim = useRef(new Animated.Value(0.9)).current;
  const pulseAnim = useRef(new Animated.Value(1)).current;
  const rotateAnim = useRef(new Animated.Value(0)).current;

  // Reference to the horizontal ScrollView
  const scrollViewRef = useRef(null);

  // Auto-rotate features and auto-scroll
  useEffect(() => {
    const interval = setInterval(() => {
      const nextIndex = (activeFeatureIndex + 1) % features.length;
      setActiveFeatureIndex(nextIndex);

      // Auto-scroll to the next feature card
      if (scrollViewRef.current) {
        scrollViewRef.current.scrollTo({
          x: nextIndex * (width * 0.65 + 20), // Card width + margin
          animated: true
        });
      }
    }, 2000); // Change every 2 seconds

    return () => clearInterval(interval);
  }, [activeFeatureIndex]);

  // Run animations when component mounts
  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.loop(
        Animated.sequence([
          Animated.timing(pulseAnim, {
            toValue: 1.1,
            duration: 1000,
            useNativeDriver: true
          }),
          Animated.timing(pulseAnim, {
            toValue: 1,
            duration: 1000,
            useNativeDriver: true
          })
        ])
      ),
      Animated.loop(
        Animated.sequence([
          Animated.timing(rotateAnim, {
            toValue: 1,
            duration: 2000,
            useNativeDriver: true,
          }),
          Animated.timing(rotateAnim, {
            toValue: 0,
            duration: 2000,
            useNativeDriver: true,
          }),
        ])
      ),
    ]).start();
  }, []);

  // Rotate interpolation for the logo
  const spin = rotateAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '10deg'],
  });

  // Button press animation
  const handleButtonPress = () => {
    Animated.sequence([
      Animated.timing(scaleAnim, {
        toValue: 0.95,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      }),
    ]).start(() => {
      navigation.navigate('Login');
    });
  };

  // Keep the original logo component but enhance with new animations
  const SchoolLogo = () => (
    <View style={styles.logoContainer}>
      <Animated.View
        style={[
          styles.logoOuter,
          {
            transform: [
              { scale: pulseAnim },
              { rotate: spin }
            ]
          }
        ]}
      >
        <View style={styles.logoInner}>
          <Text style={styles.logoText}>
            ASS
          </Text>
          <FontAwesome5 name="book-open" size={24} color="#fff" style={styles.logoIcon} />
        </View>
      </Animated.View>
    </View>
  );



  // Features data with enhanced styling and translations
  const getFeatureText = (lang) => {
    const featureTexts = {
      en: {
        feature1: {
          title: "Quality Education",
          description: "We provide high-quality education with a comprehensive curriculum designed to meet international standards and prepare students for future success."
        },
        feature2: {
          title: "Experienced Teachers",
          description: "Our dedicated team of experienced educators is committed to nurturing each student's potential through personalized attention and innovative teaching methods."
        },
        feature3: {
          title: "Modern Facilities",
          description: "Our campus features state-of-the-art classrooms, well-equipped laboratories, a comprehensive library, and modern sports facilities to enhance the learning experience."
        },
        feature4: {
          title: "Holistic Development",
          description: "We focus on developing the whole child - intellectually, physically, socially, and emotionally - through a balanced approach to education and extracurricular activities."
        }
      },
      am: {
        feature1: {
          title: "ጥራት ያለው ትምህርት",
          description: "ዓለም አቀፍ ደረጃዎችን የሚያሟላ እና ተማሪዎችን ለወደፊት ስኬት የሚያዘጋጅ ሁለንተናዊ የትምህርት ፕሮግራም እናቀርባለን።"
        },
        feature2: {
          title: "ልምድ ያላቸው መምህራን",
          description: "የተወፈዩ የልምድ አስተማሪዎች ቡድናችን በግላዊ ትኩረት እና በአዳዲስ የማስተማሪያ ዘዴዎች አማካኝነት የእያንዳንዱን ተማሪ እምቅ ችሎታ ለማዳበር ቁርጠኛ ነው።"
        },
        feature3: {
          title: "ዘመናዊ መገልገያዎች",
          description: "የእኛ ካምፓስ ዘመናዊ የትምህርት ክፍሎችን፣ በደንብ የተደራጁ ላቦራቶሪዎችን፣ ሁለንተናዊ ቤተ መጽሐፍትን እና የትምህርት ልምድን ለማሻሻል ዘመናዊ የስፖርት መገልገያዎችን ያካትታል።"
        },
        feature4: {
          title: "ሁለንተናዊ እድገት",
          description: "በትምህርት እና በተጨማሪ ትምህርት ተግባራት ሚዛናዊ አቀራረብ አማካኝነት ሙሉውን ልጅ - አእምሯዊ፣ አካላዊ፣ ማህበራዊ እና ስሜታዊ - ለማዳበር ትኩረት እናደርጋለን።"
        }
      },
      om: {
        feature1: {
          title: "Barnoota Qulqullina Qabu",
          description: "Barnoota qulqullina qabu kan sirnaa barnootaa guutuu ta'e kan sadarkaa addunyaa guutu fi barattoota milkaa'ina gara fuulduraatiif qopheessu ni dhiyeessina."
        },
        feature2: {
          title: "Barsiisota Muuxannoo Qaban",
          description: "Gareen keenya barsiisota muuxannoo qaban kan dandeettii barataa tokkoo tokkoo xiyyeeffannaa dhuunfaa fi maloota barsiisuu haaraa fayyadamuun guddisuuf kutannoo qaban."
        },
        feature3: {
          title: "Meeshaalee Ammayyaa",
          description: "Kampasiin keenya kutaalee barnootaa ammayyaa, labiraatoriiwwan sirriitti qophaa'an, mana kitaabaa guutuu, fi meeshaalee ispoortii ammayyaa kan muuxannoo barachuu fooyyessan of keessaa qaba."
        },
        feature4: {
          title: "Guddina Waliigalaa",
          description: "Daa'ima guutuu - sammuu, qaamaan, hawaasummaa, fi miira - karaa barnootaa fi gochaalee barnootaa dabalataa irratti xiyyeeffannoo ni goona."
        }
      }
    };

    return featureTexts[lang] || featureTexts.en;
  };

  const featureTexts = getFeatureText(language);

  const features = [
    {
      key: 'feature1',
      title: featureTexts.feature1.title,
      description: featureTexts.feature1.description,
      icon: <FontAwesome5 name="graduation-cap" size={28} color="#FFFFFF" />,
      color: '#4CAF50'
    },
    {
      key: 'feature2',
      title: featureTexts.feature2.title,
      description: featureTexts.feature2.description,
      icon: <FontAwesome5 name="chalkboard-teacher" size={28} color="#FFFFFF" />,
      color: '#FF9800'
    },
    {
      key: 'feature3',
      title: featureTexts.feature3.title,
      description: featureTexts.feature3.description,
      icon: <MaterialIcons name="computer" size={28} color="#FFFFFF" />,
      color: '#9C27B0'
    },
    {
      key: 'feature4',
      title: featureTexts.feature4.title,
      description: featureTexts.feature4.description,
      icon: <FontAwesome5 name="users" size={28} color="#FFFFFF" />,
      color: '#2196F3'
    }
  ];

  const handleLanguageChange = async (langCode) => {
    try {
      console.log('Changing language to:', langCode);
      await setLanguage(langCode);
      console.log('Language changed successfully to:', langCode);
      setMenuVisible(false);
    } catch (error) {
      console.error('Failed to change language:', error);
      // Try a simpler approach if the async method fails
      try {
        console.log('Trying alternative language change method');
        setLanguage(langCode);
        setMenuVisible(false);
      } catch (fallbackError) {
        console.error('Alternative language change also failed:', fallbackError);
      }
    }
  };

  return (
    <View style={styles.container}>
      <StatusBar translucent backgroundColor="transparent" barStyle="light-content" />

      {/* Fixed Header with Gradient */}
      <LinearGradient
        colors={['#1976D2', '#0D47A1']}
        style={styles.headerGradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        {/* School Logo - Fixed in the header */}
        <SchoolLogo />

        {/* Title and Subtitle - Fixed in the header */}
        <Animated.Text
          style={[
            styles.title,
            {
              opacity: fadeAnim,
              transform: [{ translateY: slideAnim }]
            }
          ]}
        >
          {translate('auth.landing.schoolName')}
        </Animated.Text>

        <Animated.Text
          style={[
            styles.subtitle,
            {
              opacity: fadeAnim
            }
          ]}
        >
          {language === 'en' ? 'Excellence in Education' :
           language === 'am' ? 'በትምህርት ልቀት' :
           'Barnootaan Caaluu'}
        </Animated.Text>
      </LinearGradient>

      {/* Scrollable Content */}
      <ScrollView
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Feature Cards */}
        <Animated.View
          style={[
            styles.featureContainer,
            {
              opacity: fadeAnim,
              transform: [{ translateY: slideAnim }]
            }
          ]}
        >
          <Text style={styles.featuresTitle}>
            {language === 'en' ? 'Why Choose Us' :
             language === 'am' ? 'እኛን ለምን ይመርጣሉ' :
             'Maaliif Nu Filattan'}
          </Text>

          <ScrollView
            ref={scrollViewRef}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.featuresScrollContainer}
            pagingEnabled={false}
            decelerationRate="fast"
            snapToInterval={width * 0.65 + 20} // Card width + margin
            snapToAlignment="center"
            onMomentumScrollEnd={(event) => {
              // Update active index when user manually scrolls
              const newIndex = Math.round(
                event.nativeEvent.contentOffset.x / (width * 0.65 + 20)
              );
              setActiveFeatureIndex(Math.min(newIndex, features.length - 1));
            }}
          >
            {features.map((feature, index) => (
              <Animated.View
                key={index}
                style={[
                  styles.featureCardWrapper,
                  {
                    transform: [{
                      scale: index === activeFeatureIndex ? 1 : 0.9
                    }],
                    opacity: index === activeFeatureIndex ? 1 : 0.7
                  }
                ]}
              >
                <Card
                  style={[
                    styles.featureCard,
                    { borderLeftColor: feature.color }
                  ]}
                >
                  <Card.Content style={styles.featureCardContent}>
                    <View
                      style={[
                        styles.featureIconContainer,
                        { backgroundColor: feature.color }
                      ]}
                    >
                      {feature.icon}
                    </View>
                    <Title style={styles.featureTitle}>
                      {feature.title}
                    </Title>
                    <Paragraph style={styles.featureDescription}>
                      {feature.description}
                    </Paragraph>
                  </Card.Content>
                </Card>
              </Animated.View>
            ))}
          </ScrollView>

          {/* Feature Indicators */}
          <View style={styles.indicatorContainer}>
            {features.map((feature, index) => (
              <TouchableOpacity
                key={index}
                onPress={() => setActiveFeatureIndex(index)}
              >
                <View
                  style={[
                    styles.indicator,
                    { backgroundColor: index === activeFeatureIndex ? feature.color : '#B0BEC5' },
                    index === activeFeatureIndex && styles.activeIndicator,
                  ]}
                />
              </TouchableOpacity>
            ))}
          </View>
        </Animated.View>

        {/* Get Started Button */}
        <Animated.View
          style={[
            styles.buttonContainer,
            {
              opacity: fadeAnim,
              transform: [
                { translateY: slideAnim },
                { scale: scaleAnim }
              ],
            },
          ]}
        >
          <TouchableOpacity
            activeOpacity={0.8}
            onPress={handleButtonPress}
            style={styles.buttonTouchable}
          >
            <LinearGradient
              colors={['#64B5F6', '#2196F3']}
              style={styles.buttonGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
            >
              <Text style={styles.buttonText}>
                {language === 'en' ? 'Get Started' :
                 language === 'am' ? 'ይጀምሩ' :
                 'Jalqabi'}
              </Text>
            </LinearGradient>
          </TouchableOpacity>
        </Animated.View>

        {/* Language Selector */}
        <Animated.View
          style={[
            styles.languageContainer,
            {
              opacity: fadeAnim,
              transform: [{ translateY: slideAnim }]
            }
          ]}
        >
          <Menu
            visible={menuVisible}
            onDismiss={() => setMenuVisible(false)}
            anchor={
              <Button
                mode="outlined"
                onPress={() => setMenuVisible(true)}
                icon="translate"
                style={styles.languageDropdownButton}
              >
                {language === 'en' ? 'English' : language === 'am' ? 'አማርኛ' : 'Afaan Oromoo'}
              </Button>
            }
          >
            <Menu.Item
              key="en"
              onPress={() => handleLanguageChange('en')}
              title="English"
              disabled={language === 'en'}
            />
            <Menu.Item
              key="am"
              onPress={() => handleLanguageChange('am')}
              title="አማርኛ"
              disabled={language === 'am'}
            />
            <Menu.Item
              key="om"
              onPress={() => handleLanguageChange('om')}
              title="Afaan Oromoo"
              disabled={language === 'om'}
            />
          </Menu>
        </Animated.View>

        {/* Footer */}
        <Animated.View
          style={[
            styles.footer,
            {
              opacity: fadeAnim,
              transform: [{ translateY: slideAnim }]
            }
          ]}
        >
          <View style={styles.footerDivider} />
          <Text style={styles.copyright}>
            {translate('auth.landing.footer.copyright')}
          </Text>
          <Text style={styles.motto}>
            {translate('auth.landing.motto')}
          </Text>
          <Text style={styles.footerContact}>
            {translate('auth.landing.footer.contactInfo')}: +251 123 456 789
          </Text>
          <Text style={styles.footerContact}>
            <EMAIL>
          </Text>
        </Animated.View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  headerGradient: {
    width: '100%',
    paddingTop: height * 0.05, // Account for status bar
    paddingBottom: 20,
    borderBottomLeftRadius: 30,
    borderBottomRightRadius: 30,
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 5,
    zIndex: 10,
  },
  scrollContent: {
    flexGrow: 1,
    paddingTop: 20,
    paddingBottom: 30,
    alignItems: 'center',
  },
  // Keep the original logo styles
  logoContainer: {
    marginBottom: 20,
    alignItems: 'center',
  },
  logoOuter: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: '#1a237e',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 4,
    borderColor: '#fff',
    elevation: 12,
  },
  logoInner: {
    width: 96,
    height: 96,
    borderRadius: 48,
    backgroundColor: '#303f9f',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#fff',
  },
  logoText: {
    fontSize: 30,
    fontWeight: 'bold',
    color: 'white',
    letterSpacing: 2,
    marginBottom: 10,
  },
  logoIcon: {
    position: 'absolute',
    bottom: 15,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
    textAlign: 'center',
    marginHorizontal: 20,
    marginBottom: 8,
    textShadowColor: 'rgba(0, 0, 0, 0.2)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  subtitle: {
    fontSize: 16,
    color: '#fff',
    textAlign: 'center',
    marginHorizontal: 30,
    marginBottom: 5,
    fontStyle: 'italic',
  },
  featureContainer: {
    width: '100%',
    alignItems: 'center',
    paddingHorizontal: 20,
    marginTop: 20,
    height: height * 0.45, // Fixed height for the feature container
  },
  featuresTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#263238',
    marginBottom: 15,
    textAlign: 'center',
    marginTop: 10,
  },
  featuresScrollContainer: {
    paddingBottom: 10,
  },
  featureCardWrapper: {
    padding: 5,
    marginHorizontal: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  featureCard: {
    width: width * 0.65,
    maxWidth: 280,
    borderRadius: 15,
    backgroundColor: '#fff',
    elevation: 6,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    marginBottom: 10,
    borderLeftWidth: 6,
    overflow: 'hidden',
  },
  featureCardContent: {
    padding: 12,
    paddingTop: 15,
    paddingBottom: 15,
    alignItems: 'center',
  },
  featureIconContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
  },
  featureTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
    color: '#263238',
  },
  featureDescription: {
    textAlign: 'center',
    color: '#546E7A',
    fontSize: 14,
    lineHeight: 20,
    maxHeight: 80, // Limit the height to keep cards compact
  },
  indicatorContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 15,
  },
  indicator: {
    width: 10,
    height: 10,
    borderRadius: 5,
    marginHorizontal: 5,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.1)',
  },
  activeIndicator: {
    width: 14,
    height: 14,
    borderRadius: 7,
    marginHorizontal: 5,
    transform: [{ translateY: -2 }],
  },
  buttonContainer: {
    marginTop: 35,
    width: '85%',
    alignItems: 'center',
  },
  buttonTouchable: {
    width: '100%',
    borderRadius: 30,
    overflow: 'hidden',
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 5,
    marginBottom: 10,
  },
  buttonGradient: {
    paddingVertical: 16,
    alignItems: 'center',
    borderRadius: 30,
  },
  buttonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
    letterSpacing: 1,
  },
  languageContainer: {
    marginTop: 35,
    alignItems: 'center',
    width: '100%',
    zIndex: 1000,
  },
  languageDropdownButton: {
    borderColor: '#1976D2',
    borderRadius: 20,
    borderWidth: 1.5,
    minWidth: 150,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  footer: {
    width: '100%',
    alignItems: 'center',
    marginTop: 30,
    paddingHorizontal: 20,
  },
  footerDivider: {
    width: '80%',
    height: 1,
    backgroundColor: '#E0E0E0',
    marginBottom: 15,
  },
  copyright: {
    fontSize: 12,
    color: '#78909C',
    textAlign: 'center',
    marginBottom: 5,
  },
  motto: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#455A64',
    textAlign: 'center',
    letterSpacing: 0.5,
    marginBottom: 10,
  },
  footerContact: {
    fontSize: 12,
    color: '#78909C',
    textAlign: 'center',
    marginBottom: 3,
  },
});

export default LandingScreen;
