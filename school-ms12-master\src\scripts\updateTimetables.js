const { initializeApp } = require('firebase/app');
const { getFirestore, collection, getDocs, updateDoc, doc } = require('firebase/firestore');

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyBHkGAhm80xBIECyaHbZ_DbeNzu3jnVuDg",
  authDomain: "schoolmn-16cbc.firebaseapp.com",
  projectId: "schoolmn-16cbc",
  storageBucket: "schoolmn-16cbc.appspot.com",
  messagingSenderId: "999485613068",
  appId: "1:999485613068:web:e9c0c3e0a4c7f4e4c8b8b8"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

const updateTimetables = async () => {
  try {
    console.log('Updating timetable entries...');

    // Get all timetable entries
    const timetablesRef = collection(db, 'timetables');
    const snapshot = await getDocs(timetablesRef);

    console.log(`Found ${snapshot.size} timetable entries`);

    // Define the morning and afternoon time slots
    const morningTimeSlots = [
      { period: 1, start: '08:00', end: '08:40', session: 'morning' },
      { period: 2, start: '08:45', end: '09:25', session: 'morning' },
      { period: 3, start: '09:30', end: '10:10', session: 'morning' },
      { period: 4, start: '10:15', end: '10:55', session: 'morning' },
      { period: 5, start: '11:00', end: '11:40', session: 'morning' },
      { period: 6, start: '11:45', end: '12:25', session: 'morning' },
    ];

    const afternoonTimeSlots = [
      { period: 1, start: '12:30', end: '13:10', session: 'afternoon' },
      { period: 2, start: '13:15', end: '13:55', session: 'afternoon' },
      { period: 3, start: '14:00', end: '14:40', session: 'afternoon' },
      { period: 4, start: '14:45', end: '15:25', session: 'afternoon' },
      { period: 5, start: '15:30', end: '16:10', session: 'afternoon' },
      { period: 6, start: '16:15', end: '16:55', session: 'afternoon' },
    ];

    const allTimeSlots = [...morningTimeSlots, ...afternoonTimeSlots];

    // Update each entry to ensure it has the required fields
    let updatedCount = 0;
    for (const docSnapshot of snapshot.docs) {
      const timetableData = docSnapshot.data();
      const updates = {};

      // Add published field if missing
      if (timetableData.published === undefined) {
        updates.published = true;
      }

      // Ensure day field has proper capitalization
      if (timetableData.day) {
        const properDay = timetableData.day.charAt(0).toUpperCase() + timetableData.day.slice(1).toLowerCase();
        if (timetableData.day !== properDay) {
          updates.day = properDay;
        }
      } else {
        // If day is missing, set a default (Monday)
        updates.day = 'Monday';
      }

      // Ensure session field is correct
      if (!timetableData.session) {
        // Determine session based on start time
        const startHour = parseInt(timetableData.startTime?.split(':')[0] || '0');
        updates.session = startHour < 12 ? 'morning' : 'afternoon';
      }

      // Ensure periodNumber field is present
      if (!timetableData.periodNumber) {
        // Try to determine period number from start time
        const startTime = timetableData.startTime;
        if (startTime) {
          const matchingTimeSlot = allTimeSlots.find(slot => slot.start === startTime);
          if (matchingTimeSlot) {
            updates.periodNumber = matchingTimeSlot.period;
          } else {
            // Default to period 1 if can't determine
            updates.periodNumber = 1;
          }
        } else {
          // Default to period 1 if no start time
          updates.periodNumber = 1;
        }
      }

      // Ensure start and end times are present and match a standard time slot
      if (!timetableData.startTime || !timetableData.endTime) {
        // If missing, set default based on period and session
        const period = timetableData.periodNumber || 1;
        const session = timetableData.session || 'morning';

        const timeSlots = session === 'morning' ? morningTimeSlots : afternoonTimeSlots;
        const matchingSlot = timeSlots.find(slot => slot.period === period) || timeSlots[0];

        if (!timetableData.startTime) {
          updates.startTime = matchingSlot.start;
        }

        if (!timetableData.endTime) {
          updates.endTime = matchingSlot.end;
        }
      }

      // Ensure classId, sectionName, subjectId, teacherId fields are present
      if (!timetableData.classId) {
        console.log(`Warning: timetable ${docSnapshot.id} missing classId`);
      }

      if (!timetableData.sectionName) {
        console.log(`Warning: timetable ${docSnapshot.id} missing sectionName`);
      }

      if (!timetableData.subjectId) {
        console.log(`Warning: timetable ${docSnapshot.id} missing subjectId`);
      }

      if (!timetableData.teacherId) {
        console.log(`Warning: timetable ${docSnapshot.id} missing teacherId`);
      }

      // Ensure roomNumber field is present
      if (!timetableData.roomNumber) {
        updates.roomNumber = 'Default Room';
      }

      // Only update if there are changes to make
      if (Object.keys(updates).length > 0) {
        await updateDoc(doc(db, 'timetables', docSnapshot.id), updates);
        updatedCount++;
        console.log(`Updated timetable ${docSnapshot.id} with:`, updates);
      }
    }

    console.log(`Updated ${updatedCount} timetable entries`);
    console.log('Timetable update completed successfully');
  } catch (error) {
    console.error('Error updating timetables:', error);
  }
};

// Run the update function
updateTimetables()
  .then(() => {
    console.log('Script completed');
    process.exit(0);
  })
  .catch(error => {
    console.error('Script failed:', error);
    process.exit(1);
  });
