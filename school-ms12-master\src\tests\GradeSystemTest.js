import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView, Alert } from 'react-native';
import { Button, Text, Card, Title, Paragraph, ActivityIndicator, Divider } from 'react-native-paper';
import { db, auth } from '../config/firebase';
import {
  collection,
  query,
  where,
  getDocs,
  doc,
  getDoc,
  addDoc,
  updateDoc,
  Timestamp
} from 'firebase/firestore';
import NotificationService from '../services/NotificationService';

/**
 * Test component for the grade system
 * This component allows testing the grade system workflow:
 * 1. Teacher submits grades
 * 2. Admin approves grades
 * 3. Students and parents receive notifications
 */
const GradeSystemTest = () => {
  const [loading, setLoading] = useState(false);
  const [status, setStatus] = useState('');
  const [testResults, setTestResults] = useState([]);

  // Add a log message to the test results
  const log = (message) => {
    setTestResults(prev => [...prev, { time: new Date().toLocaleTimeString(), message }]);
  };

  // Test the teacher grade submission
  const testTeacherGradeSubmission = async () => {
    try {
      setLoading(true);
      log('Starting teacher grade submission test...');

      // Create a test class if it doesn't exist
      const classId = await createTestClassIfNeeded();
      log(`Using test class ID: ${classId}`);

      // Create a test submission
      const submissionId = await createTestSubmission(classId);
      log(`Created test submission with ID: ${submissionId}`);

      setStatus('Teacher grade submission test completed successfully');
      log('Teacher grade submission test completed successfully');
    } catch (error) {
      console.error('Error in teacher grade submission test:', error);
      setStatus(`Error: ${error.message}`);
      log(`Error: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  // Test the admin grade approval
  const testAdminGradeApproval = async () => {
    try {
      setLoading(true);
      log('Starting admin grade approval test...');

      // Find a pending submission
      const submissionsRef = collection(db, 'gradeSubmissions');
      const q = query(submissionsRef, where('status', '==', 'pending'));
      const querySnapshot = await getDocs(q);

      if (querySnapshot.empty) {
        throw new Error('No pending submissions found. Please run the teacher grade submission test first.');
      }

      const submission = querySnapshot.docs[0];
      const submissionData = submission.data();
      log(`Found pending submission: ${submission.id}`);

      // Approve the submission
      await updateDoc(doc(db, 'gradeSubmissions', submission.id), {
        status: 'approved',
        approvedAt: Timestamp.now(),
        approvedBy: auth.currentUser?.uid || 'test-admin'
      });
      log(`Updated submission status to approved`);

      // Update the class grades
      const classRef = doc(db, 'classes', submissionData.classId);
      const classDoc = await getDoc(classRef);

      if (classDoc.exists()) {
        const classData = classDoc.data();
        const updatedGrades = classData.grades || {};

        updatedGrades[submissionData.subject] = {
          lastUpdated: Timestamp.now(),
          teacherId: submissionData.teacherId || '',
          teacherName: submissionData.teacherName || 'Test Teacher',
          assessments: submissionData.assessments || [],
          studentScores: submissionData.students.map(student => ({
            studentId: student.uid,
            studentName: student.name,
            totalScore: student.totalScore,
            assessmentScores: student.assessmentScores || []
          }))
        };

        await updateDoc(classRef, {
          grades: updatedGrades
        });
        log(`Updated class grades for ${submissionData.subject}`);
      }

      // Update scores collection
      const scoresRef = collection(db, 'scores');
      const scoresQuery = query(scoresRef, where('submissionId', '==', submission.id));
      const scoresSnapshot = await getDocs(scoresQuery);

      if (!scoresSnapshot.empty) {
        for (const scoreDoc of scoresSnapshot.docs) {
          await updateDoc(doc(db, 'scores', scoreDoc.id), {
            status: 'approved',
            approvedAt: Timestamp.now(),
            approvedBy: auth.currentUser?.uid || 'test-admin'
          });
        }
        log(`Updated ${scoresSnapshot.size} scores to approved status`);
      } else {
        log('No scores found for this submission');
      }

      // Send notifications
      if (submissionData.teacherId) {
        await NotificationService.sendGradeApprovalNotification(
          submissionData.teacherId,
          submission.id,
          submissionData.classId,
          submissionData.className,
          submissionData.sectionName,
          submissionData.subject
        );
        log(`Sent approval notification to teacher ${submissionData.teacherId}`);
      }

      // Send notifications to students and parents
      for (const student of submissionData.students) {
        if (!student.uid) continue;

        // Create notification for student
        const studentNotificationRef = collection(db, 'notifications');
        await addDoc(studentNotificationRef, {
          title: 'Grades Published',
          body: `Your grades for ${submissionData.className}-${submissionData.sectionName} ${submissionData.subject} have been published. Your total score is ${student.totalScore}%.`,
          type: 'grade_published',
          role: 'student',
          userId: student.uid,
          read: false,
          data: {
            submissionId: submission.id,
            classId: submissionData.classId,
            className: submissionData.className,
            sectionName: submissionData.sectionName,
            subject: submissionData.subject,
            totalScore: student.totalScore,
            viewPath: 'StudentGrades',
            viewParams: {
              classId: submissionData.classId,
              subject: submissionData.subject
            }
          },
          createdAt: Timestamp.now()
        });
        log(`Created notification for student ${student.uid}`);

        // Find parent for this student
        const usersRef = collection(db, 'users');
        const parentsQuery = query(usersRef, where('role', '==', 'parent'), where('children', 'array-contains', student.uid));
        const parentsSnapshot = await getDocs(parentsQuery);

        if (!parentsSnapshot.empty) {
          for (const parentDoc of parentsSnapshot.docs) {
            const parentNotificationRef = collection(db, 'notifications');
            await addDoc(parentNotificationRef, {
              title: 'Child Grades Published',
              body: `Grades for ${student.name} in ${submissionData.className}-${submissionData.sectionName} ${submissionData.subject} have been published. Total score: ${student.totalScore}%.`,
              type: 'grade_published',
              role: 'parent',
              userId: parentDoc.id,
              read: false,
              data: {
                submissionId: submission.id,
                classId: submissionData.classId,
                className: submissionData.className,
                sectionName: submissionData.sectionName,
                subject: submissionData.subject,
                studentId: student.uid,
                studentName: student.name,
                totalScore: student.totalScore,
                viewPath: 'ParentChildGrades',
                viewParams: {
                  childId: student.uid,
                  classId: submissionData.classId,
                  subject: submissionData.subject
                }
              },
              createdAt: Timestamp.now()
            });
            log(`Created notification for parent ${parentDoc.id}`);
          }
        } else {
          log(`No parents found for student ${student.uid}`);
        }
      }

      setStatus('Admin grade approval test completed successfully');
      log('Admin grade approval test completed successfully');
    } catch (error) {
      console.error('Error in admin grade approval test:', error);
      setStatus(`Error: ${error.message}`);
      log(`Error: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  // Test the student grade view
  const testStudentGradeView = async () => {
    try {
      setLoading(true);
      log('Starting student grade view test...');

      // Find a student
      const usersRef = collection(db, 'users');
      const studentsQuery = query(usersRef, where('role', '==', 'student'));
      const studentsSnapshot = await getDocs(studentsQuery);

      if (studentsSnapshot.empty) {
        throw new Error('No students found in the database');
      }

      const student = studentsSnapshot.docs[0];
      const studentData = student.data();
      log(`Found student: ${studentData.name} (${student.id})`);

      // Check if the student has a class
      if (!studentData.classId) {
        throw new Error('Student is not assigned to a class');
      }

      // Get the class data
      const classRef = doc(db, 'classes', studentData.classId);
      const classDoc = await getDoc(classRef);

      if (!classDoc.exists()) {
        throw new Error('Class not found');
      }

      const classData = classDoc.data();
      log(`Found class: ${classData.name}`);

      // Check if the class has grades
      if (!classData.grades || Object.keys(classData.grades).length === 0) {
        throw new Error('No grades found for this class');
      }

      log(`Class has grades for subjects: ${Object.keys(classData.grades).join(', ')}`);

      // Check if the student has scores in the grades
      let hasScores = false;
      for (const subject in classData.grades) {
        const subjectGrade = classData.grades[subject];
        const studentScore = subjectGrade.studentScores?.find(
          score => score.studentId === student.id
        );

        if (studentScore) {
          hasScores = true;
          log(`Student has a score of ${studentScore.totalScore} in ${subject}`);
        }
      }

      if (!hasScores) {
        log('Student does not have any scores in the class grades');
      }

      // Check if the student has notifications
      const notificationsRef = collection(db, 'notifications');
      const notificationsQuery = query(
        notificationsRef,
        where('userId', '==', student.id),
        where('type', '==', 'grade_published')
      );
      const notificationsSnapshot = await getDocs(notificationsQuery);

      if (notificationsSnapshot.empty) {
        log('Student does not have any grade notifications');
      } else {
        log(`Student has ${notificationsSnapshot.size} grade notifications`);
      }

      setStatus('Student grade view test completed successfully');
      log('Student grade view test completed successfully');
    } catch (error) {
      console.error('Error in student grade view test:', error);
      setStatus(`Error: ${error.message}`);
      log(`Error: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  // Test the parent grade view
  const testParentGradeView = async () => {
    try {
      setLoading(true);
      log('Starting parent grade view test...');

      // Find a parent
      const usersRef = collection(db, 'users');
      const parentsQuery = query(usersRef, where('role', '==', 'parent'));
      const parentsSnapshot = await getDocs(parentsQuery);

      if (parentsSnapshot.empty) {
        throw new Error('No parents found in the database');
      }

      const parent = parentsSnapshot.docs[0];
      const parentData = parent.data();
      log(`Found parent: ${parentData.name} (${parent.id})`);

      // Check if the parent has children
      if (!parentData.children || parentData.children.length === 0) {
        throw new Error('Parent does not have any children');
      }

      log(`Parent has ${parentData.children.length} children`);

      // Check each child
      for (const childId of parentData.children) {
        const childRef = doc(db, 'users', childId);
        const childDoc = await getDoc(childRef);

        if (!childDoc.exists()) {
          log(`Child ${childId} not found`);
          continue;
        }

        const childData = childDoc.data();
        log(`Found child: ${childData.name} (${childId})`);

        // Check if the child has a class
        if (!childData.classId) {
          log(`Child ${childData.name} is not assigned to a class`);
          continue;
        }

        // Get the class data
        const classRef = doc(db, 'classes', childData.classId);
        const classDoc = await getDoc(classRef);

        if (!classDoc.exists()) {
          log(`Class not found for child ${childData.name}`);
          continue;
        }

        const classData = classDoc.data();
        log(`Found class: ${classData.name} for child ${childData.name}`);

        // Check if the class has grades
        if (!classData.grades || Object.keys(classData.grades).length === 0) {
          log(`No grades found for child ${childData.name}'s class`);
          continue;
        }

        log(`Class has grades for subjects: ${Object.keys(classData.grades).join(', ')}`);

        // Check if the child has scores in the grades
        let hasScores = false;
        for (const subject in classData.grades) {
          const subjectGrade = classData.grades[subject];
          const childScore = subjectGrade.studentScores?.find(
            score => score.studentId === childId
          );

          if (childScore) {
            hasScores = true;
            log(`Child ${childData.name} has a score of ${childScore.totalScore} in ${subject}`);
          }
        }

        if (!hasScores) {
          log(`Child ${childData.name} does not have any scores in the class grades`);
        }
      }

      // Check if the parent has notifications
      const notificationsRef = collection(db, 'notifications');
      const notificationsQuery = query(
        notificationsRef,
        where('userId', '==', parent.id),
        where('type', '==', 'grade_published')
      );
      const notificationsSnapshot = await getDocs(notificationsQuery);

      if (notificationsSnapshot.empty) {
        log('Parent does not have any grade notifications');
      } else {
        log(`Parent has ${notificationsSnapshot.size} grade notifications`);
      }

      setStatus('Parent grade view test completed successfully');
      log('Parent grade view test completed successfully');
    } catch (error) {
      console.error('Error in parent grade view test:', error);
      setStatus(`Error: ${error.message}`);
      log(`Error: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  // Create a test class if it doesn't exist
  const createTestClassIfNeeded = async () => {
    // Check if the test class already exists
    const classesRef = collection(db, 'classes');
    const q = query(classesRef, where('name', '==', 'Test Class'));
    const querySnapshot = await getDocs(q);

    if (!querySnapshot.empty) {
      return querySnapshot.docs[0].id;
    }

    // Create a new test class
    const classData = {
      name: 'Test Class',
      section: 'A',
      grade: '10',
      academicYear: '2023-2024',
      createdAt: Timestamp.now(),
      createdBy: auth.currentUser?.uid || 'test-user'
    };

    const docRef = await addDoc(classesRef, classData);
    return docRef.id;
  };

  // Create a test submission
  const createTestSubmission = async (classId) => {
    // Create test assessments
    const assessments = [
      {
        id: 'test-assessment-1',
        title: 'Quiz 1',
        type: 'quiz',
        points: 20
      },
      {
        id: 'test-assessment-2',
        title: 'Midterm Exam',
        type: 'exam',
        points: 30
      },
      {
        id: 'test-assessment-3',
        title: 'Final Exam',
        type: 'exam',
        points: 50
      }
    ];

    // Create test students
    const students = [
      {
        id: 'test-student-1',
        uid: 'test-student-1',
        name: 'Test Student 1',
        rollNumber: '001',
        totalScore: '85.0',
        assessmentScores: [
          {
            assessmentId: 'test-assessment-1',
            assessmentTitle: 'Quiz 1',
            assessmentType: 'quiz',
            score: 18,
            maxPoints: 20
          },
          {
            assessmentId: 'test-assessment-2',
            assessmentTitle: 'Midterm Exam',
            assessmentType: 'exam',
            score: 25,
            maxPoints: 30
          },
          {
            assessmentId: 'test-assessment-3',
            assessmentTitle: 'Final Exam',
            assessmentType: 'exam',
            score: 42,
            maxPoints: 50
          }
        ]
      },
      {
        id: 'test-student-2',
        uid: 'test-student-2',
        name: 'Test Student 2',
        rollNumber: '002',
        totalScore: '75.0',
        assessmentScores: [
          {
            assessmentId: 'test-assessment-1',
            assessmentTitle: 'Quiz 1',
            assessmentType: 'quiz',
            score: 15,
            maxPoints: 20
          },
          {
            assessmentId: 'test-assessment-2',
            assessmentTitle: 'Midterm Exam',
            assessmentType: 'exam',
            score: 22,
            maxPoints: 30
          },
          {
            assessmentId: 'test-assessment-3',
            assessmentTitle: 'Final Exam',
            assessmentType: 'exam',
            score: 38,
            maxPoints: 50
          }
        ]
      }
    ];

    // Create the submission
    const submissionData = {
      classId,
      className: 'Test Class',
      sectionName: 'A',
      subject: 'Mathematics',
      teacherId: auth.currentUser?.uid || 'test-teacher',
      teacherName: auth.currentUser?.displayName || 'Test Teacher',
      status: 'pending',
      submittedAt: Timestamp.now(),
      assessments,
      students
    };

    const submissionRef = collection(db, 'gradeSubmissions');
    const docRef = await addDoc(submissionRef, submissionData);

    // Also create entries in the scores collection
    const scoresRef = collection(db, 'scores');
    const scorePromises = [];

    students.forEach(student => {
      assessments.forEach(assessment => {
        const assessmentScore = student.assessmentScores.find(
          score => score.assessmentId === assessment.id
        );

        if (assessmentScore) {
          scorePromises.push(
            addDoc(scoresRef, {
              studentId: student.id,
              studentName: student.name,
              assessmentId: assessment.id,
              assessmentTitle: assessment.title,
              assessmentType: assessment.type,
              points: assessmentScore.score,
              maxPoints: assessment.points,
              classId,
              className: 'Test Class',
              sectionName: 'A',
              subject: 'Mathematics',
              submissionId: docRef.id,
              timestamp: Timestamp.now(),
              status: 'pending'
            })
          );
        }
      });
    });

    await Promise.all(scorePromises);

    return docRef.id;
  };

  // Clear test results
  const clearTestResults = () => {
    setTestResults([]);
    setStatus('');
  };

  return (
    <ScrollView style={styles.container}>
      <Card style={styles.card}>
        <Card.Content>
          <Title>Grade System Test</Title>
          <Paragraph>
            This component allows testing the grade system workflow:
          </Paragraph>
          <Text>1. Teacher submits grades</Text>
          <Text>2. Admin approves grades</Text>
          <Text>3. Students and parents receive notifications</Text>
        </Card.Content>
      </Card>

      <View style={styles.buttonContainer}>
        <Button
          mode="contained"
          onPress={testTeacherGradeSubmission}
          disabled={loading}
          style={styles.button}
        >
          Test Teacher Grade Submission
        </Button>

        <Button
          mode="contained"
          onPress={testAdminGradeApproval}
          disabled={loading}
          style={styles.button}
        >
          Test Admin Grade Approval
        </Button>

        <Button
          mode="contained"
          onPress={testStudentGradeView}
          disabled={loading}
          style={styles.button}
        >
          Test Student Grade View
        </Button>

        <Button
          mode="contained"
          onPress={testParentGradeView}
          disabled={loading}
          style={styles.button}
        >
          Test Parent Grade View
        </Button>

        <Button
          mode="outlined"
          onPress={clearTestResults}
          disabled={loading}
          style={styles.button}
        >
          Clear Test Results
        </Button>
      </View>

      {loading && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" />
          <Text style={styles.loadingText}>Running test...</Text>
        </View>
      )}

      {status ? (
        <Card style={styles.statusCard}>
          <Card.Content>
            <Title>Status</Title>
            <Text>{status}</Text>
          </Card.Content>
        </Card>
      ) : null}

      {testResults.length > 0 && (
        <Card style={styles.resultsCard}>
          <Card.Content>
            <Title>Test Results</Title>
            <ScrollView style={styles.resultsContainer}>
              {testResults.map((result, index) => (
                <View key={index} style={styles.resultItem}>
                  <Text style={styles.resultTime}>{result.time}</Text>
                  <Text style={styles.resultMessage}>{result.message}</Text>
                  {index < testResults.length - 1 && <Divider style={styles.divider} />}
                </View>
              ))}
            </ScrollView>
          </Card.Content>
        </Card>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#f5f5f5',
  },
  card: {
    marginBottom: 16,
  },
  buttonContainer: {
    marginBottom: 16,
  },
  button: {
    marginBottom: 8,
  },
  loadingContainer: {
    alignItems: 'center',
    marginVertical: 16,
  },
  loadingText: {
    marginTop: 8,
    fontSize: 16,
  },
  statusCard: {
    marginBottom: 16,
    backgroundColor: '#e8f5e9',
  },
  resultsCard: {
    marginBottom: 16,
  },
  resultsContainer: {
    maxHeight: 300,
  },
  resultItem: {
    marginVertical: 4,
  },
  resultTime: {
    fontSize: 12,
    color: '#757575',
  },
  resultMessage: {
    fontSize: 14,
  },
  divider: {
    marginVertical: 4,
  },
});

export default GradeSystemTest;
