{"name": "school-ms", "license": "0BSD", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "npx expo start", "android": "npx expo run:android", "ios": "npx expo run:ios", "web": "npx expo start --web", "android-build": "cd android && gradlew.bat assembleDebug", "android-run": "cd android && gradlew.bat installDebug && npm run android", "android-clean": "cd android && gradlew.bat clean", "android-bundle": "cd android && gradlew.bat bundleRelease", "android-release": "cd android && gradlew.bat assembleRelease", "build-apk": "node build-apk.js", "direct-build-apk": "node direct-build-apk.js", "simple-build-apk": "node simple-build-apk.js", "final-build-apk": "node final-build-apk.js", "test": "jest", "lint": "eslint .", "prebuild": "node fix-gradlew-permissions.js", "build:apk": "npm run prebuild && eas build -p android --profile apk"}, "dependencies": {"@cloudinary/react": "^1.11.2", "@cloudinary/url-gen": "^1.13.0", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@expo/vector-icons": "^14.0.0", "@react-native-async-storage/async-storage": "1.21.0", "@react-native-community/cli-server-api": "11.3.7", "@react-native-community/datetimepicker": "^7.6.1", "@react-native-community/netinfo": "11.1.0", "@react-native-masked-view/masked-view": "0.3.0", "@react-native-material/core": "1.3.7", "@react-native-picker/picker": "2.6.1", "@react-navigation/bottom-tabs": "6.5.11", "@react-navigation/native": "^6.1.9", "@react-navigation/native-stack": "6.9.17", "@react-navigation/stack": "^6.3.20", "aos": "^2.3.4", "axios": "^1.7.9", "cloudinary": "^2.6.0", "cloudinary-react": "^1.8.1", "cosmiconfig": "^8.3.6", "date-fns": "^4.1.0", "dotenv": "^16.4.7", "emailjs-com": "^3.2.0", "expo": "~50.0.0", "expo-application": "~5.8.4", "expo-blur": "~12.9.2", "expo-constants": "~15.4.6", "expo-dev-client": "~3.3.12", "expo-device": "~5.9.4", "expo-document-picker": "~11.10.0", "expo-file-system": "~16.0.5", "expo-haptics": "~12.8.1", "expo-image-manipulator": "~11.8.0", "expo-image-picker": "~14.7.1", "expo-linear-gradient": "~12.7.2", "expo-linking": "~6.2.2", "expo-notifications": "^0.27.8", "expo-sharing": "~11.10.0", "expo-splash-screen": "^0.26.5", "expo-status-bar": "~1.11.1", "expo-system-ui": "~2.9.4", "firebase": "^10.7.1", "framer-motion": "^10.16.4", "metro": "~0.80.4", "metro-config": "~0.80.4", "metro-core": "~0.80.4", "metro-react-native-babel-transformer": "^0.76.8", "metro-resolver": "~0.80.4", "metro-runtime": "~0.80.4", "metro-source-map": "~0.80.4", "react": "18.2.0", "react-dom": "18.2.0", "react-native": "0.73.6", "react-native-animatable": "^1.4.0", "react-native-calendar-events": "2.2.0", "react-native-calendars": "1.1308.0", "react-native-chart-kit": "^6.12.0", "react-native-dotenv": "^3.4.11", "react-native-gesture-handler": "~2.14.0", "react-native-paper": "^5.14.1", "react-native-reanimated": "~3.6.2", "react-native-safe-area-context": "4.8.2", "react-native-screens": "~3.29.0", "react-native-svg": "14.1.0", "react-native-swipe-list-view": "^3.2.9", "react-native-vector-icons": "9.2.0", "recharts": "^2.10.3", "xlsx": "^0.18.5"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/plugin-transform-export-namespace-from": "^7.22.11", "@babel/plugin-transform-react-jsx": "^7.22.15", "@expo/metro-config": "~0.17.1", "babel-plugin-module-resolver": "^5.0.2", "babel-plugin-transform-remove-console": "^6.9.4", "babel-preset-expo": "^10.0.0", "sharp": "^0.34.1"}, "private": true}