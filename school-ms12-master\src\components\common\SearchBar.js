import React, { useState, useEffect, useRef } from 'react';
import { View, StyleSheet, TouchableOpacity, Animated, Platform, Keyboard } from 'react-native';
import { Searchbar, Menu, Button, Chip, Text, Surface, IconButton, Divider } from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import * as Animatable from 'react-native-animatable';
import { useLanguage } from '../../context/LanguageContext';

const SearchBar = ({
  placeholder = 'Search...',
  value = '',
  onChangeText,
  onSubmit,
  onFilterChange,
  onClear,
  filters = [],
  style,
  containerStyle,
  elevation = 2,
  showFilterButton = true,
  showClearButton = true,
  showSubmitButton = false,
  autoFocus = false,
  animated = true,
  debounceTime = 300,
  filterButtonIcon = 'filter-variant',
  clearButtonIcon = 'close',
  submitButtonIcon = 'magnify',
  filterButtonStyle,
  clearButtonStyle,
  submitButtonStyle,
  filterButtonColor,
  clearButtonColor,
  submitButtonColor,
  activeFilters = {},
}) => {
  const { translate, isRTL } = useLanguage();
  const [searchQuery, setSearchQuery] = useState(value);
  const [filterMenuVisible, setFilterMenuVisible] = useState(false);
  const [appliedFilters, setAppliedFilters] = useState(activeFilters);
  const [expandedFilter, setExpandedFilter] = useState(null);
  const searchBarRef = useRef(null);
  const debounceTimeout = useRef(null);
  const scaleAnim = useRef(new Animated.Value(0)).current;

  // Initialize with active filters
  useEffect(() => {
    setAppliedFilters(activeFilters);
  }, []);

  // Handle search query change with debounce
  const handleChangeText = (text) => {
    setSearchQuery(text);
    
    if (debounceTimeout.current) {
      clearTimeout(debounceTimeout.current);
    }
    
    debounceTimeout.current = setTimeout(() => {
      if (onChangeText) {
        onChangeText(text);
      }
    }, debounceTime);
  };

  // Handle search submit
  const handleSubmit = () => {
    Keyboard.dismiss();
    if (onSubmit) {
      onSubmit(searchQuery);
    }
  };

  // Handle clear search
  const handleClear = () => {
    setSearchQuery('');
    if (onClear) {
      onClear();
    }
    if (onChangeText) {
      onChangeText('');
    }
    if (searchBarRef.current) {
      searchBarRef.current.focus();
    }
  };

  // Handle filter change
  const handleFilterChange = (filter, value) => {
    const newFilters = { ...appliedFilters };
    
    if (value === null || value === undefined || value === '') {
      delete newFilters[filter.key];
    } else {
      newFilters[filter.key] = value;
    }
    
    setAppliedFilters(newFilters);
    
    if (onFilterChange) {
      onFilterChange(newFilters);
    }
  };

  // Handle clear all filters
  const handleClearAllFilters = () => {
    setAppliedFilters({});
    
    if (onFilterChange) {
      onFilterChange({});
    }
    
    setFilterMenuVisible(false);
  };

  // Handle filter menu toggle
  const toggleFilterMenu = () => {
    setFilterMenuVisible(!filterMenuVisible);
    setExpandedFilter(null);
  };

  // Handle filter expansion
  const toggleExpandFilter = (key) => {
    setExpandedFilter(expandedFilter === key ? null : key);
  };

  // Get active filter count
  const getActiveFilterCount = () => {
    return Object.keys(appliedFilters).length;
  };

  // Animation for filter menu
  useEffect(() => {
    Animated.timing(scaleAnim, {
      toValue: filterMenuVisible ? 1 : 0,
      duration: 200,
      useNativeDriver: true,
    }).start();
  }, [filterMenuVisible]);

  // Render filter options
  const renderFilterOptions = (filter) => {
    switch (filter.type) {
      case 'select':
        return (
          <View style={styles.filterOptions}>
            {filter.options.map((option) => (
              <Chip
                key={option.value}
                selected={appliedFilters[filter.key] === option.value}
                onPress={() => handleFilterChange(filter, appliedFilters[filter.key] === option.value ? null : option.value)}
                style={styles.filterChip}
                mode={appliedFilters[filter.key] === option.value ? 'flat' : 'outlined'}
              >
                {option.label}
              </Chip>
            ))}
          </View>
        );
      case 'boolean':
        return (
          <View style={styles.filterOptions}>
            <Chip
              selected={appliedFilters[filter.key] === true}
              onPress={() => handleFilterChange(filter, appliedFilters[filter.key] === true ? null : true)}
              style={styles.filterChip}
              mode={appliedFilters[filter.key] === true ? 'flat' : 'outlined'}
            >
              {filter.trueLabel || 'Yes'}
            </Chip>
            <Chip
              selected={appliedFilters[filter.key] === false}
              onPress={() => handleFilterChange(filter, appliedFilters[filter.key] === false ? null : false)}
              style={styles.filterChip}
              mode={appliedFilters[filter.key] === false ? 'flat' : 'outlined'}
            >
              {filter.falseLabel || 'No'}
            </Chip>
          </View>
        );
      case 'date':
        // Date filter would require a date picker component
        return (
          <View style={styles.filterOptions}>
            <Text style={styles.filterText}>Date filter not implemented</Text>
          </View>
        );
      default:
        return null;
    }
  };

  // Render active filter chips
  const renderActiveFilterChips = () => {
    if (getActiveFilterCount() === 0) return null;
    
    return (
      <Animatable.View 
        animation="fadeIn" 
        duration={300} 
        style={styles.activeFiltersContainer}
      >
        <ScrollView 
          horizontal 
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.activeFiltersContent}
        >
          {Object.entries(appliedFilters).map(([key, value]) => {
            const filter = filters.find(f => f.key === key);
            if (!filter) return null;
            
            let label = '';
            if (filter.type === 'select') {
              const option = filter.options.find(o => o.value === value);
              label = option ? `${filter.label}: ${option.label}` : '';
            } else if (filter.type === 'boolean') {
              label = `${filter.label}: ${value ? (filter.trueLabel || 'Yes') : (filter.falseLabel || 'No')}`;
            } else {
              label = `${filter.label}: ${value}`;
            }
            
            return (
              <Chip
                key={key}
                onClose={() => handleFilterChange(filter, null)}
                style={styles.activeFilterChip}
                mode="flat"
              >
                {label}
              </Chip>
            );
          })}
          
          <Chip
            onPress={handleClearAllFilters}
            style={styles.clearAllChip}
            mode="outlined"
          >
            {translate('common.clearAll') || 'Clear All'}
          </Chip>
        </ScrollView>
      </Animatable.View>
    );
  };

  return (
    <View style={[styles.container, containerStyle]}>
      <View style={styles.searchContainer}>
        <Searchbar
          ref={searchBarRef}
          placeholder={translate(placeholder) || placeholder}
          onChangeText={handleChangeText}
          value={searchQuery}
          style={[
            styles.searchbar,
            { elevation },
            style
          ]}
          iconColor="#757575"
          onSubmitEditing={handleSubmit}
          autoFocus={autoFocus}
        />
        
        <View style={styles.buttonsContainer}>
          {showClearButton && searchQuery !== '' && (
            <Animatable.View animation={animated ? 'fadeIn' : undefined} duration={200}>
              <IconButton
                icon={clearButtonIcon}
                size={20}
                onPress={handleClear}
                style={[styles.iconButton, clearButtonStyle]}
                color={clearButtonColor || '#757575'}
              />
            </Animatable.View>
          )}
          
          {showSubmitButton && (
            <IconButton
              icon={submitButtonIcon}
              size={20}
              onPress={handleSubmit}
              style={[styles.iconButton, submitButtonStyle]}
              color={submitButtonColor || '#2196F3'}
            />
          )}
          
          {showFilterButton && filters.length > 0 && (
            <View>
              <IconButton
                icon={filterButtonIcon}
                size={20}
                onPress={toggleFilterMenu}
                style={[styles.iconButton, filterButtonStyle]}
                color={filterButtonColor || '#757575'}
              />
              {getActiveFilterCount() > 0 && (
                <View style={styles.filterBadge}>
                  <Text style={styles.filterBadgeText}>{getActiveFilterCount()}</Text>
                </View>
              )}
              
              <Menu
                visible={filterMenuVisible}
                onDismiss={() => setFilterMenuVisible(false)}
                anchor={{ x: isRTL ? 0 : 1000, y: 60 }}
                style={styles.filterMenu}
              >
                <View style={styles.filterMenuHeader}>
                  <Text style={styles.filterMenuTitle}>
                    {translate('common.filters') || 'Filters'}
                  </Text>
                  <Button
                    mode="text"
                    onPress={handleClearAllFilters}
                    disabled={getActiveFilterCount() === 0}
                    compact
                  >
                    {translate('common.clearAll') || 'Clear All'}
                  </Button>
                </View>
                
                <Divider />
                
                <View style={styles.filterList}>
                  {filters.map((filter) => (
                    <View key={filter.key} style={styles.filterItem}>
                      <TouchableOpacity
                        onPress={() => toggleExpandFilter(filter.key)}
                        style={styles.filterHeader}
                      >
                        <Text style={styles.filterLabel}>{filter.label}</Text>
                        <IconButton
                          icon={expandedFilter === filter.key ? 'chevron-up' : 'chevron-down'}
                          size={20}
                          style={styles.expandIcon}
                        />
                      </TouchableOpacity>
                      
                      {expandedFilter === filter.key && (
                        <Animatable.View
                          animation="fadeIn"
                          duration={200}
                          style={styles.filterContent}
                        >
                          {renderFilterOptions(filter)}
                        </Animatable.View>
                      )}
                      
                      <Divider />
                    </View>
                  ))}
                </View>
                
                <View style={styles.filterMenuFooter}>
                  <Button
                    mode="contained"
                    onPress={() => setFilterMenuVisible(false)}
                  >
                    {translate('common.apply') || 'Apply'}
                  </Button>
                </View>
              </Menu>
            </View>
          )}
        </View>
      </View>
      
      {renderActiveFilterChips()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 8,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  searchbar: {
    flex: 1,
    borderRadius: 8,
  },
  buttonsContainer: {
    flexDirection: 'row',
    position: 'absolute',
    right: 8,
    alignItems: 'center',
  },
  iconButton: {
    margin: 0,
  },
  filterBadge: {
    position: 'absolute',
    top: 0,
    right: 0,
    backgroundColor: '#F44336',
    borderRadius: 10,
    width: 16,
    height: 16,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
  },
  filterBadgeText: {
    color: 'white',
    fontSize: 10,
    fontWeight: 'bold',
  },
  filterMenu: {
    width: 300,
    maxHeight: 400,
  },
  filterMenuHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  filterMenuTitle: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  filterList: {
    maxHeight: 300,
  },
  filterItem: {
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  filterHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 16,
  },
  filterLabel: {
    fontSize: 14,
    fontWeight: '500',
  },
  expandIcon: {
    margin: 0,
  },
  filterContent: {
    padding: 16,
    paddingTop: 0,
  },
  filterOptions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  filterChip: {
    margin: 4,
  },
  filterText: {
    color: '#757575',
    fontStyle: 'italic',
  },
  filterMenuFooter: {
    padding: 16,
    alignItems: 'flex-end',
  },
  activeFiltersContainer: {
    marginTop: 8,
  },
  activeFiltersContent: {
    paddingHorizontal: 4,
  },
  activeFilterChip: {
    marginHorizontal: 4,
  },
  clearAllChip: {
    marginHorizontal: 4,
  },
});

export default SearchBar;
