import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import StudentDashboard from '../screens/student/StudentDashboard';
import ScheduleManagement from '../screens/student/ScheduleManagement';
import ResultManagement from '../screens/student/ResultManagement';
import StudentAttendance from '../screens/student/StudentAttendance';
import StudentMessaging from '../screens/student/StudentMessaging';
import CalendarManagement from '../screens/student/CalendarManagement';
import LibraryAccess from '../screens/student/LibraryAccess';
import StudentGrades from '../screens/student/StudentGrades';
import SubjectDetails from '../screens/student/SubjectDetails';
import HomeworkView from '../screens/student/HomeworkView';
import BehaviorView from '../screens/shared/BehaviorView';
import NotificationCenter from '../screens/shared/NotificationCenter';
import NotificationSettings from '../screens/shared/NotificationSettings';
import ReportCenter from '../screens/shared/ReportCenter';
import StudentExamSchedule from '../screens/student/StudentExamSchedule';
import Communication from '../screens/shared/Communication';
import StudentClassSchedule from '../screens/student/StudentClassSchedule';
import ProfileManagement from '../screens/student/ProfileManagement';
import { StudentSidebarProvider } from '../context/StudentSidebarContext';

const Stack = createStackNavigator();

const StudentNavigator = () => {
  return (
    <StudentSidebarProvider>
      <Stack.Navigator>
      <Stack.Screen
        name="StudentDashboard"
        component={StudentDashboard}
        options={{ title: 'Student Dashboard' }}
      />
      <Stack.Screen
        name="ScheduleManagement"
        component={ScheduleManagement}
        options={{ title: 'My Schedule' }}
      />
      <Stack.Screen
        name="ResultManagement"
        component={ResultManagement}
        options={{ title: 'My Results' }}
      />
      <Stack.Screen
        name="StudentAttendance"
        component={StudentAttendance}
        options={{ title: 'My Attendance' }}
      />
      <Stack.Screen
        name="StudentMessaging"
        component={StudentMessaging}
        options={{ title: 'Messaging', headerShown: false }}
      />
      <Stack.Screen
        name="CalendarManagement"
        component={CalendarManagement}
        options={{ title: 'School Calendar' }}
      />
      <Stack.Screen
        name="StudentExamSchedule"
        component={StudentExamSchedule}
        options={{ title: 'Exam Schedule', headerShown: false }}
      />
      <Stack.Screen
        name="LibraryAccess"
        component={LibraryAccess}
        options={{ title: 'Library' }}
      />
      <Stack.Screen
        name="StudentGrades"
        component={StudentGrades}
        options={{ title: 'My Grades' }}
      />
      <Stack.Screen
        name="SubjectDetails"
        component={SubjectDetails}
        options={{ title: 'Subject Details' }}
      />
      <Stack.Screen
        name="HomeworkView"
        component={HomeworkView}
        options={{ title: 'My Homework' }}
      />
      <Stack.Screen
        name="BehaviorView"
        component={BehaviorView}
        options={{ title: 'Behavior Records' }}
      />
      <Stack.Screen
        name="Reports"
        component={ReportCenter}
        options={{ title: 'Report Center' }}
      />
      <Stack.Screen
        name="NotificationCenter"
        component={NotificationCenter}
        options={{ title: 'Notifications', headerShown: false }}
      />
      <Stack.Screen
        name="NotificationSettings"
        component={NotificationSettings}
        options={{ title: 'Notification Settings', headerShown: false }}
      />
      <Stack.Screen
        name="CommunicationCenter"
        component={Communication}
        options={{ title: 'Communication Center', headerShown: false }}
      />
      <Stack.Screen
        name="StudentClassSchedule"
        component={StudentClassSchedule}
        options={{ title: 'Class Schedule', headerShown: false }}
      />
      <Stack.Screen
        name="ProfileManagement"
        component={ProfileManagement}
        options={{ title: 'My Profile' }}
      />
    </Stack.Navigator>
    </StudentSidebarProvider>
  );
};

export default StudentNavigator;