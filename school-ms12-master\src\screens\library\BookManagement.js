import React, { useState, useEffect } from 'react';
import { View, ScrollView, StyleSheet } from 'react-native';
import { Card, Title, FAB, Portal, Modal, DataTable, Searchbar } from 'react-native-paper';
import { db, storage } from '../../config/firebase';
import { collection, addDoc, query, getDocs, doc, updateDoc, deleteDoc } from 'firebase/firestore';
import { ref, uploadBytes, getDownloadURL } from 'firebase/storage';
import * as ImagePicker from 'expo-image-picker';
import CustomButton from '../../components/common/CustomButton';
import CustomInput from '../../components/common/CustomInput';

const BookManagement = () => {
  const [books, setBooks] = useState([]);
  const [categories, setCategories] = useState([]);
  const [visible, setVisible] = useState(false);
  const [selectedBook, setSelectedBook] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(false);
  
  const [formData, setFormData] = useState({
    title: '',
    author: '',
    isbn: '',
    category: '',
    publisher: '',
    publishYear: '',
    edition: '',
    quantity: '',
    description: '',
    coverImage: null,
    location: '', // Physical location in library
  });

  useEffect(() => {
    fetchBooks();
    fetchCategories();
  }, []);

  const fetchBooks = async () => {
    try {
      const booksRef = collection(db, 'books');
      const q = query(booksRef);
      const querySnapshot = await getDocs(q);
      
      const booksData = [];
      querySnapshot.forEach((doc) => {
        booksData.push({ id: doc.id, ...doc.data() });
      });
      
      setBooks(booksData);
    } catch (error) {
      console.error('Error fetching books:', error);
    }
  };

  const fetchCategories = async () => {
    try {
      const categoriesRef = collection(db, 'bookCategories');
      const q = query(categoriesRef);
      const querySnapshot = await getDocs(q);
      
      const categoriesData = [];
      querySnapshot.forEach((doc) => {
        categoriesData.push({ id: doc.id, ...doc.data() });
      });
      
      setCategories(categoriesData);
    } catch (error) {
      console.error('Error fetching categories:', error);
    }
  };

  const handleImagePick = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [3, 4],
        quality: 1,
      });

      if (!result.canceled) {
        setFormData({ ...formData, coverImage: result.assets[0].uri });
      }
    } catch (error) {
      console.error('Error picking image:', error);
    }
  };

  const uploadImage = async (uri) => {
    try {
      const response = await fetch(uri);
      const blob = await response.blob();
      const filename = `books/${Date.now()}-${Math.random().toString(36).substring(7)}`;
      const storageRef = ref(storage, filename);
      
      await uploadBytes(storageRef, blob);
      const downloadURL = await getDownloadURL(storageRef);
      return downloadURL;
    } catch (error) {
      console.error('Error uploading image:', error);
      return null;
    }
  };

  const handleAddBook = async () => {
    try {
      setLoading(true);
      let imageUrl = null;
      if (formData.coverImage) {
        imageUrl = await uploadImage(formData.coverImage);
      }

      const booksRef = collection(db, 'books');
      await addDoc(booksRef, {
        ...formData,
        coverImage: imageUrl,
        quantity: parseInt(formData.quantity),
        publishYear: parseInt(formData.publishYear),
        availableQuantity: parseInt(formData.quantity),
        createdAt: new Date().toISOString(),
      });
      
      setVisible(false);
      resetForm();
      fetchBooks();
    } catch (error) {
      console.error('Error adding book:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateBook = async () => {
    try {
      setLoading(true);
      let imageUrl = formData.coverImage;
      if (formData.coverImage && formData.coverImage !== selectedBook.coverImage) {
        imageUrl = await uploadImage(formData.coverImage);
      }

      const bookRef = doc(db, 'books', selectedBook.id);
      await updateDoc(bookRef, {
        ...formData,
        coverImage: imageUrl,
        quantity: parseInt(formData.quantity),
        publishYear: parseInt(formData.publishYear),
        updatedAt: new Date().toISOString(),
      });
      
      setVisible(false);
      resetForm();
      fetchBooks();
    } catch (error) {
      console.error('Error updating book:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteBook = async (bookId) => {
    try {
      await deleteDoc(doc(db, 'books', bookId));
      fetchBooks();
    } catch (error) {
      console.error('Error deleting book:', error);
    }
  };

  const resetForm = () => {
    setFormData({
      title: '',
      author: '',
      isbn: '',
      category: '',
      publisher: '',
      publishYear: '',
      edition: '',
      quantity: '',
      description: '',
      coverImage: null,
      location: '',
    });
    setSelectedBook(null);
  };

  const filteredBooks = books.filter(book =>
    book.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    book.author.toLowerCase().includes(searchQuery.toLowerCase()) ||
    book.isbn.includes(searchQuery)
  );

  return (
    <View style={styles.container}>
      <Searchbar
        placeholder="Search books..."
        onChangeText={setSearchQuery}
        value={searchQuery}
        style={styles.searchBar}
      />

      <ScrollView>
        <DataTable>
          <DataTable.Header>
            <DataTable.Title>Title</DataTable.Title>
            <DataTable.Title>Author</DataTable.Title>
            <DataTable.Title numeric>Available</DataTable.Title>
            <DataTable.Title numeric>Total</DataTable.Title>
          </DataTable.Header>

          {filteredBooks.map((book) => (
            <DataTable.Row
              key={book.id}
              onPress={() => {
                setSelectedBook(book);
                setFormData({
                  title: book.title,
                  author: book.author,
                  isbn: book.isbn,
                  category: book.category,
                  publisher: book.publisher,
                  publishYear: book.publishYear.toString(),
                  edition: book.edition,
                  quantity: book.quantity.toString(),
                  description: book.description,
                  coverImage: book.coverImage,
                  location: book.location,
                });
                setVisible(true);
              }}
            >
              <DataTable.Cell>{book.title}</DataTable.Cell>
              <DataTable.Cell>{book.author}</DataTable.Cell>
              <DataTable.Cell numeric>{book.availableQuantity}</DataTable.Cell>
              <DataTable.Cell numeric>{book.quantity}</DataTable.Cell>
            </DataTable.Row>
          ))}
        </DataTable>
      </ScrollView>

      <Portal>
        <Modal
          visible={visible}
          onDismiss={() => {
            setVisible(false);
            resetForm();
          }}
          contentContainerStyle={styles.modalContent}
        >
          <ScrollView>
            <Title>{selectedBook ? 'Edit Book' : 'Add New Book'}</Title>

            <CustomInput
              label="Title"
              value={formData.title}
              onChangeText={(text) => setFormData({ ...formData, title: text })}
            />

            <CustomInput
              label="Author"
              value={formData.author}
              onChangeText={(text) => setFormData({ ...formData, author: text })}
            />

            <CustomInput
              label="ISBN"
              value={formData.isbn}
              onChangeText={(text) => setFormData({ ...formData, isbn: text })}
            />

            <CustomInput
              label="Category"
              value={formData.category}
              onChangeText={(text) => setFormData({ ...formData, category: text })}
            />

            <CustomInput
              label="Publisher"
              value={formData.publisher}
              onChangeText={(text) => setFormData({ ...formData, publisher: text })}
            />

            <CustomInput
              label="Publish Year"
              value={formData.publishYear}
              onChangeText={(text) => setFormData({ ...formData, publishYear: text })}
              keyboardType="numeric"
            />

            <CustomInput
              label="Edition"
              value={formData.edition}
              onChangeText={(text) => setFormData({ ...formData, edition: text })}
            />

            <CustomInput
              label="Quantity"
              value={formData.quantity}
              onChangeText={(text) => setFormData({ ...formData, quantity: text })}
              keyboardType="numeric"
            />

            <CustomInput
              label="Location in Library"
              value={formData.location}
              onChangeText={(text) => setFormData({ ...formData, location: text })}
            />

            <CustomInput
              label="Description"
              value={formData.description}
              onChangeText={(text) => setFormData({ ...formData, description: text })}
              multiline
              numberOfLines={3}
            />

            <CustomButton
              mode="contained"
              onPress={handleImagePick}
              style={styles.imageButton}
            >
              {formData.coverImage ? 'Change Cover Image' : 'Add Cover Image'}
            </CustomButton>

            <View style={styles.modalButtons}>
              <CustomButton
                mode="contained"
                onPress={selectedBook ? handleUpdateBook : handleAddBook}
                loading={loading}
              >
                {selectedBook ? 'Update' : 'Add'}
              </CustomButton>
              
              {selectedBook && (
                <CustomButton
                  mode="outlined"
                  onPress={() => handleDeleteBook(selectedBook.id)}
                  style={styles.deleteButton}
                >
                  Delete
                </CustomButton>
              )}
              
              <CustomButton
                mode="outlined"
                onPress={() => {
                  setVisible(false);
                  resetForm();
                }}
              >
                Cancel
              </CustomButton>
            </View>
          </ScrollView>
        </Modal>
      </Portal>

      <FAB
        style={styles.fab}
        icon="plus"
        onPress={() => setVisible(true)}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  searchBar: {
    margin: 10,
    elevation: 2,
  },
  modalContent: {
    backgroundColor: 'white',
    padding: 20,
    margin: 20,
    borderRadius: 10,
    maxHeight: '80%',
  },
  imageButton: {
    marginVertical: 10,
  },
  modalButtons: {
    marginTop: 20,
  },
  deleteButton: {
    marginVertical: 10,
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
  },
});

export default BookManagement;
