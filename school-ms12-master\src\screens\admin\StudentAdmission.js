import React, { useState, useEffect } from 'react';
import { View, ScrollView, StyleSheet, Image, Platform, Alert, TouchableOpacity, Dimensions } from 'react-native';
import {
  Title,
  Text,
  Portal,
  Modal,
  ActivityIndicator,
  IconButton,
  Card,
  Divider,
  useTheme,
  TextInput,
  Menu,
  Button,
  Chip,
  Dialog,
  Avatar,
  Surface,
  ProgressBar,
  Badge,
  Snackbar,
} from 'react-native-paper';
import { useLanguage } from '../../context/LanguageContext';
import { LinearGradient } from 'expo-linear-gradient';
import * as Animatable from 'react-native-animatable';
import safeTranslate from '../../utils/safeTranslate';
import SimpleTextInput from '../../components/common/SimpleTextInput';
import { db, storage } from '../../config/firebase';
import {
  collection,
  addDoc,
  query,
  getDocs,
  where,
  doc,
  updateDoc,
  deleteDoc,
  setDoc,
  orderBy,
  limit,
  getDoc,
  serverTimestamp,
} from 'firebase/firestore';
import CloudinaryService from '../../services/CloudinaryService';
import * as ImagePicker from 'expo-image-picker';
import EthiopianDatePicker from '../../components/common/EthiopianDatePicker';
import CustomButton from '../../components/common/CustomButton';
import { createUserWithEmailAndPassword, sendEmailVerification } from 'firebase/auth';
import { auth } from '../../config/firebase';
import { useNavigation } from '@react-navigation/native';
import { assignRollNumbers } from '../../utils/rollNumberUtils';
import EmailVerificationService from '../../services/EmailVerificationService';
import EmailNotificationService from '../../services/EmailNotificationService';
import UserRegistrationService from '../../services/UserRegistrationService';
import VerificationStatus from '../../components/common/VerificationStatus';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import EventService, { EVENTS } from '../../services/EventService';
import ParentAdmissionModal from '../../components/admin/ParentAdmissionModal';

const GENDER_OPTIONS = ['Male', 'Female', 'Other'];

const StudentAdmission = ({ visible, onClose, onSuccess, student }) => {
  // No theme needed
  const navigation = useNavigation();

  // Safely access language context with error handling
  let translate, language, isRTL;
  try {
    const languageContext = useLanguage();
    translate = languageContext.translate;
    language = languageContext.language;
    isRTL = languageContext.isRTL;
  } catch (error) {
    console.error('Error accessing language context in StudentAdmission:', error);
    // Provide fallback functions
    translate = (key) => key;
    language = 'en';
    isRTL = false;
  }
  const [formStep, setFormStep] = useState(1);
  const [formProgress, setFormProgress] = useState(0.33);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  const [image, setImage] = useState(null);
  const [resultImage, setResultImage] = useState(null);
  const [parentImage, setParentImage] = useState(null);
  const [kebeleIdImage, setKebeleIdImage] = useState(null);
  const [classes, setClasses] = useState([]);
  const [sections, setSections] = useState([]);

  // Email verification states
  const [studentEmailVerified, setStudentEmailVerified] = useState(false);
  const [parentEmailVerified, setParentEmailVerified] = useState(false);
  const [studentVerificationSent, setStudentVerificationSent] = useState(false);
  const [parentVerificationSent, setParentVerificationSent] = useState(false);
  const [verificationLoading, setVerificationLoading] = useState(false);
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [showSuccessDialog, setShowSuccessDialog] = useState(false);
  const [createdUserData, setCreatedUserData] = useState(null);
  const [showClassMenu, setShowClassMenu] = useState(false);
  const [showSectionMenu, setShowSectionMenu] = useState(false);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [nextAdmissionNumber, setNextAdmissionNumber] = useState('');
  const [isEditMode, setIsEditMode] = useState(false);
  const [showParentDialog, setShowParentDialog] = useState(false);
  const [existingParent, setExistingParent] = useState(null);
  const [showParentAdmissionModal, setShowParentAdmissionModal] = useState(false);

  const defaultFormData = {
    firstName: '',
    lastName: '',
    email: '',
    password: '1234qwer',
    dateOfBirth: new Date(),
    admissionDate: new Date(),
    gender: '',
    classId: '',
    section: '',
    address: {
      street: '',
      city: 'Asella',
      state: 'Oromia',
      country: 'Ethiopia',
    },
    parent: {
      firstName: '',
      lastName: '',
      email: '',
      password: '1234qwer',
      phone: '',
      alternatePhone: '',
      address: {
        street: '',
        city: 'Asella',
        state: 'Oromia',
        country: 'Ethiopia',
      },
      occupation: '',
      workAddress: '',
      workPhone: '',
      relationship: 'father',
      emergencyContact: false,
      status: 'active',
      role: 'parent',
    },
    academicDetails: {
      previousSchool: '',
      previousGrade: '',
      reasonForLeaving: '',
    },
    status: 'active',
  };

  const [formData, setFormData] = useState(defaultFormData);

  useEffect(() => {
    if (visible) {
      if (student) {
        setIsEditMode(true);
        // Create a deep copy of the student object and ensure all required fields exist
        const studentData = {
          ...defaultFormData,
          ...student,
          password: '', // Don't populate password in edit mode
          // Safely handle date conversions
          dateOfBirth: student.dateOfBirth ? formatDate(student.dateOfBirth) : formatDate(new Date()),
          admissionDate: student.admissionDate ? formatDate(student.admissionDate) : formatDate(new Date()),
          address: {
            ...defaultFormData.address,
            ...(student.address || {}),
          },
          parent: {
            ...defaultFormData.parent,
            ...(student.parent || {}),
          },
          academicDetails: {
            ...defaultFormData.academicDetails,
            ...(student.academicDetails || {}),
          },
        };
        setFormData(studentData);
        if (student.imageUrl) setImage(student.imageUrl);
        if (student.resultImageUrl) setResultImage(student.resultImageUrl);
        if (student.parent?.imageUrl) setParentImage(student.parent.imageUrl);
        if (student.parent?.kebeleIdUrl) setKebeleIdImage(student.parent.kebeleIdUrl);

        // In edit mode, we assume emails are already verified
        setStudentEmailVerified(true);
        setParentEmailVerified(true);
      } else {
        // Reset form for new student
        setIsEditMode(false);
        setFormData(defaultFormData);
        setImage(null);
        setResultImage(null);
        setParentImage(null);
        setKebeleIdImage(null);
        setStudentEmailVerified(false);
        setParentEmailVerified(false);
        setStudentVerificationSent(false);
        setParentVerificationSent(false);
        fetchNextAdmissionNumber();
      }
      fetchClasses();
    }
  }, [visible, student]);

  // Check if emails are already verified
  useEffect(() => {
    const checkEmailVerification = async () => {
      if (!isEditMode && formData.email) {
        try {
          console.log('Email verification check for student:', formData.email);

          // Check if email is already verified
          const EmailVerificationService = require('../../services/EmailVerificationService').default;
          const isVerified = await EmailVerificationService.isEmailVerified(formData.email);

          console.log('Student email verification status:', isVerified);
          setStudentEmailVerified(isVerified);

          if (isVerified) {
            setStudentVerificationSent(true);
          }
        } catch (error) {
          console.error('Error checking student email verification:', error);
          setStudentEmailVerified(false);
        }
      }
    };

    checkEmailVerification();
  }, [formData.email, isEditMode]);

  useEffect(() => {
    const checkParentEmailVerification = async () => {
      if (!isEditMode && formData.parent?.email) {
        try {
          console.log('Email verification check for parent:', formData.parent.email);

          // Check if email is already verified
          const EmailVerificationService = require('../../services/EmailVerificationService').default;
          const isVerified = await EmailVerificationService.isEmailVerified(formData.parent.email);

          console.log('Parent email verification status:', isVerified);
          setParentEmailVerified(isVerified);

          if (isVerified) {
            setParentVerificationSent(true);
          }
        } catch (error) {
          console.error('Error checking parent email verification:', error);
          setParentEmailVerified(false);
        }
      }
    };

    checkParentEmailVerification();
  }, [formData.parent?.email, isEditMode]);

  useEffect(() => {
    if (formData.classId) {
      fetchSectionsForClass(formData.classId);
    } else {
      setSections([]);
    }
  }, [formData.classId]);

  const fetchClasses = async () => {
    try {
      const classesRef = collection(db, 'classes');
      const querySnapshot = await getDocs(classesRef);

      const classesData = [];
      querySnapshot.forEach((doc) => {
        const data = doc.data();
        classesData.push({
          id: doc.id,
          name: data.name,
          capacity: data.capacity
        });
      });

      setClasses(classesData.sort((a, b) => a.name.localeCompare(b.name)));
    } catch (error) {
      console.error('Error fetching classes:', error);
      setError('Failed to fetch classes');
    }
  };

  const fetchSectionsForClass = async (classId) => {
    try {
      const classRef = doc(db, 'classes', classId);
      const classDoc = await getDoc(classRef);
      if (classDoc.exists()) {
        const classData = classDoc.data();
        const sectionNames = Array.isArray(classData.sections)
          ? classData.sections.map(section => typeof section === 'object' ? section.name : section)
          : [];
        setSections(sectionNames.sort());
      }
    } catch (error) {
      console.error('Error fetching sections:', error);
      setError('Failed to fetch sections');
    }
  };

  const fetchNextAdmissionNumber = async () => {
    try {
      const studentsRef = collection(db, 'users');
      const q = query(
        studentsRef,
        where('role', '==', 'student'),
        orderBy('admissionNumber', 'desc'),
        limit(1)
      );

      const querySnapshot = await getDocs(q);
      let nextNumber = '0001';

      if (!querySnapshot.empty) {
        const lastStudent = querySnapshot.docs[0].data();
        const lastNumber = parseInt(lastStudent.admissionNumber.slice(-4));
        nextNumber = String(lastNumber + 1).padStart(4, '0');
      }

      const year = new Date().getFullYear();
      const admissionNumber = `${year}${nextNumber}`;
      setNextAdmissionNumber(admissionNumber);
      setFormData(prev => ({ ...prev, admissionNumber }));
    } catch (error) {
      console.error('Error fetching next admission number:', error);
      setError('Failed to generate admission number');
    }
  };

  const calculateRollNumber = async (classId, firstName, lastName) => {
    try {
      const studentsRef = collection(db, 'users');
      const q = query(
        studentsRef,
        where('role', '==', 'student'),
        where('classId', '==', classId)
      );

      const querySnapshot = await getDocs(q);
      const students = [];

      querySnapshot.forEach(doc => {
        const data = doc.data();
        students.push({
          firstName: data.firstName,
          lastName: data.lastName,
        });
      });

      students.push({ firstName, lastName });

      students.sort((a, b) => {
        const nameA = `${a.firstName} ${a.lastName}`.toLowerCase();
        const nameB = `${b.firstName} ${b.lastName}`.toLowerCase();
        return nameA.localeCompare(nameB);
      });

      const position = students.findIndex(
        student =>
          student.firstName === firstName && student.lastName === lastName
      );

      return String(position + 1).padStart(2, '0');
    } catch (error) {
      console.error('Error calculating roll number:', error);
      throw error;
    }
  };

  const requestCameraPermission = async () => {
    try {
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert(
          translate('permissions.required'),
          translate('permissions.camera'),
          [{ text: translate('common.ok') }]
        );
        return false;
      }
      return true;
    } catch (error) {
      console.error('Error requesting camera permission:', error);
      return false;
    }
  };

  const requestMediaPermission = async () => {
    try {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert(
          translate('permissions.required'),
          translate('permissions.mediaLibrary'),
          [{ text: translate('common.ok') }]
        );
        return false;
      }
      return true;
    } catch (error) {
      console.error('Error requesting media permission:', error);
      return false;
    }
  };

  const takePhoto = async () => {
    try {
      const hasPermission = await requestCameraPermission();
      if (!hasPermission) return;

      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets && result.assets[0]) {
        return result.assets[0].uri;
      }
    } catch (error) {
      console.error('Error taking photo:', error);
      Alert.alert(translate('common.error'), translate('media.takePhotoError'));
    }
    return null;
  };

  const pickImage = async () => {
    try {
      const hasPermission = await requestMediaPermission();
      if (!hasPermission) return;

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets && result.assets[0]) {
        return result.assets[0].uri;
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert(translate('common.error'), translate('media.pickImageError'));
    }
    return null;
  };

  const showImageSourceMenu = async (imageType) => {
    Alert.alert(
      translate('media.selectSource'),
      translate('media.chooseSource'),
      [
        {
          text: translate('media.camera'),
          onPress: async () => {
            const uri = await takePhoto();
            if (uri) {
              switch (imageType) {
                case 'profile':
                  setImage(uri);
                  break;
                case 'result':
                  setResultImage(uri);
                  break;
                case 'parent':
                  setParentImage(uri);
                  break;
                case 'kebele':
                  setKebeleIdImage(uri);
                  break;
              }
            }
          },
        },
        {
          text: translate('media.gallery'),
          onPress: async () => {
            const uri = await pickImage();
            if (uri) {
              switch (imageType) {
                case 'profile':
                  setImage(uri);
                  break;
                case 'result':
                  setResultImage(uri);
                  break;
                case 'parent':
                  setParentImage(uri);
                  break;
                case 'kebele':
                  setKebeleIdImage(uri);
                  break;
              }
            }
          },
        },
        {
          text: translate('common.cancel'),
          style: 'cancel',
        },
      ]
    );
  };

  const uploadImage = async (uri, path) => {
    if (!uri) return null;
    if (uri.startsWith('http')) return uri;

    try {
      // Extract folder and file information
      const pathParts = path.split('/');
      const folder = pathParts[0] || 'students';
      const subfolder = pathParts[1] || '';
      const imageType = pathParts[2] || 'profile';

      // Generate a unique ID for the image
      const timestamp = Date.now();
      const publicId = `${folder}_${subfolder}_${imageType}_${timestamp}`;

      // Upload to Cloudinary
      const result = await CloudinaryService.uploadFromUri(uri, {
        folder: path,
        tags: [folder, subfolder, imageType],
        publicId: publicId
      });

      return result.url;
    } catch (error) {
      console.error(`Error uploading image to ${path}:`, error);
      return null;
    }
  };

  // Send verification email to student
  const sendStudentVerificationEmail = async () => {
    if (!formData.email || studentVerificationSent || studentEmailVerified) return;

    // Validate email format only when the button is clicked
    if (!EmailVerificationService.isValidEmail(formData.email)) {
      setError('Please enter a valid email address');
      return;
    }

    try {
      setVerificationLoading(true);
      setError(null);

      // Try to use Firebase Auth verification if possible
      try {
        // Create a temporary user to send verification email
        const userCredential = await createUserWithEmailAndPassword(
          auth,
          formData.email,
          formData.password || 'Temp123!'
        );

        // Send verification email
        await sendEmailVerification(userCredential.user);

        // Log the verification request
        try {
          await addDoc(collection(db, 'verification_logs'), {
            userId: userCredential.user.uid,
            email: userCredential.user.email,
            type: 'student_admission',
            timestamp: serverTimestamp(),
            status: 'sent'
          });
        } catch (logError) {
          console.warn('Failed to log verification request:', logError);
          // Continue even if logging fails
        }

        // Store the user ID for later use
        setFormData(prev => ({
          ...prev,
          tempUserId: userCredential.user.uid
        }));

        // Try to send custom verification email as well
        try {
          // Generate verification token
          const { token } = await EmailVerificationService.generateVerificationToken(formData.email, 'student');

          // Send custom verification email
          await EmailNotificationService.sendVerificationEmail(formData.email, token, 'student');
        } catch (customEmailError) {
          console.error('Error sending custom verification email:', customEmailError);
          // Continue even if custom email fails, as Firebase will send its own
        }

        setStudentVerificationSent(true);
        setSnackbarMessage(translate('verification.emailSent') || 'Verification email sent successfully');
        setSnackbarVisible(true);
        return;
      } catch (firebaseError) {
        console.log('Firebase verification failed, falling back to custom verification:', firebaseError.message);

        // Handle specific Firebase errors
        if (firebaseError.code === 'auth/email-already-in-use') {
          setError('This email is already in use. Please use a different email or try to log in.');
          setVerificationLoading(false);
          return;
        } else if (firebaseError.code === 'auth/invalid-email') {
          setError('Please enter a valid email address');
          setVerificationLoading(false);
          return;
        }
        // If Firebase verification fails with other errors, fall back to custom verification
      }

      // Fall back to custom verification
      try {
        // Generate verification token
        const { token } = await EmailVerificationService.generateVerificationToken(formData.email, 'student');

        // Send verification email
        await EmailNotificationService.sendVerificationEmail(formData.email, token, 'student');

        setStudentVerificationSent(true);
        setSnackbarMessage(translate('verification.emailSent') || 'Verification email sent successfully');
        setSnackbarVisible(true);
      } catch (customVerificationError) {
        console.error('Custom verification failed:', customVerificationError);
        setError('Failed to send verification email. Please check the email address and try again.');
      }
    } catch (error) {
      console.error('Error sending student verification email:', error);
      setError('Failed to send verification email. Please try again.');
    } finally {
      setVerificationLoading(false);
    }
  };

  // Send verification email to parent
  const sendParentVerificationEmail = async () => {
    if (!formData.parent?.email || parentVerificationSent || parentEmailVerified) return;

    // Validate email format only when the button is clicked
    if (!EmailVerificationService.isValidEmail(formData.parent.email)) {
      setError('Please enter a valid parent email address');
      return;
    }

    try {
      setVerificationLoading(true);
      setError(null);

      // Try to use Firebase Auth verification if possible
      try {
        // Create a temporary user to send verification email
        const userCredential = await createUserWithEmailAndPassword(
          auth,
          formData.parent.email,
          formData.parent.password || 'Temp123!'
        );

        // Send verification email
        await sendEmailVerification(userCredential.user);

        // Log the verification request
        try {
          await addDoc(collection(db, 'verification_logs'), {
            userId: userCredential.user.uid,
            email: userCredential.user.email,
            type: 'parent_admission',
            timestamp: serverTimestamp(),
            status: 'sent'
          });
        } catch (logError) {
          console.warn('Failed to log verification request:', logError);
          // Continue even if logging fails
        }

        // Store the user ID for later use
        setFormData(prev => ({
          ...prev,
          parent: {
            ...prev.parent,
            tempUserId: userCredential.user.uid
          }
        }));

        // Try to send custom verification email as well
        try {
          // Generate verification token
          const { token } = await EmailVerificationService.generateVerificationToken(formData.parent.email, 'parent');

          // Send custom verification email
          await EmailNotificationService.sendVerificationEmail(formData.parent.email, token, 'parent');
        } catch (customEmailError) {
          console.error('Error sending custom verification email:', customEmailError);
          // Continue even if custom email fails, as Firebase will send its own
        }

        setParentVerificationSent(true);
        setSnackbarMessage(translate('verification.emailSent') || 'Verification email sent successfully');
        setSnackbarVisible(true);
        return;
      } catch (firebaseError) {
        console.log('Firebase verification failed, falling back to custom verification:', firebaseError.message);

        // Handle specific Firebase errors
        if (firebaseError.code === 'auth/email-already-in-use') {
          setError('This parent email is already in use. Please use a different email or try to log in.');
          setVerificationLoading(false);
          return;
        } else if (firebaseError.code === 'auth/invalid-email') {
          setError('Please enter a valid parent email address');
          setVerificationLoading(false);
          return;
        }
        // If Firebase verification fails with other errors, fall back to custom verification
      }

      // Fall back to custom verification
      try {
        // Generate verification token
        const { token } = await EmailVerificationService.generateVerificationToken(formData.parent.email, 'parent');

        // Send verification email
        await EmailNotificationService.sendVerificationEmail(formData.parent.email, token, 'parent');

        setParentVerificationSent(true);
        setSnackbarMessage(translate('verification.emailSent') || 'Verification email sent successfully');
        setSnackbarVisible(true);
      } catch (customVerificationError) {
        console.error('Custom verification failed:', customVerificationError);
        setError('Failed to send parent verification email. Please check the email address and try again.');
      }
    } catch (error) {
      console.error('Error sending parent verification email:', error);
      setError('Failed to send parent verification email. Please try again.');
    } finally {
      setVerificationLoading(false);
    }
  };

  // Send credentials email
  const sendCredentialsEmail = async (userData, type = 'student') => {
    try {
      // Validate inputs
      if (!userData) {
        console.error(`Cannot send credentials email: userData is missing for ${type}`);
        return;
      }

      const email = type === 'student' ? userData.email : userData.parent?.email;
      const password = type === 'student' ? userData.password : userData.parent?.password;

      if (!email) {
        console.error(`Cannot send credentials email: email is missing for ${type}`);
        return;
      }

      if (!password) {
        console.error(`Cannot send credentials email: password is missing for ${type}`);
        return;
      }

      // Validate email format only when actually sending
      if (!EmailVerificationService.isValidEmail(email)) {
        console.error(`Cannot send credentials email: invalid email format for ${type}`);
        return;
      }

      try {
        await EmailNotificationService.sendCredentialsEmail(
          email,
          password,
          type === 'student' ? userData : userData.parent,
          type
        );
        console.log(`Credentials email sent to ${type} (${email})`);
      } catch (emailError) {
        console.error(`Error in EmailNotificationService.sendCredentialsEmail for ${type}:`, emailError);
        // Continue execution even if email sending fails
      }
    } catch (error) {
      console.error(`Error sending ${type} credentials email:`, error);
      // Don't throw the error to prevent blocking the main flow
    }
  };

  const handleSubmit = async () => {
    if (loading) return;

    try {
      setLoading(true);
      setError(null);

      // Validate required fields
      if (!formData.firstName?.trim() || !formData.lastName?.trim() || !formData.email?.trim()) {
        throw new Error('Please fill in all required fields');
      }

      if (!formData.classId || !formData.section) {
        throw new Error('Please select class and section');
      }

      if (!isEditMode && !formData.password?.trim()) {
        throw new Error('Please enter a password');
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(formData.email.trim())) {
        throw new Error('Please enter a valid email address for the student');
      }

      // Do not auto-verify emails - require manual verification
      // Only proceed if student email is verified
      if (!studentEmailVerified) {
        throw new Error('Please verify the student email before submitting');
      }

      // Only require parent email verification if a parent email is provided and not empty
      if (formData.parent?.email && formData.parent.email.trim() !== '') {
        if (!emailRegex.test(formData.parent.email.trim())) {
          throw new Error('Please enter a valid email address for the parent');
        }

        if (!parentEmailVerified) {
          throw new Error('Please verify the parent email before submitting');
        }
      }

      // Create authentication account first if new student
      let authUser = null;
      if (!isEditMode) {
        try {
          // Use the admin method to create user without affecting admin's auth state
          const result = await UserRegistrationService.adminCreateUser(
            formData.email.trim(),
            formData.password,
            'student',
            {
              firstName: formData.firstName,
              lastName: formData.lastName,
              displayName: `${formData.firstName} ${formData.lastName}`,
              emailVerified: false // Do not auto-verify - require manual verification
            }
          );

          if (!result.success) {
            throw new Error(result.error || 'Failed to create student account');
          }

          authUser = result.user;
          console.log('Created auth account using adminCreateUser:', authUser.uid);
        } catch (error) {
          if (error.code === 'auth/email-already-in-use') {
            throw new Error('A student with this email already exists');
          }
          throw new Error(`Failed to create student account: ${error.message}`);
        }
      }

      // Get class details
      const classRef = doc(db, 'classes', formData.classId);
      const classDoc = await getDoc(classRef);
      if (!classDoc.exists()) {
        throw new Error('Selected class not found');
      }
      const classData = classDoc.data();

      // Process parent information
      let parentData = null;
      if (formData.parent?.email?.trim()) {
        try {
          parentData = await handleParentSubmit({
            ...formData.parent,
            email: formData.parent.email.trim()
          });
          if (parentData) {
            console.log('Parent processed:', parentData.id);
          } else {
            console.log('No parent data provided or processing skipped');
          }
        } catch (error) {
          console.error('Error processing parent:', error);
          // Don't throw an error, just log it and continue without a parent
          // This allows student creation even if parent processing fails
        }
      }

      // Upload images
      console.log('Starting image uploads...');
      const userId = authUser?.uid || formData.id;

      const [studentImage, resultImage, parentImg, kebeleImg] = await Promise.all([
        image ? uploadImage(image, `students/${userId}/profile`) : null,
        resultImage ? uploadImage(resultImage, `students/${userId}/result`) : null,
        parentImage && parentData ? uploadImage(parentImage, `parents/${parentData.id}/profile`) : null,
        kebeleIdImage && parentData ? uploadImage(kebeleIdImage, `parents/${parentData.id}/kebele`) : null
      ]);

      // Clean and validate all fields
      const cleanFormData = {
        firstName: formData.firstName?.trim() || '',
        lastName: formData.lastName?.trim() || '',
        email: formData.email?.trim() || '',
        dateOfBirth: formData.dateOfBirth || null,
        admissionDate: formData.admissionDate || new Date().toISOString(),
        gender: formData.gender || 'Other',
        address: {
          street: formData.address?.street?.trim() || '',
          city: formData.address?.city?.trim() || '',
          state: formData.address?.state?.trim() || '',
          country: formData.address?.country?.trim() || ''
        },
        phone: formData.phone?.trim() || '',
        emergencyContact: formData.emergencyContact?.trim() || '',
        bloodGroup: formData.bloodGroup || 'Not Specified',
        nationality: formData.nationality?.trim() || '',
        religion: formData.religion?.trim() || '',
        previousSchool: formData.previousSchool?.trim() || ''
      };

      // Prepare student document with no undefined values
      const studentDoc = {
        id: userId,
        ...cleanFormData,
        role: 'student',
        imageUrl: studentImage || null,
        resultImageUrl: resultImage || null,
        classId: formData.classId,
        className: classData.name,
        sectionName: formData.section,
        status: 'active',
        parent: parentData ? {
          id: parentData.id,
          firstName: parentData.firstName || '',
          lastName: parentData.lastName || '',
          email: parentData.email || '',
          phone: parentData.phone || '',
          relationship: parentData.relationship || '',
          imageUrl: parentImg || parentData.imageUrl || null,
          kebeleIdUrl: kebeleImg || parentData.kebeleIdUrl || null
        } : null,
        updatedAt: new Date().toISOString(),
        createdAt: isEditMode ? formData.createdAt : new Date().toISOString()
      };

      // Save student document
      await setDoc(doc(db, 'users', userId), studentDoc);
      console.log('Saved student document:', userId);

      // Update parent's children array if parent exists
      if (parentData && parentData.id) {
        try {
          // Get current parent data to ensure we have the latest children array
          const parentRef = doc(db, 'users', parentData.id);
          const parentDoc = await getDoc(parentRef);

          if (parentDoc.exists()) {
            const currentParentData = parentDoc.data();
            const currentChildren = currentParentData.children || [];

            // Only add the child if not already in the array
            if (!currentChildren.includes(userId)) {
              const updatedChildren = [...currentChildren, userId];

              // Update the parent document with the new children array
              await updateDoc(parentRef, {
                children: updatedChildren,
                updatedAt: new Date().toISOString()
              });

              console.log(`Updated parent ${parentData.id} with child ${userId}`);
            }
          }
        } catch (error) {
          console.error('Error updating parent children array:', error);
          // Continue execution even if this fails
        }
      }

      // Update class section
      const sectionRef = doc(db, 'classes', formData.classId);
      const sectionDoc = await getDoc(sectionRef);
      if (sectionDoc.exists()) {
        const sections = sectionDoc.data().sections || [];
        const sectionIndex = sections.findIndex(s => s.name === formData.section);

        if (sectionIndex !== -1) {
          const section = sections[sectionIndex];
          const students = section.students || [];
          if (!students.includes(userId)) {
            students.push(userId);
            sections[sectionIndex] = { ...section, students };
            await updateDoc(sectionRef, { sections });
            console.log('Updated class section');

            // Assign roll numbers for all students in the section
            try {
              await assignRollNumbers(formData.classId, formData.section);
              console.log('Roll numbers assigned successfully');
            } catch (error) {
              console.error('Error assigning roll numbers:', error);
            }
          }
        }
      }

      // Store created user data for success dialog
      const finalUserData = {
        ...studentDoc,
        password: formData.password,
        parent: {
          ...studentDoc.parent,
          password: formData.parent.password
        }
      };

      setCreatedUserData(finalUserData);

      // Send credentials emails
      if (!isEditMode) {
        try {
          await sendCredentialsEmail(finalUserData, 'student');
          console.log('Student credentials email sent');
        } catch (error) {
          console.error('Error sending student credentials email:', error);
          // Continue even if email sending fails
        }

        if (finalUserData.parent && finalUserData.parent.email && finalUserData.parent.email.trim() !== '') {
          try {
            await sendCredentialsEmail(finalUserData, 'parent');
            console.log('Parent credentials email sent');
          } catch (error) {
            console.error('Error sending parent credentials email:', error);
            // Continue even if email sending fails
          }
        }
      }

      // Show success dialog
      setShowSuccessDialog(true);
      setSuccess(true);
    } catch (error) {
      console.error('Error in handleSubmit:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  // Form reset functionality is handled in the useEffect when visible changes

  const getClassName = (classId) => {
    const classItem = classes.find(c => c.id === classId);
    return classItem ? classItem.name : translate('student.selectClass');
  };

  const checkParentEmail = async (email) => {
    try {
      const parentsRef = collection(db, 'users');
      const q = query(parentsRef,
        where('role', '==', 'parent'),
        where('email', '==', email)
      );
      const querySnapshot = await getDocs(q);

      if (!querySnapshot.empty) {
        const parentDoc = querySnapshot.docs[0];
        const parentData = parentDoc.data();
        setExistingParent({
          id: parentDoc.id,
          ...parentData
        });
        setShowParentDialog(true);
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error checking parent email:', error);
      return false;
    }
  };

  const handleParentEmailChange = async (email) => {
    setFormData(prev => ({
      ...prev,
      parent: {
        ...prev.parent,
        email
      }
    }));

    if (email && email.length > 5 && email.includes('@')) {
      const exists = await checkParentEmail(email);
      if (!exists) {
        // Clear parent data if email changed and parent doesn't exist
        setFormData(prev => ({
          ...prev,
          parent: {
            email,
            firstName: '',
            lastName: '',
            phone: '',
            imageUrl: '',
            kebeleIdUrl: '',
          }
        }));
        setParentImage(null);
        setKebeleIdImage(null);
      }
    }
  };

  const handleLinkParent = () => {
    if (existingParent) {
      setFormData(prev => ({
        ...prev,
        parent: {
          ...existingParent,
          imageUrl: existingParent.imageUrl || '',
          kebeleIdUrl: existingParent.kebeleIdUrl || '',
        }
      }));
      setParentImage(existingParent.imageUrl || null);
      setKebeleIdImage(existingParent.kebeleIdUrl || null);
    }
    setShowParentDialog(false);
  };

  // Set up event listener for parent added
  useEffect(() => {
    // Create listener for parent added event
    const parentAddedListener = EventService.addListener(
      EVENTS.PARENT_ADDED,
      (newParent) => {
        // Update form data with the new parent
        setFormData(prev => ({
          ...prev,
          parent: {
            ...newParent,
            imageUrl: newParent.imageUrl || '',
            kebeleIdUrl: newParent.kebeleIdUrl || '',
          }
        }));
        setParentImage(newParent.imageUrl || null);
        setKebeleIdImage(newParent.kebeleIdUrl || null);
      }
    );

    // Clean up listener on component unmount
    return () => {
      parentAddedListener.remove();
    };
  }, []);

  const handleAddNewParent = () => {
    setShowParentDialog(false);
    // Show the parent admission modal directly within the student admission screen
    setShowParentAdmissionModal(true);
  };

  const handleViewParentDetails = () => {
    if (formData.parent?.id) {
      navigation.navigate('ParentManagement', {
        parentId: formData.parent.id,
        viewOnly: true
      });
    }
  };

  const handleParentSubmit = async (parentData) => {
    try {
      // If no parent email is provided, return null to indicate no parent
      if (!parentData.email || parentData.email.trim() === '') {
        return null;
      }

      if (!isEditMode && !parentData.password) {
        // Set a default password if none is provided
        parentData.password = '1234qwer';
      }

      // Check if parent already exists
      const parentQuery = query(
        collection(db, 'users'),
        where('email', '==', parentData.email),
        where('role', '==', 'parent')
      );
      const parentSnapshot = await getDocs(parentQuery);

      let parentId;
      let parentDoc;
      let parentAuth = null;
      let existingChildren = [];

      if (!parentSnapshot.empty) {
        // Parent exists, update their information
        const existingParent = parentSnapshot.docs[0];
        parentId = existingParent.id;
        const existingParentData = existingParent.data();

        // Preserve existing children array
        existingChildren = existingParentData.children || [];

        parentDoc = {
          ...existingParentData,
          ...parentData,
          children: existingChildren, // Ensure children array is preserved
          updatedAt: new Date().toISOString(),
        };

        await updateDoc(doc(db, 'users', parentId), parentDoc);
        console.log('Updated existing parent:', parentId);
      } else {
        // Create new parent account with authentication using adminCreateUser
        try {
          // Use the admin method to create user without affecting admin's auth state
          const result = await UserRegistrationService.adminCreateUser(
            parentData.email,
            parentData.password,
            'parent',
            {
              firstName: parentData.firstName,
              lastName: parentData.lastName,
              displayName: `${parentData.firstName} ${parentData.lastName}`,
              emailVerified: false, // Do not auto-verify - require manual verification
              children: []
            }
          );

          if (!result.success) {
            throw new Error(result.error || 'Failed to create parent account');
          }

          parentId = result.user.uid;

          // Create parent document (additional fields not covered by adminCreateUser)
          parentDoc = {
            ...parentData,
            id: parentId,
            role: 'parent',
            status: 'active',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            children: [] // Initialize empty children array
          };
          delete parentDoc.password; // Remove password from document

          // Update the user document with any additional fields
          await updateDoc(doc(db, 'users', parentId), parentDoc);
          console.log('Created new parent with adminCreateUser:', parentId);
        } catch (error) {
          console.error('Error creating parent auth:', error);
          throw new Error('Failed to create parent account: ' + error.message);
        }
      }

      return { id: parentId, ...parentDoc, children: existingChildren };
    } catch (error) {
      console.error('Error handling parent submission:', error);
      throw error;
    }
  };

  // Add a helper function to format dates for display
  const formatDate = (date) => {
    if (!date) return '';
    if (typeof date === 'string') {
      return date.split('T')[0];
    }
    if (date instanceof Date) {
      return date.toISOString().split('T')[0];
    }
    return '';
  };

  // Animation names are directly used in the Animatable components

  return (
    <Portal>
      <Modal
        visible={visible}
        onDismiss={onClose}
        contentContainerStyle={styles.modalContainer}
      >
        <ScrollView style={styles.container}>
          <LinearGradient
            colors={['#1976d2', '#f5f5f5']}
            style={styles.headerGradient}
          >
            <View style={styles.header}>
              <Animatable.Text
                animation="fadeIn"
                duration={1000}
                style={styles.headerTitle}
              >
                {translate('student.registration')}
              </Animatable.Text>
              <IconButton
                icon="close"
                size={24}
                color="white"
                onPress={onClose}
              />
            </View>
          </LinearGradient>

          {/* Progress bar */}
          <View style={styles.progressContainer}>
            <ProgressBar progress={formProgress} color={'#1976d2'} style={styles.progressBar} />
            <Text style={styles.progressText}>{translate('common.step')} {formStep} {translate('common.of')} 3</Text>
          </View>

          <Card style={styles.card}>
            <Card.Content>
              <View style={styles.imageContainer}>
                {image && (
                  <Image
                    source={{ uri: image }}
                    style={styles.previewImage}
                  />
                )}
                <Button
                  mode="contained"
                  onPress={() => showImageSourceMenu('profile')}
                  style={styles.uploadButton}
                >
                  {image ? translate('student.profileImage.change') : translate('student.profileImage.add')}
                </Button>
              </View>

              <Divider style={styles.divider} />

              {formStep === 1 && (
                <Animatable.View animation="fadeIn" duration={800} useNativeDriver style={styles.form}>
                  <Surface style={styles.sectionSurface}>
                    <Title style={styles.sectionTitle}>{safeTranslate(translate, 'student.details.personalInfo', 'Personal Information')}</Title>
                    <SimpleTextInput
                      label={safeTranslate(translate, 'student.fields.firstName', 'First Name')}
                      value={formData.firstName}
                      onChangeText={(text) => setFormData({ ...formData, firstName: text })}
                      error={!formData.firstName && error}
                      left={<TextInput.Icon icon="account" />}
                    />
                    <SimpleTextInput
                      label={safeTranslate(translate, 'student.fields.lastName', 'Last Name')}
                      value={formData.lastName}
                      onChangeText={(text) => setFormData({ ...formData, lastName: text })}
                      error={!formData.lastName && error}
                      left={<TextInput.Icon icon="account" />}
                    />
                    <View style={styles.emailVerificationContainer}>
                      <SimpleTextInput
                        label={safeTranslate(translate, 'student.fields.email', 'Email')}
                        value={formData.email}
                        onChangeText={(text) => {
                          setFormData({ ...formData, email: text });
                          setStudentEmailVerified(false);
                          setStudentVerificationSent(false);
                        }}
                        keyboardType="email-address"
                        error={!formData.email && error}
                        left={<TextInput.Icon icon="email" />}
                        disabled={isEditMode || studentEmailVerified}
                        style={{ flex: 1 }}
                      />
                    </View>

                    {!isEditMode && formData.email && (
                      <Surface style={styles.verificationContainer}>
                        <VerificationStatus
                          email={formData.email}
                          isVerified={studentEmailVerified}
                          verificationSent={studentVerificationSent}
                          onVerificationSent={() => {
                            console.log('Verification sent for student email:', formData.email);
                            setStudentVerificationSent(true);
                          }}
                          onVerificationStatusChange={(status) => {
                            console.log('Student email verification status changed to:', status);
                            setStudentEmailVerified(status);
                          }}
                          userType="student"
                        />

                        {studentVerificationSent && !studentEmailVerified && (
                          <Text style={styles.verificationMessage}>
                            {translate('verification.checkEmail') || 'Please verify your email using the button above before continuing.'}
                          </Text>
                        )}

                        {studentEmailVerified && (
                          <Text style={[styles.verificationMessage, { color: '#4CAF50' }]}>
                            {translate('verification.verified') || 'Email verified successfully!'}
                          </Text>
                        )}
                      </Surface>
                    )}
                    <SimpleTextInput
                      label={safeTranslate(translate, 'validation.password', 'Password')}
                      value={formData.password}
                      onChangeText={(text) => setFormData({ ...formData, password: text })}
                      secureTextEntry
                      error={!formData.password && error}
                      left={<TextInput.Icon icon="lock" />}
                    />
                  </Surface>

                <Surface style={styles.sectionSurface}>
                  <View style={styles.datePickerContainer}>
                    <Text style={styles.datePickerLabel}>{safeTranslate(translate, 'student.fields.dateOfBirth', 'Date of Birth')}</Text>
                    <EthiopianDatePicker
                      value={new Date(formData.dateOfBirth)}
                      onChange={(selectedDate) => {
                        setFormData({ ...formData, dateOfBirth: selectedDate.toISOString().split('T')[0] });
                      }}
                      label={translate('student.fields.selectDateOfBirth')}
                      language={language}
                    />
                  </View>
                </Surface>

                <Surface style={styles.sectionSurface}>
                  <View style={styles.genderContainer}>
                    <Text style={styles.genderLabel}>{safeTranslate(translate, 'student.fields.gender', 'Gender')}</Text>
                    <View style={styles.chipContainer}>
                      <Chip
                        key="Male"
                        selected={formData.gender === "Male"}
                        onPress={() => setFormData({ ...formData, gender: "Male" })}
                        style={[styles.chip, formData.gender === "Male" && styles.selectedChip]}
                        icon="gender-male"
                        selectedColor={'#1976d2'}
                      >
                        {safeTranslate(translate, 'student.fields.gender.male', 'Male')}
                      </Chip>
                      <Chip
                        key="Female"
                        selected={formData.gender === "Female"}
                        onPress={() => setFormData({ ...formData, gender: "Female" })}
                        style={[styles.chip, formData.gender === "Female" && styles.selectedChip]}
                        icon="gender-female"
                        selectedColor={'#1976d2'}
                      >
                        {safeTranslate(translate, 'student.fields.gender.female', 'Female')}
                      </Chip>
                      <Chip
                        key="Other"
                        selected={formData.gender === "Other"}
                        onPress={() => setFormData({ ...formData, gender: "Other" })}
                        style={[styles.chip, formData.gender === "Other" && styles.selectedChip]}
                        icon="gender-non-binary"
                        selectedColor={'#1976d2'}
                      >
                        {safeTranslate(translate, 'student.fields.gender.other', 'Other')}
                      </Chip>
                    </View>
                  </View>
                </Surface>

                <Surface style={styles.sectionSurface}>
                  <SimpleTextInput
                    label={safeTranslate(translate, 'student.fields.admissionNumber', 'Admission Number')}
                    value={formData.admissionNumber}
                    disabled
                    left={<TextInput.Icon icon="identifier" />}
                  />
                </Surface>

                <Surface style={styles.sectionSurface}>
                  <SimpleTextInput
                    label={safeTranslate(translate, 'student.fields.phone', 'Phone Number')}
                    value={formData.phone}
                    onChangeText={(text) => setFormData({ ...formData, phone: text })}
                    keyboardType="phone-pad"
                    left={<TextInput.Icon icon="phone" />}
                  />
                  <SimpleTextInput
                    label={safeTranslate(translate, 'student.fields.emergencyContact', 'Emergency Contact')}
                    value={formData.emergencyContact}
                    onChangeText={(text) => setFormData({ ...formData, emergencyContact: text })}
                    keyboardType="phone-pad"
                    left={<TextInput.Icon icon="phone-alert" />}
                  />
                </Surface>

                <Title style={styles.sectionTitle}>Class Assignment</Title>
                <Menu
                  visible={showClassMenu}
                  onDismiss={() => setShowClassMenu(false)}
                  anchor={
                    <Button
                      mode="outlined"
                      onPress={() => setShowClassMenu(true)}
                      style={styles.menuButton}
                    >
                      {getClassName(formData.classId) === 'Select Class' ? safeTranslate(translate, 'student.selectClass', 'Select Class') : getClassName(formData.classId)}
                    </Button>
                  }
                >
                  {classes.map((classItem) => (
                    <Menu.Item
                      key={classItem.id}
                      onPress={() => {
                        setFormData({ ...formData, classId: classItem.id, section: '' });
                        setShowClassMenu(false);
                      }}
                      title={classItem.name}
                    />
                  ))}
                </Menu>

                <Menu
                  visible={showSectionMenu}
                  onDismiss={() => setShowSectionMenu(false)}
                  anchor={
                    <Button
                      mode="outlined"
                      onPress={() => setShowSectionMenu(true)}
                      style={styles.menuButton}
                      disabled={!formData.classId}
                    >
                      {formData.section || safeTranslate(translate, 'student.selectSection', 'Select Section')}
                    </Button>
                  }
                >
                  {sections.map((section, index) => (
                    <Menu.Item
                      key={index}
                      onPress={() => {
                        setFormData({ ...formData, section });
                        setShowSectionMenu(false);
                      }}
                      title={section}
                    />
                  ))}
                </Menu>

                <Title style={styles.sectionTitle}>{safeTranslate(translate, 'student.address', 'Address')}</Title>
                <SimpleTextInput
                  label={safeTranslate(translate, 'student.address.street', 'Street')}
                  value={formData.address.street}
                  onChangeText={(text) =>
                    setFormData({
                      ...formData,
                      address: { ...formData.address, street: text },
                    })
                  }
                />
                <SimpleTextInput
                  label={safeTranslate(translate, 'student.address.city', 'City')}
                  value={formData.address.city}
                  onChangeText={(text) =>
                    setFormData({
                      ...formData,
                      address: { ...formData.address, city: text },
                    })
                  }
                />
                <SimpleTextInput
                  label={safeTranslate(translate, 'student.address.state', 'State/Region')}
                  value={formData.address.state}
                  onChangeText={(text) =>
                    setFormData({
                      ...formData,
                      address: { ...formData.address, state: text },
                    })
                  }
                />
                <SimpleTextInput
                  label={safeTranslate(translate, 'student.address.country', 'Country')}
                  value={formData.address.country}
                  onChangeText={(text) =>
                    setFormData({
                      ...formData,
                      address: { ...formData.address, country: text },
                    })
                  }
                />

                <Title style={styles.sectionTitle}>{safeTranslate(translate, 'student.parentInformation', 'Parent Information')}</Title>
                <Surface style={styles.sectionSurface}>
                  <View style={styles.parentSelectionContainer}>
                    <Text style={styles.parentSelectionTitle}>{safeTranslate(translate, 'student.parent.selectParent', 'Select Parent')}</Text>

                    {formData.parent?.id ? (
                      <View style={styles.selectedParentContainer}>
                        <View style={styles.parentInfo}>
                          <Text style={styles.parentInfoLabel}>{safeTranslate(translate, 'student.parent.linked', 'Linked Parent')}:</Text>
                          <Text style={styles.parentName}>
                            {formData.parent.firstName} {formData.parent.lastName}
                          </Text>
                          <Text style={styles.parentInfoDetail}>{safeTranslate(translate, 'student.parent.phone', 'Phone')}: {formData.parent.phone}</Text>
                          <Text style={styles.parentInfoDetail}>{safeTranslate(translate, 'student.parent.email', 'Email')}: {formData.parent.email}</Text>
                          {formData.parent.imageUrl && (
                            <Image
                              source={{ uri: formData.parent.imageUrl }}
                              style={styles.parentImage}
                            />
                          )}
                        </View>

                        <Button
                          mode="outlined"
                          onPress={() => {
                            setFormData({
                              ...formData,
                              parent: {
                                email: '',
                                firstName: '',
                                lastName: '',
                                phone: '',
                              }
                            });
                          }}
                          style={styles.unlinkParentButton}
                        >
                          {safeTranslate(translate, 'student.parent.unlink', 'Unlink Parent')}
                        </Button>

                        <Button
                          mode="contained"
                          onPress={handleViewParentDetails}
                          style={styles.viewParentButton}
                        >
                          {safeTranslate(translate, 'common.viewDetails', 'View Details')}
                        </Button>
                      </View>
                    ) : (
                      <View style={styles.parentSearchContainer}>
                        <SimpleTextInput
                          label={safeTranslate(translate, 'student.parent.email', 'Parent Email')}
                          value={formData.parent.email}
                          onChangeText={(text) => {
                            handleParentEmailChange(text);
                            setParentEmailVerified(false);
                            setParentVerificationSent(false);
                          }}
                          keyboardType="email-address"
                          style={styles.parentEmailInput}
                          left={<TextInput.Icon icon="email" />}
                          disabled={parentEmailVerified}
                        />

                        {!isEditMode && formData.parent.email && (
                          <Surface style={styles.verificationContainer}>
                            <VerificationStatus
                              email={formData.parent.email}
                              isVerified={parentEmailVerified}
                              verificationSent={parentVerificationSent}
                              onVerificationSent={() => {
                                console.log('Verification sent for parent email:', formData.parent.email);
                                setParentVerificationSent(true);
                              }}
                              onVerificationStatusChange={(status) => {
                                console.log('Parent email verification status changed to:', status);
                                setParentEmailVerified(status);
                              }}
                              userType="parent"
                            />

                            {parentVerificationSent && !parentEmailVerified && (
                              <Text style={styles.verificationMessage}>
                                {safeTranslate(translate, 'verification.checkEmail', 'Please verify your email using the button above before continuing.')}
                              </Text>
                            )}

                            {parentEmailVerified && (
                              <Text style={[styles.verificationMessage, { color: '#4CAF50' }]}>
                                {safeTranslate(translate, 'verification.verified', 'Email verified successfully!')}
                              </Text>
                            )}
                          </Surface>
                        )}

                        <Text style={styles.orText}>{safeTranslate(translate, 'common.or', 'OR')}</Text>

                        <Button
                          mode="contained"
                          onPress={handleAddNewParent}
                          style={styles.addNewParentButton}
                          icon="account-plus"
                        >
                          {safeTranslate(translate, 'student.parent.addNew', 'Add New Parent')}
                        </Button>

                        <Text style={styles.parentInstructions}>
                          {safeTranslate(translate, 'student.parent.instructions',
                            'Enter a parent email to search for an existing parent, or click "Add New Parent" to create a new parent record.')}
                        </Text>
                      </View>
                    )}
                  </View>
                </Surface>

                <Title style={styles.sectionTitle}>{safeTranslate(translate, 'student.academicDetails', 'Academic Details')}</Title>
                <SimpleTextInput
                  label={safeTranslate(translate, 'student.academicDetails.previousSchool', 'Previous School')}
                  value={formData.academicDetails.previousSchool}
                  onChangeText={(text) =>
                    setFormData({
                      ...formData,
                      academicDetails: {
                        ...formData.academicDetails,
                        previousSchool: text,
                      },
                    })
                  }
                />
                <SimpleTextInput
                  label={safeTranslate(translate, 'student.academicDetails.previousGrade', 'Previous Grade')}
                  value={formData.academicDetails.previousGrade}
                  onChangeText={(text) =>
                    setFormData({
                      ...formData,
                      academicDetails: {
                        ...formData.academicDetails,
                        previousGrade: text,
                      },
                    })
                  }
                />
                <SimpleTextInput
                  label={safeTranslate(translate, 'student.academicDetails.reasonForLeaving', 'Reason For Leaving')}
                  value={formData.academicDetails.reasonForLeaving}
                  onChangeText={(text) =>
                    setFormData({
                      ...formData,
                      academicDetails: {
                        ...formData.academicDetails,
                        reasonForLeaving: text,
                      },
                    })
                  }
                />

                <View style={styles.imageUploadContainer}>
                  <Text>{safeTranslate(translate, 'student.academicDetails.previousResultImage', 'Previous Result Image')}</Text>
                  {resultImage && (
                    <Image
                      source={{ uri: resultImage }}
                      style={styles.previewImage}
                    />
                  )}
                  <Button
                    mode="contained"
                    onPress={() => showImageSourceMenu('result')}
                    style={styles.uploadButton}
                  >
                    {resultImage ?
                      safeTranslate(translate, 'student.academicDetails.changeResultImage', 'Change Result Image') :
                      safeTranslate(translate, 'student.academicDetails.addResultImage', 'Add Result Image')
                    }
                  </Button>
                </View>
              </Animatable.View>
              )}

              {error && (
                <Text style={[styles.message, styles.error]}>{error}</Text>
              )}
              {success && (
                <Text style={[styles.message, styles.success]}>
                  {translate('student.messages.addSuccess')}
                </Text>
              )}

              <View style={styles.buttonContainer}>
                <CustomButton
                  mode="contained"
                  onPress={handleSubmit}
                  loading={loading}
                  style={styles.submitButton}
                >
                  {isEditMode ? safeTranslate(translate, 'student.update', 'Update Student') : safeTranslate(translate, 'student.add', 'Add Student')}
                </CustomButton>
              </View>
            </Card.Content>
          </Card>
        </ScrollView>

        <Dialog visible={showParentDialog} onDismiss={() => setShowParentDialog(false)}>
          <Dialog.Title>{safeTranslate(translate, 'student.parent.found', 'Parent Found')}</Dialog.Title>
          <Dialog.Content>
            <Text>{safeTranslate(translate, 'student.parent.foundMessage', 'We found an existing parent with this email:')}</Text>
            <Text style={styles.parentInfo}>
              Name: {existingParent?.firstName} {existingParent?.lastName}{'\n'}
              Email: {existingParent?.email}{'\n'}
              Phone: {existingParent?.phone}
            </Text>
            <Text>{safeTranslate(translate, 'student.parent.linkQuestion', 'Would you like to link this parent to the student?')}</Text>
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={() => setShowParentDialog(false)}>{safeTranslate(translate, 'common.cancel', 'Cancel')}</Button>
            <Button onPress={handleAddNewParent}>{safeTranslate(translate, 'student.parent.addNew', 'Add New Parent')}</Button>
            <Button mode="contained" onPress={handleLinkParent}>{safeTranslate(translate, 'student.parent.link', 'Link Parent')}</Button>
          </Dialog.Actions>
        </Dialog>

        {/* Success Dialog */}
        <Dialog
          visible={showSuccessDialog}
          onDismiss={() => {
            setShowSuccessDialog(false);
            onSuccess && onSuccess();
            onClose();
          }}
          style={styles.successDialog}
        >
          <Dialog.Title style={styles.successDialogTitle}>
            {safeTranslate(translate, 'student.messages.registrationSuccess', 'Registration Successful!')}
          </Dialog.Title>
          <Dialog.Content>
            <Animatable.View animation="bounceIn" duration={1000}>
              <MaterialCommunityIcons
                name="check-circle"
                size={80}
                color="#4CAF50"
                style={styles.successIcon}
              />
            </Animatable.View>

            <Text style={styles.successDialogText}>
              {isEditMode
                ? safeTranslate(translate, 'student.messages.updateSuccess', 'Student information has been updated successfully.')
                : safeTranslate(translate, 'student.messages.addSuccess', 'Student has been registered successfully.')}
            </Text>

            {!isEditMode && createdUserData && (
              <View style={styles.credentialsContainer}>
                <Text style={styles.credentialsTitle}>
                  {safeTranslate(translate, 'student.messages.credentials', 'Login Credentials')}
                </Text>
                <Surface style={styles.credentialsSurface}>
                  <View style={styles.credentialRow}>
                    <Text style={styles.credentialLabel}>{safeTranslate(translate, 'student.fields.email', 'Email')}:</Text>
                    <Text style={styles.credentialValue}>{createdUserData.email}</Text>
                  </View>
                  <View style={styles.credentialRow}>
                    <Text style={styles.credentialLabel}>{safeTranslate(translate, 'validation.password', 'Password')}:</Text>
                    <Text style={styles.credentialValue}>{createdUserData.password}</Text>
                  </View>
                </Surface>

                <Text style={styles.credentialsNote}>
                  {safeTranslate(translate, 'student.messages.credentialsNote', 'These credentials have been sent to the provided email address.')}
                </Text>
              </View>
            )}
          </Dialog.Content>
          <Dialog.Actions>
            <Button
              mode="contained"
              onPress={() => {
                setShowSuccessDialog(false);
                onSuccess && onSuccess();
                onClose();
              }}
              style={styles.successDialogButton}
            >
              {safeTranslate(translate, 'common.ok', 'OK')}
            </Button>
          </Dialog.Actions>
        </Dialog>

        {/* Snackbar for notifications */}
        <Snackbar
          visible={snackbarVisible}
          onDismiss={() => setSnackbarVisible(false)}
          duration={3000}
          style={styles.snackbar}
          action={{
            label: safeTranslate(translate, 'common.ok', 'OK'),
            onPress: () => setSnackbarVisible(false),
          }}
        >
          {snackbarMessage}
        </Snackbar>
      </Modal>

      {/* Parent Admission Modal */}
      <ParentAdmissionModal
        visible={showParentAdmissionModal}
        onClose={() => setShowParentAdmissionModal(false)}
        parentEmail={formData.parent.email}
        translate={translate}
        isRTL={isRTL}
      />
    </Portal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    backgroundColor: 'white',
    margin: 20,
    borderRadius: 8,
    maxHeight: '90%',
  },
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  card: {
    margin: 8,
  },
  imageContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    height: 150,
    backgroundColor: '#f0f0f0',
    borderRadius: 8,
    marginBottom: 16,
  },
  image: {
    width: '100%',
    height: '100%',
    borderRadius: 8,
  },
  form: {
    gap: 16,
  },
  sectionTitle: {
    fontSize: 18,
    marginTop: 16,
    marginBottom: 8,
  },
  divider: {
    marginVertical: 16,
  },
  message: {
    marginVertical: 16,
    padding: 8,
    borderRadius: 4,
  },
  error: {
    backgroundColor: '#ffebee',
    color: '#c62828',
  },
  success: {
    backgroundColor: '#e8f5e9',
    color: '#2e7d32',
  },
  buttonContainer: {
    marginTop: 24,
  },
  submitButton: {
    marginTop: 8,
  },
  menuButton: {
    marginVertical: 8,
  },
  datePickerContainer: {
    marginVertical: 8,
  },
  dateButton: {
    marginTop: 8,
  },
  genderContainer: {
    marginVertical: 8,
  },
  chipContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 8,
    gap: 8,
  },
  chip: {
    marginRight: 8,
  },
  imagePreviewContainer: {
    position: 'relative',
    width: '100%',
    height: '100%',
  },
  previewImage: {
    width: 200,
    height: 200,
    marginVertical: 10,
    borderRadius: 10,
    resizeMode: 'cover',
  },
  removeImageButton: {
    position: 'absolute',
    top: -8,
    right: -8,
    backgroundColor: 'white',
  },
  uploadButton: {
    marginTop: 10,
  },
  imageUploadContainer: {
    marginVertical: 10,
    alignItems: 'center',
  },
  parentSection: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  parentInput: {
    flex: 1,
    marginRight: 10,
  },
  viewParentButton: {
    marginLeft: 10,
  },
  parentInfo: {
    padding: 10,
    backgroundColor: '#f5f5f5',
    borderRadius: 4,
    marginBottom: 15,
  },
  parentName: {
    fontSize: 16,
    fontWeight: 'bold',
    marginVertical: 5,
  },
  parentImage: {
    width: 60,
    height: 60,
    borderRadius: 30,
    marginTop: 10,
  },
  parentPlaceholder: {
    padding: 10,
    backgroundColor: '#f5f5f5',
    borderRadius: 4,
    marginBottom: 15,
    alignItems: 'center',
  },

  // Email verification styles
  emailVerificationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  verificationStatus: {
    marginLeft: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  verifiedChip: {
    backgroundColor: 'rgba(76, 175, 80, 0.1)',
    borderColor: '#4CAF50',
  },
  verifyButton: {
    backgroundColor: '#2196F3',
  },
  verificationContainer: {
    marginTop: 8,
    marginBottom: 16,
    padding: 12,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: '#2196F3',
  },
  verificationMessage: {
    marginTop: 8,
    fontSize: 14,
    color: '#666',
    fontStyle: 'italic',
  },

  // Success dialog styles
  successDialog: {
    borderRadius: 16,
    backgroundColor: 'white',
    padding: 8,
  },
  successDialogTitle: {
    textAlign: 'center',
    fontSize: 20,
    fontWeight: 'bold',
    color: '#4CAF50',
  },
  successIcon: {
    alignSelf: 'center',
    marginVertical: 16,
  },
  successDialogText: {
    textAlign: 'center',
    fontSize: 16,
    marginBottom: 16,
  },
  credentialsContainer: {
    backgroundColor: '#f5f7fa',
    borderRadius: 8,
    padding: 16,
    marginTop: 16,
  },
  credentialsTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
    textAlign: 'center',
  },
  credentialsSurface: {
    padding: 12,
    borderRadius: 8,
    marginVertical: 8,
  },
  credentialRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: 4,
  },
  credentialLabel: {
    fontWeight: 'bold',
  },
  credentialValue: {
    fontFamily: Platform.OS === 'ios' ? 'Courier' : 'monospace',
  },
  credentialsNote: {
    fontSize: 12,
    fontStyle: 'italic',
    textAlign: 'center',
    marginTop: 8,
    color: '#666',
  },
  successDialogButton: {
    backgroundColor: '#4CAF50',
  },
  snackbar: {
    bottom: 16,
  },

  // Parent selection styles
  parentSelectionContainer: {
    padding: 16,
    borderRadius: 8,
  },
  parentSelectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 16,
    color: '#333',
  },
  selectedParentContainer: {
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
  },
  parentInfo: {
    marginBottom: 16,
  },
  parentInfoLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  parentName: {
    fontWeight: 'bold',
    fontSize: 18,
    marginVertical: 4,
    color: '#333',
  },
  parentInfoDetail: {
    fontSize: 14,
    color: '#555',
    marginVertical: 2,
  },
  parentImage: {
    width: 100,
    height: 100,
    borderRadius: 50,
    marginTop: 16,
    alignSelf: 'center',
    borderWidth: 2,
    borderColor: '#fff',
  },
  unlinkParentButton: {
    marginBottom: 8,
  },
  viewParentButton: {
    marginTop: 8,
  },
  parentSearchContainer: {
    marginVertical: 8,
  },
  parentEmailInput: {
    marginBottom: 16,
  },
  orText: {
    textAlign: 'center',
    marginVertical: 12,
    fontSize: 16,
    fontWeight: 'bold',
    color: '#666',
  },
  addNewParentButton: {
    marginVertical: 8,
  },
  parentInstructions: {
    fontSize: 14,
    color: '#666',
    fontStyle: 'italic',
    marginTop: 16,
    textAlign: 'center',
  },
});

export { StudentAdmission };
