import { db, storage } from '../config/firebase';
import { collection, getDocs, query, where, writeBatch } from 'firebase/firestore';
import { ref, uploadBytes, getDownloadURL } from 'firebase/storage';
import { encrypt } from '../utils/encryption';

class BackupService {
    static instance = null;

    constructor() {
        if (BackupService.instance) {
            return BackupService.instance;
        }
        BackupService.instance = this;

        this.criticalCollections = [
            'users',
            'students',
            'teachers',
            'grades',
            'attendance',
            'courses',
            'classes'
        ];
    }

    // Create full system backup
    async createFullBackup() {
        try {
            const timestamp = new Date().toISOString();
            const backupData = {};

            // Backup all critical collections
            for (const collectionName of this.criticalCollections) {
                const querySnapshot = await getDocs(collection(db, collectionName));
                backupData[collectionName] = [];

                querySnapshot.forEach((doc) => {
                    backupData[collectionName].push({
                        id: doc.id,
                        data: doc.data()
                    });
                });
            }

            // Encrypt backup data
            const encryptedData = encrypt(JSON.stringify(backupData));

            // Upload to Firebase Storage
            const backupRef = ref(storage, `backups/${timestamp}.enc`);
            await uploadBytes(backupRef, new Blob([encryptedData]));

            // Create backup metadata
            await this.createBackupMetadata(timestamp, 'full');

            return {
                success: true,
                timestamp,
                message: 'Full backup created successfully'
            };
        } catch (error) {
            console.error('Backup creation failed:', error);
            throw new Error('Failed to create backup');
        }
    }

    // Create incremental backup
    async createIncrementalBackup(lastBackupTime) {
        try {
            const timestamp = new Date().toISOString();
            const backupData = {};

            // Backup only modified documents since last backup
            for (const collectionName of this.criticalCollections) {
                const q = query(
                    collection(db, collectionName),
                    where('updatedAt', '>', lastBackupTime)
                );

                const querySnapshot = await getDocs(q);
                backupData[collectionName] = [];

                querySnapshot.forEach((doc) => {
                    backupData[collectionName].push({
                        id: doc.id,
                        data: doc.data()
                    });
                });
            }

            // Encrypt and store incremental backup
            const encryptedData = encrypt(JSON.stringify(backupData));
            const backupRef = ref(storage, `backups/incremental/${timestamp}.enc`);
            await uploadBytes(backupRef, new Blob([encryptedData]));

            // Create backup metadata
            await this.createBackupMetadata(timestamp, 'incremental');

            return {
                success: true,
                timestamp,
                message: 'Incremental backup created successfully'
            };
        } catch (error) {
            console.error('Incremental backup failed:', error);
            throw new Error('Failed to create incremental backup');
        }
    }

    // Create backup metadata
    async createBackupMetadata(timestamp, type) {
        await db.collection('backup_metadata').add({
            timestamp,
            type,
            status: 'completed',
            createdAt: new Date(),
            size: 0, // Will be updated after backup is complete
            collections: this.criticalCollections
        });
    }

    // Restore from backup
    async restoreFromBackup(backupId) {
        try {
            // Get backup file
            const backupRef = ref(storage, `backups/${backupId}.enc`);
            const backupUrl = await getDownloadURL(backupRef);
            const response = await fetch(backupUrl);
            const encryptedData = await response.text();

            // Decrypt backup data
            const backupData = JSON.parse(decrypt(encryptedData));

            // Start batch write
            const batch = writeBatch(db);

            // Restore each collection
            for (const [collectionName, documents] of Object.entries(backupData)) {
                for (const doc of documents) {
                    const docRef = doc(db, collectionName, doc.id);
                    batch.set(docRef, doc.data);
                }
            }

            // Commit the batch
            await batch.commit();

            return {
                success: true,
                message: 'System restored successfully from backup'
            };
        } catch (error) {
            console.error('Restore failed:', error);
            throw new Error('Failed to restore from backup');
        }
    }

    // Schedule automatic backups
    scheduleAutomaticBackups() {
        // Removed automatic backup intervals that were causing disturbance
        // Backups can be triggered manually or scheduled through external tools
        console.log('Automatic backups disabled to prevent disturbance');
    }

    // Get last backup timestamp
    async getLastBackupTimestamp() {
        const q = query(
            collection(db, 'backup_metadata'),
            orderBy('timestamp', 'desc'),
            limit(1)
        );

        const querySnapshot = await getDocs(q);
        if (querySnapshot.empty) {
            return new Date(0); // Return epoch if no backups exist
        }

        return querySnapshot.docs[0].data().timestamp;
    }
}

export default new BackupService();
