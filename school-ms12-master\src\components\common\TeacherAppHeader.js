import React, { useState } from 'react';
import { StyleSheet, View, TouchableOpacity, StatusBar, Platform, Dimensions } from 'react-native';
import { Appbar, useTheme, Badge, Menu, Text, IconButton } from 'react-native-paper';
import CloudinaryAvatar from './CloudinaryAvatar';
import { useNavigation } from '@react-navigation/native';
import { useTranslation } from '../../hooks/useTranslation';
import { useAuth } from '../../context/AuthContext';
import NotificationBadge from './NotificationBadge';
import * as Animatable from 'react-native-animatable';
import { LinearGradient } from 'expo-linear-gradient';

const TeacherAppHeader = ({
  title,
  subtitle,
  onMenuPress,
  showBackButton = false,
  onBackPress,
  showNotification = true,
  showProfile = true
}) => {
  const theme = useTheme();
  const navigation = useNavigation();
  const { t, isRTL, getTextStyle } = useTranslation();
  const { user, logout } = useAuth();
  const [profileMenuVisible, setProfileMenuVisible] = useState(false);
  const [logoutDialogVisible, setLogoutDialogVisible] = useState(false);

  // Use custom back press if provided, otherwise use default navigation.goBack()
  const handleBackPress = () => {
    if (onBackPress) {
      onBackPress();
    } else {
      navigation.goBack();
    }
  };

  // Get user initials for avatar fallback
  const getUserInitials = () => {
    if (!user || !user.displayName) return '?';

    const nameParts = user.displayName.split(' ');
    if (nameParts.length >= 2) {
      return `${nameParts[0][0]}${nameParts[1][0]}`.toUpperCase();
    }
    return nameParts[0][0].toUpperCase();
  };

  // Enhanced gradient header
  return (
    <Animatable.View animation="fadeIn" duration={500}>
      <StatusBar
        backgroundColor={theme.colors.primary}
        barStyle="light-content"
      />
      <LinearGradient
        colors={[theme.colors.primary, '#1565C0']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={styles.gradientHeader}
      >
        <Appbar.Header style={[styles.header, { backgroundColor: 'transparent' }]}>
          <Animatable.View animation={isRTL ? "fadeInRight" : "fadeInLeft"} duration={600}>
      {showBackButton ? (
        <Appbar.BackAction
          onPress={handleBackPress}
          color="white"
          style={isRTL ? styles.rightIcon : {}}
                size={26}
        />
      ) : (
        <Appbar.Action
          icon="menu"
          color="white"
          onPress={onMenuPress}
          style={isRTL ? styles.rightIcon : {}}
                size={26}
        />
      )}
          </Animatable.View>

          <Animatable.View animation="fadeInDown" duration={700} style={styles.titleContainer}>
      <Appbar.Content
        title={title}
        subtitle={subtitle}
              titleStyle={[styles.title, isRTL && styles.rtlTitle, getTextStyle({ fontSize: 18 })]}
              subtitleStyle={[styles.subtitle, isRTL && styles.rtlTitle, getTextStyle({ fontSize: 14 })]}
      />
          </Animatable.View>

          <Animatable.View animation={isRTL ? "fadeInLeft" : "fadeInRight"} duration={600} style={styles.actionsContainer}>
      {showNotification && (
        <NotificationBadge
          size={24}
          color="white"
          onPress={() => navigation.navigate('NotificationCenter')}
          containerStyle={styles.notificationContainer}
        />
      )}

            {showProfile && (
              <View>
                <TouchableOpacity
                  onPress={() => setProfileMenuVisible(true)}
                  style={styles.avatarContainer}
                >
                  <CloudinaryAvatar
                    source={user?.photoURL}
                    label={getUserInitials()}
                    size={36}
                    style={styles.avatar}
                    color="white"
                    labelStyle={styles.avatarLabel}
                  />
                </TouchableOpacity>

                <Menu
                  visible={profileMenuVisible}
                  onDismiss={() => setProfileMenuVisible(false)}
                  anchor={{
                    x: isRTL ? 20 : Platform.OS === 'web' ? window.innerWidth - 60 : Dimensions.get('window').width - 60,
                    y: Platform.OS === 'ios' ? 90 : 70
                  }}
                  contentStyle={styles.menuContent}
                >
                  <Menu.Item
                    icon="account"
                    onPress={() => {
                      setProfileMenuVisible(false);
                      navigation.navigate('ProfileManagement');
                    }}
                    title={t('teacher.dashboard.profile')}
                  />
                  <Menu.Item
                    icon="cog"
                    onPress={() => {
                      setProfileMenuVisible(false);
                      navigation.navigate('Settings');
                    }}
                    title={t('teacher.dashboard.settings')}
                  />
                  <Menu.Item
                    icon="help-circle"
                    onPress={() => {
                      setProfileMenuVisible(false);
                      navigation.navigate('Help');
                    }}
                    title={t('teacher.dashboard.help')}
                  />
                  <Menu.Item
                    icon="logout"
                    onPress={() => {
                      setProfileMenuVisible(false);
                      logout();
                    }}
                    title={t('teacher.dashboard.logout')}
                    titleStyle={styles.logoutText}
                  />
                </Menu>
              </View>
            )}
          </Animatable.View>
    </Appbar.Header>
      </LinearGradient>
    </Animatable.View>
  );
};

const styles = StyleSheet.create({
  gradientHeader: {
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
  },
  header: {
    elevation: 0,
    height: 60,
  },
  titleContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: 'white',
  },
  subtitle: {
    fontSize: 14,
    opacity: 0.9,
    color: 'rgba(255, 255, 255, 0.9)',
  },
  rtlTitle: {
    textAlign: 'right',
  },
  rightIcon: {
    marginLeft: 'auto',
  },
  actionsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  notificationContainer: {
    marginRight: 8,
  },
  avatarContainer: {
    marginHorizontal: 8,
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.5)',
    borderRadius: 20,
  },
  avatar: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  avatarLabel: {
    fontWeight: 'bold',
  },
  menuContent: {
    backgroundColor: 'white',
    borderRadius: 8,
    marginTop: 8,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
  },
  logoutText: {
    color: '#F44336',
  },
});

export default TeacherAppHeader;
