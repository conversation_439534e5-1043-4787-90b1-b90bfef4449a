import React, { useState, useEffect } from 'react';
import { View, StyleSheet, Text, Image, ActivityIndicator, Alert, TouchableOpacity } from 'react-native';
import { Button } from 'react-native-paper';
import * as ImagePicker from 'expo-image-picker';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { checkCloudinaryAvailability } from '../../utils/NetworkUtils';
import { useLanguage } from '../../context/LanguageContext';

/**
 * A test component to verify Cloudinary configuration using direct API calls
 * without any dependencies on CloudinaryService
 */
const DirectCloudinaryTest = () => {
  const [loading, setLoading] = useState(false);
  const [imageUrl, setImageUrl] = useState(null);
  const [uploadResult, setUploadResult] = useState(null);
  const [error, setError] = useState(null);

  // Cloudinary configuration
  const cloudConfig = {
    cloudName: 'dgonrknbt',
    apiKey: '173765679161433',
    apiSecret: 'TfMvTIc2j0kMeHijwsXwVgPyyGo',
    uploadPreset: 'ml_default'
  };

  // Add the translate function from useLanguage
  const { translate } = useLanguage();

  // Request camera roll permissions
  const requestPermission = async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert(
        'Permission Required',
        'Please grant camera roll permissions to upload images.',
        [{ text: 'OK' }]
      );
      return false;
    }
    return true;
  };

  // Pick an image from the camera roll
  const pickImage = async () => {
    try {
      setError(null);
      const hasPermission = await requestPermission();
      if (!hasPermission) return;

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        const selectedImage = result.assets[0];
        uploadImage(selectedImage.uri);
      }
    } catch (error) {
      console.error('Error picking image:', error);
      setError('Failed to pick image: ' + error.message);
    }
  };

  // Upload the selected image to Cloudinary
  const uploadImage = async (uri) => {
    try {
      setLoading(true);
      setError(null);

      console.log('Starting direct Cloudinary upload test');

      // First, check network connectivity
      const networkResult = await checkNetwork();
      if (!networkResult) {
        throw new Error('Cannot connect to Cloudinary API. Please check your internet connection and try again.');
      }

      // Create form data for the upload
      const formData = new FormData();

      // Prepare the image file
      const filename = uri.split('/').pop();
      const match = /\.(\w+)$/.exec(filename);
      const type = match ? `image/${match[1]}` : 'image/jpeg';

      // Create a file object from the URI
      const fileObj = {
        uri: uri,
        type: type,
        name: filename,
      };

      // Append the file to the form data
      formData.append('file', fileObj);

      // Generate timestamp for signature
      const timestamp = Math.floor(Date.now() / 1000);

      // Add upload parameters
      formData.append('timestamp', timestamp.toString());
      formData.append('api_key', cloudConfig.apiKey);
      formData.append('upload_preset', 'mobile_upload'); // Try a different preset
      formData.append('folder', 'profile_images');

      console.log('Uploading with params:', {
        timestamp: timestamp,
        apiKey: cloudConfig.apiKey.substring(0, 5) + '...',
        preset: 'mobile_upload',
        folder: 'profile_images'
      });

      // Log the upload URL
      const uploadUrl = `https://api.cloudinary.com/v1_1/${cloudConfig.cloudName}/image/upload`;
      console.log('Upload URL:', uploadUrl);

      // Make the upload request with timeout and better error handling
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

      try {
        const response = await fetch(uploadUrl, {
          method: 'POST',
          body: formData,
          headers: {
            'Accept': 'application/json',
          },
          signal: controller.signal
        });

        clearTimeout(timeoutId);

        // Get the response text for debugging
        const responseText = await response.text();
        console.log('Response status:', response.status);
        console.log('Response headers:', JSON.stringify([...response.headers.entries()]));
        console.log('Response text:', responseText);

        // Try to parse the response as JSON
        let responseData;
        try {
          responseData = JSON.parse(responseText);
        } catch (e) {
          console.error('Failed to parse response as JSON:', e);
          throw new Error(`Invalid response: ${responseText.substring(0, 100)}...`);
        }

        if (!response.ok) {
          console.error('Upload failed with response:', responseData);
          throw new Error(responseData.error?.message || `Upload failed with status: ${response.status}`);
        }

        console.log('Upload successful:', responseData);
        setUploadResult(responseData);
        setImageUrl(responseData.url || responseData.secure_url);

        Alert.alert('Success', 'Image uploaded successfully!');
      } catch (fetchError) {
        if (fetchError.name === 'AbortError') {
          throw new Error('Upload request timed out after 30 seconds');
        }
        throw fetchError;
      }
    } catch (error) {
      console.error('Error uploading image:', error);
      setError('Upload failed: ' + error.message);

      // Show a more detailed error alert
      Alert.alert(
        'Upload Failed',
        `Error: ${error.message}\n\nPlease check your internet connection and try again.`,
        [{ text: 'OK' }]
      );
    } finally {
      setLoading(false);
    }
  };

  const [networkStatus, setNetworkStatus] = useState('unknown');

  // Check network connectivity
  const checkNetwork = async () => {
    try {
      setNetworkStatus('checking');

      const result = await checkCloudinaryAvailability(cloudConfig.cloudName);

      if (result.available) {
        setNetworkStatus('connected');
        console.log('Cloudinary API is available');
        return true;
      } else {
        setNetworkStatus('disconnected');
        console.error('Cloudinary API is not available:', result.error);

        if (result.error) {
          setError(`Cloudinary API is not available: ${result.error}`);
        }

        return false;
      }
    } catch (error) {
      console.error('Network check failed:', error);
      setNetworkStatus('disconnected');
      setError(`Network check failed: ${error.message}`);
      return false;
    }
  };

  // Check network status on component mount
  useEffect(() => {
    checkNetwork();
  }, []);

  return (
    <View style={styles.container}>
      <Text style={styles.title}>{translate('Cloudinary Test')}</Text>

      <View style={styles.configContainer}>
        <Text style={styles.configTitle}>Cloudinary Configuration:</Text>
        <Text style={styles.configText}>
          Cloud Name: <Text style={styles.configValue}>{cloudConfig.cloudName}</Text>
        </Text>
        <Text style={styles.configText}>
          API Key: <Text style={styles.configValue}>{cloudConfig.apiKey.substring(0, 5)}...</Text>
        </Text>
        <Text style={styles.configText}>
          Upload Preset: <Text style={styles.configValue}>mobile_upload</Text>
        </Text>
        <Text style={styles.configText}>
          API Secret: <Text style={styles.configValue}>{cloudConfig.apiSecret.substring(0, 5)}...</Text>
        </Text>
      </View>

      <View style={styles.networkContainer}>
        <Text style={styles.networkTitle}>Network Status:</Text>
        <View style={styles.networkStatusRow}>
          <View style={[
            styles.networkIndicator,
            networkStatus === 'connected' ? styles.networkConnected :
            networkStatus === 'disconnected' ? styles.networkDisconnected :
            networkStatus === 'checking' ? styles.networkChecking :
            styles.networkUnknown
          ]} />
          <Text style={styles.networkStatusText}>
            {networkStatus === 'connected' ? 'Connected to Cloudinary' :
             networkStatus === 'disconnected' ? 'Disconnected from Cloudinary' :
             networkStatus === 'checking' ? 'Checking connection...' :
             'Unknown status'}
          </Text>
        </View>
        <Button
          mode="outlined"
          icon="refresh"
          onPress={checkNetwork}
          disabled={loading || networkStatus === 'checking'}
          style={styles.checkButton}
          labelStyle={styles.checkButtonText}
        >
          Check Connection
        </Button>
      </View>

      <View style={styles.infoContainer}>
        <Text style={styles.infoText}>
          This component uses direct API calls to Cloudinary without any dependencies.
          Make sure you have an internet connection and the Cloudinary service is available.
        </Text>
      </View>

      <Button
        mode="contained"
        icon="cloud-upload"
        onPress={pickImage}
        disabled={loading || networkStatus === 'disconnected'}
        style={styles.uploadButton}
        labelStyle={styles.uploadButtonText}
      >
        Pick and Upload Image
      </Button>

      {loading && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#0000ff" />
          <Text style={styles.loadingText}>Uploading...</Text>
        </View>
      )}

      {error && (
        <View style={styles.errorContainer}>
          <MaterialCommunityIcons name="alert-circle" size={24} color="#F44336" style={styles.errorIcon} />
          <Text style={styles.errorText}>{error}</Text>
        </View>
      )}

      {imageUrl && (
        <View style={styles.resultContainer}>
          <View style={styles.successBanner}>
            <MaterialCommunityIcons name="check-circle" size={24} color="white" />
            <Text style={styles.successText}>Upload Successful!</Text>
          </View>

          <Image source={{ uri: imageUrl }} style={styles.image} />

          <TouchableOpacity
            style={styles.copyButton}
            onPress={() => {
              Alert.alert('URL Copied', 'Image URL has been copied to clipboard');
            }}
          >
            <MaterialCommunityIcons name="content-copy" size={16} color="white" />
            <Text style={styles.copyButtonText}>Copy URL</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
  },
  configContainer: {
    backgroundColor: '#f5f5f5',
    padding: 16,
    borderRadius: 8,
    marginBottom: 20,
    width: '100%',
  },
  configTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333',
  },
  configText: {
    fontSize: 16,
    marginBottom: 8,
    color: '#555',
  },
  configValue: {
    fontWeight: 'bold',
    color: '#2196F3',
  },
  networkContainer: {
    backgroundColor: '#E8EAF6',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
    width: '100%',
  },
  networkTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
    color: '#3F51B5',
  },
  networkStatusRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  networkIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  networkConnected: {
    backgroundColor: '#4CAF50',
  },
  networkDisconnected: {
    backgroundColor: '#F44336',
  },
  networkChecking: {
    backgroundColor: '#FFC107',
  },
  networkUnknown: {
    backgroundColor: '#9E9E9E',
  },
  networkStatusText: {
    fontSize: 14,
    color: '#333',
  },
  checkButton: {
    marginTop: 8,
    borderColor: '#3F51B5',
  },
  checkButtonText: {
    color: '#3F51B5',
    fontSize: 14,
  },
  infoContainer: {
    backgroundColor: '#FFF9C4',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
    borderLeftWidth: 4,
    borderLeftColor: '#FBC02D',
    width: '100%',
  },
  infoText: {
    fontSize: 14,
    color: '#5D4037',
  },
  uploadButton: {
    marginVertical: 16,
    paddingVertical: 6,
    backgroundColor: '#3448C5',
  },
  uploadButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  loadingContainer: {
    marginTop: 20,
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
  },
  errorContainer: {
    marginTop: 20,
    padding: 16,
    backgroundColor: '#FFEBEE',
    borderRadius: 8,
    width: '100%',
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  errorIcon: {
    marginRight: 8,
  },
  errorText: {
    color: '#D32F2F',
    flex: 1,
    fontSize: 14,
    lineHeight: 20,
  },
  resultContainer: {
    marginTop: 20,
    alignItems: 'center',
    width: '100%',
    borderRadius: 12,
    overflow: 'hidden',
    backgroundColor: 'white',
    elevation: 3,
  },
  successBanner: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#4CAF50',
    width: '100%',
    padding: 12,
  },
  successText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  image: {
    width: 250,
    height: 250,
    marginVertical: 16,
  },
  copyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#3448C5',
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 4,
    marginBottom: 16,
  },
  copyButtonText: {
    color: 'white',
    fontWeight: 'bold',
    marginLeft: 8,
  },
});

export default DirectCloudinaryTest;
