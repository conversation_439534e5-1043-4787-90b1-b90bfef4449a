import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  Animated,
  TouchableOpacity,
  Text,
  Alert,
  RefreshControl,
  ActivityIndicator
} from 'react-native';
import {
  Card,
  Title,
  FAB,
  Portal,
  Modal,
  DataTable,
  Searchbar,
  List,
  Chip,
  HelperText,
  Menu,
  Button,
  Dialog,
  Surface,
  Avatar,
  IconButton,
  useTheme,
  Divider,
  Snackbar,
  ProgressBar
} from 'react-native-paper';
import { db, auth } from '../../config/firebase';
import {
  collection,
  addDoc,
  query,
  getDocs,
  doc,
  updateDoc,
  deleteDoc,
  where,
  setDoc,
  getDoc,
  limit,
  startAfter,
  orderBy
} from 'firebase/firestore';
import { createUserWithEmailAndPassword, deleteUser } from 'firebase/auth';
import CustomButton from '../../components/common/CustomButton';
import CustomInput from '../../components/common/CustomInput';
import { StudentAdmission } from './StudentAdmission';
import { useLanguage } from '../../context/LanguageContext';
import ActivityService from '../../services/ActivityService';
import { LinearGradient } from 'expo-linear-gradient';
import AdminAppHeader from '../../components/common/AdminAppHeader';
import AdminSidebar from '../../components/common/AdminSidebar';
import SidebarBackdrop from '../../components/common/SidebarBackdrop';
import * as Animatable from 'react-native-animatable';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import BulkImportModal from '../../components/admin/BulkImportModal';
import NetInfo from '@react-native-community/netinfo';

const StudentManagement = () => {
  const { translate, isRTL } = useLanguage();
  const navigation = useNavigation();

  // Data state
  const [students, setStudents] = useState([]);
  const [classes, setClasses] = useState([]);
  const [sections, setSections] = useState([]);
  const [parents, setParents] = useState([]);

  // UI state
  const [showAdmissionModal, setShowAdmissionModal] = useState(false);
  const [selectedStudent, setSelectedStudent] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [showSectionMenu, setShowSectionMenu] = useState(false);
  const [showTeacherMenu, setShowTeacherMenu] = useState(false);
  const [showSectionAssignDialog, setShowSectionAssignDialog] = useState(false);
  const [showParentAssignDialog, setShowParentAssignDialog] = useState(false);
  const [selectedClass, setSelectedClass] = useState(null);
  const [selectedSection, setSelectedSection] = useState(null);
  const [selectedParent, setSelectedParent] = useState(null);
  const [fadeAnim] = useState(new Animated.Value(0));
  const [scaleAnim] = useState(new Animated.Value(0.95));
  const [drawerOpen, setDrawerOpen] = useState(false);
  const drawerAnim = useRef(new Animated.Value(-300)).current;
  const backdropFadeAnim = useRef(new Animated.Value(0)).current;
  const [activeSidebarItem, setActiveSidebarItem] = useState('StudentManagement');
  const [fabOpen, setFabOpen] = useState(false);
  const [showBulkImportModal, setShowBulkImportModal] = useState(false);
  const [showParentMenu, setShowParentMenu] = useState(false);

  // Pagination state
  const [lastVisible, setLastVisible] = useState(null);
  const [loadingMore, setLoadingMore] = useState(false);
  const [hasMoreStudents, setHasMoreStudents] = useState(true);
  const [pageSize] = useState(20);

  // Network and error state
  const [isConnected, setIsConnected] = useState(true);
  const [errorMessage, setErrorMessage] = useState('');
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [lastUpdated, setLastUpdated] = useState(null);
  const [loadingProgress, setLoadingProgress] = useState(0);

  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 500,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        friction: 8,
        tension: 40,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  useEffect(() => {
    navigation.setOptions({
      headerShown: false,
    });
  }, [navigation]);

  const toggleDrawer = () => {
    if (drawerOpen) {
      // Close drawer
      Animated.parallel([
        Animated.timing(drawerAnim, {
          toValue: -300,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(backdropFadeAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        })
      ]).start(() => setDrawerOpen(false));
    } else {
      // Open drawer
      setDrawerOpen(true);
      Animated.parallel([
        Animated.timing(drawerAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(backdropFadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        })
      ]).start();
    }
  };



  // Function to fetch all data
  const fetchAllData = useCallback(async () => {
    try {
      setErrorMessage('');

      // Check network connectivity
      const netInfo = await NetInfo.fetch();
      setIsConnected(netInfo.isConnected);

      if (!netInfo.isConnected) {
        setErrorMessage(translate('common.noInternetConnection'));
        setSnackbarVisible(true);
        return;
      }

      // Reset pagination
      setLastVisible(null);
      setHasMoreStudents(true);

      // Fetch all data in parallel for better performance
      await Promise.all([
        fetchStudents(true),
        fetchClasses(),
        fetchParents()
      ]);

      // Update last updated timestamp
      setLastUpdated(new Date());
    } catch (error) {
      console.error('Error fetching data:', error);
      setErrorMessage(translate('common.errorFetchingData'));
      setSnackbarVisible(true);
    } finally {
      setRefreshing(false);
    }
  }, [translate]);

  // Pull-to-refresh handler
  const onRefresh = useCallback(() => {
    setRefreshing(true);
    fetchAllData();
  }, [fetchAllData]);

  // Initial data loading
  useEffect(() => {
    fetchAllData();

    // Set up network connectivity listener
    const unsubscribe = NetInfo.addEventListener(state => {
      setIsConnected(state.isConnected);

      // If connection is restored, refresh data
      if (state.isConnected && !isConnected) {
        fetchAllData();
      }
    });

    // Clean up listener on unmount
    return () => unsubscribe();
  }, [fetchAllData, isConnected]);

  // Refresh data when screen comes into focus
  useFocusEffect(
    useCallback(() => {
      fetchAllData();
    }, [fetchAllData])
  );

  const fetchSectionsForClass = async (classId) => {
    try {
      const classRef = doc(db, 'classes', classId);
      const classDoc = await getDoc(classRef);
      if (classDoc.exists()) {
        const classData = classDoc.data();
        const sectionNames = Array.isArray(classData.sections)
          ? classData.sections.map(section => typeof section === 'object' ? section.name : section)
          : [];
        setSections(sectionNames);
      }
    } catch (error) {
      console.error('Error fetching sections:', error);
    }
  };

  const handleAssignSection = async () => {
    if (!selectedStudent || !selectedClass || !selectedSection) {
      console.error('Please select a class and section');
      return;
    }

    try {
      setLoading(true);
      const studentRef = doc(db, 'users', selectedStudent.id);

      await updateDoc(studentRef, {
        classId: selectedClass,
        section: selectedSection,
        updatedAt: new Date().toISOString()
      });

      setStudents(students.map(student =>
        student.id === selectedStudent.id
          ? { ...student, classId: selectedClass, section: selectedSection }
          : student
      ));

      await ActivityService.logActivity(
        auth.currentUser.uid,
        'assignSection',
        translate('activities.assignSection'),
        translate('activities.assignSectionDesc', {
          section: selectedSection,
          name: `${selectedStudent.firstName} ${selectedStudent.lastName}`
        })
      );

      setShowSectionAssignDialog(false);
      setSelectedClass(null);
      setSelectedSection(null);
      setSelectedStudent(null);

    } catch (error) {
      console.error('Error assigning section:', error);
      console.error('Failed to assign section');
    } finally {
      setLoading(false);
    }
  };

  const fetchStudents = useCallback(async (reset = false) => {
    try {
      if (reset) {
        setLoading(true);
        setLoadingProgress(0.2);
      } else if (!hasMoreStudents) {
        return;
      } else {
        setLoadingMore(true);
      }

      // Check network connectivity
      if (!isConnected) {
        setErrorMessage(translate('common.noInternetConnection'));
        setSnackbarVisible(true);
        return;
      }

      const studentsRef = collection(db, 'users');
      let q;

      if (reset || !lastVisible) {
        // First page query
        q = query(
          studentsRef,
          where('role', '==', 'student'),
          orderBy('lastName'),
          orderBy('firstName'),
          limit(pageSize)
        );
        setLoadingProgress(0.4);
      } else {
        // Pagination query
        q = query(
          studentsRef,
          where('role', '==', 'student'),
          orderBy('lastName'),
          orderBy('firstName'),
          startAfter(lastVisible),
          limit(pageSize)
        );
      }

      const querySnapshot = await getDocs(q);
      setLoadingProgress(0.7);

      // Check if we've reached the end
      if (querySnapshot.empty || querySnapshot.docs.length < pageSize) {
        setHasMoreStudents(false);
      } else {
        // Set the last document for pagination
        setLastVisible(querySnapshot.docs[querySnapshot.docs.length - 1]);
      }

      const studentsData = [];
      querySnapshot.forEach((doc) => {
        const data = doc.data();
        studentsData.push({
          id: doc.id,
          ...data,
          dateOfBirth: data.dateOfBirth || new Date().toISOString(),
          address: data.address || {},
          parent: data.parent || {},
          academicDetails: data.academicDetails || {},
        });
      });

      setLoadingProgress(0.9);

      // Update the students list
      if (reset) {
        setStudents(studentsData);
      } else {
        setStudents(prevStudents => [...prevStudents, ...studentsData]);
      }

      setLoadingProgress(1);
      return true;
    } catch (error) {
      console.error('Error fetching students:', error);
      setErrorMessage(translate('student.errorFetchingStudents') || 'Failed to fetch students');
      setSnackbarVisible(true);
      return false;
    } finally {
      setLoading(false);
      setLoadingMore(false);
      setLoadingProgress(0);
    }
  }, [isConnected, lastVisible, hasMoreStudents, pageSize, translate]);

  const fetchClasses = useCallback(async () => {
    try {
      setLoadingProgress(0.3);

      // Check network connectivity
      if (!isConnected) {
        return false;
      }

      const classesRef = collection(db, 'classes');
      const querySnapshot = await getDocs(classesRef);

      const classesData = [];
      querySnapshot.forEach((doc) => {
        const data = doc.data();
        classesData.push({
          id: doc.id,
          name: data.name,
          capacity: data.capacity
        });
      });

      setClasses(classesData);
      setLoadingProgress(0.6);
      return true;
    } catch (error) {
      console.error('Error fetching classes:', error);
      return false;
    }
  }, [isConnected]);

  const fetchParents = useCallback(async () => {
    try {
      setLoadingProgress(0.4);

      // Check network connectivity
      if (!isConnected) {
        return false;
      }

      const parentsRef = collection(db, 'users');
      const q = query(
        parentsRef,
        where('role', '==', 'parent'),
        orderBy('lastName'),
        orderBy('firstName')
      );
      const querySnapshot = await getDocs(q);

      const parentsData = [];
      querySnapshot.forEach((doc) => {
        const data = doc.data();
        parentsData.push({
          id: doc.id,
          ...data,
          fullName: `${data.firstName || ''} ${data.lastName || ''}`.trim()
        });
      });

      setParents(parentsData);
      setLoadingProgress(0.8);
      return true;
    } catch (error) {
      console.error('Error fetching parents:', error);
      setErrorMessage(translate('student.errorFetchingParents') || 'Failed to fetch parents');
      setSnackbarVisible(true);
      return false;
    }
  }, [isConnected, translate]);

  const handleAssignParent = async () => {
    if (!selectedStudent || !selectedParent) {
      Alert.alert(
        translate('common.error') || 'Error',
        translate('student.selectParentFirst') || 'Please select a parent first'
      );
      return;
    }

    try {
      setLoading(true);

      // Get the parent data
      const parentRef = doc(db, 'users', selectedParent);
      const parentDoc = await getDoc(parentRef);

      if (!parentDoc.exists()) {
        throw new Error('Parent not found');
      }

      const parentData = parentDoc.data();

      // Update the student with parent information
      const studentRef = doc(db, 'users', selectedStudent.id);

      // Create parent info object to store in student document
      const parentInfo = {
        id: selectedParent,
        firstName: parentData.firstName || '',
        lastName: parentData.lastName || '',
        email: parentData.email || '',
        phone: parentData.phone || '',
        relationship: parentData.relationship || 'parent',
        imageUrl: parentData.imageUrl || null,
        kebeleIdUrl: parentData.kebeleIdUrl || null
      };

      // Update student document
      await updateDoc(studentRef, {
        parent: parentInfo,
        updatedAt: new Date().toISOString()
      });

      // Update parent's children array
      const currentChildren = parentData.children || [];

      // Only add if not already in the array
      if (!currentChildren.includes(selectedStudent.id)) {
        const updatedChildren = [...currentChildren, selectedStudent.id];

        await updateDoc(parentRef, {
          children: updatedChildren,
          updatedAt: new Date().toISOString()
        });
      }

      // Update local state
      setStudents(students.map(student =>
        student.id === selectedStudent.id
          ? { ...student, parent: parentInfo }
          : student
      ));

      // Log activity
      await ActivityService.logActivity(
        auth.currentUser.uid,
        'assignParent',
        translate('activities.assignParent') || 'Assigned Parent',
        translate('activities.assignParentDesc', {
          parent: `${parentData.firstName} ${parentData.lastName}`,
          student: `${selectedStudent.firstName} ${selectedStudent.lastName}`
        }) || `Assigned parent ${parentData.firstName} ${parentData.lastName} to student ${selectedStudent.firstName} ${selectedStudent.lastName}`
      );

      // Show success message
      Alert.alert(
        translate('common.success') || 'Success',
        translate('student.parentAssigned') || 'Parent assigned successfully'
      );

      // Reset state
      setShowParentAssignDialog(false);
      setSelectedParent(null);

      // Refresh student list
      fetchStudents();

    } catch (error) {
      console.error('Error assigning parent:', error);
      Alert.alert(
        translate('common.error') || 'Error',
        translate('student.errorAssigningParent') || 'Failed to assign parent'
      );
    } finally {
      setLoading(false);
    }
  };



  const handleEditStudent = (student) => {
    const preparedStudent = {
      ...student,
      address: student.address || {},
      parent: student.parent || {},
      academicDetails: student.academicDetails || {},
    };
    setSelectedStudent(preparedStudent);
    setShowAdmissionModal(true);
  };

  const handleDeleteStudent = (student) => {
    // Show confirmation dialog
    Alert.alert(
      translate('student.confirmDelete') || 'Confirm Delete',
      translate('student.deleteConfirmMessage', { name: `${student.firstName} ${student.lastName}` }) ||
        `Are you sure you want to delete ${student.firstName} ${student.lastName}? This action cannot be undone.`,
      [
        {
          text: translate('common.cancel') || 'Cancel',
          style: 'cancel'
        },
        {
          text: translate('common.delete') || 'Delete',
          style: 'destructive',
          onPress: () => deleteStudent(student)
        }
      ]
    );
  };

  const deleteStudent = async (student) => {
    try {
      setLoading(true);

      // 1. Remove student from class section
      if (student.classId && student.section) {
        const classRef = doc(db, 'classes', student.classId);
        const classDoc = await getDoc(classRef);

        if (classDoc.exists()) {
          const classData = classDoc.data();
          const sections = classData.sections || [];
          const sectionIndex = sections.findIndex(s => s.name === student.section);

          if (sectionIndex !== -1) {
            const section = sections[sectionIndex];
            const students = section.students || [];
            const updatedStudents = students.filter(id => id !== student.id);

            sections[sectionIndex] = { ...section, students: updatedStudents };
            await updateDoc(classRef, { sections });
          }
        }
      }

      // 2. Remove student from parent's children array
      if (student.parent?.id) {
        const parentRef = doc(db, 'users', student.parent.id);
        const parentDoc = await getDoc(parentRef);

        if (parentDoc.exists()) {
          const parentData = parentDoc.data();
          const children = parentData.children || [];
          const updatedChildren = children.filter(id => id !== student.id);

          await updateDoc(parentRef, {
            children: updatedChildren,
            updatedAt: new Date().toISOString()
          });
        }
      }

      // 3. Delete student document
      await deleteDoc(doc(db, 'users', student.id));

      // 4. Delete student auth account (if possible)
      try {
        // This would typically be done through a secure admin API
        // For now, we'll just delete the document
        console.log('Student auth account would be deleted here in a production environment');
      } catch (authError) {
        console.error('Error deleting student auth account:', authError);
        // Continue with the process even if this fails
      }

      // 5. Log activity
      await ActivityService.logActivity(
        auth.currentUser.uid,
        'deleteStudent',
        translate('activities.deleteStudent') || 'Deleted Student',
        translate('activities.deleteStudentDesc', {
          student: `${student.firstName} ${student.lastName}`
        }) || `Deleted student ${student.firstName} ${student.lastName}`
      );

      // 6. Update local state
      setStudents(students.filter(s => s.id !== student.id));

      // 7. Show success message
      Alert.alert(
        translate('common.success') || 'Success',
        translate('student.deleteSuccess') || 'Student deleted successfully'
      );

    } catch (error) {
      console.error('Error deleting student:', error);
      Alert.alert(
        translate('common.error') || 'Error',
        translate('student.deleteError') || 'Failed to delete student'
      );
    } finally {
      setLoading(false);
    }
  };

  // Filter students based on search query with memoization for better performance
  const getFilteredStudents = useCallback(() => {
    // If no search query, return all students
    if (!searchQuery.trim()) {
      return students;
    }

    const query = searchQuery.toLowerCase().trim();

    return students.filter(student => {
      // Search by name
      const fullName = `${student.firstName || ''} ${student.lastName || ''}`.toLowerCase();
      if (fullName.includes(query)) return true;

      // Search by email
      if (student.email?.toLowerCase().includes(query)) return true;

      // Search by admission number
      if (student.admissionNumber?.toLowerCase().includes(query)) return true;

      // Search by class
      const className = classes.find(c => c.id === student.classId)?.name?.toLowerCase() || '';
      if (className.includes(query)) return true;

      // Search by section
      if (student.section?.toLowerCase().includes(query)) return true;

      // Search by parent name
      const parentName = `${student.parent?.firstName || ''} ${student.parent?.lastName || ''}`.toLowerCase();
      if (parentName.includes(query)) return true;

      return false;
    });
  }, [students, classes, searchQuery]);

  // Memoize the filtered students for better performance
  const filteredStudents = useMemo(() => getFilteredStudents(), [getFilteredStudents]);

  const renderStudentList = useCallback(() => {

    // Show loading indicator when initially loading
    if (loading && students.length === 0) {
      return (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#1976d2" />
          <Text style={styles.loadingText}>
            {translate('student.loadingStudents') || 'Loading students...'}
          </Text>
          <ProgressBar
            progress={loadingProgress}
            color="#1976d2"
            style={styles.progressBar}
          />
        </View>
      );
    }

    // Show a message when no students are found
    if (students.length === 0 && !loading) {
      return (
        <View style={styles.noResultsContainer}>
          <MaterialCommunityIcons name="account-question" size={48} color="#9e9e9e" />
          <Text style={styles.noResultsText}>
            {translate('student.noStudentsFound') || 'No students found'}
          </Text>
          <Button
            mode="contained"
            onPress={() => setShowAdmissionModal(true)}
            style={styles.addStudentButton}
            icon="account-plus"
          >
            {translate('student.addFirstStudent') || 'Add Your First Student'}
          </Button>
        </View>
      );
    }

    // Show a message when no students match the search criteria
    if (searchQuery && filteredStudents.length === 0) {
      return (
        <View style={styles.noResultsContainer}>
          <MaterialCommunityIcons name="magnify-close" size={48} color="#9e9e9e" />
          <Text style={styles.noResultsText}>
            {translate('student.noSearchResults') || 'No students match your search criteria'}
          </Text>
          <Button
            mode="outlined"
            onPress={() => setSearchQuery('')}
            style={styles.clearSearchButton}
          >
            {translate('student.clearSearch') || 'Clear Search'}
          </Button>
        </View>
      );
    }

    return (
      <View style={styles.tableContainer}>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={true}
          style={styles.horizontalScrollView}
        >
          <View style={styles.tableWrapper}>
            <DataTable style={styles.dataTable}>
              <DataTable.Header style={styles.tableHeader}>
                <DataTable.Title style={styles.nameColumn}>{typeof translate('student.name') === 'string' ? translate('student.name') : 'Name'}</DataTable.Title>
                <DataTable.Title style={styles.emailColumn}>{typeof translate('student.email') === 'string' ? translate('student.email') : 'Email'}</DataTable.Title>
                <DataTable.Title style={styles.classColumn}>{typeof translate('student.class') === 'string' ? translate('student.class') : 'Class'}</DataTable.Title>
                <DataTable.Title style={styles.sectionColumn}>{typeof translate('student.section') === 'string' ? translate('student.section') : 'Section'}</DataTable.Title>
                <DataTable.Title style={styles.parentColumn}>{typeof translate('student.parent') === 'string' ? translate('student.parent') : 'Parent'}</DataTable.Title>
                <DataTable.Title style={styles.actionsColumn}>{typeof translate('common.actions') === 'string' ? translate('common.actions') : 'Actions'}</DataTable.Title>
              </DataTable.Header>

              <ScrollView
                style={styles.verticalScrollView}
                onScroll={({ nativeEvent }) => {
                  // Load more when reaching the end of the list
                  const { layoutMeasurement, contentOffset, contentSize } = nativeEvent;
                  const paddingToBottom = 20;
                  const isCloseToBottom = layoutMeasurement.height + contentOffset.y >= contentSize.height - paddingToBottom;

                  if (isCloseToBottom && !loadingMore && hasMoreStudents && !searchQuery) {
                    fetchStudents();
                  }
                }}
                scrollEventThrottle={400}
              >
                {filteredStudents.map((student) => (
                  <Animatable.View
                    key={student.id}
                    animation="fadeIn"
                    duration={500}
                    delay={200}
                  >
                    <DataTable.Row
                      onPress={() => handleEditStudent(student)}
                      style={styles.tableRow}
                    >
                      <DataTable.Cell style={styles.nameCell}>
                        <View style={styles.studentInfo}>
                          {student.imageUrl ? (
                            <Avatar.Image
                              size={40}
                              source={{ uri: student.imageUrl }}
                              style={styles.avatar}
                            />
                          ) : (
                            <Avatar.Text
                              size={40}
                              label={`${student.firstName?.[0] || ''}${student.lastName?.[0] || ''}`}
                              style={styles.avatar}
                            />
                          )}
                          <View style={styles.studentDetails}>
                            <Text style={styles.studentName} numberOfLines={1}>
                              {`${student.firstName || ''} ${student.lastName || ''}`}
                            </Text>
                            <Text style={styles.studentId} numberOfLines={1}>
                              {student.admissionNumber || ''}
                            </Text>
                          </View>
                        </View>
                      </DataTable.Cell>
                      <DataTable.Cell style={styles.emailCell}>
                        <Text style={styles.cellText} numberOfLines={1}>
                          {student.email || translate('common.notAvailable') || 'N/A'}
                        </Text>
                      </DataTable.Cell>
                      <DataTable.Cell style={styles.classCell}>
                        <Text style={styles.cellText} numberOfLines={1}>
                          {classes.find(c => c.id === student.classId)?.name || translate('student.notAssigned')}
                        </Text>
                      </DataTable.Cell>
                      <DataTable.Cell style={styles.sectionCell}>
                        <Text style={styles.cellText} numberOfLines={1}>
                          {student.section || translate('student.notAssigned')}
                        </Text>
                      </DataTable.Cell>
                      <DataTable.Cell style={styles.parentCell}>
                        {student.parent?.id ? (
                          <Chip
                            icon="account"
                            mode="outlined"
                            style={styles.parentChip}
                            onPress={() => {
                              // View parent details
                              Alert.alert(
                                translate('student.parentInfo') || 'Parent Information',
                                `${student.parent.firstName} ${student.parent.lastName}\n${student.parent.email || ''}${student.parent.phone ? `\n${student.parent.phone}` : ''}`,
                                [
                                  { text: translate('common.ok') || 'OK' }
                                ]
                              );
                            }}
                          >
                            {`${student.parent.firstName || ''} ${student.parent.lastName || ''}`.trim() || translate('student.parent') || 'Parent'}
                          </Chip>
                        ) : (
                          <Chip
                            icon="account-plus"
                            mode="outlined"
                            style={styles.noParentChip}
                            onPress={() => {
                              setSelectedStudent(student);
                              setSelectedParent(null);
                              setShowParentAssignDialog(true);
                            }}
                          >
                            {translate('student.noParent') || 'No Parent'}
                          </Chip>
                        )}
                      </DataTable.Cell>
                      <DataTable.Cell style={styles.actionsCell}>
                        <View style={styles.actionButtons}>
                          <IconButton
                            icon="pencil"
                            size={20}
                            onPress={() => handleEditStudent(student)}
                            style={styles.actionButton}
                          />
                          <IconButton
                            icon="account-multiple"
                            size={20}
                            onPress={() => {
                              setSelectedStudent(student);
                              setSelectedParent(student.parent?.id || null);
                              setShowParentAssignDialog(true);
                            }}
                            style={styles.actionButton}
                          />
                          <IconButton
                            icon="delete"
                            size={20}
                            color="#F44336"
                            onPress={() => handleDeleteStudent(student)}
                            style={styles.actionButton}
                          />
                        </View>
                      </DataTable.Cell>
                    </DataTable.Row>
                  </Animatable.View>
                ))}

                {/* Loading more indicator */}
                {loadingMore && (
                  <View style={styles.loadMoreContainer}>
                    <ActivityIndicator size="small" color="#1976d2" />
                    <Text style={styles.loadMoreText}>
                      {translate('student.loadingMoreStudents') || 'Loading more students...'}
                    </Text>
                  </View>
                )}

                {/* End of list indicator */}
                {!hasMoreStudents && students.length > 0 && !searchQuery && (
                  <View style={styles.endOfListContainer}>
                    <Text style={styles.endOfListText}>
                      {translate('student.endOfList') || 'End of list'}
                    </Text>
                  </View>
                )}
              </ScrollView>
            </DataTable>
          </View>
        </ScrollView>
      </View>
    );
  }, [
    loading,
    loadingMore,
    students,
    filteredStudents,
    classes,
    searchQuery,
    hasMoreStudents,
    loadingProgress,
    translate,
    getFilteredStudents,
    fetchStudents,
    handleEditStudent,
    handleDeleteStudent
  ]);

  const getClassName = (classId) => {
    const classItem = classes.find(c => c.id === classId);
    return classItem ? classItem.name : translate('student.selectClass');
  };

  useEffect(() => {
    if (selectedClass) {
      fetchSectionsForClass(selectedClass);
    } else {
      setSections([]);
    }
  }, [selectedClass]);

  // Memoize the main content to prevent unnecessary re-renders
  const renderMainContent = useMemo(() => (
    <ScrollView
      refreshControl={
        <RefreshControl
          refreshing={refreshing}
          onRefresh={onRefresh}
          colors={['#1976d2']}
          tintColor={'#1976d2'}
        />
      }
    >
      <Animatable.View animation="fadeInDown" duration={800}>
        <Surface style={styles.headerCard}>
          <LinearGradient
            colors={['#1976d2' + '20', '#f5f5f5']}
            style={styles.headerGradient}
          >
            <View style={styles.headerContent}>
              <MaterialCommunityIcons name="school" size={24} color={'#1976d2'} />
              <Title style={[styles.headerTitle, isRTL && styles.rtlText]}>
                {typeof translate('student.title') === 'string' ? translate('student.title') : 'Students'}
              </Title>
            </View>
            <Text style={[styles.headerSubtitle, isRTL && styles.rtlText]}>
              {typeof translate('student.subtitle') === 'string' ? translate('student.subtitle') : 'Manage student information'}
            </Text>

            {lastUpdated && (
              <Text style={styles.lastUpdatedText}>
                <MaterialCommunityIcons name="clock-outline" size={12} color="#757575" />
                {' '}{typeof translate('admin.dashboard.lastUpdated') === 'string' ? translate('admin.dashboard.lastUpdated') : 'Last Updated'}: {new Date(lastUpdated).toLocaleTimeString()}
              </Text>
            )}
          </LinearGradient>
        </Surface>
      </Animatable.View>

      {!isConnected && (
        <View style={styles.offlineWarning}>
          <MaterialCommunityIcons name="wifi-off" size={16} color="#f44336" />
          <Text style={styles.offlineWarningText}>{typeof translate('common.offlineMode') === 'string' ? translate('common.offlineMode') : 'Offline Mode'}</Text>
        </View>
      )}

      <Animatable.View animation="fadeInUp" duration={800} delay={300}>
        <Card style={styles.card}>
          <Card.Content>
            <View style={styles.searchContainer}>
              <View style={styles.searchBarWrapper}>
                <Searchbar
                  placeholder={typeof translate('student.searchPlaceholder') === 'string' ? translate('student.searchPlaceholder') : 'Search by name, email, class, section...'}
                  onChangeText={setSearchQuery}
                  value={searchQuery}
                  style={[styles.searchBar, isRTL && styles.searchBarRTL]}
                  iconColor={'#1976d2'}
                  inputStyle={isRTL ? { textAlign: 'right' } : {}}
                />
                {searchQuery ? (
                  <Chip
                    mode="outlined"
                    onClose={() => setSearchQuery('')}
                    style={styles.searchChip}
                  >
                    {typeof translate('student.searchingFor') === 'string' ? translate('student.searchingFor') : 'Searching for'}: {searchQuery}
                  </Chip>
                ) : null}

                {searchQuery ? (
                  <Text style={styles.searchResultsInfo}>
                    {typeof translate('student.searchResults', { count: filteredStudents.length }) === 'string'
                      ? translate('student.searchResults', { count: filteredStudents.length })
                      : `Found ${filteredStudents.length} student${filteredStudents.length !== 1 ? 's' : ''}`}
                  </Text>
                ) : null}
              </View>

              <View style={styles.statsContainer}>
                <Animatable.View animation="fadeIn" delay={400} style={styles.statItem}>
                  <Surface style={styles.statItemSurface}>
                    <MaterialCommunityIcons name="account-group" size={20} color={'#1976d2'} />
                    <Text style={styles.statValue}>{students.length}</Text>
                    <Text style={styles.statLabel}>{typeof translate('student.totalStudents') === 'string' ? translate('student.totalStudents') : 'Total Students'}</Text>
                  </Surface>
                </Animatable.View>

                <Animatable.View animation="fadeIn" delay={500} style={styles.statItem}>
                  <Surface style={styles.statItemSurface}>
                    <MaterialCommunityIcons name="school" size={20} color="#4caf50" />
                    <Text style={[styles.statValue, { color: '#4caf50' }]}>{classes.length}</Text>
                    <Text style={styles.statLabel}>{typeof translate('student.totalClasses') === 'string' ? translate('student.totalClasses') : 'Total Classes'}</Text>
                  </Surface>
                </Animatable.View>
              </View>
            </View>

            <Divider style={styles.divider} />
            {renderStudentList()}
          </Card.Content>
        </Card>
      </Animatable.View>
    </ScrollView>
  ), [
    refreshing,
    onRefresh,
    isRTL,
    translate,
    lastUpdated,
    isConnected,
    searchQuery,
    filteredStudents.length,
    students.length,
    classes.length,
    renderStudentList
  ]);

  return (
    <View style={styles.container}>
      {/* Sidebar */}
      <AdminSidebar
        drawerAnim={drawerAnim}
        activeSidebarItem={activeSidebarItem}
        setActiveSidebarItem={setActiveSidebarItem}
        toggleDrawer={toggleDrawer}
      />

      {/* Backdrop */}
      <SidebarBackdrop
        visible={drawerOpen}
        onPress={toggleDrawer}
        fadeAnim={backdropFadeAnim}
      />

      {/* Admin App Header */}
      <AdminAppHeader
        title={typeof translate('student.title') === 'string' ? translate('student.title') : 'Students'}
        onMenuPress={toggleDrawer}
      />

      <Animated.View
        style={[
          styles.contentContainer,
          {
            opacity: fadeAnim,
            transform: [{ scale: scaleAnim }]
          }
        ]}
      >
        {/* Loading overlay */}
        {loading && students.length > 0 && (
          <View style={styles.loadingOverlay}>
            <ActivityIndicator size="large" color="#1976d2" />
            <Text style={styles.loadingText}>{typeof translate('common.loading') === 'string' ? translate('common.loading') : 'Loading...'}</Text>
            <ProgressBar
              progress={loadingProgress}
              color="#1976d2"
              style={styles.progressBar}
            />
          </View>
        )}

        {renderMainContent}

        <StudentAdmission
          visible={showAdmissionModal}
          onClose={() => {
            setShowAdmissionModal(false);
            setSelectedStudent(null);
          }}
          onSuccess={() => {
            setShowAdmissionModal(false);
            setSelectedStudent(null);
            fetchAllData();
          }}
          student={selectedStudent}
        />

        <BulkImportModal
          visible={showBulkImportModal}
          onClose={() => setShowBulkImportModal(false)}
          onSuccess={() => {
            setShowBulkImportModal(false);
            fetchAllData();
          }}
        />

        <Dialog
          visible={showSectionAssignDialog}
          onDismiss={() => setShowSectionAssignDialog(false)}
          style={styles.dialog}
        >
          <Dialog.Title>{typeof translate('student.assignSection') === 'string' ? translate('student.assignSection') : 'Assign Section'}</Dialog.Title>
          <Dialog.Content>
            <View style={styles.dialogContent}>
              <Menu
                visible={showSectionMenu}
                onDismiss={() => setShowSectionMenu(false)}
                anchor={
                  <Button
                    mode="outlined"
                    onPress={() => setShowSectionMenu(true)}
                    style={styles.menuButton}
                  >
                    {getClassName(selectedClass)}
                  </Button>
                }
              >
                {classes.map((classItem) => (
                  <Menu.Item
                    key={classItem.id}
                    onPress={() => {
                      setSelectedClass(classItem.id);
                      setShowSectionMenu(false);
                    }}
                    title={classItem.name || ''}
                  />
                ))}
              </Menu>

              <Menu
                visible={showTeacherMenu}
                onDismiss={() => setShowTeacherMenu(false)}
                anchor={
                  <Button
                    mode="outlined"
                    onPress={() => setShowTeacherMenu(true)}
                    style={styles.menuButton}
                    disabled={!selectedClass || sections.length === 0}
                  >
                    {selectedSection || (typeof translate('student.selectSection') === 'string' ? translate('student.selectSection') : 'Select Section')}
                  </Button>
                }
              >
                {sections.map((section, index) => (
                  <Menu.Item
                    key={index}
                    onPress={() => {
                      setSelectedSection(section);
                      setShowTeacherMenu(false);
                    }}
                    title={section || ''}
                  />
                ))}
              </Menu>
            </View>
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={() => setShowSectionAssignDialog(false)}>
              {typeof translate('student.cancel') === 'string' ? translate('student.cancel') : 'Cancel'}
            </Button>
            <Button
              mode="contained"
              onPress={handleAssignSection}
              loading={loading}
              disabled={!selectedClass || !selectedSection}
            >
              {typeof translate('student.assign') === 'string' ? translate('student.assign') : 'Assign'}
            </Button>
          </Dialog.Actions>
        </Dialog>

        {/* Parent Assignment Dialog */}
        <Dialog
          visible={showParentAssignDialog}
          onDismiss={() => setShowParentAssignDialog(false)}
          style={styles.dialog}
        >
          <Dialog.Title>{typeof translate('student.assignParent') === 'string' ? translate('student.assignParent') : 'Assign Parent'}</Dialog.Title>
          <Dialog.Content>
            <View style={styles.dialogContent}>
              <Text style={styles.dialogText}>
                {typeof translate('student.selectParentForStudent') === 'string' ? translate('student.selectParentForStudent') : 'Select a parent for'} {selectedStudent ? `${selectedStudent.firstName} ${selectedStudent.lastName}` : ''}
              </Text>

              <Menu
                visible={showParentMenu}
                onDismiss={() => setShowParentMenu(false)}
                anchor={
                  <Button
                    mode="outlined"
                    onPress={() => setShowParentMenu(true)}
                    style={styles.menuButton}
                  >
                    {selectedParent ?
                      parents.find(p => p.id === selectedParent)?.fullName || (typeof translate('student.selectParent') === 'string' ? translate('student.selectParent') : 'Select Parent')
                      : (typeof translate('student.selectParent') === 'string' ? translate('student.selectParent') : 'Select Parent')}
                  </Button>
                }
              >
                {parents.map((parent) => (
                  <Menu.Item
                    key={parent.id}
                    onPress={() => {
                      setSelectedParent(parent.id);
                      setShowParentMenu(false);
                    }}
                    title={parent.fullName || `${parent.firstName || ''} ${parent.lastName || ''}`.trim()}
                  />
                ))}
              </Menu>

              <Button
                mode="text"
                onPress={() => {
                  setShowParentAssignDialog(false);
                  setShowAdmissionModal(true);
                }}
                style={styles.addNewParentButton}
                icon="account-plus"
              >
                {typeof translate('student.addNewParent') === 'string' ? translate('student.addNewParent') : 'Add New Parent'}
              </Button>
            </View>
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={() => setShowParentAssignDialog(false)}>
              {typeof translate('common.cancel') === 'string' ? translate('common.cancel') : 'Cancel'}
            </Button>
            <Button
              mode="contained"
              onPress={handleAssignParent}
              loading={loading}
              disabled={!selectedParent}
            >
              {typeof translate('student.assign') === 'string' ? translate('student.assign') : 'Assign'}
            </Button>
          </Dialog.Actions>
        </Dialog>

        {/* Single FAB Group with dropdown options */}
        <FAB.Group
          open={fabOpen}
          icon={fabOpen ? 'close' : 'plus'}
          actions={[
            {
              icon: 'account-plus',
              label: typeof translate('admin.studentManagement.addStudent') === 'string' ? translate('admin.studentManagement.addStudent') : 'Add New Student',
              onPress: () => setShowAdmissionModal(true),
              color: 'white',
              style: { backgroundColor: '#4CAF50' }
            },
            {
              icon: 'file-excel',
              label: typeof translate('admin.studentManagement.bulkImport') === 'string' ? translate('admin.studentManagement.bulkImport') : 'Bulk Import Students',
              onPress: () => setShowBulkImportModal(true),
              color: 'white',
              style: { backgroundColor: '#FF9800' }
            },
            {
              icon: 'refresh',
              label: typeof translate('common.refresh') === 'string' ? translate('common.refresh') : 'Refresh List',
              onPress: onRefresh,
              color: 'white',
              style: { backgroundColor: '#2196F3' }
            }
          ]}
          onStateChange={({ open }) => setFabOpen(open)}
          fabStyle={styles.fab}
          color="white"
        />

        {/* Error message snackbar */}
        <Snackbar
          visible={snackbarVisible}
          onDismiss={() => setSnackbarVisible(false)}
          action={{
            label: typeof translate('common.dismiss') === 'string' ? translate('common.dismiss') : 'Dismiss',
            onPress: () => setSnackbarVisible(false),
          }}
          duration={3000}
          style={styles.snackbar}
        >
          {errorMessage}
        </Snackbar>
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  contentContainer: {
    flex: 1,
  },
  // Loading overlay
  loadingOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#1976d2',
  },
  progressBar: {
    width: '50%',
    marginTop: 10,
  },

  // Loading container
  loadingContainer: {
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 200,
  },

  // Loading more indicator
  loadMoreContainer: {
    padding: 16,
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
  },
  loadMoreText: {
    marginLeft: 8,
    color: '#757575',
  },

  // End of list indicator
  endOfListContainer: {
    padding: 16,
    alignItems: 'center',
  },
  endOfListText: {
    color: '#757575',
    fontStyle: 'italic',
  },

  // Snackbar
  snackbar: {
    backgroundColor: '#323232',
  },

  // Offline warning
  offlineWarning: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#ffebee',
    padding: 8,
    borderRadius: 4,
    marginHorizontal: 16,
    marginBottom: 8,
  },
  offlineWarningText: {
    color: '#f44336',
    marginLeft: 8,
    fontSize: 12,
  },

  // Header
  headerCard: {
    marginHorizontal: 16,
    marginTop: 16,
    marginBottom: 8,
    borderRadius: 12,
    overflow: 'hidden',
    elevation: 2,
  },
  headerGradient: {
    padding: 16,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  headerSubtitle: {
    marginTop: 4,
    marginLeft: 32,
    opacity: 0.7,
  },
  lastUpdatedText: {
    fontSize: 12,
    color: '#757575',
    marginTop: 8,
    marginLeft: 32,
  },
  card: {
    marginHorizontal: 16,
    marginVertical: 8,
    borderRadius: 12,
    overflow: 'hidden',
    elevation: 2,
  },
  searchContainer: {
    marginBottom: 16,
  },
  searchBarWrapper: {
    marginBottom: 8,
  },
  searchBar: {
    marginBottom: 8,
    elevation: 2,
    borderRadius: 8,
  },
  searchBarRTL: {
    flexDirection: 'row-reverse',
  },
  searchChip: {
    marginBottom: 8,
    backgroundColor: '#E3F2FD',
    borderColor: '#2196F3',
  },
  searchResultsInfo: {
    marginBottom: 8,
    fontSize: 14,
    color: '#666',
    fontStyle: 'italic',
  },
  noResultsContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 32,
  },
  noResultsText: {
    fontSize: 16,
    color: '#666',
    marginTop: 16,
    marginBottom: 16,
    textAlign: 'center',
  },
  clearSearchButton: {
    marginTop: 8,
  },
  divider: {
    marginBottom: 16,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginVertical: 8,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
    padding: 4,
  },
  statItemSurface: {
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    elevation: 1,
    backgroundColor: 'white',
    width: '90%',
  },
  statValue: {
    fontSize: 18,
    fontWeight: 'bold',
    marginVertical: 4,
    color: '#2196F3',
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 16,
    bottom: 16,
    backgroundColor: '#4CAF50',
    elevation: 12,
    zIndex: 999,
    borderRadius: 28,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.30,
    shadowRadius: 4.65,
  },
  dialog: {
    borderRadius: 8,
  },
  dialogContent: {
    gap: 16,
  },
  menuButton: {
    marginVertical: 8,
  },
  row: {
    paddingVertical: 8,
  },
  studentInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  avatar: {
    backgroundColor: '#e0e0e0',
  },
  studentName: {
    fontWeight: 'bold',
  },
  studentEmail: {
    fontSize: 12,
    color: '#666',
  },
  tableContainer: {
    flex: 1,
    marginHorizontal: 8,
  },
  horizontalScrollView: {
    width: '100%',
  },
  tableWrapper: {
    minWidth: '100%',
  },
  dataTable: {
    width: '100%',
  },
  verticalScrollView: {
    maxHeight: 500,
  },
  tableHeader: {
    backgroundColor: '#f5f5f5',
    elevation: 2,
  },
  tableRow: {
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  nameColumn: {
    flex: 5,
  },
  emailColumn: {
    flex: 2,
  },
  classColumn: {
    width: '10%',
    paddingRight: 0,
  },
  sectionColumn: {
    width: '10%',
    paddingRight: 0,
  },
  parentColumn: {
    width: '20%',
    paddingRight: 0,
  },
  actionsColumn: {
    width: '10%',
    paddingRight: 0,
  },
  nameCell: {
    flex: 5,
    paddingVertical: 8,
  },
  emailCell: {
    flex: 2,
    paddingVertical: 8,
  },
  classCell: {
    width: '10%',
    paddingVertical: 8,
    paddingRight: 0,
  },
  sectionCell: {
    width: '10%',
    paddingVertical: 8,
    paddingRight: 0,
  },
  parentCell: {
    width: '20%',
    paddingVertical: 8,
    paddingRight: 0,
  },
  actionsCell: {
    width: '10%',
    paddingVertical: 8,
    paddingRight: 0,
  },
  parentChip: {
    backgroundColor: '#E3F2FD',
    borderColor: '#2196F3',
  },
  noParentChip: {
    backgroundColor: '#FFEBEE',
    borderColor: '#F44336',
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
  },
  actionButton: {
    margin: 0,
  },
  dialogText: {
    marginBottom: 16,
  },
  addNewParentButton: {
    marginTop: 16,
  },
  studentDetails: {
    flex: 1,
  },
  studentId: {
    fontSize: 12,
    color: '#666',
  },
  cellText: {
    fontSize: 14,
    color: '#000',
  },
  rtlText: {
    textAlign: 'right',
  },
});

export default StudentManagement;
