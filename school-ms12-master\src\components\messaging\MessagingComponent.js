import React from 'react';
import { View } from 'react-native';
import Communication<PERSON>enter from '../../screens/admin/CommunicationCenter';

/**
 * A reusable messaging component that can be used across all dashboards
 * 
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.customHeader - Custom header component
 * @param {React.ReactNode} props.customSidebar - Custom sidebar component
 * @param {string} props.userRole - The role of the current user ('admin', 'teacher', 'student', 'parent')
 * @returns {React.ReactNode}
 */
const MessagingComponent = ({ customHeader, customSidebar, userRole }) => {
  return (
    <View style={{ flex: 1 }}>
      <CommunicationCenter 
        customHeader={customHeader}
        customSidebar={customSidebar}
        userRole={userRole}
      />
    </View>
  );
};

export default MessagingComponent;
