import React from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import { Card, Text, Avatar, Divider, useTheme } from 'react-native-paper';
import { formatDistanceToNow } from 'date-fns';
import * as Animatable from 'react-native-animatable';

const NotificationCard = ({
  notification,
  onPress,
  animation = 'fadeIn',
  delay = 0,
  duration = 500
}) => {
  // No theme needed

  // Format timestamp
  const formatTimestamp = (timestamp) => {
    if (!timestamp) return 'Just now';

    try {
      const date = typeof timestamp === 'string'
        ? new Date(timestamp)
        : timestamp.toDate ? timestamp.toDate() : new Date(timestamp);

      return formatDistanceToNow(date, { addSuffix: true });
    } catch (error) {
      console.error('Error formatting timestamp:', error);
      return 'Recently';
    }
  };

  // Get notification icon based on type
  const getNotificationIcon = (type) => {
    switch (type) {
      case 'grade_published':
        return 'book-open-page-variant';
      case 'grade_approval':
        return 'check-circle';
      case 'grade_rejection':
        return 'close-circle';
      case 'grade_submission':
        return 'clipboard-check';
      case 'message':
        return 'message-text';
      case 'event':
        return 'calendar';
      case 'attendance':
        return 'account-check';
      case 'academic':
        return 'school';
      default:
        return 'bell';
    }
  };

  // Get notification color based on type
  const getNotificationColor = (type) => {
    switch (type) {
      case 'grade_published':
        return '#4CAF50';
      case 'grade_approval':
        return '#2196F3';
      case 'grade_rejection':
        return '#F44336';
      case 'grade_submission':
        return '#FF9800';
      case 'message':
        return '#9C27B0';
      case 'event':
        return '#3F51B5';
      case 'attendance':
        return '#00BCD4';
      case 'academic':
        return '#009688';
      default:
        return '#1976d2';
    }
  };

  // Get notification background color based on read status
  const getBackgroundColor = () => {
    return notification.read ? '#ffffff' : '#f5f5f5';
  };

  return (
    <Animatable.View
      animation={animation}
      duration={duration}
      delay={delay}
    >
      <TouchableOpacity onPress={() => onPress && onPress(notification)}>
        <Card
          style={[
            styles.card,
            { backgroundColor: getBackgroundColor() }
          ]}
          elevation={notification.read ? 1 : 3}
        >
          <Card.Content style={styles.content}>
            <Avatar.Icon
              size={40}
              icon={getNotificationIcon(notification.type)}
              style={[
                styles.icon,
                { backgroundColor: getNotificationColor(notification.type) }
              ]}
            />

            <View style={styles.textContainer}>
              <Text style={styles.title} numberOfLines={1}>
                {notification.title}
              </Text>

              <Text style={styles.body} numberOfLines={2}>
                {notification.body}
              </Text>

              <Text style={styles.timestamp}>
                {formatTimestamp(notification.timestamp)}
              </Text>
            </View>

            {!notification.read && (
              <View style={styles.unreadIndicator} />
            )}
          </Card.Content>
        </Card>
      </TouchableOpacity>
      <Divider style={styles.divider} />
    </Animatable.View>
  );
};

const styles = StyleSheet.create({
  card: {
    marginHorizontal: 8,
    marginVertical: 4,
    borderRadius: 8
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8
  },
  icon: {
    marginRight: 12
  },
  textContainer: {
    flex: 1,
    marginRight: 8
  },
  title: {
    fontWeight: 'bold',
    fontSize: 16,
    marginBottom: 4
  },
  body: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 4
  },
  timestamp: {
    fontSize: 12,
    color: '#999999'
  },
  unreadIndicator: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: '#2196F3'
  },
  divider: {
    marginHorizontal: 16
  }
});

export default NotificationCard;
