import { registerRootComponent } from 'expo';
import { AppRegistry, LogBox, Platform, UIManager } from 'react-native';
import App from './App';

// Import polyfills early
import './src/utils/platformPolyfill';
import './src/utils/uuidPolyfill';

// Enable layout animations on Android
if (Platform.OS === 'android') {
  if (UIManager.setLayoutAnimationEnabledExperimental) {
    UIManager.setLayoutAnimationEnabledExperimental(true);
  }
}

// Ignore specific warnings
LogBox.ignoreLogs([
  'Property \'theme\' doesn\'t exist',
  'Property \'Platform\' doesn\'t exist',
  'Cannot read property \'classManagement\' of undefined',
  'Requiring unknown module',
  'Failed to load local translations',
  'TypeError: Cannot read property',
  'Possible Unhandled Promise Rejection',
  'Non-serializable values were found in the navigation state',
  'ReferenceError: Property \'Platform\' doesn\'t exist'
]);

// Make sure we register the app with all possible names to avoid registration issues
const registerApp = () => {
  try {
    // Ensuring we're only registering one component name to avoid conflicts
    // 'main' is the name used in MainActivity.java
    if (Platform.OS === 'android') {
      AppRegistry.registerComponent('main', () => App);
      console.log('Registered app with name: main');
    } else {
      // Register with Expo for iOS and other platforms
      registerRootComponent(App);
      console.log('Registered with Expo');
    }
  } catch (error) {
    console.error('Error registering app:', error);
    
    // Fallback registration if the primary method fails
    try {
      // Register with React Native fallback
      AppRegistry.registerComponent('main', () => App);
      console.log('Fallback registration with name: main');
    } catch (fallbackError) {
      console.error('Fallback registration failed:', fallbackError);
    }
  }
};

// Execute registration
registerApp();

// Log registration status
console.log('App registration complete');
