import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import AnalyticsService from '../../services/AnalyticsService';
import { collection, addDoc, query, where, getDocs, Timestamp } from 'firebase/firestore';
import { db } from '../../config/firebase';

// Mock Firebase
jest.mock('../../config/firebase', () => ({
    db: {
        collection: jest.fn()
    }
}));

jest.mock('firebase/firestore', () => ({
    collection: jest.fn(),
    addDoc: jest.fn(),
    query: jest.fn(),
    where: jest.fn(),
    getDocs: jest.fn(),
    Timestamp: {
        now: jest.fn(() => ({ seconds: 1234567890, nanoseconds: 0 })),
        fromDate: jest.fn(date => ({ seconds: date.getTime() / 1000, nanoseconds: 0 }))
    }
}));

describe('AnalyticsService', () => {
    let analyticsService;
    const mockUserId = 'test-user-id';

    beforeEach(() => {
        // Clear all mocks
        jest.clearAllMocks();
        
        // Initialize service
        analyticsService = new AnalyticsService();
        
        // Mock getCurrentUserId
        analyticsService.getCurrentUserId = jest.fn(() => mockUserId);
    });

    describe('logEvent', () => {
        it('should log an event successfully', async () => {
            const eventName = 'test_event';
            const eventData = { test: 'data' };
            
            addDoc.mockResolvedValueOnce({ id: 'test-doc-id' });

            await analyticsService.logEvent(eventName, eventData);

            expect(addDoc).toHaveBeenCalledWith(
                expect.anything(),
                expect.objectContaining({
                    name: eventName,
                    data: eventData,
                    timestamp: expect.anything(),
                    userId: mockUserId
                })
            );
        });

        it('should handle errors when logging events', async () => {
            const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
            const error = new Error('Test error');
            addDoc.mockRejectedValueOnce(error);

            await analyticsService.logEvent('test_event');

            expect(consoleSpy).toHaveBeenCalledWith('Error logging event:', error);
            consoleSpy.mockRestore();
        });
    });

    describe('trackResourceUsage', () => {
        it('should track resource usage successfully', async () => {
            const resourceType = 'library';
            const action = 'view';
            const metadata = { bookId: '123' };

            addDoc.mockResolvedValueOnce({ id: 'test-doc-id' });

            await analyticsService.trackResourceUsage(resourceType, action, metadata);

            expect(addDoc).toHaveBeenCalledWith(
                expect.anything(),
                expect.objectContaining({
                    type: 'resource_usage',
                    resourceType,
                    action,
                    metadata,
                    timestamp: expect.anything(),
                    userId: mockUserId
                })
            );
        });
    });

    describe('trackReportGeneration', () => {
        it('should track report generation successfully', async () => {
            const reportType = 'academic';
            const format = 'pdf';
            const metadata = { studentId: '123' };

            addDoc.mockResolvedValueOnce({ id: 'test-doc-id' });

            await analyticsService.trackReportGeneration(reportType, format, metadata);

            expect(addDoc).toHaveBeenCalledWith(
                expect.anything(),
                expect.objectContaining({
                    type: 'report_generation',
                    reportType,
                    format,
                    metadata,
                    timestamp: expect.anything(),
                    userId: mockUserId
                })
            );
        });
    });

    describe('getAnalytics', () => {
        it('should fetch analytics data successfully', async () => {
            const mockData = [
                { id: '1', type: 'test', data: 'test1' },
                { id: '2', type: 'test', data: 'test2' }
            ];

            getDocs.mockResolvedValueOnce({
                docs: mockData.map(doc => ({
                    id: doc.id,
                    data: () => doc
                }))
            });

            const startDate = new Date('2025-01-01');
            const endDate = new Date('2025-01-31');
            const result = await analyticsService.getAnalytics('test', startDate, endDate);

            expect(query).toHaveBeenCalledWith(
                expect.anything(),
                where('type', '==', 'test'),
                where('timestamp', '>=', startDate),
                where('timestamp', '<=', endDate)
            );
            expect(result).toHaveLength(2);
            expect(result[0]).toHaveProperty('id', '1');
        });

        it('should handle errors when fetching analytics', async () => {
            const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
            const error = new Error('Test error');
            getDocs.mockRejectedValueOnce(error);

            const result = await analyticsService.getAnalytics('test', new Date(), new Date());

            expect(consoleSpy).toHaveBeenCalledWith('Error fetching analytics:', error);
            expect(result).toEqual([]);
            consoleSpy.mockRestore();
        });
    });

    describe('aggregateAnalytics', () => {
        it('should aggregate analytics data by day', async () => {
            const mockEvents = [
                {
                    id: '1',
                    type: 'test',
                    timestamp: Timestamp.fromDate(new Date('2025-01-19T10:00:00')),
                    data: 'test1'
                },
                {
                    id: '2',
                    type: 'test',
                    timestamp: Timestamp.fromDate(new Date('2025-01-19T11:00:00')),
                    data: 'test2'
                }
            ];

            getDocs.mockResolvedValueOnce({
                docs: mockEvents.map(doc => ({
                    id: doc.id,
                    data: () => doc
                }))
            });

            const result = await analyticsService.aggregateAnalytics('test', 'day');

            expect(result).toHaveLength(1); // One day
            expect(result[0]).toHaveProperty('count', 2);
            expect(result[0]).toHaveProperty('period', '2025-1-19');
        });

        it('should handle different grouping periods', async () => {
            const mockEvents = [
                {
                    id: '1',
                    type: 'test',
                    timestamp: Timestamp.fromDate(new Date('2025-01-19T10:00:00')),
                    data: 'test1'
                }
            ];

            getDocs.mockResolvedValueOnce({
                docs: mockEvents.map(doc => ({
                    id: doc.id,
                    data: () => doc
                }))
            });

            // Test hour grouping
            const hourResult = await analyticsService.aggregateAnalytics('test', 'hour');
            expect(hourResult[0].period).toMatch(/2025-1-19 10:00/);

            // Test week grouping
            const weekResult = await analyticsService.aggregateAnalytics('test', 'week');
            expect(weekResult[0].period).toMatch(/2025-1-W3/);

            // Test month grouping
            const monthResult = await analyticsService.aggregateAnalytics('test', 'month');
            expect(monthResult[0].period).toMatch(/2025-1/);
        });
    });
});
