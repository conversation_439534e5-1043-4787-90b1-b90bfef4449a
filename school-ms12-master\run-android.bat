@echo off
echo Preparing to build and run Android app directly...

echo Checking if scripts directory exists...
if not exist scripts\NUL (
  echo Creating scripts directory...
  mkdir scripts
)

echo Checking if autolinking.gradle exists...
if not exist scripts\autolinking.gradle (
  echo Creating autolinking.gradle...
  echo // This is a placeholder for autolinking.gradle > scripts\autolinking.gradle
  echo // It's referenced in android/app/build.gradle >> scripts\autolinking.gradle
)

echo Building Android app...
cd android
call gradlew.bat assembleDebug --stacktrace
if %ERRORLEVEL% NEQ 0 (
  echo Error building Android app
  cd ..
  exit /b %ERRORLEVEL%
)

echo Installing Android app...
call gradlew.bat installDebug
if %ERRORLEVEL% NEQ 0 (
  echo Error installing Android app
  echo Make sure your device is connected and USB debugging is enabled
  cd ..
  exit /b %ERRORLEVEL%
)

cd ..
echo Starting Expo development server...
call npx expo start --android
