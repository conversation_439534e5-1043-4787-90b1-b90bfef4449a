const fs = require('fs');
const path = require('path');

// Colors to replace theme references with
const COLORS = {
  primary: '#1976d2',
  primaryDark: '#005cb2',
  accent: '#9c27b0',
  background: '#f5f5f5',
  surface: '#ffffff',
  text: '#333333',
  error: '#B00020',
  disabled: '#9e9e9e',
  placeholder: '#9E9E9E',
  backdrop: 'rgba(0, 0, 0, 0.5)',
  notification: '#f50057',
  success: '#4CAF50',
  warning: '#FF9800',
  info: '#2196F3',
  onPrimary: '#ffffff',
  onSurface: '#333333',
  onSurfaceVariant: '#666666',
  onBackground: '#333333',
};

// Function to recursively find all JS files in a directory
function findJsFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);

  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);

    if (stat.isDirectory() && file !== 'node_modules' && file !== '.git') {
      findJsFiles(filePath, fileList);
    } else if (stat.isFile() && (file.endsWith('.js') || file.endsWith('.jsx'))) {
      fileList.push(filePath);
    }
  });

  return fileList;
}

// Function to fix all theme-related issues in a file
function fixThemeIssues(filePath) {
  console.log(`Processing ${filePath}...`);
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;

  // Replace theme imports
  if (content.includes('import { useTheme }') || content.includes('import {useTheme}')) {
    content = content.replace(/import\s*{\s*useTheme\s*}.*?;/g, '// Theme import removed');
    modified = true;
  }

  // Replace theme initialization
  if (content.includes('const theme = useTheme()')) {
    content = content.replace(/const\s+theme\s*=\s*useTheme\(\);/g, '// No theme needed');
    modified = true;
  }

  // Replace theme.colors references
  for (const [key, value] of Object.entries(COLORS)) {
    // Replace theme.colors.key with hardcoded value
    const regex = new RegExp(`theme\\.colors\\.${key}`, 'g');
    if (content.match(regex)) {
      content = content.replace(regex, `'${value}'`);
      modified = true;
    }
  }

  // Replace theme.colors references with dynamic values (like theme.colors.primary + '15')
  const dynamicRegex = /theme\.colors\.(\w+)(\s*\+\s*['"]([^'"]+)['"])/g;
  let match;
  while ((match = dynamicRegex.exec(content)) !== null) {
    const colorKey = match[1];
    const suffix = match[3];
    if (COLORS[colorKey]) {
      const replacement = `'${COLORS[colorKey]}${suffix}'`;
      content = content.replace(match[0], replacement);
      modified = true;
    }
  }

  // Fix incorrect gradient color syntax
  // Case 1: colors={['#1976d2', '#1976d2'Dark || '#005cb2']}
  content = content.replace(
    /colors={\[['"]#[0-9a-fA-F]+['"],\s*['"]#[0-9a-fA-F]+'[^}]*Dark\s*\|\|\s*['"]#[0-9a-fA-F]+['"]\]}/g,
    (match) => {
      const firstColorMatch = match.match(/(['"]#[0-9a-fA-F]+['"])/);
      const fallbackColorMatch = match.match(/\|\|\s*(['"]#[0-9a-fA-F]+['"])/);
      
      if (firstColorMatch && fallbackColorMatch) {
        const firstColor = firstColorMatch[1];
        const fallbackColor = fallbackColorMatch[1];
        return `colors={[${firstColor}, ${fallbackColor}]}`;
      }
      
      return match;
    }
  );

  // Case 2: colors={['#1976d2', '#1976d2'Container]}
  content = content.replace(
    /colors={\[['"]#[0-9a-fA-F]+['"],\s*['"]#[0-9a-fA-F]+'[^}]*Container\]}/g,
    (match) => {
      const firstColorMatch = match.match(/(['"]#[0-9a-fA-F]+['"])/);
      
      if (firstColorMatch) {
        const firstColor = firstColorMatch[1];
        return `colors={[${firstColor}, '#005cb2']}`;
      }
      
      return match;
    }
  );

  // Replace LinearGradient with theme colors
  const gradientRegex = /colors={\[theme\.colors\.(\w+),\s*(['"]?[^,\]]+['"]?)\]}/g;
  while ((match = gradientRegex.exec(content)) !== null) {
    const colorKey = match[1];
    const secondColor = match[2];
    if (COLORS[colorKey]) {
      const replacement = `colors={['${COLORS[colorKey]}', ${secondColor}]}`;
      content = content.replace(match[0], replacement);
      modified = true;
    }
  }

  // Save the file if modified
  if (modified) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`Fixed theme issues in ${filePath}`);
    return true;
  }

  return false;
}

// Main function
function main() {
  const srcDir = path.join(__dirname, 'src');
  const jsFiles = findJsFiles(srcDir);
  let fixedCount = 0;

  jsFiles.forEach(file => {
    if (fixThemeIssues(file)) {
      fixedCount++;
    }
  });

  console.log(`\nFixed theme issues in ${fixedCount} files.`);
}

main();
