import React from 'react';
import { View, StyleSheet, SafeAreaView, StatusBar } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import TeacherAppHeader from './TeacherAppHeader';
import TeacherSidebar from './TeacherSidebar';
import SidebarBackdrop from './SidebarBackdrop';
import { useTeacherSidebar } from '../../context/TeacherSidebarContext';

/**
 * A wrapper component for teacher screens that includes the header and sidebar
 * 
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - The screen content
 * @param {string} props.title - The screen title
 * @param {string} props.subtitle - The screen subtitle (optional)
 * @param {boolean} props.showBack - Whether to show the back button
 * @param {boolean} props.showNotification - Whether to show the notification icon
 * @param {boolean} props.showProfile - Whether to show the profile icon
 * @returns {React.ReactElement} The TeacherScreenWrapper component
 */
const TeacherScreenWrapper = ({ 
  children, 
  title, 
  subtitle,
  showBack = false,
  showNotification = true,
  showProfile = true
}) => {
  const navigation = useNavigation();
  const { 
    drawerOpen, 
    activeSidebarItem, 
    setActiveSidebarItem, 
    drawerAnim, 
    backdropFadeAnim, 
    toggleDrawer 
  } = useTeacherSidebar();

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar backgroundColor={'#1976d2'} barStyle="light-content" />
      
      {/* Teacher App Header */}
      <TeacherAppHeader
        title={title}
        subtitle={subtitle}
        onMenuPress={toggleDrawer}
        showBackButton={showBack}
        showNotification={showNotification}
        showProfile={showProfile}
      />
      
      {/* Teacher Sidebar */}
      <TeacherSidebar
        visible={drawerOpen}
        drawerAnim={drawerAnim}
        toggleDrawer={toggleDrawer}
        navigation={navigation}
        activeSidebarItem={activeSidebarItem}
        setActiveSidebarItem={setActiveSidebarItem}
      />
      
      {/* Sidebar Backdrop */}
      <SidebarBackdrop
        visible={drawerOpen}
        onPress={toggleDrawer}
        fadeAnim={backdropFadeAnim}
      />
      
      {/* Screen Content */}
      <View style={styles.content}>
        {children}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
    padding: 16,
  },
});

export default TeacherScreenWrapper;
