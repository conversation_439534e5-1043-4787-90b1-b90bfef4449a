import React from 'react';
import {
    Paper,
    Typography,
    Box,
    Grid,
    Card,
    CardContent,
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableRow,
    Chip,
    LinearProgress
} from '@mui/material';
import {
    BarChart,
    Bar,
    XAxis,
    YAxis,
    CartesianGrid,
    ResponsiveContainer,
    Legend,
    Tooltip,
    PieChart,
    Pie,
    Cell,
    RadarChart,
    PolarGrid,
    PolarAngleAxis,
    Radar
} from 'recharts';
import { useTranslation } from '../../hooks/useTranslation';
import { styled } from '@mui/material/styles';
import { sanitizeChartData, sanitizeChartDatasets } from '../../utils/ChartUtils';

const StyledPaper = styled(Paper)(({ theme }) => ({
    padding: theme.spacing(3),
    margin: theme.spacing(2)
}));

const PerformanceIndicator = styled(Box)(({ theme, performance }) => {
    const colors = {
        excellent: theme.palette.success.main,
        good: theme.palette.info.main,
        average: theme.palette.warning.main,
        poor: theme.palette.error.main
    };

    return {
        backgroundColor: colors[performance],
        color: theme.palette.common.white,
        padding: theme.spacing(1),
        borderRadius: theme.shape.borderRadius,
        textAlign: 'center'
    };
});

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042'];

const ExamAnalysisReport = ({
    studentInfo,
    examData,
    schoolInfo,
    examPeriod
}) => {
    const {
        t,
        language,
        formatDate,
        formatNumber,
        getGradeName,
        getSubjectName
    } = useTranslation();

    const getPerformanceLevel = (score) => {
        if (score >= 90) return 'excellent';
        if (score >= 80) return 'good';
        if (score >= 70) return 'average';
        return 'poor';
    };

    const renderHeader = () => (
        <Box textAlign="center" mb={3}>
            <Typography variant="h5" gutterBottom>
                {schoolInfo.name[language]}
            </Typography>
            <Typography variant="h6" gutterBottom>
                {t('exam_analysis_report')}
            </Typography>
            <Typography variant="body1">
                {t('exam_period')}: {examPeriod}
            </Typography>
        </Box>
    );

    const renderStudentInfo = () => (
        <Card variant="outlined" sx={{ mb: 3 }}>
            <CardContent>
                <Grid container spacing={2}>
                    <Grid item xs={12} md={6}>
                        <Typography variant="subtitle2" color="textSecondary">
                            {t('student_name')}
                        </Typography>
                        <Typography variant="body1" gutterBottom>
                            {studentInfo.name[language]}
                        </Typography>
                        <Typography variant="subtitle2" color="textSecondary">
                            {t('grade_section')}
                        </Typography>
                        <Typography variant="body1">
                            {getGradeName(studentInfo.grade)} - {studentInfo.section}
                        </Typography>
                    </Grid>
                    <Grid item xs={12} md={6}>
                        <Box display="flex" justifyContent="center" alignItems="center" height="100%">
                            <Box textAlign="center">
                                <Typography variant="h3">
                                    {formatNumber(examData.averageScore)}%
                                </Typography>
                                <Typography variant="body2" color="textSecondary">
                                    {t('overall_average')}
                                </Typography>
                            </Box>
                        </Box>
                    </Grid>
                </Grid>
            </CardContent>
        </Card>
    );

    const renderSubjectPerformance = () => (
        <Box mb={3}>
            <Typography variant="h6" gutterBottom>
                {t('subject_performance')}
            </Typography>
            <Grid container spacing={2}>
                {examData.subjects.map((subject, index) => (
                    <Grid item xs={12} key={index}>
                        <Card variant="outlined">
                            <CardContent>
                                <Grid container spacing={2} alignItems="center">
                                    <Grid item xs={12} md={3}>
                                        <Typography variant="subtitle1">
                                            {getSubjectName(subject.name)}
                                        </Typography>
                                    </Grid>
                                    <Grid item xs={12} md={7}>
                                        <Box mb={1}>
                                            <LinearProgress
                                                variant="determinate"
                                                value={subject.score}
                                                sx={{
                                                    height: 10,
                                                    borderRadius: 5
                                                }}
                                            />
                                        </Box>
                                        <Box display="flex" justifyContent="space-between">
                                            <Typography variant="body2">
                                                {formatNumber(subject.score)}%
                                            </Typography>
                                            <Typography variant="body2" color="textSecondary">
                                                {t('class_average')}: {formatNumber(subject.classAverage)}%
                                            </Typography>
                                        </Box>
                                    </Grid>
                                    <Grid item xs={12} md={2}>
                                        <PerformanceIndicator performance={getPerformanceLevel(subject.score)}>
                                            {t(`performance_${getPerformanceLevel(subject.score)}`)}
                                        </PerformanceIndicator>
                                    </Grid>
                                </Grid>
                            </CardContent>
                        </Card>
                    </Grid>
                ))}
            </Grid>
        </Box>
    );

    const renderPerformanceAnalysis = () => (
        <Box mb={3}>
            <Typography variant="h6" gutterBottom>
                {t('performance_analysis')}
            </Typography>
            <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                    <Card variant="outlined">
                        <CardContent>
                            <Typography variant="subtitle1" gutterBottom>
                                {t('score_distribution')}
                            </Typography>
                            <Box height={300}>
                                <ResponsiveContainer width="100%" height="100%">
                                    <PieChart>
                                        <Pie
                                            data={examData.scoreDistribution}
                                            dataKey="value"
                                            nameKey="range"
                                            cx="50%"
                                            cy="50%"
                                            label={({ name, percent }) => 
                                                `${name} ${(percent * 100).toFixed(0)}%`
                                            }
                                        >
                                            {examData.scoreDistribution.map((entry, index) => (
                                                <Cell key={index} fill={COLORS[index % COLORS.length]} />
                                            ))}
                                        </Pie>
                                        <Tooltip />
                                    </PieChart>
                                </ResponsiveContainer>
                            </Box>
                        </CardContent>
                    </Card>
                </Grid>
                <Grid item xs={12} md={6}>
                    <Card variant="outlined">
                        <CardContent>
                            <Typography variant="subtitle1" gutterBottom>
                                {t('skill_analysis')}
                            </Typography>
                            <Box height={300}>
                                <ResponsiveContainer width="100%" height="100%">
                                    <RadarChart data={examData.skillAnalysis}>
                                        <PolarGrid />
                                        <PolarAngleAxis
                                            dataKey="skill"
                                            tick={({ payload, x, y }) => (
                                                <text x={x} y={y} textAnchor="middle">
                                                    {t(`skill_${payload.value}`)}
                                                </text>
                                            )}
                                        />
                                        <Radar
                                            name={t('student_score')}
                                            dataKey="score"
                                            stroke="#8884d8"
                                            fill="#8884d8"
                                            fillOpacity={0.6}
                                        />
                                        <Radar
                                            name={t('class_average')}
                                            dataKey="average"
                                            stroke="#82ca9d"
                                            fill="#82ca9d"
                                            fillOpacity={0.6}
                                        />
                                        <Legend />
                                        <Tooltip />
                                    </RadarChart>
                                </ResponsiveContainer>
                            </Box>
                        </CardContent>
                    </Card>
                </Grid>
            </Grid>
        </Box>
    );

    const renderStrengthsWeaknesses = () => (
        <Box mb={3}>
            <Typography variant="h6" gutterBottom>
                {t('strengths_weaknesses')}
            </Typography>
            <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                    <Card variant="outlined">
                        <CardContent>
                            <Typography variant="subtitle1" gutterBottom color="success.main">
                                {t('strengths')}
                            </Typography>
                            {examData.strengths.map((strength, index) => (
                                <Box key={index} mb={1}>
                                    <Chip
                                        label={strength[language]}
                                        color="success"
                                        variant="outlined"
                                    />
                                </Box>
                            ))}
                        </CardContent>
                    </Card>
                </Grid>
                <Grid item xs={12} md={6}>
                    <Card variant="outlined">
                        <CardContent>
                            <Typography variant="subtitle1" gutterBottom color="error.main">
                                {t('areas_for_improvement')}
                            </Typography>
                            {examData.weaknesses.map((weakness, index) => (
                                <Box key={index} mb={1}>
                                    <Chip
                                        label={weakness[language]}
                                        color="error"
                                        variant="outlined"
                                    />
                                </Box>
                            ))}
                        </CardContent>
                    </Card>
                </Grid>
            </Grid>
        </Box>
    );

    const renderRecommendations = () => (
        <Box mb={3}>
            <Typography variant="h6" gutterBottom>
                {t('recommendations')}
            </Typography>
            <Card variant="outlined">
                <CardContent>
                    <Grid container spacing={2}>
                        {examData.recommendations.map((rec, index) => (
                            <Grid item xs={12} key={index}>
                                <Box mb={2}>
                                    <Typography variant="subtitle1" gutterBottom>
                                        {rec.title[language]}
                                    </Typography>
                                    <Typography variant="body2">
                                        {rec.description[language]}
                                    </Typography>
                                    <Box mt={1}>
                                        {rec.actionItems.map((item, itemIndex) => (
                                            <Chip
                                                key={itemIndex}
                                                label={item[language]}
                                                size="small"
                                                sx={{ mr: 1, mt: 1 }}
                                            />
                                        ))}
                                    </Box>
                                </Box>
                            </Grid>
                        ))}
                    </Grid>
                </CardContent>
            </Card>
        </Box>
    );

    const renderSignatures = () => (
        <Grid container spacing={4} mt={4}>
            <Grid item xs={12} md={4}>
                <Typography variant="body2" gutterBottom>
                    {t('subject_teacher')}
                </Typography>
                <Box mt={4} borderTop={1} width={200} />
                <Typography variant="body2" mt={1}>
                    {formatDate(new Date())}
                </Typography>
            </Grid>
            <Grid item xs={12} md={4}>
                <Typography variant="body2" gutterBottom>
                    {t('academic_coordinator')}
                </Typography>
                <Box mt={4} borderTop={1} width={200} />
                <Typography variant="body2" mt={1}>
                    {formatDate(new Date())}
                </Typography>
            </Grid>
            <Grid item xs={12} md={4}>
                <Typography variant="body2" gutterBottom>
                    {t('parent_signature')}
                </Typography>
                <Box mt={4} borderTop={1} width={200} />
                <Typography variant="body2" mt={1}>
                    {t('signature_date')}
                </Typography>
            </Grid>
        </Grid>
    );

    return (
        <StyledPaper>
            {renderHeader()}
            {renderStudentInfo()}
            {renderSubjectPerformance()}
            {renderPerformanceAnalysis()}
            {renderStrengthsWeaknesses()}
            {renderRecommendations()}
            {renderSignatures()}
        </StyledPaper>
    );
};

export default ExamAnalysisReport;
