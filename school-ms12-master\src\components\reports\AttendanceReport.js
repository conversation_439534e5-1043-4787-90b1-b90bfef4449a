import React from 'react';
import {
    Paper,
    Typography,
    Box,
    Grid,
    Card,
    CardContent,
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableRow,
    CircularProgress,
    Tooltip
} from '@mui/material';
import {
    <PERSON><PERSON>hart,
    Bar,
    XAxis,
    YAxis,
    CartesianGrid,
    ResponsiveContainer,
    Legend,
    LineChart,
    Line
} from 'recharts';
import { useTranslation } from '../../hooks/useTranslation';
import { styled } from '@mui/material/styles';
import CalendarHeatmap from 'react-calendar-heatmap';
import { sanitizeChartData, sanitizeChartDatasets } from '../../utils/ChartUtils';
import 'react-calendar-heatmap/dist/styles.css';

const StyledPaper = styled(Paper)(({ theme }) => ({
    padding: theme.spacing(3),
    margin: theme.spacing(2)
}));

const AttendanceIndicator = styled(Box)(({ theme, status }) => {
    const colors = {
        present: theme.palette.success.main,
        absent: theme.palette.error.main,
        late: theme.palette.warning.main,
        excused: theme.palette.info.main
    };

    return {
        width: 24,
        height: 24,
        borderRadius: '50%',
        backgroundColor: colors[status],
        display: 'inline-block',
        marginRight: theme.spacing(1)
    };
});

const AttendanceReport = ({
    studentInfo,
    attendanceData,
    schoolInfo,
    reportPeriod
}) => {
    const {
        t,
        language,
        formatDate,
        formatNumber,
        getGradeName
    } = useTranslation();

    const calculateAttendanceRate = () => {
        const total = attendanceData.records.length;
        const present = attendanceData.records.filter(r => r.status === 'present').length;
        return (present / total) * 100;
    };

    const renderHeader = () => (
        <Box textAlign="center" mb={3}>
            <Typography variant="h5" gutterBottom>
                {schoolInfo.name[language]}
            </Typography>
            <Typography variant="h6" gutterBottom>
                {t('attendance_report')}
            </Typography>
            <Typography variant="body1">
                {t('report_period')}: {reportPeriod}
            </Typography>
        </Box>
    );

    const renderStudentInfo = () => (
        <Card variant="outlined" sx={{ mb: 3 }}>
            <CardContent>
                <Grid container spacing={2}>
                    <Grid item xs={12} md={6}>
                        <Typography variant="subtitle2" color="textSecondary">
                            {t('student_name')}
                        </Typography>
                        <Typography variant="body1" gutterBottom>
                            {studentInfo.name[language]}
                        </Typography>
                        <Typography variant="subtitle2" color="textSecondary">
                            {t('grade_section')}
                        </Typography>
                        <Typography variant="body1">
                            {getGradeName(studentInfo.grade)} - {studentInfo.section}
                        </Typography>
                    </Grid>
                    <Grid item xs={12} md={6}>
                        <Box display="flex" alignItems="center" justifyContent="center" height="100%">
                            <Box textAlign="center">
                                <CircularProgress
                                    variant="determinate"
                                    value={calculateAttendanceRate()}
                                    size={80}
                                    thickness={4}
                                />
                                <Typography variant="h6" mt={1}>
                                    {formatNumber(calculateAttendanceRate())}%
                                </Typography>
                                <Typography variant="body2" color="textSecondary">
                                    {t('attendance_rate')}
                                </Typography>
                            </Box>
                        </Box>
                    </Grid>
                </Grid>
            </CardContent>
        </Card>
    );

    const renderAttendanceSummary = () => (
        <Box mb={3}>
            <Typography variant="h6" gutterBottom>
                {t('attendance_summary')}
            </Typography>
            <Grid container spacing={2}>
                {Object.entries(attendanceData.summary).map(([status, count]) => (
                    <Grid item xs={6} sm={3} key={status}>
                        <Card>
                            <CardContent>
                                <Box display="flex" alignItems="center" mb={1}>
                                    <AttendanceIndicator status={status} />
                                    <Typography variant="body2">
                                        {t(`attendance_${status}`)}
                                    </Typography>
                                </Box>
                                <Typography variant="h4">
                                    {formatNumber(count)}
                                </Typography>
                                <Typography variant="body2" color="textSecondary">
                                    {t('days')}
                                </Typography>
                            </CardContent>
                        </Card>
                    </Grid>
                ))}
            </Grid>
        </Box>
    );

    const renderMonthlyTrend = () => (
        <Box mb={3}>
            <Typography variant="h6" gutterBottom>
                {t('monthly_attendance_trend')}
            </Typography>
            <Card variant="outlined">
                <CardContent>
                    <Box height={300}>
                        <ResponsiveContainer width="100%" height="100%">
                            <LineChart data={sanitizeChartDatasets(attendanceData.monthlyTrend)}>
                                <CartesianGrid strokeDasharray="3 3" />
                                <XAxis
                                    dataKey="month"
                                    tickFormatter={(value) => t(`month_${value}`)}
                                />
                                <YAxis />
                                <Legend />
                                <Line
                                    type="monotone"
                                    dataKey="attendanceRate"
                                    name={t('attendance_rate')}
                                    stroke="#8884d8"
                                />
                            </LineChart>
                        </ResponsiveContainer>
                    </Box>
                </CardContent>
            </Card>
        </Box>
    );

    const renderAttendanceCalendar = () => (
        <Box mb={3}>
            <Typography variant="h6" gutterBottom>
                {t('attendance_calendar')}
            </Typography>
            <Card variant="outlined">
                <CardContent>
                    <CalendarHeatmap
                        startDate={new Date(attendanceData.startDate)}
                        endDate={new Date(attendanceData.endDate)}
                        values={attendanceData.records.map(record => ({
                            date: record.date,
                            count: record.status === 'present' ? 1 : 0
                        }))}
                        classForValue={(value) => {
                            if (!value) return 'color-empty';
                            return `color-scale-${value.count}`;
                        }}
                        titleForValue={(value) => {
                            if (!value) return '';
                            return `${formatDate(value.date)}: ${
                                value.count ? t('present') : t('absent')
                            }`;
                        }}
                    />
                </CardContent>
            </Card>
        </Box>
    );

    const renderDetailedRecords = () => (
        <Box mb={3}>
            <Typography variant="h6" gutterBottom>
                {t('detailed_attendance_records')}
            </Typography>
            <Table>
                <TableHead>
                    <TableRow>
                        <TableCell>{t('date')}</TableCell>
                        <TableCell>{t('status')}</TableCell>
                        <TableCell>{t('time')}</TableCell>
                        <TableCell>{t('notes')}</TableCell>
                    </TableRow>
                </TableHead>
                <TableBody>
                    {attendanceData.records.map((record, index) => (
                        <TableRow key={index}>
                            <TableCell>{formatDate(record.date)}</TableCell>
                            <TableCell>
                                <Box display="flex" alignItems="center">
                                    <AttendanceIndicator status={record.status} />
                                    {t(`attendance_${record.status}`)}
                                </Box>
                            </TableCell>
                            <TableCell>{record.time}</TableCell>
                            <TableCell>{record.notes[language]}</TableCell>
                        </TableRow>
                    ))}
                </TableBody>
            </Table>
        </Box>
    );

    const renderRecommendations = () => {
        if (calculateAttendanceRate() < 80) {
            return (
                <Box mb={3}>
                    <Typography variant="h6" gutterBottom color="error">
                        {t('attendance_warning')}
                    </Typography>
                    <Card variant="outlined">
                        <CardContent>
                            <Typography variant="body1" paragraph>
                                {t('attendance_warning_message')}
                            </Typography>
                            <Typography variant="subtitle1" gutterBottom>
                                {t('recommended_actions')}:
                            </Typography>
                            <ul>
                                {attendanceData.recommendations.map((rec, index) => (
                                    <li key={index}>
                                        <Typography variant="body2">
                                            {rec[language]}
                                        </Typography>
                                    </li>
                                ))}
                            </ul>
                        </CardContent>
                    </Card>
                </Box>
            );
        }
        return null;
    };

    const renderSignatures = () => (
        <Grid container spacing={4} mt={4}>
            <Grid item xs={12} md={4}>
                <Typography variant="body2" gutterBottom>
                    {t('class_teacher')}
                </Typography>
                <Box mt={4} borderTop={1} width={200} />
                <Typography variant="body2" mt={1}>
                    {formatDate(new Date())}
                </Typography>
            </Grid>
            <Grid item xs={12} md={4}>
                <Typography variant="body2" gutterBottom>
                    {t('attendance_officer')}
                </Typography>
                <Box mt={4} borderTop={1} width={200} />
                <Typography variant="body2" mt={1}>
                    {formatDate(new Date())}
                </Typography>
            </Grid>
            <Grid item xs={12} md={4}>
                <Typography variant="body2" gutterBottom>
                    {t('parent_signature')}
                </Typography>
                <Box mt={4} borderTop={1} width={200} />
                <Typography variant="body2" mt={1}>
                    {t('signature_date')}
                </Typography>
            </Grid>
        </Grid>
    );

    return (
        <StyledPaper>
            {renderHeader()}
            {renderStudentInfo()}
            {renderAttendanceSummary()}
            {renderMonthlyTrend()}
            {renderAttendanceCalendar()}
            {renderDetailedRecords()}
            {renderRecommendations()}
            {renderSignatures()}
        </StyledPaper>
    );
};

export default AttendanceReport;
