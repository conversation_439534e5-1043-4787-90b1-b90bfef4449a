import React from 'react';
import { View, ScrollView, StyleSheet, Animated, Dimensions } from 'react-native';
import { Avatar, Title, Paragraph, Divider, List, useTheme } from 'react-native-paper';
import { LinearGradient } from 'expo-linear-gradient';
import { useLanguage } from '../../context/LanguageContext';
import { useAuth } from '../../context/AuthContext';
import LanguageSelector from './LanguageSelector';

const ParentSidebar = ({ visible, onClose, navigation, activeSidebarItem, setActiveSidebarItem, selectedChild, children, onChildSelect }) => {
  // No theme needed
  const { translate, language } = useLanguage();
  const { user, logout } = useAuth();

  // Debug log for translation
  React.useEffect(() => {
    console.log('Sidebar - Current language:', language);
    console.log('Sidebar - Translation test:', translate('parent.navigation.dashboard'));
  }, [language, translate]);

  const drawerAnim = React.useRef(new Animated.Value(visible ? 0 : -300)).current;

  React.useEffect(() => {
    Animated.timing(drawerAnim, {
      toValue: visible ? 0 : -300,
      duration: 250,
      useNativeDriver: true,
    }).start();
  }, [visible, drawerAnim]);

  const handleNavigation = (screen) => {
    if (screen === 'logout') {
      logout();
      return;
    }

    setActiveSidebarItem(screen);
    navigation.navigate(screen);

    if (Dimensions.get('window').width < 768) {
      onClose();
    }
  };

  return (
    <Animated.View
      style={[styles.sidebar, { transform: [{ translateX: drawerAnim }] }]}
    >
      <LinearGradient
        colors={['#1976d2', '#1565C0']}
        style={styles.sidebarHeader}
      >
        <View style={styles.sidebarProfile}>
          <Avatar.Icon
            size={80}
            icon="account-child"
            style={styles.sidebarAvatar}
            color="white"
            backgroundColor="rgba(255, 255, 255, 0.2)"
          />
          <Title style={styles.sidebarName}>{user?.displayName || translate('roles.parent')}</Title>
          <Paragraph style={styles.sidebarRole}>{translate('roles.parent')}</Paragraph>
        </View>
      </LinearGradient>

      <ScrollView style={styles.sidebarMenu}>
        {/* Child Selector */}
        {children && children.length > 0 && (
          <List.Section title={translate('parent.navigation.yourChildren') || 'Your Children'}>
            {children.map((child) => (
              <List.Item
                key={child.id}
                title={`${child.firstName || ''} ${child.lastName || ''}`}
                description={child.className || child.classId ? `${child.className || `Class ${child.classId}`}${child.section ? ` - ${child.section}` : ''}` : ''}
                left={props => (
                  <Avatar.Text
                    {...props}
                    size={36}
                    label={`${(child.firstName || '').charAt(0)}${(child.lastName || '').charAt(0)}`}
                    style={{ backgroundColor: selectedChild && selectedChild.id === child.id ? '#1976d2' : '#9e9e9e' }}
                  />
                )}
                onPress={() => onChildSelect && onChildSelect(child.id)}
                style={[
                  styles.childItem,
                  selectedChild && selectedChild.id === child.id && styles.selectedChildItem
                ]}
              />
            ))}
          </List.Section>
        )}

        <Divider style={styles.sidebarDivider} />

        <List.Section>
          <List.Item
            title={translate('parent.navigation.dashboard') || 'Dashboard'}
            left={props => <List.Icon {...props} icon="view-dashboard" color={activeSidebarItem === 'ParentDashboard' ? '#1976d2' : '#757575'} />}
            onPress={() => handleNavigation('ParentDashboard')}
            style={[styles.sidebarItem, activeSidebarItem === 'ParentDashboard' && styles.sidebarItemActive]}
            titleStyle={[styles.sidebarItemText, activeSidebarItem === 'ParentDashboard' && styles.sidebarItemTextActive]}
          />

          <List.Item
            title={translate('parent.navigation.academics') || 'Academics'}
            left={props => <List.Icon {...props} icon="chart-line" color={activeSidebarItem === 'ChildProgress' ? '#1976d2' : '#757575'} />}
            onPress={() => handleNavigation('ChildProgress')}
            style={[styles.sidebarItem, activeSidebarItem === 'ChildProgress' && styles.sidebarItemActive]}
            titleStyle={[styles.sidebarItemText, activeSidebarItem === 'ChildProgress' && styles.sidebarItemTextActive]}
          />

          <List.Item
            title={translate('parent.navigation.attendance') || 'Attendance'}
            left={props => <List.Icon {...props} icon="calendar-check" color={activeSidebarItem === 'StudentMonitoring' ? '#1976d2' : '#757575'} />}
            onPress={() => handleNavigation('StudentMonitoring')}
            style={[styles.sidebarItem, activeSidebarItem === 'StudentMonitoring' && styles.sidebarItemActive]}
            titleStyle={[styles.sidebarItemText, activeSidebarItem === 'StudentMonitoring' && styles.sidebarItemTextActive]}
          />

          <List.Item
            title={translate('parent.navigation.grades') || 'Grades & Results'}
            left={props => <List.Icon {...props} icon="file-document" color={activeSidebarItem === 'StudentProgressReport' ? '#1976d2' : '#757575'} />}
            onPress={() => handleNavigation('StudentProgressReport')}
            style={[styles.sidebarItem, activeSidebarItem === 'StudentProgressReport' && styles.sidebarItemActive]}
            titleStyle={[styles.sidebarItemText, activeSidebarItem === 'StudentProgressReport' && styles.sidebarItemTextActive]}
          />

          <List.Item
            title={translate('parent.navigation.examSchedule') || 'Exam Schedule'}
            left={props => <List.Icon {...props} icon="calendar-month" color={activeSidebarItem === 'CalendarManagement' ? '#1976d2' : '#757575'} />}
            onPress={() => handleNavigation('CalendarManagement')}
            style={[styles.sidebarItem, activeSidebarItem === 'CalendarManagement' && styles.sidebarItemActive]}
            titleStyle={[styles.sidebarItemText, activeSidebarItem === 'CalendarManagement' && styles.sidebarItemTextActive]}
          />

          <List.Item
            title={translate('parent.navigation.classSchedule') || 'Class Schedule'}
            left={props => <List.Icon {...props} icon="calendar-week" color={activeSidebarItem === 'ParentClassSchedule' ? '#1976d2' : '#757575'} />}
            onPress={() => handleNavigation('ParentClassSchedule')}
            style={[styles.sidebarItem, activeSidebarItem === 'ParentClassSchedule' && styles.sidebarItemActive]}
            titleStyle={[styles.sidebarItemText, activeSidebarItem === 'ParentClassSchedule' && styles.sidebarItemTextActive]}
          />

          <List.Item
            title={translate('parent.navigation.communication') || 'Communication'}
            left={props => <List.Icon {...props} icon="account-tie" color={activeSidebarItem === 'ParentTeacherCommunication' ? '#1976d2' : '#757575'} />}
            onPress={() => handleNavigation('ParentTeacherCommunication')}
            style={[styles.sidebarItem, activeSidebarItem === 'ParentTeacherCommunication' && styles.sidebarItemActive]}
            titleStyle={[styles.sidebarItemText, activeSidebarItem === 'ParentTeacherCommunication' && styles.sidebarItemTextActive]}
          />

          <List.Item
            title={translate('parent.navigation.messages') || 'Messages'}
            left={props => <List.Icon {...props} icon="message-text" color={activeSidebarItem === 'ParentMessaging' ? '#1976d2' : '#757575'} />}
            onPress={() => handleNavigation('ParentMessaging')}
            style={[styles.sidebarItem, activeSidebarItem === 'ParentMessaging' && styles.sidebarItemActive]}
            titleStyle={[styles.sidebarItemText, activeSidebarItem === 'ParentMessaging' && styles.sidebarItemTextActive]}
          />

          <List.Item
            title={translate('parent.navigation.parentTeacherMeetings') || 'Parent-Teacher Meetings'}
            left={props => <List.Icon {...props} icon="calendar-clock" color={activeSidebarItem === 'MeetingScheduler' ? '#1976d2' : '#757575'} />}
            onPress={() => handleNavigation('MeetingScheduler')}
            style={[styles.sidebarItem, activeSidebarItem === 'MeetingScheduler' && styles.sidebarItemActive]}
            titleStyle={[styles.sidebarItemText, activeSidebarItem === 'MeetingScheduler' && styles.sidebarItemTextActive]}
          />
        </List.Section>

        <Divider style={styles.sidebarDivider} />

        <List.Item
          title={translate('parent.navigation.profile') || 'Profile'}
          left={props => <List.Icon {...props} icon="account" color={activeSidebarItem === 'ProfileManagement' ? '#1976d2' : '#757575'} />}
          onPress={() => handleNavigation('ProfileManagement')}
          style={[styles.sidebarItem, activeSidebarItem === 'ProfileManagement' && styles.sidebarItemActive]}
          titleStyle={[styles.sidebarItemText, activeSidebarItem === 'ProfileManagement' && styles.sidebarItemTextActive]}
        />

        <List.Item
          title={translate('common.logout') || 'Logout'}
          left={props => <List.Icon {...props} icon="logout" color="#F44336" />}
          onPress={() => handleNavigation('logout')}
          style={styles.sidebarItem}
          titleStyle={[styles.sidebarItemText, { color: '#F44336' }]}
        />

        <View style={styles.sidebarFooter}>
          <LanguageSelector />
        </View>
      </ScrollView>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  sidebar: {
    position: 'absolute',
    left: 0,
    top: 0,
    bottom: 0,
    width: 300,
    backgroundColor: 'white',
    zIndex: 1000,
    elevation: 16,
    borderTopRightRadius: 0,
    borderBottomRightRadius: 30,
    overflow: 'hidden',
  },
  sidebarHeader: {
    padding: 16,
    paddingTop: 40,
    paddingBottom: 24,
  },
  sidebarProfile: {
    alignItems: 'center',
    marginBottom: 8,
  },
  sidebarAvatar: {
    marginBottom: 12,
    borderWidth: 3,
    borderColor: 'rgba(255, 255, 255, 0.5)',
  },
  sidebarName: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  sidebarRole: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 14,
    textAlign: 'center',
    marginTop: 4,
  },
  sidebarMenu: {
    flex: 1,
    paddingBottom: 20,
  },
  sidebarItem: {
    marginVertical: 0,
    borderRadius: 0,
    paddingVertical: 4,
  },
  sidebarItemActive: {
    backgroundColor: 'rgba(33, 150, 243, 0.1)',
    borderLeftWidth: 4,
    borderLeftColor: '#2196F3',
  },
  sidebarItemText: {
    fontSize: 14,
    color: '#424242',
  },
  sidebarItemTextActive: {
    color: '#2196F3',
    fontWeight: 'bold',
  },
  sidebarDivider: {
    marginVertical: 12,
  },
  sidebarFooter: {
    padding: 16,
    alignItems: 'center',
  },
  childItem: {
    marginVertical: 0,
    borderRadius: 0,
    paddingVertical: 4,
  },
  selectedChildItem: {
    backgroundColor: 'rgba(33, 150, 243, 0.1)',
    borderLeftWidth: 4,
    borderLeftColor: '#2196F3',
  },
});

export default ParentSidebar;
