import React, { useEffect, useState, useRef } from 'react';
import { StyleSheet, View, TouchableOpacity, Animated, Text } from 'react-native';
import { Badge, IconButton, useTheme, Portal, Modal, Surface, Divider, Button } from 'react-native-paper';
import { useNotifications } from '../../context/NotificationContext';
import { useNavigation } from '@react-navigation/native';
import { getNotificationIcon } from '../../utils/IconUtils';
import * as Animatable from 'react-native-animatable';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useLanguage } from '../../context/LanguageContext';
import { useAuth } from '../../context/AuthContext';
import { db } from '../../config/firebase';
import { collection, query, where, getDocs } from 'firebase/firestore';

const NotificationBadge = ({
  size = 24,
  color,
  badgeSize = 16,
  style,
  onPress,
  showZero = false,
  containerStyle,
  animated = true,
  badgeColor,
  maxNotifications = 5,
  includeApprovals = false
}) => {
  const { unreadCount, fetchUserNotifications, notifications } = useNotifications();
  const { user } = useAuth();
  const theme = useTheme();
  const { translate } = useLanguage();
  const navigation = useNavigation();
  const [prevCount, setPrevCount] = useState(0);
  const [modalVisible, setModalVisible] = useState(false);
  const pulseAnim = useRef(new Animated.Value(1)).current;
  const badgeRef = useRef(null);
  const [pendingGradeCount, setPendingGradeCount] = useState(0);
  const [pendingAttendanceCount, setPendingAttendanceCount] = useState(0);
  const [totalCount, setTotalCount] = useState(0);

  // Refresh notifications when component mounts
  useEffect(() => {
    fetchUserNotifications();

    // If this badge should include approvals, fetch them too
    if (includeApprovals && user && user.role === 'admin') {
      fetchPendingGradeSubmissions();
      fetchPendingAttendanceSubmissions();

      // Set up refresh intervals
      const gradeIntervalId = setInterval(fetchPendingGradeSubmissions, 5 * 60 * 1000);
      const attendanceIntervalId = setInterval(fetchPendingAttendanceSubmissions, 5 * 60 * 1000);

      return () => {
        clearInterval(gradeIntervalId);
        clearInterval(attendanceIntervalId);
      };
    }
  }, [includeApprovals, user, fetchUserNotifications]);

  // Refresh notifications when modal is opened
  useEffect(() => {
    if (modalVisible) {
      fetchUserNotifications();
      if (includeApprovals && user && user.role === 'admin') {
        fetchPendingGradeSubmissions();
        fetchPendingAttendanceSubmissions();
      }
    }
  }, [modalVisible, includeApprovals, user, fetchUserNotifications]);

  // Update total count when any of the counts change
  useEffect(() => {
    setTotalCount(unreadCount + (includeApprovals ? pendingGradeCount + pendingAttendanceCount : 0));
  }, [unreadCount, pendingGradeCount, pendingAttendanceCount, includeApprovals]);

  // Fetch pending grade submissions
  const fetchPendingGradeSubmissions = async () => {
    try {
      const submissionsRef = collection(db, 'gradeSubmissions');
      const q = query(submissionsRef, where('status', '==', 'pending'));
      const querySnapshot = await getDocs(q);

      setPendingGradeCount(querySnapshot.size);
      console.log(`Found ${querySnapshot.size} pending grade submissions`);
    } catch (error) {
      console.error('Error fetching pending grade submissions:', error);
    }
  };

  // Fetch pending attendance submissions
  const fetchPendingAttendanceSubmissions = async () => {
    try {
      const submissionsRef = collection(db, 'attendanceSubmissions');
      const q = query(submissionsRef, where('status', '==', 'pending'));
      const querySnapshot = await getDocs(q);

      setPendingAttendanceCount(querySnapshot.size);
      console.log(`Found ${querySnapshot.size} pending attendance submissions`);
    } catch (error) {
      console.error('Error fetching pending attendance submissions:', error);
    }
  };

  // Animate badge when count changes
  useEffect(() => {
    if (animated && totalCount > 0 && totalCount !== prevCount) {
      // Pulse animation
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.3,
          duration: 200,
          useNativeDriver: true
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 200,
          useNativeDriver: true
        })
      ]).start();

      // Bounce animation using Animatable
      if (badgeRef.current) {
        badgeRef.current.bounce(800);
      }

      setPrevCount(totalCount);
    }
  }, [totalCount, animated, prevCount, pulseAnim]);

  // Format notification timestamp
  const formatTimestamp = (timestamp) => {
    if (!timestamp) return '';

    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
    const now = new Date();
    const diffMs = now - date;
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffMins < 1) return translate('notifications.justNow');
    if (diffMins < 60) return `${diffMins} ${translate('notifications.minutesAgo')}`;
    if (diffHours < 24) return `${diffHours} ${translate('notifications.hoursAgo')}`;
    if (diffDays < 7) return `${diffDays} ${translate('notifications.daysAgo')}`;

    return date.toLocaleDateString();
  };

  // Get notification color based on type
  const getNotificationColor = (type) => {
    switch (type) {
      case 'message': return '#2196F3';
      case 'announcement': return '#FF9800';
      case 'attendance': return '#4CAF50';
      case 'attendance_approval': return '#4CAF50';
      case 'grade': return '#9C27B0';
      case 'grade_approval': return '#FF9800';
      case 'grade_submission': return '#FF9800';
      case 'assignment': return '#F44336';
      case 'event': return '#00BCD4';
      case 'system': return '#607D8B';
      default: return '#757575';
    }
  };

  // Handle notification icon press
  const handlePress = () => {
    if (onPress) {
      onPress();
    } else {
      // Show notification modal
      setModalVisible(true);
    }
  };

  // Handle notification click
  const handleNotificationClick = (notification) => {
    // Mark notification as read
    if (!notification.read) {
      // This would be handled by the NotificationContext
    }

    // Navigate based on notification type
    switch (notification.type) {
      case 'message':
        navigation.navigate('CommunicationCenter', { messageId: notification.referenceId });
        break;
      case 'announcement':
        navigation.navigate('AnnouncementManagement', { announcementId: notification.referenceId });
        break;
      case 'attendance':
      case 'attendance_approval':
        navigation.navigate('AdminAttendanceApproval');
        break;
      case 'grade':
      case 'grade_approval':
      case 'grade_submission':
        navigation.navigate('AdminGradeApproval');
        break;
      case 'assignment':
        navigation.navigate('AssignmentManagement', { assignmentId: notification.referenceId });
        break;
      case 'event':
        navigation.navigate('AcademicCalendar', { eventId: notification.referenceId });
        break;
      default:
        // If notification has a specific navigation path, use it
        if (notification.data?.viewPath) {
          navigation.navigate(notification.data.viewPath, notification.data.viewParams || {});
        }
        break;
    }

    setModalVisible(false);
  };

  // View all notifications
  const viewAllNotifications = () => {
    navigation.navigate('NotificationCenter');
    setModalVisible(false);
  };

  // Don't show badge if count is 0 and showZero is false
  const showBadge = totalCount > 0 || showZero;

  // Format badge text (99+ for large numbers)
  const getBadgeText = () => {
    if (totalCount > 99) return '99+';
    return totalCount.toString();
  };

  // Get appropriate notifications to display in the modal
  const getNotificationsToDisplay = () => {
    let result = [...notifications];

    // If we're including approvals and there are pending grade submissions, add them as virtual notifications
    if (includeApprovals && user?.role === 'admin') {
      if (pendingGradeCount > 0) {
        result.unshift({
          id: 'grade-approvals',
          title: translate('notifications.pendingGradeApprovals') || 'Pending Grade Approvals',
          body: `${pendingGradeCount} ${translate('notifications.pendingGradeApprovalsDesc') || 'grade submissions need your approval'}`,
          type: 'grade_approval',
          timestamp: new Date().toISOString(),
          read: false,
          isVirtual: true
        });
      }

      if (pendingAttendanceCount > 0) {
        result.unshift({
          id: 'attendance-approvals',
          title: translate('notifications.pendingAttendanceApprovals') || 'Pending Attendance Approvals',
          body: `${pendingAttendanceCount} ${translate('notifications.pendingAttendanceApprovalsDesc') || 'attendance records need your approval'}`,
          type: 'attendance_approval',
          timestamp: new Date().toISOString(),
          read: false,
          isVirtual: true
        });
      }
    }

    return result.slice(0, maxNotifications);
  };

  return (
    <>
      <TouchableOpacity
        onPress={handlePress}
        style={[styles.container, containerStyle]}
      >
        <IconButton
          icon={getNotificationIcon('notification', unreadCount > 0)}
          size={size}
          color={color || theme.colors.primary}
          style={[styles.icon, style]}
        />
        {showBadge && (
          <Animated.View
            style={[
              styles.badgeContainer,
              { transform: [{ scale: pulseAnim }] }
            ]}
          >
            <Animatable.View ref={badgeRef}>
              <Badge
                size={badgeSize}
                style={[
                  styles.badge,
                  { backgroundColor: badgeColor || '#F44336' }
                ]}
              >
                {getBadgeText()}
              </Badge>
            </Animatable.View>
          </Animated.View>
        )}
      </TouchableOpacity>

      <Portal>
        <Modal
          visible={modalVisible}
          onDismiss={() => setModalVisible(false)}
          contentContainerStyle={styles.modalContainer}
        >
          <Surface style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>{translate('notifications.title') || 'Notifications'}</Text>
              <IconButton
                icon="close"
                size={20}
                onPress={() => setModalVisible(false)}
              />
            </View>

            <Divider />

            <View style={styles.notificationsList}>
              {getNotificationsToDisplay().length > 0 ? (
                getNotificationsToDisplay().map((notification, index) => (
                  <Animatable.View
                    key={notification.id}
                    animation="fadeIn"
                    duration={300}
                    delay={index * 50}
                  >
                    <TouchableOpacity
                      style={[
                        styles.notificationItem,
                        !notification.read && styles.unreadNotification
                      ]}
                      onPress={() => handleNotificationClick(notification)}
                    >
                      <View style={[
                        styles.notificationIconContainer,
                        { backgroundColor: getNotificationColor(notification.type) + '20' }
                      ]}>
                        <MaterialCommunityIcons
                          name={notification.type === 'grade_approval' ? 'clipboard-check' :
                                notification.type === 'attendance_approval' ? 'calendar-check' :
                                getNotificationIcon(notification.type, true)}
                          size={24}
                          color={getNotificationColor(notification.type)}
                        />
                      </View>
                      <View style={styles.notificationContent}>
                        <View style={styles.notificationHeader}>
                          <Text style={styles.notificationTitle} numberOfLines={1}>
                            {notification.title}
                          </Text>
                          {!notification.read && <View style={styles.unreadDot} />}
                        </View>
                        <Text style={styles.notificationBody} numberOfLines={2}>
                          {notification.body}
                        </Text>
                        <Text style={styles.notificationTime}>
                          {notification.isVirtual ? translate('notifications.now') || 'Now' : formatTimestamp(notification.timestamp)}
                        </Text>
                      </View>
                    </TouchableOpacity>
                    {index < getNotificationsToDisplay().length - 1 && <Divider />}
                  </Animatable.View>
                ))
              ) : (
                <View style={styles.emptyContainer}>
                  <MaterialCommunityIcons
                    name="bell-off-outline"
                    size={48}
                    color="#BDBDBD"
                  />
                  <Text style={styles.emptyText}>
                    {translate('notifications.noNotifications') || 'No notifications'}
                  </Text>
                </View>
              )}
            </View>

            <Divider />

            <View style={styles.modalFooter}>
              <Button
                mode="text"
                onPress={viewAllNotifications}
                color={theme.colors.primary}
              >
                {translate('notifications.viewAll') || 'View All'}
              </Button>
            </View>
          </Surface>
        </Modal>
      </Portal>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
  },
  icon: {
    margin: 0,
  },
  badgeContainer: {
    position: 'absolute',
    top: -4,
    right: -4,
    zIndex: 1
  },
  badge: {
    position: 'relative',
    fontWeight: 'bold',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.3,
    shadowRadius: 2,
  },
  modalContainer: {
    padding: 20,
    margin: 20,
  },
  modalContent: {
    borderRadius: 8,
    elevation: 4,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  notificationsList: {
    maxHeight: 400,
  },
  notificationItem: {
    flexDirection: 'row',
    padding: 16,
    alignItems: 'center',
  },
  unreadNotification: {
    backgroundColor: 'rgba(33, 150, 243, 0.05)',
  },
  notificationIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  notificationContent: {
    flex: 1,
  },
  notificationHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  notificationTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    flex: 1,
  },
  unreadDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#2196F3',
    marginLeft: 8,
  },
  notificationBody: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  notificationTime: {
    fontSize: 12,
    color: '#999',
  },
  modalFooter: {
    padding: 8,
    alignItems: 'center',
  },
  emptyContainer: {
    padding: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyText: {
    marginTop: 16,
    color: '#757575',
    textAlign: 'center',
  },
});

export default NotificationBadge;
