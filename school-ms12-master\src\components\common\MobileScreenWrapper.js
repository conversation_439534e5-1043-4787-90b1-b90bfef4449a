import React, { useState, useRef } from 'react';
import { View, StyleSheet, ScrollView, RefreshControl, Animated, Dimensions } from 'react-native';
import { Portal, FAB } from 'react-native-paper';
import { useNavigation } from '@react-navigation/native';
import { useLanguage } from '../../context/LanguageContext';
import MobileHeader from './MobileHeader';
import MobileBottomNavigation from './MobileBottomNavigation';
import mobileTheme from '../../theme/mobileTheme';

/**
 * Mobile-optimized screen wrapper component
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Screen content
 * @param {string} props.title - Screen title
 * @param {boolean} props.showBack - Whether to show back button
 * @param {boolean} props.showBottomNav - Whether to show bottom navigation
 * @param {Array} props.bottomNavItems - Bottom navigation items
 * @param {string} props.activeRoute - Active route for bottom navigation
 * @param {Function} props.onChangeRoute - Function to call when route changes
 * @param {Array} props.fabActions - FAB actions
 * @param {string} props.fabIcon - FAB icon
 * @param {Function} props.onFabPress - Function to call when FAB is pressed
 * @param {boolean} props.refreshing - Whether screen is refreshing
 * @param {Function} props.onRefresh - Function to call when screen is refreshed
 * @param {string} props.userRole - User role (admin, teacher, student, parent)
 * @param {Function} props.onMenuPress - Function to call when menu button is pressed
 * @param {boolean} props.showNotification - Whether to show notification icon
 * @param {boolean} props.showProfile - Whether to show profile icon
 * @param {boolean} props.showLanguage - Whether to show language selector
 */
const MobileScreenWrapper = ({
  children,
  title,
  showBack = false,
  showBottomNav = true,
  bottomNavItems = [],
  activeRoute,
  onChangeRoute,
  fabActions = [],
  fabIcon = 'plus',
  onFabPress,
  refreshing = false,
  onRefresh,
  userRole = 'admin',
  onMenuPress,
  showNotification = true,
  showProfile = true,
  showLanguage = true,
}) => {
  const navigation = useNavigation();
  const { translate, isRTL } = useLanguage();
  const [fabOpen, setFabOpen] = useState(false);
  const scrollY = useRef(new Animated.Value(0)).current;

  // Calculate bottom padding based on whether bottom nav is shown
  const getBottomPadding = () => {
    return showBottomNav ? mobileTheme.mobile.bottomNavHeight : 16;
  };

  // Handle scroll events
  const handleScroll = (event) => {
    try {
      // Extract the contentOffset from the event
      const { contentOffset } = event.nativeEvent;
      // Update the animated value
      scrollY.setValue(contentOffset.y);
    } catch (error) {
      console.error('Error in scroll handler:', error);
    }
  };

  // Calculate header opacity based on scroll position
  const headerOpacity = scrollY.interpolate({
    inputRange: [0, 50],
    outputRange: [1, 0.9],
    extrapolate: 'clamp',
  });

  // Calculate FAB translation based on scroll position
  const fabTranslateY = scrollY.interpolate({
    inputRange: [0, 100],
    outputRange: [0, 100],
    extrapolate: 'clamp',
  });

  return (
    <View style={styles.container}>
      {/* Header */}
      <Animated.View style={{ opacity: headerOpacity }}>
        <MobileHeader
          title={title}
          showBack={showBack}
          onMenuPress={onMenuPress}
          showNotification={showNotification}
          showProfile={showProfile}
          showLanguage={showLanguage}
          userRole={userRole}
        />
      </Animated.View>

      {/* Content */}
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={[
          styles.content,
          { paddingBottom: getBottomPadding() }
        ]}
        showsVerticalScrollIndicator={false}
        refreshControl={
          onRefresh ? (
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[mobileTheme.colors.primary]}
              tintColor={mobileTheme.colors.primary}
            />
          ) : undefined
        }
        onScroll={handleScroll}
        scrollEventThrottle={16}
        keyboardShouldPersistTaps="handled"
      >
        {children}
      </ScrollView>

      {/* Bottom Navigation */}
      {showBottomNav && bottomNavItems.length > 0 && (
        <MobileBottomNavigation
          items={bottomNavItems}
          activeRoute={activeRoute}
          onChangeRoute={onChangeRoute}
          userRole={userRole}
        />
      )}

      {/* FAB */}
      {(fabActions.length > 0 || onFabPress) && (
        <Portal>
          <Animated.View
            style={[
              styles.fabContainer,
              { transform: [{ translateY: fabTranslateY }] },
              isRTL && styles.fabContainerRTL
            ]}
          >
            {fabActions.length > 0 ? (
              <FAB.Group
                visible={true}
                open={fabOpen}
                icon={fabOpen ? 'close' : fabIcon}
                actions={fabActions.map(action => ({
                  ...action,
                  label: translate(action.label) || action.label,
                }))}
                onStateChange={({ open }) => setFabOpen(open)}
                onPress={() => {
                  if (fabOpen && onFabPress) {
                    onFabPress();
                  }
                }}
                fabStyle={styles.fab}
                color="white"
              />
            ) : (
              <FAB
                style={styles.fab}
                icon={fabIcon}
                color="white"
                onPress={onFabPress}
              />
            )}
          </Animated.View>
        </Portal>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: mobileTheme.colors.background,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 16,
  },
  fabContainer: {
    position: 'absolute',
    right: 16,
    bottom: 16 + mobileTheme.mobile.bottomNavHeight,
  },
  fabContainerRTL: {
    right: undefined,
    left: 16,
  },
  fab: {
    backgroundColor: mobileTheme.colors.primary,
  },
});

export default MobileScreenWrapper;
