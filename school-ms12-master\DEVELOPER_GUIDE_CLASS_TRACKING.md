# Developer Guide: Class Tracking System

This guide provides detailed information for developers who want to extend or modify the class tracking system.

## Table of Contents

1. [Architecture](#architecture)
2. [Component Structure](#component-structure)
3. [Data Flow](#data-flow)
4. [Firebase Integration](#firebase-integration)
5. [Notification System](#notification-system)
6. [Adding New Features](#adding-new-features)
7. [Modifying Existing Components](#modifying-existing-components)
8. [Testing](#testing)
9. [Performance Considerations](#performance-considerations)
10. [Security Considerations](#security-considerations)

## Architecture

The class tracking system follows a component-based architecture with the following layers:

1. **UI Layer**: React Native components that display the class tracking information
2. **Data Layer**: Firebase Firestore for data storage and retrieval
3. **Service Layer**: Services for notifications and other functionality
4. **Context Layer**: Context providers for language and other global state

### Component Hierarchy

```
App
├── UserDashboard (Teacher/Student/Parent)
│   └── DashboardClassWidget
│       └── OngoingClassTracker
└── ClassScheduleScreen (Teacher/Student/Parent)
    └── (Class schedule implementation)
```

## Component Structure

### OngoingClassTracker

The `OngoingClassTracker` component is responsible for displaying the current and next class periods. It takes the following props:

- `schedule`: Array of schedule entries
- `role`: User role (teacher, student, parent)
- `classId`: Class ID (for student role)
- `section`: Section name (for student role)
- `teacherId`: Teacher ID (for teacher role)
- `onPressViewSchedule`: Function to navigate to the schedule screen
- `style`: Additional styles

Key methods:

- `updatePeriods()`: Updates the current and next periods based on the current time
- `sendNextClassNotification()`: Sends a notification for the next class
- `formatTime()`: Formats time for display
- `getSubjectName()`, `getTeacherName()`, `getClassName()`: Helper methods for displaying information

### DashboardClassWidget

The `DashboardClassWidget` component is responsible for fetching the schedule data and displaying the `OngoingClassTracker` component. It takes the following props:

- `role`: User role (teacher, student, parent)
- `userId`: User ID
- `childId`: Child ID (for parent role)
- `onPressViewSchedule`: Function to navigate to the schedule screen
- `style`: Additional styles

Key methods:

- `fetchSchedule()`: Fetches the schedule data from Firestore
- `handleRetry()`: Retries fetching the schedule data
- `renderLoading()`, `renderError()`: Render loading and error states

## Data Flow

1. The `DashboardClassWidget` component fetches the schedule data from Firestore based on the user's role.
2. The schedule data is passed to the `OngoingClassTracker` component.
3. The `OngoingClassTracker` component processes the schedule data to determine the current and next periods.
4. The `OngoingClassTracker` component renders the current and next periods.
5. When a next period is approaching, the `OngoingClassTracker` component sends a notification.

## Firebase Integration

The class tracking system uses Firebase Firestore for data storage and retrieval. The following collections are used:

- `timetables`: Stores the class schedule entries
- `notifications`: Stores notification records
- `users`: Stores user information
- `teachers`: Stores teacher information
- `students`: Stores student information
- `classes`: Stores class information

### Firestore Queries

The `DashboardClassWidget` component uses the following queries to fetch schedule data:

- For teachers:
  ```javascript
  query(
    collection(db, 'timetables'),
    where('teacherId', '==', userId),
    where('published', '==', true)
  )
  ```

- For students:
  ```javascript
  query(
    collection(db, 'timetables'),
    where('classId', '==', classId),
    where('sectionName', '==', section),
    where('published', '==', true)
  )
  ```

- For parents:
  ```javascript
  // First get the child's class and section
  query(collection(db, 'students'), where('id', '==', childId))
  
  // Then get the schedule for that class and section
  query(
    collection(db, 'timetables'),
    where('classId', '==', studentData.classId),
    where('sectionName', '==', studentData.section),
    where('published', '==', true)
  )
  ```

## Notification System

The class tracking system uses the `NotificationService` to send and schedule notifications. The following methods are used:

- `sendUpcomingClassNotification(userId, role, periodData)`: Sends a notification for an upcoming class
- `scheduleLocalNotification(title, body, data, secondsFromNow)`: Schedules a notification for future delivery
- `handleClassScheduleNotification(notification)`: Handles navigation when a notification is tapped

### Notification Flow

1. The `OngoingClassTracker` component detects that a class is starting in 5 minutes.
2. It calls `sendNextClassNotification()` to send a notification.
3. The `sendNextClassNotification()` method calls `NotificationService.sendUpcomingClassNotification()`.
4. The `sendUpcomingClassNotification()` method creates a notification record in Firestore and sends a local notification.
5. When the user taps the notification, the `handleClassScheduleNotification()` method is called to navigate to the appropriate screen.

## Adding New Features

### Adding a New Component

To add a new component to the class tracking system:

1. Create a new component file in the appropriate directory.
2. Import the necessary dependencies.
3. Define the component and its props.
4. Implement the component logic.
5. Export the component.
6. Import and use the component in the appropriate parent component.

### Adding a New Notification Type

To add a new notification type:

1. Add a new method to the `NotificationService` class.
2. Implement the logic for creating the notification record and sending the notification.
3. Add a new handler method for the notification type.
4. Update the notification handling logic to call the new handler method.

## Modifying Existing Components

### Modifying the OngoingClassTracker Component

To modify the `OngoingClassTracker` component:

1. Locate the component file at `src/components/common/OngoingClassTracker.js`.
2. Make the necessary changes to the component logic or UI.
3. Test the changes to ensure they work as expected.

### Modifying the DashboardClassWidget Component

To modify the `DashboardClassWidget` component:

1. Locate the component file at `src/components/common/DashboardClassWidget.js`.
2. Make the necessary changes to the component logic or UI.
3. Test the changes to ensure they work as expected.

## Testing

### Unit Testing

To write unit tests for the class tracking components:

1. Create a test file for the component (e.g., `OngoingClassTracker.test.js`).
2. Import the necessary testing utilities and the component.
3. Write test cases for the component's functionality.
4. Run the tests using the testing framework.

### Integration Testing

To write integration tests for the class tracking system:

1. Create a test file for the integration tests.
2. Import the necessary testing utilities and components.
3. Write test cases that test the interaction between components.
4. Run the tests using the testing framework.

### End-to-End Testing

To write end-to-end tests for the class tracking system:

1. Create a test file for the end-to-end tests.
2. Import the necessary testing utilities.
3. Write test cases that simulate user interactions with the system.
4. Run the tests using the testing framework.

## Performance Considerations

### Optimizing Firestore Queries

To optimize Firestore queries:

1. Use compound queries to filter data efficiently.
2. Use indexes for frequently used queries.
3. Limit the amount of data fetched by using pagination or limiting the query results.
4. Use caching to reduce the number of queries.

### Optimizing Rendering

To optimize rendering:

1. Use memoization to prevent unnecessary re-renders.
2. Use virtualization for long lists.
3. Optimize images and other assets.
4. Use performance monitoring tools to identify bottlenecks.

## Security Considerations

### Firestore Security Rules

The Firestore security rules should enforce the following permissions:

- Teachers can only access timetable entries where they are the assigned teacher.
- Students can only access timetable entries for their class and section.
- Parents can only access timetable entries for their children's classes.
- Admins have full access to all collections.

### Authentication

The class tracking system relies on Firebase Authentication for user authentication. Ensure that:

1. Users are properly authenticated before accessing the system.
2. User roles are properly assigned and verified.
3. Sensitive operations require re-authentication.

### Data Validation

To ensure data integrity:

1. Validate data on the client side before sending it to Firestore.
2. Use Firestore security rules to validate data on the server side.
3. Handle edge cases and invalid data gracefully.
