import React, { useState, useEffect } from 'react';
import { View, ScrollView, StyleSheet, Dimensions } from 'react-native';
import { Card, Title, Text, DataTable, Surface, ActivityIndicator, IconButton, Chip } from 'react-native-paper';
import { db, auth } from '../../config/firebase';
import { collection, query, where, getDocs, doc, getDoc } from 'firebase/firestore';
import { useLanguage } from '../../context/LanguageContext';
import * as Animatable from 'react-native-animatable';
import StudentAppHeader from '../../components/common/StudentAppHeader';

const StudentClassSchedule = ({ navigation }) => {
  const { translate } = useLanguage();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [timetable, setTimetable] = useState([]);
  const [studentInfo, setStudentInfo] = useState(null);
  const [className, setClassName] = useState('');
  const [sectionName, setSectionName] = useState('');

  // Days of the week
  const days = [
    'Monday',
    'Tuesday',
    'Wednesday',
    'Thursday',
    'Friday',
    'Saturday',
    'Sunday',
  ];

  // Time slots for 40-minute periods
  const morningTimeSlots = [
    { period: 1, start: '08:00', end: '08:40', session: 'morning' },
    { period: 2, start: '08:45', end: '09:25', session: 'morning' },
    { period: 3, start: '09:30', end: '10:10', session: 'morning' },
    { period: 4, start: '10:15', end: '10:55', session: 'morning' },
    { period: 5, start: '11:00', end: '11:40', session: 'morning' },
    { period: 6, start: '11:45', end: '12:25', session: 'morning' },
  ];

  const afternoonTimeSlots = [
    { period: 1, start: '12:30', end: '13:10', session: 'afternoon' },
    { period: 2, start: '13:15', end: '13:55', session: 'afternoon' },
    { period: 3, start: '14:00', end: '14:40', session: 'afternoon' },
    { period: 4, start: '14:45', end: '15:25', session: 'afternoon' },
    { period: 5, start: '15:30', end: '16:10', session: 'afternoon' },
    { period: 6, start: '16:15', end: '16:55', session: 'afternoon' },
  ];

  useEffect(() => {
    fetchStudentInfo();
  }, []);

  // Fetch student information
  const fetchStudentInfo = async () => {
    try {
      setLoading(true);
      setError('');

      const currentUser = auth.currentUser;
      if (!currentUser) {
        setError('User not authenticated');
        setLoading(false);
        return;
      }

      // Get student document
      const studentRef = doc(db, 'users', currentUser.uid);
      const studentDoc = await getDoc(studentRef);

      if (!studentDoc.exists()) {
        setError('Student information not found');
        setLoading(false);
        return;
      }

      const studentData = studentDoc.data();
      setStudentInfo(studentData);

      // Get class name
      if (studentData.classId) {
        const classRef = doc(db, 'classes', studentData.classId);
        const classDoc = await getDoc(classRef);
        
        if (classDoc.exists()) {
          setClassName(classDoc.data().name);
        }
      }

      // Set section name
      if (studentData.section) {
        setSectionName(studentData.section);
      }

      // Fetch timetable for this student's class and section
      await fetchTimetable(studentData.classId, studentData.section);
    } catch (error) {
      console.error('Error fetching student info:', error);
      setError('Failed to load student information. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Fetch timetable for student's class and section
  const fetchTimetable = async (classId, section) => {
    try {
      if (!classId || !section) {
        setError('Class or section information missing');
        return;
      }

      const timetableRef = collection(db, 'timetables');
      const timetableQuery = query(
        timetableRef,
        where('classId', '==', classId),
        where('sectionName', '==', section),
        where('published', '==', true)
      );

      const querySnapshot = await getDocs(timetableQuery);
      
      const timetableData = [];
      querySnapshot.forEach((doc) => {
        timetableData.push({ id: doc.id, ...doc.data() });
      });

      setTimetable(timetableData);
    } catch (error) {
      console.error('Error fetching timetable:', error);
      setError('Failed to load timetable. Please try again.');
    }
  };

  // Get subject name by ID
  const getSubjectName = (subjectId) => {
    // This would ideally fetch from a subjects collection
    // For now, return a placeholder
    return subjectId || 'Unknown Subject';
  };

  // Get teacher name by ID
  const getTeacherName = (teacherId) => {
    // This would ideally fetch from the users collection
    // For now, return a placeholder
    return teacherId || 'Unknown Teacher';
  };

  // Organize timetable by day and session
  const getWeeklySchedule = () => {
    const weeklySchedule = {
      morning: {},
      afternoon: {}
    };
    
    // Initialize empty schedule for all days and periods
    days.forEach(day => {
      weeklySchedule.morning[day] = Array(morningTimeSlots.length).fill(null);
      weeklySchedule.afternoon[day] = Array(afternoonTimeSlots.length).fill(null);
    });
    
    // Fill in the schedule with entries
    timetable.forEach(entry => {
      const session = entry.session;
      const day = entry.day;
      const timeSlots = session === 'morning' ? morningTimeSlots : afternoonTimeSlots;
      const periodIndex = timeSlots.findIndex(slot => 
        slot.start === entry.startTime && slot.end === entry.endTime
      );
      
      if (periodIndex !== -1) {
        weeklySchedule[session][day][periodIndex] = entry;
      }
    });
    
    return weeklySchedule;
  };

  return (
    <View style={styles.container}>
      <StudentAppHeader 
        title={translate('student.classSchedule') || 'Class Schedule'} 
        navigation={navigation} 
      />
      
      <ScrollView style={styles.scrollView}>
        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#1976d2" />
            <Text style={styles.loadingText}>Loading your schedule...</Text>
          </View>
        ) : error ? (
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>{error}</Text>
          </View>
        ) : (
          <Animatable.View animation="fadeIn" duration={500}>
            <Card style={styles.card}>
              <Card.Content>
                <View style={styles.headerContainer}>
                  <Title style={styles.title}>
                    {className} - {sectionName} {translate('student.schedule') || 'Schedule'}
                  </Title>
                </View>
                
                {/* Morning Session */}
                <View style={styles.sectionHeader}>
                  <Title style={styles.sectionTitle}>
                    {translate('timetable.morningSession') || 'Morning Session (8:00 - 12:30)'}
                  </Title>
                  <IconButton icon="weather-sunny" size={24} color="#1976d2" />
                </View>
                
                <Surface style={styles.tableSurface}>
                  <View style={styles.scrollableTableContainer}>
                    <ScrollView horizontal>
                      <View>
                        <DataTable style={styles.timetableTable}>
                          <DataTable.Header style={styles.tableHeader}>
                            <DataTable.Title style={styles.periodColumn}>Period</DataTable.Title>
                            {days.map((day) => (
                              <DataTable.Title key={day} style={styles.dayColumn}>{day}</DataTable.Title>
                            ))}
                          </DataTable.Header>
                        </DataTable>
                        
                        <ScrollView style={styles.tableScrollView} nestedScrollEnabled={true}>
                          <DataTable style={styles.timetableTable}>
                            {morningTimeSlots.map((timeSlot, index) => {
                              const weeklySchedule = getWeeklySchedule();
                              
                              return (
                                <DataTable.Row key={`morning-${index}`} style={styles.tableRow}>
                                  <DataTable.Cell style={styles.periodColumn}>
                                    <Text style={styles.periodText}>Period {timeSlot.period}</Text>
                                    <Text style={styles.timeText}>{timeSlot.start} - {timeSlot.end}</Text>
                                  </DataTable.Cell>
                                  
                                  {days.map((day) => {
                                    const entry = weeklySchedule.morning[day][index];
                                    
                                    return (
                                      <DataTable.Cell key={day} style={styles.dayColumn}>
                                        {entry ? (
                                          <View style={styles.scheduleCell}>
                                            <Text style={styles.subjectText}>
                                              {getSubjectName(entry.subjectId)}
                                            </Text>
                                            <Text style={styles.teacherText}>
                                              {getTeacherName(entry.teacherId)}
                                            </Text>
                                            <Text style={styles.roomText}>Room: {entry.roomNumber}</Text>
                                          </View>
                                        ) : (
                                          <View style={styles.emptyCell}>
                                            <Text style={styles.emptyCellText}>Free</Text>
                                          </View>
                                        )}
                                      </DataTable.Cell>
                                    );
                                  })}
                                </DataTable.Row>
                              );
                            })}
                          </DataTable>
                        </ScrollView>
                      </View>
                    </ScrollView>
                  </View>
                </Surface>
                
                {/* Afternoon Session */}
                <View style={styles.sectionHeader}>
                  <Title style={styles.sectionTitle}>
                    {translate('timetable.afternoonSession') || 'Afternoon Session (12:30 - 5:00)'}
                  </Title>
                  <IconButton icon="weather-night" size={24} color="#673AB7" />
                </View>
                
                <Surface style={styles.tableSurface}>
                  <View style={styles.scrollableTableContainer}>
                    <ScrollView horizontal>
                      <View>
                        <DataTable style={styles.timetableTable}>
                          <DataTable.Header style={styles.tableHeader}>
                            <DataTable.Title style={styles.periodColumn}>Period</DataTable.Title>
                            {days.map((day) => (
                              <DataTable.Title key={day} style={styles.dayColumn}>{day}</DataTable.Title>
                            ))}
                          </DataTable.Header>
                        </DataTable>
                        
                        <ScrollView style={styles.tableScrollView} nestedScrollEnabled={true}>
                          <DataTable style={styles.timetableTable}>
                            {afternoonTimeSlots.map((timeSlot, index) => {
                              const weeklySchedule = getWeeklySchedule();
                              
                              return (
                                <DataTable.Row key={`afternoon-${index}`} style={styles.tableRow}>
                                  <DataTable.Cell style={styles.periodColumn}>
                                    <Text style={styles.periodText}>Period {timeSlot.period}</Text>
                                    <Text style={styles.timeText}>{timeSlot.start} - {timeSlot.end}</Text>
                                  </DataTable.Cell>
                                  
                                  {days.map((day) => {
                                    const entry = weeklySchedule.afternoon[day][index];
                                    
                                    return (
                                      <DataTable.Cell key={day} style={styles.dayColumn}>
                                        {entry ? (
                                          <View style={[styles.scheduleCell, { borderLeftColor: '#673AB7' }]}>
                                            <Text style={styles.subjectText}>
                                              {getSubjectName(entry.subjectId)}
                                            </Text>
                                            <Text style={styles.teacherText}>
                                              {getTeacherName(entry.teacherId)}
                                            </Text>
                                            <Text style={styles.roomText}>Room: {entry.roomNumber}</Text>
                                          </View>
                                        ) : (
                                          <View style={styles.emptyCell}>
                                            <Text style={styles.emptyCellText}>Free</Text>
                                          </View>
                                        )}
                                      </DataTable.Cell>
                                    );
                                  })}
                                </DataTable.Row>
                              );
                            })}
                          </DataTable>
                        </ScrollView>
                      </View>
                    </ScrollView>
                  </View>
                </Surface>
                
                {/* Daily Summary */}
                <View style={styles.sectionHeader}>
                  <Title style={styles.sectionTitle}>
                    {translate('timetable.dailySummary') || 'Daily Summary'}
                  </Title>
                  <IconButton icon="calendar-today" size={24} color="#1976d2" />
                </View>
                
                <Surface style={styles.tableSurface}>
                  <ScrollView horizontal>
                    <DataTable style={styles.summaryTable}>
                      <DataTable.Header style={styles.tableHeader}>
                        <DataTable.Title style={styles.summaryColumn}>Day</DataTable.Title>
                        <DataTable.Title style={styles.summaryColumn}>Subjects</DataTable.Title>
                        <DataTable.Title style={styles.summaryColumn}>Time</DataTable.Title>
                      </DataTable.Header>
                      
                      {days.map(day => {
                        const dayEntries = timetable.filter(entry => entry.day === day);
                        dayEntries.sort((a, b) => {
                          return convertTimeToMinutes(a.startTime) - convertTimeToMinutes(b.startTime);
                        });
                        
                        return (
                          <DataTable.Row key={day} style={styles.tableRow}>
                            <DataTable.Cell style={styles.summaryColumn}>{day}</DataTable.Cell>
                            <DataTable.Cell style={styles.summaryColumn}>
                              <View style={styles.subjectsContainer}>
                                {dayEntries.length > 0 ? (
                                  dayEntries.map((entry, index) => (
                                    <Chip 
                                      key={index}
                                      style={[
                                        styles.subjectChip,
                                        { 
                                          backgroundColor: entry.session === 'morning' ? '#E3F2FD' : '#EDE7F6',
                                          borderColor: entry.session === 'morning' ? '#2196F3' : '#673AB7'
                                        }
                                      ]}
                                      textStyle={{ fontSize: 12 }}
                                    >
                                      {getSubjectName(entry.subjectId)}
                                    </Chip>
                                  ))
                                ) : (
                                  <Text style={styles.noClassText}>No classes</Text>
                                )}
                              </View>
                            </DataTable.Cell>
                            <DataTable.Cell style={styles.summaryColumn}>
                              {dayEntries.length > 0 ? (
                                <View>
                                  {dayEntries.map((entry, index) => (
                                    <Text key={index} style={styles.timeRangeText}>
                                      {entry.startTime} - {entry.endTime}
                                    </Text>
                                  ))}
                                </View>
                              ) : (
                                <Text style={styles.noClassText}>-</Text>
                              )}
                            </DataTable.Cell>
                          </DataTable.Row>
                        );
                      })}
                    </DataTable>
                  </ScrollView>
                </Surface>
              </Card.Content>
            </Card>
          </Animatable.View>
        )}
      </ScrollView>
    </View>
  );
};

// Convert time string (HH:MM) to minutes for easier comparison
const convertTimeToMinutes = (timeString) => {
  const [hours, minutes] = timeString.split(':').map(Number);
  return hours * 60 + minutes;
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollView: {
    flex: 1,
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  errorContainer: {
    padding: 20,
    backgroundColor: '#ffebee',
    borderRadius: 8,
    marginVertical: 10,
  },
  errorText: {
    color: '#d32f2f',
    fontSize: 16,
  },
  card: {
    borderRadius: 8,
    elevation: 4,
    marginBottom: 16,
  },
  headerContainer: {
    marginBottom: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1976d2',
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 16,
    marginBottom: 8,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  tableSurface: {
    borderRadius: 8,
    elevation: 2,
    marginBottom: 16,
  },
  scrollableTableContainer: {
    flexDirection: 'column',
    width: '100%',
  },
  timetableTable: {
    backgroundColor: '#fff',
    borderRadius: 8,
  },
  tableHeader: {
    backgroundColor: '#f5f5f5',
  },
  tableScrollView: {
    maxHeight: 300,
  },
  tableRow: {
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  periodColumn: {
    width: 100,
    backgroundColor: '#f0f0f0',
  },
  dayColumn: {
    width: 180,
    padding: 5,
  },
  periodText: {
    fontWeight: 'bold',
    fontSize: 14,
  },
  timeText: {
    fontSize: 12,
    color: '#666',
  },
  scheduleCell: {
    padding: 10,
    backgroundColor: '#e3f2fd',
    borderRadius: 8,
    minHeight: 100,
    elevation: 2,
    borderLeftWidth: 4,
    borderLeftColor: '#2196F3',
  },
  subjectText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  teacherText: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
  },
  roomText: {
    fontSize: 12,
    color: '#666',
  },
  emptyCell: {
    padding: 10,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    minHeight: 100,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyCellText: {
    color: '#9e9e9e',
    fontStyle: 'italic',
  },
  summaryTable: {
    backgroundColor: '#fff',
    borderRadius: 8,
  },
  summaryColumn: {
    minWidth: 150,
    paddingHorizontal: 10,
  },
  subjectsContainer: {
    flexDirection: 'column',
    flexWrap: 'wrap',
  },
  subjectChip: {
    marginBottom: 4,
    marginRight: 4,
  },
  timeRangeText: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
  },
  noClassText: {
    color: '#9e9e9e',
    fontStyle: 'italic',
  },
});

export default StudentClassSchedule;
