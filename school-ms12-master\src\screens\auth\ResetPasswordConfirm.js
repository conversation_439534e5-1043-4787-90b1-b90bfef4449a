import React, { useState, useEffect } from 'react';
import { View, StyleSheet, TouchableWithoutFeedback, Keyboard, KeyboardAvoidingView, Platform } from 'react-native';
import { Text, TextInput, Button, Surface, Title, useTheme, IconButton, Menu } from 'react-native-paper';
import { LinearGradient } from 'expo-linear-gradient';
import * as Animatable from 'react-native-animatable';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useNavigation, useRoute } from '@react-navigation/native';
import { useLanguage } from '../../context/LanguageContext';
import EmailVerificationService from '../../services/EmailVerificationService';
import EmailNotificationService from '../../services/EmailNotificationService';

const ResetPasswordConfirm = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { translate, setLanguage } = useLanguage();
  const { colors } = useTheme();
  
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [message, setMessage] = useState('');
  const [success, setSuccess] = useState(false);
  const [menuVisible, setMenuVisible] = useState(false);
  const [passwordVisible, setPasswordVisible] = useState(false);
  const [confirmPasswordVisible, setConfirmPasswordVisible] = useState(false);
  const [email, setEmail] = useState('');
  const [oobCode, setOobCode] = useState('');
  const [validCode, setValidCode] = useState(false);
  const [codeChecked, setCodeChecked] = useState(false);
  
  useEffect(() => {
    // Get the oobCode from the route params or URL query params
    const params = route.params || {};
    const queryParams = new URLSearchParams(window.location.search);
    
    const code = params.oobCode || queryParams.get('oobCode');
    setOobCode(code);
    
    if (code) {
      verifyResetCode(code);
    } else {
      setError(translate('auth.errors.missingResetCode') || 'Missing password reset code');
      setCodeChecked(true);
    }
  }, [route.params]);
  
  const verifyResetCode = async (code) => {
    try {
      setLoading(true);
      const result = await EmailVerificationService.verifyPasswordResetCode(code);
      
      if (result.valid) {
        setValidCode(true);
        setEmail(result.email);
      } else {
        setError(result.message);
      }
    } catch (error) {
      console.error('Error verifying reset code:', error);
      setError(translate('auth.errors.invalidResetCode') || 'Invalid or expired reset code');
    } finally {
      setLoading(false);
      setCodeChecked(true);
    }
  };
  
  const handleResetPassword = async () => {
    if (!password || !confirmPassword) {
      setError(translate('auth.errors.allFieldsRequired') || 'Please fill in all fields');
      return;
    }
    
    if (password !== confirmPassword) {
      setError(translate('auth.errors.passwordsDoNotMatch') || 'Passwords do not match');
      return;
    }
    
    if (password.length < 6) {
      setError(translate('auth.errors.passwordTooShort') || 'Password must be at least 6 characters');
      return;
    }
    
    try {
      setLoading(true);
      setError('');
      
      const result = await EmailVerificationService.confirmPasswordReset(oobCode, password);
      
      if (result.success) {
        setSuccess(true);
        setMessage(result.message);
        
        // Send confirmation email
        try {
          await EmailNotificationService.sendPasswordResetConfirmationEmail(email);
        } catch (emailError) {
          console.error('Error sending confirmation email:', emailError);
          // Continue even if confirmation email fails
        }
      } else {
        setError(result.message);
      }
    } catch (error) {
      console.error('Error resetting password:', error);
      setError(translate('auth.errors.resetPasswordFailed') || 'Failed to reset password');
    } finally {
      setLoading(false);
    }
  };
  
  const handleLanguageChange = async (langCode) => {
    try {
      await setLanguage(langCode);
      setMenuVisible(false);
    } catch (error) {
      console.error('Failed to change language:', error);
    }
  };
  
  if (!codeChecked) {
    return (
      <View style={styles.container}>
        <LinearGradient
          colors={[colors.primary, colors.accent]}
          style={styles.gradient}
        >
          <View style={styles.loadingContainer}>
            <Animatable.View animation="pulse" easing="ease-out" iterationCount="infinite">
              <MaterialCommunityIcons name="lock-reset" size={64} color="white" />
            </Animatable.View>
            <Text style={styles.loadingText}>
              {translate('auth.verifyingResetCode') || 'Verifying reset code...'}
            </Text>
          </View>
        </LinearGradient>
      </View>
    );
  }
  
  if (success) {
    return (
      <View style={styles.container}>
        <LinearGradient
          colors={[colors.primary, colors.accent]}
          style={styles.gradient}
        >
          <Animatable.View animation="bounceIn" duration={1000} style={styles.successContainer}>
            <Surface style={styles.successSurface}>
              <Animatable.View animation="zoomIn" delay={300}>
                <MaterialCommunityIcons name="check-circle" size={80} color={colors.primary} style={styles.successIcon} />
              </Animatable.View>
              
              <Title style={styles.successTitle}>
                {translate('auth.passwordResetSuccess') || 'Password Reset Successful!'}
              </Title>
              
              <Text style={styles.successMessage}>
                {message || translate('auth.passwordResetSuccessMessage') || 'Your password has been reset successfully. You can now log in with your new password.'}
              </Text>
              
              <Animatable.View animation="fadeIn" delay={500}>
                <Button
                  mode="contained"
                  onPress={() => navigation.navigate('Login')}
                  style={[styles.button, { backgroundColor: colors.primary }]}
                >
                  {translate('auth.backToLogin') || 'Back to Login'}
                </Button>
              </Animatable.View>
            </Surface>
          </Animatable.View>
        </LinearGradient>
      </View>
    );
  }
  
  if (!validCode) {
    return (
      <View style={styles.container}>
        <LinearGradient
          colors={[colors.primary, colors.accent]}
          style={styles.gradient}
        >
          <Animatable.View animation="fadeIn" duration={1000} style={styles.errorContainer}>
            <Surface style={styles.errorSurface}>
              <Animatable.View animation="zoomIn" delay={300}>
                <MaterialCommunityIcons name="alert-circle" size={80} color={colors.error} style={styles.errorIcon} />
              </Animatable.View>
              
              <Title style={[styles.errorTitle, { color: colors.error }]}>
                {translate('auth.resetCodeInvalid') || 'Invalid Reset Code'}
              </Title>
              
              <Text style={styles.errorMessage}>
                {error || translate('auth.resetCodeInvalidMessage') || 'The password reset link is invalid or has expired. Please request a new password reset link.'}
              </Text>
              
              <Animatable.View animation="fadeIn" delay={500}>
                <Button
                  mode="contained"
                  onPress={() => navigation.navigate('ForgotPassword')}
                  style={[styles.button, { backgroundColor: colors.primary }]}
                >
                  {translate('auth.requestNewLink') || 'Request New Link'}
                </Button>
              </Animatable.View>
              
              <Animatable.View animation="fadeIn" delay={700}>
                <Button
                  mode="text"
                  onPress={() => navigation.navigate('Login')}
                  style={styles.link}
                >
                  {translate('auth.backToLogin') || 'Back to Login'}
                </Button>
              </Animatable.View>
            </Surface>
          </Animatable.View>
        </LinearGradient>
      </View>
    );
  }
  
  return (
    <View style={styles.container}>
      <LinearGradient
        colors={[colors.primary, colors.accent]}
        style={styles.gradient}
      >
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
          <KeyboardAvoidingView
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            style={styles.keyboardAvoidingView}
          >
            <View style={styles.languageMenuContainer}>
              <Menu
                visible={menuVisible}
                onDismiss={() => setMenuVisible(false)}
                anchor={
                  <IconButton
                    icon="translate"
                    color="white"
                    size={24}
                    onPress={() => setMenuVisible(true)}
                  />
                }
              >
                <Menu.Item onPress={() => handleLanguageChange('en')} title="English" />
                <Menu.Item onPress={() => handleLanguageChange('am')} title="አማርኛ" />
                <Menu.Item onPress={() => handleLanguageChange('or')} title="Afaan Oromoo" />
              </Menu>
            </View>
            
            <Animatable.View animation="fadeInUp" duration={1000} style={styles.formContainer}>
              <Surface style={styles.formSurface} elevation={10}>
                <Animatable.Text 
                  animation="fadeIn" 
                  duration={1500} 
                  style={styles.title}
                >
                  {translate('auth.createNewPassword') || 'Create New Password'}
                </Animatable.Text>
                
                <Text style={styles.subtitle}>
                  {translate('auth.resetPasswordFor') || 'Reset password for'}: {email}
                </Text>

                {error ? (
                  <Animatable.View animation="fadeIn" duration={300}>
                    <View style={styles.errorContainer}>
                      <MaterialCommunityIcons 
                        name="alert-circle" 
                        size={18} 
                        color={colors.error} 
                      />
                      <Text style={[styles.errorText, { color: colors.error }]}>
                        {error}
                      </Text>
                    </View>
                  </Animatable.View>
                ) : null}
                
                <Animatable.View animation="fadeIn" delay={400}>
                  <TextInput
                    label={translate('auth.newPassword') || 'New Password'}
                    value={password}
                    onChangeText={setPassword}
                    secureTextEntry={!passwordVisible}
                    style={styles.input}
                    right={
                      <TextInput.Icon
                        name={passwordVisible ? 'eye-off' : 'eye'}
                        onPress={() => setPasswordVisible(!passwordVisible)}
                      />
                    }
                  />
                </Animatable.View>
                
                <Animatable.View animation="fadeIn" delay={600}>
                  <TextInput
                    label={translate('auth.confirmPassword') || 'Confirm Password'}
                    value={confirmPassword}
                    onChangeText={setConfirmPassword}
                    secureTextEntry={!confirmPasswordVisible}
                    style={styles.input}
                    right={
                      <TextInput.Icon
                        name={confirmPasswordVisible ? 'eye-off' : 'eye'}
                        onPress={() => setConfirmPasswordVisible(!confirmPasswordVisible)}
                      />
                    }
                  />
                </Animatable.View>
                
                <Animatable.View animation="fadeIn" delay={800}>
                  <Button
                    mode="contained"
                    onPress={handleResetPassword}
                    loading={loading}
                    disabled={loading}
                    style={[styles.button, { backgroundColor: colors.primary }]}
                    contentStyle={styles.buttonContent}
                    labelStyle={styles.buttonLabel}
                  >
                    {translate('auth.resetPassword') || 'Reset Password'}
                  </Button>
                </Animatable.View>
                
                <Animatable.View animation="fadeIn" delay={1000}>
                  <Button
                    mode="text"
                    onPress={() => navigation.navigate('Login')}
                    style={styles.link}
                  >
                    {translate('auth.backToLogin') || 'Back to Login'}
                  </Button>
                </Animatable.View>
              </Surface>
            </Animatable.View>
          </KeyboardAvoidingView>
        </TouchableWithoutFeedback>
      </LinearGradient>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gradient: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  keyboardAvoidingView: {
    flex: 1,
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  formContainer: {
    width: '90%',
    maxWidth: 400,
  },
  formSurface: {
    padding: 24,
    borderRadius: 10,
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 14,
    marginBottom: 20,
    textAlign: 'center',
    opacity: 0.7,
  },
  input: {
    width: '100%',
    marginBottom: 16,
  },
  button: {
    width: '100%',
    marginTop: 16,
    marginBottom: 8,
  },
  buttonContent: {
    height: 48,
  },
  buttonLabel: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  link: {
    marginTop: 8,
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFEBEE',
    padding: 10,
    borderRadius: 5,
    marginBottom: 16,
  },
  errorText: {
    marginLeft: 8,
    flex: 1,
  },
  languageMenuContainer: {
    position: 'absolute',
    top: 10,
    right: 10,
    zIndex: 1000,
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    color: 'white',
    marginTop: 16,
    fontSize: 16,
  },
  successContainer: {
    width: '90%',
    maxWidth: 400,
  },
  successSurface: {
    padding: 32,
    borderRadius: 10,
    alignItems: 'center',
  },
  successIcon: {
    marginBottom: 16,
  },
  successTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
  },
  successMessage: {
    fontSize: 16,
    marginBottom: 24,
    textAlign: 'center',
    lineHeight: 24,
  },
  errorContainer: {
    width: '90%',
    maxWidth: 400,
  },
  errorSurface: {
    padding: 32,
    borderRadius: 10,
    alignItems: 'center',
  },
  errorIcon: {
    marginBottom: 16,
  },
  errorTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
  },
  errorMessage: {
    fontSize: 16,
    marginBottom: 24,
    textAlign: 'center',
    lineHeight: 24,
  },
});

export default ResetPasswordConfirm;
