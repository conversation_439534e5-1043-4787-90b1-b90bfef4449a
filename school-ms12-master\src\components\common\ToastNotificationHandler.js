import React, { useState, useEffect, useRef } from 'react';
import { AppState } from 'react-native';
import ToastNotification from './ToastNotification';
import { useNotifications } from '../../context/NotificationContext';

/**
 * Component that handles displaying toast notifications when new notifications arrive
 */
const ToastNotificationHandler = () => {
  const { notifications, markAsRead } = useNotifications();
  const [toastVisible, setToastVisible] = useState(false);
  const [currentNotification, setCurrentNotification] = useState(null);
  const notificationQueue = useRef([]);
  const appState = useRef(AppState.currentState);
  const lastNotificationId = useRef(null);

  // Listen for new notifications
  useEffect(() => {
    if (!notifications || notifications.length === 0) return;

    // Get the most recent unread notification
    const latestNotification = notifications.find(n => 
      n.status === 'unread' && n.id !== lastNotificationId.current
    );

    if (latestNotification) {
      // Add to queue if not already showing a notification
      if (!toastVisible) {
        showNotification(latestNotification);
      } else {
        notificationQueue.current.push(latestNotification);
      }
      
      // Remember this notification ID to avoid showing it again
      lastNotificationId.current = latestNotification.id;
    }
  }, [notifications]);

  // Listen for app state changes
  useEffect(() => {
    const subscription = AppState.addEventListener('change', nextAppState => {
      // When app comes to foreground, check for new notifications
      if (
        appState.current.match(/inactive|background/) &&
        nextAppState === 'active'
      ) {
        // Clear the queue when coming back to the app
        notificationQueue.current = [];
        
        // If we have notifications, show the most recent unread one
        if (notifications && notifications.length > 0) {
          const latestNotification = notifications.find(n => n.status === 'unread');
          if (latestNotification && !toastVisible) {
            showNotification(latestNotification);
            lastNotificationId.current = latestNotification.id;
          }
        }
      }
      
      appState.current = nextAppState;
    });

    return () => {
      subscription.remove();
    };
  }, [notifications, toastVisible]);

  // Show notification
  const showNotification = (notification) => {
    setCurrentNotification(notification);
    setToastVisible(true);
  };

  // Handle notification dismiss
  const handleDismiss = () => {
    setToastVisible(false);
    
    // Mark the current notification as read
    if (currentNotification) {
      markAsRead(currentNotification.id);
    }
    
    // Show the next notification in the queue after a short delay
    setTimeout(() => {
      if (notificationQueue.current.length > 0) {
        const nextNotification = notificationQueue.current.shift();
        showNotification(nextNotification);
      }
    }, 300);
  };

  // Get notification type
  const getNotificationType = (type) => {
    switch (type) {
      case 'alert':
        return 'error';
      case 'warning':
        return 'warning';
      case 'success':
        return 'success';
      case 'message':
      case 'info':
      default:
        return 'info';
    }
  };

  // Get navigation target based on notification type and data
  const getNavigationTarget = (notification) => {
    if (!notification || !notification.data) return null;
    
    const { screen, params } = notification.data;
    
    if (screen) {
      return {
        screen,
        params: params || {}
      };
    }
    
    // Default navigation based on notification type
    switch (notification.type) {
      case 'grade':
        return { screen: 'Grades' };
      case 'attendance':
        return { screen: 'Attendance' };
      case 'message':
        return { screen: 'Messages' };
      case 'event':
        return { screen: 'Calendar' };
      case 'assignment':
        return { screen: 'Assignments' };
      default:
        return { screen: 'NotificationCenter' };
    }
  };

  if (!currentNotification) return null;
  
  const navigationTarget = getNavigationTarget(currentNotification);

  return (
    <ToastNotification
      visible={toastVisible}
      onDismiss={handleDismiss}
      title={currentNotification.title}
      message={currentNotification.body}
      type={getNotificationType(currentNotification.type)}
      duration={5000}
      navigateTo={navigationTarget?.screen}
      navigationParams={navigationTarget?.params}
    />
  );
};

export default ToastNotificationHandler;
