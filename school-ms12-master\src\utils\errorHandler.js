export const handleAdminError = (error, context) => {
  // Log error
  console.error(`Admin Error in ${context}:`, error);
  
  // Categorize error
  if (error.code === 'permission-denied') {
    return 'You do not have permission to perform this action';
  }
  
  if (error.code === 'not-found') {
    return 'The requested resource was not found';
  }
  
  return 'An unexpected error occurred. Please try again.';
}; 