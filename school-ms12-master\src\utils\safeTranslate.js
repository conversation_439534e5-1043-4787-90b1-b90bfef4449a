/**
 * Safely handles translations that might return objects instead of strings
 * 
 * @param {Function} translate - The translation function
 * @param {string} key - The translation key
 * @param {string} fallback - Fallback text if translation is not a string
 * @param {Object} params - Optional parameters for the translation
 * @returns {string} - A safe string value
 */
export const safeTranslate = (translate, key, fallback, params = {}) => {
  try {
    if (!translate || typeof translate !== 'function') {
      return fallback || key;
    }
    
    const translation = translate(key, params);
    
    // If translation is an object, return the fallback
    if (translation && typeof translation === 'object') {
      return fallback || key;
    }
    
    // If translation is undefined, null, or empty, return the fallback
    if (!translation && translation !== 0) {
      return fallback || key;
    }
    
    // Otherwise return the translation as a string
    return String(translation);
  } catch (error) {
    console.error(`Error in safeTranslate for key: ${key}`, error);
    return fallback || key;
  }
};

export default safeTranslate;
