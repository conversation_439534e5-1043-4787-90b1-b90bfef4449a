import React, { useState, useEffect } from 'react';
import { View, ScrollView, StyleSheet } from 'react-native';
import { Card, Title, Text, DataTable, Portal, Modal, ActivityIndicator } from 'react-native-paper';
import { db } from '../../config/firebase';
import { collection, query, where, getDocs, doc, getDoc } from 'firebase/firestore';
import CustomButton from '../../components/common/CustomButton';
import { auth } from '../../config/firebase';

const SubjectDetails = () => {
  const [subjects, setSubjects] = useState([]);
  const [teachers, setTeachers] = useState({});
  const [selectedSubject, setSelectedSubject] = useState(null);
  const [modalVisible, setModalVisible] = useState(false);
  const [loading, setLoading] = useState(true);
  const [studentClass, setStudentClass] = useState(null);
  const [markSheets, setMarkSheets] = useState({});

  useEffect(() => {
    fetchStudentClass();
  }, []);

  useEffect(() => {
    if (studentClass) {
      fetchSubjects();
      fetchMarkSheets();
    }
  }, [studentClass]);

  const fetchStudentClass = async () => {
    try {
      const studentRef = doc(db, 'users', auth.currentUser.uid);
      const studentDoc = await getDoc(studentRef);
      if (studentDoc.exists()) {
        setStudentClass(studentDoc.data().classId);
      }
    } catch (error) {
      console.error('Error fetching student class:', error);
    }
  };

  const fetchSubjects = async () => {
    try {
      setLoading(true);
      const subjectsRef = collection(db, 'subjects');
      const q = query(subjectsRef, where('classId', '==', studentClass));
      const querySnapshot = await getDocs(q);
      
      const subjectData = [];
      const teacherPromises = [];
      
      querySnapshot.forEach((doc) => {
        const subject = { id: doc.id, ...doc.data() };
        subjectData.push(subject);
        if (subject.teacherId) {
          teacherPromises.push(fetchTeacherDetails(subject.teacherId));
        }
      });
      
      setSubjects(subjectData);
      
      // Fetch all teachers' details
      const teacherDetails = await Promise.all(teacherPromises);
      const teacherMap = {};
      teacherDetails.forEach(teacher => {
        if (teacher) {
          teacherMap[teacher.id] = teacher;
        }
      });
      setTeachers(teacherMap);
    } catch (error) {
      console.error('Error fetching subjects:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchTeacherDetails = async (teacherId) => {
    try {
      const teacherRef = doc(db, 'users', teacherId);
      const teacherDoc = await getDoc(teacherRef);
      if (teacherDoc.exists()) {
        return { id: teacherDoc.id, ...teacherDoc.data() };
      }
      return null;
    } catch (error) {
      console.error('Error fetching teacher details:', error);
      return null;
    }
  };

  const fetchMarkSheets = async () => {
    try {
      const markSheetsRef = collection(db, 'markSheets');
      const q = query(markSheetsRef, where('classId', '==', studentClass));
      const querySnapshot = await getDocs(q);
      
      const markSheetData = {};
      querySnapshot.forEach((doc) => {
        const data = doc.data();
        if (!markSheetData[data.subjectId]) {
          markSheetData[data.subjectId] = [];
        }
        markSheetData[data.subjectId].push({
          id: doc.id,
          ...data,
          studentMarks: data.marks[auth.currentUser.uid] || null,
        });
      });
      
      setMarkSheets(markSheetData);
    } catch (error) {
      console.error('Error fetching mark sheets:', error);
    }
  };

  const calculateProgress = (subjectId) => {
    const subjectMarks = markSheets[subjectId] || [];
    if (subjectMarks.length === 0) return 0;

    const totalMarks = subjectMarks.reduce((sum, sheet) => {
      return sum + (sheet.studentMarks?.marks ? parseInt(sheet.studentMarks.marks) : 0);
    }, 0);

    const totalMaxMarks = subjectMarks.reduce((sum, sheet) => {
      return sum + (sheet.maxMarks ? parseInt(sheet.maxMarks) : 0);
    }, 0);

    return totalMaxMarks > 0 ? (totalMarks / totalMaxMarks) * 100 : 0;
  };

  const renderSubjectDetails = () => {
    if (!selectedSubject) return null;

    const teacher = teachers[selectedSubject.teacherId];
    const subjectMarks = markSheets[selectedSubject.id] || [];

    return (
      <View>
        <Title>{selectedSubject.name}</Title>
        
        <View style={styles.detailSection}>
          <Text style={styles.sectionTitle}>Teacher Information</Text>
          <Text>Name: {teacher ? teacher.displayName : 'Not Assigned'}</Text>
          <Text>Email: {teacher ? teacher.email : 'N/A'}</Text>
        </View>

        <View style={styles.detailSection}>
          <Text style={styles.sectionTitle}>Course Information</Text>
          <Text>Credits: {selectedSubject.credits || 'N/A'}</Text>
          <Text>Schedule: {selectedSubject.schedule || 'Not Set'}</Text>
        </View>

        <View style={styles.detailSection}>
          <Text style={styles.sectionTitle}>Performance</Text>
          <DataTable>
            <DataTable.Header>
              <DataTable.Title>Exam Type</DataTable.Title>
              <DataTable.Title numeric>Marks</DataTable.Title>
              <DataTable.Title numeric>Max Marks</DataTable.Title>
              <DataTable.Title>Remarks</DataTable.Title>
            </DataTable.Header>

            {subjectMarks.map((markSheet, index) => (
              <DataTable.Row key={index}>
                <DataTable.Cell>{markSheet.examType}</DataTable.Cell>
                <DataTable.Cell numeric>
                  {markSheet.studentMarks?.marks || '-'}
                </DataTable.Cell>
                <DataTable.Cell numeric>{markSheet.maxMarks}</DataTable.Cell>
                <DataTable.Cell>
                  {markSheet.studentMarks?.remarks || '-'}
                </DataTable.Cell>
              </DataTable.Row>
            ))}
          </DataTable>
        </View>
      </View>
    );
  };

  return (
    <ScrollView style={styles.container}>
      <Card style={styles.card}>
        <Card.Content>
          <Title>My Subjects</Title>

          {loading ? (
            <ActivityIndicator style={styles.loader} />
          ) : (
            <DataTable>
              <DataTable.Header>
                <DataTable.Title>Subject</DataTable.Title>
                <DataTable.Title>Teacher</DataTable.Title>
                <DataTable.Title numeric>Progress</DataTable.Title>
                <DataTable.Title>Actions</DataTable.Title>
              </DataTable.Header>

              {subjects.map((subject) => (
                <DataTable.Row key={subject.id}>
                  <DataTable.Cell>{subject.name}</DataTable.Cell>
                  <DataTable.Cell>
                    {teachers[subject.teacherId]?.displayName || 'Not Assigned'}
                  </DataTable.Cell>
                  <DataTable.Cell numeric>
                    {calculateProgress(subject.id).toFixed(1)}%
                  </DataTable.Cell>
                  <DataTable.Cell>
                    <CustomButton
                      mode="outlined"
                      onPress={() => {
                        setSelectedSubject(subject);
                        setModalVisible(true);
                      }}
                      style={styles.actionButton}
                    >
                      View Details
                    </CustomButton>
                  </DataTable.Cell>
                </DataTable.Row>
              ))}
            </DataTable>
          )}
        </Card.Content>
      </Card>

      <Portal>
        <Modal
          visible={modalVisible}
          onDismiss={() => setModalVisible(false)}
          contentContainerStyle={styles.modalContent}
        >
          <ScrollView>
            {renderSubjectDetails()}
            <CustomButton
              mode="contained"
              onPress={() => setModalVisible(false)}
              style={styles.closeButton}
            >
              Close
            </CustomButton>
          </ScrollView>
        </Modal>
      </Portal>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  card: {
    margin: 16,
  },
  loader: {
    marginVertical: 20,
  },
  actionButton: {
    marginHorizontal: 4,
  },
  modalContent: {
    backgroundColor: 'white',
    padding: 20,
    margin: 20,
    borderRadius: 8,
    maxHeight: '80%',
  },
  detailSection: {
    marginVertical: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  closeButton: {
    marginTop: 20,
  },
});

export default SubjectDetails;
