import { db, auth } from '../config/firebase';
import {
  collection,
  query,
  where,
  getDocs,
  addDoc,
  updateDoc,
  deleteDoc,
  doc,
  serverTimestamp,
  getDoc,
  writeBatch,
  setDoc,
  limit
} from 'firebase/firestore';
import {
  createUserWithEmailAndPassword,
  deleteUser,
  sendPasswordResetEmail,
  updateEmail
} from 'firebase/auth';
import { handleAdminError } from '../utils/errorHandler';

class AdminService {
  /**
   * Create a new user
   * @param {Object} userData - User data including email and password
   * @param {string} role - User role (admin, teacher, student, parent)
   * @returns {Object} - Created user object
   */
  static async createUser(userData, role) {
    try {
      // Create auth user
      const userCredential = await createUserWithEmailAndPassword(
        auth,
        userData.email,
        userData.password
      );

      // Remove password from userData before storing
      const { password, ...userDataWithoutPassword } = userData;

      // Create user document
      const userRef = doc(db, 'users', userCredential.user.uid);
      await setDoc(userRef, {
        uid: userCredential.user.uid,
        ...userDataWithoutPassword,
        role,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        status: 'active'
      });

      // Create role-specific document
      const roleRef = doc(db, `${role}s`, userCredential.user.uid);
      await setDoc(roleRef, {
        uid: userCredential.user.uid,
        ...userDataWithoutPassword,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        status: 'active'
      });

      return userCredential.user;
    } catch (error) {
      handleAdminError(error, 'createUser');
      throw error;
    }
  }

  /**
   * Deactivate a user
   * @param {string} userId - User ID
   * @returns {boolean} - Success status
   */
  static async deactivateUser(userId) {
    try {
      // Get user data to determine role
      const userRef = doc(db, 'users', userId);
      const userDoc = await getDoc(userRef);

      if (!userDoc.exists()) {
        throw new Error('User not found');
      }

      const userData = userDoc.data();

      // Update user document
      await updateDoc(userRef, {
        status: 'inactive',
        updatedAt: serverTimestamp()
      });

      // Update role-specific document if it exists
      if (userData.role) {
        const roleRef = doc(db, `${userData.role}s`, userId);
        const roleDoc = await getDoc(roleRef);

        if (roleDoc.exists()) {
          await updateDoc(roleRef, {
            status: 'inactive',
            updatedAt: serverTimestamp()
          });
        }
      }

      return true;
    } catch (error) {
      handleAdminError(error, 'deactivateUser');
      throw error;
    }
  }

  /**
   * Get users by role
   * @param {string} role - User role (admin, teacher, student, parent)
   * @returns {Array} - Array of user objects
   */
  static async getUsersByRole(role) {
    try {
      const usersRef = collection(db, 'users');
      const q = query(usersRef, where('role', '==', role));
      const snapshot = await getDocs(q);
      return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    } catch (error) {
      handleAdminError(error, 'getUsersByRole');
      throw error;
    }
  }

  /**
   * Update user status
   * @param {string} userId - User ID
   * @param {string} status - New status (active, inactive, suspended)
   * @returns {boolean} - Success status
   */
  static async updateUserStatus(userId, status) {
    try {
      // Get user data to determine role
      const userRef = doc(db, 'users', userId);
      const userDoc = await getDoc(userRef);

      if (!userDoc.exists()) {
        throw new Error('User not found');
      }

      const userData = userDoc.data();

      // Update user document
      await updateDoc(userRef, {
        status,
        updatedAt: serverTimestamp()
      });

      // Update role-specific document if it exists
      if (userData.role) {
        const roleRef = doc(db, `${userData.role}s`, userId);
        const roleDoc = await getDoc(roleRef);

        if (roleDoc.exists()) {
          await updateDoc(roleRef, {
            status,
            updatedAt: serverTimestamp()
          });
        }
      }

      return true;
    } catch (error) {
      handleAdminError(error, 'updateUserStatus');
      throw error;
    }
  }

  /**
   * Create a new class
   * @param {Object} classData - Class data
   * @returns {string} - Created class ID
   */
  static async createClass(classData) {
    try {
      const classRef = collection(db, 'classes');
      const docRef = await addDoc(classRef, {
        ...classData,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });

      return docRef.id;
    } catch (error) {
      handleAdminError(error, 'createClass');
      throw error;
    }
  }

  /**
   * Assign teacher to a class
   * @param {string} classId - Class ID
   * @param {string} teacherId - Teacher ID
   * @returns {boolean} - Success status
   */
  static async assignTeacher(classId, teacherId) {
    try {
      const classRef = doc(db, 'classes', classId);
      await updateDoc(classRef, {
        teacherId,
        updatedAt: serverTimestamp()
      });
      return true;
    } catch (error) {
      handleAdminError(error, 'assignTeacher');
      throw error;
    }
  }

  /**
   * Get all users with pagination
   * @param {number} limit - Number of users to fetch
   * @param {number} page - Page number (starts from 1)
   * @returns {Array} - Array of user objects
   */
  static async getAllUsers(limit = 20, page = 1) {
    try {
      const usersRef = collection(db, 'users');
      // Use pagination with Firebase query
      const q = query(usersRef);
      const snapshot = await getDocs(q);

      // Get only the users for the current page
      const allUsers = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
      const startIndex = (page - 1) * limit;
      const users = allUsers.slice(startIndex);

      return {
        users,
        totalCount: snapshot.size,
        totalPages: Math.ceil(snapshot.size / limit)
      };
    } catch (error) {
      handleAdminError(error, 'getAllUsers');
      throw error;
    }
  }

  /**
   * Get user by ID
   * @param {string} userId - User ID
   * @returns {Object} - User object
   */
  static async getUserById(userId) {
    try {
      const userRef = doc(db, 'users', userId);
      const userDoc = await getDoc(userRef);

      if (!userDoc.exists()) {
        throw new Error('User not found');
      }

      return { id: userDoc.id, ...userDoc.data() };
    } catch (error) {
      handleAdminError(error, 'getUserById');
      throw error;
    }
  }

  /**
   * Update user profile
   * @param {string} userId - User ID
   * @param {Object} userData - User data to update
   * @returns {boolean} - Success status
   */
  static async updateUser(userId, userData) {
    try {
      const userRef = doc(db, 'users', userId);

      // Get current user data
      const userDoc = await getDoc(userRef);
      if (!userDoc.exists()) {
        throw new Error('User not found');
      }

      // Update user document
      await updateDoc(userRef, {
        ...userData,
        updatedAt: serverTimestamp()
      });

      // If role-specific data exists, update that too
      const currentData = userDoc.data();
      if (currentData.role) {
        const roleRef = doc(db, `${currentData.role}s`, userId);
        const roleDoc = await getDoc(roleRef);

        if (roleDoc.exists()) {
          await updateDoc(roleRef, {
            ...userData,
            updatedAt: serverTimestamp()
          });
        }
      }

      return true;
    } catch (error) {
      handleAdminError(error, 'updateUser');
      throw error;
    }
  }

  /**
   * Delete user
   * @param {string} userId - User ID
   * @returns {boolean} - Success status
   */
  static async deleteUser(userId) {
    try {
      // Get user data to determine role
      const userRef = doc(db, 'users', userId);
      const userDoc = await getDoc(userRef);

      if (!userDoc.exists()) {
        throw new Error('User not found');
      }

      const userData = userDoc.data();

      // Create a batch to delete all related documents
      const batch = writeBatch(db);

      // Delete user document
      batch.delete(userRef);

      // Delete role-specific document if it exists
      if (userData.role) {
        const roleRef = doc(db, `${userData.role}s`, userId);
        const roleDoc = await getDoc(roleRef);

        if (roleDoc.exists()) {
          batch.delete(roleRef);
        }
      }

      // Commit the batch
      await batch.commit();

      // Delete the auth user
      // Note: This requires the user to be recently authenticated
      // In a real app, you might want to use Firebase Admin SDK for this
      try {
        const user = auth.currentUser;
        if (user && user.uid === userId) {
          await deleteUser(user);
        }
      } catch (authError) {
        console.error('Error deleting auth user:', authError);
        // Continue even if auth deletion fails
      }

      return true;
    } catch (error) {
      handleAdminError(error, 'deleteUser');
      throw error;
    }
  }

  /**
   * Reset user password
   * @param {string} email - User email
   * @returns {boolean} - Success status
   */
  static async resetUserPassword(email) {
    try {
      await sendPasswordResetEmail(auth, email);
      return true;
    } catch (error) {
      handleAdminError(error, 'resetUserPassword');
      throw error;
    }
  }

  /**
   * Get all classes
   * @returns {Array} - Array of class objects
   */
  static async getAllClasses() {
    try {
      const classesRef = collection(db, 'classes');
      const snapshot = await getDocs(classesRef);
      return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    } catch (error) {
      handleAdminError(error, 'getAllClasses');
      throw error;
    }
  }

  /**
   * Get class by ID
   * @param {string} classId - Class ID
   * @returns {Object} - Class object
   */
  static async getClassById(classId) {
    try {
      const classRef = doc(db, 'classes', classId);
      const classDoc = await getDoc(classRef);

      if (!classDoc.exists()) {
        throw new Error('Class not found');
      }

      return { id: classDoc.id, ...classDoc.data() };
    } catch (error) {
      handleAdminError(error, 'getClassById');
      throw error;
    }
  }

  /**
   * Update class
   * @param {string} classId - Class ID
   * @param {Object} classData - Class data to update
   * @returns {boolean} - Success status
   */
  static async updateClass(classId, classData) {
    try {
      const classRef = doc(db, 'classes', classId);
      await updateDoc(classRef, {
        ...classData,
        updatedAt: serverTimestamp()
      });
      return true;
    } catch (error) {
      handleAdminError(error, 'updateClass');
      throw error;
    }
  }

  /**
   * Delete class
   * @param {string} classId - Class ID
   * @returns {boolean} - Success status
   */
  static async deleteClass(classId) {
    try {
      const classRef = doc(db, 'classes', classId);
      await deleteDoc(classRef);
      return true;
    } catch (error) {
      handleAdminError(error, 'deleteClass');
      throw error;
    }
  }

  /**
   * Get students in a class
   * @param {string} classId - Class ID
   * @returns {Array} - Array of student objects
   */
  static async getStudentsByClass(classId) {
    try {
      const studentsRef = collection(db, 'users');
      const q = query(
        studentsRef,
        where('role', '==', 'student'),
        where('classId', '==', classId)
      );
      const snapshot = await getDocs(q);
      return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    } catch (error) {
      handleAdminError(error, 'getStudentsByClass');
      throw error;
    }
  }

  /**
   * Approve grade submission
   * @param {string} submissionId - Submission ID
   * @returns {boolean} - Success status
   */
  static async approveGradeSubmission(submissionId) {
    try {
      const submissionRef = doc(db, 'gradeSubmissions', submissionId);
      const submissionDoc = await getDoc(submissionRef);

      if (!submissionDoc.exists()) {
        throw new Error('Submission not found');
      }

      await updateDoc(submissionRef, {
        status: 'approved',
        approvedAt: serverTimestamp(),
        approvedBy: auth.currentUser?.uid || 'unknown'
      });

      return true;
    } catch (error) {
      handleAdminError(error, 'approveGradeSubmission');
      throw error;
    }
  }

  /**
   * Reject grade submission
   * @param {string} submissionId - Submission ID
   * @param {string} reason - Rejection reason
   * @returns {boolean} - Success status
   */
  static async rejectGradeSubmission(submissionId, reason) {
    try {
      const submissionRef = doc(db, 'gradeSubmissions', submissionId);
      const submissionDoc = await getDoc(submissionRef);

      if (!submissionDoc.exists()) {
        throw new Error('Submission not found');
      }

      await updateDoc(submissionRef, {
        status: 'rejected',
        rejectedAt: serverTimestamp(),
        rejectedBy: auth.currentUser?.uid || 'unknown',
        rejectionReason: reason
      });

      return true;
    } catch (error) {
      handleAdminError(error, 'rejectGradeSubmission');
      throw error;
    }
  }

  /**
   * Get pending grade submissions
   * @returns {Array} - Array of submission objects
   */
  static async getPendingGradeSubmissions() {
    try {
      const submissionsRef = collection(db, 'gradeSubmissions');
      const q = query(submissionsRef, where('status', '==', 'pending'));
      const snapshot = await getDocs(q);
      return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    } catch (error) {
      handleAdminError(error, 'getPendingGradeSubmissions');
      throw error;
    }
  }

  /**
   * Get school settings
   * @returns {Object} - School settings object
   */
  static async getSchoolSettings() {
    try {
      const settingsRef = doc(db, 'schoolConfig', 'settings');
      const settingsDoc = await getDoc(settingsRef);

      if (!settingsDoc.exists()) {
        // Create default settings if they don't exist
        const defaultSettings = {
          schoolName: 'School Management System',
          address: {},
          contactInfo: {},
          academicYear: {
            startDate: new Date().toISOString(),
            endDate: new Date(new Date().setFullYear(new Date().getFullYear() + 1)).toISOString()
          },
          gradeSettings: [],
          workingHours: {},
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp()
        };

        await setDoc(settingsRef, defaultSettings);
        return defaultSettings;
      }

      return settingsDoc.data();
    } catch (error) {
      handleAdminError(error, 'getSchoolSettings');
      throw error;
    }
  }

  /**
   * Update school settings
   * @param {Object} settings - School settings
   * @returns {boolean} - Success status
   */
  static async updateSchoolSettings(settings) {
    try {
      const settingsRef = doc(db, 'schoolConfig', 'settings');
      await updateDoc(settingsRef, {
        ...settings,
        updatedAt: serverTimestamp()
      });
      return true;
    } catch (error) {
      handleAdminError(error, 'updateSchoolSettings');
      throw error;
    }
  }
}

export default AdminService;