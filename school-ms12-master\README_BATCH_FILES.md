# Batch Files for Class Tracking System

This document provides instructions for using the batch files to set up and test the class tracking system on Windows.

## Available Batch Files

1. **setup-class-tracking.bat**: Main setup script that guides you through the entire setup process
2. **deploy-firestore-rules.bat**: Deploys Firestore security rules
3. **update-timetable-data.bat**: Updates timetable data and generates sample data if needed
4. **test-class-tracking.bat**: Runs tests for the class tracking components

## Using the Batch Files

### Setup Class Tracking

To run the complete setup process:

1. Double-click on `setup-class-tracking.bat`
2. Follow the prompts to deploy Firestore rules, update timetable data, and run tests

### Deploy Firestore Rules

To deploy Firestore security rules:

1. Double-click on `deploy-firestore-rules.bat`
2. The script will check if Firebase CLI is installed and deploy the rules

### Update Timetable Data

To update timetable data:

1. Double-click on `update-timetable-data.bat`
2. Follow the prompts to update existing timetable entries and generate sample data

### Test Class Tracking

To test the class tracking features:

1. Double-click on `test-class-tracking.bat`
2. Enter the user role you want to test (teacher, student, or parent)
3. The script will run tests for the specified role

## Troubleshooting

### Path Issues

If you encounter path-related errors:

1. Make sure the batch files are in the same directory as the JavaScript files they're trying to run
2. Try running the batch files as administrator
3. If the issue persists, try running the JavaScript files directly with Node.js:
   ```
   node path\to\script.js
   ```

### Firebase CLI Issues

If you encounter issues with the Firebase CLI:

1. Make sure you have Node.js installed
2. Install Firebase CLI manually:
   ```
   npm install -g firebase-tools
   ```
3. Log in to Firebase:
   ```
   firebase login
   ```

### Script Execution Issues

If the scripts fail to execute:

1. Make sure you have Node.js installed
2. Check that all required files exist in the correct locations
3. Try running the scripts with the full path:
   ```
   node "C:\full\path\to\script.js"
   ```
