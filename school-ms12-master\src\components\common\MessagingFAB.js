import React, { useEffect, useState } from 'react';
import { StyleSheet, Animated, TouchableOpacity } from 'react-native';
import { FAB, useTheme, Badge, Surface, Text } from 'react-native-paper';
import { useNavigation } from '@react-navigation/native';
import { useAuth } from '../../context/AuthContext';
import { db } from '../../config/firebase';
import { collection, query, where, onSnapshot } from 'firebase/firestore';
// import { BlurView } from 'expo-blur';
import * as Animatable from 'react-native-animatable';
import { Ionicons } from '@expo/vector-icons';
import { useLanguage } from '../../context/LanguageContext';

const MessagingFAB = () => {
  const navigation = useNavigation();
  // No theme needed
  const { user } = useAuth();
  const { translate } = useLanguage();
  const [unreadCount, setUnreadCount] = useState(0);
  const [scaleAnim] = useState(new Animated.Value(1));
  const [showTooltip, setShowTooltip] = useState(false);

  useEffect(() => {
    if (user) {
      // Subscribe to unread messages count
      const chatsRef = collection(db, 'chats');
      const q = query(
        chatsRef,
        where('participants', 'array-contains', user.uid)
      );

      const unsubscribe = onSnapshot(q, (snapshot) => {
        let count = 0;
        snapshot.forEach((doc) => {
          const chat = doc.data();
          count += chat.unreadCount?.[user.uid] || 0;
        });
        setUnreadCount(count);
      });

      return () => unsubscribe();
    }
  }, [user]);

  const handlePress = () => {
    Animated.sequence([
      Animated.timing(scaleAnim, {
        toValue: 0.9,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      }),
    ]).start(() => {
      navigation.navigate('ChatList');
    });
  };

  return (
    <Animated.View style={[styles.container, { transform: [{ scale: scaleAnim }] }]}>
      <TouchableOpacity
        onPress={handlePress}
        onLongPress={() => setShowTooltip(true)}
        onPressOut={() => setShowTooltip(false)}
        activeOpacity={0.7}
      >
        <Surface style={[styles.fabContainer, { backgroundColor: '#1976d2' }]}>
          <Ionicons name="chatbubble-ellipses" size={24} color={'#ffffff'} />
          {unreadCount > 0 && (
            <Animatable.View
              animation="bounceIn"
              duration={500}
              style={styles.badgeContainer}
            >
              <Badge
                size={20}
                style={[styles.badge, { backgroundColor: '#f50057' }]}
              >
                {unreadCount}
              </Badge>
            </Animatable.View>
          )}
        </Surface>
      </TouchableOpacity>

      {showTooltip && (
        <Animatable.View
          animation="fadeIn"
          duration={300}
          style={styles.tooltipContainer}
        >
          <Surface style={styles.tooltip}>
            <Text style={styles.tooltipText}>
              {translate('messaging.tooltip')}
            </Text>
          </Surface>
        </Animatable.View>
      )}
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 16,
    right: 16,
    zIndex: 1000,
  },
  fabContainer: {
    width: 56,
    height: 56,
    borderRadius: 28,
    elevation: 4,
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
  },
  badgeContainer: {
    position: 'absolute',
    top: -8,
    right: -8,
  },
  badge: {
    position: 'absolute',
  },
  tooltipContainer: {
    position: 'absolute',
    bottom: 70,
    right: 0,
    width: 200,
  },
  tooltip: {
    padding: 12,
    borderRadius: 12,
    overflow: 'hidden',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    elevation: 4,
  },
  tooltipText: {
    fontSize: 14,
    textAlign: 'center',
  },
});

export default MessagingFAB;
