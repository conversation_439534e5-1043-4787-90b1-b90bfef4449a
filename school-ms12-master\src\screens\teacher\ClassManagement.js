import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  Alert,
  Text,
  ImageBackground,
  RefreshControl,
  Animated,
  TouchableOpacity,
  Dimensions
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import {
  Card,
  Title,
  Paragraph,
  FAB,
  Portal,
  Modal,
  TextInput,
  List,
  IconButton,
  Searchbar,
  Chip,
  Button,
  ActivityIndicator,
  Dialog,
  Menu,
  Divider,
  SegmentedButtons,
  Snackbar,
  DataTable,
  Avatar,
  Badge,
  Surface,
  useTheme,
  Banner,
  DefaultTheme
} from 'react-native-paper';
import { db, auth } from '../../config/firebase';
import { useLanguage } from '../../context/LanguageContext';
import {
  collection,
  query,
  where,
  getDocs,
  getDoc,
  doc,
  updateDoc,
  addDoc,
  orderBy,
  onSnapshot,
  deleteField
} from 'firebase/firestore';

const ClassManagement = ({ navigation }) => {
  const { translate, language } = useLanguage();
  // Use DefaultTheme as a fallback if useTheme() returns undefined
  let theme;
  try {
    theme = useTheme() || DefaultTheme;
  } catch (error) {
    console.log('Error using theme, falling back to DefaultTheme', error);
    theme = DefaultTheme;
  }

  // Default theme colors in case theme is still undefined
  const defaultTheme = DefaultTheme;
  const [classes, setClasses] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedClass, setSelectedClass] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [students, setStudents] = useState([]);
  const [filterValue, setFilterValue] = useState('all'); // 'all' or 'my-classes'
  const [teacherData, setTeacherData] = useState(null);
  const [viewMode, setViewMode] = useState('classes'); // 'classes', 'sections', 'students'
  const [selectedSection, setSelectedSection] = useState(null);
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [error, setError] = useState(null);
  const [fabOpen, setFabOpen] = useState(false);
  const [showTips, setShowTips] = useState(true);

  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(20)).current;

  useEffect(() => {
    fetchTeacherData();
    fetchClasses();

    // Add beautiful animations
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }),
    ]).start();
  }, [language]);

  const fetchTeacherData = async () => {
    try {
      // Check if Firebase auth is available
      if (!auth || !auth.currentUser) {
        console.warn('Firebase auth not available or user not logged in');
        // Use mock data for development/testing
        setTeacherData({
          id: 'mock-teacher-id',
          name: 'Demo Teacher',
          email: '<EMAIL>',
          sections: [
            { classId: 'class1', sectionName: 'A' },
            { classId: 'class2', sectionName: 'B' }
          ]
        });
        return;
      }

      const teacherRef = doc(db, 'users', auth.currentUser.uid);
      const teacherDoc = await getDoc(teacherRef);
      if (teacherDoc.exists()) {
        setTeacherData(teacherDoc.data());
      } else {
        console.warn('Teacher document not found');
        setError(translate('common.error'));
      }
    } catch (err) {
      console.error('Error fetching teacher data:', err);
      // Use mock data as fallback when Firebase fails
      setTeacherData({
        id: 'mock-teacher-id',
        name: 'Demo Teacher',
        email: '<EMAIL>',
        sections: [
          { classId: 'class1', sectionName: 'A' },
          { classId: 'class2', sectionName: 'B' }
        ]
      });
    }
  };

  const fetchClasses = async () => {
    try {
      setLoading(true);

      // Check if Firebase is available
      if (!db) {
        console.warn('Firebase db not available, using mock data');
        // Use mock data for development/testing
        const mockClasses = [
          {
            id: 'class1',
            name: '10',
            description: 'Grade 10 Class',
            sections: [
              {
                name: 'A',
                capacity: 30,
                teacherId: 'teacher1',
                teacherName: 'John Smith',
                students: ['student1', 'student2', 'student3']
              },
              {
                name: 'B',
                capacity: 25,
                teacherId: 'teacher2',
                teacherName: 'Jane Doe',
                students: ['student4', 'student5']
              }
            ],
            subjects: ['Math', 'Science', 'English']
          },
          {
            id: 'class2',
            name: '11',
            description: 'Grade 11 Class',
            sections: [
              {
                name: 'A',
                capacity: 28,
                teacherId: 'teacher3',
                teacherName: 'Robert Johnson',
                students: ['student6', 'student7', 'student8']
              }
            ],
            subjects: ['Physics', 'Chemistry', 'Biology', 'Math']
          }
        ];
        setClasses(mockClasses);
        return;
      }

      const classesRef = collection(db, 'classes');
      const classesSnapshot = await getDocs(classesRef);

      const allClassesData = [];
      for (const doc of classesSnapshot.docs) {
        const classData = { id: doc.id, ...doc.data() };
        allClassesData.push(classData);
      }

      setClasses(allClassesData);
    } catch (err) {
      console.error('Error fetching classes:', err);
      // Use mock data as fallback when Firebase fails
      const mockClasses = [
        {
          id: 'class1',
          name: '10',
          description: 'Grade 10 Class',
          sections: [
            {
              name: 'A',
              capacity: 30,
              teacherId: 'teacher1',
              teacherName: 'John Smith',
              students: ['student1', 'student2', 'student3']
            },
            {
              name: 'B',
              capacity: 25,
              teacherId: 'teacher2',
              teacherName: 'Jane Doe',
              students: ['student4', 'student5']
            }
          ],
          subjects: ['Math', 'Science', 'English']
        },
        {
          id: 'class2',
          name: '11',
          description: 'Grade 11 Class',
          sections: [
            {
              name: 'A',
              capacity: 28,
              teacherId: 'teacher3',
              teacherName: 'Robert Johnson',
              students: ['student6', 'student7', 'student8']
            }
          ],
          subjects: ['Physics', 'Chemistry', 'Biology', 'Math']
        }
      ];
      setClasses(mockClasses);
    } finally {
      setLoading(false);
    }
  };

  const handleClassPress = async (classData) => {
    try {
      setLoading(true);
      setError(null);
      setSelectedClass(classData);
      setViewMode('sections');
      setSelectedSection(null);
      setStudents([]);

      // Log class data for debugging
      console.log('Selected Class:', classData);

      // Get sections from class data
      const sections = classData.sections?.map(section => ({
        id: `${classData.id}_${section.name}`,
        name: section.name,
        classId: classData.id,
        className: classData.name,
        capacity: section.capacity || 30,
        teacherId: section.teacherId,
        teacherName: section.teacherName || translate('common.notAssigned'),
        students: section.students || [],
        studentCount: section.students?.length || 0
      })) || [];

      // If no sections, create a default one (for demo/testing)
      if (sections.length === 0) {
        console.warn('No sections found, creating default section');
        sections.push({
          id: `${classData.id}_A`,
          name: 'A',
          classId: classData.id,
          className: classData.name,
          capacity: 30,
          teacherId: 'teacher1',
          teacherName: 'Demo Teacher',
          students: ['student1', 'student2', 'student3'],
          studentCount: 3
        });
      }

      // Sort sections by name
      sections.sort((a, b) => a.name.localeCompare(b.name));

      console.log('Processed sections:', sections);

      // Update the selected class with sections
      setSelectedClass(prev => ({
        ...prev,
        sections: sections
      }));

    } catch (err) {
      console.error('Error processing sections:', err);
      // Create fallback sections
      const fallbackSections = [
        {
          id: `${classData.id}_A`,
          name: 'A',
          classId: classData.id,
          className: classData.name,
          capacity: 30,
          teacherId: 'teacher1',
          teacherName: 'Demo Teacher',
          students: ['student1', 'student2', 'student3'],
          studentCount: 3
        }
      ];

      // Update the selected class with fallback sections
      setSelectedClass(prev => ({
        ...prev,
        sections: fallbackSections
      }));

      setSnackbarMessage(translate('common.usingOfflineData'));
      setSnackbarVisible(true);
    } finally {
      setLoading(false);
    }
  };

  const handleSectionPress = async (section) => {
    try {
      setLoading(true);
      setError(null);
      setSelectedSection(section);
      setViewMode('students');

      console.log('Selected section:', section);
      console.log('Selected class:', selectedClass);

      if (!section.students || section.students.length === 0) {
        console.log('No students array in section');
        setStudents([]);
        return;
      }

      // Check if Firebase is available
      if (!db) {
        console.warn('Firebase db not available, using mock student data');
        // Generate mock student data
        const mockStudents = [
          {
            id: 'student1',
            name: 'John Student',
            firstName: 'John',
            lastName: 'Student',
            email: '<EMAIL>',
            rollNumber: '101',
            classId: selectedClass?.id,
            sectionName: section.name,
            gender: 'Male',
            attendance: { present: 85, absent: 5, late: 10 }
          },
          {
            id: 'student2',
            name: 'Jane Student',
            firstName: 'Jane',
            lastName: 'Student',
            email: '<EMAIL>',
            rollNumber: '102',
            classId: selectedClass?.id,
            sectionName: section.name,
            gender: 'Female',
            attendance: { present: 90, absent: 2, late: 8 }
          },
          {
            id: 'student3',
            name: 'Bob Student',
            firstName: 'Bob',
            lastName: 'Student',
            email: '<EMAIL>',
            rollNumber: '103',
            classId: selectedClass?.id,
            sectionName: section.name,
            gender: 'Male',
            attendance: { present: 75, absent: 15, late: 10 }
          }
        ];
        setStudents(mockStudents);
        return;
      }

      // Fetch students using their IDs from the section
      const usersRef = collection(db, 'users');
      const studentPromises = section.students.map(async studentId => {
        try {
          console.log('Fetching student with ID:', studentId);
          const studentDoc = await getDoc(doc(usersRef, studentId));

          if (studentDoc.exists()) {
            const studentData = studentDoc.data();
            console.log('Student data:', studentData);

            // Get the first name and last name
            const firstName = studentData.firstName || '';
            const lastName = studentData.lastName || '';
            const fullName = [firstName, lastName].filter(Boolean).join(' ');

            // If no first/last name, try displayName or username
            const displayName = studentData.displayName || studentData.username || '';

            // Use the first available name
            const studentName = fullName || displayName || translate('common.unnamed');

            return {
              id: studentDoc.id,
              name: studentName,
              firstName: firstName,
              lastName: lastName,
              email: studentData.email || '',
              rollNumber: studentData.rollNumber || translate('common.notAssigned'),
              classId: studentData.classId,
              sectionName: studentData.sectionName,
              ...studentData
            };
          } else {
            console.log('No student document found for ID:', studentId);
          }
        } catch (err) {
          console.error(`Error fetching student ${studentId}:`, err);
        }
        return null;
      });

      const studentsData = (await Promise.all(studentPromises))
        .filter(student => student !== null)
        .sort((a, b) => {
          // First sort by roll number if available
          if (a.rollNumber && b.rollNumber && a.rollNumber !== translate('common.notAssigned') && b.rollNumber !== translate('common.notAssigned')) {
            return a.rollNumber.localeCompare(b.rollNumber);
          }
          // Then sort by name
          return (a.name || '').localeCompare(b.name || '');
        });

      console.log('Found students:', studentsData.length);
      console.log('Processed students data:', studentsData);

      setStudents(studentsData);

    } catch (err) {
      console.error('Error fetching students:', err);
      // Use mock data as fallback
      const mockStudents = [
        {
          id: 'student1',
          name: 'John Student',
          firstName: 'John',
          lastName: 'Student',
          email: '<EMAIL>',
          rollNumber: '101',
          classId: selectedClass?.id,
          sectionName: section.name
        },
        {
          id: 'student2',
          name: 'Jane Student',
          firstName: 'Jane',
          lastName: 'Student',
          email: '<EMAIL>',
          rollNumber: '102',
          classId: selectedClass?.id,
          sectionName: section.name
        }
      ];
      setStudents(mockStudents);
      setSnackbarMessage(translate('common.usingOfflineData'));
      setSnackbarVisible(true);
    } finally {
      setLoading(false);
    }
  };

  const handleBack = () => {
    if (viewMode === 'students') {
      setViewMode('sections');
      setSelectedSection(null);
      setStudents([]);
    } else if (viewMode === 'sections') {
      setViewMode('classes');
      setSelectedClass(null);
    }
  };

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    setError(null);
    try {
      await fetchTeacherData();

      if (viewMode === 'classes') {
        await fetchClasses();
      } else if (viewMode === 'sections' && selectedClass) {
        await handleClassPress(selectedClass);
      } else if (viewMode === 'students' && selectedSection) {
        await handleSectionPress(selectedSection);
      }

      // Show success message
      setSnackbarMessage(translate('common.refresh') + ' ' + translate('common.success'));
      setSnackbarVisible(true);
    } catch (err) {
      console.error('Error refreshing data:', err);
      setError(translate('common.errorLoadingData'));
    } finally {
      setRefreshing(false);
    }
  }, [viewMode, selectedClass, selectedSection, language]);

  const filteredClasses = classes.filter(classItem => {
    const matchesSearch =
      classItem.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      classItem.description?.toLowerCase().includes(searchQuery.toLowerCase());

    if (filterValue === 'my-classes') {
      return matchesSearch && teacherData?.sections?.some(teacherSection =>
        teacherSection.classId === classItem.id
      );
    }
    return matchesSearch;
  });

  const renderClassList = () => (
    <>
      {filteredClasses.length > 0 ? (
        filteredClasses.map((classData) => (
          <Card
            key={classData.id}
            style={[styles.card, { backgroundColor: '#ffffff' }]}
            onPress={() => handleClassPress(classData)}
            mode="elevated"
          >
            <Card.Content>
              <View style={styles.classHeader}>
                <Avatar.Text
                  size={50}
                  label={classData.name.substring(0, 2).toUpperCase()}
                  color="#ffffff"
                  style={{ backgroundColor: '#6200ee' }}
                />
                <View style={styles.classInfo}>
                  <Title style={styles.className}>
                    {translate('teacher.classManagement.class')} {classData.name}
                  </Title>
                  <Paragraph style={styles.classDescription}>
                    {classData.description || translate('teacher.classManagement.noDescription')}
                  </Paragraph>
                </View>
              </View>

              <View style={styles.classStats}>
                <View style={styles.statItem}>
                  <Avatar.Icon
                    size={36}
                    icon="account-group"
                    color="#6200ee"
                    style={{ backgroundColor: 'transparent' }}
                  />
                  <Text style={styles.statValue}>
                    {classData.sections?.reduce((acc, section) => acc + (section.students?.length || 0), 0) || 0}
                  </Text>
                  <Text style={styles.statLabel}>{translate('teacher.classManagement.students')}</Text>
                </View>

                <View style={styles.statItem}>
                  <Avatar.Icon
                    size={36}
                    icon="view-grid-outline"
                    color="#6200ee"
                    style={{ backgroundColor: 'transparent' }}
                  />
                  <Text style={styles.statValue}>{classData.sections?.length || 0}</Text>
                  <Text style={styles.statLabel}>{translate('teacher.classManagement.sections')}</Text>
                </View>

                <View style={styles.statItem}>
                  <Avatar.Icon
                    size={36}
                    icon="book-open-variant"
                    color="#6200ee"
                    style={{ backgroundColor: 'transparent' }}
                  />
                  <Text style={styles.statValue}>{classData.subjects?.length || 0}</Text>
                  <Text style={styles.statLabel}>{translate('teacher.classManagement.subjects')}</Text>
                </View>
              </View>

              {teacherData?.sections?.some(ts => ts.classId === classData.id) && (
                <Chip
                  icon="star"
                  style={[styles.teachingChip, { backgroundColor: '#e9ddff' }]}
                  textStyle={{ color: '#21005d' }}
                >
                  {translate('teacher.classManagement.youTeachHere')}
                </Chip>
              )}
            </Card.Content>
            <Card.Actions style={styles.cardActions}>
              <Button
                mode="text"
                icon="arrow-right"
                onPress={() => handleClassPress(classData)}
                textColor="#6200ee"
              >
                {translate('common.viewDetails')}
              </Button>
            </Card.Actions>
          </Card>
        ))
      ) : (
        <Card style={styles.emptyCard}>
          <Avatar.Icon
            size={60}
            icon="book-remove"
            style={{ backgroundColor: 'transparent' }}
            color="#79747e"
          />
          <Text style={styles.emptyText}>
            {searchQuery ?
              translate('teacher.classManagement.noClassesFound') :
              translate('teacher.classManagement.noClasses')}
          </Text>
          <Button
            mode="outlined"
            onPress={onRefresh}
            style={{ marginTop: 16 }}
          >
            {translate('common.refresh')}
          </Button>
        </Card>
      )}
    </>
  );

  const renderSectionList = () => (
    <>
      <Card style={styles.headerCard}>
        <Card.Content>
          <View style={styles.headerContent}>
            <View>
              <Title>{translate('teacher.classManagement.class')} {selectedClass.name}</Title>
              <Paragraph>{selectedClass.description}</Paragraph>
            </View>
            <IconButton
              icon="arrow-left"
              size={24}
              onPress={handleBack}
            />
          </View>
        </Card.Content>
      </Card>

      {loading ? (
        <ActivityIndicator style={styles.loader} color="#6200ee" />
      ) : error ? (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
          <Button
            mode="contained"
            onPress={() => handleClassPress(selectedClass)}
            buttonColor="#6200ee"
          >
            {translate('common.retry')}
          </Button>
        </View>
      ) : selectedClass?.sections?.length > 0 ? (
        selectedClass.sections.map((section) => (
          <Card
            key={section.id}
            style={styles.card}
            onPress={() => handleSectionPress(section)}
          >
            <Card.Content>
              <View style={styles.sectionHeader}>
                <View>
                  <Title>{translate('teacher.classManagement.section')} {section.name}</Title>
                  <Paragraph>{translate('teacher.classManagement.teacher')}: {section.teacherName || translate('common.notAssigned')}</Paragraph>
                  <Paragraph>{translate('teacher.classManagement.capacity')}: {section.capacity}</Paragraph>
                  <Paragraph>{translate('teacher.classManagement.students')}: {section.studentCount}</Paragraph>
                </View>
                {teacherData?.sections?.some(
                  ts => ts.classId === selectedClass.id && ts.sectionName === section.name
                ) && (
                  <Chip
                    icon="star"
                    style={{ backgroundColor: '#e9ddff' }}
                    textStyle={{ color: '#21005d' }}
                  >
                    {translate('teacher.classManagement.yourSection')}
                  </Chip>
                )}
              </View>
              <View style={styles.sectionActions}>
                <Button
                  mode="outlined"
                  onPress={() => handleSectionPress(section)}
                  style={styles.actionButton}
                  textColor="#6200ee"
                >
                  {translate('common.viewDetails')}
                </Button>
              </View>
            </Card.Content>
          </Card>
        ))
      ) : (
        <Card style={styles.emptyCard}>
          <Card.Content>
            <Text style={styles.emptyText}>{translate('teacher.classManagement.noSections')}</Text>
          </Card.Content>
        </Card>
      )}
    </>
  );

  const renderStudentList = () => (
    <>
      <Card style={[styles.headerCard, { backgroundColor: '#e8def8' }]}>
        <Card.Content>
          <View style={styles.studentHeaderContent}>
            <View style={styles.studentHeaderInfo}>
              <Avatar.Text
                size={40}
                label={selectedSection.name.substring(0, 1).toUpperCase()}
                color="#ffffff"
                style={{ backgroundColor: '#625b71' }}
              />
              <View style={{ marginLeft: 12 }}>
                <Title style={{ color: '#1d192b' }}>
                  {translate('teacher.classManagement.section')} {selectedSection.name}
                </Title>
                <Paragraph style={{ color: '#1d192b' }}>
                  {translate('teacher.classManagement.class')} {selectedClass.name}
                </Paragraph>
              </View>
            </View>

            <Badge style={styles.studentCountBadge} size={28}>
              {students.length}
            </Badge>
          </View>

          <View style={styles.studentActions}>
            <Button
              mode="contained-tonal"
              icon="account-check"
              onPress={() => navigation.navigate('AttendanceManagement', {
                classId: selectedClass.id,
                className: selectedClass.name,
                sectionName: selectedSection.name
              })}
              style={styles.actionButton}
              buttonColor="#ffd8e4"
              textColor="#31111d"
            >
              {translate('teacher.attendance.title')}
            </Button>
            <Button
              mode="contained-tonal"
              icon="star"
              onPress={() => navigation.navigate('GradeManagement', {
                classId: selectedClass.id,
                className: selectedClass.name,
                sectionName: selectedSection.name
              })}
              style={styles.actionButton}
              buttonColor="#e8def8"
              textColor="#1d192b"
            >
              {translate('teacher.gradeManagement.title')}
            </Button>
            <Button
              mode="contained-tonal"
              icon="home-edit"
              onPress={() => navigation.navigate('HomeworkManagement', {
                classId: selectedClass.id,
                className: selectedClass.name,
                sectionName: selectedSection.name
              })}
              style={styles.actionButton}
              buttonColor="#e9ddff"
              textColor="#21005d"
            >
              {translate('teacher.assignments.title')}
            </Button>
          </View>
        </Card.Content>
      </Card>

      {loading ? (
        <ActivityIndicator style={styles.loader} color="#6200ee" />
      ) : error ? (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
          <Button
            mode="contained"
            onPress={() => handleSectionPress(selectedSection)}
            buttonColor="#6200ee"
            style={{ marginTop: 12 }}
          >
            {translate('common.retry')}
          </Button>
        </View>
      ) : students.length > 0 ? (
        <Card style={[styles.card, { backgroundColor: '#ffffff' }]} mode="elevated">
          <Card.Content>
            <Title style={styles.tableTitle}>{translate('teacher.classManagement.studentsList')}</Title>

            <DataTable style={styles.dataTable}>
              <DataTable.Header style={styles.tableHeader}>
                <DataTable.Title style={{ flex: 0.2 }}>#</DataTable.Title>
                <DataTable.Title style={{ flex: 0.5 }}>{translate('common.name')}</DataTable.Title>
                <DataTable.Title style={{ flex: 0.3 }}>{translate('common.roll')}</DataTable.Title>
              </DataTable.Header>

              <ScrollView style={{ maxHeight: 400 }}>
                {students.map((student, index) => (
                  <DataTable.Row
                    key={student.id}
                    style={index % 2 === 0 ? styles.evenRow : styles.oddRow}
                    onPress={() => {
                      setSnackbarMessage(`${translate('common.selected')}: ${student.name}`);
                      setSnackbarVisible(true);
                    }}
                  >
                    <DataTable.Cell style={{ flex: 0.2 }}>{index + 1}</DataTable.Cell>
                    <DataTable.Cell style={{ flex: 0.5 }}>
                      <View style={styles.studentNameCell}>
                        <Avatar.Text
                          size={24}
                          label={student.name.substring(0, 1).toUpperCase()}
                          style={{ marginRight: 8 }}
                        />
                        <Text>{student.name}</Text>
                      </View>
                    </DataTable.Cell>
                    <DataTable.Cell style={{ flex: 0.3 }}>
                      {student.rollNumber || translate('common.notAssigned')}
                    </DataTable.Cell>
                  </DataTable.Row>
                ))}
              </ScrollView>
            </DataTable>
          </Card.Content>
        </Card>
      ) : (
        <Card style={[styles.emptyCard, { marginTop: 20 }]}>
          <Avatar.Icon
            size={60}
            icon="account-school"
            style={{ backgroundColor: 'transparent' }}
            color="#79747e"
          />
          <Text style={styles.emptyText}>{translate('teacher.classManagement.noStudents')}</Text>
        </Card>
      )}
    </>
  );

  // Use default theme if theme is undefined
  const safeTheme = theme || defaultTheme;

  return (
    <View style={[styles.container, { backgroundColor: safeTheme.colors.background }]}>
      <ImageBackground
        source={{ uri: 'https://i.imgur.com/8FG9nwf.png' }}
        style={styles.backgroundImage}
        resizeMode="cover"
      >
        <LinearGradient
          colors={['rgba(255,255,255,0.9)', 'rgba(255,255,255,0.8)']}
          style={styles.gradientOverlay}
        >
          <Surface style={[styles.header, { elevation: 4, backgroundColor: 'rgba(255,255,255,0.9)' }]}>
            <View style={styles.headerTopRow}>
              {viewMode !== 'classes' && (
                <IconButton
                  icon="arrow-left"
                  size={24}
                  onPress={handleBack}
                  style={styles.backButton}
                  iconColor="#6200ee"
                />
              )}
              <Title style={styles.headerTitle}>
                {viewMode === 'classes' && translate('teacher.classManagement.title')}
                {viewMode === 'sections' && selectedClass &&
                  `${translate('teacher.classManagement.class')} ${selectedClass.name}`}
                {viewMode === 'students' && selectedClass && selectedSection &&
                  `${translate('teacher.classManagement.section')} ${selectedSection.name}`}
              </Title>
              <IconButton
                icon="refresh"
                size={24}
                onPress={onRefresh}
                disabled={refreshing}
                iconColor="#6200ee"
              />
            </View>

            <View style={styles.filterContainer}>
              <Searchbar
                placeholder={translate('common.search')}
                onChangeText={setSearchQuery}
                value={searchQuery}
                style={[styles.searchbar, { elevation: 2, backgroundColor: 'white' }]}
                iconColor="#6200ee"
                clearButtonMode="while-editing"
              />
              {viewMode === 'classes' && (
                <SegmentedButtons
                  value={filterValue}
                  onValueChange={setFilterValue}
                  buttons={[
                    { value: 'all', label: translate('teacher.classManagement.allClasses') },
                    { value: 'my-classes', label: translate('teacher.classManagement.myClasses') }
                  ]}
                  style={styles.filterButtons}
                />
              )}
            </View>
          </Surface>

          <Animated.ScrollView
            style={{
              opacity: fadeAnim,
              transform: [{ translateY: slideAnim }]
            }}
            refreshControl={
              <RefreshControl refreshing={refreshing} onRefresh={onRefresh} colors={['#6200ee']} />
            }
          >
            {showTips && (
              <Banner
                visible={true}
                actions={[
                  {
                    label: translate('common.dismiss'),
                    onPress: () => setShowTips(false),
                  },
                ]}
                icon="lightbulb-outline"
                style={styles.tipBanner}
              >
                {viewMode === 'classes'
                  ? translate('teacher.classManagement.title') + ': ' + translate('common.tapToView')
                  : viewMode === 'sections'
                  ? translate('teacher.classManagement.section') + ': ' + translate('common.tapToView')
                  : translate('teacher.classManagement.studentsList')}
              </Banner>
            )}

            {loading && !refreshing ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color="#6200ee" />
                <Text style={{ marginTop: 10, color: '#6200ee' }}>
                  {translate('common.loading')}
                </Text>
              </View>
            ) : error ? (
              <View style={styles.errorContainer}>
                <Avatar.Icon size={80} icon="alert" style={{ backgroundColor: '#b3261e' }} />
                <Text style={[styles.errorText, { color: '#b3261e' }]}>{error}</Text>
                <Button
                  mode="contained"
                  onPress={onRefresh}
                  style={{ marginTop: 20 }}
                  buttonColor="#6200ee"
                >
                  {translate('common.retry')}
                </Button>
              </View>
            ) : (
              <View style={styles.content}>
                {viewMode === 'classes' && renderClassList()}
                {viewMode === 'sections' && renderSectionList()}
                {viewMode === 'students' && renderStudentList()}
              </View>
            )}
          </Animated.ScrollView>

          <Portal>
            <FAB.Group
              open={fabOpen}
              icon={fabOpen ? 'close' : 'plus'}
              actions={[
                {
                  icon: 'refresh',
                  label: translate('common.refresh'),
                  onPress: onRefresh,
                },
                {
                  icon: 'home',
                  label: translate('common.dashboard'),
                  onPress: () => navigation.navigate('TeacherDashboard'),
                },
              ]}
              onStateChange={({ open }) => setFabOpen(open)}
              onPress={() => {
                if (fabOpen) {
                  // Just close the FAB
                }
              }}
              fabStyle={{ backgroundColor: '#6200ee' }}
            />
          </Portal>

          <Snackbar
            visible={snackbarVisible}
            onDismiss={() => setSnackbarVisible(false)}
            duration={3000}
            style={{ backgroundColor: '#ffffff' }}
            action={{
              label: translate('common.dismiss'),
              onPress: () => setSnackbarVisible(false),
            }}
          >
            {snackbarMessage}
          </Snackbar>
        </LinearGradient>
      </ImageBackground>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  backgroundImage: {
    flex: 1,
    width: '100%',
  },
  gradientOverlay: {
    flex: 1,
  },
  header: {
    padding: 8,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
    borderRadius: 0,
  },
  headerTopRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    flex: 1,
    textAlign: 'center',
    color: '#6200ee',
  },
  backButton: {
    marginRight: 8,
  },
  filterContainer: {
    padding: 8,
  },
  searchbar: {
    marginBottom: 8,
    borderRadius: 8,
    elevation: 2,
  },
  filterButtons: {
    marginTop: 8,
  },
  content: {
    padding: 16,
  },
  card: {
    marginBottom: 16,
    borderRadius: 12,
    overflow: 'hidden',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  headerCard: {
    marginBottom: 16,
    borderRadius: 12,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
    minHeight: 300,
  },
  errorContainer: {
    padding: 40,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 300,
    backgroundColor: 'rgba(255,255,255,0.8)',
    borderRadius: 12,
    margin: 16,
  },
  errorText: {
    textAlign: 'center',
    marginTop: 20,
    fontSize: 16,
  },
  sectionCount: {
    marginTop: 8,
    color: '#666',
  },
  sectionDetails: {
    flexDirection: 'row',
    marginTop: 8,
  },
  teacherChip: {
    marginLeft: 8,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
  },
  actionButton: {
    flex: 1,
    marginHorizontal: 4,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  sectionActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 16,
    gap: 8,
  },
  emptyCard: {
    margin: 16,
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 200,
    backgroundColor: 'rgba(255,255,255,0.9)',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  emptyText: {
    textAlign: 'center',
    color: '#666',
    fontSize: 16,
    marginTop: 12,
  },
  loader: {
    marginTop: 20,
  },
  classHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  classInfo: {
    marginLeft: 16,
    flex: 1,
  },
  className: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  classDescription: {
    marginTop: 4,
    color: '#666',
  },
  classStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
    paddingVertical: 12,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.05)',
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.05)',
  },
  statItem: {
    alignItems: 'center',
    flex: 1,
  },
  statValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginVertical: 4,
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
  },
  teachingChip: {
    alignSelf: 'flex-start',
    marginTop: 8,
  },
  cardActions: {
    justifyContent: 'flex-end',
    paddingTop: 0,
  },
  studentHeaderContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  studentHeaderInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  studentCountBadge: {
    backgroundColor: '#6200ee',
    color: '#ffffff',
  },
  studentActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
    gap: 8,
  },
  tableTitle: {
    marginBottom: 12,
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  dataTable: {
    marginTop: 8,
    backgroundColor: 'white',
    borderRadius: 8,
    overflow: 'hidden',
  },
  tableHeader: {
    backgroundColor: 'rgba(0,0,0,0.05)',
  },
  evenRow: {
    backgroundColor: 'rgba(0,0,0,0.02)',
  },
  oddRow: {
    backgroundColor: 'transparent',
  },
  studentNameCell: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  tipBanner: {
    marginHorizontal: 16,
    marginTop: 16,
    borderRadius: 8,
  },
});

export default ClassManagement;
